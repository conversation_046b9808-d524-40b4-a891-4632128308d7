'use client';

import React from 'react';
import { Info } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ZAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';

/**
 * BubbleChartGenerator component for rendering bubble charts
 * @param {Object} props - Component props
 * @param {Object} props.bubbleConfig - Bubble chart configuration
 */
export default function BubbleChartGenerator({ bubbleConfig }) {
  if (!bubbleConfig || !bubbleConfig.data || bubbleConfig.data.length === 0) {
    return (
      <div className="p-4 bg-zinc-800 rounded-md">
        <p className="text-zinc-400">No bubble chart data available</p>
      </div>
    );
  }

  const { 
    data, 
    title, 
    subtitle, 
    explanation, 
    xAxis = {}, 
    yAxis = {}, 
    zAxis = {},
    colorKey,
    colors = [
      '#3b82f6', // blue-500
      '#10b981', // emerald-500
      '#f59e0b', // amber-500
      '#ef4444', // red-500
      '#8b5cf6', // violet-500
      '#ec4899', // pink-500
      '#06b6d4', // cyan-500
      '#f97316', // orange-500
    ],
    grid = true,
    tooltip = true,
    legend = true
  } = bubbleConfig;

  // Extract unique categories if colorKey is provided
  const categories = colorKey ? 
    [...new Set(data.map(item => item[colorKey]))] : 
    [];

  // Function to get color based on category
  const getColor = (item, index) => {
    if (colorKey && item[colorKey]) {
      const categoryIndex = categories.indexOf(item[colorKey]);
      return colors[categoryIndex % colors.length];
    }
    return colors[index % colors.length];
  };

  // Calculate domain for ZAxis (bubble size)
  const zValues = data.map(item => item.z);
  const zMin = Math.min(...zValues);
  const zMax = Math.max(...zValues);
  
  // Use provided range or default
  const zRange = zAxis.range || [20, 100];

  // Custom tooltip formatter
  const renderTooltip = (props) => {
    const { active, payload } = props;
    
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      
      return (
        <div className="bg-zinc-800 p-3 border border-zinc-700 rounded-md shadow-lg">
          <p className="text-zinc-200 font-medium mb-1">{data.name || 'Point'}</p>
          <p className="text-zinc-300 text-sm">
            {xAxis.label || 'X'}: <span className="text-zinc-100 font-medium">{data.x}</span>
          </p>
          <p className="text-zinc-300 text-sm">
            {yAxis.label || 'Y'}: <span className="text-zinc-100 font-medium">{data.y}</span>
          </p>
          <p className="text-zinc-300 text-sm">
            {zAxis.label || 'Size'}: <span className="text-zinc-100 font-medium">{data.z}</span>
          </p>
          {colorKey && data[colorKey] && (
            <p className="text-zinc-300 text-sm">
              {colorKey}: <span className="text-zinc-100 font-medium">{data[colorKey]}</span>
            </p>
          )}
        </div>
      );
    }
    
    return null;
  };

  // Custom legend for categories
  const renderCategoryLegend = () => {
    if (!colorKey || categories.length === 0) return null;
    
    return (
      <div className="flex flex-wrap justify-center mt-4 mb-2">
        {categories.map((category, index) => (
          <div key={category} className="flex items-center mr-4 mb-2">
            <div 
              className="w-3 h-3 rounded-full mr-1"
              style={{ backgroundColor: colors[index % colors.length] }}
            ></div>
            <span className="text-zinc-300 text-sm">{category}</span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="bubble-chart-container bg-zinc-900 rounded-lg border border-zinc-700 p-6">
      {/* Chart header */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white">{title}</h3>
        {subtitle && <p className="text-zinc-400 mt-1">{subtitle}</p>}
      </div>

      {/* Bubble chart visualization */}
      <div className="bubble-chart-visualization mb-6">
        <ResponsiveContainer width="100%" height={400}>
          <RechartsScatterChart margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
            {grid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" />}
            <XAxis
              dataKey="x"
              type="number"
              name={xAxis.label}
              label={{ value: xAxis.label, position: 'insideBottom', offset: -10, fill: '#d1d5db' }}
              tick={{ fill: '#9ca3af' }}
              axisLine={{ stroke: '#4b5563' }}
            />
            <YAxis
              dataKey="y"
              type="number"
              name={yAxis.label}
              label={{ value: yAxis.label, angle: -90, position: 'insideLeft', fill: '#d1d5db' }}
              tick={{ fill: '#9ca3af' }}
              axisLine={{ stroke: '#4b5563' }}
            />
            <ZAxis
              dataKey="z"
              type="number"
              range={zRange}
              domain={[zMin, zMax]}
              name={zAxis.label}
            />
            {tooltip && <Tooltip 
              content={renderTooltip}
              cursor={{ strokeDasharray: '3 3', stroke: 'rgba(255, 255, 255, 0.3)' }}
            />}
            {legend && colorKey && <Legend 
              wrapperStyle={{ color: '#d1d5db' }}
              formatter={(value) => <span style={{ color: '#d1d5db' }}>{value}</span>}
            />}
            <Scatter
              name={title}
              data={data}
              fill="#8884d8"
              animationDuration={1500}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={getColor(entry, index)}
                  stroke="#fff"
                  strokeWidth={1}
                />
              ))}
            </Scatter>
          </RechartsScatterChart>
        </ResponsiveContainer>
      </div>

      {/* Category legend */}
      {colorKey && renderCategoryLegend()}

      {/* Chart explanation */}
      {explanation && (
        <div className="mt-4 p-4 bg-zinc-800 rounded-md border border-zinc-700">
          <div className="flex items-start">
            <Info className="text-blue-400 mr-2 mt-1 flex-shrink-0" size={16} />
            <p className="text-zinc-300 text-sm">{explanation}</p>
          </div>
        </div>
      )}
    </div>
  );
}
