// app/api/initializeImageGeneration/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { GenerateImageAgent } from 'components/Agents/GenerateImageAgent';
import { GenerateImageTool } from 'components/tools/generateImageTool';

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // (A) Parse the incoming JSON body
    const body = await req.json();
    const {
      prompt,
      userId,
      model,
      size,
      style,
      quality,
      format,
      background,
      compression
    } = body;

    // Log the received parameters
    console.log('[initializeImageGeneration] Received parameters:');
    console.log(`  - model: ${model}`);
    console.log(`  - size: ${size}`);
    console.log(`  - style: ${style}`);
    console.log(`  - quality: ${quality}`);
    console.log(`  - format: ${format}`);
    console.log(`  - background: ${background}`);
    console.log(`  - compression: ${compression}`);

    // (B) Validate user input
    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid prompt provided' },
        { status: 400 }
      );
    }
    if (!userId || typeof userId !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Invalid userId provided' },
        { status: 400 }
      );
    }

    // (C) Validate OpenAI API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      console.error('OpenAI API key not configured');
      return NextResponse.json(
        { success: false, error: 'Service configuration error' },
        { status: 500 }
      );
    }

    // (D) Instantiate the tool & agent
    console.log(`[initializeImageGeneration] Using model: ${model}`);
    const imageTool = new GenerateImageTool(apiKey);
    const imageAgent = new GenerateImageAgent({ generateImageTool: imageTool });

    // (E) Create a new image generation job in Firestore
    // Ensure we have defaults for all required parameters
    const jobId = await imageAgent.initializeJob(
      prompt,
      userId,
      model || 'gpt-image-1',  // Default to gpt-image-1 if not specified
      size || '1024x1024',     // Default to 1024x1024 if not specified
      style || 'vivid',        // Default to vivid if not specified
      quality || 'auto',       // Default to auto if not specified
      format || 'jpeg',        // Default to jpeg if not specified
      background || 'auto',    // Default to auto if not specified
      compression
    );

    console.log(`[initializeImageGeneration] Job created with ID: ${jobId} and model: ${model || 'gpt-image-1'}`);

    // Refine the prompt - now passing both jobId and userId
    try {
      await imageAgent.refinePrompt(jobId, userId);
    } catch (refinementError) {
      console.error('[Route] Prompt refinement failed, but continuing with job:', refinementError);
      // We'll continue with the job even if refinement fails
      // The agent will use the original prompt as a fallback
    }

    // (F) Respond with success
    return NextResponse.json({
      success: true,
      jobId,
      message: 'Image generation job initialized successfully'
    });

  } catch (error) {
    console.error('[Route] Image generation initialization failed:', error);

    // (G) Return a 500 error response
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to initialize image generation'
      },
      { status: 500 }
    );
  }
}