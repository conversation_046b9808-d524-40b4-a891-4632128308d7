/**
 * API endpoint for processing PMO-generated tasks
 *
 * This endpoint:
 * 1. Receives a PMO task with team assignment information
 * 2. Routes the task to the appropriate agent team based on assignment
 * 3. Provides agents with access to source documents via PMO collection
 * 4. Returns the processing result
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { adminDb } from '../../../components/firebase-admin';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      taskId,
      taskTitle,
      taskDescription,
      teamAssignment,
      assignedUsers,
      projectId,
      category
    } = body;

    // Validate required parameters
    if (!taskId || !taskTitle || !taskDescription) {
      return NextResponse.json(
        { error: 'Missing required parameters: taskId, taskTitle, taskDescription' },
        { status: 400 }
      );
    }

    console.log(`API: process-pmo-task - Processing task "${taskTitle}"`);
    console.log(`  - Team Assignment: ${teamAssignment || 'none'}`);
    console.log(`  - Assigned Users: [${assignedUsers?.join(', ') || 'none'}]`);
    console.log(`  - Category: ${category || 'none'}`);

    // Get the project to find PMO metadata
    const projectRef = adminDb.collection('projects').doc(projectId);
    const projectDoc = await projectRef.get();

    if (!projectDoc.exists) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    const projectData = projectDoc.data();
    const pmoId = projectData?.pmoId;
    const strategyType = projectData?.strategyType;

    if (!pmoId) {
      return NextResponse.json(
        { error: 'No PMO ID found in project metadata' },
        { status: 400 }
      );
    }

    // Get PMO record to access source documents
    const pmoDocPath = `users/${session.user.email}/PMO/${pmoId}`;
    const pmoDoc = await adminDb.doc(pmoDocPath).get();

    if (!pmoDoc.exists) {
      return NextResponse.json(
        { error: 'PMO record not found' },
        { status: 404 }
      );
    }

    const pmoData = pmoDoc.data();
    const sourceCategory = pmoData?.category || category;

    // Route to appropriate agent based on team assignment and category
    let processingResult;

    // Determine routing based on team assignment, category, or assigned users
    const routingInfo = determineAgentRouting(teamAssignment, category, assignedUsers);

    console.log(`API: process-pmo-task - Routing to: ${routingInfo.agentType} (${routingInfo.reason})`);

    switch (routingInfo.agentType) {
      case 'research':
        processingResult = await processWithResearchTeam({
          taskTitle,
          taskDescription,
          sourceCategory,
          pmoId,
          strategyType,
          userId: session.user.email,
          assignedUsers
        });
        break;

      case 'marketing':
        processingResult = await processWithMarketingTeam({
          taskTitle,
          taskDescription,
          sourceCategory,
          pmoId,
          strategyType,
          userId: session.user.email,
          assignedUsers
        });
        break;

      case 'software':
        processingResult = await processWithSoftwareDesignTeam({
          taskTitle,
          taskDescription,
          sourceCategory,
          pmoId,
          strategyType,
          userId: session.user.email,
          assignedUsers
        });
        break;

      case 'sales':
        processingResult = await processWithSalesTeam({
          taskTitle,
          taskDescription,
          sourceCategory,
          pmoId,
          strategyType,
          userId: session.user.email,
          assignedUsers
        });
        break;

      default:
        processingResult = {
          success: true,
          message: `Task "${taskTitle}" has been queued for processing. Agent routing: ${routingInfo.reason}`,
          details: `Task will be processed by the appropriate team. Routing info: ${routingInfo.agentType}`
        };
    }

    console.log(`API: process-pmo-task - Processing completed for task "${taskTitle}"`);

    return NextResponse.json({
      success: true,
      message: processingResult.message,
      details: processingResult.details,
      taskId,
      teamAssignment,
      processingTimestamp: new Date().toISOString()
    });

  } catch (error: any) {
    console.error('API: process-pmo-task - Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process PMO task',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// Agent routing logic
function determineAgentRouting(teamAssignment?: string, category?: string, assignedUsers?: string[]) {
  const team = (teamAssignment || '').toLowerCase();
  const cat = (category || '').toLowerCase();

  // Priority 1: Team assignment
  if (team.includes('research')) {
    return { agentType: 'research', reason: `Team assignment: ${teamAssignment}` };
  }
  if (team.includes('marketing')) {
    return { agentType: 'marketing', reason: `Team assignment: ${teamAssignment}` };
  }
  if (team.includes('software') || team.includes('design') || team.includes('development')) {
    return { agentType: 'software', reason: `Team assignment: ${teamAssignment}` };
  }
  if (team.includes('sales')) {
    return { agentType: 'sales', reason: `Team assignment: ${teamAssignment}` };
  }

  // Priority 2: Category-based routing
  if (cat.includes('research') || cat.includes('analysis')) {
    return { agentType: 'research', reason: `Category: ${category}` };
  }
  if (cat.includes('content') || cat.includes('marketing')) {
    return { agentType: 'marketing', reason: `Category: ${category}` };
  }
  if (cat.includes('implementation') || cat.includes('development')) {
    return { agentType: 'software', reason: `Category: ${category}` };
  }
  if (cat.includes('strategy')) {
    return { agentType: 'marketing', reason: `Category: ${category}` };
  }
  if (cat.includes('quality')) {
    return { agentType: 'software', reason: `Category: ${category}` };
  }

  // Default
  return { agentType: 'admin', reason: 'No specific routing match found' };
}

// Research Team processing
async function processWithResearchTeam(params: {
  taskTitle: string;
  taskDescription: string;
  sourceCategory: string;
  pmoId: string;
  strategyType: string;
  userId: string;
  assignedUsers?: string[];
}) {
  // TODO: Implement Research Team agent processing with document access
  // This will use QueryDocumentsAgent and QuestionAnswerAgent
  console.log(`Processing with Research Team: ${params.taskTitle}`);
  console.log(`  - Assigned Users: [${params.assignedUsers?.join(', ') || 'none'}]`);
  console.log(`  - Source Category: ${params.sourceCategory}`);
  console.log(`  - PMO ID: ${params.pmoId}`);

  return {
    success: true,
    message: `Research Team is processing task: "${params.taskTitle}". Document analysis and research synthesis in progress.`,
    details: `Task assigned to: ${params.assignedUsers?.join(', ') || 'admin'}. Processing using source documents from category: ${params.sourceCategory}`
  };
}

// Marketing Team processing
async function processWithMarketingTeam(params: {
  taskTitle: string;
  taskDescription: string;
  sourceCategory: string;
  pmoId: string;
  strategyType: string;
  userId: string;
  assignedUsers?: string[];
}) {
  // TODO: Implement Marketing Team agent processing with document access
  console.log(`Processing with Marketing Team: ${params.taskTitle}`);
  console.log(`  - Assigned Users: [${params.assignedUsers?.join(', ') || 'none'}]`);

  return {
    success: true,
    message: `Marketing Team is processing task: "${params.taskTitle}". Content creation and strategy development in progress.`,
    details: `Task assigned to: ${params.assignedUsers?.join(', ') || 'admin'}. Processing using source documents from category: ${params.sourceCategory}`
  };
}

// Software Design Team processing
async function processWithSoftwareDesignTeam(params: {
  taskTitle: string;
  taskDescription: string;
  sourceCategory: string;
  pmoId: string;
  strategyType: string;
  userId: string;
  assignedUsers?: string[];
}) {
  // TODO: Implement Software Design Team agent processing
  console.log(`Processing with Software Design Team: ${params.taskTitle}`);
  console.log(`  - Assigned Users: [${params.assignedUsers?.join(', ') || 'none'}]`);

  return {
    success: true,
    message: `Software Design Team is processing task: "${params.taskTitle}". Technical implementation and design in progress.`,
    details: `Task assigned to: ${params.assignedUsers?.join(', ') || 'admin'}. Processing using source documents from category: ${params.sourceCategory}`
  };
}

// Sales Team processing
async function processWithSalesTeam(params: {
  taskTitle: string;
  taskDescription: string;
  sourceCategory: string;
  pmoId: string;
  strategyType: string;
  userId: string;
  assignedUsers?: string[];
}) {
  // TODO: Implement Sales Team agent processing
  console.log(`Processing with Sales Team: ${params.taskTitle}`);
  console.log(`  - Assigned Users: [${params.assignedUsers?.join(', ') || 'none'}]`);

  return {
    success: true,
    message: `Sales Team is processing task: "${params.taskTitle}". Sales strategy and execution in progress.`,
    details: `Task assigned to: ${params.assignedUsers?.join(', ') || 'admin'}. Processing using source documents from category: ${params.sourceCategory}`
  };
}

export async function GET() {
  // Return API documentation
  return NextResponse.json({
    endpoint: '/api/process-pmo-task',
    description: 'Processes PMO-generated tasks by routing them to appropriate agent teams',
    methods: ['POST'],
    parameters: {
      taskId: {
        type: 'string',
        required: true,
        description: 'The ID of the task to process'
      },
      taskTitle: {
        type: 'string',
        required: true,
        description: 'The title of the task'
      },
      taskDescription: {
        type: 'string',
        required: true,
        description: 'The description of the task'
      },
      teamAssignment: {
        type: 'string',
        required: false,
        description: 'The team assigned to this task (e.g., "Research Team", "Marketing Team")'
      },
      assignedUsers: {
        type: 'array',
        required: false,
        description: 'Array of user IDs assigned to this task'
      },
      projectId: {
        type: 'string',
        required: true,
        description: 'The ID of the project this task belongs to'
      },
      category: {
        type: 'string',
        required: false,
        description: 'The category of the task for document access'
      }
    },
    response: {
      success: 'boolean',
      message: 'string - Processing status message',
      details: 'string - Additional processing details',
      taskId: 'string',
      teamAssignment: 'string',
      processingTimestamp: 'string'
    }
  });
}
