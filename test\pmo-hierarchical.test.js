// test/pmo-hierarchical.test.js

/**
 * Test suite for PMO Hierarchical Structure
 *
 * This test file validates the hierarchical PMO structure implementation
 * including migration, validation, and data access functions.
 */

const {
  getHierarchicalPMOData,
  addTaskToProject,
  createProjectDocument,
  getProjectTasks
} = require('../lib/firebase/pmoHierarchical');

const {
  migratePMOToHierarchical,
  validateHierarchicalStructure
} = require('../lib/firebase/pmoMigration');

// Test configuration
const TEST_CONFIG = {
  userId: '<EMAIL>',
  pmoId: 'c76670a7-bc7b-44ea-9905-189a4bcf36c8', // Replace with actual PMO ID for testing
  projectId: 'AMIp7WUTE26kDnzZC84T', // Replace with actual project ID for testing
  taskIds: ['klSkNTXipB3RGG0kTmHG', 'gtNIONKWHizYNemHBQRs'] // Replace with actual task IDs
};

/**
 * Test hierarchical data retrieval
 */
async function testGetHierarchicalData() {
  console.log('🧪 Testing getHierarchicalPMOData...');

  try {
    const result = await getHierarchicalPMOData(TEST_CONFIG.userId, TEST_CONFIG.pmoId);

    console.log(`  Result: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (result.success && result.data) {
      console.log(`  Projects found: ${result.data.length}`);
      result.data.forEach((project, index) => {
        console.log(`    Project ${index + 1}: ${project.projectId} (${project.taskIds.length} tasks)`);
      });
    } else {
      console.log(`  Error: ${result.error}`);
    }

    return result.success;
  } catch (error) {
    console.log(`  ❌ EXCEPTION: ${error.message}`);
    return false;
  }
}

/**
 * Test project tasks retrieval
 */
async function testGetProjectTasks() {
  console.log('🧪 Testing getProjectTasks...');

  try {
    const result = await getProjectTasks(TEST_CONFIG.userId, TEST_CONFIG.pmoId, TEST_CONFIG.projectId);

    console.log(`  Result: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (result.success) {
      console.log(`  Tasks found: ${result.taskIds?.length || 0}`);
      if (result.taskIds && result.taskIds.length > 0) {
        console.log(`  Task IDs: ${result.taskIds.slice(0, 3).join(', ')}${result.taskIds.length > 3 ? '...' : ''}`);
      }
    } else {
      console.log(`  Error: ${result.error}`);
    }

    return result.success;
  } catch (error) {
    console.log(`  ❌ EXCEPTION: ${error.message}`);
    return false;
  }
}

/**
 * Test validation
 */
async function testValidation() {
  console.log('🧪 Testing validateHierarchicalStructure...');

  try {
    const result = await validateHierarchicalStructure(TEST_CONFIG.userId, TEST_CONFIG.pmoId);

    console.log(`  Valid: ${result.isValid ? '✅ YES' : '❌ NO'}`);
    console.log(`  Has Hierarchical: ${result.details.hasHierarchicalStructure ? '✅' : '❌'}`);
    console.log(`  Has Legacy: ${result.details.hasLegacyStructure ? '✅' : '❌'}`);
    console.log(`  Projects: ${result.details.projectCount}`);
    console.log(`  Tasks: ${result.details.taskCount}`);

    if (result.issues.length > 0) {
      console.log(`  Issues:`);
      result.issues.forEach(issue => {
        console.log(`    - ${issue}`);
      });
    }

    if (result.details.orphanedTasks.length > 0) {
      console.log(`  Orphaned Tasks: ${result.details.orphanedTasks.join(', ')}`);
    }

    return result.isValid;
  } catch (error) {
    console.log(`  ❌ EXCEPTION: ${error.message}`);
    return false;
  }
}

/**
 * Test migration (dry run)
 */
async function testMigration() {
  console.log('🧪 Testing migratePMOToHierarchical (dry run)...');

  try {
    const result = await migratePMOToHierarchical(TEST_CONFIG.userId, TEST_CONFIG.pmoId, true);

    console.log(`  Result: ${result.success ? '✅ SUCCESS' : '❌ FAILED'}`);

    if (result.success && result.details) {
      console.log(`  Projects: ${result.details.projectCount}`);
      console.log(`  Tasks: ${result.details.taskCount}`);

      if (result.details.mapping) {
        console.log(`  Mapping Preview:`);
        Object.entries(result.details.mapping).forEach(([projectId, projectData]) => {
          console.log(`    ${projectId}: ${projectData.taskIds.length} tasks`);
        });
      }
    } else {
      console.log(`  Error: ${result.error}`);
    }

    return result.success;
  } catch (error) {
    console.log(`  ❌ EXCEPTION: ${error.message}`);
    return false;
  }
}

/**
 * Test creating project document and adding tasks
 */
async function testAddProjectTaskMapping() {
  console.log('🧪 Testing createProjectDocument and addTaskToProject...');

  const testProjectInfo = {
    id: TEST_CONFIG.projectId,
    name: 'Test Project',
    createdAt: new Date(),
    status: 'Active'
  };

  const testTaskData = {
    projectId: TEST_CONFIG.projectId,
    title: 'Test Task',
    description: 'Test task description',
    category: 'Testing',
    status: 'Not Started',
    startDate: new Date(),
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    assignedTo: ['test-user'],
    priority: 'High',
    dependencies: [],
    notes: 'Test task notes',
    createdBy: 'test-agent'
  };

  try {
    // Note: This is a read-only test - we won't actually modify data
    console.log(`  Would create project document for ${TEST_CONFIG.projectId}`);
    console.log(`  Project Info: ${testProjectInfo.name} (${testProjectInfo.status})`);
    console.log(`  Would add ${TEST_CONFIG.taskIds.length} tasks to project subcollection`);
    console.log(`  Task IDs: ${TEST_CONFIG.taskIds.join(', ')}`);
    console.log(`  Sample Task: ${testTaskData.title} - ${testTaskData.description}`);
    console.log(`  ⚠️ Skipping actual write operations in test mode`);

    return true;
  } catch (error) {
    console.log(`  ❌ EXCEPTION: ${error.message}`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting PMO Hierarchical Structure Tests');
  console.log('=' .repeat(60));
  console.log(`Test Configuration:`);
  console.log(`  User ID: ${TEST_CONFIG.userId}`);
  console.log(`  PMO ID: ${TEST_CONFIG.pmoId}`);
  console.log(`  Project ID: ${TEST_CONFIG.projectId}`);
  console.log(`  Task IDs: ${TEST_CONFIG.taskIds.join(', ')}`);
  console.log('');

  const tests = [
    { name: 'Hierarchical Data Retrieval', fn: testGetHierarchicalData },
    { name: 'Project Tasks Retrieval', fn: testGetProjectTasks },
    { name: 'Structure Validation', fn: testValidation },
    { name: 'Migration (Dry Run)', fn: testMigration },
    { name: 'Create Project and Add Tasks', fn: testAddProjectTaskMapping }
  ];

  const results = [];

  for (const test of tests) {
    console.log(`\n📋 ${test.name}`);
    console.log('-' .repeat(40));

    const startTime = Date.now();
    const success = await test.fn();
    const duration = Date.now() - startTime;

    results.push({ name: test.name, success, duration });

    console.log(`  Duration: ${duration}ms`);
  }

  // Summary
  console.log('\n📊 Test Summary');
  console.log('=' .repeat(60));

  const passed = results.filter(r => r.success).length;
  const total = results.length;

  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`  ${status} ${result.name} (${result.duration}ms)`);
  });

  console.log(`\n🎯 Overall Result: ${passed}/${total} tests passed`);

  if (passed === total) {
    console.log('🎉 All tests passed! The hierarchical structure is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the implementation and configuration.');
  }

  return passed === total;
}

/**
 * Main execution
 */
async function main() {
  try {
    const success = await runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  runAllTests,
  testGetHierarchicalData,
  testGetProjectTasks,
  testValidation,
  testMigration,
  testAddProjectTaskMapping,
  TEST_CONFIG
};
