import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '../../../components/firebase/admin';

export async function GET(req: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(req.url);
    const requestId = url.searchParams.get('requestId');
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const page = parseInt(url.searchParams.get('page') || '1', 10);

    // If requestId is provided and is not the string "undefined", get a specific document
    if (requestId && requestId !== "undefined") {
      console.log(`[Agent Outputs API] Fetching specific document with ID: ${requestId}`);
      const docRef = adminDb.collection('Agent_Output').doc(requestId);
      const doc = await docRef.get();

      if (!doc.exists) {
        return NextResponse.json(
          { error: 'Document not found' },
          { status: 404 }
        );
      }

      const data = doc.data();

      if (!data) {
        return NextResponse.json(
          { error: 'Document data not found' },
          { status: 404 }
        );
      }

      // Convert Firebase timestamps to JavaScript Date objects
      const convertTimestamp = (timestamp: any): Date | null => {
        if (!timestamp) return null;
        if (timestamp.toDate && typeof timestamp.toDate === 'function') {
          return timestamp.toDate();
        }
        if (timestamp.seconds && typeof timestamp.seconds === 'number') {
          return new Date(timestamp.seconds * 1000);
        }
        if (timestamp._seconds && typeof timestamp._seconds === 'number') {
          return new Date(timestamp._seconds * 1000);
        }
        // Try to parse as date string or number
        try {
          return new Date(timestamp);
        } catch {
          return null;
        }
      };

      return NextResponse.json({
        id: doc.id,
        ...data,
        timestamp: convertTimestamp(data.timestamp),
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt)
      });
    }

    // Otherwise, get a paginated list of documents
    // Get all documents and sort them in the application to handle missing timestamp fields
    console.log(`[Agent Outputs API] Fetching page ${page} with limit ${limit}`);
    const allDocsSnapshot = await adminDb.collection('Agent_Output').get();
    console.log(`[Agent Outputs API] Found ${allDocsSnapshot.docs.length} total documents`);

    if (allDocsSnapshot.empty) {
      return NextResponse.json({
        results: [],
        hasMore: false,
        page,
        limit,
        lastTimestamp: null
      });
    }

    // Convert all documents and sort them by timestamp (with fallbacks)
    const allOutputs = allDocsSnapshot.docs.map(doc => {
      const data = doc.data();

      if (!data) {
        console.warn(`Document ${doc.id} has no data, skipping`);
        return null;
      }

      // Convert Firebase timestamps to JavaScript Date objects
      const convertTimestamp = (timestamp: any): Date | null => {
        if (!timestamp) return null;

        // Handle Firestore Timestamp objects
        if (timestamp.toDate && typeof timestamp.toDate === 'function') {
          return timestamp.toDate();
        }

        // Handle serialized Firestore Timestamp objects
        if (timestamp.seconds && typeof timestamp.seconds === 'number') {
          return new Date(timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1000000);
        }

        // Handle serialized Firestore Timestamp objects (with underscore)
        if (timestamp._seconds && typeof timestamp._seconds === 'number') {
          return new Date(timestamp._seconds * 1000 + (timestamp._nanoseconds || 0) / 1000000);
        }

        // Try to parse as date string or number
        try {
          const date = new Date(timestamp);
          return isNaN(date.getTime()) ? null : date;
        } catch {
          return null;
        }
      };

      const convertedData = {
        id: doc.id,
        ...data,
        timestamp: convertTimestamp(data.timestamp),
        createdAt: convertTimestamp(data.createdAt),
        updatedAt: convertTimestamp(data.updatedAt)
      };

      // Debug logging for BusinessAnalysis and Investigative Research outputs
      if (data.agentType === 'BusinessAnalysis' ||
          (data.agentType && data.agentType.startsWith('Investigative Research'))) {
        console.log(`[API_TIMESTAMP_DEBUG] ${data.agentType} output ${doc.id}:`, {
          originalTimestamp: data.timestamp,
          originalCreatedAt: data.createdAt,
          convertedTimestamp: convertedData.timestamp,
          convertedCreatedAt: convertedData.createdAt
        });
      }

      // Special logging for the missing document
      if (doc.id === 'a61d9cd1-490a-4c21-b17d-b326ae390075') {
        console.log(`[MISSING_DOC_DEBUG] Found missing document ${doc.id}:`, {
          agentType: data.agentType,
          title: data.title,
          timestamp: data.timestamp,
          createdAt: data.createdAt,
          convertedTimestamp: convertedData.timestamp,
          convertedCreatedAt: convertedData.createdAt
        });
      }

      return convertedData;
    }).filter((item): item is NonNullable<typeof item> => Boolean(item)); // Remove null entries with type guard

    // Sort by timestamp (desc), then by createdAt (desc), then by document ID
    allOutputs.sort((a, b) => {
      // Primary sort: timestamp (most recent first)
      const aTime = a.timestamp || a.createdAt;
      const bTime = b.timestamp || b.createdAt;

      // Handle null/invalid dates - put them at the end
      const aValid = aTime && !isNaN(aTime.getTime());
      const bValid = bTime && !isNaN(bTime.getTime());

      if (aValid && !bValid) return -1; // a comes first (has valid date)
      if (!aValid && bValid) return 1;  // b comes first (has valid date)
      if (!aValid && !bValid) {
        // Both invalid, sort by document ID
        return b.id.localeCompare(a.id);
      }

      // Both have valid dates, sort by time
      // TypeScript safety: we've already checked that both are valid
      const aTimeValue = aTime!.getTime();
      const bTimeValue = bTime!.getTime();

      if (aTimeValue !== bTimeValue) {
        return bTimeValue - aTimeValue; // Descending
      }

      // Secondary sort: document ID (for consistency)
      return b.id.localeCompare(a.id);
    });

    console.log(`[Agent Outputs API] After sorting, total documents: ${allOutputs.length}`);

    // Check if the target document is in the sorted list
    const targetDocIndex = allOutputs.findIndex(doc => doc.id === 'd3678d04-17ca-4b0e-ade1-fe65510c421f');
    if (targetDocIndex >= 0) {
      console.log(`[TARGET_DOC_DEBUG] Target document d3678d04-17ca-4b0e-ade1-fe65510c421f found at index ${targetDocIndex} in sorted list`);
      const targetDoc = allOutputs[targetDocIndex];
      console.log(`[TARGET_DOC_DEBUG] Target document timestamp: ${targetDoc.timestamp}, createdAt: ${targetDoc.createdAt}`);
    } else {
      console.log(`[TARGET_DOC_DEBUG] Target document d3678d04-17ca-4b0e-ade1-fe65510c421f NOT found in sorted list`);
    }

    // Check if the missing document is in the sorted list
    const missingDocIndex = allOutputs.findIndex(doc => doc.id === 'a61d9cd1-490a-4c21-b17d-b326ae390075');
    if (missingDocIndex >= 0) {
      console.log(`[MISSING_DOC_DEBUG] Missing document found at index ${missingDocIndex} in sorted list`);
    } else {
      console.log(`[MISSING_DOC_DEBUG] Missing document NOT found in sorted list`);
    }

    // Implement pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedOutputs = allOutputs.slice(startIndex, endIndex);
    const hasMore = endIndex < allOutputs.length;

    console.log(`[Agent Outputs API] Pagination: startIndex=${startIndex}, endIndex=${endIndex}, returning ${paginatedOutputs.length} results`);

    // Check if the target document is in the current page
    const targetDocInPage = paginatedOutputs.find(doc => doc.id === 'd3678d04-17ca-4b0e-ade1-fe65510c421f');
    if (targetDocInPage) {
      console.log(`[TARGET_DOC_DEBUG] Target document IS in current page ${page}`);
    } else if (targetDocIndex >= 0) {
      const targetDocPage = Math.floor(targetDocIndex / limit) + 1;
      console.log(`[TARGET_DOC_DEBUG] Target document would be on page ${targetDocPage} (index ${targetDocIndex})`);
    }

    // Check if the missing document is in the current page
    const missingDocInPage = paginatedOutputs.find(doc => doc.id === 'a61d9cd1-490a-4c21-b17d-b326ae390075');
    if (missingDocInPage) {
      console.log(`[MISSING_DOC_DEBUG] Missing document IS in current page ${page}`);
    } else if (missingDocIndex >= 0) {
      const missingDocPage = Math.floor(missingDocIndex / limit) + 1;
      console.log(`[MISSING_DOC_DEBUG] Missing document would be on page ${missingDocPage} (index ${missingDocIndex})`);
    }

    // Get the last timestamp for pagination cursor (from the last item in current page)
    const lastItem = paginatedOutputs[paginatedOutputs.length - 1];
    const lastTimestamp = lastItem ? (lastItem.timestamp || lastItem.createdAt) : null;

    return NextResponse.json({
      results: paginatedOutputs,
      hasMore,
      page,
      limit,
      lastTimestamp: lastTimestamp ? JSON.stringify(lastTimestamp) : null
    });
  } catch (error) {
    console.error('Error retrieving agent outputs:', error);

    return NextResponse.json(
      {
        error: 'Failed to retrieve agent outputs',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
