// lib/validation/semanticCheck.ts

import { ChatGroq } from "@langchain/groq";
import { createGroqClient } from "lib/llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "@/api/auth/[...nextauth]/authOptions";

/**
 * Type guard to check if an object has a 'content' property of type string.
 */
function hasContent(obj: any): obj is { content: string } {
  return obj && typeof obj.content === 'string';
}

/**
 * Type guard to check if an object has a 'text' property of type string.
 */
function hasText(obj: any): obj is { text: string } {
  return obj && typeof obj.text === 'string';
}

/**
 * Performs a semantic check to verify if the content matches the expected format.
 * @param content - The content to validate.
 * @param queryType - The type of data ("Clients" or "Recipes").
 * @returns A boolean indicating if the content is semantically valid.
 */
export async function performSemanticCheck(content: string, queryType: string): Promise<boolean> {
  const session = await getServerSession(authOptions);
    
  if (!session?.user?.email) {
    throw new Error("Unauthorized - No valid session");
  }
  
  // Initialize Groq client with user's email for API key selection
  const groqClient = createGroqClient({ userEmail: session.user.email });

  const model = new ChatGroq({
    temperature: 0,
    model: process.env.GROQ_MODEL!, // Ensure you have a GROQ_MODEL environment variable
    apiKey: groqClient.apiKey!,
  });

  const prompt = `Does the following data match the format of ${queryType}? Please respond with "true" or "false".

${content}`;

  try {
    // Invoke the model and get the response
    const result = await model.invoke(prompt);

    // Log the full response to inspect its structure
    console.log("Groq response:", result);

    let responseText: string = "";

    // Use type guards to safely extract the response text
    if (hasContent(result)) {
      responseText = result.content;
    } else if (hasText(result)) {
      responseText = result.text;
    } else if (typeof result === 'string') {
      responseText = result;
    } else {
      console.warn("Unexpected response structure. Unable to extract response text.");
    }

    // Ensure responseText is a string before proceeding
    if (typeof responseText !== 'string') {
      console.warn("Response text is not a string. Defaulting to 'false'.");
      return false;
    }

    // Perform a case-insensitive check for "true" in the response
    return responseText.toLowerCase().includes("true");
  } catch (error) {
    console.error(`Semantic check failed for ${queryType}:`, error);
    return false;
  }
}
