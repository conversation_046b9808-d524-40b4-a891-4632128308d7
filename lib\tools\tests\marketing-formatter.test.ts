import { contentFormatterTool } from '../content-formatter';

/**
 * This is a simple test file to verify that the content formatter works correctly with marketing options.
 * You can run this test with:
 * 
 * ```
 * npx ts-node lib/tools/tests/marketing-formatter.test.ts
 * ```
 */

async function testMarketingFormatter() {
  console.log('Testing content formatter with marketing options...');
  
  const testContent = `
  <html>
    <head>
      <title>Cloud Computing Solutions - Enterprise Cloud Services</title>
    </head>
    <body>
      <header>
        <nav>
          <ul>
            <li><a href="/">Home</a></li>
            <li><a href="/products">Products</a></li>
            <li><a href="/contact">Contact</a></li>
          </ul>
        </nav>
      </header>
      <main>
        <h1>Enterprise Cloud Solutions</h1>
        <p>Transform your business with our cutting-edge cloud computing solutions designed for enterprise needs.</p>
        
        <section>
          <h2>Our Cloud Services</h2>
          <ul>
            <li>
              <h3>Cloud Infrastructure</h3>
              <p>Scalable and secure infrastructure with 99.9% uptime guarantee. Our infrastructure is built on the latest technology to ensure performance and reliability.</p>
            </li>
            <li>
              <h3>Data Analytics</h3>
              <p>Turn your data into actionable insights with our advanced analytics platform. Make better business decisions with real-time data processing.</p>
            </li>
            <li>
              <h3>AI Solutions</h3>
              <p>Leverage the power of artificial intelligence to automate processes and discover new opportunities for growth.</p>
            </li>
          </ul>
        </section>
        
        <section>
          <h2>Why Choose Us</h2>
          <ul>
            <li>Industry-leading security protocols</li>
            <li>24/7 technical support</li>
            <li>Flexible pricing models</li>
            <li>Seamless integration with existing systems</li>
          </ul>
        </section>
        
        <section>
          <h2>Client Testimonials</h2>
          <blockquote>
            "Implementing their cloud solution reduced our operational costs by 35% while improving system performance." - John Smith, CTO at TechCorp
          </blockquote>
        </section>
      </main>
      <footer>
        <p>Copyright 2023 - Cloud Solutions Inc.</p>
      </footer>
    </body>
  </html>
  `;
  
  const testUrl = 'https://example.com/cloud-solutions';
  
  try {
    // Test with default marketing options
    console.log('Testing with default marketing options...');
    const defaultResult = await contentFormatterTool.formatContent(testContent, testUrl);
    console.log('Default marketing result:');
    console.log(defaultResult);
    
    // Test with specific marketing options
    console.log('\nTesting with specific marketing options...');
    const marketingResult = await contentFormatterTool.formatContent(testContent, testUrl, 'markdown', {
      domain: 'technology marketing',
      subjectMatter: 'cloud computing services',
      perspective: 'sales',
      tone: 'professional yet persuasive',
      audience: 'enterprise IT decision-makers',
      focus: 'ROI and competitive advantages',
      contentGoal: 'persuade potential customers to request a demo',
    });
    console.log('Specific marketing result:');
    console.log(marketingResult);
    
    // Test with technical perspective
    console.log('\nTesting with technical perspective...');
    const technicalResult = await contentFormatterTool.formatContent(testContent, testUrl, 'markdown', {
      domain: 'technical',
      subjectMatter: 'cloud infrastructure',
      perspective: 'technical',
      audience: 'IT professionals and system architects',
      focus: 'technical capabilities and implementation details',
    });
    console.log('Technical perspective result:');
    console.log(technicalResult);
    
    console.log('\nTests completed successfully!');
  } catch (error) {
    console.error('Error during testing:', error);
  }
}

// Run the test
testMarketingFormatter();
