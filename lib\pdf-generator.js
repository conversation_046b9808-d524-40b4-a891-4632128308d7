import PDFDocument from "pdfkit"

/**
 * Generates a PDF from processed content
 */
export async function generatePdf(contents) {
  return new Promise((resolve, reject) => {
    try {
      // Create a new PDF document
      const doc = new PDFDocument({
        margin: 50,
        size: "A4",
      })

      // Collect the PDF data chunks
      const chunks = []
      doc.on("data", (chunk) => chunks.push(chunk))
      doc.on("end", () => resolve(Buffer.concat(chunks)))

      // Add cover page
      addCoverPage(doc, contents)

      // Add table of contents
      addTableOfContents(doc, contents)

      // Add content pages
      for (const content of contents) {
        addContentPage(doc, content)
      }

      // Finalize the PDF
      doc.end()
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * Adds a cover page to the PDF
 */
function addCoverPage(doc, contents) {
  // Title
  doc
    .fontSize(24)
    .font("Helvetica-Bold")
    .text("Web Content Extraction Report", {
      align: "center",
    })
    .moveDown(2)

  // Date
  doc
    .fontSize(12)
    .font("Helvetica")
    .text(`Generated on: ${new Date().toLocaleString()}`, {
      align: "center",
    })
    .moveDown(2)

  // URLs processed
  doc
    .fontSize(14)
    .font("Helvetica-Bold")
    .text("URLs Processed:", {
      align: "left",
    })
    .moveDown(0.5)

  contents.forEach((content, index) => {
    doc
      .fontSize(10)
      .font("Helvetica")
      .text(`${index + 1}. ${content.url}`, {
        align: "left",
      })
      .moveDown(0.5)
  })

  // Add a new page for the table of contents
  doc.addPage()
}

/**
 * Adds a table of contents to the PDF
 */
function addTableOfContents(doc, contents) {
  doc
    .fontSize(18)
    .font("Helvetica-Bold")
    .text("Table of Contents", {
      align: "center",
    })
    .moveDown(2)

  contents.forEach((content, index) => {
    // Extract title from markdown content (first heading)
    const titleMatch = content.content.match(/^# (.+)$/m)
    const title = titleMatch ? titleMatch[1] : `Content from ${content.url}`

    doc
      .fontSize(12)
      .font("Helvetica")
      .text(`${index + 1}. ${title}`, {
        align: "left",
        link: `#section-${index + 1}`,
      })
      .moveDown(0.5)
  })

  // Add a new page for the first content section
  doc.addPage()
}

/**
 * Adds a content page to the PDF
 */
function addContentPage(doc, content) {
  // Add a destination for the table of contents link
  doc.addNamedDestination(`section-${doc.page.pageNumber}`)

  if (!content.success) {
    doc
      .fontSize(16)
      .font("Helvetica-Bold")
      .text(`Failed to process: ${content.url}`, {
        align: "left",
      })
      .moveDown(1)

    doc.addPage()
    return
  }

  // Parse the markdown content
  const lines = content.content.split("\n")

  for (const line of lines) {
    // Handle headings
    if (line.startsWith("# ")) {
      doc
        .fontSize(20)
        .font("Helvetica-Bold")
        .text(line.substring(2), {
          align: "left",
        })
        .moveDown(1)
    } else if (line.startsWith("## ")) {
      doc
        .fontSize(16)
        .font("Helvetica-Bold")
        .text(line.substring(3), {
          align: "left",
        })
        .moveDown(0.5)
    } else if (line.startsWith("### ")) {
      doc
        .fontSize(14)
        .font("Helvetica-Bold")
        .text(line.substring(4), {
          align: "left",
        })
        .moveDown(0.5)
    }
    // Handle lists
    else if (line.startsWith("- ")) {
      doc
        .fontSize(12)
        .font("Helvetica")
        .text(`• ${line.substring(2)}`, {
          align: "left",
          indent: 20,
        })
        .moveDown(0.5)
    }
    // Handle numbered lists
    else if (/^\d+\.\s/.test(line)) {
      doc
        .fontSize(12)
        .font("Helvetica")
        .text(line, {
          align: "left",
          indent: 20,
        })
        .moveDown(0.5)
    }
    // Handle regular text
    else if (line.trim()) {
      doc
        .fontSize(12)
        .font("Helvetica")
        .text(line, {
          align: "left",
        })
        .moveDown(0.5)
    }
    // Handle empty lines
    else {
      doc.moveDown(0.5)
    }

    // Check if we need to add a new page
    if (doc.y > doc.page.height - 100) {
      doc.addPage()
    }
  }

  // Add URL source at the bottom of the last page
  doc.fontSize(10).font("Helvetica-Oblique").text(`Source: ${content.url}`, {
    align: "left",
  })

  // Add a new page for the next content section
  doc.addPage()
}

