'use client';

import React, { useState, useEffect } from 'react';
import {
  Target,
  Calendar,
  Users,
  BarChart,
  PlusCircle,
  Edit,
  Trash,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  Send,
  FileText,
  Twitter,
  Linkedin,
  Facebook,
  Instagram,
  Clock,
  Tag
} from 'lucide-react';
// Use API routes instead of direct library calls

/**
 * Campaign Manager component for creating and managing marketing campaigns
 */
export default function CampaignManager({ userId }) {
  const [campaigns, setCampaigns] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [expandedCampaign, setExpandedCampaign] = useState(null);
  const [selectedDocuments, setSelectedDocuments] = useState([]);
  const [availableDocuments, setAvailableDocuments] = useState([]);
  const [campaignForm, setCampaignForm] = useState({
    name: '',
    description: '',
    platforms: ['twitter', 'linkedin', 'facebook'],
    tone: 'professional',
    targetAudience: 'marketing professionals',
    callToAction: 'Learn more',
    calendarDays: 7,
  });

  // Fetch campaigns on component mount
  useEffect(() => {
    fetchCampaigns();
    fetchAvailableDocuments();
  }, [userId]);

  // Fetch user campaigns
  const fetchCampaigns = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the dedicated campaigns API route
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'getUserCampaigns',
          userId: userId || 'current-user'
        }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setCampaigns(result.campaigns);
      } else {
        setError(result.error || 'Failed to fetch campaigns');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while fetching campaigns');
    } finally {
      setLoading(false);
    }
  };

  // Fetch available documents
  const fetchAvailableDocuments = async () => {
    try {
      // This would be replaced with an actual API call to fetch documents
      // For now, we'll use mock data
      setAvailableDocuments([
        { id: 'doc1', name: 'Marketing Strategy', createdAt: new Date().toISOString() },
        { id: 'doc2', name: 'Product Launch Plan', createdAt: new Date().toISOString() },
        { id: 'doc3', name: 'SEO Analysis Report', createdAt: new Date().toISOString() },
        { id: 'doc4', name: 'Content Calendar Q3', createdAt: new Date().toISOString() },
      ]);
    } catch (err) {
      console.error('Error fetching documents:', err);
    }
  };

  // Handle campaign form change
  const handleCampaignFormChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === 'platforms') {
      // Handle platforms as a special case (multi-select)
      const platform = value;
      let updatedPlatforms = [...campaignForm.platforms];

      if (checked) {
        updatedPlatforms.push(platform);
      } else {
        updatedPlatforms = updatedPlatforms.filter(p => p !== platform);
      }

      setCampaignForm({
        ...campaignForm,
        platforms: updatedPlatforms,
      });
    } else {
      setCampaignForm({
        ...campaignForm,
        [name]: value,
      });
    }
  };

  // Toggle document selection
  const toggleDocumentSelection = (documentId) => {
    if (selectedDocuments.includes(documentId)) {
      setSelectedDocuments(selectedDocuments.filter(id => id !== documentId));
    } else {
      setSelectedDocuments([...selectedDocuments, documentId]);
    }
  };

  // Create a new campaign
  const handleCreateCampaign = async (e) => {
    e.preventDefault();

    if (!campaignForm.name) {
      setError('Campaign name is required');
      return;
    }

    if (selectedDocuments.length === 0) {
      setError('Please select at least one document');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Use the dedicated campaigns API route
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'createCampaign',
          campaignData: campaignForm,
          userId: userId || 'current-user',
          documentIds: selectedDocuments
        }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Reset form and refresh campaigns
        setCampaignForm({
          name: '',
          description: '',
          platforms: ['twitter', 'linkedin', 'facebook'],
          tone: 'professional',
          targetAudience: 'marketing professionals',
          callToAction: 'Learn more',
          calendarDays: 7,
        });
        setSelectedDocuments([]);
        setShowCreateForm(false);
        fetchCampaigns();
      } else {
        setError(result.error || 'Failed to create campaign');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while creating the campaign');
    } finally {
      setLoading(false);
    }
  };

  // Delete a campaign
  const handleDeleteCampaign = async (campaignId) => {
    if (!confirm('Are you sure you want to delete this campaign?')) {
      return;
    }

    try {
      setLoading(true);

      // Use the dedicated campaigns API route
      const response = await fetch('/api/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'deleteCampaign',
          campaignId
        }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        // Refresh campaigns
        fetchCampaigns();
      } else {
        setError(result.error || 'Failed to delete campaign');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while deleting the campaign');
    } finally {
      setLoading(false);
    }
  };

  // Toggle campaign expansion
  const toggleCampaignExpansion = (campaignId) => {
    if (expandedCampaign === campaignId) {
      setExpandedCampaign(null);
    } else {
      setExpandedCampaign(campaignId);
    }
  };

  // Platform icons and colors
  const platformConfig = {
    twitter: {
      icon: <Twitter size={16} />,
      color: 'text-blue-400',
      name: 'Twitter'
    },
    linkedin: {
      icon: <Linkedin size={16} />,
      color: 'text-blue-600',
      name: 'LinkedIn'
    },
    facebook: {
      icon: <Facebook size={16} />,
      color: 'text-blue-500',
      name: 'Facebook'
    },
    instagram: {
      icon: <Instagram size={16} />,
      color: 'text-pink-500',
      name: 'Instagram'
    },
  };

  // Render campaign creation form
  const renderCreateForm = () => (
    <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700 mb-6">
      <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
        <PlusCircle className="mr-2 text-green-400" size={20} />
        Create New Campaign
      </h2>

      <form onSubmit={handleCreateCampaign}>
        <div className="space-y-4">
          {/* Campaign details */}
          <div>
            <label className="block text-sm font-medium text-zinc-400 mb-1">
              Campaign Name*
            </label>
            <input
              type="text"
              name="name"
              value={campaignForm.name}
              onChange={handleCampaignFormChange}
              placeholder="e.g., Q3 Product Launch"
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-zinc-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-zinc-400 mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={campaignForm.description}
              onChange={handleCampaignFormChange}
              placeholder="Campaign description and goals..."
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-zinc-500 min-h-[100px]"
            />
          </div>

          {/* Platform selection */}
          <div>
            <label className="block text-sm font-medium text-zinc-400 mb-2">
              Platforms
            </label>
            <div className="flex flex-wrap gap-3">
              {Object.entries(platformConfig).map(([platform, config]) => (
                <label key={platform} className="flex items-center">
                  <input
                    type="checkbox"
                    name="platforms"
                    value={platform}
                    checked={campaignForm.platforms.includes(platform)}
                    onChange={handleCampaignFormChange}
                    className="h-4 w-4 rounded border-zinc-600 text-blue-600 focus:ring-blue-500 focus:ring-offset-zinc-900"
                  />
                  <span className="ml-2 flex items-center text-zinc-300 text-sm">
                    {config.icon}
                    <span className="ml-1">{config.name}</span>
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Campaign options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-1">
                Tone
              </label>
              <select
                name="tone"
                value={campaignForm.tone}
                onChange={handleCampaignFormChange}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="professional">Professional</option>
                <option value="casual">Casual</option>
                <option value="friendly">Friendly</option>
                <option value="authoritative">Authoritative</option>
                <option value="humorous">Humorous</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-1">
                Target Audience
              </label>
              <input
                type="text"
                name="targetAudience"
                value={campaignForm.targetAudience}
                onChange={handleCampaignFormChange}
                placeholder="e.g., marketing professionals"
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-zinc-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-1">
                Call to Action
              </label>
              <input
                type="text"
                name="callToAction"
                value={campaignForm.callToAction}
                onChange={handleCampaignFormChange}
                placeholder="e.g., Learn more"
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-zinc-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-zinc-400 mb-1">
                Content Calendar Days
              </label>
              <select
                name="calendarDays"
                value={campaignForm.calendarDays}
                onChange={handleCampaignFormChange}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="7">7 days</option>
                <option value="14">14 days</option>
                <option value="30">30 days</option>
              </select>
            </div>
          </div>

          {/* Document selection */}
          <div>
            <label className="block text-sm font-medium text-zinc-400 mb-2">
              Select Documents*
            </label>

            {availableDocuments.length > 0 ? (
              <div className="space-y-2 max-h-60 overflow-y-auto p-2 bg-zinc-800 rounded-md">
                {availableDocuments.map(doc => (
                  <div
                    key={doc.id}
                    className={`flex items-center p-2 rounded-md ${
                      selectedDocuments.includes(doc.id)
                        ? 'bg-blue-900/20 border border-blue-800'
                        : 'bg-zinc-800 hover:bg-zinc-700'
                    }`}
                  >
                    <input
                      type="checkbox"
                      id={`doc-${doc.id}`}
                      checked={selectedDocuments.includes(doc.id)}
                      onChange={() => toggleDocumentSelection(doc.id)}
                      className="h-4 w-4 rounded border-zinc-600 text-blue-600 focus:ring-blue-500 focus:ring-offset-zinc-900"
                    />
                    <label
                      htmlFor={`doc-${doc.id}`}
                      className="ml-2 flex-grow cursor-pointer"
                    >
                      <div className="text-sm text-zinc-200">{doc.name}</div>
                      <div className="text-xs text-zinc-500">
                        {new Date(doc.createdAt).toLocaleDateString()}
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 bg-zinc-800 rounded-md text-zinc-400 text-center">
                No documents available
              </div>
            )}
          </div>

          {/* Form actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={() => setShowCreateForm(false)}
              className="px-4 py-2 bg-zinc-800 text-zinc-300 rounded-md hover:bg-zinc-700 focus:outline-none"
            >
              Cancel
            </button>

            <button
              type="submit"
              disabled={loading || !campaignForm.name || selectedDocuments.length === 0}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-900 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <RefreshCw className="inline-block mr-2 animate-spin" size={16} />
                  Creating...
                </>
              ) : (
                'Create Campaign'
              )}
            </button>
          </div>
        </div>
      </form>

      {error && (
        <div className="mt-4 p-3 bg-red-900/20 border border-red-800 rounded-md text-red-200">
          <div className="flex items-start">
            <AlertTriangle className="mr-2 flex-shrink-0 mt-0.5" size={16} />
            <p>{error}</p>
          </div>
        </div>
      )}
    </div>
  );

  // Render campaign list
  const renderCampaignList = () => (
    <div className="space-y-4">
      {campaigns.length > 0 ? (
        campaigns.map(campaign => (
          <div
            key={campaign.id}
            className="bg-zinc-900 rounded-lg border border-zinc-700"
          >
            {/* Campaign header */}
            <div
              className="p-4 flex items-center justify-between cursor-pointer"
              onClick={() => toggleCampaignExpansion(campaign.id)}
            >
              <div className="flex items-center">
                <Target className="text-blue-400 mr-3" size={20} />
                <div>
                  <h3 className="text-lg font-medium text-white">{campaign.name}</h3>
                  <p className="text-sm text-zinc-400">
                    Created: {new Date(campaign.createdAt?.seconds * 1000 || campaign.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  {campaign.platforms?.map(platform => (
                    <span
                      key={platform}
                      className={`${platformConfig[platform]?.color}`}
                      title={platformConfig[platform]?.name}
                    >
                      {platformConfig[platform]?.icon}
                    </span>
                  ))}
                </div>

                <span className={`px-2 py-0.5 text-xs rounded-full ${
                  campaign.status === 'active'
                    ? 'bg-green-900/30 text-green-200'
                    : campaign.status === 'draft'
                    ? 'bg-amber-900/30 text-amber-200'
                    : 'bg-zinc-800 text-zinc-300'
                }`}>
                  {campaign.status || 'draft'}
                </span>

                {expandedCampaign === campaign.id ? (
                  <ChevronUp className="text-zinc-400" size={20} />
                ) : (
                  <ChevronDown className="text-zinc-400" size={20} />
                )}
              </div>
            </div>

            {/* Campaign details (expanded) */}
            {expandedCampaign === campaign.id && (
              <div className="border-t border-zinc-800 p-4">
                {/* Campaign description */}
                {campaign.description && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-zinc-400 mb-1">Description</h4>
                    <p className="text-zinc-300">{campaign.description}</p>
                  </div>
                )}

                {/* Campaign details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <h4 className="text-sm font-medium text-zinc-400 mb-1">Target Audience</h4>
                    <p className="text-zinc-300">{campaign.targetAudience || 'Not specified'}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-zinc-400 mb-1">Tone</h4>
                    <p className="text-zinc-300">{campaign.tone || 'Not specified'}</p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-zinc-400 mb-1">Call to Action</h4>
                    <p className="text-zinc-300">{campaign.callToAction || 'Not specified'}</p>
                  </div>
                </div>

                {/* Campaign actions */}
                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => handleDeleteCampaign(campaign.id)}
                    className="p-2 text-red-400 hover:text-red-300 focus:outline-none"
                    title="Delete campaign"
                  >
                    <Trash size={16} />
                  </button>

                  <button
                    onClick={() => {/* Handle edit campaign */}}
                    className="p-2 text-blue-400 hover:text-blue-300 focus:outline-none"
                    title="Edit campaign"
                  >
                    <Edit size={16} />
                  </button>

                  <button
                    onClick={() => {/* Handle view analytics */}}
                    className="p-2 text-purple-400 hover:text-purple-300 focus:outline-none"
                    title="View analytics"
                  >
                    <BarChart size={16} />
                  </button>

                  <button
                    onClick={() => {/* Handle view content calendar */}}
                    className="p-2 text-green-400 hover:text-green-300 focus:outline-none"
                    title="View content calendar"
                  >
                    <Calendar size={16} />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))
      ) : (
        <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
          <Target className="mx-auto mb-4 text-zinc-600" size={32} />
          <h3 className="text-lg font-semibold mb-2 text-zinc-300">No Campaigns Found</h3>
          <p>Create your first marketing campaign to get started.</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="campaign-manager space-y-6">
      {/* Header with create button */}
      <div className="flex items-center justify-between bg-zinc-900 p-4 rounded-lg border border-zinc-700">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <Target className="mr-2 text-blue-400" size={20} />
          Marketing Campaigns
        </h2>

        <button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-900 flex items-center"
        >
          {showCreateForm ? (
            <>
              <ChevronUp className="mr-1" size={16} />
              Hide Form
            </>
          ) : (
            <>
              <PlusCircle className="mr-1" size={16} />
              New Campaign
            </>
          )}
        </button>
      </div>

      {/* Create campaign form */}
      {showCreateForm && renderCreateForm()}

      {/* Campaign list */}
      {loading && campaigns.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-8 bg-zinc-900 rounded-lg border border-zinc-700 h-64">
          <RefreshCw className="animate-spin text-blue-400 mb-4" size={32} />
          <p className="text-zinc-300">Loading campaigns...</p>
        </div>
      ) : (
        renderCampaignList()
      )}
    </div>
  );
}
