# PMO Task ID Resolution Hook

This directory contains custom React hooks for working with PMO (Project Management Office) task ID resolution.

## `usePMOTaskId`

A custom hook that provides functionality to find PMO task IDs by title within specific projects.

### Usage

```typescript
import { usePMOTaskId } from './usePMOTaskId';

const MyComponent = () => {
  const { findTaskId, loading, lastResult } = usePMOTaskId();

  const handleFindTask = async () => {
    const result = await findTaskId(
      '<EMAIL>',    // userId
      'pmo-id-123',          // pmoId
      'project-id-456',      // projectId
      'Task Title to Find',  // taskTitle
      false                  // exactMatch (optional, defaults to false)
    );

    if (result.success) {
      console.log('Found task ID:', result.taskId);
      console.log('Matched title:', result.matchedTitle);
    } else {
      console.error('Error:', result.error);
    }
  };

  return (
    <div>
      <button onClick={handleFindTask} disabled={loading}>
        {loading ? 'Searching...' : 'Find Task ID'}
      </button>
      {lastResult && (
        <div>
          {lastResult.success ? (
            <p>Found: {lastResult.taskId}</p>
          ) : (
            <p>Error: {lastResult.error}</p>
          )}
        </div>
      )}
    </div>
  );
};
```

## `useAutoResolvePMOTaskId`

A convenience hook that automatically resolves PMO task IDs when PMO metadata is available in task notes.

### Usage

```typescript
import { useAutoResolvePMOTaskId } from './usePMOTaskId';
import { useSession } from 'next-auth/react';

const TaskComponent = ({ task }) => {
  const { data: session } = useSession();
  const {
    resolveTaskId,
    autoResolvedTaskId,
    loading,
    lastResult,
    extractPMOMetadata
  } = useAutoResolvePMOTaskId(task, session?.user?.email);

  useEffect(() => {
    // Automatically resolve when component mounts
    if (task.notes?.includes('PMO Record ID:')) {
      resolveTaskId();
    }
  }, [task.notes, resolveTaskId]);

  const metadata = extractPMOMetadata(task.notes || '');

  return (
    <div>
      {loading && <p>Resolving PMO Task ID...</p>}
      {autoResolvedTaskId && (
        <div>
          <p>PMO Task ID: {autoResolvedTaskId}</p>
          {lastResult?.matchedTitle && (
            <p>Matched: {lastResult.matchedTitle}</p>
          )}
        </div>
      )}
      {metadata.pmoId && (
        <p>PMO ID: {metadata.pmoId}</p>
      )}
      <button onClick={resolveTaskId} disabled={loading}>
        Refresh Task ID
      </button>
    </div>
  );
};
```

## Features

### Fuzzy Matching
The hook supports multiple matching strategies:
- **Exact match**: Exact title comparison
- **Partial match**: Substring matching in both directions
- **Keyword match**: Matches based on significant keywords

### Error Handling
- Comprehensive error handling with descriptive error messages
- Loading states for async operations
- Graceful fallbacks when tasks are not found

### PMO Metadata Extraction
- Automatically extracts PMO Record ID from task notes
- Supports legacy PMO Project ID extraction
- Handles various note formats

## Integration with TaskDetailsModal

The `TaskDetailsModal` component has been enhanced to automatically resolve and display PMO task IDs:

1. **Automatic Resolution**: When a PMO task is opened, the hook automatically attempts to resolve the task ID
2. **Visual Feedback**: Shows loading state, success state with matched title, or error state
3. **Manual Refresh**: Users can manually retry resolution with a refresh button
4. **PMO Metadata Display**: Shows the PMO Record ID extracted from task notes

## Dependencies

- `react` - For hooks and state management
- `next-auth/react` - For user session management (in auto-resolve hook)
- `/api/pmo/find-task-id` - Server-side API endpoint for task ID resolution

## API Endpoint

The hooks use the `/api/pmo/find-task-id` endpoint which provides server-side access to Firebase Admin SDK functionality. This endpoint:

- Requires authentication via NextAuth
- Validates user permissions
- Calls the `findTaskIdByTitle` function from `lib/firebase/pmoHierarchical`
- Supports both POST and GET requests
- Returns the same response format as the underlying function

## Error Scenarios

The hooks handle various error scenarios:
- Missing required parameters (userId, pmoId, projectId, taskTitle)
- Network/Firebase connection issues
- No matching tasks found
- Invalid PMO metadata in task notes
- Permission/authentication issues
