import puppeteer from "puppeteer";
import * as cheerio from "cheerio";

/**
 * Interface for scraped content
 */
export interface ScrapedContent {
  content: string;
  title: string;
  metadata: Record<string, string | null>;
  url: string;
}

/**
 * Web Scraping Tool for extracting content from URLs
 */
export class WebScraperTool {
  /**
   * Scrape content from a URL
   * @param url - The URL to scrape
   * @returns Scraped content and metadata
   */
  /**
   * Scrape content from a URL with retry mechanism and improved timeout handling
   * @param url - The URL to scrape
   * @param options - Optional scraping options
   * @returns Scraped content and metadata
   */
  async scrapeContent(
    url: string,
    options: {
      timeout?: number;
      maxRetries?: number;
      retryDelay?: number;
    } = {}
  ): Promise<ScrapedContent> {
    if (!url) throw new Error("URL is required");

    // Set default options with extended timeouts
    const timeout = options.timeout || 300000; // 5 minutes default timeout
    const maxRetries = options.maxRetries || 3;
    const retryDelay = options.retryDelay || 5000;

    const normalizedUrl = this.normalizeUrl(url);

    // Implement retry logic
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      // If this is a retry, log and wait
      if (attempt > 0) {
        console.log(`Retry attempt ${attempt}/${maxRetries} for ${url} after error: ${lastError?.message}`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

      const browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage", // Helps with memory issues in Docker
          "--disable-accelerated-2d-canvas",
          "--disable-gpu",
          "--disable-features=site-per-process", // Can help with some sites
        ],
      });

      try {
        const page = await browser.newPage();

        // Set user agent to avoid being blocked
        await page.setUserAgent(
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        );

        // Set request timeout
        page.setDefaultNavigationTimeout(timeout);
        page.setDefaultTimeout(timeout);

        // Block unnecessary resources to speed up loading
        await page.setRequestInterception(true);
        page.on('request', (req) => {
          const resourceType = req.resourceType();
          if (resourceType === 'image' || resourceType === 'font' || resourceType === 'media') {
            req.abort();
          } else {
            req.continue();
          }
        });

        // Navigate to the URL with explicit timeout
        await page.goto(normalizedUrl, {
          waitUntil: "domcontentloaded",
          timeout: timeout
        });

        // Extract page title
        const title = await page.title();

        // Extract metadata
        const metadata = await page.evaluate(() => {
          const metaTags: Record<string, string | null> = {};
          document.querySelectorAll('meta').forEach(meta => {
            const name = meta.getAttribute('name') || meta.getAttribute('property');
            if (name) metaTags[name] = meta.getAttribute('content');
          });
          return metaTags;
        });

        // Get HTML content
        const html = await page.content();

        // Parse with cheerio to clean content
        const $ = cheerio.load(html);

        // Remove script and style tags
        $('script, style, iframe, nav, footer, header, aside').remove();

        // Extract main content
        const content = $('body').text().replace(/\s+/g, ' ').trim();

        // Success! Return the result
        return {
          content,
          title,
          metadata,
          url: normalizedUrl
        };
      } catch (error) {
        // Store the error for potential retry
        lastError = error instanceof Error ? error : new Error(String(error));

        // If this is the last attempt, we'll let the error propagate after browser cleanup
        if (attempt === maxRetries) {
          console.error(`Failed to scrape ${url} after ${maxRetries + 1} attempts. Last error: ${lastError.message}`);
        }
      } finally {
        // Always close the browser
        await browser.close();
      }
    }

    // If we get here, all retries failed
    throw lastError || new Error(`Failed to scrape ${url} for unknown reasons`);
  }

  /**
   * Extract all links from a URL with retry mechanism and improved timeout handling
   * @param url - The URL to scrape for links
   * @param sameOriginOnly - Only return links from the same origin
   * @param options - Optional scraping options
   * @returns Array of extracted URLs
   */
  async extractLinks(
    url: string,
    sameOriginOnly = true,
    options: {
      timeout?: number;
      maxRetries?: number;
      retryDelay?: number;
    } = {}
  ): Promise<string[]> {
    if (!url) throw new Error("URL is required");

    // Set default options with extended timeouts
    const timeout = options.timeout || 300000; // 5 minutes default timeout
    const maxRetries = options.maxRetries || 3;
    const retryDelay = options.retryDelay || 5000;

    const normalizedUrl = this.normalizeUrl(url);
    const urlObj = new URL(normalizedUrl);
    const origin = urlObj.origin;

    // Implement retry logic
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      // If this is a retry, log and wait
      if (attempt > 0) {
        console.log(`Retry attempt ${attempt}/${maxRetries} for extracting links from ${url} after error: ${lastError?.message}`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }

      const browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage", // Helps with memory issues in Docker
          "--disable-accelerated-2d-canvas",
          "--disable-gpu",
          "--disable-features=site-per-process", // Can help with some sites
        ],
      });

      try {
        const page = await browser.newPage();

        // Set user agent to avoid being blocked
        await page.setUserAgent(
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36"
        );

        // Set request timeout
        page.setDefaultNavigationTimeout(timeout);
        page.setDefaultTimeout(timeout);

        // Block unnecessary resources to speed up loading
        await page.setRequestInterception(true);
        page.on('request', (req) => {
          const resourceType = req.resourceType();
          if (resourceType === 'image' || resourceType === 'font' || resourceType === 'media') {
            req.abort();
          } else {
            req.continue();
          }
        });

        // Navigate to the URL with explicit timeout
        await page.goto(normalizedUrl, {
          waitUntil: "domcontentloaded",
          timeout: timeout
        });

        // Extract all links
        const links = await page.evaluate((origin, sameOriginOnly) => {
          return Array.from(document.querySelectorAll('a[href]'))
            .map(a => (a as HTMLAnchorElement).href)
            .filter(href => !sameOriginOnly || href.startsWith(origin));
        }, origin, sameOriginOnly);

        // Remove duplicates and return
        return [...new Set(links)];
      } catch (error) {
        // Store the error for potential retry
        lastError = error instanceof Error ? error : new Error(String(error));

        // If this is the last attempt, we'll let the error propagate after browser cleanup
        if (attempt === maxRetries) {
          console.error(`Failed to extract links from ${url} after ${maxRetries + 1} attempts. Last error: ${lastError.message}`);
        }
      } finally {
        // Always close the browser
        await browser.close();
      }
    }

    // If we get here, all retries failed
    throw lastError || new Error(`Failed to extract links from ${url} for unknown reasons`);
  }

  /**
   * Normalizes a URL by ensuring it has a protocol
   * @param url - URL to normalize
   * @returns Normalized URL
   */
  normalizeUrl(url: string): string {
    if (!url) throw new Error("URL is required");

    try {
      // Try to create a URL object to validate
      new URL(url);
      return url;
    } catch {
      // If it fails, assume it needs https://
      if (!url.startsWith("http://") && !url.startsWith("https://")) {
        return `https://${url}`;
      }
      return url;
    }
  }
}

// Export a singleton instance
export const webScraperTool = new WebScraperTool();
