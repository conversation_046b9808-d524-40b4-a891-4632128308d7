import { useState, useEffect, useRef, useCallback } from 'react';
import { doc, getDoc, setDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../lib/firebase/config';
import { useSession } from 'next-auth/react';
import { User } from '../../admin/planner/types';

export interface UseUserProfileResult {
  profile: User | null;
  loading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
  lastFetched: Date | null;
}

/**
 * Custom hook to fetch and manage user profile data in Firestore
 *
 * Features:
 * - Automatically fetches user profile when session is available
 * - Syncs Google profile image with Firestore
 * - Provides loading and error states
 * - Allows manual refresh
 * - Handles session availability
 */
function useUserProfile(cacheDurationMs: number = 5 * 60 * 1000): UseUserProfileResult {
  const [profile, setProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetched, setLastFetched] = useState<Date | null>(null);
  const { data: session } = useSession();

  // Use a ref to track if the component is mounted
  const isMountedRef = useRef<boolean>(true);
  // Cache reference
  const cacheTimeRef = useRef<number>(0);

  // Define the fetch function with useCallback to prevent unnecessary re-renders
  const fetchUserProfile = useCallback(async (forceRefresh: boolean = false) => {
    // Don't fetch if no session
    if (!session?.user?.email) {
      setError(new Error('User session is not available'));
      return;
    }

    // Check cache unless force refresh is requested
    const now = Date.now();
    if (!forceRefresh && now - cacheTimeRef.current < cacheDurationMs && profile) {
      console.log('Using cached user profile data');
      return;
    }

    // Start loading
    setLoading(true);
    setError(null);

    const userId = session.user.email;

    try {
      console.log(`Fetching user profile for: ${userId}`);

      // Get user document
      const userDocRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();

        // Check if profile image needs updating
        if (session.user.image && userData.avatar !== session.user.image) {
          console.log('Updating user avatar with Google profile image:', session.user.image);

          try {
            // Update the avatar in Firestore
            await updateDoc(userDocRef, {
              avatar: session.user.image,
              updatedAt: serverTimestamp()
            });

            // Create updated profile
            const updatedProfile: User = {
              id: userDoc.id,
              name: userData.name || session.user.name || 'Unknown User',
              email: userData.email || userId,
              role: userData.role || 'user',
              avatar: session.user.image,
              availability: userData.availability || 'Full-time',
              isAuthorized: userData.isAuthorized !== undefined ? userData.isAuthorized : true,
              createdAt: userData.createdAt ? new Date(userData.createdAt.seconds * 1000) : new Date(),
              updatedAt: new Date(),
              photoURL: session.user.image,
              displayName: userData.name || session.user.name || 'Unknown User'
            };

            // Only update state if the component is still mounted
            if (isMountedRef.current) {
              setProfile(updatedProfile);
              setLastFetched(new Date());
              cacheTimeRef.current = now;
              console.log('User profile updated with new avatar');
            }
          } catch (avatarErr) {
            console.error('Error updating user avatar:', avatarErr);
            // Still continue with the existing profile data
          }
        } else {
          // Create profile from existing data
          const existingProfile: User = {
            id: userDoc.id,
            name: userData.name || session.user.name || 'Unknown User',
            email: userData.email || userId,
            role: userData.role || 'user',
            avatar: userData.avatar || session.user.image || '/avatars/default.png',
            availability: userData.availability || 'Full-time',
            isAuthorized: userData.isAuthorized !== undefined ? userData.isAuthorized : true,
            createdAt: userData.createdAt ? new Date(userData.createdAt.seconds * 1000) : new Date(),
            updatedAt: userData.updatedAt ? new Date(userData.updatedAt.seconds * 1000) : new Date(),
            photoURL: userData.avatar || session.user.image || '/avatars/default.png',
            displayName: userData.name || session.user.name || 'Unknown User'
          };

          // Only update state if the component is still mounted
          if (isMountedRef.current) {
            setProfile(existingProfile);
            setLastFetched(new Date());
            cacheTimeRef.current = now;
            console.log('Fetched existing user profile');
          }
        }
      } else {
        console.log('User document not found, creating new profile');

        // Create new user profile
        const newProfile: Omit<User, 'id'> = {
          name: session.user.name || 'New User',
          email: userId,
          role: 'user',
          avatar: session.user.image || '/avatars/default.png',
          availability: 'Full-time',
          isAuthorized: true,
          createdAt: new Date(),
          updatedAt: new Date(),
          photoURL: session.user.image || '/avatars/default.png',
          displayName: session.user.name || 'New User'
        };

        try {
          // Create the user document in Firestore
          await setDoc(userDocRef, {
            ...newProfile,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          });

          // Only update state if the component is still mounted
          if (isMountedRef.current) {
            setProfile({ ...newProfile, id: userId });
            setLastFetched(new Date());
            cacheTimeRef.current = now;
            console.log('Created new user profile');
          }
        } catch (createErr) {
          console.error('Error creating user profile:', createErr);
          if (isMountedRef.current) {
            setError(createErr instanceof Error ? createErr : new Error(String(createErr)));
          }
        }
      }
    } catch (err) {
      console.error('Error fetching user profile:', err);

      // Only update state if the component is still mounted
      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error(String(err)));
      }
    } finally {
      // Only update state if the component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [session, profile, cacheDurationMs]);

  // Fetch user profile when the session changes
  useEffect(() => {
    fetchUserProfile();

    // Cleanup function to prevent memory leaks
    return () => {
      isMountedRef.current = false;
    };
  }, [fetchUserProfile]);

  // Create a refresh function that forces a refresh
  const refresh = useCallback(async () => {
    await fetchUserProfile(true);
  }, [fetchUserProfile]);

  return {
    profile,
    loading,
    error,
    refresh,
    lastFetched
  };
}

export default useUserProfile;
