'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Project, Task } from '../types';
import { RefreshCw } from 'lucide-react';
import MarkdownRenderer from '../../../components/MarkdownRenderer';

interface ProjectSummaryProps {
  project: Project;
  tasks: Task[];
}

const ProjectSummary: React.FC<ProjectSummaryProps> = ({ project, tasks }) => {
  const [summary, setSummary] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const generateSummary = async () => {
    setLoading(true);
    setError(null);

    try {
      // Prepare project details for the LLM
      const projectDetails = {
        projectName: project.name,
        projectDescription: project.description,
        projectStartDate: project.startDate,
        projectEndDate: project.endDate,
        projectStatus: project.status,
        tasksCount: tasks.length,
        tasksNotStarted: tasks.filter(t => t.status === 'Not Started').length,
        tasksInProgress: tasks.filter(t => t.status === 'In Progress').length,
        tasksReviewed: tasks.filter(t => t.status === 'Reviewed').length,
        tasksComplete: tasks.filter(t => t.status === 'Complete').length,
        tasksByPriority: {
          low: tasks.filter(t => t.priority === 'Low').length,
          medium: tasks.filter(t => t.priority === 'Medium').length,
          high: tasks.filter(t => t.priority === 'High').length,
          critical: tasks.filter(t => t.priority === 'Critical').length
        },
        // Include detailed task information
        tasks: tasks.map(task => ({
          title: task.title,
          description: task.description,
          status: task.status,
          priority: task.priority,
          dueDate: task.dueDate,
          category: task.category
        }))
      };

      // Call the project summary API
      const response = await fetch('/api/project-summary', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectDetails
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate project summary');
      }

      const data = await response.json();

      if (data.success && data.summary) {
        setSummary(data.summary);
      } else {
        throw new Error(data.error || 'Unknown error generating summary');
      }
    } catch (err: any) {
      console.error('Error generating project summary:', err);
      setError(err.message || 'Failed to generate project summary');
    } finally {
      setLoading(false);
    }
  };

  // Create a dependency string to track changes in tasks
  const taskDependencyString = useMemo(() => {
    return JSON.stringify({
      taskCount: tasks.length,
      notStarted: tasks.filter(t => t.status === 'Not Started').length,
      inProgress: tasks.filter(t => t.status === 'In Progress').length,
      reviewed: tasks.filter(t => t.status === 'Reviewed').length,
      complete: tasks.filter(t => t.status === 'Complete').length,
    });
  }, [tasks]);

  // We've removed the automatic summary generation on component mount or task changes
  // Now summary will only be generated when the user clicks the Refresh button

  return (
    <div className="bg-gray-800 rounded-lg p-6 mb-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-white">Project Overview</h2>
        <button
          onClick={generateSummary}
          disabled={loading}
          className="flex items-center text-sm bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded transition-colors"
        >
          <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {loading ? (
        <div className="bg-gray-700 rounded-lg p-4 animate-pulse">
          <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-600 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-600 rounded w-5/6"></div>
        </div>
      ) : error ? (
        <div className="bg-red-900/30 text-red-300 p-4 rounded-lg">
          <p className="font-medium">Error generating summary</p>
          <p className="text-sm">{error}</p>
          <button
            onClick={generateSummary}
            className="mt-2 text-sm bg-red-700 hover:bg-red-800 text-white py-1 px-3 rounded transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : (
        <div className="bg-gray-700/50 rounded-lg">
          {summary ? (
            <MarkdownRenderer content={summary} />
          ) : (
            <p className="text-gray-200 p-4">Click "Refresh" to generate a project overview.</p>
          )}
        </div>
      )}
    </div>
  );
};

export default ProjectSummary;
