/**
 * Prompt Interpreter Tool
 *
 * This tool analyzes a prompt and establishes a list of criteria that can be applied
 * to optimize and improve the prompt. It uses the DeepSeek model from Groq.
 */

import { processWithGroq } from './groq-ai';

// Define interfaces for the tool
export interface PromptInterpreterOptions {
  prompt: string;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

export interface PromptInterpreterResult {
  success: boolean;
  criteria: string[];
  error?: string;
}

export class PromptInterpreterTool {
  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "interpretPrompt",
    description: "Analyzes a prompt and establishes a list of criteria for optimization.",
    parameters: {
      type: "object",
      properties: {
        prompt: {
          type: "string",
          description: "The prompt to analyze and interpret."
        },
        modelOptions: {
          type: "object",
          description: "Optional configuration for the DeepSeek model.",
          properties: {
            temperature: {
              type: "number",
              description: "Controls randomness in the output. Lower values are more deterministic.",
              default: 0.3
            },
            maxTokens: {
              type: "number",
              description: "Maximum number of tokens to generate.",
              default: 2000
            }
          }
        }
      },
      required: ["prompt"]
    },
    returns: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          description: "Whether the operation was successful."
        },
        criteria: {
          type: "array",
          items: {
            type: "string"
          },
          description: "List of criteria that can be applied to optimize the prompt."
        },
        error: {
          type: "string",
          description: "Error message if the operation failed."
        }
      }
    },
    examples: [
      {
        input: { prompt: "Write a blog post about climate change" },
        output: {
          success: true,
          criteria: [
            "Specify the target audience",
            "Define the tone (formal, conversational, etc.)",
            "Indicate desired length",
            "Specify if sources should be included",
            "Clarify the perspective or stance"
          ]
        }
      }
    ]
  };

  /**
   * Interpret a prompt to establish optimization criteria
   * @param options - Options for prompt interpretation
   * @returns - List of criteria for prompt optimization
   */
  async interpretPrompt(options: PromptInterpreterOptions): Promise<PromptInterpreterResult> {
    try {
      const { prompt, modelOptions = {} } = options;

      if (!prompt) {
        throw new Error("Prompt is required");
      }

      console.log("PromptInterpreterTool: Processing prompt:", prompt);

      // Create a system prompt for the DeepSeek model
      const systemPrompt = `
You are a prompt engineering expert. Your task is to analyze the following prompt and establish a list of criteria that can be applied to optimize and improve it.

Please generate criteria that a high-quality prompt should meet. Each criterion should be:
1. Specific and measurable
2. Relevant to the prompt's requirements
3. Focused on different aspects (e.g., accuracy, completeness, creativity, etc.)


Format your response as a numbered list with a brief explanation for each criterion.
PROMPT TO ANALYZE:
"""
${prompt}
"""
`;

      console.log("PromptInterpreterTool: Sending to Groq DeepSeek model");

      let result;
      try {
        // First try with DeepSeek model through Groq
        result = await processWithGroq({
          prompt: systemPrompt,
          model: "deepseek-r1-distill-llama-70b", // Using DeepSeek model as specified
          modelOptions: {
            temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.3,
            maxTokens: modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 2000,
            ...modelOptions
          }
        });

        console.log("PromptInterpreterTool: Received response from Groq DeepSeek:", result.substring(0, 100) + "...");
      } catch (groqError) {
        // If DeepSeek fails, fall back to Llama 3.3
        console.warn("PromptInterpreterTool: DeepSeek model failed, falling back to Llama 3.3:", groqError);

        result = await processWithGroq({
          prompt: systemPrompt,
          model: "llama-3.3-70b-versatile", // Using Llama 3.3 as fallback
          modelOptions: {
            temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.3,
            maxTokens: modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 2000,
            ...modelOptions
          }
        });

        console.log("PromptInterpreterTool: Received fallback response from Llama 3.3:", result.substring(0, 100) + "...");
      }

      // Parse the result to extract criteria
      const criteriaList = this._parseCriteriaFromResponse(result);

      console.log("PromptInterpreterTool: Parsed criteria:", criteriaList);

      // Verify we have at least one criterion
      if (!criteriaList || criteriaList.length === 0) {
        console.warn("PromptInterpreterTool: No criteria could be extracted from the model response. Using fallback criteria.");

        // Use fallback criteria based on common prompt improvements
        return {
          success: true,
          criteria: [
            "Specify the target audience or user persona",
            "Define the desired tone (formal, conversational, technical, etc.)",
            "Indicate the preferred length or level of detail",
            "Clarify the specific goal or outcome of the prompt",
            "Add context or background information",
            "Include format requirements or structure preferences",
            "Specify any constraints or limitations"
          ]
        };
      }

      return {
        success: true,
        criteria: criteriaList
      };
    } catch (error: any) {
      console.error("Error interpreting prompt:", error);
      return {
        success: false,
        criteria: [],
        error: error.message || "Unknown error occurred while interpreting the prompt"
      };
    }
  }

  /**
   * Parse criteria from the LLM response
   * @private
   * @param response - Raw response from the LLM
   * @returns - Array of criteria
   */
  private _parseCriteriaFromResponse(response: string): string[] {
    console.log("Parsing criteria from response:", response);

    // Handle empty or invalid responses
    if (!response || typeof response !== 'string') {
      console.error("Invalid response received:", response);
      return [];
    }

    // Split the response by newlines and filter out empty lines
    const lines = response.split('\n').filter(line => line.trim() !== '');
    console.log("Split lines:", lines);

    // Extract criteria from numbered list format (e.g., "1. Criterion")
    const criteriaRegex = /^\s*\d+\.?\s*(.+)$/;

    // Try to extract criteria using the regex
    const extractedCriteria = lines
      .map(line => {
        const match = line.match(criteriaRegex);
        return match ? match[1].trim() : null;
      })
      .filter((criterion): criterion is string => criterion !== null);

    console.log("Extracted criteria using regex:", extractedCriteria);

    // If we couldn't extract any criteria with the regex, try a fallback approach
    if (extractedCriteria.length === 0) {
      console.log("No criteria found with regex, trying fallback approach");

      // Fallback: Try to extract any non-empty lines that might be criteria
      // This handles cases where the model didn't format as a numbered list
      const fallbackCriteria = lines
        .filter(line => {
          // Filter out common non-criteria lines
          const lowerLine = line.toLowerCase();
          return !lowerLine.includes("here are") &&
                 !lowerLine.includes("criteria") &&
                 !lowerLine.includes("list of") &&
                 !lowerLine.includes("introduction") &&
                 !lowerLine.includes("conclusion") &&
                 line.length > 10; // Minimum length for a meaningful criterion
        })
        .map(line => line.trim());

      console.log("Fallback criteria:", fallbackCriteria);

      return fallbackCriteria;
    }

    return extractedCriteria;
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof PromptInterpreterTool.description {
    return PromptInterpreterTool.description;
  }

  /**
   * Get all available tool methods with their descriptions
   * @returns Map of method names to their descriptions
   */
  getAvailableMethods(): Record<string, string> {
    return {
      interpretPrompt: "Analyze a prompt and establish a list of criteria for optimization"
    };
  }
}

// Export a singleton instance
export const promptInterpreterTool = new PromptInterpreterTool();
