/**
 * start-with-memory.js
 *
 * This script launches the application with increased memory limits
 * to handle large document processing without running into memory issues.
 */

const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

// Default memory limit (can be overridden with environment variable)
const DEFAULT_MEMORY_LIMIT = 4096; // 4GB

// Get memory limit from environment or use default
const memoryLimit = process.env.NODE_MEMORY_LIMIT || DEFAULT_MEMORY_LIMIT;

// Command to run (can be passed as arguments to this script)
const command = process.argv[2] || 'node';
const args = process.argv.slice(3);

// Verify system has enough memory
const totalSystemMemory = os.totalmem() / (1024 * 1024); // Convert to MB
const freeSystemMemory = os.freemem() / (1024 * 1024); // Convert to MB
const requestedMemory = parseInt(memoryLimit);

console.log(`System memory: ${Math.round(totalSystemMemory)}MB total, ${Math.round(freeSystemMemory)}MB free`);
console.log(`Requested memory limit: ${requestedMemory}MB`);

// Check if we're in a container (rough check)
const inContainer = process.env.CONTAINER === 'true' ||
                   (os.hostname().length === 12 && !isNaN(Number('0x' + os.hostname()))) ||
                   require('fs').existsSync('/.dockerenv');

if (inContainer) {
  console.warn('WARNING: Running in a container environment. Container memory limits may override Node.js settings.');
}

// Warn if requested memory exceeds available memory
if (requestedMemory > totalSystemMemory) {
  console.warn(`WARNING: Requested memory (${requestedMemory}MB) exceeds total system memory (${Math.round(totalSystemMemory)}MB).`);
  console.warn('This may cause system instability or the process might be killed by the OS.');
}

if (requestedMemory > freeSystemMemory) {
  console.warn(`WARNING: Requested memory (${requestedMemory}MB) exceeds free system memory (${Math.round(freeSystemMemory)}MB).`);
  console.warn('Performance may be degraded due to swapping.');
}

// Verify correct usage
if (args.length === 0) {
  console.warn('WARNING: No application specified to run. Usage: node start-with-memory.js node your-app.js [args]');
}

console.log(`Starting process with increased memory limit: ${memoryLimit}MB`);
console.log(`Command: ${command} ${args.join(' ')}`);

// Add the memory flag and expose garbage collection
const nodeArgs = [`--max-old-space-size=${memoryLimit}`, '--expose-gc'];

// If the command is 'node', add the memory flags directly
if (command === 'node') {
  const fullArgs = [...nodeArgs, ...args];
  console.log(`Executing: ${command} ${fullArgs.join(' ')}`);

  const childProcess = spawn(command, fullArgs, {
    stdio: 'inherit',
    shell: process.platform === 'win32' // Use shell on Windows for better compatibility
  });

  childProcess.on('close', (code) => {
    if (code !== 0) {
      console.error(`Process exited with code ${code}`);
    }
    process.exit(code);
  });

  childProcess.on('error', (err) => {
    console.error('Failed to start child process:', err);
    process.exit(1);
  });
} else {
  // For other commands (like npm scripts), we need to modify the NODE_OPTIONS env var
  const env = { ...process.env };
  env.NODE_OPTIONS = `${env.NODE_OPTIONS || ''} --max-old-space-size=${memoryLimit} --expose-gc`;

  console.log(`Setting NODE_OPTIONS=${env.NODE_OPTIONS}`);

  const childProcess = spawn(command, args, {
    stdio: 'inherit',
    env,
    shell: process.platform === 'win32' // Use shell on Windows for better compatibility
  });

  childProcess.on('close', (code) => {
    if (code !== 0) {
      console.error(`Process exited with code ${code}`);
    }
    process.exit(code);
  });

  childProcess.on('error', (err) => {
    console.error('Failed to start child process:', err);
    process.exit(1);
  });
}

// Add a note about direct invocation alternative
console.log('\nAlternative: You can also run your app directly with:');
console.log(`node --max-old-space-size=${memoryLimit} --expose-gc ${args.join(' ')}`);


