'use client';

import React from 'react';
import Link from 'next/link';

export default function TestPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="bg-gray-800 rounded-lg shadow-md p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-white mb-4">Test Page</h1>
        <p className="text-gray-300 mb-6">
          This is a test page to verify that the routing is working correctly.
        </p>
        <div className="space-y-4">
          <Link 
            href="/services/admin"
            className="block w-full py-2 px-4 bg-purple-600 text-white rounded-md text-center hover:bg-purple-700 transition-colors"
          >
            Go to Admin Dashboard
          </Link>
          <Link 
            href="/services/admin/login"
            className="block w-full py-2 px-4 bg-blue-600 text-white rounded-md text-center hover:bg-blue-700 transition-colors"
          >
            Go to Login Page
          </Link>
          <Link 
            href="/services/admin/planner"
            className="block w-full py-2 px-4 bg-green-600 text-white rounded-md text-center hover:bg-green-700 transition-colors"
          >
            Go to Project Planner
          </Link>
        </div>
      </div>
    </div>
  );
}
