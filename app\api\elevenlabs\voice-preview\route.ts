import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { voiceId, text } = await request.json();

    if (!voiceId || !text) {
      return NextResponse.json(
        { error: 'Voice ID and text are required' },
        { status: 400 }
      );
    }

    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;

    if (!apiKey) {
      return NextResponse.json(
        { error: 'ElevenLabs API key not configured' },
        { status: 500 }
      );
    }

    console.log(`[VOICE_PREVIEW_API] Generating voice preview for voiceId: ${voiceId}`);
    console.log(`[VOICE_PREVIEW_API] Text length: ${text.length} characters`);

    const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        'Accept': 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey,
      },
      body: JSON.stringify({
        text: text,
        model_id: 'eleven_turbo_v2_5',
        voice_settings: {
          stability: 0.5,
          similarity_boost: 0.5,
          style: 0.0,
          use_speaker_boost: true
        }
      }),
    });

    console.log(`[VOICE_PREVIEW_API] ElevenLabs response status: ${response.status}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[VOICE_PREVIEW_API] ElevenLabs API error:`, errorText);
      return NextResponse.json(
        { error: `ElevenLabs API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const audioBlob = await response.blob();
    console.log(`[VOICE_PREVIEW_API] Audio blob size: ${audioBlob.size} bytes`);

    if (audioBlob.size === 0) {
      return NextResponse.json(
        { error: 'Received empty audio blob from ElevenLabs' },
        { status: 500 }
      );
    }

    // Convert blob to buffer and return as audio response
    const audioBuffer = await audioBlob.arrayBuffer();

    return new NextResponse(audioBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.byteLength.toString(),
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });

  } catch (error) {
    console.error('[VOICE_PREVIEW_API] Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
