// app/api/pmo-hierarchical-migration/route.ts
import { NextRequest, NextResponse } from 'next/server';
import {
  migratePMOToHierarchical,
  migrateAllPMOsToHierarchical,
  validateHierarchicalStructure
} from '../../../lib/firebase/pmoMigration';
import {
  getHierarchicalPMOData,
  addTaskToProject,
  createProjectDocument,
  getProjectTasks
} from '../../../lib/firebase/pmoHierarchical';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, pmoId, dryRun = false } = body;

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    console.log(`PMO Hierarchical Migration API: ${action} for user ${userId}${pmoId ? `, PMO ${pmoId}` : ''}`);

    switch (action) {
      case 'migrate-all':
        const migrateAllResult = await migrateAllPMOsToHierarchical(userId, dryRun);
        return NextResponse.json({
          success: migrateAllResult.success,
          data: {
            migratedPMOs: migrateAllResult.migratedPMOs,
            errors: migrateAllResult.errors,
            details: migrateAllResult.details
          },
          message: `${dryRun ? 'Simulated' : 'Executed'} migration for ${migrateAllResult.migratedPMOs} PMO records`
        });

      case 'migrate-single':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required for single migration' },
            { status: 400 }
          );
        }

        const migrateSingleResult = await migratePMOToHierarchical(userId, pmoId, dryRun);
        return NextResponse.json({
          success: migrateSingleResult.success,
          data: migrateSingleResult.details,
          error: migrateSingleResult.error,
          message: migrateSingleResult.success
            ? `${dryRun ? 'Simulated' : 'Executed'} migration for PMO ${pmoId}`
            : `Migration failed for PMO ${pmoId}`
        });

      case 'validate':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required for validation' },
            { status: 400 }
          );
        }

        const validateResult = await validateHierarchicalStructure(userId, pmoId);
        return NextResponse.json({
          success: true,
          data: {
            isValid: validateResult.isValid,
            issues: validateResult.issues,
            details: validateResult.details
          },
          message: validateResult.isValid
            ? `PMO ${pmoId} has valid hierarchical structure`
            : `PMO ${pmoId} has ${validateResult.issues.length} validation issues`
        });

      case 'get-hierarchical-data':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required to get hierarchical data' },
            { status: 400 }
          );
        }

        const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);
        return NextResponse.json({
          success: hierarchicalResult.success,
          data: hierarchicalResult.data,
          error: hierarchicalResult.error,
          message: hierarchicalResult.success
            ? `Retrieved hierarchical data for PMO ${pmoId}`
            : `Failed to get hierarchical data for PMO ${pmoId}`
        });

      case 'get-project-tasks':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required to get project tasks' },
            { status: 400 }
          );
        }

        const { projectId } = body;
        if (!projectId) {
          return NextResponse.json(
            { success: false, error: 'Project ID is required to get project tasks' },
            { status: 400 }
          );
        }

        const projectTasksResult = await getProjectTasks(userId, pmoId, projectId);
        return NextResponse.json({
          success: projectTasksResult.success,
          data: { taskIds: projectTasksResult.taskIds },
          error: projectTasksResult.error,
          message: projectTasksResult.success
            ? `Retrieved ${projectTasksResult.taskIds?.length || 0} tasks for project ${projectId}`
            : `Failed to get tasks for project ${projectId}`
        });

      case 'create-project':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required to create project' },
            { status: 400 }
          );
        }

        const { projectId: createProjectId, projectInfo: createProjectInfo } = body;
        if (!createProjectId || !createProjectInfo) {
          return NextResponse.json(
            { success: false, error: 'Project ID and project info are required' },
            { status: 400 }
          );
        }

        const createProjectResult = await createProjectDocument(
          userId,
          pmoId,
          createProjectId,
          createProjectInfo
        );

        return NextResponse.json({
          success: createProjectResult.success,
          error: createProjectResult.error,
          message: createProjectResult.success
            ? `Created project document for ${createProjectId}`
            : `Failed to create project document for ${createProjectId}`
        });

      case 'add-task-to-project':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required to add task to project' },
            { status: 400 }
          );
        }

        const { projectId: taskProjectId, taskId, taskData } = body;
        if (!taskProjectId || !taskId || !taskData) {
          return NextResponse.json(
            { success: false, error: 'Project ID, task ID, and task data are required' },
            { status: 400 }
          );
        }

        const addTaskResult = await addTaskToProject(
          userId,
          pmoId,
          taskProjectId,
          taskId,
          taskData
        );

        return NextResponse.json({
          success: addTaskResult.success,
          error: addTaskResult.error,
          message: addTaskResult.success
            ? `Added task ${taskId} to project ${taskProjectId}`
            : `Failed to add task ${taskId} to project ${taskProjectId}`
        });

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('PMO Hierarchical Migration API Error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const userId = searchParams.get('userId');
    const pmoId = searchParams.get('pmoId');

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'validate':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required for validation' },
            { status: 400 }
          );
        }

        const validateResult = await validateHierarchicalStructure(userId, pmoId);
        return NextResponse.json({
          success: true,
          data: {
            isValid: validateResult.isValid,
            issues: validateResult.issues,
            details: validateResult.details
          }
        });

      case 'get-hierarchical-data':
        if (!pmoId) {
          return NextResponse.json(
            { success: false, error: 'PMO ID is required to get hierarchical data' },
            { status: 400 }
          );
        }

        const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);
        return NextResponse.json({
          success: hierarchicalResult.success,
          data: hierarchicalResult.data,
          error: hierarchicalResult.error
        });

      default:
        return NextResponse.json(
          { success: false, error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('PMO Hierarchical Migration API Error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
