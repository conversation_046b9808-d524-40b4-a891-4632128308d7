/**
 * Project Creation Status Component
 * 
 * Displays the status of automatic project creation from Strategic Director Agent outputs.
 * Shows when projects and tasks have been automatically created after "Send to Team" action.
 */

'use client';

import React from 'react';
import { CheckCircle, XCircle, Clock, FolderPlus, ListTodo, AlertTriangle } from 'lucide-react';

export interface ProjectCreationInfo {
  success: boolean;
  projectsCreated?: number;
  totalTasksCreated?: number;
  projectIds?: string[];
  pmoUpdated?: boolean;
  error?: string;
  analysis?: string;
  createdAt?: Date;
  attemptedAt?: Date;
}

interface ProjectCreationStatusProps {
  projectCreation?: ProjectCreationInfo;
  className?: string;
  showDetails?: boolean;
}

export const ProjectCreationStatus: React.FC<ProjectCreationStatusProps> = ({
  projectCreation,
  className = '',
  showDetails = true
}) => {
  if (!projectCreation) {
    return null;
  }

  const { success, projectsCreated, totalTasksCreated, pmoUpdated, error, createdAt, attemptedAt } = projectCreation;

  // Determine status and styling
  const getStatusInfo = () => {
    if (success) {
      return {
        icon: CheckCircle,
        color: 'text-green-400',
        bgColor: 'bg-green-500/10',
        borderColor: 'border-green-500/20',
        title: 'Projects Created Successfully',
        message: `${projectsCreated || 0} projects and ${totalTasksCreated || 0} tasks created automatically`
      };
    } else {
      return {
        icon: XCircle,
        color: 'text-red-400',
        bgColor: 'bg-red-500/10',
        borderColor: 'border-red-500/20',
        title: 'Project Creation Failed',
        message: error || 'Failed to create projects from agent output'
      };
    }
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;
  const timestamp = createdAt || attemptedAt;

  return (
    <div className={`rounded-lg border ${statusInfo.borderColor} ${statusInfo.bgColor} p-3 ${className}`}>
      <div className="flex items-start space-x-3">
        <StatusIcon className={`w-5 h-5 ${statusInfo.color} mt-0.5 flex-shrink-0`} />
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className={`text-sm font-medium ${statusInfo.color}`}>
              {statusInfo.title}
            </h4>
            {timestamp && (
              <span className="text-xs text-gray-400">
                {new Date(timestamp).toLocaleTimeString()}
              </span>
            )}
          </div>
          
          <p className="text-sm text-gray-300 mt-1">
            {statusInfo.message}
          </p>

          {showDetails && success && (
            <div className="mt-2 space-y-1">
              {projectsCreated && projectsCreated > 0 && (
                <div className="flex items-center space-x-2 text-xs text-gray-400">
                  <FolderPlus className="w-3 h-3" />
                  <span>{projectsCreated} project{projectsCreated !== 1 ? 's' : ''} created</span>
                </div>
              )}
              
              {totalTasksCreated && totalTasksCreated > 0 && (
                <div className="flex items-center space-x-2 text-xs text-gray-400">
                  <ListTodo className="w-3 h-3" />
                  <span>{totalTasksCreated} task{totalTasksCreated !== 1 ? 's' : ''} assigned to ADMIN (HIGH priority)</span>
                </div>
              )}
              
              {pmoUpdated && (
                <div className="flex items-center space-x-2 text-xs text-gray-400">
                  <CheckCircle className="w-3 h-3" />
                  <span>PMO record updated with project IDs</span>
                </div>
              )}
            </div>
          )}

          {showDetails && !success && error && (
            <div className="mt-2">
              <div className="flex items-center space-x-2 text-xs text-red-400">
                <AlertTriangle className="w-3 h-3" />
                <span>Error: {error}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Compact version for use in lists or smaller spaces
 */
export const ProjectCreationStatusCompact: React.FC<ProjectCreationStatusProps> = ({
  projectCreation,
  className = ''
}) => {
  if (!projectCreation) {
    return null;
  }

  const { success, projectsCreated, totalTasksCreated } = projectCreation;

  if (success) {
    return (
      <div className={`inline-flex items-center space-x-1 text-xs text-green-400 ${className}`}>
        <CheckCircle className="w-3 h-3" />
        <span>{projectsCreated || 0}P / {totalTasksCreated || 0}T</span>
      </div>
    );
  } else {
    return (
      <div className={`inline-flex items-center space-x-1 text-xs text-red-400 ${className}`}>
        <XCircle className="w-3 h-3" />
        <span>Failed</span>
      </div>
    );
  }
};

/**
 * Loading state component for when project creation is in progress
 */
export const ProjectCreationLoading: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`rounded-lg border border-blue-500/20 bg-blue-500/10 p-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <Clock className="w-5 h-5 text-blue-400 animate-pulse" />
        <div>
          <h4 className="text-sm font-medium text-blue-400">Creating Projects...</h4>
          <p className="text-sm text-gray-300 mt-1">
            Extracting projects and tasks from strategic analysis
          </p>
        </div>
      </div>
    </div>
  );
};

/**
 * Hook to extract project creation info from agent output
 */
export const useProjectCreationStatus = (agentOutput: any): ProjectCreationInfo | null => {
  if (!agentOutput?.projectCreation) {
    return null;
  }

  return {
    success: agentOutput.projectCreation.success || false,
    projectsCreated: agentOutput.projectCreation.projectsCreated || 0,
    totalTasksCreated: agentOutput.projectCreation.totalTasksCreated || 0,
    projectIds: agentOutput.projectCreation.projectIds || [],
    pmoUpdated: agentOutput.projectCreation.pmoUpdated || false,
    error: agentOutput.projectCreation.error,
    analysis: agentOutput.projectCreation.analysis,
    createdAt: agentOutput.projectCreation.createdAt ? new Date(agentOutput.projectCreation.createdAt) : undefined,
    attemptedAt: agentOutput.projectCreation.attemptedAt ? new Date(agentOutput.projectCreation.attemptedAt) : undefined
  };
};

export default ProjectCreationStatus;
