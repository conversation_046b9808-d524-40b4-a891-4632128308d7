// pages/api/pmo-agent-handler.ts
import { NextRequest, NextResponse } from 'next/server';
import { PMOAgent } from '../../../lib/agents/pmo/PMOAgent'; // Adjust path
import { PMOFormInput, PMOAgentResult } from '../../../lib/agents/pmo/PMOInterfaces'; // Adjust path
import { initializePlannerContextForUser } from '@/lib/firebase/planner'; // Correct import path

export async function POST(
  req: NextRequest
): Promise<NextResponse<PMOAgentResult | { error: string }>> {
  if (req.method !== 'POST') {
    return NextResponse.json({ error: 'Method Not Allowed' }, { status: 405 });
  }

  const { userId, ...formData } = await req.json() as PMOFormInput & { userId: string };

  if (!userId) {
    return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
  }
  if (!formData.title || !formData.description) {
    return NextResponse.json({ error: 'Title and description are required' }, { status: 400 });
  }

  try {
    // Initialize Planner Context
    const plannerContext = await initializePlannerContextForUser(userId);
    if (!plannerContext) {
        throw new Error("Failed to initialize planner context for the user.");
    }

    const pmoAgent = new PMOAgent({
      userId: userId, // The user performing the action
      // includeExplanation: true, // if you want more details from the agent itself
      // streamResponse: false,
    });
    
    pmoAgent.setPlannerContext(plannerContext); // Inject the context

    const result = await pmoAgent.processRequest(formData as PMOFormInput);

    if (result.success) {
      return NextResponse.json(result);
    } else {
      return NextResponse.json({ ...result, error: result.error || "PMO Agent processing failed" }, { status: 500 });
    }
  } catch (error: any) {
    console.error('API Error handling PMO request:', error);
    return NextResponse.json({
        success: false,
        pmoRecordId: '',
        originalRequest: formData.description,
        formalizedRequest: '',
        taskAnalysis: {},
        assignedTeams: [],
        createdTasks: [],
        error: error.message || 'Internal server error'
    }, { status: 500 });
  }
}
