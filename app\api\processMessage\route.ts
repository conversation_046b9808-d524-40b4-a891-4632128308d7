import { NextRequest, NextResponse } from "next/server";
import { Pinecone } from "@pinecone-database/pinecone";
import OpenAI from "openai";
import { getDownloadURL, ref, uploadBytes } from "firebase/storage";
import { collection, query, orderBy, limit, getDocs } from "firebase/firestore";
import { convertPromptToVector } from "components/scriptreaderAI/vectorUtils";
import { fetchDocumentChunksByChunkIds } from "components/scriptreaderAI/fetchDocumentChunksByChunkIds";
import { callGroqAiEx } from "components/groqClient";
import { getRelevantHistory } from "components/scriptreaderAI/getRelevantHistory";
// Import initialized Firebase services
import { db, storage } from "components/firebase";
// Import the addMessage function
import { addMessage } from "components/scriptreaderAI/message";

// Initialize Pinecone
const pinecone = new Pinecone({ apiKey: process.env.PINECONE_API_KEY || '3b176b1d-1ed2-437b-8346-0f497c138048' });
const pineconeIndex = pinecone.Index("scenemate");

// Initialize OpenAI
const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || "********************************************************************************************************************************************************************" 
});

/** 
 * Fetches the recent chat history for a given user and chat.
 * @param userId - The ID of the user.
 * @param chatId - The ID of the chat.
 * @param messageLimit - The maximum number of messages to fetch (default: 10).
 * @returns A string containing the formatted chat history.
 */
async function fetchChatHistory(userId: string, chatId: string, messageLimit: number = 10): Promise<string> {
  // Use the imported db instance instead of creating a new one
  const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
  const messagesQuery = query(messagesRef, orderBy("createdAt", "desc"), limit(messageLimit));
  const messagesSnapshot = await getDocs(messagesQuery);

  if (messagesSnapshot.empty) return "";

  const messages = messagesSnapshot.docs
    .map(doc => ({ role: doc.data().role, text: doc.data().text || "", timestamp: doc.data().createdAt }))
    .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

  return messages.map(msg => `${msg.role === "user" ? "User" : "Assistant"}: ${msg.text}`).join("\n");
}

/**
 * Handles POST requests to process a message, generate a response, and return text and audio.
 * @param req - The incoming Next.js request object.
 * @returns A JSON response with the processed content and audio URL or an error message.
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  console.log("Processing message request...");
  
  // Step 1: Parse and validate request parameters
  const { userId, chatId, messageText, fileDocumentId } = await req.json();
  
  if (!userId || !chatId || !messageText) {
    console.error("Missing required parameters");
    return NextResponse.json(
      { success: false, error: "Missing required parameters" }, 
      { status: 400 }
    );
  }

  if (!fileDocumentId) {
    console.error("Missing fileDocumentId - required for message processing");
    return NextResponse.json(
      { success: false, error: "Missing fileDocumentId parameter" }, 
      { status: 400 }
    );
  }

  console.log(`Processing message for user ${userId}, chat ${chatId}, file ${fileDocumentId}`);
  let streamedContent = "";
  
  try {
    // IMPORTANT: Store the user message first with fileDocumentId
    // This ensures the user message has the correct file association
    console.log("Storing user message with fileDocumentId");
    await addMessage(
      userId,
      chatId,
      messageText,
      "user",
      fileDocumentId
    );
    
    // Step 2: Convert message to vector for similarity search
    const queryVector = await convertPromptToVector(messageText);
    
    // Step 3: Use fileDocumentId as namespace for Pinecone search
    let namespace = fileDocumentId;
    
    // Step 4: Query Pinecone for relevant chunks using the determined namespace
    console.log(`Querying Pinecone namespace: ${namespace}`);
    const pineconeResponse = await pineconeIndex.namespace(namespace).query({
      vector: queryVector,
      topK: 5,
      includeValues: true,
      includeMetadata: true,
    });

    // Step 5: Extract and validate chunk IDs
    const chunkIds = pineconeResponse.matches
      ?.map(match => match.metadata?.chunk_id as string)
      .filter(Boolean) || [];
    
    console.log(`Found ${chunkIds.length} relevant chunks`);
    
    // Step 6: Fetch the actual document chunks
    let chunks: any[] = [];
    if (chunkIds.length > 0) {
      chunks = await fetchDocumentChunksByChunkIds(chunkIds, userId);
      console.log(`Retrieved ${chunks.length} document chunks`);
    }

    // Step 7: Get relevant chat history
    const relevantHistory = await getRelevantHistory(
      messageText, 
      await fetchChatHistory(userId, chatId)
    );

    // Step 8: Construct prompt for the AI model
    const systemMessage = {
      role: "system" as const,
      content: `You are an AI Script/Rehearsal Assistant that helps actors 
      practice their lines and prepare for performances. 
      The following are your strict instructions:
      1. You must always format your response in Markdown.
      2. Your purpose is to serve as a reliable rehearsal partner who can read opposite parts and help with line memorization.
      
As a rehearsal partner:

Read the other character's lines clearly when we practice scenes together
Prompt me when I forget my lines or make errors in the dialogue
Adjust your reading pace and emotional tone based on my requests
Continue the scene from wherever I indicate if we need to restart
Maintain awareness of the full script structure and character relationships

Your capabilities include:

Working with any script I provide, regardless of format
Distinguishing between different characters' dialogue
Detecting when I've skipped lines or made errors
Being available whenever I need to practice
Adapting to my specific rehearsal style and preferences

Your interaction style should be:

Patient and willing to repeat scenes as many times as needed
Non-judgmental about my performance
Clear in your delivery but flexible enough to change based on direction
Professional yet supportive in your approach
Capable of simulating different emotional qualities when reading other parts

When I'm ready to begin, I'll share the script with you, indicate which character I'm playing, and let you know where we should start..

${chunks.length > 0 ? 
`Relevant script chunks:
${chunks.map(chunk => chunk.pageContent).join("\n")}` : 
"No specific script chunks were found for your query. If you're trying to rehearse lines, please make sure you've uploaded a script file first."
}

${relevantHistory ? `Relevant rehearsal history:\n${relevantHistory}` : ""}`
    };

    const userMessage = { 
      role: "user" as const, 
      content: messageText, 
      name: userId 
    };
    
    const messages = [systemMessage, userMessage];

    // Step 9: Generate response using Groq AI
    console.log("Generating AI response");
    await callGroqAiEx({
      messages,
      stream: true,
      onChunk: async (chunk: string) => {
        streamedContent += chunk;
      },
    });

    // Step 10: Generate audio from the response text
    console.log("Generating audio response");
    const audioResponse = await openai.audio.speech.create({
      model: "tts-1-hd",
      voice: "echo",
      input: streamedContent,
    });
    
    const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());
    const audioFileName = `response-${Date.now()}.mp3`;

    // Step 11: Upload audio to Firebase Storage - using the imported storage instance
    console.log("Uploading audio file");
    const storageRef = ref(storage, `audio/${userId}/rehearsals/${audioFileName}`);
    await uploadBytes(storageRef, audioBuffer, { contentType: "audio/mp3" });
    const audioUrl = await getDownloadURL(storageRef);

    // IMPORTANT: Store the assistant message with fileDocumentId
    // This ensures the assistant response also has the correct file association
    console.log("Storing assistant response with fileDocumentId");
    await addMessage(
      userId,
      chatId,
      streamedContent,
      "assistant",
      fileDocumentId,
      { audioUrl } // Include audioUrl as an additional field
    );

    // Step 12: Return successful response with content and audio URL
    console.log("Processing complete");
    return NextResponse.json({ 
      success: true, 
      content: streamedContent, 
      audioUrl 
    });
    
  } catch (error) {
    // Comprehensive error handling
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    console.error(`Error in processMessage: ${errorMessage}`);
    
    // If we have a more specific error, include it in the response
    let statusCode = 500;
    let detailedError = errorMessage;
    
    if (error instanceof Error) {
      if (errorMessage.includes("not found") || errorMessage.includes("does not exist")) {
        statusCode = 404;
      } else if (errorMessage.includes("permission") || errorMessage.includes("unauthorized")) {
        statusCode = 403;
      }
    }
    
    return NextResponse.json({ 
      success: false, 
      error: detailedError 
    }, { 
      status: statusCode 
    });
  }
}

/**
 * Handles OPTIONS requests for CORS preflight.
 * @param req - The incoming Next.js request object.
 * @returns An empty JSON response with a 200 status.
 */
export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}