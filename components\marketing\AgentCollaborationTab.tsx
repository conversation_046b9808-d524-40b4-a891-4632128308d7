'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { collection, query, getDocs } from 'firebase/firestore';
import { db } from '../firebase';
import MarkdownRenderer from '../MarkdownRenderer';
import { <PERSON>rkles, ArrowRight, Search, HelpCircle, Brain, CheckCircle, ChevronDown, Settings, Edit3, Play } from 'lucide-react';

type ModelProvider = 'openai' | 'anthropic' | 'groq' | 'google';
type AgentType = 'strategic-director' | 'query-documents' | 'question-answer' | 'user';

interface AgentMessage {
  from: AgentType;
  to: AgentType | 'user';
  message: string;
  timestamp: Date;
  thinking?: string;
  isLoading?: boolean;
}

interface CollaborationProps {
  selectedProvider: ModelProvider;
  selectedModel: string;
  onNavigateToOutputs?: () => void;
}

// Document interface for Firestore documents
interface Document {
  id:string;
  name: string;
  category: string;
  namespace: string;
}

// Agent Output interface for Firestore documents
interface AgentOutput {
  id: string;
  requestId: string;
  timestamp: { seconds: number; nanoseconds: number } | number | { _seconds: number; _nanoseconds: number };
  agentType: string;
  prompt: string;
  result: {
    thinking?: string;
    output: string;
    documentUrl?: string;
  };
  modelInfo: {
    provider: string;
    model: string;
  };
  isCollaboration?: boolean;
  agentMessages?: {
    from: string;
    to: string;
    message: string;
  }[];
}

// Interface for collaboration request body
interface CollaborationRequestBody {
  prompt: string;
  modelProvider: ModelProvider;
  modelName: string;
  userId: string;
  context?: string;
  documentReferences?: string[];
  category?: string;
}

const AgentCollaborationTab: React.FC<CollaborationProps> = ({
  selectedProvider: initialProvider,
  selectedModel: initialModel,
  onNavigateToOutputs,
}) => {
  const { data: session } = useSession();
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingError, setProcessingError] = useState<string | null>(null);
  const [conversation, setConversation] = useState<AgentMessage[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>(initialProvider);
  const [selectedModel, setSelectedModel] = useState(initialModel);
  const [showToast, setShowToast] = useState(false);
  const [showOutputStoredToast, setShowOutputStoredToast] = useState(false);
  const [showInternalMessages, setShowInternalMessages] = useState(false);

  // Agent outputs state
  const [outputs, setOutputs] = useState<AgentOutput[]>([]);
  const [selectedOutput, setSelectedOutput] = useState<AgentOutput | null>(null);
  const [loadingOutputs, setLoadingOutputs] = useState<boolean>(true);
  const [outputsError, setOutputsError] = useState<string | null>(null);

  // Context options state
  const [customContext, setCustomContext] = useState<string>('');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [documentSearchQuery, setDocumentSearchQuery] = useState<string>('');
  const [isDocDropdownOpen, setIsDocDropdownOpen] = useState<boolean>(false);
  const [isCatDropdownOpen, setIsCatDropdownOpen] = useState<boolean>(false);
  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(true);
  const docDropdownRef = useRef<HTMLDivElement>(null);
  const catDropdownRef = useRef<HTMLDivElement>(null);

  interface LogEntry {
    text: string;
    status: 'entering' | 'active' | 'exiting';
    id: number;
  }

  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [nextLogId, setNextLogId] = useState(0);
  const [showLogs, setShowLogs] = useState(false);
  const [predefinedPrompts, setPredefinedPrompts] = useState<string[]>([
    "Develop a comprehensive content strategy based on our existing marketing documents",
    "Research our competitors' social media strategies and recommend improvements to our approach",
    "Analyze our Q1 marketing performance and suggest strategic adjustments for Q2",
    "Create a marketing plan for our new AI-powered product that targets enterprise customers",
    "Generate a PDF report analyzing the effectiveness of our email marketing campaigns"
  ]);

  const providerModels: Record<ModelProvider, string[]> = {
    'openai': ['gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'],
    'anthropic': ['claude-sonnet-4-0', 'claude-opus-4-0', 'claude-sonnet-4-0', 'claude-3-5-sonnet', 'claude-3-haiku'],
    'groq': ['deepseek-r1-distill-llama-70b', 'llama-3.3-70b-versatile'],
    'google': ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.5-flash', 'gemini-2.5-pro']
  };

  const handlePredefinedPrompt = (promptText: string) => {
    setPrompt(promptText);
  };

  // Function to fetch agent outputs
  const fetchAgentOutputs = async () => {
    setLoadingOutputs(true);
    try {
      const response = await fetch('/api/agent-outputs?limit=20');
      if (!response.ok) {
        throw new Error(`Error fetching agent outputs: ${response.statusText}`);
      }
      const data = await response.json();
      const outputsData = data.results || [];

      // Process the outputs to identify collaboration outputs
      const processedOutputs = outputsData.map((output: AgentOutput) => {
        // Check if this is a collaboration output (Strategic Analysis)
        const isCollaboration =
          (output.agentMessages && output.agentMessages.length > 0) ||
          (output.result && output.result.output && output.result.output.includes('Strategic Analysis'));

        return {
          ...output,
          isCollaboration
        };
      });

      // Filter to only show Strategic Analysis outputs
      const strategicOutputs = processedOutputs.filter((output: AgentOutput) =>
        output.isCollaboration || output.agentType === 'strategic-director'
      );

      setOutputs(strategicOutputs);

      // If there are outputs, select the most recent one by default
      if (strategicOutputs.length > 0) {
        setSelectedOutput(strategicOutputs[0]);
      }

      setOutputsError(null);
    } catch (err) {
      console.error('Error fetching agent outputs:', err);
      setOutputsError('Failed to load agent outputs');
    } finally {
      setLoadingOutputs(false);
    }
  };

  // Function to format timestamp
  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';

    // Handle different timestamp formats that might come from Firebase
    let date: Date;

    if (typeof timestamp === 'number') {
      // Handle timestamp as milliseconds
      date = new Date(timestamp);
    } else if (timestamp.seconds !== undefined && timestamp.nanoseconds !== undefined) {
      // Handle Firestore Timestamp format
      date = new Date(timestamp.seconds * 1000);
    } else if (timestamp._seconds !== undefined && timestamp._nanoseconds !== undefined) {
      // Handle serialized Firestore Timestamp format
      date = new Date(timestamp._seconds * 1000);
    } else if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      // Handle Firestore Timestamp object with toDate method
      date = timestamp.toDate();
    } else {
      // Fallback: try to parse as Date object
      date = new Date(timestamp);
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid timestamp format:', timestamp);
      return 'Invalid date';
    }

    // Format the date manually without using date-fns
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = months[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();

    // Format time (h:mm a)
    let hours = date.getHours();
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${month} ${day}, ${year} ${hours}:${minutes} ${ampm}`;
  };

  // Function to format agent type
  const formatAgentType = (type: string) => {
    return type
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Function to handle selecting an output
  const handleSelectOutput = (output: AgentOutput) => {
    setSelectedOutput(output);
  };

  useEffect(() => {
    async function fetchDocumentsAndCategories() {
      if (!session?.user?.email) return;
      try {
        setLoadingDocuments(true);
        const userId = session.user.email;
        const filesRef = collection(db, `users/${userId}/files`);
        const q = query(filesRef);
        const querySnapshot = await getDocs(q);

        // All documents for reference
        const allDocs: Document[] = [];

        // Documents with category 'Unknown' for document list
        const unknownCategoryDocs: Document[] = [];

        // Set to collect categories that are not 'Unknown'
        const nonUnknownCategories = new Set<string>();

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          const category = data.category || 'Uncategorized';

          const documentObj = {
            id: doc.id,
            name: data.name || 'Unnamed Document',
            category: category,
            namespace: data.namespace || ''
          };

          // Add to all documents
          allDocs.push(documentObj);

          // Filter documents with category 'Unknown' for document list
          if (category === 'Unknown') {
            unknownCategoryDocs.push(documentObj);
          }
          // Collect non-Unknown categories for category list
          else {
            nonUnknownCategories.add(category);
          }
        });

        // Store all documents for reference
        setDocuments(allDocs);

        // Set filtered documents to show only 'Unknown' category documents
        setFilteredDocuments(unknownCategoryDocs);

        // Set categories to show only non-'Unknown' categories, sorted alphabetically
        setCategories(Array.from(nonUnknownCategories).sort());

        console.log(`Loaded ${unknownCategoryDocs.length} documents with 'Unknown' category`);
        console.log(`Loaded ${nonUnknownCategories.size} categories (excluding 'Unknown')`);
      } catch (err) {
        console.error('Error fetching documents:', err);
      } finally {
        setLoadingDocuments(false);
      }
    }
    fetchDocumentsAndCategories();
  }, [session]);

  useEffect(() => {
    // Get only documents with category 'Unknown'
    const unknownCategoryDocs = documents.filter(doc => doc.category === 'Unknown');

    if (documentSearchQuery.trim() === '') {
      // If no search query, show all documents with category 'Unknown'
      setFilteredDocuments(unknownCategoryDocs);
      return;
    }

    // Filter within the 'Unknown' category documents
    const queryVal = documentSearchQuery.toLowerCase();
    const filtered = unknownCategoryDocs.filter(doc =>
      doc.name.toLowerCase().includes(queryVal)
    );

    setFilteredDocuments(filtered);
  }, [documentSearchQuery, documents]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (docDropdownRef.current && !docDropdownRef.current.contains(event.target as Node)) setIsDocDropdownOpen(false);
      if (catDropdownRef.current && !catDropdownRef.current.contains(event.target as Node)) setIsCatDropdownOpen(false);
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (showToast) {
      const timer = setTimeout(() => setShowToast(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [showToast]);

  useEffect(() => {
    if (showOutputStoredToast) {
      const timer = setTimeout(() => setShowOutputStoredToast(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [showOutputStoredToast]);

  // Fetch agent outputs when component mounts
  useEffect(() => {
    fetchAgentOutputs();
  }, []);

  const handleProviderChange = (provider: ModelProvider) => {
    setSelectedProvider(provider);
    setSelectedModel(providerModels[provider][0]);
    setShowToast(true);
  };

  const handleModelChange = (model: string) => {
    setSelectedModel(model);
    setShowToast(true);
  };

  const handleDocumentChange = (docName: string) => {
    const selectedDoc = documents.find(d => d.name === docName);
    if (selectedDoc) {
        setSelectedDocument(selectedDoc.namespace); // Store namespace or ID
        // Clear category selection (mutually exclusive with document)
        setSelectedCategory('');
        // Don't clear custom context - it can be combined with document
    } else {
        // If no document is selected (e.g., user clicked but didn't select anything)
        setSelectedDocument('');
    }
    setIsDocDropdownOpen(false);
  };

  const handleCategoryChange = (categoryName: string) => {
    if (categoryName) {
      setSelectedCategory(categoryName);
      // Clear document selection (mutually exclusive with category)
      setSelectedDocument('');
      // Don't clear custom context - it can be combined with category
    } else {
      // If no category is selected (e.g., user clicked but didn't select anything)
      setSelectedCategory('');
    }
    setIsCatDropdownOpen(false);
  };

  const terminalContentRef = React.useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (terminalContentRef.current) {
      terminalContentRef.current.scrollTop = terminalContentRef.current.scrollHeight;
    }
  }, [logs]);

  useEffect(() => {
    if (isLoading) {
      const demoLogs = [
        "Firebase config (components/firebase/config.ts): { apiKey: 'Set', authDomain: 'Set', projectId: 'Set' }",
        `Using model provider: ${selectedProvider}, model: ${selectedModel}`,
        `Processing collaboration request with prompt: ${prompt || '(No prompt provided)'}`,
        "Step 1: Strategic Director analyzes if request is research-related",
        `[StrategicDirectorAgent] Analyzing if request is research-related: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`,
        `[StrategicDirectorAgent] Request classified as RESEARCH-RELATED`,
        `[StrategicDirectorAgent] Handling research request: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`,
        `[StrategicDirectorAgent] Created ResearchInsightsTool instance`,
        `[StrategicDirectorAgent] Determining appropriate research operation...`,
        `[StrategicDirectorAgent] Selected operation: researchMarketingTopic`,
        `[StrategicDirectorAgent] Operation parameters: {"operation":"researchMarketingTopic","topic":"${prompt.substring(0, 30)}...","researchQuestions":["What are the current trends?","Who are the key competitors?"],"useInternetSearch":true}`,
        `[StrategicDirectorAgent] Delegating to ResearchInsightsAgent...`,
        `[StrategicDirectorAgent] Calling researchMarketingTopic with topic: "${prompt.substring(0, 30)}..."`,
        `[ResearchInsightsTool] Calling ResearchInsightsAgent.researchMarketingTopic with topic: "${prompt.substring(0, 30)}..."`,
        `[ResearchInsightsTool] Research questions: ["What are the current trends?","Who are the key competitors?"]`,
        `[ResearchInsightsTool] Using internet search: true`,
        "Step 2: Research Insights Agent conducts research",
        "Initializing internet search capabilities...",
        "Searching for relevant information...",
        "Analyzing search results...",
        "Synthesizing research findings...",
        `[ResearchInsightsTool] ResearchInsightsAgent.researchMarketingTopic completed successfully`,
        `[StrategicDirectorAgent] Research operation completed successfully`,
        `[StrategicDirectorAgent] Formatting research results for user...`,
        "Step 3: Strategic Director formats and returns research results",
        `Processing with ${selectedProvider} model: ${selectedModel}`,
        `[StrategicDirectorAgent] Research request handling completed successfully`,
        `[AGENT_COLLABORATION] Storing collaboration data with requestId: ${Math.random().toString(36).substring(2)}`,
        "[AGENT_COLLABORATION] Successfully stored collaboration data",
        `[AGENT_OUTPUT] Storing strategic analysis output with requestId: ${Math.random().toString(36).substring(2)}`,
        "[AGENT_OUTPUT] Successfully stored strategic analysis output",
        "Research insights PDF generated successfully (simulated for UI)"
      ];
      setLogs([]);
      setNextLogId(0);
      const addLogsWithDelay = async () => {
        const MAX_VISIBLE_LOGS = 8;
        for (let i = 0; i < demoLogs.length; i++) {
          const delay = Math.floor(Math.random() * 500) + 300;
          await new Promise(resolve => setTimeout(resolve, delay));
          const newLogId = nextLogId + i;
          setLogs(prevLogs => {
            let updatedLogs = [...prevLogs];
            if (updatedLogs.length >= MAX_VISIBLE_LOGS) {
              const oldestActiveIndex = updatedLogs.findIndex(log => log.status === 'active');
              if (oldestActiveIndex !== -1) {
                updatedLogs[oldestActiveIndex] = { ...updatedLogs[oldestActiveIndex], status: 'exiting' };
                setTimeout(() => {
                  setLogs(currentLogs => currentLogs.filter(log => log.id !== updatedLogs[oldestActiveIndex].id));
                }, 1500);
              }
            }
            updatedLogs = updatedLogs.map(log => log.status === 'entering' ? { ...log, status: 'active' } : log);
            return [...updatedLogs, { text: demoLogs[i], status: 'entering', id: newLogId }];
          });
          setNextLogId(newLogId + 1);
        }
      };
      addLogsWithDelay();
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, prompt, selectedModel, selectedProvider, session]);

  // Add this function to fetch the latest output after collaboration completes
  const fetchLatestOutput = async (requestId: string) => {
    try {
      // Fetch the specific output by requestId
      const response = await fetch(`/api/agent-outputs/${requestId}`);
      if (!response.ok) {
        throw new Error(`Error fetching output: ${response.statusText}`);
      }

      const output = await response.json();

      // Update the conversation with the actual output from Firestore
      setConversation(prev => {
        // Find the last message from strategic-director to user
        const lastIndex = prev.findLastIndex(
          msg => msg.from === 'strategic-director' && msg.to === 'user'
        );

        if (lastIndex >= 0) {
          // Create a new array with the updated message
          const updated = [...prev];
          updated[lastIndex] = {
            ...updated[lastIndex],
            message: output.result.output,
            thinking: output.result.thinking || updated[lastIndex].thinking
          };
          return updated;
        }

        return prev;
      });

      setProcessingError(null);
    } catch (err) {
      console.error('Error fetching latest output:', err);
      setProcessingError('Failed to load the latest output. Please check the Agent Outputs tab.');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleCollaboration();
  };

  const handleCollaboration = async () => {
    if (!prompt.trim()) return;

    setIsProcessing(true);
    setIsLoading(true);     // Add this line to show the processing UI
    setShowLogs(true);      // Add this line to ensure logs are visible
    setProcessingError(null);

    // Create an AbortController with a 15-minute timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 900000); // 15 minutes

    try {
      // Prepare the collaboration body
      const collaborationBody: CollaborationRequestBody = {
        prompt,
        modelProvider: selectedProvider,
        modelName: selectedModel,
        userId: session?.user?.email || 'anonymous',
      };

      // Add context options if provided
      if (customContext) collaborationBody.context = customContext;
      if (selectedDocument) collaborationBody.documentReferences = [selectedDocument];
      if (selectedCategory) collaborationBody.category = selectedCategory;

      // Use the AbortController's signal in the fetch request
      const response = await fetch('/api/marketing-agent-collaboration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(collaborationBody),
        signal: controller.signal
      });

      // Clear the timeout since the request completed
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.conversation && Array.isArray(data.conversation)) {
        const processedConversation = data.conversation.map((msg: any) => ({
          ...msg, timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
        }));

        setConversation([
          { from: 'user', to: 'strategic-director', message: prompt, timestamp: new Date() },
          ...processedConversation
        ]);

        // If we have a requestId, fetch the latest output
        if (data.requestId) {
          await fetchLatestOutput(data.requestId);
        }
      }

      setPrompt('');
      setShowOutputStoredToast(true);
      setTimeout(() => setShowOutputStoredToast(false), 3000);

    } catch (error: any) {
      console.error('Error in agent collaboration:', error);

      // Handle abort errors specifically
      if (error.name === 'AbortError') {
        setProcessingError('Request timed out after 15 minutes. Please try a simpler query.');
      } else {
        setProcessingError(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      setConversation([
        { from: 'user', to: 'strategic-director', message: prompt, timestamp: new Date() },
        {
          from: 'strategic-director',
          to: 'user',
          message: 'An error occurred. Please try again with a simpler query or try later.',
          timestamp: new Date()
        },
      ]);
    } finally {
      // Make sure to clear the timeout if there was an error
      clearTimeout(timeoutId);
      setIsProcessing(false);
      setIsLoading(false);  // Add this line to hide the processing UI when done
    }
  };

  const getAgentIcon = (agent: AgentType | 'user') => {
    switch (agent) {
      case 'strategic-director': return <Brain className="w-5 h-5 text-purple-400" />;
      case 'query-documents': return <Search className="w-5 h-5 text-blue-400" />;
      case 'question-answer': return <HelpCircle className="w-5 h-5 text-green-400" />;
      case 'user': return <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path></svg>;
      default: return <Sparkles className="w-5 h-5 text-amber-400" />;
    }
  };

  const getAgentName = (agent: AgentType | 'user') => {
    if (agent === 'user') return 'You';
    return agent.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  const getMessageStyle = (agent: AgentType | 'user') => {
    switch (agent) {
      case 'strategic-director': return 'bg-purple-900/30 border-purple-700/50';
      case 'query-documents': return 'bg-blue-900/30 border-blue-700/50';
      case 'question-answer': return 'bg-green-900/30 border-green-700/50';
      case 'user': return 'bg-gray-800 border-gray-700';
      default: return 'bg-amber-900/30 border-amber-700/50';
    }
  };

  return (
    <div className="space-y-8">
      {showToast && (
        <div className="fixed top-4 right-4 z-50 flex items-center p-4 bg-green-800 text-white rounded-lg shadow-lg animate-fadeIn">
          <CheckCircle className="w-5 h-5 mr-2" /><span>Model updated successfully!</span>
        </div>
      )}
      {showOutputStoredToast && (
        <div className="fixed top-4 right-4 z-50 flex items-center p-4 bg-indigo-800 text-white rounded-lg shadow-lg animate-fadeIn">
          <CheckCircle className="w-5 h-5 mr-2" />
          <div>
            <span>Analysis saved to Agent Outputs tab!</span>
            <div className="text-xs mt-1">
              <button
                onClick={() => {
                  // Navigate to the Agent Outputs tab using the prop
                  if (onNavigateToOutputs) {
                    onNavigateToOutputs();
                  }
                  setShowOutputStoredToast(false);
                }}
                className="underline hover:text-white/80"
              >
                Click to view
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Section 1: Configuration */}
      <div className="p-6 bg-gray-800/70 rounded-lg border border-gray-700 shadow-lg">
        <div className="flex items-center mb-6">
            <Settings className="w-6 h-6 mr-3 text-purple-300" />
            <h2 className="text-xl font-semibold text-purple-200">Configuration</h2>
        </div>

        {/* Research Insights Integration Notice */}
        <div className="mb-6 p-3 bg-indigo-900/30 border border-indigo-700/50 rounded-lg">
          <div className="flex items-start">
            <Sparkles className="w-5 h-5 text-indigo-400 mt-1 mr-2 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-indigo-300 mb-1">Research Insights Integration</h3>
              <p className="text-xs text-gray-300">
                The Strategic Director Agent now automatically delegates research-related requests to the Research Insights Agent.
                When you submit a research query, the Strategic Director will analyze it, determine if it's research-related,
                and seamlessly pass it to the Research Insights Agent for specialized processing.
              </p>
            </div>
          </div>
        </div>

        {/* Model Selection */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-2">
            <h3 className="text-lg font-medium text-purple-300">Model Selection</h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-300">Using:</span>
              <span className="bg-purple-700 text-white text-sm px-2 py-1 rounded-md">
                {selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)} / {selectedModel}
              </span>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-900/50 p-3 rounded-lg border border-gray-700/50">
              <h4 className="text-sm font-medium mb-2 text-purple-400">Provider</h4>
              <div className="flex flex-wrap gap-2">
                {Object.keys(providerModels).map((provider) => (
                  <button key={provider} onClick={() => handleProviderChange(provider as ModelProvider)}
                    className={`px-3 py-1 text-xs rounded transition-colors ${selectedProvider === provider ? 'bg-purple-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-200'}`}>
                    {provider.charAt(0).toUpperCase() + provider.slice(1)}
                  </button>
                ))}
              </div>
            </div>
            <div className="bg-gray-900/50 p-3 rounded-lg border border-gray-700/50">
              <h4 className="text-sm font-medium mb-2 text-purple-400">Model</h4>
              <div className="flex flex-wrap gap-2">
                {providerModels[selectedProvider].map((model) => (
                  <button key={model} onClick={() => handleModelChange(model)}
                    className={`px-3 py-1 text-xs rounded transition-colors ${selectedModel === model ? 'bg-purple-600 text-white' : 'bg-[#2d2a3a] hover:bg-[#3d2e5a] text-gray-200'}`}>
                    {model}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Context Options */}
        <div>
          <h3 className="text-lg font-medium mb-3 text-purple-300">Context Options</h3>
          <p className="text-xs text-gray-400 mb-3">
            All context options are optional. You can provide custom context in the text box and combine it with either a document or category selection. Document and category selections are mutually exclusive (document takes precedence if both are selected).
          </p>
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700/50 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">Custom Context (Optional)</label>
              <textarea value={customContext}
                onChange={(e) => {
                  setCustomContext(e.target.value);
                  // Custom context can be combined with document or category
                  // No need to clear other selections
                }}
                className="w-full p-3 bg-gray-700 text-gray-200 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                rows={2} placeholder="Enter custom context for the agent (optional)..." />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative" ref={docDropdownRef}>
                <label className="block text-sm font-medium text-gray-300 mb-1">Files</label>
                <div className="flex items-center justify-between p-3 bg-gray-700 border border-gray-600 rounded-md cursor-pointer"
                  onClick={() => setIsDocDropdownOpen(!isDocDropdownOpen)}>
                  <span className={selectedDocument ? "text-white" : "text-gray-400"}>
                    {documents.find(d => d.namespace === selectedDocument)?.name || selectedDocument || "Select a file (optional)"}
                  </span>
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isDocDropdownOpen ? 'rotate-180' : ''}`} />
                </div>
                {isDocDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
                    <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-600">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input type="text" placeholder="Search documents..." value={documentSearchQuery} onChange={(e) => setDocumentSearchQuery(e.target.value)}
                          className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm" />
                      </div>
                    </div>
                    <div className="py-1">
                      <div className="px-4 py-2 text-xs text-amber-400 border-b border-gray-600">
                        Showing only files with category 'Unknown'
                      </div>
                      <ul className="py-1">
                        {loadingDocuments ? <li className="px-4 py-3 text-gray-400 text-center text-sm">Loading documents...</li>
                          : filteredDocuments.length > 0 ? filteredDocuments.map((doc) => (
                            <li key={doc.id} className="px-4 py-2 hover:bg-purple-900/50 cursor-pointer text-sm" onClick={() => handleDocumentChange(doc.name)}>
                              <div className="font-medium">{doc.name}</div>
                              <div className="text-xs text-gray-400">{doc.category}</div>
                            </li>))
                          : <li className="px-4 py-3 text-gray-400 text-center text-sm">No files found</li>}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
              <div className="relative" ref={catDropdownRef}>
                <label className="block text-sm font-medium text-gray-300 mb-1">Categories</label>
                <div className="flex items-center justify-between p-3 bg-gray-700 border border-gray-600 rounded-md cursor-pointer"
                  onClick={() => setIsCatDropdownOpen(!isCatDropdownOpen)}>
                  <span className={selectedCategory ? "text-white" : "text-gray-400"}>{selectedCategory || "Select a category (optional)"}</span>
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isCatDropdownOpen ? 'rotate-180' : ''}`} />
                </div>
                {isCatDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
                    <div className="py-1">
                      <div className="px-4 py-2 text-xs text-amber-400 border-b border-gray-600">
                        Showing all categories except 'Unknown'
                      </div>
                      <ul className="py-1">
                        {loadingDocuments ? <li className="px-4 py-3 text-gray-400 text-center text-sm">Loading categories...</li>
                          : categories.length > 0 ? categories.map((category, index) => (
                            <li key={index} className="px-4 py-2 hover:bg-purple-900/50 cursor-pointer text-sm" onClick={() => handleCategoryChange(category)}>
                              <div className="font-medium">{category}</div>
                              <div className="text-xs text-gray-400">{documents.filter(doc => doc.category === category).length} documents</div>
                            </li>))
                          : <li className="px-4 py-3 text-gray-400 text-center text-sm">No categories available</li>}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Section 2: Define Your Request */}
      <div className="p-6 bg-gray-800/70 rounded-lg border border-gray-700 shadow-lg">
        <div className="flex items-center mb-6">
            <Edit3 className="w-6 h-6 mr-3 text-purple-300" />
            <h2 className="text-xl font-semibold text-purple-200">Define Your Request</h2>
        </div>

        {/* Predefined Prompts */}
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3 text-purple-300">Quick Start Prompts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {predefinedPrompts.map((p, index) => (
              <button key={index} onClick={() => handlePredefinedPrompt(p)}
                className="p-3 bg-gray-700 hover:bg-gray-600/70 rounded-lg text-left text-sm border border-gray-600/80 transition-all hover:border-purple-500/50">
                {p}
              </button>
            ))}
          </div>
        </div>

        {/* Prompt Input Form */}
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="collaborationPrompt" className="block text-lg font-medium mb-2 text-purple-300">
              Your Strategic Prompt
            </label>
            <textarea id="collaborationPrompt" value={prompt} onChange={(e) => setPrompt(e.target.value)}
              className="w-full p-4 bg-gray-900/50 text-gray-200 border border-gray-700/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              rows={4} placeholder="Enter a marketing request or problem for the Strategic Director Agent..." />
          </div>
          <button type="submit" disabled={isLoading || !prompt.trim()}
            className="w-full md:w-auto px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium disabled:opacity-60 disabled:cursor-not-allowed transition-colors flex items-center justify-center shadow-md hover:shadow-lg">
            {isLoading ? (
              <><svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Processing...</>
            ) : (<><Play className="w-5 h-5 mr-2" />Start Strategic Analysis</>)}
          </button>
        </form>
      </div>

      {/* Section 3: Live Processing & Analysis */}
      {(isLoading || conversation.length > 0 || showLogs) && (
        <div className="p-6 bg-gray-800/70 rounded-lg border border-gray-700 shadow-lg">
          <div className="flex items-center mb-6">
            <Sparkles className="w-6 h-6 mr-3 text-purple-300" />
            <h2 className="text-xl font-semibold text-purple-200">Live Processing & Analysis</h2>
          </div>

          {/* Log Display */}
          {showLogs && (
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-purple-300">Agent Processing Logs</h3>
                <button onClick={() => setShowLogs(!showLogs)} className="px-3 py-1 text-xs rounded bg-gray-700 hover:bg-gray-600 text-gray-200">
                  {showLogs ? 'Hide Logs' : 'Show Logs'}
                </button>
              </div>
              <div className="border border-gray-700/50 rounded-lg bg-black mb-6 overflow-hidden shadow-inner">
                <div className="terminal-header bg-gray-800 px-4 py-2 flex items-center justify-between border-b border-gray-700/50">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div><div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div><div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-xs text-gray-300 ml-2">Agent Processing Console</span>
                  </div>
                  <div className="text-xs text-gray-400">{selectedProvider} / {selectedModel}</div>
                </div>
                <div className="terminal-container font-mono text-xs text-green-400 max-h-60 overflow-y-auto p-4" style={{ position: 'relative' }}>
                  <div ref={terminalContentRef} className="terminal-content" style={{ maxHeight: '240px', overflowY: 'auto', overflowX: 'hidden' }}>
                    {logs.map((log) => (
                      <div key={log.id} className={`terminal-line ${log.status}`}
                        style={{ whiteSpace: 'nowrap', overflow: 'hidden', marginBottom: '2px', animation: log.status === 'entering' ? 'slideIn 1s ease-out forwards' : log.status === 'exiting' ? 'slideOut 1s ease-in forwards' : 'none',
                          color: log.text.includes('Error') || log.text.includes('error') ? '#ff6b6b' : log.text.includes('Success') || log.text.includes('success') || log.text.includes('complete') || log.text.includes('generated') ? '#4ade80' : log.text.includes('Step') || log.text.includes('Processing') ? '#f0db4f' : '#4ade80' }}>
                        {log.text}
                      </div>))}
                    <div className="terminal-cursor"></div>
                  </div>
                </div>
              </div>
              <style jsx>{`
                .terminal-container { background-color: #000; font-family: 'Courier New', monospace; color: #4ade80; position: relative; }
                .terminal-container::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: repeating-linear-gradient(0deg, rgba(0,0,0,0.15), rgba(0,0,0,0.15) 1px, transparent 1px, transparent 2px); pointer-events: none; opacity: 0.5; }
                .terminal-line { position: relative; padding-left: 4px; border-left: 2px solid #4ade80; width: 100%; text-shadow: 0 0 5px rgba(74,222,128,0.5); }
                .terminal-line.entering { transform: translateX(-100%); opacity: 0; }
                .terminal-line.active { transform: translateX(0); opacity: 1; }
                .terminal-line.exiting { transform: translateX(0); opacity: 1; }
                .terminal-cursor { position: relative; display: inline-block; width: 8px; height: 16px; background-color: #4ade80; margin-left: 4px; animation: blink-caret 0.75s step-end infinite; }
                @keyframes slideIn { 0% { transform: translateX(-100%); opacity: 0; } 20% { transform: translateX(-50%); opacity: 0.5; } 100% { transform: translateX(0); opacity: 1; } }
                @keyframes slideOut { 0% { transform: translateX(0); opacity: 1; } 80% { transform: translateX(50%); opacity: 0.5; } 100% { transform: translateX(100%); opacity: 0; } }
                .terminal-content { scroll-behavior: smooth; }
              `}</style>
            </div>
          )}

          {/* Processing Error Display */}
          {processingError && (
            <div className="mb-4 p-3 bg-red-900/30 border border-red-700/50 rounded-lg">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-red-400 mt-1 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-red-300 mb-1">Error</h3>
                  <p className="text-xs text-gray-300">{processingError}</p>
                </div>
              </div>
            </div>
          )}

          {/* Conversation Display */}
          {conversation.length > 0 && (
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-purple-300">Strategic Analysis Breakdown</h3>
                <div className="flex items-center">
                  <label htmlFor="showInternalMessages" className="mr-2 text-sm text-gray-300">Show intermediate steps</label>
                  <div className="relative inline-block w-10 mr-2 align-middle select-none">
                    <input type="checkbox" id="showInternalMessages" checked={showInternalMessages} onChange={() => setShowInternalMessages(!showInternalMessages)} className="sr-only" />
                    <div className={`block h-6 rounded-full w-10 ${showInternalMessages ? 'bg-purple-600' : 'bg-gray-600'}`}></div>
                    <div className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform transform ${showInternalMessages ? 'translate-x-4' : ''}`}></div>
                  </div>
                </div>
              </div>
              <div className="border border-gray-700/50 rounded-lg bg-gray-900/30 p-4 space-y-4 shadow-inner">
                {conversation
                  .filter(message => {
                    if (message.from === 'user') return true;
                    if (message.from === 'strategic-director' && message.to === 'user') {
                      const lastDirectorMessage = [...conversation].reverse().find(msg => msg.from === 'strategic-director' && msg.to === 'user');
                      return message === lastDirectorMessage;
                    }
                    return showInternalMessages;
                  })
                  .map((message, index) => (
                    <div key={index} className={`p-4 rounded-lg border ${getMessageStyle(message.from)}`}>
                      <div className="flex items-center mb-3">
                        <div className="mr-2">{getAgentIcon(message.from)}</div>
                        <div className="font-medium text-gray-200">{getAgentName(message.from)}</div>
                        {message.to !== 'user' && (
                          <>
                            <ArrowRight className="mx-2 text-gray-500 w-4 h-4" />
                            <div className="mr-2">{getAgentIcon(message.to)}</div>
                            <div className="font-medium text-gray-200">{getAgentName(message.to)}</div>
                          </>
                        )}
                        <div className="ml-auto text-xs text-gray-500">{message.timestamp.toLocaleTimeString()}</div>
                      </div>

                      {message.isLoading ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse delay-150"></div>
                          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse delay-300"></div>
                          <span className="text-gray-400 ml-2">Thinking...</span>
                        </div>
                      ) : (
                        <>
                          {message.thinking && (
                            <div className="mb-3">
                              <div className="flex items-center mb-1 text-sm font-medium text-amber-300">
                                <Brain className="w-4 h-4 mr-2 text-amber-400" />Agent Reasoning
                              </div>
                              <div className="bg-black/30 border border-amber-800/40 rounded p-3 text-sm text-gray-300 max-h-60 overflow-y-auto custom-scrollbar">
                                <MarkdownRenderer content={message.thinking} />
                              </div>
                            </div>
                          )}
                          {message.message && (
                             <div>
                                <div className="flex items-center mb-1 text-sm font-medium text-purple-300">
                                    <Sparkles className="w-4 h-4 mr-2 text-purple-400" />
                                    {message.from === 'strategic-director' && message.to === 'user' ? 'Final Analysis Output' : 'Agent Message'}
                                </div>
                                <div className="bg-black/30 border border-purple-800/40 rounded p-3 text-sm text-gray-300">
                                    <div className="prose prose-sm prose-invert max-w-none">
                                    <MarkdownRenderer content={message.message} />
                                    </div>
                                </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AgentCollaborationTab;


