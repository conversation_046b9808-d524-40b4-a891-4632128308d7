/**
 * Centralized timeout configuration for long-running operations
 * 
 * This configuration provides appropriate timeout values for different types of operations,
 * with environment variable support for runtime configuration.
 */

export interface TimeoutConfig {
  /** Request timeout in milliseconds */
  requestTimeout: number;
  /** Connection timeout in milliseconds */
  connectionTimeout: number;
  /** Keep-alive timeout in milliseconds */
  keepAliveTimeout: number;
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Delay between retry attempts in milliseconds */
  retryDelay: number;
}

export interface BusinessAnalysisTimeoutConfig extends TimeoutConfig {
  /** Analysis type specific timeouts */
  analysisTypeTimeouts: {
    system: number;
    product: number;
    requirements: number;
    specification: number;
    comprehensive: number;
  };
  /** Per-analyst additional timeout in milliseconds */
  perAnalystTimeout: number;
}

/**
 * Default timeout configurations
 */
export const DEFAULT_TIMEOUTS = {
  /** Standard API request timeout (5 minutes) */
  STANDARD_REQUEST: 5 * 60 * 1000,
  
  /** Long-running operation timeout (30 minutes) */
  LONG_RUNNING: 30 * 60 * 1000,
  
  /** Extended operation timeout (60 minutes) */
  EXTENDED: 60 * 60 * 1000,
  
  /** Connection establishment timeout (30 seconds) */
  CONNECTION: 30 * 1000,
  
  /** Keep-alive timeout (5 minutes) */
  KEEP_ALIVE: 5 * 60 * 1000,
  
  /** Retry delay (5 seconds) */
  RETRY_DELAY: 5 * 1000,
} as const;

/**
 * Business Analysis specific timeout configuration
 */
export const BUSINESS_ANALYSIS_TIMEOUTS: BusinessAnalysisTimeoutConfig = {
  // Base timeout for business analysis requests (45 minutes)
  requestTimeout: parseInt(process.env.BA_REQUEST_TIMEOUT || '2700000'), // 45 minutes
  
  // Connection timeout (30 seconds)
  connectionTimeout: parseInt(process.env.BA_CONNECTION_TIMEOUT || '30000'),
  
  // Keep-alive timeout (10 minutes)
  keepAliveTimeout: parseInt(process.env.BA_KEEP_ALIVE_TIMEOUT || '600000'),
  
  // Maximum retry attempts
  maxRetries: parseInt(process.env.BA_MAX_RETRIES || '2'),
  
  // Retry delay (10 seconds)
  retryDelay: parseInt(process.env.BA_RETRY_DELAY || '10000'),
  
  // Analysis type specific timeouts (in milliseconds)
  analysisTypeTimeouts: {
    // System analysis: 20 minutes
    system: parseInt(process.env.BA_SYSTEM_TIMEOUT || '1200000'),
    
    // Product overview: 15 minutes  
    product: parseInt(process.env.BA_PRODUCT_TIMEOUT || '900000'),
    
    // Requirements engineering: 25 minutes
    requirements: parseInt(process.env.BA_REQUIREMENTS_TIMEOUT || '1500000'),
    
    // Specification development: 30 minutes
    specification: parseInt(process.env.BA_SPECIFICATION_TIMEOUT || '1800000'),
    
    // Comprehensive analysis: 60 minutes
    comprehensive: parseInt(process.env.BA_COMPREHENSIVE_TIMEOUT || '3600000'),
  },
  
  // Additional timeout per analyst (5 minutes)
  perAnalystTimeout: parseInt(process.env.BA_PER_ANALYST_TIMEOUT || '300000'),
};

/**
 * PMO notification timeout configuration
 */
export const PMO_NOTIFICATION_TIMEOUTS: TimeoutConfig = {
  // Request timeout for PMO notifications (50 minutes to accommodate BA processing)
  requestTimeout: parseInt(process.env.PMO_REQUEST_TIMEOUT || '3000000'), // 50 minutes
  
  // Connection timeout (30 seconds)
  connectionTimeout: parseInt(process.env.PMO_CONNECTION_TIMEOUT || '30000'),
  
  // Keep-alive timeout (15 minutes)
  keepAliveTimeout: parseInt(process.env.PMO_KEEP_ALIVE_TIMEOUT || '900000'),
  
  // Maximum retry attempts
  maxRetries: parseInt(process.env.PMO_MAX_RETRIES || '1'),
  
  // Retry delay (30 seconds)
  retryDelay: parseInt(process.env.PMO_RETRY_DELAY || '30000'),
};

/**
 * Calculate dynamic timeout based on analysis type and analyst count
 */
export function calculateBusinessAnalysisTimeout(
  analysisType: keyof BusinessAnalysisTimeoutConfig['analysisTypeTimeouts'],
  analystCount: number = 1
): number {
  const baseTimeout = BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts[analysisType];
  const additionalTimeout = BUSINESS_ANALYSIS_TIMEOUTS.perAnalystTimeout * Math.max(0, analystCount - 1);
  
  return baseTimeout + additionalTimeout;
}

/**
 * Get fetch configuration for long-running requests
 */
export function getLongRunningFetchConfig(timeoutMs: number): RequestInit {
  return {
    // Use AbortController for timeout management
    signal: AbortSignal.timeout(timeoutMs),
    
    // Enable keep-alive for long connections
    keepalive: true,
    
    // Headers for optimal connection handling
    headers: {
      'Connection': 'keep-alive',
      'Cache-Control': 'no-cache',
    },
  };
}

/**
 * Create AbortController with timeout
 */
export function createTimeoutController(timeoutMs: number): {
  controller: AbortController;
  timeoutId: NodeJS.Timeout;
  cleanup: () => void;
} {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, timeoutMs);
  
  const cleanup = () => {
    clearTimeout(timeoutId);
  };
  
  return { controller, timeoutId, cleanup };
}

/**
 * Retry configuration for failed requests
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

export const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 5000, // 5 seconds
  maxDelay: 60000, // 1 minute
  backoffMultiplier: 2,
};

/**
 * Calculate retry delay with exponential backoff
 */
export function calculateRetryDelay(attempt: number, config: RetryConfig = DEFAULT_RETRY_CONFIG): number {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
  return Math.min(delay, config.maxDelay);
}

/**
 * Timeout error types for better error handling
 */
export enum TimeoutErrorType {
  REQUEST_TIMEOUT = 'REQUEST_TIMEOUT',
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT',
  KEEP_ALIVE_TIMEOUT = 'KEEP_ALIVE_TIMEOUT',
  ABORT_TIMEOUT = 'ABORT_TIMEOUT',
}

/**
 * Enhanced timeout error class
 */
export class TimeoutError extends Error {
  constructor(
    public type: TimeoutErrorType,
    public timeoutMs: number,
    message?: string
  ) {
    super(message || `Operation timed out after ${timeoutMs}ms (${type})`);
    this.name = 'TimeoutError';
  }
}

/**
 * Validate timeout configuration
 */
export function validateTimeoutConfig(config: TimeoutConfig): void {
  if (config.requestTimeout <= 0) {
    throw new Error('Request timeout must be positive');
  }
  if (config.connectionTimeout <= 0) {
    throw new Error('Connection timeout must be positive');
  }
  if (config.maxRetries < 0) {
    throw new Error('Max retries cannot be negative');
  }
  if (config.retryDelay < 0) {
    throw new Error('Retry delay cannot be negative');
  }
}

// Validate configurations on module load
validateTimeoutConfig(BUSINESS_ANALYSIS_TIMEOUTS);
validateTimeoutConfig(PMO_NOTIFICATION_TIMEOUTS);
