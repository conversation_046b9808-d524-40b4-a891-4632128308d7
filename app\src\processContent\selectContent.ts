/**
 * ContentSelector.ts
 *
 * This module implements vector-based content selection using Pinecone for similarity search
 * and Firestore for document storage. It manages the retrieval and processing of relevant
 * document chunks based on vector similarity queries across multiple namespaces.
 *
 * Key features:
 * - Vector similarity search using Pinecone
 * - Document chunk retrieval from Firestore
 * - Cross-namespace content querying
 * - Relevance-based content processing
 * - Token-aware content selection
 */

import { Pinecone } from "@pinecone-database/pinecone";
import { FirestoreStore } from "lib/FirestoreStore";
import { DocumentProcessor } from "lib/optimizedDocumentProcessing";
import { fetchDocumentChunksByChunkIds } from "lib/fetchDocumentChunksByChunkIds";
import { TokenManagement } from '../tokenTracker/tokenManagement';
import { QueryMatch, ContentSelectionResult, Source } from '../types/shared';
import { adminDb } from "components/firebase-admin";

/**
 * Manages content selection through vector similarity search and document processing.
 * Integrates with Pinecone for vector search and Firestore for document storage.
 */
export class ContentSelector {
  private pineconeIndex: any;
  private firestoreStore: FirestoreStore;
  private documentProcessor: DocumentProcessor;

  /**
   * Initializes content selector with user-specific storage paths
   *
   * @param userId - User identifier for scoping document storage
   */
  constructor(userId: string) {
    this.pineconeIndex = new Pinecone().Index(process.env.PINECONE_INDEX!);
    this.firestoreStore = new FirestoreStore({
      collectionPath: `users/${userId}/byteStoreCollection`
    });
    this.documentProcessor = new DocumentProcessor();
  }

  /**
   * Normalizes chunk IDs by removing duplicate segments while preserving the final identifier
   *
   * @param chunkId - Raw chunk identifier that may contain duplicates
   * @returns Cleaned chunk identifier
   */
  private cleanChunkId(chunkId: string): string {
    const parts = chunkId.split('_');
    if (parts.length > 2) {
      const uniqueParts = [...new Set(parts.slice(0, -1))];
      return `${uniqueParts.join('_')}_${parts[parts.length - 1]}`;
    }
    return chunkId;
  }

  /**
   * Queries a specific namespace in Pinecone for similar vectors
   *
   * @param namespace - Namespace to query within Pinecone
   * @param queryVector - Vector to find similarities against
   * @returns Array of matching results with metadata
   */
  private async queryNamespace(
    namespace: string,
    queryVector: number[]
  ): Promise<QueryMatch[]> {
    try {
      console.log(`Querying namespace: ${namespace}`);
      const queryResponse = await this.pineconeIndex.namespace(namespace).query({
        vector: queryVector,
        topK: 5,  // Retrieve top 5 most similar vectors
        includeValues: true,
        includeMetadata: true,
      });

      // Clean and normalize chunk IDs in the response
      const mappedMatches = (queryResponse.matches || []).map((match: { metadata: { chunkId: { toString: () => string; }; }; id: any; }) => ({
        ...match,
        metadata: {
          ...match.metadata,
          chunkId: this.cleanChunkId(
            match.metadata?.chunkId ?
            match.metadata.chunkId.toString() :
            `${namespace}_${match.id}`
          )
        }
      }));

      mappedMatches.forEach((match: { metadata: { chunkId: string; }; }) => {
        console.log(`  - Chunk ID from ${namespace}: ${match.metadata.chunkId}`);
      });

      return mappedMatches;
    } catch (error) {
      console.error(`Error querying namespace ${namespace}:`, error);
      return [];
    }
  }

  /**
   * Selects and processes content across multiple namespaces based on vector similarity
   *
   * @param queryVector - Vector representation of the query
   * @param namespaces - Array of namespaces to search within
   * @param tokenManager - Manager for tracking token usage
   * @returns Processed content with metadata or null if no relevant content found
   */
  public async selectContent(
    queryVector: number[],
    namespaces: string[],
    tokenManager: TokenManagement
  ): Promise<ContentSelectionResult | null> {
    try {
      // Query all specified namespaces in parallel
      const queryResults = await Promise.all(
        namespaces.map(namespace => this.queryNamespace(namespace, queryVector))
      );

      // Extract and filter metadata from all results
      const allMetadata = queryResults
        .flat()
        .map(match => match.metadata)
        .filter((metadata): metadata is NonNullable<typeof metadata> => !!metadata);

      // Get unique chunk IDs across all results
      const uniqueChunkIds = [...new Set(allMetadata.map(metadata => metadata.chunkId))];
      console.log("Unique Chunk IDs after Pinecone filtering:", uniqueChunkIds);

      if (uniqueChunkIds.length === 0) {
        console.warn("No chunk IDs found in Pinecone. Attempting to fetch document content directly.");

        // Try to fetch document content directly from raw_content collection
        try {
          const directContent = await this.fetchDirectDocumentContent(namespaces);

          if (directContent && directContent.length > 0) {
            console.log("Successfully retrieved direct document content");

            // Create a synthetic result with the direct content
            return this.createSyntheticResult(directContent, namespaces);
          }
        } catch (directError) {
          console.error("Error fetching direct document content:", directError);
        }

        return null;
      }

      // Fetch actual document chunks from Firestore
      const documentChunks = await fetchDocumentChunksByChunkIds(
        uniqueChunkIds,
        this.firestoreStore
      );

      if (documentChunks.length === 0) {
        console.warn("No document chunks found in Firestore. Attempting to fetch document content directly.");

        // Try to fetch document content directly from raw_content collection
        try {
          const directContent = await this.fetchDirectDocumentContent(namespaces);

          if (directContent && directContent.length > 0) {
            console.log("Successfully retrieved direct document content");

            // Create a synthetic result with the direct content
            return this.createSyntheticResult(directContent, namespaces);
          }
        } catch (directError) {
          console.error("Error fetching direct document content:", directError);
        }

        return null;
      }

      // Enrich chunks with vector search data
      const enrichedChunks = await Promise.all(
        documentChunks.map(async doc => {
          const matchingPineconeData = queryResults.flat().find(
            match => this.cleanChunkId(match.metadata?.chunkId!) === doc.metadata.chunk_id
          );

          return {
            ...doc,
            values: matchingPineconeData?.values,
            metadata: {
              ...doc.metadata,
              namespace: matchingPineconeData?.metadata?.namespace
            }
          };
        })
      );

      // Process and select most relevant chunks
      const processedData = await this.documentProcessor.selectRelevantChunks(
        enrichedChunks,
        queryVector,
        tokenManager.getTokenConfig()
      );

      // Transform source metadata into expected format and include the actual text content
      const transformedSources: Source[] = processedData.metadata.sources.map(source => {
        // Find the matching chunk to get the actual content
        // The source object from selectRelevantChunks doesn't have chunkId, so we need to match by doc_id
        const matchingChunk = documentChunks.find(chunk =>
          chunk.id === source.doc_id ||
          chunk.metadata?.doc_id === source.doc_id ||
          chunk.metadata?.chunk_id?.includes(source.doc_id)
        );

        // Log the chunk content for debugging
        if (matchingChunk) {
          const chunkContent = matchingChunk.pageContent ||
                              (matchingChunk as any).content ||
                              '';
          console.log(`ContentSelector: Found matching chunk for ${source.doc_id}. Content preview: ${
            chunkContent.substring(0, 100)
          }...`);
        } else {
          console.log(`ContentSelector: No matching chunk found for ${source.doc_id}`);
        }

        // Get the content from the matching chunk
        const chunkContent = matchingChunk ?
                            (matchingChunk.pageContent ||
                             (matchingChunk as any).content ||
                             '') : '';

        return {
          title: source.title,
          page: source.page,
          doc_id: source.doc_id || 'unknown',
          relevance: source.relevance,
          // Include the actual text content from the chunk
          text_content: chunkContent,
          // Include the chunk ID for reference - use the doc_id as the chunkId
          chunkId: source.doc_id
        };
      });

      // Log the transformed sources for debugging
      console.log(`ContentSelector: Transformed ${transformedSources.length} sources with content. First source preview:`,
        transformedSources.length > 0 ? {
          title: transformedSources[0].title,
          doc_id: transformedSources[0].doc_id,
          chunkId: transformedSources[0].chunkId,
          contentPreview: transformedSources[0].text_content?.substring(0, 100) + '...'
        } : 'No sources'
      );

      // Return final processed content with metadata
      return {
        content: processedData.content,
        metadata: {
          sources: transformedSources,
          totalTokens: processedData.metadata.totalTokens,
          chunkCount: processedData.metadata.chunkCount,
          averageRelevance: processedData.metadata.averageRelevance,
          namespaceDistribution: processedData.metadata.namespaceDistribution
        }
      };

    } catch (error) {
      console.error("Error in content selection:", error);
      return null;
    }
  }

  /**
   * Fetches document content directly from Firestore as a fallback when vector search fails
   *
   * @param namespaces - Array of namespaces (document IDs) to fetch
   * @returns Array of document content objects with title, content, and metadata
   */
  private async fetchDirectDocumentContent(namespaces: string[]): Promise<Array<{
    docId: string;
    title: string;
    content: string;
    page?: number;
  }>> {
    const results: Array<{
      docId: string;
      title: string;
      content: string;
      page?: number;
    }> = [];

    // Try to fetch each document directly from Firestore
    await Promise.all(namespaces.map(async (namespace) => {
      try {
        // First, try to get the document from the files collection to get metadata
        const fileSnapshot = await adminDb.collection('users')
          .doc(this.firestoreStore.collectionPath.split('/')[1]) // Extract userId from collectionPath
          .collection('files')
          .where('namespace', '==', namespace)
          .limit(1)
          .get();

        if (!fileSnapshot.empty) {
          const fileDoc = fileSnapshot.docs[0];
          const fileData = fileDoc.data();

          // Now try to get the actual content from the raw_content collection
          const contentSnapshot = await adminDb.collection('users')
            .doc(this.firestoreStore.collectionPath.split('/')[1])
            .collection('raw_content')
            .doc(namespace)
            .get();

          if (contentSnapshot.exists) {
            const contentData = contentSnapshot.data();
            results.push({
              docId: namespace,
              title: fileData.name || `Document ${namespace}`,
              content: contentData?.content || contentData?.text || '',
              page: 1
            });
          } else {
            // If no raw_content, try to get content from the file document itself
            if (fileData.content) {
              results.push({
                docId: namespace,
                title: fileData.name || `Document ${namespace}`,
                content: fileData.content,
                page: 1
              });
            }
          }
        } else {
          // If we can't find the file metadata, try to get content directly from raw_content
          const directContentSnapshot = await adminDb.collection('users')
            .doc(this.firestoreStore.collectionPath.split('/')[1])
            .collection('raw_content')
            .doc(namespace)
            .get();

          if (directContentSnapshot.exists) {
            const contentData = directContentSnapshot.data();
            results.push({
              docId: namespace,
              title: `Document ${namespace}`,
              content: contentData?.content || contentData?.text || '',
              page: 1
            });
          }
        }
      } catch (error) {
        console.error(`Error fetching direct content for namespace ${namespace}:`, error);
      }
    }));

    return results;
  }

  /**
   * Creates a synthetic result from direct document content
   *
   * @param directContent - Array of document content objects
   * @param namespaces - Array of namespaces used for the query
   * @returns ContentSelectionResult with the direct content
   */
  private createSyntheticResult(
    directContent: Array<{
      docId: string;
      title: string;
      content: string;
      page?: number;
    }>,
    namespaces: string[]
  ): ContentSelectionResult {
    // Combine all content
    const combinedContent = directContent.map(doc => doc.content).join("\n\n");

    // Create sources from the direct content
    const sources: Source[] = directContent.map(doc => ({
      title: doc.title,
      page: doc.page || 1,
      doc_id: doc.docId,
      relevance: 0.8 // Assign a reasonable relevance score
    }));

    // Create namespace distribution
    const namespaceDistribution = namespaces.reduce((acc, namespace) => {
      const count = directContent.filter(doc => doc.docId === namespace).length;
      if (count > 0) {
        acc[namespace] = count;
      }
      return acc;
    }, {} as Record<string, number>);

    return {
      content: combinedContent,
      metadata: {
        sources,
        totalTokens: combinedContent.length / 4, // Rough estimate of tokens
        chunkCount: directContent.length,
        averageRelevance: 0.8,
        namespaceDistribution
      }
    };
  }
}