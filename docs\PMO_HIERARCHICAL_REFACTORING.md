# PMO Hierarchical Structure Refactoring

## Overview

This document describes the refactoring of the Firebase PMO collection from a flat array structure to a hierarchical project-task organization. The new structure provides better data organization, improved query performance, and clearer relationships between projects and tasks.

## Current vs Target Structure

### Current Structure (Legacy)
```
PMO Document {
  id: "c76670a7-bc7b-44ea-9905-189a4bcf36c8",
  projectIds: ["AMIp7WUTE26kDnzZC84T", "BNJq8XVUF37lEo0aD95U"],
  taskIds: ["klSkNTXipB3RGG0kTmHG", "gtNIONKWHizYNemHBQRs", "uLisPz7y1OYLUi2saoFP"]
}
```

### Target Structure (True Hierarchical Subcollections)
```
Firebase Structure:
users/{userId}/PMO/{pmoId}/projects/{projectId}/tasks/{taskId}

Example:
users/
  <EMAIL>/
    PMO/
      c76670a7-bc7b-44ea-9905-189a4bcf36c8/
        projects/
          AMIp7WUTE26kDnzZC84T/
            (project document with metadata)
            tasks/
              klSkNTXipB3RGG0kTmHG/
                (task document)
              gtNIONKWHizYNemHBQRs/
                (task document)
          BNJq8XVUF37lEo0aD95U/
            (project document with metadata)
            tasks/
              uLisPz7y1OYLUi2saoFP/
                (task document)

PMO Document (with legacy compatibility):
{
  id: "c76670a7-bc7b-44ea-9905-189a4bcf36c8",
  // Legacy arrays (maintained for backward compatibility)
  projectIds: ["AMIp7WUTE26kDnzZC84T", "BNJq8XVUF37lEo0aD95U"],
  taskIds: ["klSkNTXipB3RGG0kTmHG", "gtNIONKWHizYNemHBQRs", "uLisPz7y1OYLUi2saoFP"]
}

Project Document:
{
  id: "AMIp7WUTE26kDnzZC84T",
  name: "Marketing Campaign Project",
  createdAt: "2025-06-28T10:00:00Z",
  status: "Active"
}

Task Document (in project's tasks subcollection):
{
  id: "klSkNTXipB3RGG0kTmHG",
  projectId: "AMIp7WUTE26kDnzZC84T",
  title: "Create Marketing Strategy",
  description: "Develop comprehensive marketing strategy",
  // ... other task fields
}
```

## Benefits

1. **True Data Isolation**: Each project has its own tasks subcollection, providing complete data isolation
2. **Optimal Query Performance**: Direct access to project tasks without filtering or joins
3. **Scalable Architecture**: Firebase subcollections scale independently and efficiently
4. **Clear Data Hierarchy**: Natural parent-child relationship between projects and tasks
5. **Simplified Security Rules**: Can apply security rules at the project level
6. **Reduced Data Transfer**: Only fetch tasks for the specific project needed
7. **Backward Compatibility**: Legacy flat structure is maintained during transition

## Implementation Files

### Core Files
- `lib/agents/pmo/PMOInterfaces.ts` - Updated PMO interfaces with hierarchical structure
- `lib/firebase/pmoHierarchical.ts` - New utilities for hierarchical operations
- `lib/firebase/pmoMigration.ts` - Migration utilities to convert existing data
- `lib/agents/pmoProjectsTaskAgent.ts` - Updated to use hierarchical structure

### API and Scripts
- `app/api/pmo-hierarchical-migration/route.ts` - API endpoint for migration operations
- `scripts/pmo-hierarchical-migration.js` - Command-line migration script

## Usage

### 1. Migration

#### Using the API
```javascript
// Migrate all PMO records for a user
const response = await fetch('/api/pmo-hierarchical-migration', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'migrate-all',
    userId: '<EMAIL>',
    dryRun: true // Set to false for actual migration
  })
});

// Migrate a single PMO record
const response = await fetch('/api/pmo-hierarchical-migration', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'migrate-single',
    userId: '<EMAIL>',
    pmoId: 'c76670a7-bc7b-44ea-9905-189a4bcf36c8',
    dryRun: false
  })
});
```

#### Using the Script
```bash
# Dry run migration for all PMO records
node scripts/pmo-hierarchical-migration.<NAME_EMAIL> --dry-run

# Actual migration for all PMO records
node scripts/pmo-hierarchical-migration.<NAME_EMAIL>

# Migrate a single PMO record
node scripts/pmo-hierarchical-migration.js migrate-single <EMAIL> c76670a7-bc7b-44ea-9905-189a4bcf36c8
```

### 2. Validation

```bash
# Validate hierarchical structure
node scripts/pmo-hierarchical-migration.<NAME_EMAIL> c76670a7-bc7b-44ea-9905-189a4bcf36c8
```

### 3. Testing

```bash
# Test hierarchical data access
node scripts/pmo-hierarchical-migration.<NAME_EMAIL> c76670a7-bc7b-44ea-9905-189a4bcf36c8
```

### 4. Using Hierarchical Functions

```typescript
import {
  getHierarchicalPMOData,
  createProjectDocument,
  addTaskToProject,
  getProjectTasks
} from '../lib/firebase/pmoHierarchical';

// Get all hierarchical data for a PMO (reads from subcollections)
const result = await getHierarchicalPMOData(userId, pmoId);
if (result.success) {
  result.data.forEach(projectData => {
    console.log(`Project: ${projectData.projectId}`);
    console.log(`Tasks: ${projectData.taskIds.length}`);
  });
}

// Get tasks for a specific project (from subcollection)
const projectTasks = await getProjectTasks(userId, pmoId, projectId, true); // true = include task data
if (projectTasks.success) {
  console.log(`Task IDs: ${projectTasks.taskIds}`);
  console.log(`Task Data: ${projectTasks.tasks}`);
}

// Create a new project document
const projectInfo = {
  id: projectId,
  name: 'New Project',
  createdAt: new Date(),
  status: 'Active'
};
const createResult = await createProjectDocument(userId, pmoId, projectId, projectInfo);

// Add a task to a project's subcollection
const taskData = {
  projectId,
  title: 'New Task',
  description: 'Task description',
  category: 'Development',
  status: 'Not Started',
  startDate: new Date(),
  dueDate: new Date(),
  assignedTo: ['<EMAIL>'],
  priority: 'High',
  dependencies: [],
  notes: 'Task notes',
  createdBy: 'agent'
};
const addTaskResult = await addTaskToProject(userId, pmoId, projectId, taskId, taskData);
```

## Migration Strategy

### Phase 1: Preparation
1. ✅ Update PMO interfaces to support hierarchical structure
2. ✅ Create hierarchical utility functions
3. ✅ Create migration utilities
4. ✅ Update PMOProjectsTaskAgent to use hierarchical structure

### Phase 2: Migration
1. Run dry-run migrations to test the process
2. Validate existing data structure
3. Execute actual migrations for production data
4. Verify migration results

### Phase 3: Cleanup (Future)
1. Remove legacy array dependencies once all systems are updated
2. Optimize queries to use only hierarchical structure
3. Remove backward compatibility code

## Backward Compatibility

The refactoring maintains backward compatibility by:

1. **Preserving Legacy Arrays**: The original `projectIds` and `taskIds` arrays are maintained
2. **Fallback Logic**: The PMOProjectsTaskAgent falls back to legacy structure if hierarchical data is not available
3. **Dual Updates**: When new tasks are created, both hierarchical and legacy structures are updated

## Error Handling

The implementation includes comprehensive error handling:

1. **Graceful Degradation**: Falls back to legacy structure if hierarchical operations fail
2. **Detailed Logging**: Extensive logging for debugging and monitoring
3. **Validation**: Built-in validation to ensure data integrity
4. **Dry Run Support**: Test migrations without making actual changes

## Monitoring and Validation

Use the validation functions to ensure data integrity:

```typescript
import { validateHierarchicalStructure } from '../lib/firebase/pmoMigration';

const validation = await validateHierarchicalStructure(userId, pmoId);
if (!validation.isValid) {
  console.log('Issues found:', validation.issues);
  console.log('Orphaned tasks:', validation.details.orphanedTasks);
}
```

## Next Steps

1. **Test Migration**: Run dry-run migrations on development data
2. **Validate Results**: Use validation functions to ensure data integrity
3. **Execute Migration**: Run actual migrations on production data
4. **Monitor Performance**: Track query performance improvements
5. **Update Dependencies**: Update other systems to use hierarchical structure
6. **Remove Legacy Code**: Eventually remove backward compatibility code

## Support

For issues or questions about the hierarchical refactoring:

1. Check the validation output for data integrity issues
2. Review the migration logs for detailed error information
3. Use the test functions to verify hierarchical data access
4. Consult the API documentation for integration guidance
