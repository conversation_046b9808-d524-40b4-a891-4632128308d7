"use client";

import { getStorage, ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import { useState, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import { doc, setDoc } from "firebase/firestore";
import { db } from "../components/firebase";

export enum StatusText {
  UPLOADING = "Uploading file...",
  UPLOADED = "File uploaded successfully...",
  PROCESSING = "Processing file...",
  COMPLETED = "Upload and processing complete.",
  ERROR = "An error occurred.",
  GENERATING = "GENERATING",
  SAVING = "SAVING",
  IDLE = "IDLE"
}

export type Status = StatusText;

// Complete list of supported file types
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg', 
  'image/png', 
  'image/gif', 
  'image/webp'
];

const SUPPORTED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',                                                  // .doc
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'text/plain',                                                         // .txt
  'application/rtf',                                                    // .rtf
  'application/vnd.ms-excel',                                          // .xls
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'text/csv'                                                           // .csv
];

const MAX_IMAGE_SIZE = 4 * 1024 * 1024;  // 4MB
const MAX_DOC_SIZE = 50 * 1024 * 1024;   // 50MB

interface UploadHookResult {
  progress: number | null;
  status: Status | null;
  handleUpload: (file: File, category: string | null) => Promise<void>;
  error: string | null;
}

function useUpload(): UploadHookResult {
  const [progress, setProgress] = useState<number | null>(null);
  const [status, setStatus] = useState<Status | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { data: session } = useSession();
  const userId = session?.user?.email ?? null;
  const router = useRouter();

  const isImageFile = (file: File): boolean => {
    return SUPPORTED_IMAGE_TYPES.includes(file.type);
  };

  const isDocumentFile = (file: File): boolean => {
    return SUPPORTED_DOCUMENT_TYPES.includes(file.type);
  };

  const validateFileSize = (file: File): boolean => {
    if (isImageFile(file)) {
      return file.size <= MAX_IMAGE_SIZE;
    }
    return file.size <= MAX_DOC_SIZE;
  };

  const checkImageDimensions = (file: File): Promise<boolean> => {
    return new Promise((resolve) => {
      if (!isImageFile(file)) {
        resolve(true);
        return;
      }

      const img = new Image();
      const objectUrl = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(objectUrl);
        const pixels = img.width * img.height;
        const MAX_IMAGE_PIXELS = 33177600; // Maximum pixels allowed by Groq
        if (pixels > MAX_IMAGE_PIXELS) {
          setError(`Image dimensions too large. Maximum allowed pixels is ${MAX_IMAGE_PIXELS.toLocaleString()}. Your image has ${pixels.toLocaleString()} pixels (${img.width}x${img.height}).`);
          resolve(false);
        } else {
          resolve(true);
        }
      };

      img.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        setError("Failed to read image dimensions");
        resolve(false);
      };

      img.src = objectUrl;
    });
  };

  const handleUpload = useCallback(async (file: File, category: string | null) => {
    setError(null);
    
    if (!file || !userId) {
      setError("No file selected or user not authenticated.");
      return;
    }

    if (!isImageFile(file) && !isDocumentFile(file)) {
      setError(`Unsupported file type: ${file.type}\n\nSupported types are:\nImages: ${SUPPORTED_IMAGE_TYPES.join(', ')}\nDocuments: ${SUPPORTED_DOCUMENT_TYPES.join(', ')}`);
      setStatus(StatusText.ERROR);
      return;
    }

    if (!validateFileSize(file)) {
      const maxSize = isImageFile(file) ? "4MB" : "50MB";
      setError(`File size exceeds the ${maxSize} limit`);
      setStatus(StatusText.ERROR);
      return;
    }

    const validDimensions = await checkImageDimensions(file);
    if (!validDimensions) {
      setStatus(StatusText.ERROR);
      return;
    }

    setProgress(0);
    setStatus(StatusText.UPLOADING);

    try {
      const resolvedCategory = category || "Unknown";
      const storage = getStorage();
      const newDocumentId = uuidv4();
      const storageRef = ref(storage, `uploads/${userId}/${newDocumentId}`);
      const uploadTask = uploadBytesResumable(storageRef, file);

      uploadTask.on(
        "state_changed",
        (snapshot) => {
          const percent = Math.round((snapshot.bytesTransferred / snapshot.totalBytes) * 100);
          setProgress(percent);
          setStatus(StatusText.UPLOADING);
          console.log(`Upload progress: ${percent}%`);
        },
        (error) => {
          console.error("Error uploading file:", error);
          setError(`Error uploading file: ${error.message}`);
          setStatus(StatusText.ERROR);
          setProgress(null);
        },
        async () => {
          setStatus(StatusText.UPLOADED);
          const downloadUrl = await getDownloadURL(uploadTask.snapshot.ref);

          // Store file metadata in Firestore
          await setDoc(doc(db, "users", userId, "files", newDocumentId), {
            name: file.name,
            size: file.size,
            type: file.type,
            category: resolvedCategory,
            namespace: newDocumentId,
            downloadUrl: downloadUrl,
            ref: uploadTask.snapshot.ref.fullPath,
            createdAt: new Date(),
            isImage: isImageFile(file)
          });

          setStatus(StatusText.PROCESSING);

          // Process the file
          const response = await fetch("/api/processFile", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              docId: newDocumentId,
              userId,
              category: resolvedCategory,
              fileName: file.name,
              fileType: file.type,
              fileUrl: downloadUrl,
              isImage: isImageFile(file)
            }),
          });

          if (!response.ok) {
            let errorMessage = "Failed to process the uploaded file.";
            try {
              const errorData = await response.json();
              errorMessage = errorData.error || errorMessage;
            } catch (e) {
              console.error("Error parsing error response:", e);
            }
            throw new Error(errorMessage);
          }

          setStatus(StatusText.COMPLETED);
          router.push("/fileManager");
        }
      );
    } catch (error: any) {
      console.error("Error during file upload or processing:", error);
      setError(error.message || "Error during file upload or processing");
      setStatus(StatusText.ERROR);
      setProgress(null);
    }
  }, [userId, router]);

  return { progress, status, handleUpload, error };
}

export default useUpload;