/**
 * enhancedMemoryManager.ts
 *
 * Enhanced utilities for managing memory in Node.js applications,
 * particularly for large document processing tasks.
 */

/**
 * Memory usage statistics interface
 */
export interface MemoryUsageStats {
  heapUsed: number;
  heapTotal: number;
  rss: number;
  external: number;
  arrayBuffers: number;
  percentHeapUsed: number;
  raw: NodeJS.MemoryUsage;
}

/**
 * Get detailed memory usage statistics
 * @param label - Optional label for the log message
 * @returns Memory usage statistics
 */
export function getDetailedMemoryUsage(label: string = ''): MemoryUsageStats {
  const memoryUsage = process.memoryUsage();
  const heapUsed = Math.round(memoryUsage.heapUsed / (1024 * 1024));
  const heapTotal = Math.round(memoryUsage.heapTotal / (1024 * 1024));
  const rss = Math.round(memoryUsage.rss / (1024 * 1024));
  const external = Math.round(memoryUsage.external / (1024 * 1024));
  const arrayBuffers = Math.round(memoryUsage.arrayBuffers / (1024 * 1024));

  const percentHeapUsed = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);

  if (label) {
    console.log(`[${label}] Memory Usage:`);
    console.log(`  Heap Used: ${heapUsed} MB (${percentHeapUsed}% of total heap)`);
    console.log(`  Heap Total: ${heapTotal} MB`);
    console.log(`  RSS: ${rss} MB`);
    console.log(`  External: ${external} MB`);
    console.log(`  Array Buffers: ${arrayBuffers} MB`);
  }

  return {
    heapUsed,
    heapTotal,
    rss,
    external,
    arrayBuffers,
    percentHeapUsed,
    raw: memoryUsage
  };
}

/**
 * Aggressively tries to free memory
 * @returns True if garbage collection was triggered
 */
export function forceMemoryCleanup(): boolean {
  // First, clear any module caches that might be holding references
  Object.keys(require.cache).forEach(key => {
    if (!key.includes('node_modules')) {
      delete require.cache[key];
    }
  });

  // Clear any interval timers
  const timerHandle = setTimeout(() => {}, 0);
  // Get the numeric value of the timeout handle
  const timerValue = Number(timerHandle);
  for (let i = 1; i < timerValue; i++) {
    clearTimeout(i);
  }

  // Try to run garbage collection multiple times
  let gcSuccess = false;
  try {
    if (typeof global.gc === 'function') {
      // Run GC multiple times to ensure thorough cleanup
      global.gc();

      // Small delay to allow GC to complete
      setTimeout(() => {
        if (typeof global.gc === 'function') {
          global.gc();
        }
      }, 100);

      gcSuccess = true;
    }
  } catch (error) {
    console.warn('Failed to trigger garbage collection:', error);
    console.warn('Make sure Node.js is started with --expose-gc flag');
  }

  return gcSuccess;
}

/**
 * Process large text in smaller chunks to avoid memory issues
 * @param text - The large text to process
 * @param processFn - Function to process each chunk
 * @param chunkSize - Size of each chunk in characters
 * @returns Array of results from processing each chunk
 */
export async function processTextInChunks<T>(
  text: string,
  processFn: (chunk: string, index: number) => Promise<T>,
  chunkSize: number = 5000
): Promise<(T | null)[]> {
  const results: (T | null)[] = [];
  const totalChunks = Math.ceil(text.length / chunkSize);

  console.log(`Processing text of length ${text.length} in ${totalChunks} chunks`);

  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, text.length);
    const chunk = text.substring(start, end);

    console.log(`Processing chunk ${i+1}/${totalChunks} (${chunk.length} chars)`);

    try {
      // Process the current chunk
      const result = await processFn(chunk, i);
      results.push(result);

      // Clean up after each chunk
      if (i < totalChunks - 1) {
        getDetailedMemoryUsage(`After chunk ${i+1}/${totalChunks}`);
        forceMemoryCleanup();

        // Small delay to allow GC to complete
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } catch (error) {
      console.error(`Error processing chunk ${i+1}/${totalChunks}:`, error);
      results.push(null); // Push null for failed chunks
    }
  }

  return results;
}

/**
 * Stream process a large file instead of loading it all into memory
 * @param filePath - Path to the file
 * @param processFn - Function to process each chunk
 * @param chunkSize - Size of each chunk in bytes
 * @returns Array of results from processing each chunk
 */
export async function streamProcessFile<T>(
  filePath: string,
  processFn: (chunk: string, index: number) => Promise<T>,
  chunkSize: number = 65536
): Promise<(T | null)[]> {
  const fs = require('fs');
  const { createReadStream } = fs;

  return new Promise((resolve, reject) => {
    const results: (T | null)[] = [];
    let chunkIndex = 0;
    let buffer = '';

    const readStream = createReadStream(filePath, {
      highWaterMark: chunkSize,
      encoding: 'utf8'
    });

    readStream.on('data', async (chunk: string) => {
      // Pause the stream to process the current chunk
      readStream.pause();

      buffer += chunk;

      // Process complete chunks
      if (buffer.length >= chunkSize) {
        const processChunk = buffer.substring(0, chunkSize);
        buffer = buffer.substring(chunkSize);

        try {
          console.log(`Processing file chunk ${++chunkIndex}`);
          const result = await processFn(processChunk, chunkIndex);
          results.push(result);

          // Clean up after processing
          getDetailedMemoryUsage(`After file chunk ${chunkIndex}`);
          forceMemoryCleanup();

          // Small delay to allow GC to complete
          await new Promise(r => setTimeout(r, 200));
        } catch (error) {
          console.error(`Error processing file chunk ${chunkIndex}:`, error);
          results.push(null);
        }
      }

      // Resume the stream
      readStream.resume();
    });

    readStream.on('end', async () => {
      // Process any remaining data
      if (buffer.length > 0) {
        try {
          console.log(`Processing final file chunk ${++chunkIndex}`);
          const result = await processFn(buffer, chunkIndex);
          results.push(result);
        } catch (error) {
          console.error(`Error processing final file chunk:`, error);
          results.push(null);
        }
      }

      resolve(results);
    });

    readStream.on('error', (error: Error) => {
      reject(error);
    });
  });
}

// Add a declaration for the global gc function
declare global {
  namespace NodeJS {
    interface Global {
      gc?: () => void;
    }
  }
}