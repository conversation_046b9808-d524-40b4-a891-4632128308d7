import { NextResponse } from "next/server";
import { seoAnalyzer } from "lib/seo-analyzer";

/**
 * API route for SEO analysis
 */
export async function POST(request) {
  try {
    const { url } = await request.json();
    
    if (!url) {
      return NextResponse.json({ 
        error: "URL is required" 
      }, { status: 400 });
    }
    
    // Perform SEO analysis on the server side
    const analysis = await seoAnalyzer.analyzeUrl(url);
    
    // Return the analysis results
    return NextResponse.json({
      success: true,
      url,
      analysis,
      reportDate: new Date(),
    });
  } catch (error) {
    console.error("Error in SEO analysis:", error);
    return NextResponse.json({ 
      success: false,
      error: error.message || "An error occurred during SEO analysis"
    }, { status: 500 });
  }
}
