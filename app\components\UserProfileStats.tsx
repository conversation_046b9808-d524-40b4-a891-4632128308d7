'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { collection, getDocs, query, where, doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../lib/firebase/config';
import { RefreshCw, FolderIcon, FileIcon, MessageSquare, User } from 'lucide-react';
import Image from 'next/image';

interface UserStats {
  folders: number;
  files: number;
  chats: number;
}

export default function UserProfileStats() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<UserStats>({ folders: 0, files: 0, chats: 0 });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [profileSynced, setProfileSynced] = useState(false);
  const [userAvatar, setUserAvatar] = useState<string | null>(null);

  // Fetch user stats
  useEffect(() => {
    const fetchStats = async () => {
      if (!session?.user?.email) return;

      setLoading(true);
      setError(null);

      try {
        const userEmail = session.user.email;

        // Fetch files and count categories
        const filesCollection = collection(db, 'users', userEmail, 'files');
        const filesSnapshot = await getDocs(filesCollection);

        // Count files
        const filesCount = filesSnapshot.size;

        // Count folders (categories)
        const categories = new Set();
        filesSnapshot.forEach(doc => {
          const fileData = doc.data();
          const category = fileData.category;
          if (category && category !== 'Unknown') {
            categories.add(category);
          }
        });
        const foldersCount = categories.size;

        // Count chats
        const chatsCollection = collection(db, 'users', userEmail, 'chats');
        const chatsSnapshot = await getDocs(chatsCollection);
        const chatsCount = chatsSnapshot.size;

        setStats({
          folders: foldersCount,
          files: filesCount,
          chats: chatsCount
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
        setError('Failed to load user statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [session]);

  // Sync user profile image
  useEffect(() => {
    const syncProfileImage = async () => {
      if (!session?.user?.email || !session?.user?.image) return;

      try {
        const userEmail = session.user.email;
        const userImage = session.user.image;
        const userName = session.user.name;
        const userDocRef = doc(db, 'users', userEmail);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();
          setUserAvatar(userData.avatar || null);

          // Check if profile image needs updating
          if (userImage && userData.avatar !== userImage) {
            console.log('Updating user avatar with Google profile image');

            await updateDoc(userDocRef, {
              avatar: userImage,
              updatedAt: serverTimestamp()
            });

            setUserAvatar(userImage);
            setProfileSynced(true);
          } else if (userData.avatar === userImage) {
            setProfileSynced(true);
          }
        } else {
          // Create new user document
          console.log('Creating new user document');
          // Use setDoc instead of updateDoc for new documents
          const { setDoc } = await import('firebase/firestore');
          await setDoc(userDocRef, {
            name: userName || userEmail.split('@')[0],
            email: userEmail,
            avatar: userImage,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          });

          setUserAvatar(userImage);
          setProfileSynced(true);
        }
      } catch (error) {
        console.error('Error syncing profile image:', error);
      }
    };

    syncProfileImage();
  }, [session]);

  if (!session) {
    return (
      <div className="p-4 bg-gray-800 rounded-lg shadow-md">
        <div className="flex items-center justify-center">
          <User className="w-6 h-6 text-gray-400 mr-2" />
          <p className="text-gray-400">Not signed in</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-800 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-white">User Profile</h2>
        <button
          onClick={() => window.location.reload()}
          disabled={loading}
          className="p-1 rounded-full hover:bg-gray-700 transition-colors"
          title="Refresh stats"
        >
          <RefreshCw className={`w-5 h-5 text-gray-400 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* User info */}
      <div className="flex items-center mb-6">
        <div className="relative mr-4">
          <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
            {userAvatar || session?.user?.image ? (
              <img
                src={userAvatar || session?.user?.image || ''}
                alt={session?.user?.name || 'User'}
                className="w-full h-full object-cover"
                referrerPolicy="no-referrer"
              />
            ) : (
              <span className="text-2xl font-medium text-gray-300">
                {(session?.user?.name || 'User').charAt(0).toUpperCase()}
              </span>
            )}
          </div>

          {profileSynced && (
            <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1" title="Profile image synced">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          )}
        </div>

        <div>
          <h3 className="text-xl font-medium text-white">{session?.user?.name || 'User'}</h3>
          <p className="text-gray-400">{session?.user?.email || ''}</p>
        </div>
      </div>

      {/* Stats */}
      {error ? (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-4">
          {error}
        </div>
      ) : loading ? (
        <div className="flex justify-center py-4">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : (
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-gray-700 p-3 rounded-lg text-center">
            <FolderIcon className="w-6 h-6 mx-auto mb-2 text-purple-400" />
            <div className="text-2xl font-bold text-white">{stats.folders}</div>
            <div className="text-xs text-gray-400">Folders</div>
          </div>

          <div className="bg-gray-700 p-3 rounded-lg text-center">
            <FileIcon className="w-6 h-6 mx-auto mb-2 text-blue-400" />
            <div className="text-2xl font-bold text-white">{stats.files}</div>
            <div className="text-xs text-gray-400">Files</div>
          </div>

          <div className="bg-gray-700 p-3 rounded-lg text-center">
            <MessageSquare className="w-6 h-6 mx-auto mb-2 text-green-400" />
            <div className="text-2xl font-bold text-white">{stats.chats}</div>
            <div className="text-xs text-gray-400">Chats</div>
          </div>
        </div>
      )}
    </div>
  );
}
