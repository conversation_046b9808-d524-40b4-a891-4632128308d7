import type { Groq } from "groq-sdk";
import createGroqClient from "./scriptreaderAI/llms/groq";
import {
  ChatCompletionTool,
  ChatCompletionToolChoiceOption,
  type ChatCompletionCreateParamsBase,
} from "groq-sdk/resources/chat/completions";

// Extend the base type to include reasoning_format
interface ExtendedChatCompletionCreateParams extends ChatCompletionCreateParamsBase {
  reasoning_format?: "raw" | "parsed" | "hidden";
}

const DEFAULT_GROQ_MODEL = process.env.GROQ_MODEL2 || "meta-llama/llama-4-maverick-17b-128e-instruct";
const STREAMING_GROQ_MODEL = "meta-llama/llama-4-maverick-17b-128e-instruct";
const MAX_TOKENS = 3500;
const DEFAULT_TEMP = 0.7;

interface ChatCompletionMessage {
  role: "system" | "user" | "assistant" | "function";
  content: string;
  name?: string;
}

interface ChatCompletionRequest {
  messages: ChatCompletionMessage[];
  temperature?: number;
  modelName?: string;
  maxTokens?: number;
  reasoning_format?: "raw" | "parsed" | "hidden";
  tools?: ChatCompletionTool[] | null;
  toolChoice?: ChatCompletionToolChoiceOption | null;
}

interface ChatCompletionResponse {
  choices: Array<{
    message: {
      content: string | null;
      tool_calls?: Array<{
        id: string;
        type: string;
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
  }>;
}

interface StreamingChatCompletionRequest extends ChatCompletionRequest {
  stream?: boolean;
  onChunk?: (content: string) => Promise<void> | void;
}

// Store instances by user email
const groqInstances: Map<string, Groq> = new Map();

export function getGroqClient(config: { userEmail: string }): Groq {
  const { userEmail } = config;
  if (!groqInstances.has(userEmail)) {
    const client = createGroqClient({ userEmail });
    groqInstances.set(userEmail, client);
  }
  return groqInstances.get(userEmail)!;
}

export async function callGroqAi({
  messages,
  temperature = DEFAULT_TEMP,
  modelName = DEFAULT_GROQ_MODEL,
  maxTokens = MAX_TOKENS,
  tools,
  toolChoice,
}: ChatCompletionRequest): Promise<ChatCompletionResponse> {
  if (!messages?.length) {
    throw new Error("Messages array is required and must not be empty");
  }

  const userEmail = messages.find((msg) => msg.name)?.name;
  if (!userEmail) {
    throw new Error("User email not found in messages");
  }

  const groqInstance = getGroqClient({ userEmail });

  const request: ExtendedChatCompletionCreateParams = {
    messages: messages.map((message) => ({
      role: message.role,
      content: message.content,
      name: message.name ?? "defaultName",
    })),
    model: modelName,
    temperature,
    max_tokens: maxTokens,
    tools,
    tool_choice: toolChoice,
  };

  try {
    const response = await groqInstance.chat.completions.create(request);
    if (!('choices' in response) || !response.choices?.[0]?.message) {
      throw new Error("Invalid response from Groq API");
    }
    return response;
  } catch (error) {
    console.error("Groq API error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    throw new Error(`Failed to get Groq completion: ${errorMessage}`);
  }
}

export async function callGroqAiEx({
  messages,
  stream = false,
  onChunk,
  temperature = DEFAULT_TEMP,
  modelName = STREAMING_GROQ_MODEL,
  maxTokens = MAX_TOKENS,
}: StreamingChatCompletionRequest): Promise<ChatCompletionResponse> {
  if (!messages?.length) {
    throw new Error("Messages array is required and must not be empty");
  }

  const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || '********************************************************';
  if (!apiKey) {
    throw new Error("Groq API key is missing");
  }

  if (stream && !onChunk) {
    throw new Error("onChunk callback is required when stream is true");
  }

  const formattedMessages = messages.map((message) => ({
    role: message.role,
    content: message.content,
    name: message.name,
  }));

  const url = "https://api.groq.com/openai/v1/chat/completions";
  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${apiKey}`,
    },
    body: JSON.stringify({
      model: modelName,
      messages: formattedMessages,
      stream,
      temperature,
      max_tokens: maxTokens,
    }),
  });

  if (!response.ok) {
    const errorText = await response.json()
      .then((json) => json.error?.message || `HTTP error ${response.status}`)
      .catch(() => `HTTP error ${response.status}`);
    throw new Error(`Groq API error: ${errorText}`);
  }

  if (stream) {
    if (!response.body) {
      throw new Error("Response body is null or undefined");
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";
    let fullContent = "";

    const processChunk = async ({ done, value }: ReadableStreamReadResult<Uint8Array>): Promise<void> => {
      if (done) {
        if (buffer.trim() && onChunk) {
          try {
            const jsonStr = buffer.replace(/^data: /, "").trim();
            if (jsonStr && jsonStr !== "[DONE]") {
              const json = JSON.parse(jsonStr);
              const content = json.choices[0]?.delta?.content || "";
              if (content) {
                fullContent += content;
                await onChunk(content);
              }
            }
          } catch (error) {
            console.error("Error parsing final chunk:", error);
          }
        }
        return;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (!line.trim() || line === "data: [DONE]") continue;
        if (!line.startsWith("data: ")) continue;

        try {
          const jsonStr = line.replace(/^data: /, "").trim();
          if (!jsonStr) continue;

          const json = JSON.parse(jsonStr);
          const content = json.choices[0]?.delta?.content || "";
          if (content && onChunk) {
            fullContent += content;
            await onChunk(content);
          }
        } catch (error) {
          console.error("Error parsing chunk JSON:", error);
        }
      }

      return reader.read().then(processChunk);
    };

    await reader.read().then(processChunk);
    return { choices: [{ message: { content: fullContent } }] };
  }

  return response.json();
}