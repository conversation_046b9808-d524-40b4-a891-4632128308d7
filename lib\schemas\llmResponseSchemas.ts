/**
 * llmResponseSchemas.ts
 * 
 * This file contains Zod schemas for parsing and validating JSON responses from LLMs.
 * These schemas provide type safety and better error handling for JSON parsing.
 */

import { z } from 'zod';

/**
 * Schema for query context analysis responses
 * Used by QueryDocumentsAgent and QuestionAnswerAgent
 */
export const QueryContextAnalysisSchema = z.object({
  hasEnoughContext: z.boolean().default(true),
  missingContext: z.array(z.string()).default([]),
  enhancedQuery: z.string().optional(),
  recommendation: z.string().default("I'll search for documents based on your query.")
});

export type QueryContextAnalysis = z.infer<typeof QueryContextAnalysisSchema>;

/**
 * Schema for request for information responses
 * Used by RequestForInformationTool
 */
export const RequestForInformationSchema = z.object({
  requestMessage: z.string().default("I need more information to provide a comprehensive response."),
  specificQuestions: z.array(z.string()).default([
    "Could you provide more details about what you're looking for?",
    "What specific aspects of this topic are you most interested in?",
    "Do you have any existing documents or resources you can share?"
  ]),
  recommendedActions: z.array(z.string()).default([
    "Share any existing materials or documents",
    "Provide information about your specific requirements",
    "Specify your target audience or intended use case"
  ])
});

export type RequestForInformation = z.infer<typeof RequestForInformationSchema>;

/**
 * Schema for logical questions generated by QuestionAnswerTool
 */
export const LogicalQuestionSchema = z.object({
  question: z.string(),
  reasoning: z.string(),
  purpose: z.string(),
  expectedImpact: z.string(),
  priority: z.number().int().min(1).max(10),
  type: z.string(),
  relatedTo: z.array(z.string())
});

export type LogicalQuestion = z.infer<typeof LogicalQuestionSchema>;

export const LogicalQuestionsArraySchema = z.array(LogicalQuestionSchema);

/**
 * Schema for synthesized final answer from QuestionAnswerTool
 */
export const SynthesizedAnswerSchema = z.object({
  content: z.string(),
  reasoning: z.string(),
  confidence: z.number().min(0).max(1).default(0.5),
  followUpQuestions: z.array(z.string()).optional()
});

export type SynthesizedAnswer = z.infer<typeof SynthesizedAnswerSchema>;

/**
 * Schema for context analysis results from QuestionAnswerAgent
 */
export const ContextAnalysisResultSchema = z.object({
  explicitRequirements: z.array(z.string()),
  implicitRequirements: z.array(z.string()),
  inferredObjectives: z.array(z.string()),
  constraints: z.array(z.string()),
  stakeholders: z.array(z.string()),
  domainContext: z.string(),
  knowledgeGaps: z.array(z.string()),
  assumptionsMade: z.array(z.string()),
  confidenceLevel: z.number().min(0).max(1)
});

export type ContextAnalysisResult = z.infer<typeof ContextAnalysisResultSchema>;

/**
 * Schema for objective inference results from QuestionAnswerAgent
 */
export const ObjectiveInferenceResultSchema = z.object({
  primaryObjective: z.string(),
  secondaryObjectives: z.array(z.string()),
  businessValue: z.string(),
  successCriteria: z.array(z.string()),
  risks: z.array(z.string()),
  confidenceInInference: z.number().min(0).max(1)
});

export type ObjectiveInferenceResult = z.infer<typeof ObjectiveInferenceResultSchema>;

/**
 * Helper function to safely parse JSON from LLM responses
 * Handles markdown code blocks and other formatting issues
 */
export function safeParseJson<T>(
  schema: z.ZodType<T>,
  jsonString: string,
  defaultValue?: T
): T {
  try {
    // Try to extract JSON from markdown code blocks if present
    let cleanedJsonString = jsonString;
    
    // Check if the response is wrapped in a markdown code block
    const jsonMatch = jsonString.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonMatch && jsonMatch[1]) {
      cleanedJsonString = jsonMatch[1];
    }
    
    // Parse the JSON string
    const parsedJson = JSON.parse(cleanedJsonString);
    
    // Validate with Zod schema
    const result = schema.safeParse(parsedJson);
    
    if (result.success) {
      return result.data;
    } else {
      console.error("Zod validation error:", result.error);
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      // If no default value provided, return a default-initialized version using the schema
      return schema.parse({});
    }
  } catch (error) {
    console.error("Error parsing JSON:", error);
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    // If no default value provided, return a default-initialized version using the schema
    return schema.parse({});
  }
}
