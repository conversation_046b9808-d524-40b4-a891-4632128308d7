// src/components/tools/generateImageTool.ts

import OpenAI from 'openai';

interface GenerateImageResult {
  success: boolean;
  base64Image?: string;
  error?: string;
}

export class GenerateImageTool {
  private openai: OpenAI;

  constructor(openaiApiKey: string) {
    this.openai = new OpenAI({
      apiKey: openaiApiKey,
    });
  }

  /**
   * Refines a user’s initial prompt via a GPT model.
   */
  public async refinePrompt(initialPrompt: string): Promise<string> {
    try {
      const chatCompletion = await this.openai.chat.completions.create({
        model: 'gpt-4o', // Using GPT-4o for prompt refinement
        messages: [
          {
            role: 'system',
            content: `You are an expert in generating highly detailed, photorealistic image prompts. When a user provides a prompt, your task is to transform it into a single, exceptionally descriptive and precise prompt. Focus on the following elements to enhance the image’s realism and artistic quality:

Lighting and Atmosphere

Specify the type of lighting (e.g., soft natural light, dramatic backlight, golden-hour glow).
Include atmospheric details (e.g., mist, haze, crisp air).
Camera Angle and Perspective

Indicate viewpoint (e.g., low-angle shot, eye-level, aerial).
Determine focal length and depth of field (e.g., wide-angle lens, shallow depth of field).
Color Palette and Mood

Choose complementary or contrasting colors to establish a specific mood (e.g., warm and inviting, cool and mysterious).
Include descriptive adjectives for the palette (e.g., muted pastels, vibrant neons).
Textures and Materials

Describe the surfaces and materials in detail (e.g., weathered wood, glossy metal, soft fabric).
Highlight subtle elements like reflections or patterns.
Environmental Details

Enrich the scene with contextual elements (e.g., surrounding architecture, natural landscape, distant objects).
Ensure they support the main subject without overwhelming it.
Composition and Framing

Apply compositional guidelines (e.g., rule of thirds, centered framing, leading lines).
Clarify the placement of the subject and supporting elements.
How to Apply These Steps

Read the User’s Core Concept – Identify the subject or central idea.
Expand with Relevant Details – Incorporate each of the categories above to heighten realism.
Maintain Clarity – Combine all details into a single prompt, ensuring it flows naturally and concisely.
Avoid Extraneous Commentary – Present the final refined prompt without additional explanations or meta-notes.
Example of a Refined Prompt (Using These Steps)
“Capture a crisp, eye-level shot of a solitary figure standing on a secluded
shoreline at dawn, illuminated by soft, warm light filtering through a gentle
morning haze. Incorporate a shallow depth of field to keep the subject in sharp focus
while softly blurring the rolling waves and distant horizon. Emphasize natural textures—subtle ripples
in wet sand, the subdued sheen of the water, and the slight wrinkles in the figure’s light-colored
fabric. Use a serene color palette of soft blues and pale golds to evoke a peaceful, reflective mood.
Place the main subject slightly off-center, guided by the rule of thirds, to balance the wide,
open sky and gentle surf. Highlight realistic details such as delicate reflections on the water’s
surface and faint footprints trailing behind the figure, creating a professional,
photorealistic scene.”`
          },
          { role: 'user', content: initialPrompt }
        ],
        max_tokens: 2000,
        temperature: 0.7
      });

      // If no message, return original prompt to avoid throwing
      return chatCompletion.choices[0]?.message?.content || initialPrompt;
    } catch (error) {
      console.error('Prompt refinement error:', error);

      // Log detailed error information
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }

      // Fallback: just return original prompt
      return initialPrompt;
    }
  }

  /**
   * Generates a base64-encoded image from a prompt and returns it.
   * @param prompt The text prompt to generate an image from
   * @param model The model to use for generation (dall-e-3, gpt-image-1, etc.)
   * @param size The size of the generated image
   * @param quality The quality level (low, medium, high, auto)
   * @param format The output format (png, jpeg, webp)
   * @param background The background type (transparent, opaque, auto)
   * @param compression The output compression level (0-100) for JPEG and WebP formats
   */
  public async generateImageBase64(
    prompt: string,
    model = 'dall-e-3',
    size: string = '1024x1024',
    quality: string = 'auto',
    format: string = 'png',
    background: string = 'auto',
    compression?: number
  ): Promise<GenerateImageResult> {
    try {
      console.log(`[generateImageTool] Input model parameter: ${model}`);
      console.log(`[generateImageTool] Generating image with model: ${model}`);
      console.log(`[generateImageTool] Using size: ${size}`);
      console.log(`[generateImageTool] Using quality: ${quality}`);
      console.log(`[generateImageTool] Using format: ${format}`);
      console.log(`[generateImageTool] Using background: ${background}`);
      if (compression !== undefined) {
        console.log(`[generateImageTool] Using compression: ${compression}`);
      }

      // Configure parameters based on the selected model
      if (model === 'dall-e-3') {
        // DALL-E 3 specific parameters
        const dalleParams = {
          prompt,
          model: 'dall-e-3',
          n: 1,
          size: size as '1024x1024' | '1792x1024' | '1024x1792' | '512x512' | '256x256', // Type assertion for size
          response_format: 'b64_json' as 'b64_json', // Type assertion to match OpenAI's expected types
          style: 'vivid' as 'vivid',
          quality: 'hd' as 'hd'
        };

        const response = await this.openai.images.generate(dalleParams);
        const b64Data = response.data?.[0]?.b64_json;

        if (b64Data) {
          console.log('Successfully generated image with dall-e-3 model');
          return { success: true, base64Image: b64Data };
        } else {
          throw new Error('No image data returned from dall-e-3');
        }
      }
      else if (model === 'gpt-image-1') {
        // GPT-Image-1 specific parameters

        // Build the parameters object
        const gptImageParams: any = {
          prompt,
          model: 'gpt-image-1',
          n: 1,
          size: size as '1024x1024' | '1536x1024' | '1024x1536' | '1792x1024' | '1024x1792' | 'auto', // Type assertion for size
          quality: quality as 'low' | 'medium' | 'high' | 'auto', // Type assertion for quality
          background: background as 'transparent' | 'opaque' | 'auto', // Type assertion for background
          output_format: format as 'png' | 'jpeg' | 'webp' // Type assertion for format
        };

        // Add output_compression parameter only if it's defined and format is jpeg or webp
        if (compression !== undefined && (format === 'jpeg' || format === 'webp')) {
          gptImageParams.output_compression = compression;
        }

        const response = await this.openai.images.generate(gptImageParams);
        const b64Data = response.data?.[0]?.b64_json;

        if (b64Data) {
          console.log('Successfully generated image with gpt-image-1 model');
          return { success: true, base64Image: b64Data };
        } else {
          throw new Error('No image data returned from gpt-image-1');
        }
      }
      else if (model === 'dall-e-2') {
        // DALL-E 2 specific parameters
        const dalle2Params = {
          prompt,
          model: 'dall-e-2',
          n: 1,
          size: size as '1024x1024' | '512x512' | '256x256', // Type assertion for size
          response_format: 'b64_json' as 'b64_json' // Type assertion to match OpenAI's expected types
        };

        const response = await this.openai.images.generate(dalle2Params);
        const b64Data = response.data?.[0]?.b64_json;

        if (b64Data) {
          console.log('Successfully generated image with dall-e-2 model');
          return { success: true, base64Image: b64Data };
        } else {
          throw new Error('No image data returned from dall-e-2');
        }
      }
      else if (model.includes('imagen')) {
        // For Imagen models, we should not use the OpenAI client
        // Instead, we should return an error indicating that Imagen models should be handled separately
        console.log('Imagen models should be handled by the Imagen-specific implementation');
        throw new Error('Imagen models cannot be used with the OpenAI client. Use the Imagen-specific implementation instead.');
      }
      else {
        // For any other model, use generic parameters and fall back if needed
        try {
          // Use type assertion to handle the different parameter requirements
          // Only include parameters that are supported by all models
          const genericParams: any = {
            prompt,
            model: model,
            n: 1,
            size: size as '1024x1024' | '1792x1024' | '1024x1792' | '512x512' | '256x256' // Type assertion for size
          };

          // Only add response_format for models that support it (dall-e-2, dall-e-3)
          if (model === 'dall-e-2' || model === 'dall-e-3') {
            genericParams.response_format = 'b64_json';
          }

          const response = await this.openai.images.generate(genericParams);
          const b64Data = response.data?.[0]?.b64_json;

          if (b64Data) {
            console.log(`Successfully generated image with ${model} model`);
            return { success: true, base64Image: b64Data };
          } else {
            throw new Error(`No image data returned from ${model}`);
          }
        } catch (modelError) {
          // Log the error but don't automatically fall back to gpt-image-1
          console.error(`${model} generation failed:`, modelError);

          // Log detailed error information for debugging
          if (modelError instanceof Error) {
            console.error(`${model} Error Details:`, {
              name: modelError.name,
              message: modelError.message,
              stack: modelError.stack,
              status: (modelError as any).status,
              code: (modelError as any).code,
              type: (modelError as any).type,
              param: (modelError as any).param
            });
          }

          // Only fall back to gpt-image-1 if the model is not already gpt-image-1
          if (model !== 'gpt-image-1') {
            console.warn(`Falling back to gpt-image-1 as requested model ${model} failed`);

            try {
              // Build the fallback parameters object with only the parameters supported by gpt-image-1
              const fallbackParams: any = {
                prompt,
                model: 'gpt-image-1',
                n: 1,
                size: size as '1024x1024' | '1536x1024' | '1024x1536' | '1792x1024' | '1024x1792' | 'auto' // Type assertion for size
              };

              // Add gpt-image-1 specific parameters
              fallbackParams.quality = quality as 'low' | 'medium' | 'high' | 'auto';
              fallbackParams.background = background as 'transparent' | 'opaque' | 'auto';
              fallbackParams.output_format = format as 'png' | 'jpeg' | 'webp';

              // Add output_compression parameter only if it's defined and format is jpeg or webp
              if (compression !== undefined && (format === 'jpeg' || format === 'webp')) {
                fallbackParams.output_compression = compression;
              }

              console.log('Attempting to generate image with gpt-image-1 fallback using parameters:', fallbackParams);
              const fallbackResponse = await this.openai.images.generate(fallbackParams);

              const fallbackB64Data = fallbackResponse.data?.[0]?.b64_json;
              if (fallbackB64Data) {
                console.log('Successfully generated image with gpt-image-1 fallback');
                return { success: true, base64Image: fallbackB64Data };
              } else {
                throw new Error('No image data returned from gpt-image-1 fallback');
              }
            } catch (fallbackError: any) {
              console.error('gpt-image-1 fallback failed:', fallbackError);
              throw new Error(`Fallback to gpt-image-1 failed: ${fallbackError?.message || 'Unknown error'}`);
            }
          } else {
            // If the model is already gpt-image-1 and it failed, don't try again
            throw new Error('gpt-image-1 model failed to generate an image');
          }
        }
      }
    } catch (error: any) {
      console.error('All image generation attempts failed:', error);
      return {
        success: false,
        error: error?.message || 'Image generation failed with all models',
      };
    }
  }
}
