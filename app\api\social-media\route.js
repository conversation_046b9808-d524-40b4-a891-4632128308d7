import { NextResponse } from "next/server";
import { socialMediaService } from "lib/social-media";

/**
 * API route for social media content generation
 */
export async function POST(request) {
  try {
    const { action, content, platforms, options, days } = await request.json();
    
    if (!content) {
      return NextResponse.json({ 
        error: "Content is required" 
      }, { status: 400 });
    }
    
    let result;
    
    // Handle different actions
    switch (action) {
      case "generatePosts":
        result = await socialMediaService.generatePosts(
          content,
          platforms || ["twitter", "linkedin", "facebook"],
          options || {}
        );
        break;
        
      case "generateContentCalendar":
        result = await socialMediaService.generateContentCalendar(
          content,
          platforms || ["twitter", "linkedin", "facebook"],
          days || 7,
          options || {}
        );
        break;
        
      default:
        return NextResponse.json({ 
          error: "Invalid action. Supported actions: generatePosts, generateContentCalendar" 
        }, { status: 400 });
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in social media content generation:", error);
    return NextResponse.json({ 
      success: false,
      error: error.message || "An error occurred during content generation"
    }, { status: 500 });
  }
}
