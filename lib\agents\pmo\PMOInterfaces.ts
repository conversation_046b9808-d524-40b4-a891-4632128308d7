/**
 * PMO Interfaces
 *
 * This file contains interfaces for the PMO Agent system, which manages tasks
 * across five Agentic Teams: Marketing, Research, Software Design, Sales, and Business Analysis.
 */

import { Task } from 'admin/planner/types'; // Assuming this path is correct and Task is defined

// Agentic Team IDs
export enum AgenticTeamId {
  Marketing = 'Ag001',
  Research = 'Ag002',
  SoftwareDesign = 'Ag003',
  Sales = 'Ag004',
  BusinessAnalysis = 'Ag005',
  InvestigativeResearch = 'Ag006',
  CodebaseDocumentation = 'Ag007'
}

// PMO Record Status
export type PMORecordStatus = 'Draft' | 'In Progress' | 'Completed' | 'Cancelled';

// PMO Record Priority
export type PMORecordPriority = 'Low' | 'Medium' | 'High' | 'Critical';

// Model Provider
export type ModelProvider = 'openai' | 'anthropic' | 'groq' | 'google';

// PMO Project Task Mapping interface for hierarchical structure
export interface PMOProjectTaskMapping {
  [projectId: string]: {
    projectInfo: {
      id: string;
      name: string;
      createdAt: Date;
      status: string;
    };
    taskIds: string[];
  };
}

// PMO Record interface
export interface PMORecord {
  id: string; // PMOId (Auto generated UUIDv4)
  title: string; // PMO Title from the form
  description: string; // Optimized description from the form
  status: PMORecordStatus;
  priority: PMORecordPriority;
  createdAt: Date; // Start Date/time
  updatedAt: Date;
  dueDate?: Date | null; // Optional due date
  createdBy: string; // User ID (email)

  // DEPRECATED: Legacy flat arrays - kept for backward compatibility
  projectIds: string[]; // Array of project IDs [Many ProjectId's can have the same PMOId]
  taskIds: string[]; // Array of task IDs created for ProjectId

  // NEW: Hierarchical structure
  projectTaskMapping?: PMOProjectTaskMapping; // Hierarchical project-task mapping

  category: string; // 'Unknown' or Category Name
  sourceFile?: string | null; // Namespace of file (optional)
  fileName?: string | null; // File name (optional)
  customContext?: string | null; // Custom context text
  contextFiles?: string[] | null; // Array of file IDs for context
  contextCategories?: string[] | null; // Array of categories for context
  pmoAssessment?: string | null; // Analysis generated by PMO Agent
  agentIds: AgenticTeamId[]; // Array of AgentIds (Ag001 -> Ag005) assigned to this PMO
  summary?: string | null; // PMO-generated summary
  teamSelectionRationale?: string | null; // Rationale for team selection
  resourceRecommendations?: string[] | null; // Recommended resources/tools
}

// PMO Task Assignment interface
export interface PMOTaskAssignment {
  pmoRecordId: string;
  teamId: AgenticTeamId;
  taskId: string;
  assignedAt: Date;
  status: 'Pending' | 'In Progress' | 'Completed' | 'Failed';
  output?: string; // Output from the team
  outputDocumentIds?: string[]; // IDs of documents produced by the team
}

// Team Selection Result interface
// Note: This seems similar to parts of TaskAnalysisResult. Review if needed.
export interface TeamSelectionResult {
  selectedTeams: AgenticTeamId[];
  rationale: string;
  recommendedResources?: string[];
}

// Task Analysis Result interface
// This is used by PMOAgent (the one that creates planner tasks)
export interface TaskAnalysisResult {
  formalizedTask: string;
  breakdown: string[];
  selectedTeams: AgenticTeamId[];
  teamSelectionRationale: string;
  recommendedResources?: string[];
}

// PMO Context Options interface
export interface PMOContextOptions {
  customContext?: string | null;
  fileIds?: string[] | null;
  categoryIds?: string[] | null;
}

// PMO Form Input interface
// This is likely used as input to both PMOAssessmentAgent and PMOAgent
export interface PMOFormInput {
  title: string; // PMO Title
  description: string; // Description to be optimized/analyzed
  priority: PMORecordPriority;
  projectId?: string | null; // Optional initial project ID (for PMOAgent that creates tasks)
  category?: string | null; // 'Unknown' or Category Name
  sourceFile?: string | null; // Namespace of file (optional)
  fileName?: string | null; // File name (optional)
  contextOptions: PMOContextOptions;
  modelProvider?: ModelProvider | null; // AI model provider (openai, anthropic, etc.)
  modelName?: string | null; // Specific model name (gpt-4o, claude-3, etc.)
}

// PMO Agent Options interface
// Common options for any PMO-related agent
export interface PMOAgentOptions {
  userId: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: PMOStreamUpdate) => void;
}

// PMO Stream Update interface
export interface PMOStreamUpdate {
  stage:
    // Stages primarily for PMOAssessmentAgent
    | 'analyzing-task' // Initial analysis step for PMOAssessmentAgent
    | 'context-retrieval' // ADDED: For when PMOAssessmentAgent is getting context
    | 'generating-assessment'
    | 'delegating-to-strategic-director'
    | 'generating-requirements-document'
    // Stages primarily for PMOAgent (task creator) - keep if this interface is shared
    | 'selecting-team' // PMOAgent's internal team selection
    | 'creating-pmo-record' // PMOAgent creating the PMORecord in Firestore
    | 'assigning-tasks' // PMOAgent creating tasks in the planner
    // General/Shared Stages
    | 'complete'
    | 'error'; // ADDED: For streaming error information
  data?: any;
  message?: string;
  timestamp?: string; // ADDED: Useful for client-side logging/ordering
}

// PMO Agent Result interface
// This result seems specific to the PMOAgent that creates planner tasks
export interface PMOAgentResult {
  success: boolean;
  pmoRecordId: string;
  originalRequest: string;
  formalizedRequest: string;
  taskAnalysis: TaskAnalysisResult;
  pmoAssessment: string; // The analysis generated by PMO Agent (could be from TaskAnalysisResult.formalizedTask)
  assignedTeams: AgenticTeamId[];
  createdTasks: Task[];
  supportingCharts?: any[]; // Chart generation results for visual PMO documentation
  error?: string;
}