import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { z } from 'zod';

// Zod schema for request validation
const CodebaseAnalysisRequestSchema = z.object({
  paths: z.array(z.string().min(1, "Path cannot be empty")).min(1, "At least one path is required"),
  analysisType: z.enum(['quick', 'detailed']).default('detailed'),
  includeContent: z.boolean().default(false)
});

interface CodebaseAnalysisRequest {
  paths: string[];
  analysisType?: 'quick' | 'detailed';
  includeContent?: boolean;
}

type CodebaseAnalysisRequestType = z.infer<typeof CodebaseAnalysisRequestSchema>;

interface CodebaseAnalysisResult {
  totalFiles: number;
  totalLines: number;
  totalSize: number;
  languages: { [key: string]: number };
  directories: string[];
  largestFiles: Array<{ path: string; size: number; lines?: number }>;
  frameworks: string[];
  dependencies: string[];
  configFiles: string[];
  complexity: 'low' | 'medium' | 'high';
  recommendations: string[];
}

/**
 * POST /api/codebase-documentation/analyze
 * 
 * Analyze codebase structure and provide metrics
 */
export async function POST(request: Request) {
  try {
    const rawBody = await request.json();

    // Validate request using Zod schema
    const validationResult = CodebaseAnalysisRequestSchema.safeParse(rawBody);

    if (!validationResult.success) {
      console.error('Request validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: validationResult.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const body: CodebaseAnalysisRequestType = validationResult.data;
    const analysis = await analyzeCodebase(body.paths, body.analysisType, body.includeContent);

    return NextResponse.json({
      success: true,
      analysis,
      analyzedPaths: body.paths,
      analysisType: body.analysisType
    });

  } catch (error: any) {
    console.error('Codebase analysis API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to analyze codebase' 
      },
      { status: 500 }
    );
  }
}

/**
 * Analyze codebase structure and metrics
 */
async function analyzeCodebase(
  paths: string[],
  analysisType: 'quick' | 'detailed',
  includeContent: boolean
): Promise<CodebaseAnalysisResult> {
  const analysis: CodebaseAnalysisResult = {
    totalFiles: 0,
    totalLines: 0,
    totalSize: 0,
    languages: {},
    directories: [],
    largestFiles: [],
    frameworks: [],
    dependencies: [],
    configFiles: [],
    complexity: 'low',
    recommendations: []
  };

  const processedFiles = new Set<string>();
  const frameworkIndicators = new Map<string, string[]>([
    ['React', ['react', 'jsx', 'tsx', 'react-dom']],
    ['Next.js', ['next', 'next.config.js', 'next.config.ts']],
    ['Vue.js', ['vue', '.vue', 'vue-router']],
    ['Angular', ['@angular', 'angular.json', '.component.ts']],
    ['Express', ['express', 'app.js', 'server.js']],
    ['Django', ['django', 'manage.py', 'settings.py']],
    ['Flask', ['flask', 'app.py']],
    ['Spring Boot', ['spring-boot', 'application.properties', 'pom.xml']],
    ['Laravel', ['laravel', 'artisan', 'composer.json']],
    ['Rails', ['rails', 'Gemfile', 'config/routes.rb']]
  ]);

  for (const inputPath of paths) {
    const safePath = path.resolve(inputPath);
    
    // Security check
    if (!safePath.startsWith(process.cwd())) {
      continue;
    }

    await analyzePathRecursively(safePath, analysis, processedFiles, analysisType, includeContent);
  }

  // Detect frameworks
  analysis.frameworks = detectFrameworks(analysis, frameworkIndicators);

  // Calculate complexity
  analysis.complexity = calculateComplexity(analysis);

  // Generate recommendations
  analysis.recommendations = generateRecommendations(analysis);

  // Sort largest files
  analysis.largestFiles.sort((a, b) => b.size - a.size);
  analysis.largestFiles = analysis.largestFiles.slice(0, 10);

  return analysis;
}

/**
 * Recursively analyze a path
 */
async function analyzePathRecursively(
  currentPath: string,
  analysis: CodebaseAnalysisResult,
  processedFiles: Set<string>,
  analysisType: 'quick' | 'detailed',
  includeContent: boolean
): Promise<void> {
  try {
    const stats = await fs.stat(currentPath);

    if (stats.isDirectory()) {
      analysis.directories.push(currentPath);
      
      // Skip certain directories
      const dirName = path.basename(currentPath);
      const skipDirs = ['node_modules', '.git', '.next', 'dist', 'build', '.vscode', 'coverage'];
      if (skipDirs.includes(dirName)) {
        return;
      }

      const items = await fs.readdir(currentPath);
      for (const item of items) {
        const itemPath = path.join(currentPath, item);
        await analyzePathRecursively(itemPath, analysis, processedFiles, analysisType, includeContent);
      }
    } else {
      // Process file
      if (processedFiles.has(currentPath)) {
        return;
      }
      processedFiles.add(currentPath);

      analysis.totalFiles++;
      analysis.totalSize += stats.size;

      const ext = path.extname(currentPath).toLowerCase();
      const fileName = path.basename(currentPath);

      // Count language files
      const language = getLanguageFromExtension(ext);
      if (language) {
        analysis.languages[language] = (analysis.languages[language] || 0) + 1;
      }

      // Track config files
      if (isConfigFile(fileName)) {
        analysis.configFiles.push(currentPath);
      }

      // Track dependencies from package.json, requirements.txt, etc.
      if (fileName === 'package.json' || fileName === 'requirements.txt' || fileName === 'Gemfile') {
        try {
          const content = await fs.readFile(currentPath, 'utf-8');
          const deps = extractDependencies(fileName, content);
          analysis.dependencies.push(...deps);
        } catch (error) {
          // Ignore read errors
        }
      }

      // Count lines for text files (if detailed analysis or content requested)
      if ((analysisType === 'detailed' || includeContent) && isTextFile(ext)) {
        try {
          const content = await fs.readFile(currentPath, 'utf-8');
          const lines = content.split('\n').length;
          analysis.totalLines += lines;

          // Track largest files
          analysis.largestFiles.push({
            path: currentPath,
            size: stats.size,
            lines
          });
        } catch (error) {
          // File might be binary or unreadable
          analysis.largestFiles.push({
            path: currentPath,
            size: stats.size
          });
        }
      } else {
        analysis.largestFiles.push({
          path: currentPath,
          size: stats.size
        });
      }
    }
  } catch (error) {
    // Skip files/directories that can't be accessed
  }
}

/**
 * Get programming language from file extension
 */
function getLanguageFromExtension(ext: string): string | null {
  const languageMap: { [key: string]: string } = {
    '.js': 'JavaScript',
    '.jsx': 'JavaScript',
    '.ts': 'TypeScript',
    '.tsx': 'TypeScript',
    '.py': 'Python',
    '.java': 'Java',
    '.cpp': 'C++',
    '.c': 'C',
    '.cs': 'C#',
    '.php': 'PHP',
    '.rb': 'Ruby',
    '.go': 'Go',
    '.rs': 'Rust',
    '.swift': 'Swift',
    '.kt': 'Kotlin',
    '.scala': 'Scala',
    '.html': 'HTML',
    '.css': 'CSS',
    '.scss': 'SCSS',
    '.sass': 'Sass',
    '.less': 'Less',
    '.vue': 'Vue',
    '.svelte': 'Svelte',
    '.sql': 'SQL',
    '.sh': 'Shell',
    '.bash': 'Bash',
    '.ps1': 'PowerShell',
    '.yaml': 'YAML',
    '.yml': 'YAML',
    '.json': 'JSON',
    '.xml': 'XML',
    '.md': 'Markdown',
    '.dockerfile': 'Docker'
  };

  return languageMap[ext] || null;
}

/**
 * Check if file is a configuration file
 */
function isConfigFile(fileName: string): boolean {
  const configFiles = [
    'package.json', 'tsconfig.json', 'webpack.config.js', 'next.config.js',
    'tailwind.config.js', 'postcss.config.js', '.env', '.env.local',
    'docker-compose.yml', 'Dockerfile', '.gitignore', '.eslintrc',
    'prettier.config.js', 'jest.config.js', 'babel.config.js'
  ];

  return configFiles.includes(fileName) || fileName.startsWith('.env');
}

/**
 * Check if file is a text file
 */
function isTextFile(ext: string): boolean {
  const textExtensions = [
    '.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.cs',
    '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.html',
    '.css', '.scss', '.sass', '.less', '.vue', '.svelte', '.sql',
    '.sh', '.bash', '.ps1', '.yaml', '.yml', '.json', '.xml', '.md',
    '.txt', '.log', '.config'
  ];

  return textExtensions.includes(ext);
}

/**
 * Extract dependencies from package files
 */
function extractDependencies(fileName: string, content: string): string[] {
  const deps: string[] = [];

  try {
    if (fileName === 'package.json') {
      const pkg = JSON.parse(content);
      if (pkg.dependencies) {
        deps.push(...Object.keys(pkg.dependencies));
      }
      if (pkg.devDependencies) {
        deps.push(...Object.keys(pkg.devDependencies));
      }
    } else if (fileName === 'requirements.txt') {
      const lines = content.split('\n');
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const dep = trimmed.split('==')[0].split('>=')[0].split('<=')[0];
          deps.push(dep);
        }
      }
    }
  } catch (error) {
    // Ignore parsing errors
  }

  return deps;
}

/**
 * Detect frameworks based on analysis
 */
function detectFrameworks(
  analysis: CodebaseAnalysisResult,
  frameworkIndicators: Map<string, string[]>
): string[] {
  const detectedFrameworks: string[] = [];

  for (const [framework, indicators] of frameworkIndicators) {
    const hasIndicator = indicators.some(indicator => 
      analysis.dependencies.includes(indicator) ||
      analysis.configFiles.some(file => path.basename(file) === indicator)
    );

    if (hasIndicator) {
      detectedFrameworks.push(framework);
    }
  }

  return detectedFrameworks;
}

/**
 * Calculate codebase complexity
 */
function calculateComplexity(analysis: CodebaseAnalysisResult): 'low' | 'medium' | 'high' {
  const fileCount = analysis.totalFiles;
  const languageCount = Object.keys(analysis.languages).length;
  const dependencyCount = analysis.dependencies.length;

  if (fileCount > 500 || languageCount > 5 || dependencyCount > 50) {
    return 'high';
  } else if (fileCount > 100 || languageCount > 3 || dependencyCount > 20) {
    return 'medium';
  } else {
    return 'low';
  }
}

/**
 * Generate recommendations based on analysis
 */
function generateRecommendations(analysis: CodebaseAnalysisResult): string[] {
  const recommendations: string[] = [];

  if (analysis.totalFiles > 1000) {
    recommendations.push('Consider breaking down the codebase into smaller, more manageable modules');
  }

  if (Object.keys(analysis.languages).length > 5) {
    recommendations.push('Multiple programming languages detected - consider standardizing where possible');
  }

  if (analysis.dependencies.length > 100) {
    recommendations.push('Large number of dependencies - review and remove unused dependencies');
  }

  if (analysis.configFiles.length === 0) {
    recommendations.push('No configuration files detected - consider adding proper configuration management');
  }

  if (!analysis.configFiles.some(file => file.includes('README'))) {
    recommendations.push('Add a comprehensive README.md file for better documentation');
  }

  return recommendations;
}
