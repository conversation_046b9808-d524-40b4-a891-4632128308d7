'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AlertCircle } from 'lucide-react';
import { useSession, signIn } from 'next-auth/react';
import { checkMarketingAgentAccess } from '../../lib/firebase/accounts';

export default function MarketingAgentLoginPage() {
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  const { data: session, status } = useSession();
  const router = useRouter();

  // Check if user is already authenticated and has access
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true);

      if (status === 'loading') {
        return;
      }

      if (status === 'authenticated' && session?.user?.email) {
        try {
          const hasAccess = await checkMarketingAgentAccess(session.user.email);

          if (hasAccess) {
            // User is authenticated and has access, redirect to marketing agent tests
            router.push('/marketing-agent-tests');
          } else {
            // User is authenticated but doesn't have access
            setError('You do not have permission to access the Marketing Agent Tests. Please contact an administrator.');
            setIsCheckingAuth(false);
          }
        } catch (error) {
          console.error('Error checking authorization:', error);
          setError('An error occurred while checking your access. Please try again.');
          setIsCheckingAuth(false);
        }
      } else {
        // User is not authenticated, show login form
        setIsCheckingAuth(false);
      }
    };

    checkAuth();
  }, [session, status, router]);



  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      setError('');
      await signIn('google', { callbackUrl: '/marketing-agent-tests' });
    } catch (err: any) {
      console.error('Google login error:', err);
      setError(err.message || 'Failed to login with Google');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking authorization
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-lg shadow-lg">
          <div className="flex justify-center mb-6">
            <img
              src="/logo5b.png"
              alt="ike Logo"
              className="h-14 w-auto hover:opacity-80 transition-opacity duration-300"
            />
          </div>
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative">
              <div className="w-16 h-16 border-t-4 border-b-4 border-purple-500 rounded-full animate-spin"></div>
              <div className="w-16 h-16 border-l-4 border-r-4 border-transparent rounded-full absolute top-0 animate-spin-slow"></div>
            </div>
            <p className="text-center text-purple-300 font-medium">Checking authorization...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="w-full max-w-md p-8 space-y-8 bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <img
              src="/logo5b.png"
              alt="ike Logo"
              className="h-14 w-auto hover:opacity-80 transition-opacity duration-300"
            />
          </div>
          <h1 className="text-2xl font-bold text-white">Marketing Agent Login</h1>
          <p className="mt-2 text-gray-400">Sign in to access the marketing agent tests</p>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded flex items-start">
            <AlertCircle className="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}

        <div className="mt-8 space-y-6">
          <button
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <g transform="matrix(1, 0, 0, 1, 27.009001, -39.238998)">
                    <path fill="#4285F4" d="M -3.264 51.509 C -3.264 50.719 -3.334 49.969 -3.454 49.239 L -14.754 49.239 L -14.754 53.749 L -8.284 53.749 C -8.574 55.229 -9.424 56.479 -10.684 57.329 L -10.684 60.329 L -6.824 60.329 C -4.564 58.239 -3.264 55.159 -3.264 51.509 Z" />
                    <path fill="#34A853" d="M -14.754 63.239 C -11.514 63.239 -8.804 62.159 -6.824 60.329 L -10.684 57.329 C -11.764 58.049 -13.134 58.489 -14.754 58.489 C -17.884 58.489 -20.534 56.379 -21.484 53.529 L -25.464 53.529 L -25.464 56.619 C -23.494 60.539 -19.444 63.239 -14.754 63.239 Z" />
                    <path fill="#FBBC05" d="M -21.484 53.529 C -21.734 52.809 -21.864 52.039 -21.864 51.239 C -21.864 50.439 -21.724 49.669 -21.484 48.949 L -21.484 45.859 L -25.464 45.859 C -26.284 47.479 -26.754 49.299 -26.754 51.239 C -26.754 53.179 -26.284 54.999 -25.464 56.619 L -21.484 53.529 Z" />
                    <path fill="#EA4335" d="M -14.754 43.989 C -12.984 43.989 -11.404 44.599 -10.154 45.789 L -6.734 42.369 C -8.804 40.429 -11.514 39.239 -14.754 39.239 C -19.444 39.239 -23.494 41.939 -25.464 45.859 L -21.484 48.949 C -20.534 46.099 -17.884 43.989 -14.754 43.989 Z" />
                  </g>
                </svg>
                Sign in with Google
              </>
            )}
          </button>

          <div className="flex items-center justify-center">
            <div className="text-sm">
              <a href="/" className="font-medium text-purple-400 hover:text-purple-300">
                Return to Home
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
