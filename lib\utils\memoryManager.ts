/**
 * memoryManager.ts
 * 
 * Utility for managing memory usage in Node.js applications,
 * particularly for large document processing tasks.
 */

/**
 * Attempts to run garbage collection if available
 * Note: Node.js must be started with --expose-gc flag
 * 
 * @returns {boolean} True if garbage collection was triggered, false otherwise
 */
export function triggerGarbageCollection(): boolean {
  try {
    if (typeof global.gc === 'function') {
      global.gc();
      return true;
    }
    return false;
  } catch (error) {
    console.warn('Failed to trigger garbage collection:', error);
    console.warn('Make sure Node.js is started with --expose-gc flag');
    return false;
  }
}

/**
 * Gets the current memory usage statistics
 * 
 * @returns {object} Memory usage statistics
 */
export function getMemoryUsage(): {
  heapUsed: number;
  heapTotal: number;
  rss: number;
  external: number;
  arrayBuffers: number;
  percentHeapUsed: number;
} {
  const memoryUsage = process.memoryUsage();
  
  return {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
    rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
    external: Math.round(memoryUsage.external / 1024 / 1024), // MB
    arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024), // MB
    percentHeapUsed: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
  };
}

/**
 * Logs the current memory usage
 * 
 * @param {string} label - Label to identify this memory usage log
 */
export function logMemoryUsage(label: string = 'Memory Usage'): void {
  const usage = getMemoryUsage();
  
  console.log(`[${label}] Memory Usage:`);
  console.log(`  Heap Used: ${usage.heapUsed} MB (${usage.percentHeapUsed}% of total heap)`);
  console.log(`  Heap Total: ${usage.heapTotal} MB`);
  console.log(`  RSS: ${usage.rss} MB`);
  console.log(`  External: ${usage.external} MB`);
  console.log(`  Array Buffers: ${usage.arrayBuffers} MB`);
}

/**
 * Checks if memory usage is above a certain threshold and triggers GC if needed
 * 
 * @param {number} thresholdPercent - Percentage threshold (0-100) to trigger GC
 * @returns {boolean} True if GC was triggered, false otherwise
 */
export function checkAndCleanMemory(thresholdPercent: number = 75): boolean {
  const usage = getMemoryUsage();
  
  if (usage.percentHeapUsed > thresholdPercent) {
    console.log(`Memory usage above ${thresholdPercent}% threshold (${usage.percentHeapUsed}%), triggering garbage collection`);
    return triggerGarbageCollection();
  }
  
  return false;
}

// Add TypeScript declaration for global.gc
declare global {
  namespace NodeJS {
    interface Global {
      gc?: () => void;
    }
  }
}
