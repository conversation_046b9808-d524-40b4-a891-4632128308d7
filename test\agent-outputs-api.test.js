/**
 * Test suite for agent-outputs API
 * 
 * This test verifies that the agent-outputs API works correctly
 * and can handle documents with missing timestamp fields.
 */

const { describe, test, expect } = require('@jest/globals');

// Mock the admin database
const mockAdminDb = {
  collection: jest.fn(() => ({
    doc: jest.fn(() => ({
      get: jest.fn()
    })),
    get: jest.fn()
  }))
};

// Mock the firebase admin module
jest.mock('../components/firebase/admin', () => ({
  adminDb: mockAdminDb
}));

// Mock NextResponse
const mockNextResponse = {
  json: jest.fn((data, options) => ({ data, options }))
};

jest.mock('next/server', () => ({
  NextResponse: mockNextResponse
}));

describe('Agent Outputs API', () => {
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/agent-outputs', () => {
    
    test('should handle empty collection', async () => {
      // Mock empty collection
      mockAdminDb.collection().get.mockResolvedValue({
        empty: true,
        docs: []
      });

      const { GET } = require('../app/api/agent-outputs/route');
      
      const mockRequest = {
        url: 'http://localhost:3001/api/agent-outputs?page=1&limit=15'
      };

      await GET(mockRequest);

      expect(mockNextResponse.json).toHaveBeenCalledWith({
        results: [],
        hasMore: false,
        page: 1,
        limit: 15,
        lastTimestamp: null
      });
    });

    test('should handle documents with missing timestamps', async () => {
      // Mock documents with various timestamp scenarios
      const mockDocs = [
        {
          id: 'doc1',
          data: () => ({
            agentType: 'BusinessAnalysis',
            title: 'Test Doc 1',
            timestamp: { toDate: () => new Date('2025-01-15') },
            createdAt: { toDate: () => new Date('2025-01-15') }
          })
        },
        {
          id: 'a61d9cd1-490a-4c21-b17d-b326ae390075',
          data: () => ({
            agentType: 'BusinessAnalysis',
            title: 'Agent iKe voice agent implementation',
            // No timestamp field
            createdAt: { toDate: () => new Date('2025-01-13') }
          })
        },
        {
          id: 'doc3',
          data: () => ({
            agentType: 'Investigative Research',
            title: 'Test Doc 3',
            timestamp: null,
            createdAt: { toDate: () => new Date('2025-01-14') }
          })
        }
      ];

      mockAdminDb.collection().get.mockResolvedValue({
        empty: false,
        docs: mockDocs
      });

      const { GET } = require('../app/api/agent-outputs/route');
      
      const mockRequest = {
        url: 'http://localhost:3001/api/agent-outputs?page=1&limit=15'
      };

      await GET(mockRequest);

      expect(mockNextResponse.json).toHaveBeenCalled();
      const callArgs = mockNextResponse.json.mock.calls[0][0];
      
      // Should return all documents
      expect(callArgs.results).toHaveLength(3);
      
      // Should include the missing document
      const missingDoc = callArgs.results.find(doc => doc.id === 'a61d9cd1-490a-4c21-b17d-b326ae390075');
      expect(missingDoc).toBeDefined();
      expect(missingDoc.title).toBe('Agent iKe voice agent implementation');
    });

    test('should handle specific document request', async () => {
      const mockDoc = {
        exists: true,
        id: 'a61d9cd1-490a-4c21-b17d-b326ae390075',
        data: () => ({
          agentType: 'BusinessAnalysis',
          title: 'Agent iKe voice agent implementation',
          createdAt: { toDate: () => new Date('2025-01-13') }
        })
      };

      mockAdminDb.collection().doc().get.mockResolvedValue(mockDoc);

      const { GET } = require('../app/api/agent-outputs/route');
      
      const mockRequest = {
        url: 'http://localhost:3001/api/agent-outputs?requestId=a61d9cd1-490a-4c21-b17d-b326ae390075'
      };

      await GET(mockRequest);

      expect(mockNextResponse.json).toHaveBeenCalled();
      const callArgs = mockNextResponse.json.mock.calls[0][0];
      
      expect(callArgs.id).toBe('a61d9cd1-490a-4c21-b17d-b326ae390075');
      expect(callArgs.title).toBe('Agent iKe voice agent implementation');
    });

    test('should handle document not found', async () => {
      mockAdminDb.collection().doc().get.mockResolvedValue({
        exists: false
      });

      const { GET } = require('../app/api/agent-outputs/route');
      
      const mockRequest = {
        url: 'http://localhost:3001/api/agent-outputs?requestId=nonexistent'
      };

      await GET(mockRequest);

      expect(mockNextResponse.json).toHaveBeenCalledWith(
        { error: 'Document not found' },
        { status: 404 }
      );
    });

    test('should sort documents correctly', async () => {
      const mockDocs = [
        {
          id: 'old-doc',
          data: () => ({
            agentType: 'BusinessAnalysis',
            timestamp: { toDate: () => new Date('2025-01-10') },
            createdAt: { toDate: () => new Date('2025-01-10') }
          })
        },
        {
          id: 'new-doc',
          data: () => ({
            agentType: 'BusinessAnalysis',
            timestamp: { toDate: () => new Date('2025-01-15') },
            createdAt: { toDate: () => new Date('2025-01-15') }
          })
        },
        {
          id: 'no-timestamp-doc',
          data: () => ({
            agentType: 'BusinessAnalysis',
            // No timestamp, only createdAt
            createdAt: { toDate: () => new Date('2025-01-12') }
          })
        }
      ];

      mockAdminDb.collection().get.mockResolvedValue({
        empty: false,
        docs: mockDocs
      });

      const { GET } = require('../app/api/agent-outputs/route');
      
      const mockRequest = {
        url: 'http://localhost:3001/api/agent-outputs?page=1&limit=15'
      };

      await GET(mockRequest);

      const callArgs = mockNextResponse.json.mock.calls[0][0];
      const results = callArgs.results;
      
      // Should be sorted by timestamp/createdAt descending
      expect(results[0].id).toBe('new-doc'); // Most recent
      expect(results[1].id).toBe('no-timestamp-doc'); // Middle
      expect(results[2].id).toBe('old-doc'); // Oldest
    });
  });
});

// Helper function to run a quick integration test
async function testMissingDocumentRetrieval() {
  console.log('Testing missing document retrieval...');
  
  try {
    // This would be run in a real environment
    const response = await fetch('http://localhost:3001/api/agent-outputs?requestId=a61d9cd1-490a-4c21-b17d-b326ae390075');
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Missing document found:', data.id);
      return true;
    } else {
      console.log('❌ Missing document not found:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Error testing missing document:', error.message);
    return false;
  }
}

module.exports = {
  testMissingDocumentRetrieval
};
