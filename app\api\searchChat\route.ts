import { NextResponse } from "next/server";
import { setDoc, doc as firestoreDoc, increment, arrayUnion } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from "@pinecone-database/pinecone";
import { FirestoreStore } from "lib/FirestoreStore";
import { createGroqClient } from "lib/llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

// Initialize constants
const MODEL = process.env.GROQ_MODEL || "";
const pinecone = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || "",
});

// Type definitions
interface SearchResult {
  id: string;
  score: number;
  metadata: {
    category: string;
    summary: string;
    namespace: string;
    documentNamespace: string;
    chatId: string;
    userMessageId: string;
    aiMessageId: string;
    text: string;
    fileName: string;
    fileDocumentId: string;
    role: string;
    userId: string;
    createdAt: {
      seconds: number;
      nanoseconds: number;
    };
    timestamp: number;
  };
}

interface SearchLog {
  userId: string;
  query: string;
  timestamp: string;
  resultCount: number;
  topResultScore: number | null;
  hasResults: boolean;
  queryDurationMs: number;
  namespace: string;
  categories: string[];
  searchSessionId: string;
}

// Search logging function
async function logSearchActivity(
  session: any,
  query: string,
  results: SearchResult[],
  userNamespace: string,
  queryDuration: number
) {
  const searchLog: SearchLog = {
    userId: session.user.email,
    query: query,
    timestamp: new Date().toISOString(),
    resultCount: results.length,
    topResultScore: results.length > 0 ? results[0].score : null,
    hasResults: results.length > 0,
    queryDurationMs: queryDuration,
    namespace: userNamespace,
    categories: [...new Set(results.map(r => r.metadata.category))],
    searchSessionId: `${session.user.email}_${new Date().toDateString()}`
  };
  const userId = session.user.email
  const logId = `${Date.now()}_${userId}_${Math.random().toString(36).substring(7)}`;
  
  try {
    // Log the search details
    await setDoc(
      firestoreDoc(db, "searchLogs", logId),
      // doc(db, 'users', userId!, 'files', id)
      searchLog
    );
    
    // Update user's search statistics
    await setDoc(
      firestoreDoc(db, "user", userId,"userSearchStats"),
      {
        totalSearches: increment(1),
        lastSearchTimestamp: new Date().toISOString(),
        recentQueries: arrayUnion(query)
      },
      { merge: true }
    );
  } catch (error) {
    console.error("Failed to log search activity:", error);
  }
}

// Main route handler
export async function POST(req: Request) {
  try {
    const startTime = performance.now();

    // Authenticate user session
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Parse request body
    const { query, topK = 3 } = await req.json();
    const userNamespace = `${session.user.email}_chats`;
    
    // Initialize OpenAI embeddings
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY
    });

    // Generate embedding for search query
    const queryEmbedding = await embeddings.embedQuery(query);

    // Initialize Pinecone index and query namespace
    const index = await pinecone.index(process.env.PINECONE_INDEX!);
    const queryResponse = await index.namespace(userNamespace).query({
      vector: queryEmbedding,
      topK: topK,
      includeValues: true,
      includeMetadata: true,
    });

// Update the results mapping in the route handler
const results: SearchResult[] = queryResponse.matches.map(match => {
  const metadata = match.metadata as any; // Type as any initially to access all fields

  return {
    id: match.id,
    score: match.score ?? 0,
    metadata: {
      category: metadata.category || "Unknown",
      summary: metadata.summary || "No summary available",
      namespace: metadata.namespace || "",
      documentNamespace: metadata.documentNamespace || "",
      chatId: metadata.chatId || "",
      userMessageId: metadata.userMessageId || "",
      aiMessageId: metadata.aiMessageId || "",
      text: metadata.text || "",
      fileName: metadata.fileName || "",
      fileDocumentId: metadata.fileDocumentId || "",
      role: metadata.role || "user",
      userId: metadata.userId || "",
      createdAt: {
        seconds: metadata.createdAt?.seconds || 0,
        nanoseconds: metadata.createdAt?.nanoseconds || 0
      },
      timestamp: metadata.timestamp || Date.now()
    }
  };
});


    // Group results by document namespace
    const groupedResults = results.reduce((acc, result) => {
      const key = result.metadata.documentNamespace;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(result);
      return acc;
    }, {} as Record<string, SearchResult[]>);

    // Calculate query duration and log search activity
    const queryDuration = performance.now() - startTime;
    await logSearchActivity(
      session,
      query,
      results,
      userNamespace,
      queryDuration
    );

    // Return search results
    return NextResponse.json({
      results,
      groupedResults,
      totalResults: results.length,
      searchMetrics: {
        queryDurationMs: queryDuration,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error("Search error:", error);
    return new NextResponse(
      JSON.stringify({ 
        error: "Internal Server Error", 
        details: (error as Error).message,
        timestamp: new Date().toISOString()
      }), 
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}