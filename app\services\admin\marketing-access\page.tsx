'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '../../../../components/firebase';
import { grantMarketingAgentAccess, revokeMarketingAgentAccess } from '../../../lib/firebase/accounts';

interface UserAccess {
  email: string;
  firstName?: string;
  surname?: string;
  hasAccess: boolean;
}

export default function MarketingAccessPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState<UserAccess[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user is authorized to access this page (only SYS_ADMIN)
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/services/admin/login');
      return;
    }

    if (status === 'authenticated' && session?.user?.email !== '<EMAIL>') {
      router.push('/services/admin/unauthorized');
    }
  }, [session, status, router]);

  // Load all users from the Accounts collection
  useEffect(() => {
    const loadUsers = async () => {
      if (status !== 'authenticated' || session?.user?.email !== '<EMAIL>') {
        return;
      }

      setIsLoading(true);
      try {
        const accountsRef = collection(db, 'Accounts');
        const querySnapshot = await getDocs(accountsRef);

        const usersList: UserAccess[] = [];

        for (const docSnapshot of querySnapshot.docs) {
          const userData = docSnapshot.data();
          const email = docSnapshot.id;

          // Skip the SYS_ADMIN account
          if (email === '<EMAIL>') {
            continue;
          }

          const hasAccess = userData.accessRights &&
            (userData.accessRights.Admin === true || userData.accessRights.Agents === true);

          usersList.push({
            email,
            firstName: userData.firstName,
            surname: userData.surname,
            hasAccess: hasAccess || false
          });
        }

        // Sort by email
        usersList.sort((a, b) => a.email.localeCompare(b.email));

        setUsers(usersList);
      } catch (error) {
        console.error('Error loading users:', error);
        setError('Failed to load users. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadUsers();
  }, [session, status]);

  const handleToggleAccess = async (email: string, currentAccess: boolean) => {
    try {
      setIsSubmitting(true);
      setError('');
      setSuccess('');

      if (currentAccess) {
        await revokeMarketingAgentAccess(email);
      } else {
        await grantMarketingAgentAccess(email);
      }

      // Update the local state
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.email === email
            ? { ...user, hasAccess: !currentAccess }
            : user
        )
      );

      setSuccess(`Successfully ${currentAccess ? 'revoked' : 'granted'} access for ${email}`);
    } catch (error) {
      console.error('Error toggling access:', error);
      setError(`Failed to ${currentAccess ? 'revoke' : 'grant'} access. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newUserEmail.trim()) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      setIsSubmitting(true);
      setError('');
      setSuccess('');

      // Check if user already exists in our list
      const existingUser = users.find(user => user.email === newUserEmail);

      if (existingUser) {
        if (existingUser.hasAccess) {
          setError('This user already has access to marketing agent tests');
          return;
        }

        // Toggle access for existing user
        await handleToggleAccess(newUserEmail, false);
      } else {
        // Grant access to new user
        await grantMarketingAgentAccess(newUserEmail);

        // Add to local state
        setUsers(prevUsers => [
          ...prevUsers,
          { email: newUserEmail, hasAccess: true }
        ].sort((a, b) => a.email.localeCompare(b.email)));

        setSuccess(`Successfully granted access for ${newUserEmail}`);
      }

      // Clear the input
      setNewUserEmail('');
    } catch (error) {
      console.error('Error adding user:', error);
      setError('Failed to add user. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'loading' || (status === 'authenticated' && isLoading)) {
    return (
      <div className="min-h-screen bg-gray-900 p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold text-white mb-8">Marketing Agent Access Management</h1>
          <div className="bg-gray-800 p-8 rounded-lg shadow-lg">
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
            <p className="text-center text-gray-300 mt-4">Loading user data...</p>
          </div>
        </div>
      </div>
    );
  }

  if (status === 'authenticated' && session?.user?.email !== '<EMAIL>') {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">Marketing Agent Access Management</h1>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded mb-6">
            {success}
          </div>
        )}

        <div className="bg-gray-800 p-6 rounded-lg shadow-lg mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Add User Access</h2>
          <form onSubmit={handleAddUser} className="flex gap-4">
            <input
              type="email"
              value={newUserEmail}
              onChange={(e) => setNewUserEmail(e.target.value)}
              placeholder="Enter user email"
              className="flex-grow px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              disabled={isSubmitting}
            />
            <button
              type="submit"
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Processing...' : 'Grant Access'}
            </button>
          </form>
        </div>

        <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <h2 className="text-xl font-semibold text-white p-6 border-b border-gray-700">User Access List</h2>

          {users.length === 0 ? (
            <div className="p-6 text-center text-gray-400">
              No users found in the system.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Email
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Access Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-700">
                  {users.map((user) => (
                    <tr key={user.email} className="hover:bg-gray-750">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-200">
                        {user.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-200">
                        {user.firstName && user.surname
                          ? `${user.firstName} ${user.surname}`
                          : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {user.hasAccess ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-900 text-green-200">
                            Has Access
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-900 text-red-200">
                            No Access
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleToggleAccess(user.email, user.hasAccess)}
                          className={`px-3 py-1 rounded-md text-sm font-medium ${
                            user.hasAccess
                              ? 'bg-red-700 hover:bg-red-800 text-white'
                              : 'bg-green-700 hover:bg-green-800 text-white'
                          }`}
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? 'Processing...' : user.hasAccess ? 'Revoke Access' : 'Grant Access'}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
