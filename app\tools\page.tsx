'use client';

import React, { useState, ReactNode } from 'react';
import {
  <PERSON>ch,
  Search,
  BarChart,
  Send,
  Target,
  ChevronRight,
  ArrowLeft,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: ReactNode;
}

/**
 * AI Marketing Tools Page
 */
export default function ToolsPage() {
  const [selectedTool, setSelectedTool] = useState<string | null>(null);

  // Sample content for social media tool
  const sampleContent = `
Our new AI-powered marketing platform helps businesses streamline their content creation and distribution process. With advanced natural language processing capabilities, the platform can generate high-quality marketing copy, social media posts, and email campaigns tailored to your brand voice and target audience.

Key features include:
- AI-generated content that matches your brand voice
- Automated social media scheduling across multiple platforms
- Performance analytics and optimization recommendations
- SEO analysis and improvement suggestions
- Campaign management and tracking

The platform integrates seamlessly with popular marketing tools and CRMs, making it easy to incorporate into your existing workflow. Our customers report saving an average of 15 hours per week on content creation and seeing a 30% increase in engagement rates.

Try our 14-day free trial today and experience the future of marketing automation.
  `;

  // Tool configuration
  const tools: Tool[] = [
    {
      id: 'seo',
      name: 'SEO Analyzer',
      description: 'Analyze and optimize your content for search engines',
      icon: <Search className="text-blue-400" size={24} />
    },
    {
      id: 'analytics',
      name: 'Analytics Dashboard',
      description: 'Track and analyze your content performance',
      icon: <BarChart className="text-purple-400" size={24} />
    },
    {
      id: 'social',
      name: 'Social Media Generator',
      description: 'Create engaging social media content for multiple platforms',
      icon: <Send className="text-green-400" size={24} />
    },
    {
      id: 'campaign',
      name: 'Campaign Manager',
      description: 'Create and manage marketing campaigns',
      icon: <Target className="text-amber-400" size={24} />
    }
  ];

  // Render tool selection
  const renderToolSelection = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {tools.map(tool => (
        <div
          key={tool.id}
          onClick={() => setSelectedTool(tool.id)}
          className="bg-zinc-900 p-6 rounded-lg border border-zinc-700 hover:border-zinc-600 cursor-pointer transition-colors"
        >
          <div className="flex items-start justify-between">
            <div className="flex items-center">
              {tool.icon}
              <h3 className="text-xl font-semibold ml-3 text-white">{tool.name}</h3>
            </div>
            <ChevronRight className="text-zinc-500" size={20} />
          </div>
          <p className="mt-3 text-zinc-400">{tool.description}</p>
        </div>
      ))}
    </div>
  );

  // Render selected tool
  const renderSelectedTool = () => {
    const tool = tools.find(t => t.id === selectedTool);

    if (!tool) return null;

    return (
      <div>
        <button
          onClick={() => setSelectedTool(null)}
          className="flex items-center text-blue-400 hover:text-blue-300 mb-4"
        >
          <ArrowLeft size={16} className="mr-1" />
          Back to Tools
        </button>

        <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700 mb-6">
          <h2 className="text-2xl font-semibold flex items-center text-white mb-2">
            {tool.icon}
            <span className="ml-3">{tool.name}</span>
          </h2>
          <p className="text-zinc-400">{tool.description}</p>
        </div>

        <div className="bg-amber-900/20 border border-amber-800 rounded-lg p-6 text-amber-200">
          <div className="flex items-start">
            <AlertTriangle className="mr-3 mt-1 flex-shrink-0" size={24} />
            <div>
              <h3 className="text-lg font-semibold mb-2">Tool Under Development</h3>
              <p>
                This tool is currently being implemented. The functionality will be available in the next phase of the application.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <main className="min-h-screen p-4 md:p-8 bg-zinc-950 text-zinc-100">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-3xl font-bold text-white flex items-center">
            <Wrench className="mr-3 text-blue-400" size={28} />
            AI Marketing Tools
          </h1>

          <div className="flex space-x-2">
            <Link
              href="/tools-test"
              className="flex items-center px-4 py-2 bg-zinc-800 text-zinc-300 rounded-md hover:bg-zinc-700 focus:outline-none"
            >
              <Search className="mr-2 text-green-400" size={16} />
              Tools Test Page
            </Link>
            <Link
              href="/"
              className="flex items-center px-4 py-2 bg-zinc-800 text-zinc-300 rounded-md hover:bg-zinc-700 focus:outline-none"
            >
              Back to Scraper
            </Link>
          </div>
        </div>

        {selectedTool ? renderSelectedTool() : renderToolSelection()}
      </div>
    </main>
  );
}
