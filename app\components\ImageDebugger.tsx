'use client';

import React, { useState } from 'react';

interface ImageDebuggerProps {
  imageUrl: string;
  userName: string;
}

export default function ImageDebugger({ imageUrl, userName }: ImageDebuggerProps) {
  const [showDebug, setShowDebug] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  return (
    <div className="relative">
      <img
        src={imageUrl}
        alt={userName}
        className="h-10 w-10 rounded-full object-cover"
        referrerPolicy="no-referrer"
        onLoad={handleImageLoad}
        onError={handleImageError}
      />
      
      <button 
        className="absolute -top-1 -right-1 bg-gray-800 text-white rounded-full w-4 h-4 flex items-center justify-center text-xs"
        onClick={() => setShowDebug(!showDebug)}
        title="Debug image"
      >
        ?
      </button>
      
      {showDebug && (
        <div className="absolute top-full left-0 mt-2 p-2 bg-gray-900 text-xs text-white rounded shadow-lg z-50 w-64">
          <p className="mb-1"><strong>URL:</strong> {imageUrl}</p>
          <p className="mb-1"><strong>Status:</strong> {
            imageLoaded ? '✅ Loaded' : 
            imageError ? '❌ Error' : 
            '⏳ Loading...'
          }</p>
          <p className="mb-1"><strong>Type:</strong> {
            imageUrl.includes('googleusercontent') ? 'Google' :
            imageUrl.startsWith('/avatars/') ? 'Local' :
            'Other'
          }</p>
          <button 
            className="mt-2 px-2 py-1 bg-gray-700 text-white rounded text-xs"
            onClick={() => window.open(imageUrl, '_blank')}
          >
            Open in new tab
          </button>
        </div>
      )}
    </div>
  );
}
