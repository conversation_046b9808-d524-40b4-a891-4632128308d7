import { NextRequest, NextResponse } from 'next/server';
import { OpenAIEmbeddings } from "@langchain/openai";
import { adminDb } from "../../../components/firebase-admin";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { queryGroqAcrossNamespacesAndProcessAI } from '@/src/queryGroqAcrossNamespacesAndProcessAI';
import { getRelevantHistory } from '../../../lib/getRelevantHistory';

interface RequestBody {
  prompt: string;
  combinedHistory: string;
  fileDocumentId: string;
  menuSelected?: string;
}

// Enhanced vector conversion with error handling
async function convertPromptToVector(prompt: string): Promise<number[]> {
  try {
    const embeddings = new OpenAIEmbeddings();
    // embeddings.model="text-embedding-3-small"
    // embeddings.dimensions = 1024

    const queryVector = await embeddings.embedQuery(prompt);
    return queryVector;
  } catch (error) {
    console.error("Error converting prompt to vector:", error);
    throw new Error("Failed to convert prompt to vector");
  }
}

// Enhanced category fetching with error handling
async function whichCategoryForNamespace(userId: string, namespace: string): Promise<string> {
  try {
    const snapshot = await adminDb.collection('users')
      .doc(userId)
      .collection('files')
      .where('namespace', '==', namespace)
      .limit(1)
      .get();

    if (!snapshot.empty) {
      const doc = snapshot.docs[0];
      const data = doc.data();
      return data.category || 'Unknown';
    }

    return 'Unknown';
  } catch (error) {
    console.error("Error fetching category:", error);
    return 'Unknown';
  }
}

// Enhanced namespace fetching with error handling
async function fetchNamespacesForCategory(userId: string, category: string): Promise<string[]> {
  try {
    const snapshot = await adminDb.collection('users')
      .doc(userId)
      .collection('files')
      .where('category', '==', category)
      .get();

    return snapshot.docs
      .map(doc => doc.data().namespace)
      .filter((namespace): namespace is string => !!namespace);
  } catch (error) {
    console.error("Error fetching namespaces:", error);
    return [];
  }
}

// Main route handler with improved error handling and types
export async function POST(req: NextRequest) {
  // Initialize response headers
  const responseHeaders = {
    'Content-Type': 'text/plain',
    'Cache-Control': 'no-cache, no-transform',
    'Connection': 'keep-alive',
    'X-Content-Type-Options': 'nosniff'
  };

  try {
    // Auth validation
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Request body validation
    const body: RequestBody = await req.json();
    const { prompt, combinedHistory, fileDocumentId, menuSelected } = body;

    if (!prompt || !fileDocumentId) {
      return new NextResponse('Missing required fields', { 
        status: 400,
        headers: responseHeaders 
      });
    }

    // Process request with error handling
    const userId = session.user.email;
    let category: string | null = null;
    let namespaces: string[] = [];

    // Convert prompt to vector
    const queryVector = await convertPromptToVector(prompt);
    
    // Get relevant history
    const relevantHist = await getRelevantHistory(prompt, combinedHistory);

    // Determine category and namespaces
    try {
      category = await whichCategoryForNamespace(userId, fileDocumentId);
      
      if (category !== 'Unknown') {
        namespaces = await fetchNamespacesForCategory(userId, category);
        
        if (!namespaces.length) {
          console.warn("No namespaces found for category:", category);
          namespaces = [fileDocumentId]; // Fallback to fileDocumentId
        }
      } else {
        namespaces = [fileDocumentId];
      }
    } catch (error) {
      console.error("Error processing category/namespaces:", error);
      namespaces = [fileDocumentId]; // Fallback to fileDocumentId
    }

    // Create and return stream
    const stream = new ReadableStream({
      async start(controller) {
        try {
          await queryGroqAcrossNamespacesAndProcessAI(
            controller,
            queryVector,
            namespaces,
            prompt,
            relevantHist,
            category,
            userId
          );
        } catch (error) {
          console.error("Stream processing error:", error);
          controller.error(new Error("Stream processing failed"));
        }
      },
      cancel() {
        console.log("Stream cancelled by client");
      }
    });

    return new NextResponse(stream, { headers: responseHeaders });

  } catch (error) {
    console.error("Route handler error:", error);
    return new NextResponse('Internal Server Error', { 
      status: 500,
      headers: responseHeaders 
    });
  }
}