# Firebase Domain Authorization Guide

## Problem

If you're experiencing issues with Firebase authentication or Firestore data not loading in production, it's likely due to domain authorization issues. Firebase requires that all domains accessing your Firebase project are explicitly authorized in the Firebase console.

## Symptoms

- Projects and users data loads correctly in development but not in production
- Firebase authentication works in development but not in production
- Console errors related to permissions or unauthorized domains
- The diagnostic component shows domain authorization errors

## Solution

### 1. Add Your Domain to Firebase Authorized Domains

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`indef2024-d11b5`)
3. Navigate to **Authentication** > **Settings** > **Authorized domains**
4. Click **Add domain**
5. Add your production domain(s):
   - `ike-ai.com`
   - `www.ike-ai.com`
   - Any other domains where your application is hosted

### 2. Verify Firebase Security Rules

Make sure your Firestore security rules allow access from your application:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to all authenticated users
    match /{document=**} {
      allow read: if request.auth != null;
    }
    
    // More specific rules for write operations
    match /users/{userId} {
      allow write: if request.auth != null && 
                    (request.auth.uid == userId || 
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    match /projects/{projectId} {
      allow write: if request.auth != null;
    }
    
    match /tasks/{taskId} {
      allow write: if request.auth != null;
    }
  }
}
```

### 3. Check for API Key Restrictions

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to **APIs & Services** > **Credentials**
4. Find your Firebase API key
5. Make sure there are no domain restrictions that would prevent your production domain from using the API key

## Testing

After making these changes, you can use the Firebase Diagnostic component to verify that the connection is working correctly. The diagnostic component will be automatically displayed on the Project Planner and Team pages when there's an error.

## Additional Resources

- [Firebase Authentication Documentation](https://firebase.google.com/docs/auth)
- [Firebase Security Rules Documentation](https://firebase.google.com/docs/firestore/security/get-started)
- [Google Cloud API Keys Documentation](https://cloud.google.com/docs/authentication/api-keys)
