import { NextRequest, NextResponse } from "next/server";
import { llmTool } from "../../../lib/tools/llm-tool";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

/**
 * API route for generating project summaries
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({
        success: false,
        error: "Unauthorized"
      }, { status: 401 });
    }

    // Parse request body
    const { projectDetails } = await request.json();

    if (!projectDetails) {
      return NextResponse.json({
        success: false,
        error: "Project details are required"
      }, { status: 400 });
    }

    // Create the prompt with the project details
    const prompt = `
**Role:** Act as a project analyst tasked with summarizing project status.
**Objective:** Provide a clear and concise overview of the current project status based solely on the information provided below.

**Project Details:**
---
${JSON.stringify(projectDetails, null, 2)}
---
**Instructions:**
1. Analyze the provided project details.
2. Synthesize the key information regarding progress, risks, milestones, and overall health without specifically listing each status item, or detailing stats.
A broad summarization will suffice.
3. Focus only on information present in the details; do not infer or add external information.
4. Add an advisory comment on what improvements or next steps can be taken to boost productivity or re-align expectations given due dates on tasks and the overall project.
5. Format your response with clear sections using markdown headers (## for main sections).
6. Use bullet points for lists and statistics.
7. Use bold or italic text for emphasis on important metrics or deadlines.

**Deliverable:**
A well-formatted markdown summary of the overall project status and final comment
`;

    // Process with Groq
    const result = await llmTool.processContent({
      prompt,
      provider: 'groq',
      model: 'llama-3.3-70b-versatile',
      modelOptions: {
        temperature: 0.4
      }
    });

    return NextResponse.json({
      success: true,
      summary: result
    });
  } catch (error: any) {
    console.error("Error generating project summary:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred during summary generation"
    }, { status: 500 });
  }
}
