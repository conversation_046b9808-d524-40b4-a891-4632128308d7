'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Calendar,
  <PERSON><PERSON>hart,
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  Eye,
  Clock,
  RefreshCw
} from 'lucide-react';
// Use API routes instead of direct library calls

/**
 * Analytics Dashboard component for displaying content performance metrics
 */
export default function AnalyticsDashboard({ userId }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [timeRange, setTimeRange] = useState(30); // Default to 30 days

  // Fetch analytics data
  useEffect(() => {
    async function fetchAnalytics() {
      try {
        setLoading(true);
        setError(null);

        // Use the dedicated analytics API route
        const response = await fetch('/api/analytics', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'getContentPerformanceMetrics',
            days: timeRange,
            userId: userId || 'current-user'
          }),
        });

        if (!response.ok) {
          throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
          // Also fetch user activity metrics
          const userActivityResponse = await fetch('/api/analytics', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'getUserActivityMetrics',
              days: timeRange
            }),
          });

          if (!userActivityResponse.ok) {
            throw new Error(`Server responded with ${userActivityResponse.status}: ${userActivityResponse.statusText}`);
          }

          const userActivityResult = await userActivityResponse.json();

          // Combine the results into a report format
          const report = {
            success: true,
            reportDate: new Date(),
            timeRange: {
              start: new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000),
              end: new Date(),
              days: timeRange,
            },
            contentMetrics: result.metrics,
            userMetrics: userActivityResult.success ? userActivityResult.metrics : null,
          };

          setReportData(report);
        } else {
          setError(result.error || 'Failed to generate analytics report');
        }
      } catch (err) {
        setError(err.message || 'An error occurred while fetching analytics');
      } finally {
        setLoading(false);
      }
    }

    fetchAnalytics();
  }, [timeRange, userId]);

  // Handle time range change
  const handleTimeRangeChange = (days) => {
    setTimeRange(days);
  };

  // Render loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-zinc-900 rounded-lg border border-zinc-700 h-64">
        <RefreshCw className="animate-spin text-blue-400 mb-4" size={32} />
        <p className="text-zinc-300">Loading analytics data...</p>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-6 bg-red-900/20 border border-red-800 rounded-lg text-red-200">
        <h3 className="text-lg font-semibold mb-2">Error Loading Analytics</h3>
        <p>{error}</p>
      </div>
    );
  }

  // Render empty state
  if (!reportData || !reportData.contentMetrics) {
    return (
      <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
        <FileText className="mx-auto mb-4 text-zinc-600" size={32} />
        <h3 className="text-lg font-semibold mb-2 text-zinc-300">No Analytics Data Available</h3>
        <p>Start generating and viewing content to see analytics here.</p>
      </div>
    );
  }

  // Extract metrics from report data
  const { contentMetrics, userMetrics, timeRange: reportTimeRange } = reportData;

  return (
    <div className="analytics-dashboard space-y-6">
      {/* Time range selector */}
      <div className="flex items-center justify-between bg-zinc-900 p-4 rounded-lg border border-zinc-700">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <BarChart className="mr-2 text-blue-400" size={20} />
          Content Analytics
        </h2>

        <div className="flex space-x-2">
          {[7, 30, 90].map((days) => (
            <button
              key={days}
              onClick={() => handleTimeRangeChange(days)}
              className={`px-3 py-1 rounded-md text-sm ${
                timeRange === days
                  ? 'bg-blue-600 text-white'
                  : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'
              }`}
            >
              {days} days
            </button>
          ))}
        </div>
      </div>

      {/* Summary metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total views */}
        <div className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
          <div className="flex items-center justify-between">
            <h3 className="text-zinc-400 text-sm">Total Views</h3>
            <Eye className="text-blue-400" size={18} />
          </div>
          <p className="text-2xl font-bold text-white mt-2">
            {contentMetrics.totalViews.toLocaleString()}
          </p>
          <p className="text-xs text-zinc-500 mt-1">
            Last {reportTimeRange.days} days
          </p>
        </div>

        {/* Total generations */}
        <div className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
          <div className="flex items-center justify-between">
            <h3 className="text-zinc-400 text-sm">Content Generated</h3>
            <FileText className="text-emerald-400" size={18} />
          </div>
          <p className="text-2xl font-bold text-white mt-2">
            {contentMetrics.totalGenerations.toLocaleString()}
          </p>
          <p className="text-xs text-zinc-500 mt-1">
            Last {reportTimeRange.days} days
          </p>
        </div>

        {/* Unique documents */}
        <div className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
          <div className="flex items-center justify-between">
            <h3 className="text-zinc-400 text-sm">Unique Documents</h3>
            <FileText className="text-amber-400" size={18} />
          </div>
          <p className="text-2xl font-bold text-white mt-2">
            {contentMetrics.uniqueDocumentsViewed.toLocaleString()}
          </p>
          <p className="text-xs text-zinc-500 mt-1">
            Last {reportTimeRange.days} days
          </p>
        </div>

        {/* Active users */}
        <div className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
          <div className="flex items-center justify-between">
            <h3 className="text-zinc-400 text-sm">Active Users</h3>
            <Users className="text-purple-400" size={18} />
          </div>
          <p className="text-2xl font-bold text-white mt-2">
            {userMetrics?.totalUsers || 0}
          </p>
          <p className="text-xs text-zinc-500 mt-1">
            Last {reportTimeRange.days} days
          </p>
        </div>
      </div>

      {/* Top content */}
      <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
        <h3 className="text-lg font-semibold mb-4 text-white">Top Performing Content</h3>

        {contentMetrics.documentMetrics.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-zinc-800">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Document
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Views
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Generations
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Last Viewed
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-zinc-800">
                {contentMetrics.documentMetrics
                  .sort((a, b) => b.views - a.views)
                  .slice(0, 5)
                  .map((doc, index) => (
                    <tr key={index} className="hover:bg-zinc-800/50">
                      <td className="px-4 py-3 text-sm text-zinc-300">
                        {doc.documentId.substring(0, 8)}...
                      </td>
                      <td className="px-4 py-3 text-sm text-zinc-300">
                        {doc.views}
                      </td>
                      <td className="px-4 py-3 text-sm text-zinc-300">
                        {doc.generations}
                      </td>
                      <td className="px-4 py-3 text-sm text-zinc-300">
                        {doc.lastViewed ? new Date(doc.lastViewed).toLocaleDateString() : 'N/A'}
                      </td>
                    </tr>
                  ))}
              </tbody>
            </table>
          </div>
        ) : (
          <p className="text-zinc-400 text-center py-4">No content data available</p>
        )}
      </div>

      {/* Active users */}
      {userMetrics && userMetrics.mostActiveUsers && userMetrics.mostActiveUsers.length > 0 && (
        <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
          <h3 className="text-lg font-semibold mb-4 text-white">Most Active Users</h3>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-zinc-800">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Total Events
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Views
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Generations
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                    Last Active
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-zinc-800">
                {userMetrics.mostActiveUsers.slice(0, 5).map((user, index) => (
                  <tr key={index} className="hover:bg-zinc-800/50">
                    <td className="px-4 py-3 text-sm text-zinc-300">
                      {user.userId === 'anonymous' ? 'Anonymous' : user.userId.substring(0, 8)}...
                    </td>
                    <td className="px-4 py-3 text-sm text-zinc-300">
                      {user.totalEvents}
                    </td>
                    <td className="px-4 py-3 text-sm text-zinc-300">
                      {user.views}
                    </td>
                    <td className="px-4 py-3 text-sm text-zinc-300">
                      {user.generations}
                    </td>
                    <td className="px-4 py-3 text-sm text-zinc-300">
                      {user.lastActive ? new Date(user.lastActive).toLocaleDateString() : 'N/A'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
