'use client';

import React, { useState, useEffect } from 'react';
import { X, Copy, Check, FileDown } from 'lucide-react';
import MarkdownRenderer from '../MarkdownRenderer';

interface AgentOutputModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  content: string;
  contentType: 'output' | 'thinking' | 'collaboration' | 'query-documents' | 'qa-analysis';
  timestamp?: string;
  modelInfo?: {
    provider: string;
    model: string;
  };
}

const AgentOutputModal: React.FC<AgentOutputModalProps> = ({
  isOpen,
  onClose,
  title,
  content,
  contentType,
  timestamp,
  modelInfo,
}) => {
  const [copied, setCopied] = useState(false);
  const [generating, setGenerating] = useState(false);

  // Close modal on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose]);

  // Prevent scrolling of the body when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy content:', error);
    }
  };

  const handleGeneratePdf = () => {
    try {
      setGenerating(true);

      // Create a printable window with the content
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${title}</title>
              <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }
                h1 { color: #6b46c1; }
                h2 { color: #805ad5; }
                pre { white-space: pre-wrap; background: #f0f0f0; padding: 10px; border-radius: 5px; }
                code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
                .header { margin-bottom: 20px; border-bottom: 1px solid #e2e8f0; padding-bottom: 10px; }
                .content { margin-top: 20px; }
                @media print {
                  body { font-size: 12pt; }
                  h1 { font-size: 18pt; }
                  h2 { font-size: 16pt; }
                  .no-print { display: none; }
                  a { text-decoration: none; color: #000; }
                }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>${title}</h1>
                ${timestamp ? `<p>Generated on: ${timestamp}</p>` : ''}
                ${modelInfo ? `<p>Model: ${modelInfo.provider} | ${modelInfo.model}</p>` : ''}
                <p>Content Type: ${contentType.charAt(0).toUpperCase() + contentType.slice(1)}</p>
                <div class="no-print">
                  <button onclick="window.print();" style="padding: 8px 16px; background: #6b46c1; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                    Save as PDF
                  </button>
                  <p style="font-size: 12px; color: #666;">
                    (Use your browser's print function to save as PDF)
                  </p>
                </div>
              </div>
              <div class="content">
                ${formatMarkdownForHTML(content)}
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();

        // Add a small delay to ensure the content is loaded before printing
        setTimeout(() => {
          printWindow.focus();
          setGenerating(false);
        }, 500);
      } else {
        setGenerating(false);
        alert('Unable to open print window. Please check your popup blocker settings.');
      }
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      setGenerating(false);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  // Format markdown content for HTML display
  const formatMarkdownForHTML = (markdown: string) => {
    // Basic markdown formatting
    let html = markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      // Lists
      .replace(/^\s*\d+\.\s+(.*$)/gim, '<ol><li>$1</li></ol>')
      .replace(/^\s*[\-\*]\s+(.*$)/gim, '<ul><li>$1</li></ul>')
      // Line breaks
      .replace(/\n/gim, '<br>');

    return html;
  };

  // Get content type label
  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'output':
        return 'Output';
      case 'thinking':
        return 'Agent Output Response';
      case 'collaboration':
        return 'Strategic Analysis Collaboration';
      default:
        return 'Content';
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
      <div className="relative w-full h-full max-w-full max-h-full flex flex-col bg-gray-900 text-white">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-purple-300">{title}</h2>
            <div className="text-sm text-gray-400 flex items-center">
              <span className="mr-2">{getContentTypeLabel()}</span>
              {timestamp && <span className="mr-2">• {timestamp}</span>}
              {modelInfo && (
                <span>
                  • <span className="capitalize">{modelInfo.provider}</span> | {modelInfo.model}
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleCopyToClipboard}
              className="p-2 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors"
              title="Copy to clipboard"
            >
              {copied ? (
                <Check className="h-5 w-5 text-green-400" />
              ) : (
                <Copy className="h-5 w-5 text-purple-300" />
              )}
            </button>
            <button
              onClick={handleGeneratePdf}
              className="p-2 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors"
              title="Print / Save as PDF"
              disabled={generating}
            >
              <FileDown className={`h-5 w-5 ${generating ? 'text-gray-500' : 'text-purple-300'}`} />
            </button>
            <button
              onClick={onClose}
              className="p-2 bg-gray-800 hover:bg-gray-700 rounded-full transition-colors"
              title="Close"
            >
              <X className="h-5 w-5 text-purple-300" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto custom-scrollbar p-4 text-sm">
          <MarkdownRenderer content={content} />
        </div>
      </div>
    </div>
  );
};

export default AgentOutputModal;
