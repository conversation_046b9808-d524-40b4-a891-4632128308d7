import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from '../../../../../components/firebase-admin';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../../api/auth/[...nextauth]/authOptions';
import { v4 as uuidv4 } from 'uuid';

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  const { projectId } = await params;
  console.log(`[PROJECT DOCUMENTS] Starting document upload process for project: ${projectId}`);
  try {
    // Get the current session
    const session = await getServerSession(authOptions);
    console.log(`[PROJECT DOCUMENTS] Session user: ${session?.user?.email || 'No user found'}`);

    if (!session?.user?.email) {
      console.log(`[PROJECT DOCUMENTS] Unauthorized - No valid session`);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log(`[PROJECT DOCUMENTS] Processing document upload for project: ${projectId}`);

    if (!projectId) {
      console.log(`[PROJECT DOCUMENTS] Missing project ID`);
      return NextResponse.json({ error: 'Missing project ID' }, { status: 400 });
    }

    // Check if the user has access to this project
    console.log(`[PROJECT DOCUMENTS] Checking user access to project: ${projectId}`);
    const projectRef = adminDb.collection('projects').doc(projectId);
    const projectDoc = await projectRef.get();

    if (!projectDoc.exists) {
      console.log(`[PROJECT DOCUMENTS] Project not found: ${projectId}`);
      return NextResponse.json({ error: 'Project not found' }, { status: 404 });
    }

    const projectData = projectDoc.data();
    const userEmail = session.user.email;
    console.log(`[PROJECT DOCUMENTS] Checking access for user: ${userEmail}`);

    // Check if user is system admin
    const isSysAdmin = userEmail === '<EMAIL>';
    console.log(`[PROJECT DOCUMENTS] User is system admin: ${isSysAdmin}`);

    // Check if user is the project owner
    const isOwner = projectData?.owner === userEmail;
    console.log(`[PROJECT DOCUMENTS] User is project owner: ${isOwner}`);

    // Check if user is a project member
    // First get the user's ID
    console.log(`[PROJECT DOCUMENTS] Checking if user is a project member`);
    const userQuery = await adminDb.collection('users')
      .where('email', '==', userEmail)
      .limit(1)
      .get();

    let isMember = false;
    if (!userQuery.empty) {
      const userId = userQuery.docs[0].id;
      console.log(`[PROJECT DOCUMENTS] Found user ID: ${userId}`);
      isMember = projectData?.members && Array.isArray(projectData.members) && projectData.members.includes(userId);
      console.log(`[PROJECT DOCUMENTS] User is project member: ${isMember}`);
      if (projectData?.members) {
        console.log(`[PROJECT DOCUMENTS] Project members: ${JSON.stringify(projectData.members)}`);
      } else {
        console.log(`[PROJECT DOCUMENTS] Project has no members defined`);
      }
    } else {
      console.log(`[PROJECT DOCUMENTS] User not found in database`);
    }

    // If user doesn't have access, return 403
    if (!isSysAdmin && !isOwner && !isMember) {
      console.log(`[PROJECT DOCUMENTS] Access denied for user: ${userEmail} to project: ${projectId}`);
      return NextResponse.json({ error: 'You do not have access to this project' }, { status: 403 });
    }

    console.log(`[PROJECT DOCUMENTS] Access granted for user: ${userEmail} to project: ${projectId}`);


    // Get form data from the request
    console.log(`[PROJECT DOCUMENTS] Getting form data`);
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      console.log(`[PROJECT DOCUMENTS] No file provided`);
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    console.log(`[PROJECT DOCUMENTS] File received: ${file.name}, size: ${file.size}, type: ${file.type}`);

    // Generate a unique ID for the document
    const documentId = uuidv4();
    console.log(`[PROJECT DOCUMENTS] Generated document ID: ${documentId}`);

    // Get file extension
    const fileExtension = file.name.split('.').pop();

    // Hard-code the user email for file storage
    const storageUserEmail = "<EMAIL>";
    console.log(`[PROJECT DOCUMENTS] Using storage user email: ${storageUserEmail}`);

    // Create a storage path
    const filePath = `uploads/${storageUserEmail}/${documentId}.${fileExtension}`;
    console.log(`[PROJECT DOCUMENTS] Storage path: ${filePath}`);

    // Get file buffer
    console.log(`[PROJECT DOCUMENTS] Getting file buffer`);
    const buffer = await file.arrayBuffer();

    // Upload to Firebase Storage
    console.log(`[PROJECT DOCUMENTS] Uploading to Firebase Storage`);
    const bucket = adminStorage.bucket();
    const fileRef = bucket.file(filePath);

    await fileRef.save(Buffer.from(buffer), {
      metadata: {
        contentType: file.type,
        metadata: {
          originalName: file.name,
          uploadedBy: session.user.email,
          uploadedAt: new Date().toISOString()
        }
      }
    });
    console.log(`[PROJECT DOCUMENTS] File uploaded to Firebase Storage`);

    // Generate a download URL
    console.log(`[PROJECT DOCUMENTS] Generating download URL`);
    const [downloadUrl] = await fileRef.getSignedUrl({
      action: 'read',
      expires: '03-01-2500' // Long expiration
    });
    console.log(`[PROJECT DOCUMENTS] Download URL generated: ${downloadUrl.substring(0, 50)}...`);

    // Store metadata in the fixed user's files collection
    console.log(`[PROJECT DOCUMENTS] Storing metadata in Firestore users collection`);
    const category = formData.get('category') || 'Project Planner';
    console.log(`[PROJECT DOCUMENTS] Using category: ${category}`);

    await adminDb.collection('users').doc(storageUserEmail).collection('files').doc(documentId).set({
      name: file.name,
      size: file.size,
      type: file.type,
      category: category,
      namespace: documentId, // Important for Pinecone
      downloadUrl: downloadUrl,
      ref: filePath,
      createdAt: new Date(),
      createdBy: session.user.email, // Keep track of who actually uploaded it
      isImage: file.type.startsWith('image/'),
      projectId: projectId // Reference to the project
    });
    console.log(`[PROJECT DOCUMENTS] Metadata stored in users collection`);

    // Also store reference in project documents collection with all necessary fields
    console.log(`[PROJECT DOCUMENTS] Storing metadata in projects collection`);
    await adminDb.collection('projects').doc(projectId).collection('documents').doc(documentId).set({
      fileId: documentId,
      namespace: documentId, // Important for deleteDocumentAndChats API
      name: file.name,
      size: file.size,
      type: file.type,
      category: category,
      downloadUrl: downloadUrl,
      path: filePath,
      createdAt: new Date(),
      createdBy: session.user.email,
      isImage: file.type.startsWith('image/')
    });
    console.log(`[PROJECT DOCUMENTS] Metadata stored in projects collection`);

    // Process document by calling the processFile API
    console.log(`[PROJECT DOCUMENTS] Calling processFile API to generate embeddings`);
    const processFileUrl = `${req.nextUrl.origin}/api/processFile`;
    console.log(`[PROJECT DOCUMENTS] ProcessFile API URL: ${processFileUrl}`);

    // Get the session token to pass to the API
    const sessionCookie = req.cookies.get('next-auth.session-token')?.value ||
                          req.cookies.get('__Secure-next-auth.session-token')?.value;
    console.log(`[PROJECT DOCUMENTS] Session cookie available: ${!!sessionCookie}`);

    const processFilePayload = {
      docId: documentId,
      userId: storageUserEmail,
      category: category,
      fileName: file.name,
      fileType: file.type,
      fileUrl: downloadUrl,
      isImage: file.type.startsWith('image/'),
      projectId: projectId, // Pass projectId to ensure alignment
      userEmail: session.user.email // Pass the user email for authentication
    };
    console.log(`[PROJECT DOCUMENTS] ProcessFile payload: ${JSON.stringify({...processFilePayload, fileUrl: processFilePayload.fileUrl.substring(0, 50) + '...'})}`);

    const processResponse = await fetch(processFileUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Pass the session cookie if available
        ...(sessionCookie ? { 'Cookie': `next-auth.session-token=${sessionCookie}` } : {})
      },
      body: JSON.stringify(processFilePayload),
    });

    if (processResponse.ok) {
      console.log(`[PROJECT DOCUMENTS] Document processing successful`);
    } else {
      const errorText = await processResponse.text();
      console.log(`[PROJECT DOCUMENTS] Document processing failed: ${errorText}`);
      console.warn('Document uploaded but processing failed:', errorText);
    }

    console.log(`[PROJECT DOCUMENTS] Document upload and processing complete, returning success response`);
    return NextResponse.json({
      success: true,
      documentId,
      downloadUrl
    });
  } catch (error: any) {
    console.error('Error uploading document:', error);
    console.log(`[PROJECT DOCUMENTS] Error during document upload: ${error.message}`);
    return NextResponse.json(
      { error: `Failed to upload document: ${error.message}` },
      { status: 500 }
    );
  }
}




