'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User as FirebaseUser,
  GoogleAuthProvider,
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { auth } from '../../lib/firebase/config';
import { getUserById, getUsers, initializeUsers } from '../../lib/firebase/planner';
import { User } from '../../../admin/planner/types';

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  error: string | null;
  isAuthorized: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  checkAuthorization: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Initialize users if they don't exist
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeUsers();
      } catch (err) {
        console.error('Error initializing users:', err);
      }
    };

    initialize();
  }, []);

  // Listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      setFirebaseUser(firebaseUser);

      if (firebaseUser) {
        try {
          // Check if user exists in our database
          let userData = await getUserById(firebaseUser.uid);

          // If user doesn't exist in our database but is authenticated
          if (!userData) {
            // Check if the email matches our admin user
            const allUsers = await getUsers();
            const matchingUser = allUsers.find(u => u.email === firebaseUser.email);

            if (matchingUser) {
              userData = matchingUser;
            } else {
              // Default to first user if no match (for demo purposes)
              userData = allUsers[0] || null;
            }
          }

          setUser(userData);
        } catch (err) {
          console.error('Error fetching user data:', err);
          setError('Failed to load user data');
        }
      } else {
        setUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signInWithGoogle = async () => {
    setLoading(true);
    setError(null);

    try {
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
    } catch (err: any) {
      console.error('Google sign in error:', err);
      setError(err.message || 'Failed to sign in with Google');
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    setError(null);

    try {
      await firebaseSignOut(auth);
    } catch (err: any) {
      console.error('Sign out error:', err);
      setError(err.message || 'Failed to sign out');
    } finally {
      setLoading(false);
    }
  };

  const checkAuthorization = async (): Promise<boolean> => {
    if (!firebaseUser?.email) {
      console.log('No email found in firebaseUser');
      return false;
    }

    try {
      console.log('Checking authorization for:', firebaseUser.email);

      // System admin is always authorized
      if (firebaseUser.email === '<EMAIL>') {
        console.log('System admin detected in checkAuthorization');
        setIsAuthorized(true);
        return true;
      }

      // For now, all authenticated users are authorized
      setIsAuthorized(true);
      return true;
    } catch (err) {
      console.error('Error checking authorization:', err);
      setIsAuthorized(false);
      return false;
    }
  };

  const value = {
    user,
    firebaseUser,
    loading,
    error,
    isAuthorized,
    signInWithGoogle,
    signOut,
    checkAuthorization
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
