import { db } from "../../components/firebase/config";
import { collection, addDoc, updateDoc, doc, getDoc, serverTimestamp, setDoc } from "firebase/firestore";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { v4 as uuidv4 } from 'uuid';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from '@langchain/openai';
import { Pinecone } from '@pinecone-database/pinecone';

// Define interfaces for storage tool
export interface FileObject {
  data: Buffer | Blob | Uint8Array;
  name: string;
  path: string;
  metadata?: Record<string, any>;
}

export interface SaveContentWithFilesResult {
  docId: string;
  fileUrls: Record<string, string>;
}

export interface SaveContentChunksResult {
  parentId: string;
  chunkIds: string[];
  chunkCount: number;
}

export interface SavePdfToByteStoreResult {
  documentId: string;
  downloadUrl: string;
  totalChunks: number;
  success: boolean;
}

/**
 * Storage Tool for saving content and files to Firebase
 */
// Constants for text chunking
const CHUNK_SIZE = 1000;
const CHUNK_OVERLAP = 200;

// Helper function to clean metadata
function cleanMetadata(metadata: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};

  for (const [key, value] of Object.entries(metadata)) {
    if (value !== undefined && value !== null) {
      cleaned[key] = value;
    } else {
      cleaned[key] = '';
    }
  }

  return cleaned;
}

export class StorageTool {
  /**
   * Save content to Firestore
   * @param data - The data to save
   * @param collectionName - The collection to save to
   * @returns The document ID
   */
  async saveToFirestore(data: Record<string, any>, collectionName = "documents"): Promise<string> {
    try {
      // Add timestamp
      const dataWithTimestamp = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      // Save to Firestore
      const docRef = await addDoc(collection(db, collectionName), dataWithTimestamp);

      return docRef.id;
    } catch (error) {
      console.error("Error saving to Firestore:", error);
      throw error;
    }
  }

  /**
   * Update content in Firestore
   * @param docId - The document ID to update
   * @param data - The data to update
   * @param collectionName - The collection containing the document
   * @returns Promise<void>
   */
  async updateInFirestore(docId: string, data: Record<string, any>, collectionName = "documents"): Promise<void> {
    try {
      // Add timestamp
      const dataWithTimestamp = {
        ...data,
        updatedAt: serverTimestamp()
      };

      // Update in Firestore
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, dataWithTimestamp);
    } catch (error) {
      console.error("Error updating in Firestore:", error);
      throw error;
    }
  }

  /**
   * Save a file to Firebase Storage
   * @param fileData - The file data to save
   * @param path - The storage path
   * @param fileName - The file name
   * @param metadata - File metadata
   * @returns The download URL
   */
  async saveToStorage(
    fileData: Buffer | Blob | Uint8Array,
    path: string,
    fileName: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    try {
      const storage = getStorage();
      const fullPath = `${path}/${fileName}`;
      const storageRef = ref(storage, fullPath);

      // Upload file
      await uploadBytes(storageRef, fileData, metadata);

      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);

      return downloadURL;
    } catch (error) {
      console.error("Error saving to Storage:", error);
      throw error;
    }
  }

  /**
   * Save content and related files
   * @param content - The content to save
   * @param files - Array of file objects with data, name, and path
   * @param collectionName - The collection to save content to
   * @returns Result with document ID and file URLs
   */
  async saveContentWithFiles(
    content: Record<string, any>,
    files: FileObject[] = [],
    collectionName = "documents"
  ): Promise<SaveContentWithFilesResult> {
    try {
      // First save the content to get a document ID
      const docId = await this.saveToFirestore(content, collectionName);

      // Save each file and collect URLs
      const fileUrls: Record<string, string> = {};

      for (const file of files) {
        if (!file.data || !file.name || !file.path) {
          console.warn("Skipping invalid file:", file);
          continue;
        }

        // Use document ID in the path for organization
        const storagePath = `${file.path}/${docId}`;
        const downloadUrl = await this.saveToStorage(
          file.data,
          storagePath,
          file.name,
          file.metadata || {}
        );

        fileUrls[file.name] = downloadUrl;
      }

      // Update the document with file URLs if any were saved
      if (Object.keys(fileUrls).length > 0) {
        await this.updateInFirestore(
          docId,
          { fileUrls },
          collectionName
        );
      }

      return {
        docId,
        fileUrls
      };
    } catch (error) {
      console.error("Error saving content with files:", error);
      throw error;
    }
  }

  /**
   * Split content into chunks and save to Firestore
   * @param content - The content to split and save
   * @param metadata - Metadata for the content
   * @param chunkSize - Maximum size of each chunk
   * @param collectionName - The collection to save chunks to
   * @returns Result with parent document ID and chunk IDs
   */
  async saveContentChunks(
    content: string,
    metadata: Record<string, any> = {},
    chunkSize = 1000,
    collectionName = "content_chunks"
  ): Promise<SaveContentChunksResult> {
    try {
      if (!content) {
        throw new Error("Content is required");
      }

      // Create parent document
      const parentData = {
        ...metadata,
        contentLength: content.length,
        chunkCount: 0,
        chunkSize,
        isParent: true
      };

      const parentId = await this.saveToFirestore(parentData, collectionName);

      // Split content into chunks
      const chunks = this.splitContentIntoChunks(content, chunkSize);
      const chunkIds: string[] = [];

      // Save each chunk
      for (let i = 0; i < chunks.length; i++) {
        const chunkData = {
          content: chunks[i],
          parentId,
          chunkIndex: i,
          ...metadata,
          isChunk: true
        };

        const chunkId = await this.saveToFirestore(chunkData, collectionName);
        chunkIds.push(chunkId);
      }

      // Update parent with chunk count and IDs
      await this.updateInFirestore(
        parentId,
        {
          chunkCount: chunks.length,
          chunkIds
        },
        collectionName
      );

      return {
        parentId,
        chunkIds,
        chunkCount: chunks.length
      };
    } catch (error) {
      console.error("Error saving content chunks:", error);
      throw error;
    }
  }

  /**
   * Split content into chunks
   * @param content - The content to split
   * @param chunkSize - Maximum size of each chunk
   * @returns Array of content chunks
   * @private
   */
  private splitContentIntoChunks(content: string, chunkSize = 1000): string[] {
    if (!content || typeof content !== "string") {
      return [];
    }

    // Simple paragraph-based splitting
    const paragraphs = content.split("\n\n");
    const chunks: string[] = [];
    let currentChunk = "";

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > chunkSize) {
        // Add current chunk to results
        chunks.push(currentChunk.trim());

        // Start new chunk
        currentChunk = paragraph;
      } else {
        // Add paragraph to current chunk
        currentChunk += (currentChunk ? "\n\n" : "") + paragraph;
      }
    }

    // Add the last chunk if not empty
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  /**
   * Save a PDF to byteStoreCollection and files collection
   * @param pdfBuffer - The PDF buffer to save
   * @param title - The title of the PDF
   * @param content - The text content of the PDF
   * @param category - The category of the PDF (defaults to 'Marketing Agent Team')
   * @param metadata - Additional metadata for the PDF
   * @returns Promise<SavePdfToByteStoreResult> - Result of the operation
   */
  async savePdfToByteStore(
    pdfBuffer: Buffer,
    title: string,
    content: string,
    category: string = 'Marketing Agent Team',
    metadata: Record<string, any> = {}
  ): Promise<SavePdfToByteStoreResult> {
    try {
      // Generate a unique ID using UUIDv4
      const documentId = uuidv4();
      const fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_${documentId}.pdf`;
      const sysAdmin = process.env.SYS_ADMIN || '<EMAIL>';
      const byteStoreCollection = `users/${sysAdmin}/byteStoreCollection`;

      // 1. Upload PDF to Firebase Storage
      const storagePath = `marketing_pdfs/${fileName}`;
      const storage = getStorage();
      const storageRef = ref(storage, storagePath);

      await uploadBytes(storageRef, pdfBuffer, {
        contentType: 'application/pdf',
        customMetadata: {
          title,
          category,
          generatedBy: 'MarketingAgent',
          ...metadata
        }
      });

      // Get download URL
      const downloadUrl = await getDownloadURL(storageRef);

      // 2. Store metadata in files collection
      const filesCollection = `users/${sysAdmin}/files`;
      await setDoc(doc(db, filesCollection, documentId), {
        category,
        createdAt: new Date().toISOString(),
        downloadUrl,
        name: fileName,
        namespace: documentId,
        ref: `uploads/${sysAdmin}/${documentId}`,
        size: pdfBuffer.length,
        type: 'application/pdf',
        title,
        generatedBy: 'MarketingAgent',
        ...metadata
      });

      // 3. Process content for byteStoreCollection
      // Initialize the text splitter for chunking
      const textSplitter = new RecursiveCharacterTextSplitter({
        chunkSize: CHUNK_SIZE,
        chunkOverlap: CHUNK_OVERLAP,
        separators: ["\n\n", "\n", ". ", " ", ""]
      });

      // Split the content into chunks
      const textChunks = await textSplitter.createDocuments([content]);

      // Initialize OpenAI embeddings
      const embeddings = new OpenAIEmbeddings({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Initialize Pinecone index
      const pinecone = new Pinecone();
      const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX || 'ikedia');

      // Process each chunk
      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];
        const chunkId = `${documentId}_${i + 1}`;

        // Create metadata for this chunk
        const chunkMetadata = cleanMetadata({
          doc_id: documentId,
          chunk_id: chunkId,
          document_title: title,
          category,
          file_type: 'application/pdf',
          position: i + 1,
          total_chunks: textChunks.length,
          is_summary: i === 0, // First chunk is considered the summary
          source_url: downloadUrl,
          processed_at: new Date().toISOString(),
          ...metadata
        });

        // Store the raw chunk in Firestore
        await setDoc(doc(db, byteStoreCollection, chunkId), {
          content: chunk.pageContent,
          metadata: chunkMetadata
        });

        // Generate embedding for this chunk
        const embedding = await embeddings.embedQuery(chunk.pageContent);

        // Store the embedding in Pinecone
        await pineconeIndex.namespace(documentId).upsert([
          {
            id: chunkId,
            values: embedding,
            metadata: chunkMetadata
          }
        ]);
      }

      return {
        documentId,
        downloadUrl,
        totalChunks: textChunks.length,
        success: true
      };
    } catch (error) {
      console.error('Error saving PDF to byteStore:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const storageTool = new StorageTool();
