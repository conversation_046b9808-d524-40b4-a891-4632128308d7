/**
 * DateTime Tool Test
 *
 * This file demonstrates how to use the DateTime tool.
 * You can run this test with:
 *
 * ```
 * npx ts-node lib/tools/tests/datetime-tool.test.ts
 * ```
 */

import { dateTimeTool } from '../dateTimeTool';

async function testDateTimeTool() {
  console.log('=== Testing DateTime Tool ===\n');

  // Test getCurrentDateTime with different formats
  console.log('Testing getCurrentDateTime with different formats:');
  console.log(`Full format: ${dateTimeTool.getCurrentDateTime({ format: 'full' })}`);
  console.log(`Long format: ${dateTimeTool.getCurrentDateTime({ format: 'long' })}`);
  console.log(`Medium format: ${dateTimeTool.getCurrentDateTime({ format: 'medium' })}`);
  console.log(`Short format: ${dateTimeTool.getCurrentDateTime({ format: 'short' })}`);
  console.log();

  // Test with custom format
  console.log('Testing getCurrentDateTime with custom format:');
  console.log(`Custom format (YYYY-MM-DD): ${dateTimeTool.getCurrentDateTime({ format: 'YYYY-MM-DD' })}`);
  console.log(`Custom format with time: ${dateTimeTool.getCurrentDateTime({ format: 'YYYY-MM-DD', includeTime: true })}`);
  console.log();

  // Test with day of week
  console.log('Testing getCurrentDateTime with day of week:');
  console.log(`With day of week: ${dateTimeTool.getCurrentDateTime({ format: 'medium', includeDayOfWeek: true })}`);
  console.log();

  // Test with 24-hour format
  console.log('Testing getCurrentDateTime with 24-hour format:');
  console.log(`24-hour format: ${dateTimeTool.getCurrentDateTime({ format: 'medium', includeTime: true, use24HourFormat: true })}`);
  console.log(`12-hour format: ${dateTimeTool.getCurrentDateTime({ format: 'medium', includeTime: true, use24HourFormat: false })}`);
  console.log();

  // Test getCurrentDate
  console.log('Testing getCurrentDate:');
  console.log(`Default format: ${dateTimeTool.getCurrentDate()}`);
  console.log(`Full format: ${dateTimeTool.getCurrentDate({ format: 'full' })}`);
  console.log(`With day of week: ${dateTimeTool.getCurrentDate({ format: 'medium', includeDayOfWeek: true })}`);
  console.log();

  // Test getCurrentTime
  console.log('Testing getCurrentTime:');
  console.log(`Default format: ${dateTimeTool.getCurrentTime()}`);
  console.log(`24-hour format: ${dateTimeTool.getCurrentTime({ use24HourFormat: true })}`);
  console.log();

  console.log('All tests completed successfully!');
}

// Run the test
testDateTimeTool();
