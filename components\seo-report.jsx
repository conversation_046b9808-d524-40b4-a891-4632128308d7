'use client';

import React, { useState } from 'react';
import {
  Search,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Globe,
  Smartphone,
  Zap,
  FileText,
  RefreshCw,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  Info
} from 'lucide-react';
// No need for additional imports, useState is already imported above

/**
 * SEO Report component for analyzing and displaying SEO metrics
 */
export default function SeoReport() {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [report, setReport] = useState(null);
  const [expandedSections, setExpandedSections] = useState({
    metadata: true,
    performance: true,
    content: true,
    mobile: true,
    recommendations: true,
    issues: true,
  });

  // Handle URL analysis
  const handleAnalyzeUrl = async (e) => {
    e.preventDefault();

    if (!url) {
      setError('Please enter a URL to analyze');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Use the dedicated SEO API route
      const response = await fetch('/api/seo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      const seoReport = await response.json();

      if (seoReport.success) {
        setReport(seoReport);
      } else {
        setError(seoReport.error || 'Failed to generate SEO report');
      }
    } catch (err) {
      setError(err.message || 'An error occurred during analysis');
    } finally {
      setLoading(false);
    }
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections({
      ...expandedSections,
      [section]: !expandedSections[section],
    });
  };

  // Render section header
  const renderSectionHeader = (title, icon, section) => (
    <div
      className="flex items-center justify-between cursor-pointer p-4 bg-zinc-800 rounded-t-lg border-b border-zinc-700"
      onClick={() => toggleSection(section)}
    >
      <div className="flex items-center">
        {icon}
        <h3 className="text-lg font-semibold ml-2 text-white">{title}</h3>
      </div>
      {expandedSections[section] ? (
        <ChevronUp className="text-zinc-400" size={20} />
      ) : (
        <ChevronDown className="text-zinc-400" size={20} />
      )}
    </div>
  );

  return (
    <div className="seo-report space-y-6">
      {/* URL input form */}
      <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
          <Search className="mr-2 text-blue-400" size={20} />
          SEO Analyzer
        </h2>

        <form onSubmit={handleAnalyzeUrl} className="mb-4">
          <div className="flex flex-col md:flex-row gap-3">
            <input
              type="url"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              placeholder="https://example.com"
              className="flex-1 px-4 py-2 bg-zinc-800 border border-zinc-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-zinc-400"
              disabled={loading}
            />
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-900 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <RefreshCw className="inline-block mr-2 animate-spin" size={16} />
                  Analyzing...
                </>
              ) : (
                'Analyze URL'
              )}
            </button>
          </div>
        </form>

        {error && (
          <div className="p-4 bg-red-900/20 border border-red-800 rounded-md text-red-200">
            <div className="flex items-start">
              <AlertTriangle className="mr-2 flex-shrink-0 mt-0.5" size={16} />
              <p>{error}</p>
            </div>
          </div>
        )}
      </div>

      {/* SEO Report Results */}
      {report && report.analysis && (
        <div className="space-y-4">
          {/* Overview */}
          <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">SEO Report Overview</h2>
              <a
                href={report.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 flex items-center text-sm"
              >
                <ExternalLink size={14} className="mr-1" />
                Visit URL
              </a>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {/* Issues summary */}
              <div className="bg-zinc-800 p-4 rounded-lg border border-zinc-700">
                <h3 className="text-sm font-medium text-zinc-400 mb-2">Issues Found</h3>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <AlertTriangle
                      className={
                        report.analysis.issues.filter(i => i.type === 'critical').length > 0
                          ? "text-red-500 mr-2"
                          : "text-amber-500 mr-2"
                      }
                      size={20}
                    />
                    <span className="text-xl font-bold text-white">
                      {report.analysis.issues.length}
                    </span>
                  </div>
                  <div>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-900/30 text-red-200 mr-1">
                      {report.analysis.issues.filter(i => i.type === 'critical').length} Critical
                    </span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-amber-900/30 text-amber-200">
                      {report.analysis.issues.filter(i => i.type === 'warning').length} Warnings
                    </span>
                  </div>
                </div>
              </div>

              {/* Performance */}
              <div className="bg-zinc-800 p-4 rounded-lg border border-zinc-700">
                <h3 className="text-sm font-medium text-zinc-400 mb-2">Page Performance</h3>
                <div className="flex items-center">
                  <Zap
                    className={
                      report.analysis.performance.loadTime < 3000
                        ? "text-green-500 mr-2"
                        : report.analysis.performance.loadTime < 5000
                        ? "text-amber-500 mr-2"
                        : "text-red-500 mr-2"
                    }
                    size={20}
                  />
                  <div>
                    <p className="text-xl font-bold text-white">
                      {(report.analysis.performance.loadTime / 1000).toFixed(2)}s
                    </p>
                    <p className="text-xs text-zinc-400">Load Time</p>
                  </div>
                </div>
              </div>

              {/* Mobile Friendliness */}
              <div className="bg-zinc-800 p-4 rounded-lg border border-zinc-700">
                <h3 className="text-sm font-medium text-zinc-400 mb-2">Mobile Friendliness</h3>
                <div className="flex items-center">
                  {report.analysis.mobileFriendliness.isMobileFriendly ? (
                    <CheckCircle className="text-green-500 mr-2" size={20} />
                  ) : (
                    <XCircle className="text-red-500 mr-2" size={20} />
                  )}
                  <div>
                    <p className="text-xl font-bold text-white">
                      {report.analysis.mobileFriendliness.isMobileFriendly ? 'Good' : 'Poor'}
                    </p>
                    <p className="text-xs text-zinc-400">Mobile Experience</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Page metadata */}
            <div className="bg-zinc-800 p-4 rounded-lg border border-zinc-700">
              <h3 className="text-sm font-medium text-zinc-400 mb-2">Page Metadata</h3>
              <div className="space-y-2">
                <div>
                  <p className="text-xs text-zinc-500">Title</p>
                  <p className="text-sm text-zinc-200 break-words">
                    {report.analysis.metadata.title || 'Missing title'}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-zinc-500">Meta Description</p>
                  <p className="text-sm text-zinc-200 break-words">
                    {report.analysis.metadata.metaDescription || 'Missing meta description'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Recommendations */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700">
            {renderSectionHeader(
              'Recommendations',
              <CheckCircle className="text-emerald-500" size={20} />,
              'recommendations'
            )}

            {expandedSections.recommendations && (
              <div className="p-4">
                {report.analysis.recommendations && report.analysis.recommendations.length > 0 ? (
                  <ul className="space-y-3">
                    {report.analysis.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="text-emerald-500 mr-2 flex-shrink-0 mt-0.5" size={16} />
                        <p className="text-zinc-200">{rec.recommendation}</p>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-zinc-400 text-center py-2">No recommendations available</p>
                )}
              </div>
            )}
          </div>

          {/* Issues */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700">
            {renderSectionHeader(
              'Issues',
              <AlertTriangle className="text-amber-500" size={20} />,
              'issues'
            )}

            {expandedSections.issues && (
              <div className="p-4">
                {report.analysis.issues && report.analysis.issues.length > 0 ? (
                  <ul className="space-y-3">
                    {report.analysis.issues.map((issue, index) => (
                      <li key={index} className="flex items-start">
                        {issue.type === 'critical' ? (
                          <XCircle className="text-red-500 mr-2 flex-shrink-0 mt-0.5" size={16} />
                        ) : (
                          <AlertTriangle className="text-amber-500 mr-2 flex-shrink-0 mt-0.5" size={16} />
                        )}
                        <div>
                          <p className="text-zinc-200">{issue.message}</p>
                          <p className="text-xs text-zinc-500">Category: {issue.category}</p>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-zinc-400 text-center py-2">No issues found</p>
                )}
              </div>
            )}
          </div>

          {/* Content Quality */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700">
            {renderSectionHeader(
              'Content Quality',
              <FileText className="text-blue-500" size={20} />,
              'content'
            )}

            {expandedSections.content && (
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <p className="text-xs text-zinc-500">Word Count</p>
                    <p className="text-xl font-bold text-white">
                      {report.analysis.contentQuality.wordCount.toLocaleString()}
                    </p>
                    <p className="text-xs text-zinc-400">
                      Content Depth: {report.analysis.contentQuality.contentDepth}
                    </p>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <p className="text-xs text-zinc-500">Readability Score</p>
                    <p className="text-xl font-bold text-white">
                      {report.analysis.contentQuality.readabilityScore.toFixed(1)}
                    </p>
                    <p className="text-xs text-zinc-400">
                      {report.analysis.contentQuality.readabilityScore > 80
                        ? 'Easy to read'
                        : report.analysis.contentQuality.readabilityScore > 60
                        ? 'Moderately readable'
                        : 'Difficult to read'}
                    </p>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <p className="text-xs text-zinc-500">Paragraphs</p>
                    <p className="text-xl font-bold text-white">
                      {report.analysis.contentQuality.paragraphCount}
                    </p>
                    <p className="text-xs text-zinc-400">
                      Avg Length: {report.analysis.contentQuality.avgParagraphLength.toFixed(1)} words
                    </p>
                  </div>
                </div>

                {/* Keyword Analysis */}
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-zinc-300 mb-2">Keyword Analysis</h4>

                  {Object.keys(report.analysis.keywordAnalysis).length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-zinc-800">
                        <thead>
                          <tr>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              Keyword
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              In Title
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              In Meta
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              In H1
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              Content
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              Density
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-zinc-400 uppercase tracking-wider">
                              Optimization
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-zinc-800">
                          {Object.entries(report.analysis.keywordAnalysis).map(([keyword, analysis], index) => (
                            <tr key={index} className="hover:bg-zinc-800/50">
                              <td className="px-3 py-2 text-sm text-zinc-300">
                                {keyword}
                              </td>
                              <td className="px-3 py-2 text-sm">
                                {analysis.occurrences.title > 0 ? (
                                  <CheckCircle className="text-green-500" size={16} />
                                ) : (
                                  <XCircle className="text-red-500" size={16} />
                                )}
                              </td>
                              <td className="px-3 py-2 text-sm">
                                {analysis.occurrences.metaDescription > 0 ? (
                                  <CheckCircle className="text-green-500" size={16} />
                                ) : (
                                  <XCircle className="text-red-500" size={16} />
                                )}
                              </td>
                              <td className="px-3 py-2 text-sm">
                                {analysis.occurrences.h1 > 0 ? (
                                  <CheckCircle className="text-green-500" size={16} />
                                ) : (
                                  <XCircle className="text-red-500" size={16} />
                                )}
                              </td>
                              <td className="px-3 py-2 text-sm text-zinc-300">
                                {analysis.occurrences.content}
                              </td>
                              <td className="px-3 py-2 text-sm text-zinc-300">
                                {analysis.density.toFixed(2)}%
                              </td>
                              <td className="px-3 py-2 text-sm">
                                <span className={`px-2 py-0.5 rounded-full text-xs ${
                                  analysis.optimization === 'excellent'
                                    ? 'bg-green-900/30 text-green-200'
                                    : analysis.optimization === 'good'
                                    ? 'bg-blue-900/30 text-blue-200'
                                    : analysis.optimization === 'fair'
                                    ? 'bg-amber-900/30 text-amber-200'
                                    : 'bg-red-900/30 text-red-200'
                                }`}>
                                  {analysis.optimization}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <p className="text-zinc-400 text-center py-2">No keyword data available</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Performance Details */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700">
            {renderSectionHeader(
              'Performance',
              <Zap className="text-amber-500" size={20} />,
              'performance'
            )}

            {expandedSections.performance && (
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <p className="text-xs text-zinc-500">Load Time</p>
                    <p className="text-xl font-bold text-white">
                      {(report.analysis.performance.loadTime / 1000).toFixed(2)}s
                    </p>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <p className="text-xs text-zinc-500">First Contentful Paint</p>
                    <p className="text-xl font-bold text-white">
                      {report.analysis.performance.metrics.firstContentfulPaint
                        ? (report.analysis.performance.metrics.firstContentfulPaint / 1000).toFixed(2) + 's'
                        : 'N/A'}
                    </p>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <p className="text-xs text-zinc-500">DOM Content Loaded</p>
                    <p className="text-xl font-bold text-white">
                      {report.analysis.performance.metrics.domContentLoaded
                        ? (report.analysis.performance.metrics.domContentLoaded / 1000).toFixed(2) + 's'
                        : 'N/A'}
                    </p>
                  </div>
                </div>

                <div className="mt-4 bg-zinc-800 p-3 rounded-lg">
                  <p className="text-xs text-zinc-500">Page Size</p>
                  <p className="text-xl font-bold text-white">
                    {(report.analysis.performance.size / 1024).toFixed(0)} KB
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Mobile Friendliness */}
          <div className="bg-zinc-900 rounded-lg border border-zinc-700">
            {renderSectionHeader(
              'Mobile Friendliness',
              <Smartphone className="text-purple-500" size={20} />,
              'mobile'
            )}

            {expandedSections.mobile && (
              <div className="p-4">
                <div className="flex items-center mb-4">
                  {report.analysis.mobileFriendliness.isMobileFriendly ? (
                    <div className="flex items-center bg-green-900/20 text-green-200 px-3 py-2 rounded-md">
                      <CheckCircle className="mr-2" size={20} />
                      <span>This page is mobile-friendly</span>
                    </div>
                  ) : (
                    <div className="flex items-center bg-red-900/20 text-red-200 px-3 py-2 rounded-md">
                      <XCircle className="mr-2" size={20} />
                      <span>This page is not mobile-friendly</span>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-zinc-300">Viewport Meta Tag</p>
                      {report.analysis.mobileFriendliness.hasViewportMeta ? (
                        <CheckCircle className="text-green-500" size={16} />
                      ) : (
                        <XCircle className="text-red-500" size={16} />
                      )}
                    </div>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-zinc-300">Horizontal Scrolling</p>
                      {!report.analysis.mobileFriendliness.hasHorizontalScroll ? (
                        <CheckCircle className="text-green-500" size={16} />
                      ) : (
                        <XCircle className="text-red-500" size={16} />
                      )}
                    </div>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-zinc-300">Small Tap Targets</p>
                        <p className="text-xs text-zinc-500">
                          {report.analysis.mobileFriendliness.smallTapTargets} elements
                        </p>
                      </div>
                      {report.analysis.mobileFriendliness.smallTapTargets < 3 ? (
                        <CheckCircle className="text-green-500" size={16} />
                      ) : (
                        <XCircle className="text-red-500" size={16} />
                      )}
                    </div>
                  </div>

                  <div className="bg-zinc-800 p-3 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-zinc-300">Small Font Elements</p>
                        <p className="text-xs text-zinc-500">
                          {report.analysis.mobileFriendliness.smallFontElements} elements
                        </p>
                      </div>
                      {report.analysis.mobileFriendliness.smallFontElements < 3 ? (
                        <CheckCircle className="text-green-500" size={16} />
                      ) : (
                        <XCircle className="text-red-500" size={16} />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
