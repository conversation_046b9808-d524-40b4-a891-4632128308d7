'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import useUserProfile from '../hooks/useUserProfile';
import Image from 'next/image';
import { RefreshCw, AlertCircle, CheckCircle, User } from 'lucide-react';

interface UserProfileCardProps {
  showDebug?: boolean;
}

export default function UserProfileCard({ showDebug = false }: UserProfileCardProps) {
  const { data: session } = useSession();
  const { profile, loading, error, refresh, lastFetched } = useUserProfile();

  if (!session) {
    return (
      <div className="p-4 bg-gray-800 rounded-lg shadow-md">
        <div className="flex items-center justify-center">
          <User className="w-6 h-6 text-gray-400 mr-2" />
          <p className="text-gray-400">Not signed in</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-gray-800 rounded-lg shadow-md">
      {/* Header with refresh button */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-white">User Profile</h2>
        <button
          onClick={() => refresh()}
          disabled={loading}
          className="p-1 rounded-full hover:bg-gray-700 transition-colors"
          title="Refresh profile"
        >
          <RefreshCw className={`w-5 h-5 text-gray-400 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Error message */}
      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-md flex items-start">
          <AlertCircle className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
          <p className="text-sm text-red-300">{error.message}</p>
        </div>
      )}

      {/* Loading state */}
      {loading && !profile && (
        <div className="flex justify-center items-center py-8">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Profile content */}
      {profile && (
        <div className="flex flex-col items-center">
          {/* Profile image */}
          <div className="relative mb-4">
            <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
              {profile.avatar ? (
                <Image
                  src={profile.avatar}
                  alt={profile.name}
                  width={80}
                  height={80}
                  className="object-cover w-full h-full"
                  referrerPolicy="no-referrer"
                  onError={(e) => {
                    // Fallback to initials if image fails to load
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.parentElement!.dataset.initials = profile.name.charAt(0).toUpperCase();
                  }}
                />
              ) : (
                <span className="text-2xl font-medium text-gray-300">
                  {profile.name.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            
            {/* Sync status indicator */}
            {session?.user?.image && profile.avatar === session.user.image && (
              <div className="absolute -bottom-1 -right-1 bg-green-500 rounded-full p-1" title="Profile image synced">
                <CheckCircle className="w-4 h-4 text-white" />
              </div>
            )}
          </div>

          {/* User info */}
          <h3 className="text-xl font-medium text-white mb-1">{profile.name}</h3>
          <p className="text-gray-400 mb-2">{profile.email}</p>
          <div className="flex items-center mb-3">
            <span className="px-2 py-1 bg-purple-900 rounded-full text-xs text-purple-300 font-medium">
              {profile.role}
            </span>
            {profile.isAuthorized && (
              <span className="ml-2 px-2 py-1 bg-green-900 rounded-full text-xs text-green-300 font-medium">
                Authorized
              </span>
            )}
          </div>
          <p className="text-sm text-gray-500">
            Availability: {profile.availability}
          </p>
        </div>
      )}

      {/* Debug information */}
      {showDebug && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <h3 className="text-sm font-medium text-gray-400 mb-2">Debug Info</h3>
          <div className="bg-gray-900 p-3 rounded-md text-xs font-mono text-gray-400 overflow-x-auto">
            <p>Session Email: {session?.user?.email || 'none'}</p>
            <p>Session Image: {session?.user?.image || 'none'}</p>
            <p>Profile Avatar: {profile?.avatar || 'none'}</p>
            <p>Last Fetched: {lastFetched?.toLocaleString() || 'never'}</p>
            <p>Image Synced: {profile?.avatar === session?.user?.image ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
}
