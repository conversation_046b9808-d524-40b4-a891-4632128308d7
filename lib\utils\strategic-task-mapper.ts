/**
 * Strategic Task Mapper Utility
 * 
 * Centralized mapping logic for Strategic Director Agent task management.
 * This module contains all mapping functions for teams, categories, priorities, and statuses.
 */

import { TaskStatus, TaskPriority } from '../../admin/planner/types';

// Type definitions for StrategicTask (extracted from StrategicDirectorAgent)
export type StrategicTaskCategory = 'Market Intelligence' | 'Product Analysis' | 'Customer Intelligence' | 'Marketing Infrastructure' | 'Performance Metrics' | 'Customer Validation' | 'Strategic Planning' | 'Implementation' | 'Research' | 'Content Creation';
export type StrategicTaskPriority = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
export type StrategicTaskStatus = 'IDENTIFIED' | 'ASSIGNED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'BLOCKED' | 'CANCELLED';
export type StrategicTeamName = 'Research Team' | 'Business Analysis Team' | 'Marketing Team' | 'Sales Team' | 'Software Design Team' | 'Content Team' | 'Strategic Director';

/**
 * Strategic Task Mapper Class
 * Contains all mapping logic for strategic task management
 */
export class StrategicTaskMapper {
  
  /**
   * Normalize team names to standard format
   */
  static normalizeTeamName(teamName: string): string {
    const normalized = teamName.trim();
    const lowerName = normalized.toLowerCase();

    if (lowerName.includes('research')) return 'Research Team';
    if (lowerName.includes('marketing')) return 'Marketing Team';
    if (lowerName.includes('business') || lowerName.includes('analysis')) return 'Business Analysis Team';
    if (lowerName.includes('sales')) return 'Sales Team';
    if (lowerName.includes('software') || lowerName.includes('design')) return 'Software Design Team';
    if (lowerName.includes('strategic') || lowerName.includes('director')) return 'Strategic Director';
    if (lowerName.includes('content')) return 'Content Team';

    return normalized; // Return as-is if no match
  }

  /**
   * Map team names to proper task categories
   */
  static mapTeamToCategory(teamName: string): StrategicTaskCategory {
    const lowerTeam = teamName.toLowerCase();

    // Map to proper task categories that match the system
    if (lowerTeam.includes('research')) return 'Market Intelligence';
    if (lowerTeam.includes('business') || lowerTeam.includes('analysis')) return 'Product Analysis';
    if (lowerTeam.includes('marketing')) return 'Marketing Infrastructure';
    if (lowerTeam.includes('sales')) return 'Customer Validation';
    if (lowerTeam.includes('strategic') || lowerTeam.includes('director')) return 'Strategic Planning';
    if (lowerTeam.includes('software') || lowerTeam.includes('design')) return 'Implementation';
    if (lowerTeam.includes('content')) return 'Content Creation';

    return 'Market Intelligence'; // Default to Market Intelligence instead of generic Research
  }

  /**
   * Map priority text to StrategicTask priority
   */
  static mapPriorityText(priorityText: string): StrategicTaskPriority {
    const lowerPriority = priorityText.toLowerCase();

    if (lowerPriority.includes('critical') || lowerPriority.includes('urgent')) return 'CRITICAL';
    if (lowerPriority.includes('high')) return 'HIGH';
    if (lowerPriority.includes('medium') || lowerPriority.includes('moderate')) return 'MEDIUM';
    if (lowerPriority.includes('low')) return 'LOW';

    return 'MEDIUM'; // Default
  }

  /**
   * Map task category to valid StrategicTask category
   */
  static mapTaskCategory(category: string): StrategicTaskCategory {
    const lowerCategory = category.toLowerCase();
    
    if (lowerCategory.includes('strategic') || lowerCategory.includes('planning')) return 'Strategic Planning';
    if (lowerCategory.includes('implementation')) return 'Implementation';
    if (lowerCategory.includes('market') || lowerCategory.includes('intelligence')) return 'Market Intelligence';
    if (lowerCategory.includes('product') || lowerCategory.includes('analysis')) return 'Product Analysis';
    if (lowerCategory.includes('customer') && lowerCategory.includes('intelligence')) return 'Customer Intelligence';
    if (lowerCategory.includes('marketing') || lowerCategory.includes('infrastructure')) return 'Marketing Infrastructure';
    if (lowerCategory.includes('performance') || lowerCategory.includes('metrics')) return 'Performance Metrics';
    if (lowerCategory.includes('validation')) return 'Customer Validation';
    if (lowerCategory.includes('research')) return 'Research';
    if (lowerCategory.includes('content')) return 'Content Creation';
    
    return 'Strategic Planning'; // Default
  }

  /**
   * Map team name to user IDs - Always assign to Admin User
   */
  static mapTeamToUserIds(teamName: string): string[] {
    // All tasks should be assigned to Admin User as requested
    console.log(`StrategicTaskMapper: Mapping team "${teamName}" to Admin User`);
    return ['<EMAIL>']; // Always assign to admin user (matches pmoProjectsTaskAgent)
  }

  /**
   * Map information gap priority to task priority
   */
  static mapPriorityToTaskPriority(gapPriority: 'HIGH' | 'MEDIUM' | 'LOW'): 'High' | 'Medium' | 'Low' {
    const priorityMap: Record<string, 'High' | 'Medium' | 'Low'> = {
      'HIGH': 'High',
      'MEDIUM': 'Medium',
      'LOW': 'Low'
    };

    return priorityMap[gapPriority] || 'Medium';
  }

  /**
   * Map StrategicTask status to standard TaskStatus
   */
  static mapStrategicStatusToTaskStatus(strategicStatus: StrategicTaskStatus): TaskStatus {
    const statusMap: Record<StrategicTaskStatus, TaskStatus> = {
      'IDENTIFIED': 'Not Started',
      'ASSIGNED': 'Not Started',
      'IN_PROGRESS': 'In Progress',
      'REVIEW': 'Reviewed',
      'COMPLETED': 'Complete',
      'BLOCKED': 'Not Started',
      'CANCELLED': 'Not Started'
    };

    return statusMap[strategicStatus] || 'Not Started';
  }

  /**
   * Map StrategicTask priority to standard TaskPriority
   */
  static mapStrategicPriorityToTaskPriority(strategicPriority: StrategicTaskPriority): TaskPriority {
    const priorityMap: Record<StrategicTaskPriority, TaskPriority> = {
      'CRITICAL': 'Critical',
      'HIGH': 'High',
      'MEDIUM': 'Medium',
      'LOW': 'Low'
    };

    return priorityMap[strategicPriority] || 'Medium';
  }

  /**
   * Get emoji for team type
   */
  static getTeamEmoji(teamName: string): string {
    const lowerTeam = teamName.toLowerCase();

    if (lowerTeam.includes('research')) return '🔍';
    if (lowerTeam.includes('business') || lowerTeam.includes('analysis')) return '📊';
    if (lowerTeam.includes('sales')) return '💼';
    if (lowerTeam.includes('marketing')) return '📈';
    if (lowerTeam.includes('software') || lowerTeam.includes('design')) return '💻';
    if (lowerTeam.includes('content')) return '📝';
    if (lowerTeam.includes('strategic')) return '🎯';

    return '👥'; // Default team emoji
  }
}
