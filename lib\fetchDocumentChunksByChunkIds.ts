// fetchDocumentChunksByChunkIds.ts

import { FirestoreStore } from "lib/FirestoreStore"; // Adjust the path as necessary
import { Document } from "langchain/document";

/**
 * Fetches document chunks from Firestore based on the provided chunk IDs.
 * @param chunkIds - An array of chunk IDs.
 * @param firestoreStore - The initialized FirestoreStore instance.
 * @returns An array of Document instances.
 */
export async function fetchDocumentChunksByChunkIds(
  chunkIds: string[],
  firestoreStore: FirestoreStore
): Promise<Document[]> {
  const allChunks: Document[] = [];

  // Fetch all chunks in parallel
  const chunkFetchPromises = chunkIds.map(async (chunkId) => {
    try {
      const chunk = await firestoreStore.mget([chunkId]);
      if (chunk && chunk.length > 0 && chunk[0] !== undefined) {
        allChunks.push(chunk[0]);
      } else {
        console.warn(`Chunk not found for ID: ${chunkId}`);
      }
    } catch (error) {
      console.error(`Error fetching chunk for ID: ${chunkId}`, error);
    }
  });

  // Wait for all fetch operations to complete
  await Promise.all(chunkFetchPromises);

  // Return all collected chunks
  return allChunks;
}
