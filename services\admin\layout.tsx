'use client';

import React, { ReactNode, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  BarChart2,
  Menu,
  X,
  LogOut
} from 'lucide-react';
import { useState } from 'react';
import { useAuth } from '../../app/context/AuthContext';

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { user, loading, signOut } = useAuth();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user && pathname !== '/services/admin/login') {
      router.push('/services/admin/login');
    }
  }, [user, loading, pathname, router]);

  const navItems = [
    { name: 'Dashboard', href: '/services/admin', icon: <LayoutDashboard className="w-5 h-5" /> },
    { name: 'Project Planner', href: '/services/admin/planner', icon: <Calendar className="w-5 h-5" /> },
    { name: 'Team', href: '/services/admin/team', icon: <Users className="w-5 h-5" /> },
    { name: 'Analytics', href: '/services/admin/analytics', icon: <BarChart2 className="w-5 h-5" /> },
    { name: 'Settings', href: '/services/admin/settings', icon: <Settings className="w-5 h-5" /> },
  ];

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/services/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  // If not logged in and not on login page, don't render anything (will redirect)
  if (!user && pathname !== '/services/admin/login') {
    return null;
  }

  // Don't show layout on login page
  if (pathname === '/services/admin/login') {
    return <>{children}</>;
  }

  return (
    <div className="flex h-screen bg-gray-900 text-gray-100">
      {/* Mobile sidebar toggle */}
      <div className="fixed top-0 left-0 z-40 md:hidden p-4">
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md bg-gray-800 shadow-md text-gray-200"
        >
          {isSidebarOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-center h-16 border-b border-gray-700">
            <h1 className="text-xl font-bold text-purple-400">Admin Panel</h1>
          </div>

          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navItems.map((item) => {
              const isActive = pathname === item.href ||
                (item.href !== '/services/admin' && pathname?.startsWith(item.href));

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                    isActive
                      ? 'bg-purple-900/50 text-purple-300'
                      : 'text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  <span className={`${isActive ? 'text-purple-300' : 'text-gray-400'}`}>
                    {item.icon}
                  </span>
                  <span className="ml-3 font-medium">{item.name}</span>
                </Link>
              );
            })}
          </nav>

          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-900 rounded-full flex items-center justify-center text-purple-300">
                  <span className="font-medium">{user?.name?.charAt(0) || 'A'}</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-200">{user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-400">{user?.email || '<EMAIL>'}</p>
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 rounded-full hover:bg-gray-700 text-gray-400 hover:text-gray-200"
                title="Logout"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 md:ml-64">
        <main className="p-4 md:p-6 min-h-screen">
          {children}
        </main>
      </div>

      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black bg-opacity-70 md:hidden"
          onClick={toggleSidebar}
        ></div>
      )}
    </div>
  );
}
