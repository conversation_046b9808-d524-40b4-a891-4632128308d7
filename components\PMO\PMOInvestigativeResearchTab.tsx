'use client';

import React, { useState, useEffect } from 'react';
import { Search, FileText, Users, Clock, TrendingUp, ChevronRight, Loader2, AlertCircle } from "lucide-react";
import { Button } from '../ui/button';
import { useAuth } from '../../app/context/AuthContext';
import InvestigativeResearchInterface from './InvestigativeResearchInterface';
import PMOInvestigativeResearchCard from './PMOInvestigativeResearchCard';

interface PMOInvestigativeResearchTabProps {
  onInvestigationComplete?: (result: any) => void;
}

const PMOInvestigativeResearchTab: React.FC<PMOInvestigativeResearchTabProps> = ({
  onInvestigationComplete
}) => {
  const { user } = useAuth();
  const [showInterface, setShowInterface] = useState(false);
  const [investigationHistory, setInvestigationHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load investigation history on component mount
  useEffect(() => {
    if (user?.email) {
      loadInvestigationHistory();
    }
  }, [user?.email]);

  const loadInvestigationHistory = async () => {
    if (!user?.email) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/investigative-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'history',
          userId: user.email,
          limit: 10
        })
      });

      const data = await response.json();
      if (data.success) {
        setInvestigationHistory(data.data || []);
      } else {
        setError(data.error || 'Failed to load investigation history');
      }
    } catch (error: any) {
      setError(error.message || 'Failed to load investigation history');
    } finally {
      setLoading(false);
    }
  };

  const handleInvestigationComplete = (result: any) => {
    // Refresh history
    loadInvestigationHistory();
    
    // Call parent callback
    if (onInvestigationComplete) {
      onInvestigationComplete(result);
    }
    
    // Hide interface
    setShowInterface(false);
  };

  if (showInterface) {
    return (
      <InvestigativeResearchInterface
        onInvestigationComplete={handleInvestigationComplete}
        onClose={() => setShowInterface(false)}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-semibold text-white flex items-center">
              <Search className="mr-3 h-6 w-6 text-purple-400" />
              Investigative Research
            </h2>
            <p className="text-zinc-400 mt-2">
              Conduct comprehensive investigative research using specialized AI journalist agents
            </p>
          </div>
          <Button
            onClick={() => setShowInterface(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Search className="mr-2 h-4 w-4" />
            Start Investigation
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
            <Clock className="h-6 w-6 text-purple-400 mx-auto mb-2" />
            <div className="text-lg font-semibold text-white">5-15 min</div>
            <div className="text-xs text-zinc-400">Average Duration</div>
          </div>
          
          <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
            <Users className="h-6 w-6 text-purple-400 mx-auto mb-2" />
            <div className="text-lg font-semibold text-white">3-6</div>
            <div className="text-xs text-zinc-400">Journalist Agents</div>
          </div>
          
          <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
            <FileText className="h-6 w-6 text-purple-400 mx-auto mb-2" />
            <div className="text-lg font-semibold text-white">5-15</div>
            <div className="text-xs text-zinc-400">Pages Generated</div>
          </div>
          
          <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
            <TrendingUp className="h-6 w-6 text-purple-400 mx-auto mb-2" />
            <div className="text-lg font-semibold text-white">95%</div>
            <div className="text-xs text-zinc-400">Success Rate</div>
          </div>
        </div>
      </div>

      {/* Investigation Types Overview */}
      <PMOInvestigativeResearchCard 
        onInvestigationComplete={handleInvestigationComplete}
      />

      {/* Investigation History */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-white">Recent Investigations</h3>
          <Button
            onClick={loadInvestigationHistory}
            variant="outline"
            size="sm"
            disabled={loading}
            className="border-zinc-600 text-zinc-300 hover:bg-zinc-700"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              'Refresh'
            )}
          </Button>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-900/20 border border-red-700 rounded-lg text-red-300">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              <p className="font-semibold">Error Loading History</p>
            </div>
            <p className="mt-1 text-sm">{error}</p>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-purple-400 mr-2" />
            <span className="text-zinc-400">Loading investigation history...</span>
          </div>
        ) : investigationHistory.length === 0 ? (
          <div className="text-center py-8">
            <Search className="h-12 w-12 mx-auto mb-3 text-zinc-600" />
            <p className="text-zinc-500">No investigations found</p>
            <p className="text-sm text-zinc-600 mt-1">Start your first investigation to see results here</p>
          </div>
        ) : (
          <div className="space-y-3">
            {investigationHistory.map((investigation, index) => (
              <div
                key={investigation.investigationId || index}
                className="flex items-center justify-between p-4 bg-zinc-800/30 rounded-lg border border-zinc-700 hover:border-zinc-600 transition-colors"
              >
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-white">
                    {investigation.title || 'Untitled Investigation'}
                  </h4>
                  <div className="flex items-center mt-1 text-xs text-zinc-400 space-x-4">
                    <span className="capitalize">
                      {investigation.investigationType?.replace('_', ' ') || 'Unknown Type'}
                    </span>
                    <span>
                      {investigation.keyFindings?.length || 0} findings
                    </span>
                    <span>
                      {investigation.createdAt ? new Date(investigation.createdAt).toLocaleDateString() : 'Unknown date'}
                    </span>
                  </div>
                </div>
                <ChevronRight className="h-4 w-4 text-zinc-500" />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Integration Information */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 p-6">
        <h3 className="text-lg font-medium text-white mb-4">PMO Integration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-purple-400">Workflow Integration</h4>
            <ul className="space-y-2 text-sm text-zinc-400">
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                Automatic task assignment to Research Team (Ag006)
              </li>
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                Results stored in PMO Output system
              </li>
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                PDF reports generated automatically
              </li>
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                Progress tracking with real-time updates
              </li>
            </ul>
          </div>
          
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-purple-400">Technical Capabilities</h4>
            <ul className="space-y-2 text-sm text-zinc-400">
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                Multi-LLM comparison analysis
              </li>
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                Specialized journalist AI personas
              </li>
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                8 investigation types supported
              </li>
              <li className="flex items-start">
                <span className="text-purple-400 mr-2">•</span>
                Professional report consolidation
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PMOInvestigativeResearchTab;
