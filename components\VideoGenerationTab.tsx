// components/VideoGenerationTab.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Loader2, UploadCloud, Video as VideoIcon, Wand2, Save, Download, ExternalLink, AlertTriangle } from 'lucide-react';


export interface SaveVideoPayload {
  veoVideoUri: string; // The temporary URI from Google Veo
  prompt: string;
  model: string;
  sourceImageUrl?: string;
  duration?: number;
  aspectRatio?: string;
}

export interface GeneratedVideoResponse {
  videoUrl: string; // URL of the stored video (after saving to your gallery)
  thumbnailUrl?: string; // Optional thumbnail
  prompt: string;
  sourceImageUrl?: string; // If image-to-video
  model: string; // e.g., 'veo-2.0-generate-001'
  operationId: string; // Veo operation ID
  jobId: string; // A unique ID for this video job
  namespace: string; // e.g., 'google-veo'
  timestamp: string;
  savedToGallery: boolean;
  galleryNamespace?: string;
  duration?: number;
  aspectRatio?: string;
  tempVideoUri?: string; // Temporary Veo URI
}


interface VideoGenerationTabProps {
  apiKey: string;
  userId?: string;
  onResult: (data: GeneratedVideoResponse | null, error: string | null) => void;
  onSaveToGallery: (videoData: {
    tempVideoUri: string;
    prompt: string;
    model: string;
    jobId: string;
    namespace: string;
    duration?: number;
    aspectRatio?: string;
    sourceImageUrl?: string;
  }) => Promise<void>;
  onOpenGallery: (namespace?: string) => void;
  setPageLoading: (loading: boolean) => void;
}

// --- Google GenAI SDK Import ---
import { GoogleGenAI } from "@google/genai";

// Using GoogleGenAI directly in the component
// --- End Google GenAI SDK Import ---

export default function VideoGenerationTab({ apiKey, userId, onResult, onSaveToGallery, onOpenGallery, setPageLoading }: VideoGenerationTabProps) {
  const [mode, setMode] = useState<'text-to-video' | 'image-to-video'>('text-to-video');
  const [sourceImage, setSourceImage] = useState<File | null>(null);
  const [sourceImagePreview, setSourceImagePreview] = useState<string | null>(null);
  const [videoPrompt, setVideoPrompt] = useState<string>('');
  const [negativePrompt, setNegativePrompt] = useState<string>('');
  const [aspectRatio, setAspectRatio] = useState<string>('16:9');
  const [numVideos, setNumVideos] = useState<number>(1);
  const [durationSeconds, setDurationSeconds] = useState<number>(5);
  const [personGeneration, setPersonGeneration] = useState<string>('dont_allow');
  const [enhancePrompt, setEnhancePrompt] = useState<boolean>(true);
  const [selectedModel, setSelectedModel] = useState<string>('veo-2.0-generate-001');

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentVideoResult, setCurrentVideoResult] = useState<GeneratedVideoResponse | null>(null);
  const [operation, setOperation] = useState<any | null>(null);
  const [operationStatus, setOperationStatus] = useState<string>('');

  // Create a stable reference to the Google GenAI SDK
  const ai = React.useMemo(() => new GoogleGenAI({ apiKey }), [apiKey]);

  // Handle source image change with appropriate cleanup
  const handleSourceImageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSourceImage(file);
      // Clean up previous URL object if it exists
      if (sourceImagePreview) URL.revokeObjectURL(sourceImagePreview);
      setSourceImagePreview(URL.createObjectURL(file));
    } else {
      setSourceImage(null);
      if (sourceImagePreview) URL.revokeObjectURL(sourceImagePreview);
      setSourceImagePreview(null);
    }
  }, [sourceImagePreview]);

  // Convert file to base64 for API consumption
  const fileToImageBytes = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        const base64Data = result.split(',')[1]; // Get the Base64 string
        resolve(base64Data);
      };
      reader.onerror = (error) => reject(error);
    });
  }, []);

  // Form submission handler
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Validate inputs
    if (!videoPrompt && mode === 'text-to-video') {
      setError("Please provide a video prompt.");
      onResult(null, "Video prompt is required.");
      return;
    }

    if (mode === 'image-to-video' && !sourceImage) {
      setError("Please upload a source image for image-to-video mode.");
      onResult(null, "Source image is required for image-to-video.");
      return;
    }

    // Set loading states
    setIsLoading(true);
    setPageLoading(true);
    setError(null);
    setCurrentVideoResult(null);
    onResult(null, null);
    setOperation(null);
    setOperationStatus('Initializing video generation...');

    try {
      // Prepare parameters for Veo API with model-specific configuration
      const veoParams: any = {
        model: selectedModel,
        prompt: videoPrompt,
        config: {
          aspectRatio: aspectRatio,
          numberOfVideos: numVideos,
          enhance_prompt: enhancePrompt,
        }
      };

      // Add model-specific parameters
      if (selectedModel === 'veo-2.0-generate-001') {
        // Veo 2.0 specific parameters
        veoParams.config.durationSeconds = durationSeconds;
        veoParams.config.personGeneration = personGeneration;
      } else if (selectedModel === 'veo-3.0-generate-preview') {
        // Veo 3.0 specific parameters
        veoParams.config.personGeneration = 'allow_all'; // Fixed value for Veo 3.0
        // durationSeconds is not supported and enhancePrompt is always on
      }

      // Add negative prompt if provided
      if (negativePrompt) veoParams.negativePrompt = negativePrompt;

      // Handle image-to-video vs text-to-video differences
      if (mode === 'image-to-video' && sourceImage) {
        const imageBytes = await fileToImageBytes(sourceImage);
        veoParams.image = { imageBytes, mimeType: sourceImage.type };
        // Person generation for image-to-video mode (only Veo 2.0 supports image-to-video)
        if (selectedModel === 'veo-2.0-generate-001') {
          veoParams.config.personGeneration = personGeneration;
        }
      }
      // For text-to-video, personGeneration is already set above based on model

      // Start video generation
      const initialOperation = await ai.models.generateVideos(veoParams);
      // The operation name is returned as a string in the actual SDK
      setOperation(initialOperation);
      setOperationStatus('Video generation started. Polling for results...');

    } catch (err: any) {
      console.error("Error starting video generation:", err);
      let errorMessage = err.message || "Failed to start video generation.";

      // Handle specific Veo 3.0 availability issues
      if (selectedModel === 'veo-3.0-generate-preview' &&
          (err.message?.includes('not supported') || err.message?.includes('INVALID_ARGUMENT'))) {
        errorMessage = `Veo 3.0 is not available in your region or requires special access. Please try Veo 2.0 instead.`;
        // Automatically switch to Veo 2.0
        setSelectedModel('veo-2.0-generate-001');
      }

      setError(errorMessage);
      onResult(null, errorMessage);
      setIsLoading(false);
      setPageLoading(false);
      setOperationStatus('');
    }
  };

  // Polling effect for Veo API operation status
  useEffect(() => {
    let pollInterval: NodeJS.Timeout | undefined;

    // Only poll if we have an operation and no result yet
    if (operation && !currentVideoResult) {
      setIsLoading(true);

      const poll = async () => {
        try {
          setOperationStatus(`Polling operation: ${operation?.name?.substring(0, 15) || 'unknown'}...`);

          // Get the operation status using the operation name
          // Pass the operation object or ID directly
          const currentOpState = await ai.operations.getVideosOperation({
            operation: operation,
          });

          if (currentOpState.done) {
            setOperationStatus('Video generation complete!');

            // Safely access generatedVideos array from the response
            const generatedVideosArray = currentOpState.response?.generatedVideos;

            if (generatedVideosArray && generatedVideosArray.length > 0) {
              // Process the first generated video data
              const firstVideoData = generatedVideosArray[0];
              const resultJobId = `video-${Date.now()}`;

              // Make sure video URI exists before using it
              if (firstVideoData.video?.uri) {
                const newResult: GeneratedVideoResponse = {
                  videoUrl: `${firstVideoData.video.uri}&key=${apiKey}`, // Add API key for authentication
                  tempVideoUri: firstVideoData.video.uri, // Store raw URI for saving
                  prompt: videoPrompt,
                  model: selectedModel, // Use the selected model
                  operationId: operation?.name || '', // operation name from state
                  jobId: resultJobId,
                  namespace: 'google-veo',
                  timestamp: new Date().toISOString(),
                  savedToGallery: false,
                  duration: durationSeconds,
                  aspectRatio: aspectRatio,
                  sourceImageUrl: sourceImagePreview || undefined,
                };

                setCurrentVideoResult(newResult);
                onResult(newResult, null);
              } else {
                const errorMessage = "Video URI is missing from the response.";
                setError(errorMessage);
                onResult(null, errorMessage);
              }
            } else {
              const errorMessage = "Operation finished but no videos were generated.";
              setError(errorMessage);
              onResult(null, errorMessage);
            }

            // Clean up polling state
            setOperation(null);
            setIsLoading(false);
            setPageLoading(false);
          } else {
            // Update status and continue polling
            // Use a default message if status is not available
            const statusMessage = `Still processing: ${operation?.name?.substring(0, 15) || 'unknown'}...`;
            setOperationStatus(statusMessage);
            pollInterval = setTimeout(poll, 10000); // Poll every 10 seconds
          }
        } catch (err: any) {
          console.error("Error polling Veo operation:", err);
          const errorMessage = err.message || "Error during video processing.";
          setError(errorMessage);
          onResult(null, errorMessage);

          // Clean up polling state
          setOperation(null);
          setIsLoading(false);
          setPageLoading(false);
          setOperationStatus('');
        }
      };

      // Set initial polling with delay
      pollInterval = setTimeout(poll, 3000);

      // Clean up function to prevent memory leaks
      return () => {
        if (pollInterval) clearTimeout(pollInterval);
      };
    }

    // No cleanup needed if not polling
    return () => {};
  }, [operation, currentVideoResult, ai, apiKey, onResult, videoPrompt, durationSeconds, aspectRatio, sourceImagePreview, setPageLoading]);

  // Handle saving to gallery
  const handleSaveToGallery = async () => {
    if (!currentVideoResult || !currentVideoResult.tempVideoUri) {
      alert("No video data to save.");
      return;
    }

    setPageLoading(true);

    try {
      await onSaveToGallery({
        tempVideoUri: currentVideoResult.tempVideoUri,
        prompt: currentVideoResult.prompt,
        model: currentVideoResult.model,
        jobId: currentVideoResult.jobId,
        namespace: currentVideoResult.namespace,
        duration: currentVideoResult.duration,
        aspectRatio: currentVideoResult.aspectRatio,
        sourceImageUrl: currentVideoResult.sourceImageUrl,
      });

      // Update local state to reflect saved status
      setCurrentVideoResult(prev => prev ? {...prev, savedToGallery: true} : null);
      alert("Video sent for saving to gallery! You can now view it in the gallery.");
    } catch (error) {
      console.error("Failed to save video to gallery:", error);
      alert("Failed to save video to gallery. Please try again.");
    } finally {
      setPageLoading(false);
    }
  };

  // Cleanup source image preview URL on component unmount
  useEffect(() => {
    return () => {
      if (sourceImagePreview) URL.revokeObjectURL(sourceImagePreview);
    };
  }, [sourceImagePreview]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Input Card */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <Wand2 className="mr-2 h-5 w-5 text-orange-400" />
            Video Generation Settings (Google Veo)
          </h2>
          <p className="text-sm text-zinc-400">Configure text-to-video or image-to-video generation.</p>
        </div>
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Mode Selection */}
          <div className="flex space-x-4">
            <label className="flex items-center text-sm text-zinc-300">
              <input
                type="radio"
                name="videoMode"
                value="text-to-video"
                checked={mode === 'text-to-video'}
                onChange={() => setMode('text-to-video')}
                className="mr-1.5 accent-orange-500"
              />
              Text-to-Video
            </label>
            <label className="flex items-center text-sm text-zinc-300">
              <input
                type="radio"
                name="videoMode"
                value="image-to-video"
                checked={mode === 'image-to-video'}
                onChange={() => setMode('image-to-video')}
                className="mr-1.5 accent-orange-500"
              />
              Image-to-Video
            </label>
          </div>

          {/* Source Image (for Image-to-Video) */}
          {mode === 'image-to-video' && (
            <div className="space-y-2">
              <label htmlFor="sourceVideoImage" className="block text-sm font-medium text-zinc-300">Source Image</label>
              <input
                id="sourceVideoImage"
                type="file"
                accept="image/*"
                onChange={handleSourceImageChange}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-300 file:mr-2 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-orange-600 file:text-white hover:file:bg-orange-700"
              />
              {sourceImagePreview && <img src={sourceImagePreview} alt="Preview" className="mt-2 rounded-md max-h-32 object-contain"/>}
            </div>
          )}

          {/* Video Prompt */}
          <div className="space-y-2">
            <label htmlFor="videoPrompt" className="block text-sm font-medium text-zinc-300">Video Prompt</label>
            <textarea
              id="videoPrompt"
              rows={3}
              value={videoPrompt}
              onChange={(e) => setVideoPrompt(e.target.value)}
              placeholder="e.g., 'A golden retriever puppy playing in a field of flowers, cinematic lighting'"
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-orange-500"
              required={mode === 'text-to-video'}
            />
          </div>

          {/* Negative Prompt */}
          <div className="space-y-2">
            <label htmlFor="negativePrompt" className="block text-sm font-medium text-zinc-300">Negative Prompt (Optional)</label>
            <textarea
              id="negativePrompt"
              rows={2}
              value={negativePrompt}
              onChange={(e) => setNegativePrompt(e.target.value)}
              placeholder="e.g., 'blurry, low quality, text, watermark'"
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>

          {/* Model Selection */}
          <div className="space-y-2">
            <label htmlFor="veoModel" className="block text-sm font-medium text-zinc-300">Veo Model</label>
            <select
              id="veoModel"
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="veo-2.0-generate-001">Veo 2.0 (Stable) - Recommended</option>
              <option value="veo-3.0-generate-preview">Veo 3.0 (Preview) - Limited Availability</option>
            </select>
            <div className="text-xs text-zinc-500">
              {selectedModel === 'veo-2.0-generate-001' ? (
                <div>
                  <p className="text-green-400">✅ Stable version with duration control and proven reliability</p>
                  <p>• Supports text-to-video and image-to-video</p>
                  <p>• Configurable duration (5-8 seconds)</p>
                  <p>• Multiple aspect ratios (16:9, 9:16)</p>
                </div>
              ) : (
                <div>
                  <p className="text-amber-400">⚠️ Preview version with enhanced quality and audio</p>
                  <p>• May have regional restrictions or limited access</p>
                  <p>• Automatic 8-second duration</p>
                  <p>• Only 16:9 aspect ratio</p>
                  <p>• Includes integrated audio generation</p>
                </div>
              )}
            </div>
          </div>

          {/* Settings Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
                <label htmlFor="aspectRatio" className="block text-sm font-medium text-zinc-300">Aspect Ratio</label>
                <select
                  id="aspectRatio"
                  value={aspectRatio}
                  onChange={(e) => setAspectRatio(e.target.value)}
                  className="w-full mt-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white"
                >
                    <option value="16:9">16:9 (Landscape)</option>
                    <option value="9:16">9:16 (Portrait)</option>
                </select>
            </div>
            <div>
                <label htmlFor="numVideos" className="block text-sm font-medium text-zinc-300">Number of Videos (1-2)</label>
                <input
                  type="number"
                  id="numVideos"
                  value={numVideos}
                  onChange={(e) => setNumVideos(Math.max(1, Math.min(2, parseInt(e.target.value) || 1)))}
                  min="1"
                  max="2"
                  className="w-full mt-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white"
                />
            </div>
            <div>
                <label htmlFor="durationSeconds" className="block text-sm font-medium text-zinc-300">
                  Duration (s, 5-8)
                  {selectedModel === 'veo-3.0-generate-preview' && (
                    <span className="text-xs text-amber-400 ml-1">(Not supported in Veo 3.0)</span>
                  )}
                </label>
                <input
                  type="number"
                  id="durationSeconds"
                  value={durationSeconds}
                  onChange={(e) => setDurationSeconds(Math.max(5, Math.min(8, parseInt(e.target.value) || 5)))}
                  min="5"
                  max="8"
                  disabled={selectedModel === 'veo-3.0-generate-preview'}
                  className="w-full mt-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white disabled:opacity-50 disabled:cursor-not-allowed"
                />
                {selectedModel === 'veo-3.0-generate-preview' && (
                  <p className="text-xs text-zinc-500 mt-1">Veo 3.0 uses automatic duration optimization.</p>
                )}
            </div>
            <div>
                <label htmlFor="personGeneration" className="block text-sm font-medium text-zinc-300">
                  Person Generation
                  {selectedModel === 'veo-3.0-generate-preview' && (
                    <span className="text-xs text-amber-400 ml-1">(Fixed to "Allow All" in Veo 3.0)</span>
                  )}
                </label>
                <select
                  id="personGeneration"
                  value={selectedModel === 'veo-3.0-generate-preview' ? 'allow_all' : personGeneration}
                  onChange={(e) => setPersonGeneration(e.target.value)}
                  disabled={mode === 'image-to-video' || selectedModel === 'veo-3.0-generate-preview'}
                  className="w-full mt-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <option value="dont_allow">Don't Allow</option>
                    <option value="allow_adult">Allow Adults</option>
                    {selectedModel === 'veo-3.0-generate-preview' && (
                      <option value="allow_all">Allow All (Veo 3.0 Default)</option>
                    )}
                </select>
                {mode === 'image-to-video' && <p className="text-xs text-zinc-500 mt-1">Disabled for Image-to-Video.</p>}
                {selectedModel === 'veo-3.0-generate-preview' && mode !== 'image-to-video' && (
                  <p className="text-xs text-zinc-500 mt-1">Veo 3.0 automatically allows all person generation.</p>
                )}
            </div>
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="enhancePrompt"
              checked={selectedModel === 'veo-3.0-generate-preview' ? true : enhancePrompt}
              onChange={(e) => setEnhancePrompt(e.target.checked)}
              disabled={selectedModel === 'veo-3.0-generate-preview'}
              className="h-4 w-4 rounded border-zinc-700 text-orange-500 focus:ring-orange-500 bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
            />
            <label htmlFor="enhancePrompt" className="ml-2 text-sm text-zinc-300">
              Enhance Prompt
              {selectedModel === 'veo-3.0-generate-preview' && (
                <span className="text-xs text-amber-400 ml-1">(Always enabled in Veo 3.0)</span>
              )}
            </label>
          </div>

          <div className="pt-2">
            <button
              type="submit"
              disabled={isLoading || !!operation}
              className="w-full flex items-center justify-center px-4 py-2.5 rounded-md text-sm font-semibold transition-colors disabled:cursor-not-allowed bg-orange-600 text-white hover:bg-orange-700 disabled:bg-zinc-700 disabled:text-zinc-400"
            >
              {isLoading || !!operation ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Wand2 className="mr-2 h-4 w-4" />}
              {isLoading || !!operation ? 'Generating...' : 'Generate Video(s)'}
            </button>
          </div>
        </form>
      </div>

      {/* Result Card */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <VideoIcon className="mr-2 h-5 w-5 text-orange-400" />
            Generated Video
          </h2>
        </div>
        <div className="p-4 min-h-[300px] flex flex-col items-center justify-center">
          {isLoading || !!operation ? (
            <div className="text-center">
              <Loader2 className="h-10 w-10 animate-spin text-orange-500 mx-auto" />
              <p className="mt-2 text-sm text-zinc-400">{operationStatus || "Initializing..."}</p>
            </div>
          ) : error ? (
            <div className="bg-red-900/20 p-4 rounded-md text-red-400 text-center">
              <AlertTriangle className="h-6 w-6 mx-auto mb-2" />
              <p className="font-semibold">Error</p>
              <p>{error}</p>
            </div>
          ) : currentVideoResult?.videoUrl ? (
            <div className="space-y-3 w-full">
              <video
                controls
                src={currentVideoResult.videoUrl}
                className="rounded-md border border-zinc-700 w-full max-h-80"
              />
              <div className="text-xs text-zinc-400 space-y-0.5">
                  <p><span className="font-semibold text-zinc-300">Prompt:</span> {currentVideoResult.prompt}</p>
                  <p><span className="font-semibold text-zinc-300">Model:</span> {currentVideoResult.model}</p>
                  <p><span className="font-semibold text-zinc-300">Job ID:</span> {currentVideoResult.jobId}</p>
                  {currentVideoResult.tempVideoUri && <p className="text-amber-400">
                    <AlertTriangle size={12} className="inline mr-1"/>
                    Displayed video is temporary. Save to gallery for permanent storage.
                  </p>}
              </div>
              <div className="flex space-x-2">
                <a
                  href={currentVideoResult.videoUrl}
                  download={`${currentVideoResult.jobId}.mp4`}
                  className="flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm"
                >
                  <Download className="mr-1.5 h-4 w-4" /> Download
                </a>
                {!currentVideoResult.savedToGallery && currentVideoResult.tempVideoUri ? (
                    <button
                        onClick={handleSaveToGallery}
                        disabled={!userId}
                        className="flex items-center px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md text-sm disabled:opacity-50"
                    >
                        <Save className="mr-1.5 h-4 w-4" /> Save to Gallery
                    </button>
                ) : (
                    <button
                        onClick={() => onOpenGallery(currentVideoResult.galleryNamespace)}
                        className="flex items-center px-3 py-1.5 bg-teal-600 hover:bg-teal-700 text-white rounded-md text-sm"
                    >
                        <ExternalLink className="mr-1.5 h-4 w-4" /> View in Gallery
                    </button>
                )}
              </div>
              {!userId && <p className="text-xs text-amber-400 mt-1">Login to save to gallery.</p>}
              <p className="text-xs text-zinc-500 mt-1">Video URIs from Veo are temporary. Save to gallery to persist.</p>
            </div>
          ) : (
            <div className="text-center">
              <VideoIcon className="h-10 w-10 text-zinc-700 mx-auto" />
              <p className="mt-4 text-zinc-500">Your generated video will appear here.</p>
              <p className="text-xs text-zinc-600 mt-2">Fill out the form and click "Generate Video" to start.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}