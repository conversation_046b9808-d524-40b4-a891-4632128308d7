/**
 * Internet Search Tool for performing web searches
 * Uses Brave Search API to find relevant web pages
 */

// Define interfaces for search results
export interface SearchResultItem {
  title: string;
  link: string;
  snippet?: string;
}

export interface SearchOptions {
  numResults?: number;
}

export interface SearchResultMetadata {
  source: string;
  searchTime?: number;
  resultCount?: number;
  error?: string;
}

export interface SearchResult {
  success: boolean;
  results: SearchResultItem[];
  formattedResults: string;
  metadata: SearchResultMetadata;
}

class InternetSearchTool {
  private apiKey: string | undefined;
  static description = `Use this tool to search the internet for current information when:
  - The query requires up-to-date or real-time information
  - The information needed is not likely to be in the existing knowledge base
  - The user explicitly requests internet search or current information
  - The query involves recent events, news, or developments
  
  Input should be a specific search query.
  Output will contain relevant search results including titles, snippets, and URLs.`;

  constructor() {
    // Get API key from environment
    this.apiKey = process.env.SEARCH_API || process.env.BRAVE_SEARCH_API_KEY;
    if (!this.apiKey) {
      console.warn('Warning: SEARCH_API or BRAVE_SEARCH_API_KEY environment variable is not set. Internet search may not work properly.');
    }
  }

  /**
   * Perform a web search using Brave Search API
   * @param query - The search query
   * @param options - Search options
   * @returns - Search results
   */
  async search(query: string, options: SearchOptions = {}): Promise<SearchResult> {
    if (!query) {
      return {
        success: false,
        results: [],
        formattedResults: 'Error: Search query is required',
        metadata: {
          source: 'brave_search',
          error: 'Search query is required'
        }
      };
    }

    try {
      const startTime = Date.now();
      const numResults = Math.min(options.numResults || 5, 10);

      const response = await fetch(
        `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}`,
        {
          headers: {
            'Accept': 'application/json',
            'X-Subscription-Token': this.apiKey || ''
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Brave Search API error: ${response.statusText}`);
      }

      const data = await response.json();

      // Extract and process results
      const webResults = data.web?.results?.slice(0, numResults) || [];
      const results: SearchResultItem[] = webResults.map((result: any) => ({
        title: result.title,
        link: result.url,
        snippet: result.description
      }));

      return {
        success: true,
        results,
        formattedResults: this._formatResultsAsMarkdown(results),
        metadata: {
          source: 'brave_search',
          searchTime: Date.now() - startTime,
          resultCount: results.length
        }
      };
    } catch (error: any) {
      console.error('Internet search error:', error);
      return {
        success: false,
        results: [],
        formattedResults: `Error: ${error.message || 'Unknown error occurred'}`,
        metadata: {
          source: 'brave_search',
          error: error.message || 'Unknown error occurred'
        }
      };
    }
  }

  /**
   * Format search results as markdown for display
   * @param results - Search result items
   * @returns Formatted markdown string
   */
  private _formatResultsAsMarkdown(results: SearchResultItem[]): string {
    if (results.length === 0) {
      return 'No results found.';
    }

    return results.map((item, index) => {
      return `### ${index + 1}. [${item.title}](${item.link})

${item.snippet || 'No description available.'}
`;
    }).join('\n');
  }

  /**
   * Process a search query and return formatted results
   * @param options - Search options including query
   * @returns Formatted search results
   */
  async process(options: { query: string; numResults?: number }): Promise<{ 
    success: boolean; 
    content: string; 
    sources: SearchResultItem[];
    error?: string;
  }> {
    try {
      const { query, numResults } = options;
      
      if (!query) {
        return {
          success: false,
          content: '',
          sources: [],
          error: 'Search query is required'
        };
      }

      const searchResult = await this.search(query, { numResults });
      
      if (!searchResult.success || searchResult.results.length === 0) {
        return {
          success: false,
          content: '',
          sources: [],
          error: searchResult.metadata.error || 'No search results found'
        };
      }

      return {
        success: true,
        content: searchResult.formattedResults,
        sources: searchResult.results
      };
    } catch (error) {
      console.error('Error in internet search process:', error);
      return {
        success: false,
        content: '',
        sources: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "internetSearch",
        description: "Search the internet for current information that might not be available in the document collection",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The search query"
            },
            numResults: {
              type: "integer",
              description: "Number of search results to return (default: 5, max: 10)"
            }
          },
          required: ["query"]
        }
      }
    };
  }
}

// Export a singleton instance
export const internetSearchTool = new InternetSearchTool();
