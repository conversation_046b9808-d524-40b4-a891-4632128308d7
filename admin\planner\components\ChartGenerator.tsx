'use client';

import React from 'react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart as RechartsR<PERSON>r<PERSON><PERSON>,
  Composed<PERSON>hart as RechartsComposed<PERSON>hart,
  Bar,
  Line,
  Pie,
  Area,
  Scatter,
  Radar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Cell,
} from 'recharts';
import {
  <PERSON><PERSON><PERSON> as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  <PERSON><PERSON>hart as LineChartIcon,
  AlertTriangle,
  Info,
  GitBranch,
  Grid,
  Circle,
} from 'lucide-react';
import TableGenerator from './TableGenerator';
import FlowChartGenerator from 'components/FlowChartGenerator';
import HeatMapGenerator from 'components/HeatMapGenerator';
import BubbleChartGenerator from 'components/BubbleChartGenerator';

// Define interfaces for chart configuration
interface AxisConfig {
  dataKey?: string;
  label?: string;
}

interface ChartConfig {
  chartType: string;
  title: string;
  subtitle?: string;
  data: Array<Record<string, any>>;
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
  colors?: string[];
  legend?: boolean;
  tooltip?: boolean;
  grid?: boolean;
  explanation?: string;
  colorScheme?: 'sequential' | 'diverging' | 'categorical' | 'monochrome';
  useDistinctColors?: boolean;
  donut?: boolean;
  scatterName?: string;
  colorByValue?: boolean;
  barKeys?: string[];
  lineKeys?: string[];
  areaKeys?: string[];
  columns?: Array<{ key: string; label: string }>;
  pagination?: boolean;
  rowsPerPage?: number;
  nodes?: Array<{ id: string; label: string; [key: string]: any }>;
  edges?: Array<{ source: string; target: string; [key: string]: any }>;
  direction?: string;
  fitView?: boolean;
  colorScale?: string[];
  showValues?: boolean;
  zAxis?: AxisConfig;
  colorKey?: string;
}

/**
 * Chart Generator component for rendering various chart types
 */
const ChartGenerator: React.FC<{ chartConfig?: ChartConfig }> = ({ chartConfig }) => {
  // Enhanced color palette with vibrant, accessible colors
  const defaultColors: string[] = [
    '#3b82f6', // blue-500
    '#10b981', // emerald-500
    '#f59e0b', // amber-500
    '#ef4444', // red-500
    '#8b5cf6', // violet-500
    '#ec4899', // pink-500
    '#06b6d4', // cyan-500
    '#f97316', // orange-500
    '#84cc16', // lime-500
    '#6366f1', // indigo-500
    '#14b8a6', // teal-500
    '#a855f7', // purple-500
    '#f43f5e', // rose-500
    '#0ea5e9', // sky-500
    '#22c55e', // green-500
  ];

  // Category-specific color palettes
  const colorPalettes: Record<string, string[]> = {
    sequential: ['#0ea5e9', '#38bdf8', '#7dd3fc', '#bae6fd', '#e0f2fe'],
    diverging: ['#ef4444', '#f97316', '#eab308', '#84cc16', '#22c55e'],
    categorical: defaultColors,
    monochrome: ['#1e3a8a', '#1e40af', '#1d4ed8', '#2563eb', '#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe', '#dbeafe'],
  };

  // If no chart config is provided, show a placeholder
  if (!chartConfig) {
    return (
      <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
        <div className="flex justify-center space-x-4 mb-4">
          <BarChartIcon className="text-zinc-600" size={28} />
          <LineChartIcon className="text-zinc-600" size={28} />
          <PieChartIcon className="text-zinc-600" size={28} />
          <GitBranch className="text-zinc-600" size={28} />
          <Grid className="text-zinc-600" size={28} />
          <Circle className="text-zinc-600" size={28} />
        </div>
        <h3 className="text-lg font-semibold mb-2 text-zinc-300">No Chart Data Available</h3>
        <p>Enter a prompt to generate a chart visualization.</p>
      </div>
    );
  }

  // Debug output
  console.log('Chart Config:', chartConfig);
  console.log('Chart Data:', chartConfig.data);

  // Extract chart configuration with default values
  const {
    chartType,
    title,
    subtitle,
    data,
    xAxis = {},
    yAxis = {},
    colors = defaultColors,
    legend = true,
    tooltip = true,
    grid = true,
    explanation,
  } = chartConfig;

  // Render the appropriate chart based on chartType
  const renderChart = (): JSX.Element => {
    switch (chartType) {
      case 'bar':
        // Check if data has the expected structure
        const hasValueKey = data.some((item) => 'value' in item);
        const valueKeys = hasValueKey
          ? ['value']
          : Object.keys(data[0] || {}).filter((key) => key !== 'name' && key !== xAxis.dataKey);

        console.log('Bar chart value keys:', valueKeys);

        // Determine which color palette to use
        const barPalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : valueKeys.length > 1
          ? defaultColors
          : colorPalettes.categorical;

        // For single series, use gradient if not specified
        const useSingleColor = valueKeys.length === 1 && !chartConfig.useDistinctColors;

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsBarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
              {grid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" />}
              <XAxis
                dataKey={xAxis.dataKey || 'name'}
                label={{ value: xAxis.label, position: 'insideBottom', offset: -10, fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              <YAxis
                label={{ value: yAxis.label, angle: -90, position: 'insideLeft', fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                  cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                />
              )}

              {/* Render bars for each value key */}
              {valueKeys.map((dataKey, keyIndex) => (
                <Bar
                  key={`bar-${dataKey}`}
                  dataKey={dataKey}
                  name={dataKey}
                  fill={barPalette[keyIndex % barPalette.length]}
                  radius={[4, 4, 0, 0]}
                  barSize={valueKeys.length > 1 ? 30 : 40}
                  animationDuration={1000}
                >
                  {!useSingleColor &&
                    data.map((_, index) => (
                      <Cell
                        key={`cell-${dataKey}-${index}`}
                        fill={barPalette[(keyIndex + index) % barPalette.length]}
                      />
                    ))}
                  {useSingleColor && (
                    <defs>
                      <linearGradient id={`barGradient-${dataKey}`} x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor={barPalette[0]} stopOpacity={0.9} />
                        <stop offset="100%" stopColor={barPalette[2] || barPalette[0]} stopOpacity={0.6} />
                      </linearGradient>
                    </defs>
                  )}
                </Bar>
              ))}
            </RechartsBarChart>
          </ResponsiveContainer>
        );

      case 'line':
        // Check if data has the expected structure
        const lineValueKeys = Object.keys(data[0] || {}).filter(
          (key) => key !== 'name' && key !== xAxis.dataKey
        );

        console.log('Line chart value keys:', lineValueKeys);

        // Determine which color palette to use
        const linePalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : defaultColors;

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsLineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
              {grid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" />}
              <XAxis
                dataKey={xAxis.dataKey || 'name'}
                label={{ value: xAxis.label, position: 'insideBottom', offset: -10, fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              <YAxis
                label={{ value: yAxis.label, angle: -90, position: 'insideLeft', fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                  cursor={{ stroke: 'rgba(255, 255, 255, 0.3)' }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                />
              )}

              {/* Render lines for each value key */}
              {lineValueKeys.map((dataKey, index) => (
                <Line
                  key={`line-${dataKey}`}
                  type="monotone"
                  dataKey={dataKey}
                  name={dataKey}
                  stroke={linePalette[index % linePalette.length]}
                  strokeWidth={2}
                  dot={{ fill: linePalette[index % linePalette.length], strokeWidth: 1, r: 4, strokeDasharray: '' }}
                  activeDot={{ r: 8, fill: linePalette[index % linePalette.length], stroke: '#fff' }}
                  animationDuration={1500}
                />
              ))}
            </RechartsLineChart>
          </ResponsiveContainer>
        );

      case 'pie':
        // Check if data has the expected structure
        const hasValueKeyPie = data.some((item) => 'value' in item);
        const valueKeyPie = hasValueKeyPie
          ? 'value'
          : Object.keys(data[0] || {}).find((key) => key !== 'name' && typeof data[0][key] === 'number');

        console.log('Pie chart value key:', valueKeyPie);

        // Determine which color palette to use
        const piePalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : defaultColors;

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsPieChart margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
              {valueKeyPie && (
                <Pie
                  data={data}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  label={({ name, percent }: { name: string; percent: number }) => (
                    <text
                      x={name.length > 10 ? -40 : -30}
                      y={0}
                      fill="#fff"
                      fontSize={12}
                      fontWeight="500"
                      textAnchor="middle"
                    >
                      {`${name}: ${(percent * 100).toFixed(0)}%`}
                    </text>
                  )}
                  outerRadius={150}
                  innerRadius={chartConfig.donut ? 80 : 0}
                  paddingAngle={2}
                  fill="#8884d8"
                  dataKey={valueKeyPie}
                  nameKey="name"
                  animationDuration={1000}
                  animationBegin={200}
                >
                  {data.map((_, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={piePalette[index % piePalette.length]}
                      stroke="#1f2937"
                      strokeWidth={1}
                    />
                  ))}
                </Pie>
              )}
              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                  layout="vertical"
                  verticalAlign="middle"
                  align="right"
                />
              )}
            </RechartsPieChart>
          </ResponsiveContainer>
        );

      case 'area':
        // Check if data has the expected structure
        const areaValueKeys = Object.keys(data[0] || {}).filter(
          (key) => key !== 'name' && key !== xAxis.dataKey
        );

        console.log('Area chart value keys:', areaValueKeys);

        // Determine which color palette to use
        const areaPalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : colorPalettes.sequential;

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsAreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
              {grid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" />}
              <XAxis
                dataKey={xAxis.dataKey || 'name'}
                label={{ value: xAxis.label, position: 'insideBottom', offset: -10, fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              <YAxis
                label={{ value: yAxis.label, angle: -90, position: 'insideLeft', fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                  cursor={{ stroke: 'rgba(255, 255, 255, 0.3)' }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                />
              )}

              {/* Render gradients for areas */}
              {areaValueKeys.map((dataKey, index) => (
                <defs key={`gradient-${dataKey}`}>
                  <linearGradient id={`colorGradient${index}`} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={areaPalette[index % areaPalette.length]} stopOpacity={0.8} />
                    <stop offset="95%" stopColor={areaPalette[index % areaPalette.length]} stopOpacity={0.1} />
                  </linearGradient>
                </defs>
              ))}

              {areaValueKeys.map((dataKey, index) => (
                <Area
                  key={`area-${dataKey}`}
                  type="monotone"
                  dataKey={dataKey}
                  name={dataKey}
                  fill={`url(#colorGradient${index})`}
                  stroke={areaPalette[index % areaPalette.length]}
                  strokeWidth={2}
                  activeDot={{ r: 6, stroke: '#fff' }}
                  animationDuration={1500}
                />
              ))}
            </RechartsAreaChart>
          </ResponsiveContainer>
        );

      case 'scatter':
        // Check if data has the expected structure for scatter plot
        const hasXYKeys = data.some((item) => 'x' in item && 'y' in item);

        // If data doesn't have x and y keys, try to adapt it
        let scatterData: Array<{ x: number; y: number; name?: string; original?: Record<string, any> }> = [];

        // Initialize with the original data if it already has the correct format
        if (hasXYKeys) {
          scatterData = data as Array<{ x: number; y: number; name?: string; original?: Record<string, any> }>;
        }
        if (!hasXYKeys) {
          console.log('Scatter plot data missing x/y keys, attempting to adapt data');

          // Try to find numeric keys that could be used as x and y
          const keys = Object.keys(data[0] || {});
          const numericKeys = keys.filter((key) => typeof data[0][key] === 'number' && key !== 'name');

          if (numericKeys.length >= 2) {
            // Use the first two numeric keys as x and y
            const xKey = numericKeys[0];
            const yKey = numericKeys[1];

            scatterData = data.map((item) => ({
              name: item.name || '',
              x: item[xKey],
              y: item[yKey],
              original: { ...item },
            }));

            console.log('Adapted scatter data using keys:', xKey, yKey);
            console.log('Adapted data:', scatterData);
          }
        }

        // Determine which color palette to use
        const scatterPalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : defaultColors;

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsScatterChart margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
              {grid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" />}
              <XAxis
                dataKey="x"
                type="number"
                name={xAxis.label}
                label={{ value: xAxis.label, position: 'insideBottom', offset: -10, fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              <YAxis
                dataKey="y"
                type="number"
                name={yAxis.label}
                label={{ value: yAxis.label, angle: -90, position: 'insideLeft', fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                  cursor={{ strokeDasharray: '3 3', stroke: 'rgba(255, 255, 255, 0.3)' }}
                  formatter={(value: number, name: string, _props: any) => {
                    if (name === 'x' || name === 'y') {
                      const label = name === 'x' ? xAxis.label || 'X' : yAxis.label || 'Y';
                      return [`${value}`, label];
                    }
                    return [value, name];
                  }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                />
              )}
              <Scatter
                name={chartConfig.scatterName || 'Data Points'}
                data={scatterData}
                fill={scatterPalette[0]}
                animationDuration={1500}
              >
                {scatterData.map((entry, index) => {
                  // Calculate color based on value if specified
                  let pointColor: string;
                  if (chartConfig.colorByValue) {
                    // Use a gradient based on y value
                    const min = Math.min(...scatterData.map((d) => d.y));
                    const max = Math.max(...scatterData.map((d) => d.y));
                    const normalizedValue = (entry.y - min) / (max - min || 1);
                    const colorIndex = Math.floor(normalizedValue * (scatterPalette.length - 1));
                    pointColor = scatterPalette[colorIndex];
                  } else {
                    // Use categorical colors
                    pointColor = scatterPalette[index % scatterPalette.length];
                  }

                  return (
                    <Cell
                      key={`cell-${index}`}
                      fill={pointColor}
                      stroke="#fff"
                      strokeWidth={1}
                    />
                  );
                })}
              </Scatter>
            </RechartsScatterChart>
          </ResponsiveContainer>
        );

      case 'radar':
        // Check if data has the expected structure
        const hasValueKeyRadar = data.some((item) => 'value' in item);
        const valueKeyRadar = hasValueKeyRadar
          ? 'value'
          : Object.keys(data[0] || {}).find((key) => key !== 'name' && typeof data[0][key] === 'number');

        console.log('Radar chart value key:', valueKeyRadar);

        // Determine which color palette to use
        const radarPalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : colorPalettes.categorical;

        // Get all data keys for multi-series radar charts
        const radarKeys = Object.keys(data[0] || {}).filter(
          (key) => key !== 'name' && key !== xAxis.dataKey && typeof data[0][key] === 'number'
        );

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsRadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
              <PolarGrid stroke="#4b5563" />
              <PolarAngleAxis
                dataKey={xAxis.dataKey || 'name'}
                tick={{ fill: '#d1d5db' }}
                stroke="#4b5563"
              />
              <PolarRadiusAxis tick={{ fill: '#9ca3af' }} stroke="#4b5563" />

              {/* For multi-series radar charts */}
              {radarKeys.length > 1 ? (
                radarKeys.map((key, index) => (
                  <Radar
                    key={`radar-${key}`}
                    name={key}
                    dataKey={key}
                    stroke={radarPalette[index % radarPalette.length]}
                    fill={radarPalette[index % radarPalette.length]}
                    fillOpacity={0.4}
                    strokeWidth={2}
                    animationDuration={1500}
                    animationBegin={index * 300}
                  />
                ))
              ) : (
                valueKeyRadar && (
                  <Radar
                    name={valueKeyRadar}
                    dataKey={valueKeyRadar}
                    stroke={radarPalette[0]}
                    fill={radarPalette[0]}
                    fillOpacity={0.6}
                    strokeWidth={2}
                    animationDuration={1500}
                  />
                )
              )}

              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                />
              )}
            </RechartsRadarChart>
          </ResponsiveContainer>
        );

      case 'composed':
        // For composed charts, identify keys for different chart types
        const keys = Object.keys(data[0] || {}).filter((key) => key !== 'name' && key !== xAxis.dataKey);

        // Try to identify keys by name or by configuration
        const barKeys = chartConfig.barKeys || keys.filter((key) => key.toLowerCase().includes('bar'));
        const lineKeys = chartConfig.lineKeys || keys.filter((key) => key.toLowerCase().includes('line'));
        const areaKeys = chartConfig.areaKeys || keys.filter((key) => key.toLowerCase().includes('area'));

        // Distribute remaining keys
        const remainingKeys = keys.filter(
          (key) => !barKeys.includes(key) && !lineKeys.includes(key) && !areaKeys.includes(key)
        );

        const distributedBarKeys = [...barKeys];
        const distributedLineKeys = [...lineKeys];
        const distributedAreaKeys = [...areaKeys];

        remainingKeys.forEach((key, index) => {
          const mod = index % 3;
          if (mod === 0) distributedBarKeys.push(key);
          else if (mod === 1) distributedLineKeys.push(key);
          else distributedAreaKeys.push(key);
        });

        console.log('Composed chart keys:', {
          bar: distributedBarKeys,
          line: distributedLineKeys,
          area: distributedAreaKeys,
        });

        // Determine which color palette to use
        const composedPalette = chartConfig.colorScheme
          ? colorPalettes[chartConfig.colorScheme] || defaultColors
          : defaultColors;

        return (
          <ResponsiveContainer width="100%" height={400}>
            <RechartsComposedChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
              {grid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" />}
              <XAxis
                dataKey={xAxis.dataKey || 'name'}
                label={{ value: xAxis.label, position: 'insideBottom', offset: -10, fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              <YAxis
                label={{ value: yAxis.label, angle: -90, position: 'insideLeft', fill: '#d1d5db' }}
                tick={{ fill: '#9ca3af' }}
                axisLine={{ stroke: '#4b5563' }}
              />
              {tooltip && (
                <Tooltip
                  contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                  itemStyle={{ color: '#f9fafb' }}
                  cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
                />
              )}
              {legend && (
                <Legend
                  wrapperStyle={{ color: '#d1d5db' }}
                  formatter={(value: string) => <span style={{ color: '#d1d5db' }}>{value}</span>}
                />
              )}

              {/* Define gradients for areas */}
              <defs>
                {distributedAreaKeys.map((key, index) => (
                  <linearGradient key={`gradient-${key}`} id={`areaGradient-${key}`} x1="0" y1="0" x2="0" y2="1">
                    <stop
                      offset="5%"
                      stopColor={
                        composedPalette[
                          (index + distributedBarKeys.length + distributedLineKeys.length) % composedPalette.length
                        ]
                      }
                      stopOpacity={0.8}
                    />
                    <stop
                      offset="95%"
                      stopColor={
                        composedPalette[
                          (index + distributedBarKeys.length + distributedLineKeys.length) % composedPalette.length
                        ]
                      }
                      stopOpacity={0.2}
                    />
                  </linearGradient>
                ))}
              </defs>

              {/* Render bars */}
              {distributedBarKeys.map((key, index) => (
                <Bar
                  key={`bar-${key}`}
                  dataKey={key}
                  name={key}
                  fill={composedPalette[index % composedPalette.length]}
                  radius={[4, 4, 0, 0]}
                  barSize={30}
                  animationDuration={1000}
                  animationBegin={index * 150}
                />
              ))}

              {/* Render lines */}
              {distributedLineKeys.map((key, index) => (
                <Line
                  key={`line-${key}`}
                  type="monotone"
                  dataKey={key}
                  name={key}
                  stroke={composedPalette[(index + distributedBarKeys.length) % composedPalette.length]}
                  strokeWidth={2}
                  dot={{
                    fill: composedPalette[(index + distributedBarKeys.length) % composedPalette.length],
                    r: 4,
                  }}
                  activeDot={{ r: 7, stroke: '#fff' }}
                  animationDuration={1500}
                  animationBegin={300 + index * 150}
                />
              ))}

              {/* Render areas */}
              {distributedAreaKeys.map((key, index) => (
                <Area
                  key={`area-${key}`}
                  type="monotone"
                  dataKey={key}
                  name={key}
                  fill={`url(#areaGradient-${key})`}
                  stroke={
                    composedPalette[
                      (index + distributedBarKeys.length + distributedLineKeys.length) % composedPalette.length
                    ]
                  }
                  strokeWidth={1.5}
                  animationDuration={1500}
                  animationBegin={600 + index * 150}
                />
              ))}
            </RechartsComposedChart>
          </ResponsiveContainer>
        );

      case 'table':
        return (
          <TableGenerator
            tableConfig={{
              title,
              subtitle,
              columns: chartConfig?.columns,
              data,
              pagination: chartConfig?.pagination !== false,
              rowsPerPage: chartConfig?.rowsPerPage || 10,
              explanation,
            }}
          />
        );

      case 'flow':
        return (
          <FlowChartGenerator
            flowConfig={{
              title,
              subtitle,
              nodes: chartConfig?.nodes || [],
              edges: chartConfig?.edges || [],
              direction: chartConfig?.direction || 'LR',
              fitView: chartConfig?.fitView !== false,
              colors,
              explanation,
            }}
          />
        );

      case 'heatmap':
        return (
          <HeatMapGenerator
            heatmapConfig={{
              title,
              subtitle,
              data,
              xAxis,
              colorScale: chartConfig?.colorScale || colors,
              showValues: chartConfig?.showValues !== false,
              explanation,
            }}
          />
        );

      case 'bubble':
        return (
          <BubbleChartGenerator
            bubbleConfig={{
              title,
              subtitle,
              data,
              xAxis,
              yAxis,
              zAxis: chartConfig?.zAxis || {},
              colorKey: chartConfig?.colorKey,
              colors,
              grid,
              tooltip,
              legend,
              explanation,
            }}
          />
        );

      default:
        return (
          <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
            <AlertTriangle className="mx-auto mb-4 text-amber-500" size={32} />
            <h3 className="text-lg font-semibold mb-2 text-zinc-300">Unsupported Chart Type</h3>
            <p>The chart type "{chartType}" is not supported.</p>
          </div>
        );
    }
  };

  return (
    <div className="chart-container bg-zinc-900 rounded-lg border border-zinc-700 p-6">
      {/* Chart header */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white">{title}</h3>
        {subtitle && <p className="text-zinc-400 mt-1">{subtitle}</p>}
      </div>

      {/* Chart visualization */}
      <div className="chart-visualization mb-6">{renderChart()}</div>

      {/* Chart explanation */}
      {explanation && (
        <div className="mt-4 p-4 bg-zinc-800 rounded-md border border-zinc-700">
          <div className="flex items-start">
            <Info className="text-blue-400 mr-2 mt-1 flex-shrink-0" size={16} />
            <p className="text-zinc-300 text-sm">{explanation}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartGenerator;