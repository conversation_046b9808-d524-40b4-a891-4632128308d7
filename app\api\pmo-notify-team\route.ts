import { NextRequest, NextResponse } from 'next/server';
import { addDoc, collection } from 'firebase/firestore';
import { db } from '../../../components/firebase';
import { ResearchTeamAgent } from '../../../lib/agents/research/ResearchTeamAgent';
import { Task, TaskPriority } from '../../../admin/planner/types';
import { AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';
import {
  PMO_NOTIFICATION_TIMEOUTS,
  createTimeoutController,
  TimeoutError,
  TimeoutErrorType
} from '../../../lib/config/timeout-config';

// Extended Task interface for PMO operations that includes metadata
interface PMOTask extends Task {
  metadata?: {
    source?: string;
    pmoId?: string;
    pmoAssessment?: string;
    teamSelectionRationale?: string;
    autoTriggered?: boolean;
    triggerTimestamp?: string;
    context?: string;
    pmoContextCategories?: any;
    pmoCustomContext?: any;
    priority?: string;
    notificationId?: string;
    [key: string]: any;
  };
}

// Helper function to normalize priority values
function normalizePriority(priority: string): TaskPriority {
  const normalizedPriority = priority.trim().toLowerCase();

  if (normalizedPriority === 'low') return 'Low';
  if (normalizedPriority === 'medium') return 'Medium';
  if (normalizedPriority === 'high') return 'High';
  if (normalizedPriority === 'critical') return 'Critical';

  // Default to Medium if not recognized
  return 'Medium';
}

/**
 * API endpoint to notify teams about PMO requirements
 * Enhanced to automatically trigger marketing collaboration for Marketing team
 */
export async function POST(request: NextRequest) {
  try {
    const {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription,
      pmoAssessment,
      teamSelectionRationale,
      priority,
      category,
      userId,
      pmoRecord,
      metadata
    } = await request.json();

    // Validate required fields
    if (!pmoId || !teamId || !teamName || !projectTitle || !userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create team notification record
    const teamNotification = {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      projectDescription: projectDescription || '',
      pmoAssessment: pmoAssessment || '',
      teamSelectionRationale: teamSelectionRationale || '',
      priority: priority || 'Medium',
      category,
      userId,
      status: 'Pending Strategic Plan',
      notifiedAt: new Date(),
      requiresStrategicPlan: true,
      strategicPlanCreated: false,
      metadata: {
        ...metadata,
        documentType: 'Team Notification',
        source: 'PMO'
      }
    };

    // Save to team notifications collection
    const notificationRef = await addDoc(
      collection(db, 'users', userId, 'teamNotifications'),
      teamNotification
    );

    // Also create a record in the team-specific collection for easy access
    const teamSpecificNotification = {
      ...teamNotification,
      notificationId: notificationRef.id,
      teamSpecific: true
    };

    await addDoc(
      collection(db, 'users', userId, `${teamName.toLowerCase().replace(/\s+/g, '')}TeamTasks`),
      teamSpecificNotification
    );

    let marketingCollaborationResult = null;
    let researchCollaborationResult = null;
    let businessAnalysisCollaborationResult = null;
    let codebaseDocumentationResult = null;

    // If this is for the Marketing team, automatically trigger marketing collaboration
    if (teamName.toLowerCase() === 'marketing') {
      try {
        marketingCollaborationResult = await triggerMarketingCollaboration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (marketingError) {
        console.error('Error triggering marketing collaboration:', marketingError);
        // Don't fail the entire request if marketing collaboration fails
        // The notification was still created successfully
      }
    }

    // If this is for the Research team, automatically trigger research collaboration
    if (teamName.toLowerCase() === 'research') {
      try {
        researchCollaborationResult = await triggerResearchCollaboration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (researchError) {
        console.error('Error triggering research collaboration:', researchError);
        // Don't fail the entire request if research collaboration fails
        // The notification was still created successfully
      }
    }

    // If this is for the Business Analysis team, automatically trigger business analysis collaboration
    if (teamName.toLowerCase() === 'business analysis' || teamName.toLowerCase() === 'businessanalysis') {
      try {
        businessAnalysisCollaborationResult = await triggerBusinessAnalysisCollaboration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (businessAnalysisError) {
        console.error('Error triggering business analysis collaboration:', businessAnalysisError);
        // Don't fail the entire request if business analysis collaboration fails
        // The notification was still created successfully
      }
    }

    // If this is for the Codebase Documentation team, automatically trigger codebase documentation generation
    if (teamName.toLowerCase() === 'codebase documentation' || teamName.toLowerCase() === 'codebasedocumentation') {
      try {
        codebaseDocumentationResult = await triggerCodebaseDocumentationGeneration({
          pmoId,
          projectTitle,
          projectDescription,
          pmoAssessment,
          teamSelectionRationale,
          priority,
          category,
          userId,
          notificationId: notificationRef.id,
          pmoRecord
        });
      } catch (codebaseDocumentationError) {
        console.error('Error triggering codebase documentation generation:', codebaseDocumentationError);
        // Don't fail the entire request if codebase documentation generation fails
        // The notification was still created successfully
      }
    }

    return NextResponse.json({
      success: true,
      notificationId: notificationRef.id,
      message: `Requirements sent to ${teamName} team successfully`,
      marketingCollaboration: marketingCollaborationResult,
      researchCollaboration: researchCollaborationResult,
      businessAnalysisCollaboration: businessAnalysisCollaborationResult,
      codebaseDocumentation: codebaseDocumentationResult
    });

  } catch (error) {
    console.error('Error notifying team:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to notify team'
      },
      { status: 500 }
    );
  }
}

/**
 * Trigger marketing collaboration workflow from PMO notification
 */
async function triggerMarketingCollaboration(pmoData: {
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  userId: string;
  notificationId: string;
  pmoRecord?: any;
}) {
  // Send the original user request description as the prompt for the marketing team
  // The PMO assessment and other details will be provided as context
  const marketingPrompt = pmoData.projectDescription;

  // Prepare comprehensive context from PMO assessment for the marketing team
  const contextInformation = `
# PMO Marketing Requirements Analysis Context

## Project Overview
**Title:** ${pmoData.projectTitle}
**Priority:** ${pmoData.priority}
**PMO ID:** ${pmoData.pmoId}

## PMO Assessment
${pmoData.pmoAssessment}

## Team Selection Rationale
${pmoData.teamSelectionRationale}

## Marketing Team Objectives
Based on the PMO requirements above, please provide a comprehensive marketing strategy analysis that includes:

1. **Strategic Marketing Assessment** - Analyze the marketing implications and opportunities
2. **Target Audience Analysis** - Identify and profile the target market segments
3. **Competitive Landscape** - Research and analyze competitive positioning
4. **Marketing Channel Strategy** - Recommend optimal marketing channels and tactics
5. **Content Strategy** - Develop content themes and messaging framework
6. **Campaign Planning** - Outline campaign structure and timeline
7. **Success Metrics** - Define KPIs and measurement framework
8. **Resource Requirements** - Estimate budget and resource needs

Please ensure your analysis is comprehensive and actionable, providing specific recommendations that the marketing team can implement.

## Additional Context
${pmoData.pmoRecord?.customContext ? `- Custom Context: ${pmoData.pmoRecord.customContext}` : ''}
${pmoData.pmoRecord?.contextCategories?.length ? `- Source Document Categories: ${pmoData.pmoRecord.contextCategories.join(', ')}` : ''}
  `.trim();

  // Use the first contextCategory as the primary category for document search
  const documentCategory = pmoData.pmoRecord?.contextCategories?.length > 0
    ? pmoData.pmoRecord.contextCategories[0]
    : pmoData.category;

  // Call the marketing-agent-collaboration API
  const collaborationBody = {
    prompt: marketingPrompt,
    modelProvider: 'openai',
    modelName: 'o3-2025-04-16',
    userId: pmoData.userId,
    context: contextInformation,
    category: documentCategory, // Use PMO's contextCategories for document search
    metadata: {
      source: 'PMO',
      pmoId: pmoData.pmoId,
      recordTitle: pmoData.projectTitle, // Add PMO title for project naming
      projectTitle: pmoData.projectTitle, // Also add as projectTitle for compatibility
      notificationId: pmoData.notificationId,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      pmoContextCategories: pmoData.pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoData.pmoRecord?.customContext || null
    }
  };

  const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/marketing-agent-collaboration`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(collaborationBody)
  });

  if (!response.ok) {
    throw new Error(`Marketing collaboration API error: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();

  return {
    success: true,
    requestId: result.requestId,
    message: 'Marketing collaboration triggered successfully',
    collaborationData: result
  };
}

/**
 * Trigger research collaboration workflow from PMO notification
 */
async function triggerResearchCollaboration(pmoData: {
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  userId: string;
  notificationId: string;
  pmoRecord?: any;
}) {
  console.log(`📊 Triggering research collaboration for PMO ${pmoData.pmoId}...`);

  // Send the comprehensive PMO assessment as the primary task description for the research team
  // This ensures the Research Team receives the detailed PMO analysis to elaborate upon
  const researchPrompt = `# PMO Research Task: ${pmoData.projectTitle}

## Original User Request
${pmoData.projectDescription}

## PMO Assessment and Requirements Analysis
${pmoData.pmoAssessment}

## Research Team Assignment
${pmoData.teamSelectionRationale}

## Research Objectives
Based on the PMO assessment above, the Research Team is tasked with:

1. **Elaborating on PMO Findings**: Build upon and expand the detailed analysis provided by the PMO
2. **Comprehensive Research**: Conduct in-depth research on all aspects identified in the PMO assessment
3. **Strategic Implementation**: Develop actionable strategies and implementation plans based on PMO requirements
4. **Quality Enhancement**: Enhance the PMO analysis with additional research, data, and strategic insights
5. **Cross-team Coordination**: Identify opportunities for collaboration with other teams as outlined in the PMO assessment

## Expected Deliverables
- Comprehensive research analysis that builds upon the PMO assessment
- Strategic implementation recommendations aligned with PMO requirements
- Enhanced documentation with additional research findings
- Quality-assured outputs that meet PMO standards and expectations

The Research Team should treat the PMO assessment as the foundation and elaborate on every aspect with detailed research and strategic analysis.`;

  // Prepare comprehensive context from PMO assessment for the research team
  const contextInformation = `
# PMO Research Requirements Analysis Context

## Project Overview
- **PMO ID**: ${pmoData.pmoId}
- **Project Title**: ${pmoData.projectTitle}
- **Priority**: ${pmoData.priority}
- **Category**: ${pmoData.category}

## PMO Assessment
${pmoData.pmoAssessment}

## Team Selection Rationale
${pmoData.teamSelectionRationale}

## Research Requirements
The Research team has been selected to provide comprehensive analysis and strategic insights for this PMO initiative. The research should focus on:

1. **Information Gathering**: Collect relevant data and sources related to the project scope
2. **Analysis and Synthesis**: Process information to identify key insights, patterns, and recommendations
3. **Strategic Planning**: Develop strategic implementation recommendations aligned with PMO objectives
4. **Quality Assurance**: Ensure all research outputs meet PMO standards and requirements
5. **Cross-team Coordination**: Identify opportunities for collaboration with other agentic teams

## Expected Deliverables
- Comprehensive research analysis report
- Strategic implementation plan (if applicable)
- Quality-assured findings and recommendations
- PMO-compliant documentation and citations

## Context Categories
${pmoData.pmoRecord?.contextCategories?.join(', ') || 'General Research'}

${pmoData.pmoRecord?.customContext ? `## Additional Context\n${pmoData.pmoRecord.customContext}` : ''}
`;

  // Determine document category for research context
  const documentCategory = pmoData.pmoRecord?.contextCategories?.[0] || pmoData.category || 'research';

  // Create PMO Task object for ResearchTeamAgent
  const pmoTask: PMOTask = {
    id: `${pmoData.pmoId}-${Date.now()}`,
    projectId: 'pmo-project', // Add required projectId field
    title: pmoData.projectTitle || 'PMO Research Task',
    description: researchPrompt,
    category: documentCategory || 'Research',
    priority: normalizePriority(pmoData.priority || 'medium'),
    status: 'In Progress',
    startDate: new Date(), // Add required startDate field
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Default: 1 week from now
    assignedTo: [AgenticTeamId.Research],
    dependencies: [], // Add required dependencies field
    createdAt: new Date(),
    updatedAt: new Date(),
    metadata: {
      source: 'PMO',
      pmoId: pmoData.pmoId,
      pmoAssessment: pmoData.pmoAssessment,
      teamSelectionRationale: pmoData.teamSelectionRationale,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      context: contextInformation,
      pmoContextCategories: pmoData.pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoData.pmoRecord?.customContext || null,
      priority: pmoData.priority,
      notificationId: pmoData.notificationId
    }
  };

  // Initialize ResearchTeamAgent directly (no API call needed)
  const researchTeamAgent = new ResearchTeamAgent({
    userId: pmoData.userId,
    includeExplanation: true,
    streamResponse: false
  });

  // Process the PMO task directly
  const result = await researchTeamAgent.processTask(pmoTask);

  console.log(`✅ Research collaboration triggered successfully for PMO ${pmoData.pmoId}`);

  return {
    success: result.success,
    researchTaskId: result.taskId,
    researchPlanId: (result as any).researchSpecificOutput?.researchPlanId,
    output: result.output,
    documentIds: result.outputDocumentIds,
    methodology: (result as any).researchSpecificOutput?.researchMethodology,
    triggeredAt: new Date().toISOString(),
    pmoId: pmoData.pmoId,
    notificationId: pmoData.notificationId,
    message: 'Research collaboration triggered successfully',
    collaborationData: {
      source: 'Research Team Agent',
      pmoId: pmoData.pmoId,
      processedAt: new Date().toISOString(),
      teamId: AgenticTeamId.Research,
      methodology: (result as any).researchSpecificOutput?.researchMethodology,
      error: result.error
    }
  };
}

/**
 * Trigger business analysis collaboration workflow from PMO notification
 */
async function triggerBusinessAnalysisCollaboration(pmoData: {
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: string;
  category: string;
  userId: string;
  notificationId: string;
  pmoRecord?: any;
}) {
  console.log(`📊 Triggering business analysis collaboration for PMO ${pmoData.pmoId}...`);

  // Send the comprehensive PMO assessment as the primary task description for the business analysis team
  const businessAnalysisPrompt = `# PMO Business Analysis Task: ${pmoData.projectTitle}

## Project Overview
${pmoData.projectDescription}

## PMO Assessment
${pmoData.pmoAssessment}

## Team Selection Rationale
${pmoData.teamSelectionRationale}

## Priority Level
${pmoData.priority}

## Required Business Analysis Deliverables
- Comprehensive system architecture analysis
- Product overview and strategic positioning
- Detailed requirements engineering documentation
- Functional specifications and use case development
- Cross-functional business analysis recommendations
- Strategic implementation roadmap

## Context Categories
${pmoData.pmoRecord?.contextCategories?.join(', ') || 'General Business Analysis'}

${pmoData.pmoRecord?.customContext ? `## Additional Context\n${pmoData.pmoRecord.customContext}` : ''}
`;

  // Determine document category for business analysis context
  const documentCategory = pmoData.pmoRecord?.contextCategories?.[0] || pmoData.category || 'business-analysis';

  // Call the business-analysis-agent-collaboration API
  const collaborationBody = {
    prompt: businessAnalysisPrompt,
    modelProvider: 'anthropic',
    modelName: 'claude-sonnet-4-0',
    userId: pmoData.userId,
    context: pmoData.projectDescription,
    category: documentCategory,
    metadata: {
      source: 'PMO',
      pmoId: pmoData.pmoId,
      recordTitle: pmoData.projectTitle,
      projectTitle: pmoData.projectTitle,
      projectDescription: pmoData.projectDescription,
      notificationId: pmoData.notificationId,
      autoTriggered: true,
      triggerTimestamp: new Date().toISOString(),
      pmoContextCategories: pmoData.pmoRecord?.contextCategories || [],
      pmoCustomContext: pmoData.pmoRecord?.customContext || null,
      priority: pmoData.priority,
      teamSelectionRationale: pmoData.teamSelectionRationale,
      pmoAssessment: pmoData.pmoAssessment,
      category: documentCategory
    }
  };

  // Prepare headers for internal API authentication
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  // Add internal authentication headers for server-to-server calls
  const internalSecret = process.env.INTERNAL_API_SECRET;
  if (internalSecret) {
    headers['X-Internal-Auth'] = internalSecret;
    headers['X-Acting-As-User-Id'] = pmoData.userId;
    console.log(`[PMO Business Analysis] Using internal authentication for user: ${pmoData.userId}`);
  } else {
    console.warn('[PMO Business Analysis] INTERNAL_API_SECRET not configured, relying on session authentication');
  }

  // Create timeout controller with PMO notification timeout configuration
  const timeoutConfig = PMO_NOTIFICATION_TIMEOUTS;
  const { controller, timeoutId, cleanup } = createTimeoutController(timeoutConfig.requestTimeout);

  console.log(`[PMO Business Analysis] Starting collaboration request with ${Math.floor(timeoutConfig.requestTimeout / 1000)}s timeout`);

  try {
    // Create fetch options with enhanced timeout and keep-alive configuration
    const fetchOptions: RequestInit & { agent?: any } = {
      method: 'POST',
      headers: {
        ...headers,
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache'
      },
      body: JSON.stringify(collaborationBody),
      signal: controller.signal,
      keepalive: true
    };

    // Add custom HTTP agent for Node.js environment with extended timeouts
    if (typeof window === 'undefined') {
      try {
        const https = require('https');

        const agentOptions = {
          keepAlive: true,
          keepAliveMsecs: timeoutConfig.keepAliveTimeout,
          maxSockets: 10,
          maxFreeSockets: 2,
          timeout: timeoutConfig.requestTimeout,
          freeSocketTimeout: timeoutConfig.keepAliveTimeout,
          // Disable default socket timeout to prevent premature connection drops
          socketTimeout: 0
        };

        fetchOptions.agent = new https.Agent(agentOptions);
        console.log(`[PMO Business Analysis] Using custom HTTPS agent with ${Math.floor(timeoutConfig.keepAliveTimeout / 1000)}s keep-alive`);
      } catch (error) {
        console.warn('[PMO Business Analysis] Failed to create custom HTTP agent, using default:', error);
      }
    }

    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/business-analysis-agent-collaboration`, fetchOptions);

    cleanup();

    if (!response.ok) {
      throw new Error(`Business analysis collaboration API error: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    console.log(`[PMO Business Analysis] Collaboration completed successfully with requestId: ${result.requestId}`);

    return {
      success: true,
      requestId: result.requestId,
      message: 'Business analysis collaboration triggered successfully',
      collaborationData: result
    };
  } catch (error: any) {
    cleanup();

    // Enhanced timeout error handling
    if (error.name === 'AbortError') {
      const timeoutMinutes = Math.floor(timeoutConfig.requestTimeout / 60000);
      console.warn(`[PMO Business Analysis] Request timed out after ${timeoutMinutes} minutes, but analysis may still be running in background`);
      throw new TimeoutError(
        TimeoutErrorType.ABORT_TIMEOUT,
        timeoutConfig.requestTimeout,
        `Business analysis collaboration request timed out after ${timeoutMinutes} minutes. The analysis may still be processing in the background.`
      );
    }

    // Handle specific timeout errors from the underlying HTTP client
    if (error.cause?.code === 'UND_ERR_HEADERS_TIMEOUT') {
      console.warn('[PMO Business Analysis] Headers timeout error, but analysis may still be running in background');
      throw new TimeoutError(
        TimeoutErrorType.CONNECTION_TIMEOUT,
        timeoutConfig.requestTimeout,
        'Business analysis collaboration request experienced a headers timeout. The analysis may still be processing in the background.'
      );
    }

    // Handle body timeout errors
    if (error.cause?.code === 'UND_ERR_BODY_TIMEOUT') {
      console.warn('[PMO Business Analysis] Body timeout error, but analysis may still be running in background');
      throw new TimeoutError(
        TimeoutErrorType.REQUEST_TIMEOUT,
        timeoutConfig.requestTimeout,
        'Business analysis collaboration request experienced a body timeout. The analysis may still be processing in the background.'
      );
    }

    // Handle connection errors
    if (error.cause?.code === 'ECONNRESET' || error.cause?.code === 'ENOTFOUND') {
      console.error('[PMO Business Analysis] Connection error:', error.cause);
      throw new Error('Business analysis collaboration request failed due to connection issues. Please check your network connection and try again.');
    }

    console.error('[PMO Business Analysis] Unexpected error:', error);
    throw error;
  }
}

/**
 * Trigger codebase documentation generation for PMO delegation
 */
async function triggerCodebaseDocumentationGeneration(pmoData: any) {
  console.log(`🔧 Triggering codebase documentation generation for PMO ${pmoData.pmoId}`);

  try {
    // Extract selectedPaths from PMO Assessment content
    const selectedPaths = extractSelectedPathsFromAssessment(pmoData.pmoAssessment, pmoData.projectDescription);

    // Prepare the codebase documentation request using PMO Assessment as the primary specification
    const documentationRequest = {
      userId: pmoData.userId,
      selectedPaths: selectedPaths,
      description: pmoData.pmoAssessment || pmoData.projectDescription,
      customContext: `PMO Assessment Context:\n${pmoData.pmoAssessment}\n\nTeam Selection Rationale:\n${pmoData.teamSelectionRationale}\n\nPriority: ${pmoData.priority}\nCategory: ${pmoData.category}`,
      category: pmoData.title || pmoData.category || 'PMO Auto-Generated Documentation', // Add the missing category field
      documentationScope: 'full',
      outputFormat: 'markdown',
      includeArchitecture: true,
      includeApiDocs: true,
      includeDataFlow: true,
      pmoRecordId: pmoData.pmoId,
      autoTriggered: true,
      triggerSource: 'pmo-delegation'
    };

    console.log(`[PMO Codebase Documentation] Extracted paths: ${selectedPaths.join(', ')}`);
    console.log(`[PMO Codebase Documentation] Using PMO Assessment as primary specification`);
    console.log(`[PMO Codebase Documentation] PMO Data title: ${pmoData.title}`);
    console.log(`[PMO Codebase Documentation] PMO Data category: ${pmoData.category}`);
    console.log(`[PMO Codebase Documentation] Final category being sent: ${documentationRequest.category}`);

    // Call the codebase documentation streaming API
    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/codebase-documentation/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(documentationRequest)
    });

    if (!response.ok) {
      throw new Error(`Codebase documentation API error: ${response.status} ${response.statusText}`);
    }

    // Since this is a streaming endpoint, we'll get the response stream
    // For PMO delegation, we'll start the process and return immediately
    console.log(`✅ Codebase documentation generation initiated successfully for PMO ${pmoData.pmoId}`);

    return {
      success: true,
      message: 'Codebase documentation generation initiated successfully',
      selectedPaths: selectedPaths,
      documentationScope: 'full',
      pmoRecordId: pmoData.pmoId
    };

  } catch (error: any) {
    console.error(`❌ Error triggering codebase documentation generation for PMO ${pmoData.pmoId}:`, error);
    throw error;
  }
}

/**
 * Extract selectedPaths from PMO Assessment content intelligently
 */
function extractSelectedPathsFromAssessment(pmoAssessment: string, projectDescription: string): string[] {
  const content = `${pmoAssessment || ''} ${projectDescription || ''}`;

  console.log(`[PMO Path Extraction] Processing content length: ${content.length}`);
  console.log(`[PMO Path Extraction] Content preview: ${content.substring(0, 500)}...`);

  // First, look for explicit Windows/Unix paths mentioned in the content
  const explicitPathPatterns = [
    // Windows paths with spaces - more comprehensive pattern
    /([A-Za-z]:\\(?:[^"'\n\r<>|*?]+(?:\s+[^"'\n\r<>|*?]+)*)+)/g,
    // Unix/Linux paths (/path/to/folder)
    /(\/[^"'\n\r<>|*?\s]+)/g,
    // Relative paths (./path or ../path)
    /(\.[\/\\][^"'\n\r<>|*?\s]+)/g
  ];

  const extractedPaths = new Set<string>();

  // Extract explicit paths first
  for (const pattern of explicitPathPatterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      if (match[1]) {
        let path = match[1].trim();
        // Clean up the path
        path = path.replace(/['"`,;:()]/g, '');
        if (path.length > 3 && path.length < 200) {
          extractedPaths.add(path);
          console.log(`[PMO Codebase Documentation] Found explicit path: ${path}`);
        }
      }
    }
  }

  // If explicit paths found, use them
  if (extractedPaths.size > 0) {
    const paths = Array.from(extractedPaths);
    console.log(`[PMO Codebase Documentation] Using explicit paths: ${paths.join(', ')}`);
    return paths;
  }

  // Fallback: Look for common project directory patterns in lowercase content
  const contentLower = content.toLowerCase();
  const commonPatterns = [
    // Common project directories
    /(src|lib|components|pages|app|api|utils|helpers|services|models|controllers|views|public|assets|styles|tests|test|spec|docs|documentation)/gi
  ];

  for (const pattern of commonPatterns) {
    let match;
    while ((match = pattern.exec(contentLower)) !== null) {
      if (match[1]) {
        extractedPaths.add(match[1]);
      }
    }
  }

  // Default fallback paths if no specific paths found
  const defaultPaths = ['src', 'lib', 'components', 'app', 'api'];

  // Convert to array and filter
  let paths = Array.from(extractedPaths).filter(path => {
    // Filter out obviously invalid paths
    return path.length > 1;
  });

  // If no valid paths found, use intelligent defaults based on content
  if (paths.length === 0) {
    if (contentLower.includes('react') || contentLower.includes('component') || contentLower.includes('jsx')) {
      paths = ['src', 'components', 'pages'];
    } else if (contentLower.includes('api') || contentLower.includes('backend') || contentLower.includes('server')) {
      paths = ['src', 'api', 'lib'];
    } else if (contentLower.includes('frontend') || contentLower.includes('ui') || contentLower.includes('interface')) {
      paths = ['src', 'components', 'styles'];
    } else {
      paths = defaultPaths;
    }
  }

  // Limit to reasonable number of paths
  if (paths.length > 10) {
    paths = paths.slice(0, 10);
  }

  console.log(`[PMO Codebase Documentation] Extracted paths: ${paths.join(', ')}`);
  return paths;
}
