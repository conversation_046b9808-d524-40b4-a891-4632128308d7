/**
 * Test suite for Enhanced PMO Projects Task Agent
 * Tests the enhanced task detail extraction from Firebase Agent_Output collection
 */

import { PMOProjectsTaskAgent } from '../lib/agents/pmoProjectsTaskAgent';

// Mock Firebase Admin
jest.mock('../../components/firebase-admin', () => ({
  adminDb: {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn()
      }))
    }))
  }
}));

// Mock Groq AI
jest.mock('../lib/tools/groq-ai', () => ({
  processWithGroq: jest.fn()
}));

// Mock Google AI
jest.mock('../lib/tools/google-ai', () => ({
  processWithGoogleAI: jest.fn()
}));

// Mock Calendar Tool
jest.mock('../lib/tools/calendarTool', () => ({
  CalendarTool: jest.fn().mockImplementation(() => ({
    process: jest.fn().mockResolvedValue({
      success: true,
      result: '2025-01-15'
    })
  }))
}));

// Mock addTask function
jest.mock('../../app/lib/firebase/planner', () => ({
  addTask: jest.fn().mockResolvedValue('mock-task-id-123')
}));

describe('Enhanced PMO Projects Task Agent', () => {
  let agent: PMOProjectsTaskAgent;
  let mockFirebaseDoc: any;
  let mockProcessWithGroq: any;
  let mockProcessWithGoogleAI: any;

  beforeEach(() => {
    agent = new PMOProjectsTaskAgent();
    
    // Setup mocks
    const { adminDb } = require('../../components/firebase-admin');
    const { processWithGroq } = require('../lib/tools/groq-ai');
    const { processWithGoogleAI } = require('../lib/tools/google-ai');
    
    mockFirebaseDoc = {
      exists: true,
      data: jest.fn()
    };
    
    adminDb.collection().doc().get.mockResolvedValue(mockFirebaseDoc);
    mockProcessWithGroq = processWithGroq;
    mockProcessWithGoogleAI = processWithGoogleAI;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Enhanced Data Access Pattern', () => {
    test('should correctly access result.output field from Firebase document', async () => {
      // Mock Firebase document with correct structure
      const mockAgentOutputData = {
        agentType: 'strategic-director',
        category: 'PMO - Test Project',
        result: {
          output: '# COMPREHENSIVE STRATEGIC IMPLEMENTATION PLAN\n\n## TEAM ASSIGNMENTS\n\nTask 1 – Market Research Analysis • Assigned to : Research Team\nTask 2 – Content Strategy Development • Assigned to : Marketing Team',
          thinking: 'Strategic analysis thinking process...'
        },
        pmoMetadata: {
          category: 'Strategic Planning',
          teamName: 'Marketing'
        }
      };

      mockFirebaseDoc.data.mockReturnValue(mockAgentOutputData);

      // Mock LLM response
      const mockLLMResponse = JSON.stringify({
        tasks: [
          {
            title: 'Market Research Analysis',
            description: 'Comprehensive market research analysis including competitor analysis, market sizing, and customer segmentation',
            category: 'Research & Analysis',
            priority: 'High',
            teamAssignment: 'Research Team'
          }
        ],
        analysis: 'Successfully extracted tasks from strategic analysis',
        confidence: 0.9,
        projectContext: 'Strategic implementation plan for test project'
      });

      mockProcessWithGoogleAI.mockResolvedValue(mockLLMResponse);

      // Test the extraction
      const result = await agent.extractTasksFromAgentOutput(
        'test-request-id',
        'Test Project',
        'Test project description'
      );

      // Verify correct data access
      expect(result.success).toBe(true);
      expect(result.tasksCreated).toHaveLength(1);
      expect(result.tasksCreated[0].title).toBe('Market Research Analysis');
      expect(result.tasksCreated[0].teamAssignment).toBe('Research Team');
    });

    test('should handle missing result.output field gracefully', async () => {
      // Mock Firebase document without result.output
      const mockAgentOutputData = {
        agentType: 'strategic-director',
        category: 'PMO - Test Project',
        // Missing result.output field
        result: {
          thinking: 'Some thinking content...'
        }
      };

      mockFirebaseDoc.data.mockReturnValue(mockAgentOutputData);

      // Test the extraction
      const result = await agent.extractTasksFromAgentOutput(
        'test-request-id',
        'Test Project',
        'Test project description'
      );

      // Should fail gracefully
      expect(result.success).toBe(false);
      expect(result.error).toContain('No strategic analysis content found in result.output field');
    });
  });

  describe('Enhanced Strategic Content Processing', () => {
    test('should properly analyze strategic content structure', async () => {
      const mockAgentOutputData = {
        agentType: 'strategic-director',
        category: 'PMO - Test Project',
        result: {
          output: `# COMPREHENSIVE STRATEGIC IMPLEMENTATION PLAN

## TEAM ASSIGNMENTS

Task 1 – Validate Market Metrics • Assigned to : Research Team
Task 2 – Competitive Analysis Report • Assigned to : Research Team  
Task 3 – Messaging Framework & Personas • Assigned to : Marketing Team
Task 4 – Content Strategy Development • Assigned to : Marketing Team
Task 5 – Software Architecture Design • Assigned to : Software Design Team

## IMPLEMENTATION TIMELINE

Phase 1: Research and Analysis (Weeks 1-2)
Phase 2: Strategy Development (Weeks 3-4)
Phase 3: Implementation (Weeks 5-8)

## SUCCESS CRITERIA

- Market research completion with 95% confidence
- Messaging framework approval by stakeholders
- Technical architecture review passed`,
          thinking: 'Strategic analysis thinking...'
        }
      };

      mockFirebaseDoc.data.mockReturnValue(mockAgentOutputData);

      // Mock enhanced LLM response with rich detail
      const mockLLMResponse = JSON.stringify({
        tasks: [
          {
            title: 'Validate Market Metrics',
            description: 'Comprehensive market validation including: 1) Market size analysis with 95% confidence level, 2) Customer segmentation based on demographic and psychographic data, 3) Competitive landscape mapping with SWOT analysis, 4) Revenue potential assessment using TAM/SAM/SOM methodology, 5) Risk assessment for market entry strategies',
            category: 'Research & Analysis',
            priority: 'High',
            estimatedDuration: '2 weeks',
            teamAssignment: 'Research Team',
            dependencies: [],
            notes: 'Strategic context: Critical foundation for all subsequent strategic decisions. Success criteria: 95% confidence level in market data. Risk mitigation: Use multiple data sources for validation.'
          },
          {
            title: 'Messaging Framework & Personas',
            description: 'Develop comprehensive messaging framework including: 1) Brand positioning strategy aligned with market research findings, 2) Customer persona development with detailed behavioral profiles, 3) Value proposition articulation for each target segment, 4) Messaging hierarchy and tone of voice guidelines, 5) Competitive differentiation messaging',
            category: 'Content Creation',
            priority: 'High',
            estimatedDuration: '1 week',
            teamAssignment: 'Marketing Team',
            dependencies: ['Validate Market Metrics'],
            notes: 'Strategic context: Foundation for all marketing communications. Deliverable: Comprehensive messaging framework document with stakeholder approval required.'
          }
        ],
        analysis: 'Successfully extracted 2 detailed tasks from strategic analysis with comprehensive team assignments section. Prioritized tasks based on implementation timeline and dependencies.',
        confidence: 0.95,
        projectContext: 'Strategic implementation plan with clear team assignments, timeline, and success criteria. High-quality strategic analysis with detailed implementation guidance.'
      });

      mockProcessWithGoogleAI.mockResolvedValue(mockLLMResponse);

      const result = await agent.extractTasksFromAgentOutput(
        'test-request-id',
        'Test Project',
        'Test project description'
      );

      // Verify enhanced processing
      expect(result.success).toBe(true);
      expect(result.tasksCreated).toHaveLength(2);
      
      // Verify rich detail extraction
      const firstTask = result.tasksCreated[0];
      expect(firstTask.description).toContain('95% confidence level');
      expect(firstTask.description).toContain('TAM/SAM/SOM methodology');
      expect(firstTask.notes).toContain('Strategic context');
      expect(firstTask.estimatedDuration).toBe('2 weeks');
      
      const secondTask = result.tasksCreated[1];
      expect(secondTask.dependencies).toContain('Validate Market Metrics');
      expect(secondTask.teamAssignment).toBe('Marketing Team');
      expect(secondTask.description).toContain('Brand positioning strategy');
    });
  });

  describe('Data Structure Validation', () => {
    test('should validate and log data structure correctly', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      
      const mockAgentOutputData = {
        agentType: 'strategic-director',
        category: 'PMO - Test Project',
        result: {
          output: 'Test strategic content',
          thinking: 'Test thinking'
        },
        pmoMetadata: {
          category: 'Strategic Planning'
        }
      };

      mockFirebaseDoc.data.mockReturnValue(mockAgentOutputData);
      mockProcessWithGoogleAI.mockResolvedValue(JSON.stringify({
        tasks: [],
        analysis: 'Test analysis',
        confidence: 0.8,
        projectContext: 'Test context'
      }));

      await agent.extractTasksFromAgentOutput(
        'test-request-id',
        'Test Project',
        'Test project description'
      );

      // Verify data structure validation logging
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Data structure validation:')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('result.output exists: true')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('result.output length: 21')
      );

      consoleSpy.mockRestore();
    });
  });
});
