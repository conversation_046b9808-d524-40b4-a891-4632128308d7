'use client';

import { useState } from 'react';
import { Loader2, Wand2, Co<PERSON>, Check } from 'lucide-react';
import <PERSON>down<PERSON>enderer from 'components/MarkdownRenderer';
// Updated import to use SimplePromptOptimizer
import {
  simplePromptOptimizer,
  SimplePromptOptimizerOptions,
  SimplePromptOptimizerResult
} from 'lib/tools/simplePromptOptimizer';

export default function SimplePromptAgentTool() {
  const [originalPrompt, setOriginalPrompt] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null); // For displaying errors
  const [optimizationResult, setOptimizationResult] = useState<SimplePromptOptimizerResult | null>(null);
  const [includeExplanation, setIncludeExplanation] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);

  const handleOptimizePrompt = async () => {
    if (!originalPrompt.trim()) { // Check if trimmed prompt is empty
        setError("Please enter a prompt.");
        return;
    }

    if (originalPrompt.trim().length < 10) {
      setError("Please enter a longer prompt (at least 10 characters). Short prompts are difficult to optimize effectively.");
      return;
    }

    setLoading(true);
    setError(null); // Clear previous errors at the start of a new attempt
    // setOptimizationResult(null); // Optionally clear previous results, or let new result overwrite

    try {
      const options: SimplePromptOptimizerOptions = {
        originalPrompt,
        includeExplanation,
        modelOptions: {
          temperature: 0.7,
          maxTokens: 2000
        }
      };

      console.log("Sending options to optimizer:", options);
      const result: SimplePromptOptimizerResult = await simplePromptOptimizer.optimizePrompt(options);
      console.log("Optimizer result:", result);

      setOptimizationResult(result); // Always set the result to update UI fields

      if (!result.success) {
        // If optimization failed, display the error message from the result
        setError(result.error || 'Optimization failed for an unknown reason.');
      } else {
        setError(null); // Clear error if successful
        // If success is true but there's an error message (e.g. AI returned short prompt), show it
        if (result.error) {
            setError(result.error);
        }
      }
    } catch (err) { // This catches unexpected exceptions from the optimizer call itself
      console.error('Critical error calling prompt optimizer:', err);
      const errorMessage = err instanceof Error ? err.message : 'A critical system error occurred during optimization.';
      setError(errorMessage);
      // Ensure optimizationResult is set to show original prompt with error
      setOptimizationResult({
          success: false,
          optimizedPrompt: originalPrompt, // Show original prompt in its field
          error: errorMessage,
          explanation: includeExplanation ? `Optimization process failed: ${errorMessage}` : undefined
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle copying to clipboard
  const handleCopyToClipboard = async () => {
    // Use optimizedPrompt from optimizationResult, even if success is false (it might be the original prompt)
    if (optimizationResult?.optimizedPrompt) {
      await navigator.clipboard.writeText(optimizationResult.optimizedPrompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-zinc-800 p-4 border-b border-zinc-700">
        <div className="flex items-center space-x-2">
          <Wand2 className="h-5 w-5 text-red-500" />
          <p className="text-lg font-medium text-white">
            Prompt Optimizer
          </p>
        </div>
        <p className="text-sm text-zinc-400 mt-1">
          Optimize your prompts using AI
        </p>

        <div className="mt-3 p-3 bg-zinc-800/50 border border-zinc-700 rounded-md">
          <h3 className="text-xs font-medium text-zinc-300 mb-1">How it works:</h3>
          <ol className="list-decimal pl-5 text-xs text-zinc-400 space-y-1">
            <li>Enter a simple prompt you'd like to improve (e.g., "Tell me about climate change")</li>
            <li>The tool first analyzes your prompt to determine optimization goals</li>
            <li>Then it applies those goals to create an enhanced version of your prompt</li>
            <li>The optimized prompt will get you better results from AI systems</li>
          </ol>
          <p className="text-xs text-zinc-500 mt-2 italic">Note: This tool creates better prompts, not answers to your questions.</p>
        </div>
      </div>

      {/* Content */}
      <div className="flex flex-col md:flex-row h-full">
        {/* Input Section */}
        <div className="w-full md:w-1/2 p-4 border-r border-zinc-700 flex flex-col h-full">
          <div className="space-y-4 flex-1">
            <div>
              <label htmlFor="original-prompt" className="block text-sm font-medium text-zinc-300 mb-1">Original Prompt</label>
              <textarea
                id="original-prompt"
                placeholder="Enter your prompt here... (e.g., 'Tell me about climate change' or 'How to make pasta')"
                value={originalPrompt}
                onChange={(e) => {
                    setOriginalPrompt(e.target.value);
                    if (error && e.target.value.trim().length >= 10) setError(null); // Clear length error if corrected
                }}
                className="w-full h-40 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="include-explanation"
                checked={includeExplanation}
                onChange={(e) => setIncludeExplanation(e.target.checked)}
                className="h-4 w-4 rounded border-zinc-700 text-red-600 focus:ring-red-500 bg-zinc-800"
              />
              <label htmlFor="include-explanation" className="ml-2 block text-sm text-zinc-300">
                Include explanation of changes
              </label>
            </div>
          </div>

          <div className="mt-4">
            <button
              onClick={handleOptimizePrompt}
              disabled={loading || !originalPrompt.trim()} // Disable if trimmed prompt is empty
              className={`w-full flex items-center justify-center px-4 py-2 rounded-md text-white ${
                loading || !originalPrompt.trim()
                  ? 'bg-zinc-700 cursor-not-allowed'
                  : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 mr-2" />
                  Optimize Prompt
                </>
              )}
            </button>
          </div>
        </div>

        {/* Results Section */}
        <div className="w-full md:w-1/2 p-4 overflow-auto h-full">
          {error && ( // Display general error message prominently
            <div className="mb-4 p-4 bg-red-900/20 border border-red-700 rounded-md text-red-200">
              <p className="font-semibold">Error</p>
              <p className="text-sm mt-1">{error}</p>
              {/* Conditionally show troubleshooting if it's a generic error, not one from optimizer */}
              {!optimizationResult?.error && (
                <div className="mt-3 text-xs text-red-300/80">
                  <p className="font-medium">Troubleshooting tips:</p>
                  <ul className="list-disc pl-5 mt-1 space-y-1">
                    <li>Try a more detailed prompt that provides clear context</li>
                    <li>Check if the prompt is too short or vague</li>
                    <li>Ensure your prompt is in English</li>
                    <li>Try refreshing the page and trying again</li>
                    <li>Ensure your CLAUDE_API_KEY is correctly configured if running locally.</li>
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Display optimization result details even if there was an error, as it might contain fallback original prompt */}
          {optimizationResult ? (
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium text-zinc-300">Optimized Prompt</h3>
                  {optimizationResult.optimizedPrompt && (
                    <button
                      onClick={handleCopyToClipboard}
                      className="flex items-center text-xs text-zinc-400 hover:text-zinc-200 transition-colors"
                    >
                      {copied ? (
                        <>
                          <Check className="h-3.5 w-3.5 mr-1 text-green-500" />
                          <span className="text-green-500">Copied!</span>
                        </>
                      ) : (
                        <>
                          <Copy className="h-3.5 w-3.5 mr-1" />
                          <span>Copy</span>
                        </>
                      )}
                    </button>
                  )}
                </div>
                <div className="p-3 bg-zinc-800/50 border border-zinc-700 rounded-md">
                  <p className="text-white whitespace-pre-wrap">
                    {optimizationResult.optimizedPrompt || "No optimized prompt available."}
                  </p>
                </div>
                <p className="text-xs text-zinc-500 mt-1 italic">
                  Use this optimized prompt with any AI system to get better results.
                </p>
              </div>

              {/* Display explanation if available (either success or error explanation) */}
              {optimizationResult.explanation && (
                <div>
                  <h3 className="text-sm font-medium text-zinc-300 mb-2">Explanation</h3>
                  <div className="p-3 bg-zinc-800/50 border border-zinc-700 rounded-md">
                    <MarkdownRenderer content={optimizationResult.explanation} />
                  </div>
                </div>
              )}
            </div>
          ) : !error && !loading ? ( // Show initial message only if no error, not loading, and no result yet
            <div className="h-full flex items-center justify-center text-zinc-500">
              <p>Enter a prompt and click "Optimize Prompt" to see results</p>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}