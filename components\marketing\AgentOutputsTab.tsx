// components/marketing/AgentOutputsTab.tsx
'use client';

import React, { useState, useEffect } from 'react';
import MarkdownRenderer from '../MarkdownRenderer';
import AgentOutputModal from './AgentOutputModal';
import { FileText, Clock, User, Tag, ExternalLink, RefreshCw } from 'lucide-react';

interface AgentOutput {
  id: string;
  requestId: string;
  timestamp: { seconds: number; nanoseconds: number } | number | { _seconds: number; _nanoseconds: number };
  agentType: string;
  prompt: string;
  result: {
    thinking?: string;
    output: string;
    documentUrl?: string;
  };
  modelInfo: {
    provider: string;
    model: string;
  };
  isCollaboration?: boolean;
  agentMessages?: {
    from: string;
    to: string;
    message: string;
  }[];
}

export default function AgentOutputsTab() {
  const [outputs, setOutputs] = useState<AgentOutput[]>([]);
  const [selectedOutput, setSelectedOutput] = useState<AgentOutput | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalContent, setModalContent] = useState<{
    title: string;
    content: string;
    contentType: 'output' | 'thinking' | 'collaboration';
  } | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [lastTimestamp, setLastTimestamp] = useState<string | null>(null);
  const ITEMS_PER_PAGE = 10;

  const fetchOutputs = async (resetPage = true) => {
    if (resetPage) {
      setPage(1);
      setLastTimestamp(null);
      setLoading(true);
      setHasMore(true);
    } else {
      setLoadingMore(true);
    }

    try {
      // Add pagination parameters to the API call
      const currentPage = resetPage ? 1 : page;
      let url = `/api/agent-outputs?page=${currentPage}&limit=${ITEMS_PER_PAGE}`;

      // Add lastTimestamp for cursor-based pagination if we're loading more
      if (!resetPage && lastTimestamp) {
        url += `&lastTimestamp=${encodeURIComponent(lastTimestamp)}`;
      }

      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Error fetching agent outputs: ${response.statusText}`);
      }

      const data = await response.json();

      // Process the outputs to identify collaboration outputs
      const processedOutputs = data.results.map((output: AgentOutput) => {
        // Check if this is a collaboration output (Strategic Analysis)
        // Look for agent messages or specific patterns in the data
        const isCollaboration =
          (output.agentMessages && output.agentMessages.length > 0) ||
          (output.result && output.result.output && output.result.output.includes('Strategic Analysis'));

        return {
          ...output,
          isCollaboration
        };
      });

      // Update state based on whether we're resetting or loading more
      if (resetPage) {
        setOutputs(processedOutputs);
      } else {
        setOutputs(prev => [...prev, ...processedOutputs]);
      }

      // Save the lastTimestamp for the next pagination request
      if (data.lastTimestamp) {
        setLastTimestamp(data.lastTimestamp);
      }

      // Check if there are more results to load
      setHasMore(data.hasMore);

      // If we're loading more, increment the page number
      if (!resetPage) {
        setPage(currentPage + 1);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching agent outputs:', err);
      setError('Failed to load agent outputs');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // Function to load more results
  const loadMoreOutputs = () => {
    if (!loading && !loadingMore && hasMore) {
      fetchOutputs(false);
    }
  };

  useEffect(() => {
    fetchOutputs();
  }, []);

  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';

    // Handle different timestamp formats that might come from Firebase
    let date: Date;

    if (typeof timestamp === 'number') {
      // Handle timestamp as milliseconds
      date = new Date(timestamp);
    } else if (timestamp.seconds !== undefined && timestamp.nanoseconds !== undefined) {
      // Handle Firestore Timestamp format
      date = new Date(timestamp.seconds * 1000);
    } else if (timestamp._seconds !== undefined && timestamp._nanoseconds !== undefined) {
      // Handle serialized Firestore Timestamp format
      date = new Date(timestamp._seconds * 1000);
    } else if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      // Handle Firestore Timestamp object with toDate method
      date = timestamp.toDate();
    } else {
      // Fallback: try to parse as Date object
      date = new Date(timestamp);
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid timestamp format:', timestamp);
      return 'Invalid date';
    }

    // Format the date manually without using date-fns
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = months[date.getMonth()];
    const day = date.getDate();
    const year = date.getFullYear();

    // Format time (h:mm a)
    let hours = date.getHours();
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${month} ${day}, ${year} ${hours}:${minutes} ${ampm}`;
  };

  const formatAgentType = (type: string) => {
    return type
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const handleSelectOutput = (output: AgentOutput) => {
    setSelectedOutput(output);
  };

  const openOutputModal = (title: string, content: string, contentType: 'output' | 'thinking' | 'collaboration') => {
    setModalContent({
      title,
      content,
      contentType
    });
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Modal for full-screen content view */}
      {modalContent && (
        <AgentOutputModal
          isOpen={modalOpen}
          onClose={closeModal}
          title={modalContent.title}
          content={modalContent.content}
          contentType={modalContent.contentType}
          timestamp={selectedOutput ? formatTimestamp(selectedOutput.timestamp) : undefined}
          modelInfo={selectedOutput?.modelInfo}
        />
      )}

      <div className="md:col-span-1 bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-purple-300">Test Results</h3>
          <button
            onClick={() => fetchOutputs(true)}
            className="p-2 bg-gray-700 hover:bg-gray-600 rounded-full"
            title="Refresh outputs"
          >
            <RefreshCw className="h-4 w-4 text-purple-300" />
          </button>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-400">
            <p>{error}</p>
            <button
              onClick={() => fetchOutputs(true)}
              className="mt-4 px-4 py-2 bg-purple-700 hover:bg-purple-800 rounded-lg text-white"
            >
              Try Again
            </button>
          </div>
        ) : outputs.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            <p>No test results found</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-[600px] overflow-y-auto pr-2 custom-scrollbar">
            {outputs.map((output) => (
              <div
                key={output.id}
                onClick={() => handleSelectOutput(output)}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedOutput?.id === output.id
                    ? 'bg-purple-700/30 border border-purple-500/50'
                    : 'bg-gray-700 hover:bg-gray-600 border border-gray-600'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <Tag className="h-4 w-4 mr-2 text-purple-300" />
                    <span className="font-medium text-purple-200">
                      {formatAgentType(output.agentType)}
                    </span>
                    {output.isCollaboration && (
                      <span className="ml-2 px-1.5 py-0.5 text-xs bg-indigo-800 text-indigo-200 rounded">
                        Strategic Analysis
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-400 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatTimestamp(output.timestamp)}
                  </div>
                </div>
                <p className="text-sm text-gray-300 line-clamp-2 mb-2">
                  {output.prompt}
                </p>
                <div className="flex items-center text-xs text-gray-400">
                  <User className="h-3 w-3 mr-1" />
                  <span className="capitalize">{output.modelInfo.provider}</span>
                  <span className="mx-1">|</span>
                  <span>{output.modelInfo.model}</span>
                </div>
              </div>
            ))}

            {/* Load More button */}
            {hasMore && (
              <div className="flex justify-center mt-4 pb-2">
                <button
                  onClick={loadMoreOutputs}
                  disabled={loading || loadingMore}
                  className={`px-4 py-2 rounded-lg text-white ${
                    loading || loadingMore
                      ? 'bg-gray-600 cursor-not-allowed'
                      : 'bg-purple-700 hover:bg-purple-800'
                  }`}
                >
                  {loadingMore ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading...
                    </div>
                  ) : (
                    'Load More'
                  )}
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="md:col-span-2 bg-gray-800/50 rounded-lg p-4 border border-gray-700">
        {selectedOutput ? (
          <div>
            <div className="mb-4 pb-4 border-b border-gray-700">
              <h3 className="text-xl font-medium text-purple-200 mb-2">
                {selectedOutput.isCollaboration
                  ? "Strategic Analysis Result"
                  : `${formatAgentType(selectedOutput.agentType)} Test Result`}
              </h3>
              <div className="flex flex-wrap gap-y-2 text-sm text-gray-400">
                <div className="flex items-center mr-4">
                  <Clock className="h-4 w-4 mr-1 text-purple-300" />
                  {formatTimestamp(selectedOutput.timestamp)}
                </div>
                <div className="flex items-center mr-4">
                  <User className="h-4 w-4 mr-1 text-purple-300" />
                  <span className="capitalize">{selectedOutput.modelInfo.provider}</span>
                  <span className="mx-1">|</span>
                  <span>{selectedOutput.modelInfo.model}</span>
                </div>
                {selectedOutput.result.documentUrl && (
                  <a
                    href={selectedOutput.result.documentUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center text-purple-300 hover:text-purple-200"
                  >
                    <FileText className="h-4 w-4 mr-1" />
                    View Document
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                )}
              </div>
            </div>

            <div className="mb-4">
              <h4 className="text-md font-medium text-purple-300 mb-2">Prompt</h4>
              <div className="p-3 bg-gray-700/50 rounded-lg text-gray-300 text-sm">
                {selectedOutput.prompt}
              </div>
            </div>

            <div className="mb-4">
              <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                Output
                <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
              </h4>
              <div
                className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[400px] custom-scrollbar cursor-pointer hover:bg-gray-700 transition-colors"
                onClick={() => openOutputModal(
                  `${formatAgentType(selectedOutput.agentType)} Output`,
                  selectedOutput.result.output,
                  'output'
                )}
              >
                <MarkdownRenderer content={selectedOutput.result.output} />
              </div>
            </div>

            {selectedOutput.result.thinking && (
              <div>
                <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                  Agent Output Response
                  <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
                </h4>
                <div
                  className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[300px] custom-scrollbar cursor-pointer hover:bg-gray-700 transition-colors"
                  onClick={() => openOutputModal(
                    `${formatAgentType(selectedOutput.agentType)} Thinking`,
                    selectedOutput.result.thinking || '',
                    'thinking'
                  )}
                >
                  <MarkdownRenderer content={selectedOutput.result.thinking} />
                </div>
              </div>
            )}

            {selectedOutput.isCollaboration && selectedOutput.agentMessages && selectedOutput.agentMessages.length > 0 && (
              <div className="mt-4">
                <h4 className="text-md font-medium text-purple-300 mb-2 flex items-center">
                  Strategic Analysis Collaboration
                  <span className="ml-2 text-xs text-gray-400">(Click to view full screen)</span>
                </h4>
                <div
                  className="p-3 bg-gray-700/50 rounded-lg overflow-auto max-h-[300px] custom-scrollbar space-y-3 cursor-pointer hover:bg-gray-700 transition-colors"
                  onClick={() => {
                    // Combine all messages into a single markdown string
                    const collaborationContent = (selectedOutput.agentMessages || [])
                      .filter(message =>
                        (message.from === 'strategic-director' && message.to === 'user') ||
                        (message.from === 'user' && message.to === 'strategic-director') ||
                        (message.message && message.message.length > 100)
                      )
                      .map(message => {
                        const fromLabel = message.from === 'user' ? 'You' :
                          message.from.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                        const toLabel = message.to === 'user' ? 'You' :
                          message.to.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
                        return `### ${fromLabel} → ${toLabel}\n\n${message.message}\n\n---\n\n`;
                      })
                      .join('');

                    openOutputModal(
                      'Strategic Analysis Collaboration',
                      collaborationContent,
                      'collaboration'
                    );
                  }}
                >
                  {/* Filter out internal messages and only show key interactions */}
                  {selectedOutput.agentMessages
                    .filter(message =>
                      // Only show messages between strategic-director and user
                      // or messages with substantial content
                      (message.from === 'strategic-director' && message.to === 'user') ||
                      (message.from === 'user' && message.to === 'strategic-director') ||
                      (message.message && message.message.length > 100)
                    )
                    .map((message, index) => (
                      <div key={index} className={`p-2 border rounded-lg ${
                        message.from === 'strategic-director' && message.to === 'user'
                          ? 'border-purple-600/50 bg-purple-900/20'
                          : 'border-gray-600'
                      }`}>
                        <div className="flex items-center mb-1 text-sm">
                          <span className={`font-medium ${
                            message.from === 'strategic-director' ? 'text-purple-300' :
                            message.from === 'user' ? 'text-blue-300' : 'text-gray-300'
                          }`}>
                            {message.from === 'user' ? 'You' :
                             message.from.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                          </span>
                          <span className="mx-2 text-gray-400">→</span>
                          <span className={`font-medium ${
                            message.to === 'strategic-director' ? 'text-purple-300' :
                            message.to === 'user' ? 'text-blue-300' : 'text-gray-300'
                          }`}>
                            {message.to === 'user' ? 'You' :
                             message.to.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                          </span>
                        </div>
                        <div className="text-sm text-gray-300">
                          <MarkdownRenderer content={message.message} />
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full py-16 text-center text-gray-400">
            <FileText className="h-16 w-16 mb-4 opacity-20 text-purple-400" />
            <p className="text-lg mb-2">Select a test result to view details</p>
            <p className="text-sm max-w-md">
              Test results include the agent's output, thinking process (if enabled), and any generated documents.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}