/**
 * Test script for LLM-based intelligent journalist selection
 */

const testCases = [
  {
    title: "Corporate Financial Investigation",
    description: "Investigate potential financial irregularities and accounting fraud in vendor contracts, focusing on procurement processes and payment authorization systems.",
    expectedJournalists: ["financial-reporter", "investigative-journalist"],
    investigationType: "financial"
  },
  {
    title: "Technology Innovation Analysis",
    description: "Comprehensive analysis of emerging AI technologies and their impact on traditional industries, including regulatory implications and market disruption.",
    expectedJournalists: ["technology-analyst", "investigative-journalist"],
    investigationType: "technology"
  },
  {
    title: "Political Corruption Expose",
    description: "Deep dive investigation into allegations of political corruption, campaign finance violations, and influence peddling in government contracts.",
    expectedJournalists: ["political-correspondent", "investigative-journalist"],
    investigationType: "political"
  },
  {
    title: "Environmental Impact Study",
    description: "Investigative research on environmental damage caused by industrial operations, including water contamination and air quality violations.",
    expectedJournalists: ["environmental-reporter", "investigative-journalist"],
    investigationType: "environmental"
  },
  {
    title: "Social Affairs Investigation",
    description: "Investigation into social welfare program fraud and mismanagement, focusing on community impact and vulnerable populations.",
    expectedJournalists: ["social-affairs-reporter", "investigative-journalist"],
    investigationType: "social_affairs"
  }
];

async function testLLMJournalistSelection() {
  console.log("🤖 Testing LLM-Based Intelligent Journalist Selection\n");
  
  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.title}`);
      console.log(`📝 Description: ${testCase.description.substring(0, 100)}...`);
      console.log(`🎯 Investigation Type: ${testCase.investigationType}`);
      
      const response = await fetch('http://localhost:3000/api/investigative-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'recommend-journalists',
          title: testCase.title,
          description: testCase.description,
          investigationType: testCase.investigationType,
          maxJournalists: 3
        })
      });
      
      const result = await response.json();
      
      if (result.success && result.data) {
        const selectedJournalists = result.data;
        console.log(`🤖 LLM Selected Journalists:`);
        selectedJournalists.forEach((journalist, index) => {
          console.log(`   ${index + 1}. ${journalist.name} (${journalist.id})`);
          console.log(`      Specialty: ${journalist.specialty}`);
          console.log(`      Style: ${journalist.investigationStyle}`);
        });
        
        // Check if selection makes sense
        const selectedIds = selectedJournalists.map(j => j.id);
        const hasRelevantJournalist = testCase.expectedJournalists.some(expected => 
          selectedIds.includes(expected)
        );
        
        const hasInvestigativeJournalist = selectedIds.includes('investigative-journalist');
        
        const score = (hasRelevantJournalist ? 50 : 0) + (hasInvestigativeJournalist ? 50 : 0);
        const status = score >= 50 ? "✅ GOOD" : "⚠️ REVIEW";
        
        console.log(`📊 Selection Quality: ${score}% ${status}`);
        console.log(`   - Relevant specialist: ${hasRelevantJournalist ? '✅' : '❌'}`);
        console.log(`   - Investigative journalist: ${hasInvestigativeJournalist ? '✅' : '❌'}`);
        
      } else {
        console.log(`❌ API Error: ${result.error}`);
      }
      
      console.log(""); // Empty line for readability
      
    } catch (error) {
      console.log(`❌ Test Error: ${error.message}\n`);
    }
  }
}

async function testFallbackMechanism() {
  console.log("🔄 Testing Fallback Mechanism\n");
  
  try {
    // Test with invalid/empty data to trigger fallback
    const response = await fetch('http://localhost:3000/api/investigative-research', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'recommend-journalists',
        title: "", // Empty title to potentially trigger fallback
        description: "", // Empty description to potentially trigger fallback
        maxJournalists: 3
      })
    });
    
    const result = await response.json();
    
    if (result.success && result.data) {
      console.log("✅ Fallback mechanism working - returned default journalists");
      result.data.forEach((journalist, index) => {
        console.log(`   ${index + 1}. ${journalist.name} (${journalist.id})`);
      });
    } else {
      console.log(`⚠️ Fallback test result: ${result.error || 'No data returned'}`);
    }
    
  } catch (error) {
    console.log(`❌ Fallback test error: ${error.message}`);
  }
}

async function testEndToEndWorkflow() {
  console.log("🔗 Testing End-to-End PMO → Investigative Research Workflow\n");
  
  const testRequest = {
    title: "Corporate Fraud Investigation",
    description: "Investigate potential financial fraud in corporate accounting practices, focusing on revenue recognition irregularities and expense manipulation schemes.",
    investigationType: "financial"
  };
  
  try {
    console.log("Step 1: Getting LLM journalist recommendations...");
    
    const journalistResponse = await fetch('http://localhost:3000/api/investigative-research', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'recommend-journalists',
        title: testRequest.title,
        description: testRequest.description,
        investigationType: testRequest.investigationType,
        maxJournalists: 3
      })
    });
    
    const journalistResult = await journalistResponse.json();
    
    if (journalistResult.success && journalistResult.data) {
      const selectedJournalistIds = journalistResult.data.map(j => j.id);
      console.log(`✅ LLM selected journalists: ${selectedJournalistIds.join(', ')}`);
      
      console.log("Step 2: Starting investigation with selected journalists...");
      
      const investigationResponse = await fetch('http://localhost:3000/api/investigative-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'conduct', // Changed from 'investigate' to 'conduct' to avoid duplicate storage
          pmoId: 'test_pmo_001',
          title: testRequest.title,
          description: testRequest.description,
          investigationType: testRequest.investigationType,
          selectedJournalistIds: selectedJournalistIds,
          priority: 'High',
          userId: '<EMAIL>'
        })
      });
      
      const investigationResult = await investigationResponse.json();
      
      if (investigationResult.success) {
        console.log("✅ End-to-end workflow successful!");
        console.log(`   Investigation ID: ${investigationResult.data?.investigationId || 'N/A'}`);
      } else {
        console.log(`❌ Investigation failed: ${investigationResult.error}`);
      }
      
    } else {
      console.log(`❌ Journalist selection failed: ${journalistResult.error}`);
    }
    
  } catch (error) {
    console.log(`❌ End-to-end test error: ${error.message}`);
  }
}

// Run tests
if (typeof window === 'undefined') {
  console.log("🧪 Starting LLM-Based Journalist Selection Tests\n");
  
  testLLMJournalistSelection()
    .then(() => testFallbackMechanism())
    .then(() => testEndToEndWorkflow())
    .then(() => console.log("\n🎉 All tests completed!"))
    .catch(console.error);
}

module.exports = { 
  testCases, 
  testLLMJournalistSelection, 
  testFallbackMechanism, 
  testEndToEndWorkflow 
};
