/**
 * Markdown Renderer Tool
 *
 * This tool provides markdown processing functionality extracted from the MarkdownRenderer component.
 * It ensures consistent markdown rendering across the application, including PDFs.
 */

/**
 * Efficiently preprocesses markdown content with minimal transformations
 * Preserves indentation and structure while ensuring proper rendering
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Minimally processed markdown
 */
function preprocessMarkdown(markdown: string): string {
  // Ensure markdown is a string
  if (!markdown || typeof markdown !== 'string') return "";

  // Identify code blocks to protect them from processing
  const codeBlockRegex = /```[\s\S]*?```/g;
  const codeBlocks: string[] = [];

  // Temporarily replace code blocks with placeholders
  let processedMarkdown = markdown.replace(codeBlockRegex, match => {
    const placeholder = `CODE_BLOCK_${codeBlocks.length}`;
    codeBlocks.push(match);
    return placeholder;
  });

  // Single-pass essential formatting corrections
  processedMarkdown = processedMarkdown
    // Normalize line endings to ensure consistent processing
    .replace(/\r\n/g, '\n')
    // Replace 3+ consecutive newlines with exactly 2
    .replace(/\n{3,}/g, '\n\n')
    // Ensure headers are followed by blank lines
    .replace(/^(#{1,6}[^\n]*)\n(?!\n)/gm, '$1\n\n')
    // Ensure list items in the same list stay grouped together
    .replace(/^(\s*[-*+]\s[^\n]*)\n(?!\n|\s*[-*+])/gm, '$1\n\n')
    // Ensure consistent formatting for character dialogue lines
    // This matches patterns like "**1. Character**: Text" and ensures consistent formatting
    .replace(/^\*\*(\d+)\. ([^*:]+)\*\*:(.+)$/gm, '**$1. $2**: $3')
    // Ensure character names are consistently formatted
    .replace(/^([A-Z][A-Za-z]*(?:\s[A-Z][A-Za-z]*)?):(.+)$/gm, '**$1**: $2')
    // Ensure line numbers are consistently formatted when they appear alone
    .replace(/^(\d+)\. /gm, '**$1**. ')
    // Ensure line numbers are consistently formatted when they appear with character names
    .replace(/^(\d+)\. ([A-Z][A-Za-z]*(?:\s[A-Z][A-Za-z]*)?):/gm, '**$1. $2**:');

  // Restore code blocks
  processedMarkdown = processedMarkdown.replace(/CODE_BLOCK_(\d+)/g, (_, index) =>
    codeBlocks[parseInt(index)]
  );

  return processedMarkdown.trim();
}

/**
 * Converts markdown to a format suitable for PDF rendering
 * Handles headers, lists, bold text, etc.
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Processed markdown ready for PDF rendering
 */
function markdownToPdfFormat(markdown: string): string {
  // First preprocess the markdown for consistent structure
  const processedMarkdown = preprocessMarkdown(markdown);

  // Enhanced processing for PDF rendering
  let pdfReadyMarkdown = processedMarkdown;

  // Identify code blocks to protect them from processing
  const codeBlockRegex = /```[\s\S]*?```/g;
  const codeBlocks: string[] = [];

  // Temporarily replace code blocks with placeholders
  pdfReadyMarkdown = pdfReadyMarkdown.replace(codeBlockRegex, match => {
    const placeholder = `CODE_BLOCK_${codeBlocks.length}`;
    codeBlocks.push(match);
    return placeholder;
  });

  // Process headings to ensure proper formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
    const level = hashes.length;
    const fontSize = 24 - ((level - 1) * 2); // h1=24, h2=22, h3=20, etc.
    return `<h${level} style="font-size: ${fontSize}px; font-weight: bold; margin-top: 16px; margin-bottom: 8px;">${title}</h${level}>`;
  });

  // Process lists for better formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(\s*)[-*+]\s+(.+)$/gm, (match, indent, content) => {
    const indentSize = indent.length;
    return `${indent}• ${content}`;
  });

  // Process numbered lists
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(\s*)(\d+)\.\s+(.+)$/gm, (match, indent, num, content) => {
    return `${indent}${num}. ${content}`;
  });

  // Process bold text
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/(\*\*|__)(.*?)\1/g, '<strong>$2</strong>');

  // Process italic text
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/(\*|_)(.*?)\1/g, '<em>$2</em>');

  // Process links
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

  // Process blockquotes
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^>\s+(.+)$/gm, '<blockquote style="border-left: 4px solid #ccc; padding-left: 8px; margin-left: 0;">$1</blockquote>');

  // Restore code blocks with formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/CODE_BLOCK_(\d+)/g, (_, index) => {
    const codeBlock = codeBlocks[parseInt(index)];
    // Strip the backticks and language identifier
    const code = codeBlock.replace(/```(?:\w+)?\n([\s\S]*?)```/g, '$1');
    return `<pre style="background-color: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace;">${code}</pre>`;
  });

  return pdfReadyMarkdown;
}

/**
 * Extracts plain text from markdown for search indexing
 * Removes markdown syntax while preserving content
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Plain text without markdown syntax
 */
function markdownToPlainText(markdown: string): string {
  // First preprocess the markdown
  let processedMarkdown = preprocessMarkdown(markdown);

  // Remove markdown syntax
  processedMarkdown = processedMarkdown
    // Remove headers
    .replace(/^#{1,6}\s+(.+)$/gm, '$1')
    // Remove bold/italic
    .replace(/(\*\*|__)(.*?)\1/g, '$2')
    .replace(/(\*|_)(.*?)\1/g, '$2')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`([^`]+)`/g, '$1')
    // Remove links but keep text
    .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
    // Remove images
    .replace(/!\[([^\]]+)\]\([^\)]+\)/g, '$1')
    // Remove blockquotes
    .replace(/^>\s+(.+)$/gm, '$1')
    // Remove list markers
    .replace(/^(\s*)[-*+]\s+/gm, '$1')
    .replace(/^(\s*)\d+\.\s+/gm, '$1');

  return processedMarkdown.trim();
}

/**
 * Identifies and extracts sections from markdown content
 * Useful for creating a table of contents or section navigation
 *
 * @param {string} markdown - Raw markdown content
 * @return {Array<{level: number, title: string, content: string}>} Array of sections
 */
function extractMarkdownSections(markdown: string): Array<{level: number, title: string, content: string}> {
  const processedMarkdown = preprocessMarkdown(markdown);
  const lines = processedMarkdown.split('\n');
  const sections: Array<{level: number, title: string, content: string}> = [];

  let currentSection: {level: number, title: string, content: string} | null = null;
  let contentBuffer: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);

    if (headerMatch) {
      // If we have a current section, save it before starting a new one
      if (currentSection) {
        currentSection.content = contentBuffer.join('\n');
        sections.push(currentSection);
        contentBuffer = [];
      }

      // Start a new section
      currentSection = {
        level: headerMatch[1].length,
        title: headerMatch[2],
        content: ''
      };
    } else if (currentSection) {
      // Add to current section's content
      contentBuffer.push(line);
    }
  }

  // Don't forget the last section
  if (currentSection) {
    currentSection.content = contentBuffer.join('\n');
    sections.push(currentSection);
  }

  return sections;
}

export class MarkdownRendererTool {
  /**
   * Process markdown for consistent rendering
   */
  preprocessMarkdown(markdown: string): string {
    return preprocessMarkdown(markdown);
  }

  /**
   * Format markdown for PDF rendering
   */
  markdownToPdfFormat(markdown: string): string {
    return markdownToPdfFormat(markdown);
  }

  /**
   * Convert markdown to plain text
   */
  markdownToPlainText(markdown: string): string {
    return markdownToPlainText(markdown);
  }

  /**
   * Extract sections from markdown
   */
  extractSections(markdown: string): Array<{level: number, title: string, content: string}> {
    return extractMarkdownSections(markdown);
  }
}

// Export a singleton instance
export const markdownRendererTool = new MarkdownRendererTool();
