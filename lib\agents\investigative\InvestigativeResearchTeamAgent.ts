/**
 * Investigative Research Team Agent
 * 
 * This class implements the TeamAgent interface to integrate the Investigative Research Agent
 * with the existing PMO team agent framework. It serves as a bridge between the specialized
 * investigative research functionality and the standardized team agent interface.
 */

import { Task } from '../../../admin/planner/types';
import { 
  TeamAgent, 
  InvestigativeResearchTeamAgentOptions, 
  InvestigativeResearchTeamAgentResult,
  TeamAgentStreamUpdate
} from '../pmo/TeamAgentInterfaces';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { InvestigativeResearchAgentManager } from './InvestigativeResearchAgentManager';
import { InvestigationType, InvestigationResult } from './InvestigativeResearchAgent';
import { PMOInvestigativeIntegration } from './PMOInvestigativeIntegration';

/**
 * Team Agent implementation for Investigative Research
 */
export class InvestigativeResearchTeamAgent implements TeamAgent {
  private agentManager: InvestigativeResearchAgentManager;
  private pmoIntegration: PMOInvestigativeIntegration;
  private options: InvestigativeResearchTeamAgentOptions;

  constructor(options: InvestigativeResearchTeamAgentOptions) {
    this.options = options;
    this.agentManager = new InvestigativeResearchAgentManager({
      userId: options.userId,
      defaultLlmProvider: 'openai',
      defaultLlmModel: 'gpt-o3-2025-04-16'
    });
    this.pmoIntegration = new PMOInvestigativeIntegration(options.userId);
  }

  /**
   * Process a task using the Investigative Research Agent
   * Implements the core TeamAgent interface method
   */
  async processTask(task: Task): Promise<InvestigativeResearchTeamAgentResult> {
    console.log(`InvestigativeResearchTeamAgent: Processing task ${task.id}`);

    try {
      // Send initial stream update
      this.sendStreamUpdate('analyzing-task', 'Analyzing task for investigative research requirements...');

      // Determine investigation type from task
      const investigationType = this.determineInvestigationType(task);
      
      // Get recommended journalists if not specified
      const selectedJournalistIds = this.options.selectedJournalistIds || 
        this.getRecommendedJournalists(investigationType);

      // Send processing update
      this.sendStreamUpdate('processing-task', `Starting ${investigationType} investigation with ${selectedJournalistIds.length} journalists...`);

      // Create PMO investigation request
      const investigationRequest = {
        pmoId: task.projectId || `task_${task.id}`,
        title: task.title,
        description: task.description,
        investigationType,
        selectedJournalistIds,
        criteriaModel: this.options.criteriaModel,
        optimizationModel: this.options.optimizationModel,
        assessmentModel: this.options.assessmentModel,
        consolidate: this.options.consolidateReport !== false,
        consolidationModel: this.options.consolidationModel,
        userId: this.options.userId,
        priority: this.mapTaskPriorityToPMOPriority(task.priority)
      };

      // Send generation update
      this.sendStreamUpdate('generating-output', 'Conducting multi-journalist investigation...');

      // Conduct the investigation
      const result = await this.agentManager.conductPMOInvestigation(investigationRequest);

      // Generate output documents (PDF report)
      const outputDocumentIds = await this.generateOutputDocuments(result);

      // Send completion update
      this.sendStreamUpdate('complete', 'Investigation completed successfully');

      // Format result according to team agent interface
      const teamAgentResult: InvestigativeResearchTeamAgentResult = {
        success: true,
        taskId: task.id,
        output: this.formatInvestigationOutput(result),
        outputDocumentIds,
        investigationId: result.investigationId,
        investigationType: result.investigationType,
        journalistResponses: result.journalistResponses,
        criteria: result.criteria,
        optimizedPrompt: result.optimizedPrompt,
        assessment: result.assessment,
        consolidatedReport: result.consolidatedReport || undefined,
        keyFindings: result.keyFindings,
        recommendations: result.recommendations,
        sources: result.sources,
        modelConfiguration: {
          criteriaModel: result.criteriaModel,
          optimizationModel: result.optimizationModel,
          assessmentModel: result.assessmentModel,
          consolidationModel: result.consolidationModel || undefined
        }
      };

      console.log(`InvestigativeResearchTeamAgent: Task ${task.id} completed successfully`);
      return teamAgentResult;

    } catch (error) {
      console.error(`InvestigativeResearchTeamAgent: Task ${task.id} failed:`, error);
      
      return {
        success: false,
        taskId: task.id,
        output: '',
        outputDocumentIds: [],
        error: error instanceof Error ? error.message : String(error),
        investigationId: '',
        investigationType: InvestigationType.INVESTIGATIVE,
        journalistResponses: [],
        criteria: '',
        optimizedPrompt: '',
        assessment: '',
        keyFindings: [],
        recommendations: [],
        sources: [],
        modelConfiguration: {
          criteriaModel: '',
          optimizationModel: '',
          assessmentModel: ''
        }
      };
    }
  }

  /**
   * Determine investigation type from task content
   */
  private determineInvestigationType(task: Task): InvestigationType {
    if (this.options.investigationType) {
      return this.options.investigationType as InvestigationType;
    }

    const content = `${task.title} ${task.description}`.toLowerCase();

    // Use keyword-based classification
    if (content.includes('financial') || content.includes('market') || content.includes('economic')) {
      return InvestigationType.FINANCIAL;
    }
    if (content.includes('political') || content.includes('government') || content.includes('policy')) {
      return InvestigationType.POLITICAL;
    }
    if (content.includes('technology') || content.includes('tech') || content.includes('cyber')) {
      return InvestigationType.TECHNOLOGY;
    }
    if (content.includes('social') || content.includes('community') || content.includes('human')) {
      return InvestigationType.SOCIAL_AFFAIRS;
    }
    if (content.includes('corporate') || content.includes('business') || content.includes('company')) {
      return InvestigationType.CORPORATE;
    }
    if (content.includes('environment') || content.includes('climate') || content.includes('sustainability')) {
      return InvestigationType.ENVIRONMENTAL;
    }
    if (content.includes('story') || content.includes('narrative') || content.includes('feature')) {
      return InvestigationType.FEATURE;
    }

    return InvestigationType.INVESTIGATIVE;
  }

  /**
   * Get recommended journalists for investigation type
   */
  private getRecommendedJournalists(investigationType: InvestigationType): string[] {
    const recommended = this.agentManager.getRecommendedJournalists(investigationType);
    return recommended.slice(0, 3).map(j => j.id);
  }

  /**
   * Map task priority to PMO priority
   */
  private mapTaskPriorityToPMOPriority(taskPriority: string): 'Low' | 'Medium' | 'High' | 'Critical' {
    switch (taskPriority?.toLowerCase()) {
      case 'low':
        return 'Low';
      case 'medium':
        return 'Medium';
      case 'high':
        return 'High';
      case 'critical':
        return 'Critical';
      default:
        return 'Medium';
    }
  }

  /**
   * Format investigation result for team agent output
   */
  private formatInvestigationOutput(result: InvestigationResult): string {
    let output = `# Investigative Research Report\n\n`;
    output += `**Investigation ID:** ${result.investigationId}\n`;
    output += `**Investigation Type:** ${result.investigationType.toUpperCase()}\n`;
    output += `**Conducted:** ${result.createdAt.toLocaleDateString()}\n\n`;

    output += `## Executive Summary\n\n`;
    output += `This investigation employed ${result.journalistResponses.length} specialized journalist AI agents to conduct comprehensive research on: "${result.originalPrompt}"\n\n`;

    output += `## Key Findings\n\n`;
    result.keyFindings.forEach((finding, index) => {
      output += `${index + 1}. ${finding}\n`;
    });
    output += `\n`;

    output += `## Recommendations\n\n`;
    result.recommendations.forEach((rec, index) => {
      output += `${index + 1}. ${rec}\n`;
    });
    output += `\n`;

    if (result.consolidatedReport) {
      output += `## Consolidated Investigation Report\n\n`;
      output += `${result.consolidatedReport}\n\n`;
    }

    output += `## Investigation Methodology\n\n`;
    output += `**Criteria Model:** ${result.criteriaModel} (${result.criteriaProvider})\n`;
    output += `**Optimization Model:** ${result.optimizationModel} (${result.optimizationProvider})\n`;
    output += `**Assessment Model:** ${result.assessmentModel} (${result.assessmentProvider})\n`;
    if (result.consolidationModel) {
      output += `**Consolidation Model:** ${result.consolidationModel} (${result.consolidationProvider})\n`;
    }

    output += `\n**Journalist Agents Used:**\n`;
    result.journalistResponses.forEach((response, index) => {
      output += `${index + 1}. ${response.journalistName} (${response.model} via ${response.provider})\n`;
    });

    return output;
  }

  /**
   * Generate output documents (PDF reports)
   */
  private async generateOutputDocuments(result: InvestigationResult): Promise<string[]> {
    // In a real implementation, this would generate and store PDF documents
    // For now, we'll return a placeholder document ID
    const documentId = `investigation_report_${result.investigationId}_${Date.now()}`;
    
    console.log(`Generated investigation report document: ${documentId}`);
    
    return [documentId];
  }

  /**
   * Send stream update if callback is provided
   */
  private sendStreamUpdate(stage: TeamAgentStreamUpdate['stage'], message: string, data?: any): void {
    if (this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        message,
        data
      });
    }
  }

  /**
   * Get team agent capabilities
   */
  static getCapabilities() {
    return {
      canGenerateImages: false,
      canGenerateDocuments: true,
      canGenerateCharts: false,
      canPerformResearch: true,
      canWriteCode: false,
      canAnalyzeData: true,
      canCreatePresentations: false
    };
  }

  /**
   * Get team information
   */
  static getTeamInfo() {
    return {
      teamId: AgenticTeamId.InvestigativeResearch,
      teamName: 'Investigative Research Team',
      description: 'Conducts comprehensive investigative research using specialized journalist AI agents and multi-LLM analysis.',
      capabilities: InvestigativeResearchTeamAgent.getCapabilities()
    };
  }
}
