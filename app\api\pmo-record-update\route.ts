import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { updatePMORecord } from '../../../lib/firebase/pmoCollection';

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { recordId, ...updateData } = body;

    // Validate request body
    if (!recordId) {
      return NextResponse.json({ error: 'Missing recordId' }, { status: 400 });
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return NextResponse.json({ error: 'Missing update data' }, { status: 400 });
    }

    // Update the PMO record
    await updatePMORecord(session.user.email, recordId, updateData);

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'PMO record updated successfully'
    });

  } catch (error: any) {
    console.error('Error updating PMO record:', error);
    return NextResponse.json(
      { 
        success: false,
        error: error.message || 'Failed to update PMO record' 
      },
      { status: 500 }
    );
  }
}
