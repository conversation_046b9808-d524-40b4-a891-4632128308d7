# Selected Paths Implementation

## Problem Summary

The codebase documentation system was not respecting user-selected paths. When users selected specific directories (e.g., `components`, `hooks`) in the frontend, the backend would still scan the entire project root directory instead of limiting the analysis to the selected paths.

### Root Cause
The `findCodeFiles` method in `CodebaseIndexingTool` only accepted a single `rootPath` parameter and had no mechanism to limit scanning to user-selected paths.

## Solution Overview

Implemented end-to-end support for targeted path scanning by:

1. **Frontend Enhancement**: Added `rootPath` to API requests
2. **Backend Interface Update**: Added `selectedPaths` to `CodebaseIndexingOptions`
3. **Core Logic Enhancement**: Modified `findCodeFiles` to support targeted scanning
4. **Integration Fix**: Updated orchestrator agent to pass selected paths to indexing tool

## Changes Made

### 1. Frontend Changes

**File**: `components/PMO/SystemsDocumentationTab.tsx`

```typescript
// Added rootPath to request body
const requestBody = {
  userId: user.email,
  rootPath: currentPath, // ✅ NEW: Root path for proper file discovery
  selectedPaths,
  description: `Comprehensive codebase analysis for: ${documentTitle.trim()}`,
  // ... other fields
};
```

### 2. Backend Interface Changes

**File**: `lib/tools/codebase-indexing-tool.ts`

```typescript
// Updated interface to include selectedPaths
export interface CodebaseIndexingOptions {
  rootPath: string;
  userId: string;
  projectName: string;
  selectedPaths?: string[]; // ✅ NEW: Specific paths selected by user
  excludePatterns?: string[];
  includeExtensions?: string[];
  // ... other fields
}
```

### 3. Core Logic Enhancement

**File**: `lib/tools/codebase-indexing-tool.ts`

```typescript
// Enhanced findCodeFiles method signature
private async findCodeFiles(
  rootPath: string,
  excludePatterns: string[],
  includeExtensions: string[],
  verbose: boolean = false,
  selectedPaths?: string[] // ✅ NEW: Optional targeted paths
): Promise<string[]>

// Updated scanning logic
const pathsToScan = (selectedPaths && selectedPaths.length > 0) 
  ? selectedPaths 
  : [normalizedRootPath];

// Process each target path (directory or file)
const discoveryPromises = pathsToScan.map(async (startPath) => {
  const normalizedStartPath = path.resolve(startPath);
  const stats = await fs.stat(normalizedStartPath);
  
  if (stats.isDirectory()) {
    // Traverse directory
    const relativeToRoot = path.relative(normalizedRootPath, normalizedStartPath);
    await traverse(normalizedStartPath, relativeToRoot);
  } else if (stats.isFile()) {
    // Process single file
    const ext = path.extname(normalizedStartPath).toLowerCase();
    if (includeExtSet.has(ext)) {
      files.push(normalizedStartPath);
    }
  }
});
```

### 4. Integration Fix

**File**: `lib/agents/pmo/CodebaseDocumentationOrchestratorAgent.ts`

```typescript
// Updated indexing call to pass selectedPaths
const result = await codebaseIndexingTool.indexCodebase({
  rootPath,
  userId: this.options.userId,
  projectName: projectName,
  selectedPaths: selectedPaths, // ✅ NEW: Pass user's selected paths
  excludePatterns: [/* ... */],
  includeExtensions: [/* ... */]
});
```

## Behavior Changes

### Before Implementation
- User selects specific directories in frontend
- Backend ignores selection and scans entire project root
- Results include files from unselected directories
- Inefficient processing of large codebases

### After Implementation
- User selects specific directories/files in frontend
- Backend respects selection and only scans chosen paths
- Results limited to selected paths only
- Efficient targeted analysis

## Supported Selection Types

1. **Directory Selection**: Scans all files within selected directories
2. **File Selection**: Processes individual selected files
3. **Mixed Selection**: Handles combination of files and directories
4. **Fallback Behavior**: If no paths selected, scans entire rootPath

## Example Usage

```typescript
// Scan only selected paths
const result = await codebaseIndexingTool.indexCodebaseDirect({
  rootPath: '/project/root',
  userId: 'user-123',
  projectName: 'my-app',
  selectedPaths: [
    '/project/root/src/components',
    '/project/root/src/hooks',
    '/project/root/lib/utils.ts'
  ]
});

// Fallback: scan entire rootPath if no selectedPaths
const result2 = await codebaseIndexingTool.indexCodebaseDirect({
  rootPath: '/project/root',
  userId: 'user-123',
  projectName: 'my-app'
  // No selectedPaths - scans entire rootPath
});
```

## Testing

Created comprehensive integration tests in `test/selected-paths-integration.test.js` covering:

- ✅ Full rootPath scanning (fallback behavior)
- ✅ Targeted directory scanning
- ✅ Single file selection
- ✅ Mixed file/directory selection
- ✅ Non-existent path handling
- ✅ Integration with `indexCodebaseDirect`

## Benefits

1. **Precision**: Users get exactly what they selected
2. **Performance**: Faster processing by avoiding unnecessary file scanning
3. **Cost Optimization**: Reduced token usage and API costs
4. **User Experience**: Predictable behavior matching user expectations
5. **Scalability**: Efficient handling of large codebases

## Backward Compatibility

- ✅ Existing code without `selectedPaths` continues to work (scans entire rootPath)
- ✅ All existing API endpoints remain functional
- ✅ No breaking changes to public interfaces

## Future Enhancements

1. **Path Validation**: Add frontend validation for selected paths
2. **Progress Tracking**: Show per-path progress in streaming responses
3. **Smart Suggestions**: Recommend related paths based on dependencies
4. **Exclusion Overrides**: Allow path-specific exclusion patterns
