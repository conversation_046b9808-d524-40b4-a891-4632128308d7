/**
 * Strategic Chart Generator Utility
 *
 * Centralized chart generation logic for Strategic Director Agent.
 * This module contains all chart and visualization methods for strategic analysis.
 */

import { chartTool, ChartGenerationResult as BaseChartGenerationResult } from '../tools/chart-tool';

// Extended chart generation result with fallback description
interface ExtendedChartGenerationResult extends BaseChartGenerationResult {
  fallbackDescription?: string;
}

// Type definitions for strategic analysis data structures
export interface ProductAnalysis {
  id: string;
  name: string;
  swotAnalysis: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    threats: string[];
  };
}

export interface MarketingStrategy {
  id: string;
  name: string;
  targetAudience: {
    demographics?: string[];
    behaviors?: string[];
    segmentDetails?: Array<{
      name: string;
      characteristics: string[];
    }>;
  };
  channels?: string[];
  channelStrategy?: Record<string, string>;
  contentStrategy?: string;
  timeline?: {
    startDate: Date;
    endDate: Date;
    milestones: Array<{
      date: Date | string;
      description: string;
    }>;
  };
  kpis?: string[];
  valueProposition?: string;
  keyMessages?: string[];
}

/**
 * Strategic Chart Generator Class
 * Contains all chart generation methods for strategic analysis
 */
export class StrategicChartGenerator {

  /**
   * Create a SWOT analysis chart
   */
  static async createSwotAnalysisChart(productAnalysis: ProductAnalysis): Promise<ExtendedChartGenerationResult> {
    const swotData = [
      ...productAnalysis.swotAnalysis.strengths.map(s => ({ category: 'Strengths', value: s })),
      ...productAnalysis.swotAnalysis.weaknesses.map(w => ({ category: 'Weaknesses', value: w })),
      ...productAnalysis.swotAnalysis.opportunities.map(o => ({ category: 'Opportunities', value: o })),
      ...productAnalysis.swotAnalysis.threats.map(t => ({ category: 'Threats', value: t }))
    ];

    const chartPrompt = `Generate a quadrant chart titled "SWOT Analysis: ${productAnalysis.name}" with the following data: ${JSON.stringify(swotData)}.
    The x-axis should represent 'Internal Factors | External Factors', and the y-axis should represent 'Helpful to achieving objectives | Harmful to achieving objectives'.
    Create four quadrants: Strengths (Internal, Helpful), Weaknesses (Internal, Harmful), Opportunities (External, Helpful), Threats (External, Harmful).
    Include a detailed explanation of how this SWOT analysis impacts the marketing strategy.`;

    const baseResult = await chartTool.generateChart({ prompt: chartPrompt });
    return {
      ...baseResult,
      fallbackDescription: !baseResult.chartConfig ?
        `SWOT Analysis for ${productAnalysis.name}: Strengths: ${productAnalysis.swotAnalysis.strengths.join(', ')}. Weaknesses: ${productAnalysis.swotAnalysis.weaknesses.join(', ')}. Opportunities: ${productAnalysis.swotAnalysis.opportunities.join(', ')}. Threats: ${productAnalysis.swotAnalysis.threats.join(', ')}.` :
        undefined
    };
  }

  /**
   * Create a competitive analysis chart
   */
  static async createCompetitiveAnalysisChart(productAnalysis: ProductAnalysis): Promise<ExtendedChartGenerationResult> {
    const competitiveData = [
      { competitor: productAnalysis.name, feature1: 10, feature2: 8, feature3: 9, marketShare: 25 },
      { competitor: 'Competitor A', feature1: 8, feature2: 9, feature3: 7, marketShare: 30 },
      { competitor: 'Competitor B', feature1: 7, feature2: 6, feature3: 8, marketShare: 20 },
    ];

    const chartPrompt = `Generate a radar chart titled "Competitive Analysis: ${productAnalysis.name}" with data: ${JSON.stringify(competitiveData)}.
    Compare competitors across feature1, feature2, feature3. Highlight "${productAnalysis.name}".
    Optionally include a market share pie chart. Explain the competitive landscape.`;

    const baseResult = await chartTool.generateChart({ prompt: chartPrompt, chartType: 'radar' });
    return {
      ...baseResult,
      fallbackDescription: !baseResult.chartConfig ?
        `Competitive Analysis for ${productAnalysis.name}: Market positioning shows competitive advantages in key features with 25% market share compared to competitors.` :
        undefined
    };
  }

  /**
   * Create a target audience segmentation chart
   */
  static async createTargetAudienceChart(strategy: MarketingStrategy): Promise<ExtendedChartGenerationResult> {
    const audienceData: Array<{ segment: string; characteristic: string; value: number; }> = [];

    (strategy.targetAudience.demographics || []).forEach(d => audienceData.push({ segment: 'Demographics', characteristic: d, value: Math.random() * 100 }));
    (strategy.targetAudience.behaviors || []).forEach(b => audienceData.push({ segment: 'Behaviors', characteristic: b, value: Math.random() * 100 }));
    (strategy.targetAudience.segmentDetails || []).forEach(sd => sd.characteristics.forEach(c => audienceData.push({ segment: `Segment: ${sd.name}`, characteristic: c, value: Math.random() * 100 })));

    const chartPrompt = `Generate a visualization for "Target Audience Segmentation: ${strategy.name}" with data: ${JSON.stringify(audienceData)}.
    Show breakdown by segment type and characteristic importance. Explain implications.`;

    const baseResult = await chartTool.generateChart({ prompt: chartPrompt });
    return {
      ...baseResult,
      fallbackDescription: !baseResult.chartConfig ?
        `Target Audience for ${strategy.name}: Demographics: ${(strategy.targetAudience.demographics || []).join(', ')}. Behaviors: ${(strategy.targetAudience.behaviors || []).join(', ')}.` :
        undefined
    };
  }

  /**
   * Create a marketing channels effectiveness chart
   */
  static async createChannelEffectivenessChart(strategy: MarketingStrategy): Promise<ExtendedChartGenerationResult> {
    const channelData = (strategy.channels || []).map(ch => ({
      channel: ch, reach: Math.random() * 10000, engagement: Math.random() * 500, conversion: Math.random() * 100, roi: Math.random() * 200 - 50
    }));

    const chartPrompt = `Generate visualization for "Channel Effectiveness: ${strategy.name}" with data: ${JSON.stringify(channelData)}.
    Show reach, engagement, conversion, ROI for each channel. Highlight best performers.`;

    const baseResult = await chartTool.generateChart({ prompt: chartPrompt });
    return {
      ...baseResult,
      fallbackDescription: !baseResult.chartConfig ?
        `Channel Effectiveness for ${strategy.name}: Channels include ${(strategy.channels || []).join(', ')} with varying performance metrics across reach, engagement, and ROI.` :
        undefined
    };
  }

  /**
   * Create a marketing timeline visualization
   */
  static async createMarketingTimelineChart(strategy: MarketingStrategy): Promise<ExtendedChartGenerationResult> {
    if (!strategy.timeline || !strategy.timeline.milestones) {
      throw new Error(`Strategy/timeline not found for strategy: ${strategy.name}`);
    }

    const timelineData = strategy.timeline.milestones.map(m => ({
      milestone: m.description,
      start: (m.date instanceof Date ? m.date : new Date(String(m.date))).toISOString().split('T')[0],
      duration: `${Math.floor(Math.random() * 14) + 1}d`, description: m.description
    }));

    const chartPrompt = `Generate a Gantt chart for "Marketing Timeline: ${strategy.name}" with data: ${JSON.stringify(timelineData)}.
    Show milestones from ${strategy.timeline.startDate.toISOString().split('T')[0]} to ${strategy.timeline.endDate.toISOString().split('T')[0]}. Explain timeline.`;

    const baseResult = await chartTool.generateChart({ prompt: chartPrompt, chartType: 'flow' });
    return {
      ...baseResult,
      fallbackDescription: !baseResult.chartConfig ?
        `Marketing Timeline for ${strategy.name}: ${strategy.timeline.milestones.length} milestones from ${strategy.timeline.startDate.toISOString().split('T')[0]} to ${strategy.timeline.endDate.toISOString().split('T')[0]}.` :
        undefined
    };
  }

  /**
   * Create a KPI dashboard visualization
   */
  static async createKpiDashboardChart(strategy: MarketingStrategy): Promise<ExtendedChartGenerationResult> {
    if (!strategy.kpis) {
      throw new Error(`Strategy/KPIs not found for strategy: ${strategy.name}`);
    }

    const kpiData = strategy.kpis.map(kpi => ({
      kpi, target: Math.random() * 1000 + 500, current: Math.random() * 1500,
      trend: ['increasing', 'decreasing', 'stable'][Math.floor(Math.random() * 3)] as 'increasing' | 'decreasing' | 'stable'
    }));

    const chartPrompt = `Generate a KPI dashboard for "Marketing KPIs: ${strategy.name}" with data: ${JSON.stringify(kpiData)}.
    Use gauges, progress bars, line charts. Show targets, current values, trends. Explain performance.`;

    const baseResult = await chartTool.generateChart({ prompt: chartPrompt });
    return {
      ...baseResult,
      fallbackDescription: !baseResult.chartConfig ?
        `KPI Dashboard for ${strategy.name}: Tracking ${strategy.kpis.length} key performance indicators including ${strategy.kpis.join(', ')}.` :
        undefined
    };
  }

  /**
   * Generate strategic analysis charts for marketing insights
   */
  static async generateStrategicCharts(analysisContent: string, projectTitle: string, analysisType: string = 'strategic'): Promise<BaseChartGenerationResult[]> {
    console.log('StrategicChartGenerator: Generating strategic analysis charts');

    const charts: BaseChartGenerationResult[] = [];

    try {
      // 1. Generate SWOT Analysis Matrix (Table)
      const swotPrompt = `
Based on this strategic analysis, create a SWOT analysis matrix table showing:
- Category (Strengths/Weaknesses/Opportunities/Threats)
- Factor
- Impact Level (High/Medium/Low)
- Strategic Implication

Analysis Content:
${analysisContent.substring(0, 2000)}

Project: ${projectTitle}
`;

      const swotChart = await chartTool.generateChart({
        prompt: swotPrompt,
        chartType: 'table' as any,
        model: 'gpt-4o',
        provider: 'openai'
      });

      if (swotChart.success) {
        charts.push(swotChart);
        console.log('StrategicChartGenerator: Successfully generated SWOT analysis chart');
      }

      // 2. Generate Strategic Recommendations Timeline (Flow Chart)
      const timelinePrompt = `
Create a strategic implementation timeline flowchart showing:
- Phase names and durations
- Key milestones and dependencies
- Resource allocation points
- Decision gates

Analysis Content:
${analysisContent.substring(0, 2000)}

Project: ${projectTitle}
`;

      const timelineChart = await chartTool.generateChart({
        prompt: timelinePrompt,
        chartType: 'flow' as any,
        model: 'gpt-4o',
        provider: 'openai'
      });

      if (timelineChart.success) {
        charts.push(timelineChart);
        console.log('StrategicChartGenerator: Successfully generated timeline chart');
      }

      // 3. Generate Market Opportunity Matrix (Scatter Plot)
      const opportunityPrompt = `
Create a market opportunity matrix scatter plot showing:
- X-axis: Market Attractiveness (Low to High)
- Y-axis: Competitive Advantage (Low to High)
- Bubble size: Market Size/Revenue Potential
- Different opportunities as bubbles

Analysis Content:
${analysisContent.substring(0, 2000)}

Project: ${projectTitle}
`;

      const opportunityChart = await chartTool.generateChart({
        prompt: opportunityPrompt,
        chartType: 'scatter' as any,
        model: 'gpt-4o',
        provider: 'openai'
      });

      if (opportunityChart.success) {
        charts.push(opportunityChart);
        console.log('StrategicChartGenerator: Successfully generated opportunity matrix chart');
      }

    } catch (error) {
      console.error('StrategicChartGenerator: Error generating strategic charts:', error);
    }

    console.log(`StrategicChartGenerator: Generated ${charts.length} strategic analysis charts`);
    return charts;
  }
}
