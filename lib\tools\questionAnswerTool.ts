/**
 * questionAnswerTool.ts
 *
 * This tool provides an interface for agents to ask logical questions based on context,
 * requirements, and inferred objectives. It leverages the QuestionAnswerAgent to generate
 * questions and synthesize answers.
 *
 * The tool is designed to be used exclusively by the PMOAgent and other agents that need
 * to ask logical questions and receive responses.
 */

import { questionAnswerAgent, QuestionAnswerAgent, QuestionAnswerPair, ContextAnalysisResult, ObjectiveInferenceResult } from '../../components/Agents/QuestionAnswerAgent';
import { Source } from '../../app/src/types/shared';
import { processWithGroq } from './groq-ai';
import { LogicalQuestionsArraySchema, SynthesizedAnswerSchema, safeParseJson } from '../schemas/llmResponseSchemas';

export interface LogicalQuestion {
  question: string;
  reasoning: string;
  purpose: string; // What this question aims to achieve
  expectedImpact: string; // How the answer will impact the final response
  priority: number; // 1-10, higher is more important
  type: string; // e.g., "clarification", "requirement", "objective", "constraint", etc.
  relatedTo: string[]; // Requirements or objectives this question relates to
}

export interface QuestionAnswerToolRequest {
  query: string;                      // The main query or request
  context?: string;                   // Additional context for the query
  userId: string;                     // User ID for document access
  category?: string;                  // Document category to search within
  previousExchanges?: {               // Previous Q&A exchanges for context
    question: string;
    answer: string;
  }[];
  responseCallback?: (question: LogicalQuestion) => Promise<string>; // Callback with logical question
  maxQuestions?: number;              // Maximum number of questions to ask
  maxIterations?: number;             // Maximum back-and-forth iterations
  questionTypes?: string[];           // Types of questions to prioritize
  toolOptions?: {                     // Options for sub-tools
    useInternetSearch?: boolean;
    useCalculator?: boolean;
    useCalendar?: boolean;
  };
}

export interface QuestionAnswerToolResult {
  success: boolean;
  finalAnswer: string;                // The final synthesized answer
  reasoning: string;                  // Reasoning behind the final answer
  contextAnalysis?: ContextAnalysisResult;
  objectiveInference?: ObjectiveInferenceResult;
  exchanges: {                        // Record of all Q&A exchanges
    question: LogicalQuestion | string;
    answer: string;
    confidence?: number;
    sources?: Source[];
  }[];
  sources?: Source[];                 // Sources used for the final answer
  followUpQuestions?: string[];       // Suggested follow-up questions
  confidence: number;                 // Overall confidence in the answer (0-1)
  error?: string;                     // Error message if any
}

export class QuestionAnswerTool {
  private agent: QuestionAnswerAgent;

  constructor() {
    this.agent = questionAnswerAgent;
  }

  /**
   * Process a query with interactive question-answering and reasoning
   */
  async process(request: QuestionAnswerToolRequest): Promise<QuestionAnswerToolResult> {
    try {
      // Initialize result tracking
      const exchanges: {
        question: LogicalQuestion | string;
        answer: string;
        confidence?: number;
        sources?: Source[];
      }[] = [];

      // Start with the main query
      let currentQuery = request.query;
      let iterations = 0;
      const maxIterations = request.maxIterations || 3;
      const maxQuestions = request.maxQuestions || 3;

      // Initial answer based on documents
      const initialResult = await this.agent.process({
        userRequest: currentQuery,
        context: request.context,
        userId: request.userId,
        category: request.category,
        previousQuestions: request.previousExchanges?.map(e => ({
          question: e.question,
          answer: e.answer
        })),
        maxQuestions: 1, // Just get the direct answer first
        enabledTools: request.toolOptions ? {
          internetSearch: request.toolOptions.useInternetSearch,
          calculator: request.toolOptions.useCalculator,
          calendar: request.toolOptions.useCalendar
        } : undefined
      });

      if (!initialResult.success) {
        return {
          success: false,
          finalAnswer: "",
          reasoning: "",
          exchanges: [],
          confidence: 0,
          error: initialResult.error
        };
      }

      // Add initial answer to exchanges
      const initialQA = initialResult.questions[0];
      exchanges.push({
        question: currentQuery,
        answer: initialQA.answer,
        confidence: initialQA.confidence,
        sources: initialQA.sources
      });

      // If no callback is provided, return the initial answer
      if (!request.responseCallback) {
        return {
          success: true,
          finalAnswer: initialQA.answer,
          reasoning: "Direct answer based on document search",
          exchanges,
          sources: initialQA.sources,
          followUpQuestions: initialResult.questions.slice(1).map(q => q.question),
          confidence: initialQA.confidence || 0.7
        };
      }

      // Analyze context and infer objectives
      const contextAnalysis = await this.agent.analyzeContext(
        request.query,
        request.context,
        request.previousExchanges
      );

      const objectiveInference = await this.agent.inferObjectives(
        request.query,
        contextAnalysis
      );

      // Generate logical questions with reasoning to fill knowledge gaps
      const logicalQuestions = await this.generateLogicalQuestions({
        query: request.query,
        initialAnswer: initialQA.answer,
        contextAnalysis,
        objectiveInference,
        previousExchanges: request.previousExchanges,
        maxQuestions,
        questionTypes: request.questionTypes
      });

      // Interactive Q&A loop
      for (const question of logicalQuestions) {
        if (iterations >= maxIterations) break;

        // Ask question through callback, providing the reasoning
        const answer = await request.responseCallback(question);

        // Add to exchanges
        exchanges.push({
          question,
          answer
        });

        iterations++;
      }

      // Synthesize final answer from all exchanges with reasoning
      const finalAnswer = await this.synthesizeFinalAnswer(
        request.query,
        initialQA.answer,
        contextAnalysis,
        objectiveInference,
        exchanges
      );

      return {
        success: true,
        finalAnswer: finalAnswer.content,
        reasoning: finalAnswer.reasoning,
        contextAnalysis,
        objectiveInference,
        exchanges,
        sources: finalAnswer.sources,
        followUpQuestions: finalAnswer.followUpQuestions,
        confidence: finalAnswer.confidence
      };
    } catch (error) {
      console.error("Error in QuestionAnswerTool:", error);
      return {
        success: false,
        finalAnswer: "",
        reasoning: "",
        exchanges: [],
        confidence: 0,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Generate logical questions based on context, requirements, and inferred objectives
   */
  private async generateLogicalQuestions(options: {
    query: string;
    initialAnswer?: string;
    contextAnalysis: ContextAnalysisResult;
    objectiveInference: ObjectiveInferenceResult;
    previousExchanges?: { question: string; answer: string }[];
    maxQuestions: number;
    questionTypes?: string[];
  }): Promise<LogicalQuestion[]> {
    const prompt = `
You are an expert business analyst tasked with generating logical questions to gather missing information.

Original Query: ${options.query}

${options.initialAnswer ? `Initial Answer: ${options.initialAnswer}\n\n` : ''}

Context Analysis:
- Explicit Requirements: ${options.contextAnalysis.explicitRequirements.join(', ')}
- Implicit Requirements: ${options.contextAnalysis.implicitRequirements.join(', ')}
- Inferred Objectives: ${options.contextAnalysis.inferredObjectives.join(', ')}
- Constraints: ${options.contextAnalysis.constraints.join(', ')}
- Stakeholders: ${options.contextAnalysis.stakeholders.join(', ')}
- Domain Context: ${options.contextAnalysis.domainContext}
- Knowledge Gaps: ${options.contextAnalysis.knowledgeGaps.join(', ')}
- Assumptions Made: ${options.contextAnalysis.assumptionsMade.join(', ')}

Objective Inference:
- Primary Objective: ${options.objectiveInference.primaryObjective}
- Secondary Objectives: ${options.objectiveInference.secondaryObjectives.join(', ')}
- Business Value: ${options.objectiveInference.businessValue}
- Success Criteria: ${options.objectiveInference.successCriteria.join(', ')}
- Risks: ${options.objectiveInference.risks.join(', ')}

${options.previousExchanges && options.previousExchanges.length > 0 ? `
Previous Exchanges:
${options.previousExchanges.map((e, i) => `
Q${i+1}: ${e.question}
A${i+1}: ${e.answer}
`).join('\n')}
` : ''}

Generate ${options.maxQuestions} logical questions that will help gather critical missing information.
For each question:
1. Provide detailed reasoning explaining why this question is important
2. Explain the purpose of the question
3. Describe the expected impact of the answer on the final response
4. Assign a priority from 1-10 (10 being highest)
5. Categorize the question type
6. List which requirements or objectives this question relates to

Focus on questions that:
- Address the most critical knowledge gaps first
- Help validate or refine assumptions
- Clarify ambiguous requirements
- Explore potential constraints or risks
- Align with the inferred objectives
- Build upon previous answers

${options.questionTypes && options.questionTypes.length > 0 ?
  `Prioritize questions of these types: ${options.questionTypes.join(', ')}` : ''}

Provide your questions as a JSON array of objects with these fields: question, reasoning, purpose, expectedImpact, priority, type, relatedTo
`;

    const response = await processWithGroq({
      prompt,
      model: 'deepseek-r1-distill-llama-70b',
      modelOptions: {
        temperature: 0.3,
        maxTokens: 3000
      }
    });

    try {
      // Use Zod schema to parse and validate the response
      const questions = safeParseJson(LogicalQuestionsArraySchema, response);

      // Sort by priority (highest first)
      return questions.sort((a: LogicalQuestion, b: LogicalQuestion) => b.priority - a.priority);
    } catch (error) {
      console.error("Error parsing logical questions:", error);

      // Fallback: try to extract questions from text response
      const questions = response
        .split('\n')
        .filter(line => line.includes('?'))
        .map((line, index) => {
          const question = line.match(/(.+\?)/)?.[1]?.trim() || line.trim();
          return {
            question,
            reasoning: "Generated to address knowledge gaps",
            purpose: "Gather missing information",
            expectedImpact: "Improve answer completeness",
            priority: 10 - index, // Assign decreasing priority
            type: "clarification",
            relatedTo: []
          };
        })
        .slice(0, options.maxQuestions);

      return questions;
    }
  }

  /**
   * Synthesize a final answer with reasoning from all Q&A exchanges
   */
  private async synthesizeFinalAnswer(
    query: string,
    initialAnswer: string,
    contextAnalysis: ContextAnalysisResult,
    objectiveInference: ObjectiveInferenceResult,
    exchanges: {
      question: LogicalQuestion | string;
      answer: string;
      sources?: Source[];
    }[]
  ): Promise<{
    content: string;
    reasoning: string;
    sources?: Source[];
    followUpQuestions?: string[];
    confidence: number;
  }> {
    const prompt = `
You are an expert business consultant tasked with synthesizing a comprehensive answer.

Original Query: ${query}

Initial Answer: ${initialAnswer}

Context Analysis:
- Explicit Requirements: ${contextAnalysis.explicitRequirements.join(', ')}
- Implicit Requirements: ${contextAnalysis.implicitRequirements.join(', ')}
- Inferred Objectives: ${contextAnalysis.inferredObjectives.join(', ')}
- Primary Objective: ${objectiveInference.primaryObjective}
- Business Value: ${objectiveInference.businessValue}

Q&A Exchanges:
${exchanges.map((e, i) => `
Question ${i+1}: ${typeof e.question === 'string' ? e.question : e.question.question}
${typeof e.question !== 'string' ? `Question Reasoning: ${e.question.reasoning}
Question Purpose: ${e.question.purpose}` : ''}
Answer ${i+1}: ${e.answer}
`).join('\n')}

Synthesize a comprehensive answer that:
1. Directly addresses the original query
2. Incorporates insights from the follow-up questions
3. Aligns with the inferred objectives
4. Addresses both explicit and implicit requirements
5. Provides actionable recommendations where appropriate

Also provide:
1. Detailed reasoning explaining how you arrived at this answer
2. A confidence score (0-1) indicating your certainty
3. Three potential follow-up questions that might be relevant

Format your response as a JSON object with fields: content, reasoning, confidence, followUpQuestions
`;

    const response = await processWithGroq({
      prompt,
      model: 'deepseek-r1-distill-llama-70b',
      modelOptions: {
        temperature: 0.3,
        maxTokens: 3000
      }
    });

    try {
      // Use Zod schema to parse and validate the response
      const result = safeParseJson(SynthesizedAnswerSchema, response, {
        content: initialAnswer,
        reasoning: "Synthesized from the available information",
        confidence: 0.5
      });

      // Collect all sources from exchanges
      const allSources = exchanges
        .flatMap(e => e.sources || [])
        .filter((source, index, self) =>
          index === self.findIndex(s => s.doc_id === source.doc_id)
        );

      return {
        content: result.content,
        reasoning: result.reasoning,
        sources: allSources.length > 0 ? allSources : undefined,
        followUpQuestions: result.followUpQuestions,
        confidence: result.confidence ?? 0.5 // Ensure confidence is always a number
      };
    } catch (error) {
      console.error("Error parsing synthesized answer:", error);

      return {
        content: initialAnswer,
        reasoning: "Synthesized from the available information",
        confidence: 0.5
      };
    }
  }

  /**
   * Tool definition for function calling
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "questionAnswerTool",
        description: "Ask logical questions based on context, requirements, and inferred objectives to provide comprehensive answers",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The main query or request to answer"
            },
            context: {
              type: "string",
              description: "Additional context for the query"
            },
            userId: {
              type: "string",
              description: "User ID for document access"
            },
            category: {
              type: "string",
              description: "Document category to search within"
            },
            maxQuestions: {
              type: "integer",
              description: "Maximum number of follow-up questions to ask"
            },
            toolOptions: {
              type: "object",
              properties: {
                useInternetSearch: {
                  type: "boolean",
                  description: "Whether to use internet search as a fallback"
                },
                useCalculator: {
                  type: "boolean",
                  description: "Whether to use calculator for mathematical operations"
                },
                useCalendar: {
                  type: "boolean",
                  description: "Whether to use calendar for date-related queries"
                }
              },
              description: "Options for enabling specific tools"
            }
          },
          required: ["query", "userId"]
        }
      }
    };
  }
}

// Export a singleton instance for easy import
export const questionAnswerTool = new QuestionAnswerTool();
