/**
 * Content Creator Agent
 *
 * This agent handles the production of marketing materials:
 * - Creates compelling written copy for social media posts
 * - Develops concepts for visual content (images, infographics, short videos)
 * - Adapts messaging to highlight product benefits
 * - Maintains consistent brand voice across all content
 * - Tailors content for different platforms and audience segments
 */

import { MarketingAgent } from './MarketingAgent';
import type { AgentMessage } from './MarketingAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { contentFormatterTool, ContentFormattingOptions } from '../../tools/content-formatter';

export interface ContentPiece {
  id: string;
  title: string;
  content: string;
  type: 'blog' | 'social' | 'email' | 'ad' | 'landing' | 'video-script' | 'infographic';
  platform?: string;
  audienceSegment?: string;
  tone: string;
  keywords: string[];
  callToAction?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ContentStrategy {
  id: string;
  name: string;
  description: string;
  targetAudience: string[];
  contentTypes: string[];
  contentThemes: string[];
  contentCalendar: Array<{
    date: Date;
    contentType: string;
    topic: string;
    platform: string;
    audienceSegment: string;
  }>;
  keyMessages: string[];
  brandVoice: {
    tone: string;
    style: string;
    vocabulary: string[];
    doUse: string[];
    dontUse: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

export class ContentCreatorAgent extends MarketingAgent {
  private contentPieces: ContentPiece[] = [];
  private contentStrategies: ContentStrategy[] = [];

  constructor(
    id: string = 'content-creator',
    name: string = 'Content Creator',
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'openai',
    defaultLlmModel: string = 'gpt-4o'
  ) {
    const role = 'Content Creation Specialist';
    const description = `As the Content Creation Specialist, I am responsible for developing compelling
marketing content that resonates with target audiences. I create engaging copy for various platforms,
maintain consistent brand voice, adapt messaging to highlight product benefits, and tailor content
for different audience segments and platforms.`;

    super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
  }

  /**
   * Create a content strategy
   */
  async createContentStrategy(
    name: string,
    description: string,
    targetAudience: string[],
    contentTypes: string[],
    keyMessages: string[],
    brandVoice: any
  ): Promise<ContentStrategy> {
    // Create a prompt for content theme generation
    const prompt = `
    Generate content themes for a content strategy with the following details:

    Strategy Name: ${name}
    Description: ${description}
    Target Audience: ${targetAudience.join(', ')}
    Content Types: ${contentTypes.join(', ')}
    Key Messages: ${keyMessages.join(', ')}
    Brand Voice: ${JSON.stringify(brandVoice)}

    Please suggest 5-7 content themes that would resonate with the target audience and support the key messages.
    Format your response as a JSON array of theme objects, each with a name and description.
    `;

    // Process with LLM
    const themesJson = await this.processRequest(prompt);

    try {
      // Parse the JSON response
      const themesData = JSON.parse(themesJson);

      // Create content themes
      const contentThemes = themesData.map((theme: any) => theme.name || 'Unnamed Theme');

      // Create a prompt for content calendar generation
      const calendarPrompt = `
      Generate a 30-day content calendar for a content strategy with the following details:

      Strategy Name: ${name}
      Description: ${description}
      Target Audience: ${targetAudience.join(', ')}
      Content Types: ${contentTypes.join(', ')}
      Content Themes: ${contentThemes.join(', ')}

      For each day, suggest a content piece with:
      1. Date (within the next 30 days)
      2. Content type
      3. Topic (based on the themes)
      4. Platform
      5. Target audience segment

      Format your response as a JSON array of calendar entry objects.
      `;

      // Process with LLM
      const calendarJson = await this.processRequest(calendarPrompt);

      // Parse the JSON response
      const calendarData = JSON.parse(calendarJson);

      // Create content calendar
      const contentCalendar = calendarData.map((entry: any) => ({
        date: new Date(entry.date),
        contentType: entry.contentType,
        topic: entry.topic,
        platform: entry.platform,
        audienceSegment: entry.audienceSegment
      }));

      // Create the content strategy object
      const contentStrategy: ContentStrategy = {
        id: `strategy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        targetAudience,
        contentTypes,
        contentThemes,
        contentCalendar,
        keyMessages,
        brandVoice,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.contentStrategies.push(contentStrategy);

      // Store in memory for context
      if (!this.memory.Agent_Response.contentStrategies) {
        this.memory.Agent_Response.contentStrategies = [];
      }
      this.memory.Agent_Response.contentStrategies.push(contentStrategy);

      return contentStrategy;
    } catch (error) {
      console.error('Error creating content strategy:', error);
      throw new Error('Failed to create content strategy');
    }
  }

  /**
   * Check if we have enough information to create content
   * This method validates that we have the necessary information before proceeding
   */
  private checkRequiredContentInformation(
    type: 'blog' | 'social' | 'email' | 'ad' | 'landing' | 'video-script' | 'infographic',
    params: Record<string, any>
  ): { hasEnoughInfo: boolean; missingInfo: string[] } {
    const missingInfo: string[] = [];

    // Common required fields for all content types
    const requiredFields: Record<string, string> = {
      title: "Content title",
      audienceSegment: "Target audience information",
      tone: "Content tone and style",
      keywords: "Keywords or key topics"
    };

    // Additional required fields based on content type
    if (type === 'blog') {
      requiredFields.wordCount = "Approximate word count";
    } else if (type === 'social') {
      requiredFields.platform = "Social media platform";
    } else if (type === 'email') {
      requiredFields.subject = "Email subject line";
      requiredFields.callToAction = "Call to action";
    } else if (type === 'ad') {
      requiredFields.platform = "Ad platform";
      requiredFields.callToAction = "Call to action";
      requiredFields.budget = "Budget information";
    } else if (type === 'landing') {
      requiredFields.callToAction = "Call to action";
      requiredFields.conversionGoal = "Conversion goal";
    } else if (type === 'video-script') {
      requiredFields.duration = "Approximate video duration";
      requiredFields.visualStyle = "Visual style guidelines";
    } else if (type === 'infographic') {
      requiredFields.dataPoints = "Key data points to include";
      requiredFields.visualStyle = "Visual style guidelines";
    }

    // Check for missing required fields
    for (const [field, description] of Object.entries(requiredFields)) {
      if (!params[field] ||
          (Array.isArray(params[field]) && params[field].length === 0) ||
          (typeof params[field] === 'string' && params[field].trim() === '')) {
        missingInfo.push(description);
      }
    }

    // Check for content-specific quality requirements
    if (type === 'blog' && params.keywords && params.keywords.length < 3) {
      missingInfo.push("At least 3 keywords for effective blog SEO");
    }

    if ((type === 'social' || type === 'ad') && !params.platform) {
      missingInfo.push("Specific platform information for proper formatting");
    }

    return {
      hasEnoughInfo: missingInfo.length === 0,
      missingInfo
    };
  }

  /**
   * Create a content piece
   */
  async createContentPiece(
    title: string,
    type: 'blog' | 'social' | 'email' | 'ad' | 'landing' | 'video-script' | 'infographic',
    platform: string,
    audienceSegment: string,
    tone: string,
    keywords: string[],
    callToAction: string,
    brief: string
  ): Promise<ContentPiece> {
    // Check if we have enough information to create the content
    const params = { title, platform, audienceSegment, tone, keywords, callToAction, brief };
    const { hasEnoughInfo, missingInfo } = this.checkRequiredContentInformation(type, params);

    // If we don't have enough information, request it from the Strategic Director
    if (!hasEnoughInfo) {
      console.log(`ContentCreatorAgent: Missing information for ${type} content creation: ${missingInfo.join(', ')}`);

      // Request the missing information from the Strategic Director
      await this.requestInformation(
        'strategic-director',
        'additional_context',
        {
          topic: `Creating ${type} content: ${title}`,
          questions: [
            `What is the specific ${missingInfo.join(', ')} for this ${type} content?`,
            `Are there any brand guidelines or restrictions I should be aware of?`,
            `What are the key differentiators that should be highlighted in this content?`
          ],
          currentProgress: `I'm preparing to create a ${type} content piece titled "${title}" but need additional information.`
        }
      );

      // Return a placeholder content piece
      return {
        id: `pending-content-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        title: `[PENDING] ${title}`,
        content: `Content creation pending - waiting for additional information: ${missingInfo.join(', ')}`,
        type,
        platform,
        audienceSegment,
        tone,
        keywords,
        callToAction,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    // Create a prompt for content generation
    const prompt = `
    Create a ${type} content piece with the following details:

    Title: ${title}
    Platform: ${platform}
    Target Audience: ${audienceSegment}
    Tone: ${tone}
    Keywords: ${keywords.join(', ')}
    Call to Action: ${callToAction}
    Brief: ${brief}

    Please generate the complete content piece in a format appropriate for the content type.
    `;

    // Process with LLM
    const content = await this.processRequest(prompt);

    // Format the content based on the type
    let formattedContent = content;

    if (type === 'blog' || type === 'landing') {
      // Format as markdown for blogs and landing pages
      const formattingOptions: ContentFormattingOptions = {
        format: 'markdown',
        domain: 'marketing',
        subjectMatter: keywords.join(', '),
        perspective: 'marketing',
        tone: tone,
        audience: audienceSegment,
        focus: 'engagement and conversion',
        contentGoal: callToAction
      };

      formattedContent = await contentFormatterTool.formatContent(content, '', 'markdown', formattingOptions);
    }

    // Create the content piece object
    const contentPiece: ContentPiece = {
      id: `content-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title,
      content: formattedContent,
      type,
      platform,
      audienceSegment,
      tone,
      keywords,
      callToAction,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.contentPieces.push(contentPiece);

    // Store in memory for context
    if (!this.memory.Agent_Response.contentPieces) {
      this.memory.Agent_Response.contentPieces = [];
    }
    this.memory.Agent_Response.contentPieces.push(contentPiece);

    // Save to storage
    await this.saveContent({
      type: 'content_piece',
      contentType: type,
      title,
      content: formattedContent,
      platform,
      audienceSegment,
      tone,
      keywords,
      callToAction,
      createdAt: new Date()
    }, 'marketing_content');

    return contentPiece;
  }

  /**
   * Generate social media posts for a product
   */
  async generateSocialMediaPosts(
    productName: string,
    productDescription: string,
    targetAudience: string,
    keyFeatures: string[],
    platforms: string[],
    postsPerPlatform: number = 3
  ): Promise<Record<string, ContentPiece[]>> {
    const result: Record<string, ContentPiece[]> = {};

    for (const platform of platforms) {
      // Create a prompt for social media post generation
      const prompt = `
      Generate ${postsPerPlatform} social media posts for ${platform} with the following details:

      Product: ${productName}
      Description: ${productDescription}
      Target Audience: ${targetAudience}
      Key Features: ${keyFeatures.join(', ')}

      For each post, include:
      1. Post text
      2. Hashtags (if appropriate)
      3. Call to action
      4. Suggested image description

      Format your response as a JSON array of post objects.
      `;

      // Process with LLM
      const postsJson = await this.processRequest(prompt);

      try {
        // Parse the JSON response
        const postsData = JSON.parse(postsJson);

        // Create content pieces for each post
        const posts: ContentPiece[] = [];

        for (let i = 0; i < postsData.length; i++) {
          const post = postsData[i];
          const title = `${productName} - ${platform} Post ${i + 1}`;
          const content = `${post.text}\n\n${post.hashtags || ''}\n\n${post.callToAction || ''}`;

          const contentPiece: ContentPiece = {
            id: `content-${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${i}`,
            title,
            content,
            type: 'social',
            platform,
            audienceSegment: targetAudience,
            tone: 'engaging',
            keywords: keyFeatures,
            callToAction: post.callToAction,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          posts.push(contentPiece);
          this.contentPieces.push(contentPiece);

          // Store in memory for context
          if (!this.memory.Agent_Response.contentPieces) {
            this.memory.Agent_Response.contentPieces = [];
          }
          this.memory.Agent_Response.contentPieces.push(contentPiece);

          // Save to storage
          await this.saveContent({
            type: 'social_media_post',
            contentType: 'social',
            title,
            content,
            platform,
            audienceSegment: targetAudience,
            tone: 'engaging',
            keywords: keyFeatures,
            callToAction: post.callToAction,
            imageDescription: post.imageDescription,
            createdAt: new Date()
          }, 'marketing_content');
        }

        result[platform] = posts;
      } catch (error) {
        console.error(`Error generating social media posts for ${platform}:`, error);
        result[platform] = [];
      }
    }

    return result;
  }

  /**
   * Create a blog post
   */
  async createBlogPost(
    title: string,
    topic: string,
    targetAudience: string,
    keywords: string[],
    tone: string,
    callToAction: string,
    wordCount: number = 800
  ): Promise<ContentPiece> {
    // Research the topic
    const research = await this.researchWeb(`${topic} ${keywords.join(' ')} latest trends`);

    // Create a prompt for blog post generation
    const prompt = `
    Write a blog post with the following details:

    Title: ${title}
    Topic: ${topic}
    Target Audience: ${targetAudience}
    Keywords: ${keywords.join(', ')}
    Tone: ${tone}
    Call to Action: ${callToAction}
    Word Count: approximately ${wordCount} words

    Research to incorporate:
    ${research}

    Please write a well-structured blog post with:
    1. Engaging introduction
    2. 3-5 main sections with subheadings
    3. Conclusion with call to action

    Format the post in markdown.
    `;

    // Process with LLM
    const content = await this.processRequest(prompt);

    // Format the content
    const formattingOptions: ContentFormattingOptions = {
      format: 'markdown',
      domain: 'marketing',
      subjectMatter: topic,
      perspective: 'marketing',
      tone: tone,
      audience: targetAudience,
      focus: 'engagement and education',
      contentGoal: callToAction
    };

    const formattedContent = await contentFormatterTool.formatContent(content, '', 'markdown', formattingOptions);

    // Create the content piece object
    const contentPiece: ContentPiece = {
      id: `content-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title,
      content: formattedContent,
      type: 'blog',
      audienceSegment: targetAudience,
      tone,
      keywords,
      callToAction,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.contentPieces.push(contentPiece);

    // Store in memory for context
    if (!this.memory.Agent_Response.contentPieces) {
      this.memory.Agent_Response.contentPieces = [];
    }
    this.memory.Agent_Response.contentPieces.push(contentPiece);

    // Save to storage
    await this.saveContent({
      type: 'blog_post',
      contentType: 'blog',
      title,
      content: formattedContent,
      audienceSegment: targetAudience,
      tone,
      keywords,
      callToAction,
      createdAt: new Date()
    }, 'marketing_content');

    // Generate PDF version
    await this.generatePdf(title, formattedContent, {
      title,
      subtitle: `Blog Post for ${targetAudience}`,
      date: new Date().toLocaleDateString()
    });

    return contentPiece;
  }

  /**
   * Get all content pieces
   */
  getContentPieces(): ContentPiece[] {
    return this.contentPieces;
  }

  /**
   * Get a specific content piece
   */
  getContentPiece(contentId: string): ContentPiece | undefined {
    return this.contentPieces.find(piece => piece.id === contentId);
  }

  /**
   * Get all content strategies
   */
  getContentStrategies(): ContentStrategy[] {
    return this.contentStrategies;
  }

  /**
   * Get a specific content strategy
   */
  getContentStrategy(strategyId: string): ContentStrategy | undefined {
    return this.contentStrategies.find(strategy => strategy.id === strategyId);
  }

  /**
   * Process messages for this agent
   * This method handles incoming messages, including responses to information requests
   */
  async processMessages(): Promise<void> {
    const messages = await this.receiveMessages();

    for (const message of messages) {
      console.log(`ContentCreatorAgent: Processing message from ${message.from}: ${message.content.substring(0, 100)}...`);

      // Check if this is a response to an information request
      if (message.metadata?.messageType === 'information_response') {
        await this.handleInformationResponse(message);
      }
      // Process other messages that require a response
      else if (message.metadata?.requiresResponse) {
        const response = await this.processRequest(message.content);
        await this.sendMessage(message.from, response, {
          inResponseTo: message.metadata?.messageId,
          isResponse: true
        });
      }
    }
  }

  /**
   * Handle responses to information requests
   * This method processes information provided by other agents (typically the Strategic Director)
   *
   * @param message - The message containing the information response
   */
  private async handleInformationResponse(message: AgentMessage): Promise<void> {
    const { from, content, metadata } = message;

    console.log(`ContentCreatorAgent: Handling information response from ${from}`);

    // Extract the task ID if available
    const taskId = metadata?.taskId;

    // If there's a task ID, update the task status
    if (taskId) {
      await this.updateTaskStatus(taskId, 'in-progress');

      // Store the information in memory for context
      if (!this.memory.Agent_Response.informationResponses) {
        this.memory.Agent_Response.informationResponses = {};
      }

      this.memory.Agent_Response.informationResponses[taskId] = {
        content,
        from,
        timestamp: new Date()
      };

      // Send acknowledgment
      await this.sendMessage(from, `Thank you for providing the requested information. I'll proceed with the task.`, {
        inResponseTo: metadata?.messageId,
        isResponse: true,
        taskId
      });
    } else {
      // If there's no task ID, just store the information in general memory
      if (!this.memory.Agent_Response.generalInformation) {
        this.memory.Agent_Response.generalInformation = [];
      }

      this.memory.Agent_Response.generalInformation.push({
        content,
        from,
        timestamp: new Date()
      });
    }
  }
}
