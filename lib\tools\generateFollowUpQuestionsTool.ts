/**
 * Tool for generating follow-up questions based on a query and response content
 * This tool helps users explore document content more deeply by suggesting
 * relevant follow-up questions.
 */

import { processWithGroq } from './groq-ai';

export interface GenerateFollowUpQuestionsOptions {
  query: string;
  responseContent: string;
  count?: number;
}

export interface GenerateFollowUpQuestionsResult {
  success: boolean;
  questions: string[];
  error?: string;
}

class GenerateFollowUpQuestionsTool {
  /**
   * Generate follow-up questions based on the query and response content
   * 
   * @param options - Options including query and response content
   * @returns Array of follow-up questions
   */
  async process(options: GenerateFollowUpQuestionsOptions): Promise<GenerateFollowUpQuestionsResult> {
    try {
      const { query, responseContent, count = 3 } = options;
      
      if (!query || !responseContent) {
        return {
          success: false,
          questions: [],
          error: 'Query and response content are required'
        };
      }

      const systemPrompt = `
You are an AI assistant that helps users explore document content more deeply.
Based on the user's query and the response content,
generate ${count} follow-up questions that would help the user gain additional insights.

User Query: "${query}"
Response Content: "${responseContent.substring(0, 1500)}${responseContent.length > 1500 ? '...' : ''}"

Generate ${count} follow-up questions that:
1. Explore different aspects of the topic
2. Seek clarification on details mentioned in the response
3. Expand the user's understanding of the subject

Provide ONLY a numbered list of questions without any explanations or additional text.
`;

      const followUpQuestionsText = await processWithGroq({
        prompt: systemPrompt,
        model: "deepseek-r1-distill-llama-70b",
        modelOptions: {
          temperature: 0.7,
          maxTokens: 300
        }
      });

      // Parse the numbered list into an array
      const questions = followUpQuestionsText
        .split('\n')
        .filter(line => /^\d+\./.test(line.trim()))
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .filter(question => question.length > 0);

      return {
        success: true,
        questions: questions.length > 0 ? questions.slice(0, count) : []
      };
    } catch (error) {
      console.error("Error generating follow-up questions:", error);
      return {
        success: false,
        questions: [],
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "generateFollowUpQuestions",
        description: "Generate follow-up questions based on a query and response content to help users explore document content more deeply",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The original user query"
            },
            responseContent: {
              type: "string",
              description: "The content of the response to the query"
            },
            count: {
              type: "integer",
              description: "Number of follow-up questions to generate (default: 3)"
            }
          },
          required: ["query", "responseContent"]
        }
      }
    };
  }
}

// Export a singleton instance for easy import
export const generateFollowUpQuestionsTool = new GenerateFollowUpQuestionsTool();
