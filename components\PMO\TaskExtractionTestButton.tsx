"use client"

import React from 'react'
import { Sparkles } from 'lucide-react'

/**
 * Test button to manually trigger the task extraction success modal
 * This is for testing purposes and can be removed in production
 */
export const TaskExtractionTestButton: React.FC = () => {
  const triggerTestModal = () => {
    // Create a test task extraction result
    const testResult = {
      taskCount: 11,
      extractionMode: 'PMO' as const,
      modelUsed: 'Gemini' as const,
      tasks: [
        {
          title: 'Conduct comprehensive market research and competitor analysis',
          category: 'Research',
          assignedTo: 'Research Team',
          dueDate: '2024-02-15'
        },
        {
          title: 'Develop brand messaging framework and value proposition',
          category: 'Marketing',
          assignedTo: 'Marketing Team',
          dueDate: '2024-02-20'
        },
        {
          title: 'Create content strategy and editorial calendar',
          category: 'Marketing',
          assignedTo: 'Marketing Team',
          dueDate: '2024-02-25'
        },
        {
          title: 'Design and develop marketing website landing pages',
          category: 'Design',
          assignedTo: 'Software Design Team',
          dueDate: '2024-03-01'
        },
        {
          title: 'Implement social media marketing campaigns',
          category: 'Marketing',
          assignedTo: 'Marketing Team',
          dueDate: '2024-03-05'
        },
        {
          title: 'Set up marketing automation workflows',
          category: 'Operations',
          assignedTo: 'Business Analysis Team',
          dueDate: '2024-03-10'
        },
        {
          title: 'Create performance tracking dashboard',
          category: 'Analytics',
          assignedTo: 'Business Analysis Team',
          dueDate: '2024-03-15'
        },
        {
          title: 'Conduct user testing and feedback collection',
          category: 'Research',
          assignedTo: 'Research Team',
          dueDate: '2024-03-20'
        },
        {
          title: 'Launch pilot marketing campaign',
          category: 'Marketing',
          assignedTo: 'Marketing Team',
          dueDate: '2024-03-25'
        },
        {
          title: 'Analyze campaign performance and metrics',
          category: 'Analytics',
          assignedTo: 'Business Analysis Team',
          dueDate: '2024-03-30'
        },
        {
          title: 'Prepare final strategy presentation',
          category: 'Admin',
          assignedTo: 'ADMIN (<EMAIL>)',
          dueDate: '2024-04-05'
        }
      ],
      confidence: 0.92,
      processingTime: 4,
      projectName: 'Strategic Marketing Initiative',
      agentType: 'Strategic Director',
      requestId: 'test-extraction-' + Date.now()
    }

    // Dispatch the custom event
    const event = new CustomEvent('taskExtractionSuccess', {
      detail: testResult
    })
    window.dispatchEvent(event)
    
    console.log('TaskExtractionTestButton: Triggered test modal with 11 tasks')
  }

  return (
    <button
      onClick={triggerTestModal}
      className="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm font-medium"
    >
      <Sparkles className="w-4 h-4 mr-2" />
      Test Task Extraction Modal
    </button>
  )
}

export default TaskExtractionTestButton
