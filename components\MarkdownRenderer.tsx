// components/MarkdownRenderer.tsx
import React, { FC, useMemo, useState } from "react";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import rehypeRaw from "rehype-raw";
import { Copy, Check } from "lucide-react";

// Define custom props for code component
interface CodeProps extends React.HTMLAttributes<HTMLElement> {
  node?: any;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

interface MarkdownRendererProps {
  content: string;
}

/**
 * Efficiently renders markdown content with syntax highlighting and copy functionality.
 * Uses memoization to optimize performance and prevent unnecessary re-renders.
 *
 * @param props - The component props
 * @param props.content - The markdown content to render
 */
const MarkdownRenderer: FC<MarkdownRendererProps> = ({ content }) => {
  const [copiedCodeBlock, setCopiedCodeBlock] = useState<string | null>(null);

  // Ensure content is a string and handle null/undefined cases
  const safeContent = useMemo(() => {
    if (content === null || content === undefined) return "";
    return typeof content === "string" ? content : String(content);
  }, [content]);

  // Preprocess markdown content (e.g., normalize line endings, trim)
  const preprocessedContent = useMemo(() => {
    // Check if content is JSON string and extract formattedResults if present
    if (safeContent.startsWith('{') && safeContent.includes('"formattedResults"')) {
      try {
        const parsed = JSON.parse(safeContent);
        if (parsed.formattedResults && typeof parsed.formattedResults === 'string') {
          return parsed.formattedResults
            .replace(/\r\n/g, "\n") // Normalize Windows line endings
            .trim();
        }
      } catch (e) {
        console.error('Error parsing JSON in MarkdownRenderer:', e);
        // Continue with normal processing if JSON parsing fails
      }
    }

    return safeContent
      .replace(/\r\n/g, "\n") // Normalize Windows line endings
      .trim();
  }, [safeContent]);

  // Handle copying code to clipboard
  const handleCopyCodeBlock = (code: string) => {
    navigator.clipboard.writeText(code).then(() => {
      setCopiedCodeBlock(code);
      setTimeout(() => setCopiedCodeBlock(null), 2000); // Reset after 2 seconds
    });
  };

  return (
    <div className="p-4 max-w-full h-full overflow-y-auto bg-zinc-900 rounded-md border border-zinc-700 prose prose-invert prose-sm">
      <ReactMarkdown
        rehypePlugins={[rehypeRaw]}
        components={{
          // Paragraph renderer
          p: ({ node, ...props }) => (
            <p
              className="mb-3 text-amber-100 text-sm leading-relaxed"
              {...props}
            />
          ),
          // Heading 1 renderer
          h1: ({ node, ...props }) => (
            <h1
              className="text-3xl font-bold mb-4 text-amber-100"
              {...props}
            />
          ),
          // Heading 2 renderer
          h2: ({ node, ...props }) => (
            <h2
              className="text-2xl font-semibold mb-3 text-amber-100"
              {...props}
            />
          ),
          // Unordered list renderer
          ul: ({ node, ...props }) => (
            <ul
              className="list-disc list-outside ml-5 mb-3 text-amber-100"
              {...props}
            />
          ),
          // Ordered list renderer
          ol: ({ node, ...props }) => (
            <ol
              className="list-decimal list-outside ml-5 mb-3 text-amber-100"
              {...props}
            />
          ),
          // List item renderer
          li: ({ node, ...props }) => (
            <li className="mb-1 text-amber-100" {...props} />
          ),
          // Code block and inline code renderer
          code: ({ node, inline, className, children, ...props }: CodeProps) => {
            const match = /language-(\w+)/.exec(className || "");
            const codeString = String(children).replace(/\n$/, "");

            if (!inline && match) {
              // Block code with syntax highlighting
              return (
                <div className="relative my-2">
                  <SyntaxHighlighter
                    style={vscDarkPlus as any}
                    language={match[1]}
                    PreTag="div"
                    className="rounded-md"
                    showLineNumbers
                    {...props}
                  >
                    {codeString}
                  </SyntaxHighlighter>
                  <button
                    onClick={() => handleCopyCodeBlock(codeString)}
                    className="absolute top-2 right-2 p-1 bg-zinc-800 rounded hover:bg-zinc-700 transition-colors"
                    title="Copy code"
                  >
                    {copiedCodeBlock === codeString ? (
                      <Check className="w-4 h-4 text-green-400" />
                    ) : (
                      <Copy className="w-4 h-4 text-amber-100" />
                    )}
                  </button>
                </div>
              );
            }
            // Inline code
            return (
              <code
                className="bg-zinc-800 text-amber-200 px-1 py-0.5 rounded"
                {...props}
              >
                {children}
              </code>
            );
          },
          // Blockquote renderer
          blockquote: ({ node, ...props }) => (
            <blockquote
              className="border-l-4 border-amber-500 pl-4 italic text-amber-100 my-2"
              {...props}
            />
          ),
          // Link renderer
          a: ({ node, ...props }) => (
            <a
              className="text-blue-400 hover:text-blue-300 underline"
              target="_blank"
              rel="noopener noreferrer"
              {...props}
            />
          ),
          // Strong (bold) renderer
          strong: ({ node, ...props }) => (
            <strong
              className="font-bold text-amber-300"
              {...props}
            />
          ),
          // Emphasis (italic) renderer
          em: ({ node, ...props }) => (
            <em
              className="italic text-amber-200"
              {...props}
            />
          ),
        }}
      >
        {preprocessedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;