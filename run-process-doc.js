/**
 * run-process-doc.js
 * 
 * A simple script to run the process-document.ts file with the correct memory settings
 */

const { spawn } = require('child_process');
const path = require('path');

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  console.error('Usage: node run-process-doc.js <file-path> [options]');
  process.exit(1);
}

// Set memory limit
const memoryLimit = 4096; // 4GB

console.log(`Running document processing with ${memoryLimit}MB memory limit`);

// Create the command
const tsNodePath = path.join(__dirname, 'node_modules', '.bin', 'ts-node');
const scriptPath = path.join(__dirname, 'scripts', 'process-document.ts');

// Build the arguments
const nodeArgs = [`--max-old-space-size=${memoryLimit}`, '--expose-gc', tsNodePath, scriptPath, ...args];

// Spawn the process
const child = spawn('node', nodeArgs, { 
  stdio: 'inherit',
  shell: process.platform === 'win32' // Use shell on Windows for better compatibility
});

// Handle process events
child.on('close', (code) => {
  if (code !== 0) {
    console.error(`Process exited with code ${code}`);
  }
  process.exit(code);
});

child.on('error', (err) => {
  console.error('Failed to start child process:', err);
  process.exit(1);
});
