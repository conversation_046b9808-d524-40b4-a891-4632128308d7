'use client';

import { useState } from 'react';
import { Button } from 'components/ui/button';
import { Input } from 'components/ui/input';
import { Textarea } from 'components/ui/textarea';
import { Label } from 'components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from 'components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from 'components/ui/accordion';
import { PlayCircle, Plus, Trash } from 'lucide-react';

interface AgentControlsProps {
  onStartCampaign: (campaignDetails: any) => void;
}

const AgentControls = ({ onStartCampaign }: AgentControlsProps) => {
  const [productName, setProductName] = useState('');
  const [productDescription, setProductDescription] = useState('');
  const [targetAudience, setTargetAudience] = useState('');
  const [campaignObjectives, setCampaignObjectives] = useState<string[]>(['Increase brand awareness']);
  const [budget, setBudget] = useState(10000);
  const [duration, setDuration] = useState(30);

  // Handle adding a new campaign objective
  const addCampaignObjective = () => {
    setCampaignObjectives([...campaignObjectives, '']);
  };

  // Handle removing a campaign objective
  const removeCampaignObjective = (index: number) => {
    setCampaignObjectives(campaignObjectives.filter((_, i) => i !== index));
  };

  // Handle updating a campaign objective
  const updateCampaignObjective = (index: number, value: string) => {
    const newObjectives = [...campaignObjectives];
    newObjectives[index] = value;
    setCampaignObjectives(newObjectives);
  };

  // Handle starting the campaign
  const handleStartCampaign = () => {
    // Validate required fields
    if (!productName || !productDescription || !targetAudience || campaignObjectives.length === 0) {
      alert('Please fill in all required fields');
      return;
    }

    // Filter out empty objectives
    const filteredObjectives = campaignObjectives.filter(obj => obj.trim() !== '');

    if (filteredObjectives.length === 0) {
      alert('Please add at least one campaign objective');
      return;
    }

    // Create campaign details object
    const campaignDetails = {
      productName,
      productDescription,
      targetAudience,
      campaignObjectives: filteredObjectives,
      budget,
      duration
    };

    // Call the onStartCampaign callback
    onStartCampaign(campaignDetails);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="product-name" className="text-gray-300">Product Name</Label>
        <Input
          id="product-name"
          placeholder="Enter product name"
          value={productName}
          onChange={(e) => setProductName(e.target.value)}
          className="bg-gray-700 border-gray-500/30 text-white placeholder:text-gray-400"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="product-description" className="text-gray-300">Product Description</Label>
        <Textarea
          id="product-description"
          placeholder="Describe your product..."
          value={productDescription}
          onChange={(e) => setProductDescription(e.target.value)}
          rows={3}
           className="bg-gray-700 border-gray-500/30 text-white placeholder:text-gray-400"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="target-audience" className="text-gray-300">Target Audience</Label>
        <Input
          id="target-audience"
          placeholder="Describe your target audience"
          value={targetAudience}
          onChange={(e) => setTargetAudience(e.target.value)}
           className="bg-gray-700 border-gray-500/30 text-white placeholder:text-gray-400"
        />
      </div>

      <Accordion type="single" collapsible defaultValue="campaign-objectives" className="border-gray-700/30">
        <AccordionItem value="campaign-objectives" className="border-gray-700/30">
          <AccordionTrigger className="text-gray-300 hover:text-purple-300">Campaign Objectives</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              {campaignObjectives.map((objective, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Input
                    placeholder="Enter campaign objective"
                    value={objective}
                    onChange={(e) => updateCampaignObjective(index, e.target.value)}
                    className="bg-gray-700/30 border-gray-500/30 text-white placeholder:text-gray-500"
                  />
                  <Button
                    onClick={() => removeCampaignObjective(index)}
                    disabled={campaignObjectives.length <= 1}
                    className="text-gray-400 hover:text-red-300 hover:bg-gray-700/30 p-2"
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                onClick={addCampaignObjective}
                className="mt-2 bg-purple-600 border-none text-white hover:bg-purple-700 transition-colors text-sm py-1 px-3"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Objective
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="advanced-settings" className="border-gray-700/30">
          <AccordionTrigger className="text-gray-300 hover:text-purple-300">Advanced Settings</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="budget" className="text-gray-300">Budget ($)</Label>
                <Input
                  id="budget"
                  type="number"
                  min={1000}
                  step={1000}
                  value={budget}
                  onChange={(e) => setBudget(Number(e.target.value))}
                  className="bg-gray-700/30 border-gray-600/30 text-white"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration" className="text-gray-300">Campaign Duration (days)</Label>
                <Input
                  id="duration"
                  type="number"
                  min={7}
                  max={365}
                  value={duration}
                  onChange={(e) => setDuration(Number(e.target.value))}
                  className="bg-gray-700/30 border-gray-600/30 text-white"
                />
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Button
        className="w-full mt-4 bg-purple-600 text-white hover:bg-purple-700 transition-colors shadow-md"
        onClick={handleStartCampaign}
      >
        <PlayCircle className="h-4 w-4 mr-2" />
        Start Marketing Campaign
      </Button>
    </div>
  );
};

export default AgentControls;
