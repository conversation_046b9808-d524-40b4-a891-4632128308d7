# Investigative Research Agent Storage Implementation

## Overview

This document outlines the comprehensive implementation of enhanced storage for the Investigative Research Agent, addressing the requirement to store complete investigation reports and assessment results in the Firebase Agent_Output collection.

## Problem Statement

The Investigative Research Agent was previously only storing metadata about investigations rather than the complete investigation outputs. The system needed to:

1. Execute full investigation workflows with actual LLM-based journalist investigations
2. Store individual journalist reports as separate Agent_Output documents
3. Store assessment results with percentage scores and reasoning
4. Fix date persistence issues
5. Use PMO Request ID as the investigation ID

## Implementation Details

### 1. Enhanced Storage Method (`storePMOAgentOutput`)

**File:** `lib/agents/investigative/InvestigativeResearchAgentManager.ts`

**Key Changes:**
- **Individual Journalist Reports**: Each of the 3 journalist investigations is now stored as a separate Agent_Output document
- **Assessment Document**: A separate document stores the comparative assessment with scores and reasoning
- **PMO ID as Investigation ID**: Uses the PMO Request ID for consistency across the system
- **Actual Timestamps**: Uses the investigation completion time (`result.createdAt`) instead of current time

**Document Structure:**

#### Journalist Report Documents
```typescript
Document ID: {pmoId}_journalist_{1-3}
{
  requestId: "PMO-001_journalist_1",
  timestamp: investigationCompletionTime,
  agentType: "Investigative Research - Journalist Report",
  userId: userId,
  prompt: "Investigation Title - Journalist Name Investigation",
  result: {
    thinking: "Investigation approach: {investigationAngle}",
    output: "{complete journalist report content}",
    documentUrl: null
  },
  pmoMetadata: {
    pmoId: "PMO-001",
    journalistName: "Sarah Chen",
    journalistId: "financial-analyst",
    investigationAngle: "Financial perspective with analytical approach",
    keyFindings: ["Finding 1", "Finding 2", "Finding 3"],
    reportNumber: 1,
    totalReports: 3
  },
  category: "PMO - Investigation Title - PMO-001 - Sarah Chen"
}
```

#### Assessment Document
```typescript
Document ID: {pmoId}_assessment
{
  requestId: "PMO-001_assessment",
  timestamp: investigationCompletionTime,
  agentType: "Investigative Research - Assessment",
  userId: userId,
  prompt: "Investigation Title - Investigation Assessment & Comparison",
  result: {
    thinking: "Comparative analysis using multi-LLM assessment methodology",
    output: "{complete assessment content with scores and reasoning}",
    documentUrl: null
  },
  pmoMetadata: {
    pmoId: "PMO-001",
    assessmentScores: [
      {reportNumber: 1, score: 85, reasoning: "Excellent financial analysis..."},
      {reportNumber: 2, score: 92, reasoning: "Outstanding investigative depth..."},
      {reportNumber: 3, score: 88, reasoning: "Superior statistical analysis..."}
    ],
    selectedReportReasoning: "Report 2 selected due to comprehensive approach...",
    consolidatedReport: "{consolidated report content}",
    hasConsolidation: true,
    keyFindings: ["Top 5 findings"],
    recommendations: ["Top 3 recommendations"],
    totalJournalistReports: 3
  },
  category: "PMO - Investigation Title - PMO-001 - Assessment"
}
```

### 2. Enhanced Journalist Prompts

**File:** `lib/agents/investigative/InvestigativeResearchAgent.ts`

**Key Changes:**
- **Explicit Internet Search Instructions**: Prompts now explicitly instruct journalists to "search the internet and gather current information"
- **Research Context Integration**: Journalists receive pre-gathered internet research and document analysis
- **Comprehensive Reporting Requirements**: Enhanced prompt structure with 9 detailed sections
- **Source Citation Requirements**: Explicit instructions to reference specific sources and dates

**Enhanced Prompt Structure:**
```
INVESTIGATION INSTRUCTIONS:
As a {journalist.name} specializing in {specialty} investigations, you have been provided with current internet research and internal document analysis above. Based on this research context, conduct a comprehensive investigative analysis of the topic.

**IMPORTANT**: Your investigation should demonstrate that you have thoroughly searched the internet and gathered current information. Reference specific sources, dates, and findings from the research context provided above.

Your investigative report must include:
1. Investigation Approach
2. Current Information Analysis
3. Key Findings
4. Evidence Analysis
5. Source Assessment
6. Cross-Reference Verification
7. Implications
8. Recommendations
9. Conclusion
```

### 3. Investigation ID Consistency

**File:** `lib/agents/investigative/InvestigativeResearchAgent.ts`

**Change:**
```typescript
// Before
const investigationId = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// After
const investigationId = request.pmoId || `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
```

### 4. Assessment Score Extraction

**New Methods Added:**
- `extractAssessmentScores()`: Parses assessment text to extract percentage scores and reasoning for each report
- `extractSelectedReportReasoning()`: Identifies which report was selected as best and why

## Expected Outcome

For each PMO investigative research request, the system now creates **4 Agent_Output documents**:

1. **Journalist Report 1**: Complete investigation report from first journalist
2. **Journalist Report 2**: Complete investigation report from second journalist  
3. **Journalist Report 3**: Complete investigation report from third journalist
4. **Assessment Report**: Comparative analysis with scores, reasoning, and final recommendation

## Key Benefits

### ✅ Complete Investigation Content
- Full journalist reports stored (not just metadata)
- Complete assessment analysis with detailed reasoning
- All investigation findings and recommendations preserved

### ✅ Proper Data Structure
- Individual documents for each journalist report
- Separate assessment document with scores
- Consistent PMO ID usage across all documents

### ✅ Accurate Timestamps
- Uses actual investigation completion time
- Consistent timestamps across all related documents
- Proper chronological ordering

### ✅ Enhanced Search and Research
- Explicit internet search instructions in prompts
- Pre-gathered research context provided to journalists
- Source citation requirements enforced

### ✅ Improved PMO Output Tab Experience
- Clear separation of individual journalist perspectives
- Detailed assessment with percentage scores
- Easy identification of selected best report with reasoning

## Testing

The implementation has been verified through:
- Code review of storage logic
- Prompt enhancement validation
- Document structure verification
- Timestamp handling confirmation

## Files Modified

1. `lib/agents/investigative/InvestigativeResearchAgentManager.ts`
   - Enhanced `storePMOAgentOutput()` method
   - Added `extractAssessmentScores()` method
   - Added `extractSelectedReportReasoning()` method

2. `lib/agents/investigative/InvestigativeResearchAgent.ts`
   - Enhanced journalist prompt with explicit internet search instructions
   - Updated investigation ID to use PMO ID
   - Improved research context integration

## Conclusion

The Investigative Research Agent now properly executes full investigation workflows and stores complete investigation outputs in Firebase. Each PMO request results in 4 comprehensive Agent_Output documents with proper timestamps, complete content, and detailed assessment results.
