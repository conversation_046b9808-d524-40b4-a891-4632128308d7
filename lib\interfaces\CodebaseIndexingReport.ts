/**
 * Interface definitions for Codebase Indexing Confirmation Reports
 */

export interface CodebaseIndexingReport {
  id: string; // UUID for the report
  projectName: string;
  userId: string;
  indexingSessionId: string; // Document ID from the indexing process
  createdAt: Date;
  completedAt: Date;
  processingTimeMs: number;
  selectedPaths?: string[]; // User-selected paths that were processed
  
  // Indexing Statistics
  statistics: {
    totalFiles: number;
    totalChunks: number;
    totalSize: number; // in bytes
    averageChunkSize: number;
    processingTimePerFile: number; // average ms per file
  };
  
  // File Analysis Results
  fileAnalysis: CodebaseFileAnalysis[];
  
  // Vector Embedding Confirmation
  vectorEmbedding: {
    pineconeNamespace: string;
    totalEmbeddings: number;
    embeddingModel: string;
    embeddingDimensions: number;
    indexingComplete: boolean;
  };
  
  // Firebase Storage Confirmation
  firebaseStorage: {
    reportDocumentId: string;
    reportPdfUrl?: string;
    storageComplete: boolean;
  };
  
  // Processing Summary
  summary: {
    successfulFiles: number;
    failedFiles: number;
    skippedFiles: number;
    errorMessages: string[];
    warnings: string[];
  };
}

export interface CodebaseFileAnalysis {
  filePath: string;
  fileName: string;
  language: string;
  fileSize: number; // in bytes
  chunkCount: number;
  
  // LLM-Generated Analysis
  llmSummary: string;
  codeEntityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown';
  definedEntities: string[];
  
  // Code Intelligence
  imports: string[];
  exports: string[];
  apiEndpoints: string[];
  
  // Processing Details
  processingTimeMs: number;
  success: boolean;
  errorMessage?: string;
  
  // Chunk Details
  chunks: {
    chunkId: string;
    chunkIndex: number;
    contentPreview: string; // First 200 characters
    summary: string;
  }[];
}

export interface CodebaseIndexingReportOptions {
  includeChunkPreviews: boolean;
  includeErrorDetails: boolean;
  includePerformanceMetrics: boolean;
  maxFileAnalysisEntries: number;
}

// For Firebase storage
export interface StoredCodebaseIndexingReport extends Omit<CodebaseIndexingReport, 'createdAt' | 'completedAt'> {
  createdAt: string; // ISO string for Firebase
  completedAt: string; // ISO string for Firebase
}
