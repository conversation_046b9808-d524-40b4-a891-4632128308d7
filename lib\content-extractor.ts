import { llmTool, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./tools/llm-tool";
import { contentFormatterTool } from "./tools/content-formatter";

/**
 * Options for content extraction
 */
export interface ContentExtractionOptions {
  model?: string;
  provider?: LlmProvider;
  format?: string;
  formatOptions?: {
    domain?: string;
    focus?: string;
    audience?: string;
    keyPoints?: string;
    maxChunkSize?: number;
    maxChunks?: number;
    [key: string]: any;
  };
}

/**
 * Extracts and processes content from raw text
 * @param content - The raw content to process
 * @param url - The URL of the content source
 * @param options - Optional processing options
 * @param options.model - The model to use (default: "gpt-4o")
 * @param options.provider - The LLM provider (default: "openai")
 * @param options.format - Output format (default: "markdown")
 * @param options.formatOptions - Additional formatting options
 * @returns The processed content
 */
export async function extractContent(
  content: string,
  url: string,
  options: ContentExtractionOptions = {}
): Promise<string> {
  try {
    const {
      model = "gpt-4o",
      provider = "openai",
      format = "markdown",
      formatOptions = {}
    } = options;

    // Use the content formatter tool to process the content
    const processedContent = await contentFormatterTool.formatContent(
      content,
      url,
      format,
      {
        model,
        domain: formatOptions.domain || "customer support documentation",
        focus: formatOptions.focus || "product usage",
        audience: formatOptions.audience || "customer support staff",
        keyPoints: formatOptions.keyPoints,
        maxChunkSize: formatOptions.maxChunkSize || 4000,
        maxChunks: formatOptions.maxChunks || 10
      }
    );

    // Return processed content or a fallback if empty
    return processedContent || `No meaningful content extracted from ${url}.`;
  } catch (error) {
    console.error("Error extracting content:", error);
    // Return fallback content instead of throwing error
    return `Error processing content from ${url}: Unable to extract meaningful information.`;
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use extractContent instead
 */
export async function processContentWithLLM(
  content: string,
  url: string,
  options: ContentExtractionOptions = {}
): Promise<string> {
  console.warn("processContentWithLLM is deprecated. Please use extractContent instead.");
  return extractContent(content, url, options);
}