"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { X, MessageSquare, Clock } from "lucide-react"
import { collection, query, where, getDocs, orderBy, Timestamp } from "firebase/firestore"
import { getFirestore } from "firebase/firestore"

interface Chat {
  id: string
  createdAt: Timestamp
  fileNamespace?: string
  firstMessage?: string
}

interface ChatHistoryProps {
  isVisible: boolean
  onClose: () => void
  currentChatId: string | null
  onChatSelect: (chatId: string) => void
  userId: string
  namespace: string | null
}

export function ChatHistory({ isVisible, onClose, currentChatId, onChatSelect, userId, namespace }: ChatHistoryProps) {
  const [chats, setChats] = useState<Chat[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (isVisible && userId) {
      fetchChats()
    }
  }, [isVisible, userId, namespace])

  const fetchChats = async () => {
    if (!userId) {
      setLoading(false)
      return
    }

    setLoading(true)
    try {
      const db = getFirestore()
      const chatsRef = collection(db, `users/${userId}/chats`)

      // Query chats with the file's namespace
      let q = query(chatsRef, orderBy("createdAt", "desc"))

      if (namespace) {
        q = query(chatsRef, where("fileNamespace", "==", namespace), orderBy("createdAt", "desc"))
      }

      const querySnapshot = await getDocs(q)
      const chatsList: Chat[] = []

      querySnapshot.forEach((doc) => {
        const chatData = doc.data()
        chatsList.push({
          id: doc.id,
          createdAt: chatData.createdAt,
          fileNamespace: chatData.fileNamespace,
          firstMessage: chatData.firstMessage || "New Rehearsal",
        })
      })

      setChats(chatsList)
      setLoading(false)
    } catch (err) {
      console.error("Error fetching chats:", err)
      setError(`Failed to load chats: ${err instanceof Error ? err.message : "Unknown error"}`)
      setLoading(false)
    }
  }

  // Group chats by date categories
  const groupChatsByDate = () => {
    const groups: { [key: string]: Chat[] } = {}

    chats.forEach((chat) => {
      if (!chat.createdAt) return

      let chatDate: Date | null = null
      if (chat.createdAt instanceof Timestamp) {
        chatDate = chat.createdAt.toDate()
      } else {
        console.error("Invalid createdAt type:", chat.createdAt)
        return // Skip this chat
      }

      // Handle potentially invalid date
      if (!chatDate || isNaN(chatDate.getTime())) {
        console.error("Invalid date after parsing:", chat.createdAt)
        return
      }

      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      let dateKey: string

      if (chatDate.toDateString() === today.toDateString()) {
        dateKey = "Today"
      } else if (chatDate.toDateString() === yesterday.toDateString()) {
        dateKey = "Yesterday"
      } else {
        dateKey = chatDate.toLocaleDateString()
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(chat)
    })

    return groups
  }

  const chatGroups = groupChatsByDate()

  // Format time for display
  const formatTime = (timestamp: Timestamp | null | undefined): string => {
    if (!timestamp) return ""

    let date: Date
    if (timestamp instanceof Timestamp) {
      date = timestamp.toDate()
    } else {
      return ""
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      console.error("Invalid date after parsing:", timestamp)
      return ""
    }
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  // Truncate message for display
  const truncateMessage = (message: string | undefined, maxLength = 40): string => {
    if (!message) return "New Rehearsal"
    if (message.length <= maxLength) return message
    return message.substring(0, maxLength) + "..."
  }

  return (
    <>
      <motion.div
        initial={{ x: "calc(-100% + 5px)" }}
        animate={{ x: isVisible ? 0 : "calc(-100% + 5px)" }}
        transition={{ duration: 0.1, ease: "easeInOut" }}
        className="absolute md:relative z-10 w-full md:w-1/4 bg-black/95 md:bg-white/20 backdrop-blur-md border border-white/20 h-full overflow-y-auto rounded-xl shadow-lg"
      >
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-bold text-white">Rehearsals</h3>
            <button
              onClick={onClose}
              className="md:hidden p-1 text-gray-400 hover:text-white hover:bg-white/10 rounded-full"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {loading ? (
            <div className="text-center py-4 text-gray-400">Loading rehearsals...</div>
          ) : error ? (
            <div className="text-center py-4 text-red-400">{error}</div>
          ) : chats.length === 0 ? (
            <div className="text-gray-400 text-sm">No rehearsals found for this script</div>
          ) : (
            Object.entries(chatGroups).map(
              ([groupName, groupChats]) =>
                groupChats.length > 0 && (
                  <div key={groupName} className="mb-4">
                    <h4 className="text-xs uppercase font-semibold text-gray-500 mb-2 px-2">{groupName}</h4>
                    <ul className="space-y-2">
                      {groupChats.map((chat) => (
                        <li
                          key={chat.id}
                          onClick={() => onChatSelect(chat.id)}
                          className={`flex items-center py-2 px-3 rounded-lg text-sm cursor-pointer transition-colors ${
                            currentChatId === chat.id
                              ? "bg-white text-black font-medium shadow-inner"
                              : "text-gray-300 hover:bg-white/10 hover:shadow"
                          }`}
                        >
                          <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" />
                          <div className="flex-1 overflow-hidden">
                            <div className="truncate">{truncateMessage(chat.firstMessage)}</div>
                            <div className="flex items-center justify-between text-xs mt-1">
                              <div className="flex items-center text-gray-500">
                                <Clock className="w-3 h-3 mr-1" />
                                {formatTime(chat.createdAt)}
                              </div>

                              {chat.fileNamespace && (
                                <span className="text-xs bg-gray-700 rounded-full px-2 py-0.5 text-gray-300 ml-1 truncate max-w-[80px]">
                                  {truncateMessage(chat.fileNamespace, 15)}
                                </span>
                              )}
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                ),
            )
          )}
        </div>
      </motion.div>
    </>
  )
}
