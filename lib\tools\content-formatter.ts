import { streamText } from "ai";
import { openai } from "@ai-sdk/openai";

// Define interfaces for content formatting
export interface ContentFormattingOptions {
  // Processing options
  maxChunkSize?: number;
  maxChunks?: number;
  model?: string;
  temperature?: number;
  maxTokens?: number;

  // Content focus options
  domain?: string;           // Subject matter domain (e.g., "marketing", "finance", "technology")
  subjectMatter?: string;    // Specific subject within the domain (e.g., "social media marketing")
  focus?: string;            // What to focus on (e.g., "benefits", "features", "use cases")
  audience?: string;         // Target audience (e.g., "marketers", "executives", "customers")
  perspective?: string;      // Perspective to write from (e.g., "marketing", "technical", "educational")
  tone?: string;             // Tone of the content (e.g., "professional", "conversational", "persuasive")
  keyPoints?: string;        // Key points to extract
  contentGoal?: string;      // Goal of the content (e.g., "inform", "persuade", "educate")
  outputFormat?: string;     // Specific format requirements beyond markdown

  // Additional options
  [key: string]: any;
}

/**
 * Content Formatter Tool for processing raw content into structured markdown
 */
export class ContentFormatterTool {
  /**
   * Process raw content into structured markdown
   * @param content - Raw content to process
   * @param url - Source URL of the content
   * @param format - Format type (default: "markdown")
   * @param options - Additional formatting options
   * @returns Formatted content
   */
  async formatContent(content: string, url: string, format = "markdown", options: ContentFormattingOptions = {}): Promise<string> {
    try {
      // Split content into chunks if it's too large
      const chunks = this.splitIntoChunks(content, options.maxChunkSize || 4000, options.maxChunks || 10);

      let processedContent = "";

      for (const chunk of chunks) {
        const prompt = this.buildPrompt(chunk, url, format, options);

        // Use streamText to generate the response
        const { textStream } = streamText({
          model: openai(options.model || "gpt-4o"),
          messages: [{ role: 'user', content: prompt }],
          temperature: options.temperature || 0.7,
          maxTokens: options.maxTokens || 1000
        });

        let chunkResult = "";
        for await (const delta of textStream) {
          chunkResult += delta;
        }

        processedContent += chunkResult + "\n\n";
      }

      // Return processed content or a fallback if empty
      return processedContent.trim() || `No meaningful content extracted from ${url}.`;
    } catch (error: any) {
      console.error("Error formatting content:", error);
      return `Error processing content from ${url}: ${error.message}`;
    }
  }

  /**
   * Build prompt for content formatting
   * @private
   */
  private buildPrompt(chunk: string, url: string, format: string, options: ContentFormattingOptions): string {
    // Determine domain and subject matter
    const domain = options.domain || "marketing";
    const subjectMatter = options.subjectMatter ? ` with a focus on ${options.subjectMatter}` : '';

    // Determine perspective and tone
    const perspective = options.perspective || "marketing";
    const tone = options.tone ? `, using a ${options.tone} tone` : '';

    // Determine audience
    const audience = options.audience || "potential customers";

    // Determine content goal
    const contentGoal = options.contentGoal ? ` The goal is to ${options.contentGoal}.` : '';

    // Determine focus areas
    const focus = options.focus || "value propositions and benefits";

    // Determine key points to extract
    const defaultKeyPoints =
      domain === "marketing" ?
        "- Value propositions and unique selling points\n   - Product/service benefits and features\n   - Target audience and use cases\n   - Competitive advantages\n   - Testimonials or social proof\n   - Call to action elements" :
      domain === "technical" ?
        "- Technical specifications and capabilities\n   - Implementation details\n   - System requirements\n   - Integration points\n   - Technical benefits\n   - Performance metrics" :
      domain === "research" ?
        "- Research methodology and approach\n   - Key findings and results\n   - Data analysis and interpretation\n   - Theoretical framework\n   - Literature review highlights\n   - Implications and applications\n   - Limitations and future research directions" :
        "- Key information points\n   - Main arguments or claims\n   - Supporting evidence\n   - Contextual details\n   - Relevant statistics or data";

    const keyPoints = options.keyPoints || defaultKeyPoints;

    // Determine output format specifics
    const outputFormatDetails = options.outputFormat ? `\n\n${options.outputFormat}` : '';

    const basePrompt = `
You are a content extraction and formatting expert specializing in ${domain}${subjectMatter}.
Your task is to extract meaningful content from the following webpage text
and convert it into a well-structured ${format === "markdown" ? "Markdown" : format} document from a ${perspective} perspective${tone}.

The content is from: ${url}

${contentGoal}

Focus on:
1. Main article content relevant to ${focus}
2. Important headings and subheadings
3. Key information valuable for ${audience}, such as:
   ${keyPoints}
4. Removing navigation elements, advertisements, and other non-essential content

Format the output as a clean ${format === "markdown" ? "Markdown" : format} document with proper structure, including:
- Clear, descriptive headings and subheadings
- Well-organized sections with logical flow
- Bullet points for lists and key features
- Emphasis on important points${outputFormatDetails}

Here is the content:
${chunk}
`;

    return basePrompt;
  }

  /**
   * Splits text into chunks of a specified maximum length
   * @param text - The input text to split
   * @param maxLength - Maximum length of each chunk
   * @param maxChunks - Maximum number of chunks to generate
   * @returns Array of text chunks
   */
  private splitIntoChunks(text: string, maxLength: number, maxChunks = 10): string[] {
    const chunks: string[] = [];

    if (!text || typeof text !== "string") return chunks;

    // First attempt: Split by paragraphs
    const paragraphs = text.split("\n");
    let currentChunk = "";

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > maxLength) {
        chunks.push(currentChunk.trim());
        currentChunk = paragraph;
        if (chunks.length >= maxChunks - 1) break; // Reserve last chunk for remainder
      } else {
        currentChunk += (currentChunk ? "\n" : "") + paragraph;
      }
    }

    if (currentChunk) {
      chunks.push(currentChunk.trim());
    }

    // If no chunks or insufficient splitting, fall back to character-based splitting
    if (chunks.length === 0 || chunks.length > maxChunks) {
      for (let i = 0; i < text.length && chunks.length < maxChunks; i += maxLength) {
        chunks.push(text.substring(i, i + maxLength).trim());
      }
    }

    // Truncate to maxChunks if exceeded
    return chunks.slice(0, maxChunks);
  }
}

// Export a singleton instance
export const contentFormatterTool = new ContentFormatterTool();

/**
 * Processes raw content with an LLM to extract meaningful text (Legacy function for backward compatibility)
 * @param content - The raw content to process
 * @param url - The URL of the content source
 * @param options - Optional formatting options
 * @returns The processed content in Markdown
 */
export async function processContentWithLLM(content: string, url: string, options?: ContentFormattingOptions): Promise<string> {
  return contentFormatterTool.formatContent(content, url, 'markdown', options);
}
