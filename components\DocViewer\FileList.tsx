import React, { useState, useMemo } from 'react';
import { File, Download } from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import { FileData } from './FileManagerstructure';
import { SortConfig } from './FileManagerDirectory';
import FileManagerPagination from './FileManagerPagination';

interface FileListProps {
  files: FileData[];
  handleFileClick: (file: FileData) => void;
  truncateFileName: (fileName: string, maxLength?: number) => string;
  formatTimestamp: (timestamp: Timestamp | undefined) => string;
  sortConfig: SortConfig | null;
  variant: 'unknown' | 'categorized';
}

const FileList: React.FC<FileListProps> = ({
  files,
  handleFileClick,
  truncateFileName,
  formatTimestamp,
  sortConfig,
  variant,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [filesPerPage, setFilesPerPage] = useState(10); // Show 10 files per page by default

  const abbreviateFileType = (type: string | undefined): string => {
    if (!type) return '';
    if (type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return 'application/docx';
    }
    if (type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return 'application/xlsx';
    }
    return type;
  };

  // Sort files based on the current sort configuration
  const sortedFiles = useMemo(() => {
    if (!sortConfig) return files;

    return [...files].sort((a, b) => {
      switch (sortConfig.key) {
        case 'files':
          return sortConfig.direction === 'asc'
            ? a.name.localeCompare(b.name)
            : b.name.localeCompare(a.name);
        case 'date':
          if (!a.createdAt || !b.createdAt) return 0;
          return sortConfig.direction === 'asc'
            ? a.createdAt.seconds - b.createdAt.seconds
            : b.createdAt.seconds - a.createdAt.seconds;
        case 'type':
          return sortConfig.direction === 'asc'
            ? (a.type || '').localeCompare(b.type || '')
            : (b.type || '').localeCompare(a.type || '');
        default:
          return 0;
      }
    });
  }, [files, sortConfig]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedFiles.length / filesPerPage);

  // Get current page files
  const currentFiles = useMemo(() => {
    const indexOfLastFile = currentPage * filesPerPage;
    const indexOfFirstFile = indexOfLastFile - filesPerPage;
    return sortedFiles.slice(indexOfFirstFile, indexOfLastFile);
  }, [sortedFiles, currentPage, filesPerPage]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const baseClasses = "grid grid-cols-6 gap-2 ml-2 text-xs justify-center rounded-md bg-opacity-65 mb-1 border-b-2 border-gray-900 p-1 mr-2 transition-colors duration-200 ease-in-out";
  const variantClasses = {
    unknown: "bg-ike-purple_b pl-2 text-white hover:bg-gray-700",
    categorized: "bg-gray-800 text-gray-200 hover:bg-ike-purple_b"
  };

  return (
    <>
      {currentFiles.map((file) => (
        <div
          key={file.id}
          className={`${baseClasses} ${variantClasses[variant]}`}
        >
          <div className="col-span-2 ml-1 font-normal flex items-center pl-2">
            <File
              className={`h-4 w-4 inline mr-2 ${
                variant === 'unknown' ? 'text-white' : 'text-blue-400'
              } text-left`}
            />
            <span
              className="hover:text-blue-400 mr-5 transition-colors text-left duration-200 ease-in-out cursor-pointer"
              onClick={() => handleFileClick(file)}
            >
              {truncateFileName(file.name)}
            </span>
          </div>
          <div className="flex items-center justify-center w-full">
            <a
              href={file.downloadUrl}
              download
              className="hover:text-blue-400 transition-colors duration-200 ease-in-out"
            >
              <Download className="h-4 w-4" />
            </a>
          </div>
          <div className="flex items-center justify-center w-full">
            {file.chatCount || 0}
          </div>
          <div className="flex items-center justify-center w-full">
            {formatTimestamp(file.createdAt)}
          </div>
          <div className="flex items-center mr-1 justify-right w-full">
            {abbreviateFileType(file.type)}
          </div>
        </div>
      ))}

      {/* Show pagination controls and files per page */}
      {files.length > 10 && (
        <div className="mt-0.5 mb-0.5 flex justify-between items-center px-2">
          <div className="flex items-center text-gray-400 text-xs">
            <span className="mr-1">Files per page:</span>
            <select
              value={filesPerPage}
              onChange={(e) => {
                const newValue = Number(e.target.value);
                setFilesPerPage(newValue);
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
              className="bg-gray-800 text-gray-200 border border-gray-700 rounded px-1 py-0.5 text-xs"
            >
              <option value="10">10</option>
              <option value="20">20</option>
              <option value="50">50</option>
            </select>
          </div>

          {totalPages > 1 && (
            <FileManagerPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      )}
    </>
  );
};

export default FileList;