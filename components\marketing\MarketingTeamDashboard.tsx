'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Bell, Play, CheckCircle, Clock, AlertTriangle, ExternalLink, FileText, Users } from 'lucide-react';

interface PMONotification {
  id: string;
  pmoId: string;
  projectTitle: string;
  projectDescription: string;
  pmoAssessment: string;
  teamSelectionRationale: string;
  priority: 'Low' | 'Medium' | 'High';
  category: string;
  status: string;
  notifiedAt: any;
  requiresStrategicPlan: boolean;
  strategicPlanCreated: boolean;
  marketingCollaborationRequestId?: string;
  metadata?: any;
}

const MarketingTeamDashboard: React.FC = () => {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<PMONotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [triggeringCollaboration, setTriggeringCollaboration] = useState<string | null>(null);
  const [selectedNotification, setSelectedNotification] = useState<PMONotification | null>(null);

  useEffect(() => {
    if (session?.user?.email) {
      fetchNotifications();
    }
  }, [session]);

  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/pmo-marketing-integration');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch notifications: ${response.statusText}`);
      }

      const data = await response.json();
      setNotifications(data.notifications || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  };

  const triggerMarketingCollaboration = async (notificationId: string) => {
    try {
      setTriggeringCollaboration(notificationId);
      
      const response = await fetch('/api/pmo-marketing-integration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notificationId,
          action: 'trigger-marketing-collaboration'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to trigger marketing collaboration');
      }

      const result = await response.json();
      
      // Refresh notifications to show updated status
      await fetchNotifications();
      
      // Show success message
      alert(`Marketing collaboration triggered successfully! Request ID: ${result.requestId}`);
      
    } catch (err) {
      console.error('Error triggering marketing collaboration:', err);
      alert(err instanceof Error ? err.message : 'Failed to trigger marketing collaboration');
    } finally {
      setTriggeringCollaboration(null);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'text-red-400 bg-red-900/30 border-red-700/50';
      case 'medium': return 'text-yellow-400 bg-yellow-900/30 border-yellow-700/50';
      case 'low': return 'text-green-400 bg-green-900/30 border-green-700/50';
      default: return 'text-gray-400 bg-gray-900/30 border-gray-700/50';
    }
  };

  const getStatusIcon = (notification: PMONotification) => {
    if (notification.strategicPlanCreated) {
      return <CheckCircle className="w-5 h-5 text-green-400" />;
    }
    if (notification.requiresStrategicPlan) {
      return <Clock className="w-5 h-5 text-yellow-400" />;
    }
    return <AlertTriangle className="w-5 h-5 text-red-400" />;
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown date';
    
    let date: Date;
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    } else {
      date = new Date(timestamp);
    }
    
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
        <span className="ml-2 text-gray-300">Loading PMO notifications...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-900/30 border border-red-700/50 rounded-lg">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-400 mr-2" />
          <span className="text-red-300">Error: {error}</span>
        </div>
        <button 
          onClick={fetchNotifications}
          className="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Users className="w-6 h-6 mr-3 text-purple-300" />
          <h2 className="text-xl font-semibold text-purple-200">Marketing Team Dashboard</h2>
        </div>
        <div className="flex items-center text-sm text-gray-400">
          <Bell className="w-4 h-4 mr-1" />
          {notifications.length} PMO notification{notifications.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Notifications List */}
      {notifications.length === 0 ? (
        <div className="text-center py-12 text-gray-400">
          <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No PMO notifications found</p>
          <p className="text-sm mt-1">Check back later for new project requirements</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {notifications.map((notification) => (
            <div
              key={notification.id}
              className="p-6 bg-gray-800/70 rounded-lg border border-gray-700 shadow-lg hover:border-purple-500/50 transition-colors"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  {getStatusIcon(notification)}
                  <h3 className="text-lg font-medium text-white ml-2 truncate">
                    {notification.projectTitle}
                  </h3>
                </div>
                <div className={`px-2 py-1 rounded text-xs border ${getPriorityColor(notification.priority)}`}>
                  {notification.priority}
                </div>
              </div>

              {/* Content */}
              <div className="space-y-3 mb-4">
                <div>
                  <p className="text-sm text-gray-300 line-clamp-2">
                    {notification.projectDescription}
                  </p>
                </div>
                
                <div className="flex items-center text-xs text-gray-400">
                  <Clock className="w-3 h-3 mr-1" />
                  Received: {formatDate(notification.notifiedAt)}
                </div>

                <div className="flex items-center text-xs text-gray-400">
                  <FileText className="w-3 h-3 mr-1" />
                  PMO ID: {notification.pmoId}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-700">
                <button
                  onClick={() => setSelectedNotification(notification)}
                  className="text-sm text-purple-400 hover:text-purple-300 flex items-center"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  View Details
                </button>

                {!notification.strategicPlanCreated ? (
                  <button
                    onClick={() => triggerMarketingCollaboration(notification.id)}
                    disabled={triggeringCollaboration === notification.id}
                    className="px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {triggeringCollaboration === notification.id ? (
                      <>
                        <div className="animate-spin rounded-full h-3 w-3 border-b border-white mr-1"></div>
                        Processing...
                      </>
                    ) : (
                      <>
                        <Play className="w-3 h-3 mr-1" />
                        Start Analysis
                      </>
                    )}
                  </button>
                ) : (
                  <div className="flex items-center text-sm text-green-400">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Analysis Complete
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Notification Details Modal */}
      {selectedNotification && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg border border-gray-700 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-white">
                  {selectedNotification.projectTitle}
                </h3>
                <button
                  onClick={() => setSelectedNotification(null)}
                  className="text-gray-400 hover:text-white"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-medium text-purple-300 mb-1">Project Description</h4>
                  <p className="text-gray-300">{selectedNotification.projectDescription}</p>
                </div>

                <div>
                  <h4 className="font-medium text-purple-300 mb-1">PMO Assessment</h4>
                  <p className="text-gray-300">{selectedNotification.pmoAssessment}</p>
                </div>

                <div>
                  <h4 className="font-medium text-purple-300 mb-1">Team Selection Rationale</h4>
                  <p className="text-gray-300">{selectedNotification.teamSelectionRationale}</p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-purple-300 mb-1">Priority</h4>
                    <span className={`px-2 py-1 rounded text-xs border ${getPriorityColor(selectedNotification.priority)}`}>
                      {selectedNotification.priority}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium text-purple-300 mb-1">Status</h4>
                    <p className="text-gray-300">{selectedNotification.status}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setSelectedNotification(null)}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketingTeamDashboard;
