"use client"
"use client"
import type React from "react"
import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image" // For the logo
import { X, MessageSquare, FileText, Mic, Loader, Info, Bot, Menu, ChevronDown, Upload, BookOpen } from "lucide-react"
import ChatTab from "./ChatTab"
import { collection, query, where, getDocs, doc, getDoc, orderBy } from "firebase/firestore"
import { useGetNamespace } from "./useGetNamespace"
import { useSession } from "next-auth/react"
import { v4 as uuidv4 } from "uuid"
import useUpload, { StatusText } from "./useUpload"
import { db } from "components/firebase"
import SideBar from "./SideBar"
import FileDetails from "./FileDetails"
import DialogueDisplay from "./DialogueDisplay"
import Realtime from "./RealtimeConnection"
import ScriptTab from "./ScriptTab"

// --- Type Definitions ---
interface ScriptFile {
  id: string
  name: string
  namespace: string
}

interface ReadermodalProps {
  isOpen: boolean
  onClose: () => void
  fileId?: string
}

// --- Main Component ---
function Readermodal({ isOpen, onClose, fileId }: ReadermodalProps) {
  // --- Responsive Layout State ---
  const [isMobileView, setIsMobileView] = useState<boolean>(true)

  // --- Core State ---
  const [activeTab, setActiveTab] = useState<string | null>(fileId ?? null)
  const [activeSection, setActiveSection] = useState<string>("connecting")
  const [scriptFiles, setScriptFiles] = useState<ScriptFile[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const isMounted = useRef<boolean>(true)
  const { data: session, status: sessionStatus } = useSession()
  const userId: string = session?.user?.email || ""

  // --- Upload State ---
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [uploadProgress, setUploadProgress] = useState<number | null>(null)
  const [uploadStatusText, setUploadStatusText] = useState<string>("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { handleUpload, progress, status, error: uploadError } = useUpload()

  // --- Script Content State ---
  const [fileName, setFileName] = useState<string | null>(null)
  const [scriptContent, setScriptContent] = useState<string>("")
  const [isScriptLoading, setIsScriptLoading] = useState<boolean>(false)
  const [isScriptReady, setIsScriptReady] = useState<boolean>(false)
  const { namespace } = useGetNamespace(userId, activeTab || null)

  // --- Dialogue State (Shared) ---
  const [realtimeDialogue, setRealtimeDialogue] = useState<string>("")
  const [dialogueHistory, setDialogueHistory] = useState<Array<{ role: string; content: string }>>([])

  // --- WebRTC Status State (Restored for UI display) ---
  const [isListening, setIsListening] = useState<boolean>(false)
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false)
  const [apiConfigStatus, setApiConfigStatus] = useState<"unchecked" | "valid" | "invalid" | "connecting">("unchecked")
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null)
  const [diagnosisInProgress, setDiagnosisInProgress] = useState<boolean>(false)
  const [voiceErrorMessage, setVoiceErrorMessage] = useState<string>("")
  const [hasPermission, setHasPermission] = useState<boolean>(false)
  const [isMuted, setIsMuted] = useState<boolean>(false)

  // --- UI State ---
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState<boolean>(false)
  const [scriptSelectOpen, setScriptSelectOpen] = useState<boolean>(false)

  // --- Interaction Mode State ---
  const [interactionMode, setInteractionMode] = useState<"direct" | "conversational">("conversational")

  // --- Voice Settings State ---
  const [voiceGender, setVoiceGender] = useState<"male" | "female">("male")
  const [voiceTone, setVoiceTone] = useState<"neutral" | "professional" | "friendly" | "dramatic">("neutral")
  const [emotionalIntensity, setEmotionalIntensity] = useState<"low" | "medium" | "high">("medium")
  const [readingPace, setReadingPace] = useState<"slow" | "normal" | "fast">("normal")

  // --- Effects ---

  // Effect: Detect mobile view
  useEffect(() => {
    const checkMobileView = () => {
      const isMobile = window.innerWidth < 768 // md breakpoint
      setIsMobileView(isMobile)
    }
    checkMobileView()
    window.addEventListener("resize", checkMobileView)
    return () => window.removeEventListener("resize", checkMobileView)
  }, [])

  // Effect: Update activeTab if fileId prop changes externally
  useEffect(() => {
    if (fileId && fileId !== activeTab) {
      console.log("[Effect] Setting activeTab from fileId prop:", fileId)
      setActiveTab(fileId)
      setScriptContent("")
      setIsScriptReady(false)
      setFileName(null)
      setDialogueHistory([])
      setRealtimeDialogue("")
    }
  }, [fileId, activeTab])

  // Effect: Fetch script content when activeTab or userId changes
  useEffect(() => {
    const fetchScriptContent = async () => {
      if (!activeTab || !userId) {
        setScriptContent("")
        setFileName(null)
        setIsScriptReady(false)
        return
      }
      console.log(`[Effect] Fetching script content for activeTab: ${activeTab}`)
      setIsScriptLoading(true)
      setError(null)
      setScriptContent("")
      setFileName(null)
      setIsScriptReady(false)
      try {
        const fileDocRef = doc(db, `users/${userId}/files/${activeTab}`)
        const fileDocSnap = await getDoc(fileDocRef)
        if (!fileDocSnap.exists()) {
          setError("Selected script file not found.")
          setIsScriptLoading(false)
          return
        }
        const fileData = fileDocSnap.data()
        const fileNamespace = fileData.namespace || activeTab
        const currentFileName = fileData.name || "Untitled Script"
        setFileName(currentFileName)

        const chunksRef = collection(db, `users/${userId}/byteStoreCollection`)
        const q = query(chunksRef, where("metadata.doc_id", "==", fileNamespace))
        const querySnapshot = await getDocs(q)
        if (querySnapshot.empty) {
          setError("No content found for this script.")
          setIsScriptLoading(false)
          return
        }
        const chunks = querySnapshot.docs.map((d) => d.data())
        if (chunks.length > 0 && "position" in chunks[0]) {
          chunks.sort((a, b) => (a.position || 0) - (b.position || 0))
        } else if (chunks.length > 0 && "metadata" in chunks[0] && "page_number" in chunks[0].metadata) {
          chunks.sort((a, b) => (a.metadata.page_number || 0) - (b.metadata.page_number || 0))
        }
        const contentField = chunks[0]?.pageContent !== undefined ? "pageContent" : "content"
        const content = chunks.map((chunk) => chunk[contentField] || "").join("\n")
        if (!content?.trim()) {
          setError("Script appears to be empty.")
          setIsScriptLoading(false)
          return
        }
        setScriptContent(content)
        setIsScriptReady(true)
      } catch (err) {
        console.error("Error fetching script content:", err)
        setError("Failed to load script content: " + (err instanceof Error ? err.message : String(err)))
        setIsScriptReady(false)
      } finally {
        setIsScriptLoading(false)
      }
    }
    fetchScriptContent()
  }, [activeTab, userId])

  // Effect: Fetch list of script files
  const fetchScriptFiles = useCallback(async () => {
    if (sessionStatus !== "authenticated" || !userId) {
      setError(sessionStatus === "unauthenticated" ? "Please sign in." : null)
      setScriptFiles([])
      setLoading(false)
      return
    }
    setLoading(true)
    setError(null)
    try {
      const filesRef = collection(db, `users/${userId}/files`)
      const q = query(filesRef, where("category", "==", "SceneMate"), orderBy("name", "asc"))
      const querySnapshot = await getDocs(q)
      const files: ScriptFile[] = querySnapshot.docs.map((d) => ({
        id: d.id,
        name: d.data().name || "Untitled Script",
        namespace: d.data().namespace || d.id,
      }))
      setScriptFiles(files)

      const currentActiveTabStillValid = activeTab && files.some((f) => f.id === activeTab)
      const fileIdPropIsValid = fileId && files.some((f) => f.id === fileId)

      if (fileIdPropIsValid && activeTab !== fileId) {
        setActiveTab(fileId)
      } else if (!currentActiveTabStillValid && files.length > 0) {
        setActiveTab(files[0].id)
      } else if (files.length === 0) {
        setActiveTab(null)
      }
    } catch (err) {
      console.error("Error fetching script files:", err)
      setError("Failed to load scripts: " + (err instanceof Error ? err.message : String(err)))
      setScriptFiles([])
    } finally {
      setLoading(false)
    }
  }, [userId, sessionStatus, fileId, activeTab])

  useEffect(() => {
    fetchScriptFiles()
  }, [fetchScriptFiles])

  // Effect: Handle upload status updates
  useEffect(() => {
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`)
      setIsUploading(false)
      setUploadProgress(null)
      setUploadStatusText("Upload failed")
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true)
      setUploadProgress(progress || 0)
      setUploadStatusText("Uploading...")
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true)
      setUploadProgress(100)
      setUploadStatusText("Processing...")
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false)
      setUploadProgress(null)
      setUploadStatusText("Upload complete!")
      fetchScriptFiles() // Refresh script list
      setTimeout(() => setUploadStatusText(""), 3000)
    }
  }, [status, progress, uploadError, fetchScriptFiles])

  // Effect: Component mount/unmount cleanup
  useEffect(() => {
    isMounted.current = true
    const handleVoiceSettingsUpdate = (event: CustomEvent) => {
      if (isMounted.current) {
        const settings = event.detail
        console.log("Received voice settings update:", settings)
        if (settings.gender) setVoiceGender(settings.gender)
        if (settings.tone) setVoiceTone(settings.tone)
        if (settings.emotionalIntensity) setEmotionalIntensity(settings.emotionalIntensity)
        if (settings.readingPace) setReadingPace(settings.readingPace)
      }
    }
    document.addEventListener("voice-settings-update", handleVoiceSettingsUpdate as EventListener)
    return () => {
      isMounted.current = false
      document.removeEventListener("voice-settings-update", handleVoiceSettingsUpdate as EventListener)
      console.log("[Effect] Unmounting Readermodal, ensuring cleanup.")
    }
  }, [])

  // Effect: Handle mobile sidebar visibility on screen resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileSidebarOpen(false)
      }
    }
    window.addEventListener("resize", handleResize)
    handleResize() // Initial check
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  // Effect: Auto-switch to Script tab when connection is established
  useEffect(() => {
    if (apiConfigStatus === "valid" && isListening && activeSection === "connecting") {
      const timer = setTimeout(() => {
        if (isMounted.current) {
          setActiveSection("script")
          console.log("[Effect] Auto-switching to Script tab after connection established")
        }
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [apiConfigStatus, isListening, activeSection])

  // --- File Upload Handlers ---
  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  const handleFileUpload = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0]
      if (!file || !userId) {
        setError("No file selected or user not authenticated.")
        if (fileInputRef.current) fileInputRef.current.value = ""
        return
      }
      if (!isMounted.current) return
      setIsUploading(true)
      setError(null)
      setUploadStatusText("Starting upload...")
      try {
        const docId: string = uuidv4()
        console.log(`[Upload] Initiating upload for ${file.name} with docId: ${docId}`)
        await handleUpload(file, null, userId, docId)
        if (isMounted.current) {
          setActiveTab(docId)
          console.log(`[Upload] Set active tab to ${docId}`)
          setFileName(file.name)
          setIsScriptReady(false)
          setScriptContent("")
          setDialogueHistory([])
          setRealtimeDialogue("")
        }
      } catch (err) {
        console.error("[Upload] Failed:", err)
        if (isMounted.current) {
          setError(err instanceof Error ? `Upload failed: ${err.message}` : "Upload failed")
          setIsUploading(false)
          setUploadStatusText("Upload failed")
        }
      } finally {
        if (fileInputRef.current) fileInputRef.current.value = ""
      }
    },
    [userId, handleUpload],
  )

  // --- Script Selection & Mobile Sidebar Handlers ---
  const toggleScriptSelect = useCallback(() => {
    setScriptSelectOpen((prev) => !prev)
  }, [])

  const handleScriptSelection = useCallback(
    (scriptId: string) => {
      if (scriptId !== activeTab) {
        setActiveTab(scriptId)
        setDialogueHistory([])
        setRealtimeDialogue("")
      }
      setScriptSelectOpen(false)
    },
    [activeTab],
  )

  const toggleMobileSidebar = useCallback(() => {
    setIsMobileSidebarOpen((prev) => !prev)
  }, [])

  // --- Render Logic ---
  if (!isOpen) return null

  const selectedScriptName = activeTab
    ? scriptFiles.find((f) => f.id === activeTab)?.name || "Select a script"
    : "Select a script"

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="relative w-full h-full max-h-full sm:h-[90vh] md:h-[85vh] sm:max-w-3xl md:max-w-4xl lg:max-w-6xl bg-[#070706] rounded-lg sm:rounded-xl md:rounded-2xl shadow-2xl overflow-hidden border border-gray-500"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-white/5 to-transparent pointer-events-none" />

        {/* Top-right elements: Logo and buttons group */}
        {/* Ensure Clogo2.png is in /public folder (e.g. public/Clogo2.png) */}
        <div className="absolute top-0 right-3 flex items-center space-x-2 z-50 mt-3 ">


          {isMobileView && (
            <button
              onClick={handleUploadClick}
              className="p-1.5 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200 backdrop-blur-sm"
              disabled={isUploading}
              aria-label="Upload script"
            >
              {isUploading ? (
                <Loader className="w-4 h-4 text-purple-300 animate-spin" />
              ) : (
                <Upload className="w-4 h-4" />
              )}
            </button>
          )}



          <button
            onClick={onClose}
            className="p-1.5 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200 backdrop-blur-sm"
            aria-label="Close modal"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Status Indicators moved to Connection Status panel */}

        {/* Mobile Layout */}
        {isMobileView ? (
          <div className="flex flex-col h-full">
            <div className="flex items-center px-4 py-3 border-b border-white/10 bg-[#070706]/90">
              <div className="flex items-center">
                <div className="text-green-400 text-sm font-medium">Scripts</div>
              </div>
            </div>

            <div className="relative px-4 py-2 border-b border-white/10 bg-[#070706]/80">
              <button
                onClick={toggleScriptSelect}
                className="w-full flex items-center justify-between px-3 py-2 bg-gray-800/50 hover:bg-gray-700/50 rounded-md text-left text-sm text-white transition-colors"
              >
                <span className="truncate">{isScriptLoading ? "Loading script..." : selectedScriptName}</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${scriptSelectOpen ? "rotate-180" : ""}`} />
              </button>

              {scriptSelectOpen && (
                <div className="absolute left-4 right-4 mt-1 py-1 bg-gray-800 rounded-md shadow-lg z-20 max-h-60 overflow-y-auto scrollbar-thin scrollbar-track-gray-900 scrollbar-thumb-gray-700">
                  {loading ? (
                    <div className="px-4 py-2 text-sm text-gray-400 flex items-center">
                      <Loader className="w-3 h-3 mr-2 animate-spin" />
                      Loading scripts...
                    </div>
                  ) : scriptFiles.length === 0 ? (
                    <div className="px-4 py-2 text-sm text-gray-400">No scripts found</div>
                  ) : (
                    scriptFiles.map((file) => (
                      <button
                        key={file.id}
                        onClick={() => handleScriptSelection(file.id)}
                        className={`w-full px-4 py-2 text-sm text-left hover:bg-gray-700 transition-colors ${
                          activeTab === file.id ? "text-purple-300 bg-purple-900/30" : "text-gray-200"
                        }`}
                      >
                        {file.name}
                      </button>
                    ))
                  )}
                </div>
              )}
            </div>

            {isUploading && (
              <div className="px-4 py-2 border-b border-white/10 bg-[#070706]/80">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs text-gray-300">{uploadStatusText}</span>
                  <span className="text-xs text-gray-400">
                    {uploadProgress !== null ? `${Math.round(uploadProgress)}%` : ""}
                  </span>
                </div>
                <div className="w-full h-1 bg-gray-800 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-purple-500 transition-all"
                    style={{ width: `${uploadProgress ?? 0}%` }}
                  ></div>
                </div>
              </div>
            )}

            <div className="flex-1 overflow-hidden">
              <div style={{ display: activeSection === "connecting" ? "block" : "none" }}>
                <Realtime
                  scriptContent={scriptContent}
                  isScriptLoading={isScriptLoading}
                  isScriptReady={isScriptReady}
                  scriptName={fileName}
                  dialogueHistory={dialogueHistory}
                  setDialogueHistory={setDialogueHistory}
                  realtimeDialogue={realtimeDialogue}
                  setRealtimeDialogue={setRealtimeDialogue}
                  interactionMode={interactionMode}
                  setInteractionMode={setInteractionMode}
                  voiceGender={voiceGender}
                  setVoiceGender={setVoiceGender}
                  voiceTone={voiceTone}
                  emotionalIntensity={emotionalIntensity}
                  readingPace={readingPace}
                  setExternalIsListening={setIsListening}
                  setExternalIsSpeaking={setIsSpeaking}
                  setExternalApiConfigStatus={setApiConfigStatus}
                  setExternalDetailedErrorInfo={setDetailedErrorInfo}
                  setExternalDiagnosisInProgress={setDiagnosisInProgress}
                  setExternalVoiceErrorMessage={setVoiceErrorMessage}
                  setExternalIsMuted={setIsMuted}
                  setExternalHasPermission={setHasPermission}
                />
              </div>

              {activeSection === "script" && apiConfigStatus === "valid" && isListening ? (
                <ScriptTab
                  scriptContent={scriptContent}
                  isScriptLoading={isScriptLoading}
                  isScriptReady={isScriptReady}
                  scriptName={fileName}
                  isListening={isListening}
                  isMuted={isMuted}
                  toggleMute={() => {
                    // Create a custom event to toggle mute
                    const toggleMuteEvent = new CustomEvent("toggle-mute");
                    document.dispatchEvent(toggleMuteEvent);
                  }}
                  handleEndConversation={async () => {
                    try {
                      setActiveSection("connecting")
                      await new Promise((resolve) => setTimeout(resolve, 100))
                      setIsListening(false)
                      setIsSpeaking(false)
                      setApiConfigStatus("unchecked")
                      setRealtimeDialogue("")
                      const endEvent = new CustomEvent("end-rehearsal-from-script")
                      document.dispatchEvent(endEvent)
                      console.log("[ScriptTab] Ended rehearsal from Script tab")
                    } catch (error) {
                      console.error("[ScriptTab] Error ending rehearsal:", error)
                    }
                  }}
                />
              ) : null}

              {activeSection === "dialogue" && apiConfigStatus === "valid" && isListening ? (
                <DialogueDisplay
                  dialogue={dialogueHistory}
                  currentResponse={realtimeDialogue}
                  isSpeaking={isSpeaking}
                  isListening={isListening}
                />
              ) : null}

              {activeSection === "chat" && (
                <ChatTab chatId={activeTab ?? ""} namespace={namespace} fileId={activeTab} />
              )}

              {activeSection === "details" && (
                <FileDetails
                  activeTab={activeTab}
                  fileName={fileName}
                  namespace={namespace}
                  apiConfigStatus={apiConfigStatus}
                  sessionStatus={sessionStatus}
                  detailedErrorInfo={detailedErrorInfo}
                />
              )}
            </div>

            <div className="border-b border-white/10 px-4 sm:px-6 py-3 sm:py-4 bg-[#070706]/80 relative">
              {/* Logo removed as requested */}
              <div className="flex flex-wrap gap-4 sm:space-x-6 pt-3">
                {[
                  { name: "connecting", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: false },
                  { name: "script", icon: <BookOpen className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: apiConfigStatus !== "valid" || !isListening },
                  { name: "dialogue", icon: <Bot className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: apiConfigStatus !== "valid" || !isListening },
                  { name: "chat", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: false },
                  { name: "details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: false },
                ].map((section) => (
                  <button
                    key={section.name}
                    onClick={() => !section.disabled && setActiveSection(section.name)}
                    disabled={section.disabled}
                    className={`text-xs sm:text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
                      activeSection === section.name
                        ? "border-white text-white"
                        : section.disabled
                          ? "border-transparent text-gray-500 opacity-50 cursor-not-allowed"
                          : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
                    }`}
                  >
                    {section.icon}
                    {section.name.charAt(0).toUpperCase() + section.name.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileUpload}
              accept=".pdf,.docx,.doc,.txt,.jpg,.jpeg,.png"
            />
          </div>
        ) : (
          /* Desktop Layout */
          <div className="flex flex-row h-full w-full">
            <div className="hidden md:block md:flex-none md:w-64">
              <SideBar
                activeTab={activeTab}
                setActiveTab={(tabId) => {
                  if (tabId !== activeTab) {
                    setActiveTab(tabId)
                    setDialogueHistory([])
                    setRealtimeDialogue("")
                  }
                }}
                scriptFiles={scriptFiles}
                loading={loading}
                error={error && error.startsWith("Failed to load scripts") ? error : null}
                setError={setError}
                isUploading={isUploading}
                uploadProgress={uploadProgress}
                uploadStatusText={uploadStatusText}
                handleUploadClick={handleUploadClick}
                handleFileUpload={handleFileUpload}
                fileInputRef={fileInputRef}
                sessionStatus={sessionStatus}
                session={session}
                onClose={onClose}
                isMobileSidebarOpen={isMobileSidebarOpen}
                setIsMobileSidebarOpen={setIsMobileSidebarOpen}
                voiceStatus={isListening ? "connected" : "disconnected"}
                isConnecting={apiConfigStatus === "connecting" || apiConfigStatus === "invalid" || diagnosisInProgress || sessionStatus === "loading"}
              />
            </div>

            <button
              onClick={toggleMobileSidebar}
              className="md:hidden absolute top-3 left-3 p-1.5 z-40 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all"
              aria-label={isMobileSidebarOpen ? "Close sidebar" : "Open sidebar"}
            >
              <Menu className="w-4 h-4" />
            </button>

            <AnimatePresence>
              {isMobileSidebarOpen && (
                <motion.div
                  key="sm-tablet-sidebar"
                  initial={{ x: -250, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: -250, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="absolute inset-y-0 left-0 z-30 md:hidden"
                >
                  <div className="h-full w-64 bg-[#070706]/90 backdrop-blur-md">
                    <SideBar
                      activeTab={activeTab}
                      setActiveTab={(tabId) => {
                        if (tabId !== activeTab) {
                          setActiveTab(tabId)
                          setDialogueHistory([])
                          setRealtimeDialogue("")
                          setIsMobileSidebarOpen(false) // Close sidebar on selection
                        }
                      }}
                      scriptFiles={scriptFiles}
                      loading={loading}
                      error={error && error.startsWith("Failed to load scripts") ? error : null}
                      setError={setError}
                      isUploading={isUploading}
                      uploadProgress={uploadProgress}
                      uploadStatusText={uploadStatusText}
                      handleUploadClick={handleUploadClick}
                      handleFileUpload={handleFileUpload}
                      fileInputRef={fileInputRef}
                      sessionStatus={sessionStatus}
                      session={session}
                      onClose={onClose}
                      isMobileSidebarOpen={isMobileSidebarOpen}
                      setIsMobileSidebarOpen={setIsMobileSidebarOpen}
                      voiceStatus={isListening ? "connected" : "disconnected"}
                      isConnecting={apiConfigStatus === "connecting" || apiConfigStatus === "invalid" || diagnosisInProgress || sessionStatus === "loading"}
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div
              className={`flex-1 flex flex-col overflow-hidden backdrop-blur-sm bg-[#070706]/90 transition-all duration-200 ${isMobileSidebarOpen && !isMobileView ? "ml-64 md:ml-0" : "ml-0"}`}
            >
              <div className="border-b border-white/10 px-4 sm:px-6 py-3 sm:py-4 bg-black/20 text-center relative">
                {/* Logo removed as requested */}
                <div className="flex flex-wrap gap-4 sm:space-x-6 pt-3">
                  {[
                    { name: "connecting", icon: <Mic className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: false },
                    { name: "script", icon: <BookOpen className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: apiConfigStatus !== "valid" || !isListening },
                    { name: "dialogue", icon: <Bot className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: apiConfigStatus !== "valid" || !isListening },
                    { name: "chat", icon: <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: false },
                    { name: "details", icon: <FileText className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />, disabled: false },
                  ].map((section) => (
                    <button
                      key={section.name}
                      onClick={() => !section.disabled && setActiveSection(section.name)}
                      disabled={section.disabled}
                      className={`text-xs sm:text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
                        activeSection === section.name
                          ? "border-white text-white"
                          : section.disabled
                            ? "border-transparent text-gray-500 opacity-50 cursor-not-allowed"
                            : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
                      }`}
                    >
                      {section.icon}
                      {section.name.charAt(0).toUpperCase() + section.name.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
                <div style={{ display: activeSection === "connecting" ? "block" : "none" }}>
                  <Realtime
                    scriptContent={scriptContent}
                    isScriptLoading={isScriptLoading}
                    isScriptReady={isScriptReady}
                    scriptName={fileName}
                    dialogueHistory={dialogueHistory}
                    setDialogueHistory={setDialogueHistory}
                    realtimeDialogue={realtimeDialogue}
                    setRealtimeDialogue={setRealtimeDialogue}
                    interactionMode={interactionMode}
                    setInteractionMode={setInteractionMode}
                    voiceGender={voiceGender}
                    setVoiceGender={setVoiceGender}
                    voiceTone={voiceTone}
                    emotionalIntensity={emotionalIntensity}
                    readingPace={readingPace}
                    setExternalIsListening={setIsListening}
                    setExternalIsSpeaking={setIsSpeaking}
                    setExternalApiConfigStatus={setApiConfigStatus}
                    setExternalDetailedErrorInfo={setDetailedErrorInfo}
                    setExternalDiagnosisInProgress={setDiagnosisInProgress}
                    setExternalVoiceErrorMessage={setVoiceErrorMessage}
                    setExternalIsMuted={setIsMuted}
                    setExternalHasPermission={setHasPermission}
                    sessionStatus={sessionStatus}
                  />
                </div>

                <AnimatePresence mode="wait">
                  {activeSection !== "connecting" &&
                  ((activeSection !== "script" && activeSection !== "dialogue") ||
                    (apiConfigStatus === "valid" && isListening)) ? (
                    <motion.div
                      key={`${activeTab || "no-tab"}-${activeSection}`}
                      initial={{ opacity: 0, y: 15 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -15 }}
                      transition={{ duration: 0.2 }}
                      className="h-full"
                    >
                      {activeSection === "script" && (
                        <ScriptTab
                          scriptContent={scriptContent}
                          isScriptLoading={isScriptLoading}
                          isScriptReady={isScriptReady}
                          scriptName={fileName}
                          isListening={isListening}
                          isMuted={isMuted}
                          toggleMute={() => {
                            // Create a custom event to toggle mute
                            const toggleMuteEvent = new CustomEvent("toggle-mute");
                            document.dispatchEvent(toggleMuteEvent);
                          }}
                          handleEndConversation={async () => {
                            try {
                              setActiveSection("connecting")
                              await new Promise((resolve) => setTimeout(resolve, 100))
                              setIsListening(false)
                              setIsSpeaking(false)
                              setApiConfigStatus("unchecked")
                              setRealtimeDialogue("")
                              const endEvent = new CustomEvent("end-rehearsal-from-script")
                              document.dispatchEvent(endEvent)
                              console.log("[ScriptTab] Ended rehearsal from Script tab")
                            } catch (error) {
                              console.error("[ScriptTab] Error ending rehearsal:", error)
                            }
                          }}
                        />
                      )}
                      {activeSection === "dialogue" && (
                        <DialogueDisplay
                          dialogue={dialogueHistory}
                          currentResponse={realtimeDialogue}
                          isSpeaking={isSpeaking}
                          isListening={isListening}
                        />
                      )}
                      {activeSection === "chat" && (
                        <ChatTab chatId={activeTab ?? ""} namespace={namespace} fileId={activeTab} />
                      )}
                      {activeSection === "details" && (
                        <FileDetails
                          activeTab={activeTab}
                          fileName={fileName}
                          namespace={namespace}
                          apiConfigStatus={apiConfigStatus}
                          sessionStatus={sessionStatus}
                          detailedErrorInfo={detailedErrorInfo}
                        />
                      )}
                    </motion.div>
                  ) : activeSection !== "connecting" ? (
                    <motion.div
                      key="redirect-to-connecting"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 0 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.1 }}
                      onAnimationComplete={() => {
                        // Redirect to connecting tab immediately.
                        // The 'if (activeSection !== "connecting")' check was removed from here as it's redundant.
                        setActiveSection("connecting");
                      }}
                      className="hidden" // This div itself is not visible, just triggers action
                    />
                  ) : null}
                </AnimatePresence>
              </div>
            </div>

            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              onChange={handleFileUpload}
              accept=".pdf,.docx,.doc,.txt,.jpg,.jpeg,.png"
            />
          </div>
        )}

        <style jsx global>{`
          .status-badge {
            @apply text-xs px-2 py-1 rounded-md flex items-center;
            font-size: 0.7rem;
            display: inline-flex;
            white-space: nowrap;
          }
          @media (min-width: 640px) {
            .status-badge {
              font-size: 0.75rem;
            }
          }
          .tab-button {
            @apply text-sm font-medium px-3 py-1.5 border-b-2 transition-all duration-200 flex items-center rounded-t-md;
          }
          .tab-button-active {
            @apply border-purple-500 text-purple-300 bg-white/5;
          }
          .tab-button-inactive {
            @apply border-transparent text-gray-400 hover:text-white hover:border-white/20;
          }
        `}</style>
      </motion.div>
    </div>
  )
}

export { Readermodal }