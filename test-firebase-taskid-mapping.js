/**
 * Test script for Firebase-based TaskId mapping in PMOProjectsTaskAgent
 * Verifies that the system now uses actual Firebase Project IDs instead of synthetic PMO format IDs
 */

const { PMOProjectsTaskAgent } = require('./lib/agents/pmoProjectsTaskAgent');

// Mock Firebase Admin for testing
jest.mock('./components/firebase-admin', () => ({
  adminDb: {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        collection: jest.fn(() => ({
          doc: jest.fn(() => ({
            get: jest.fn()
          })),
          where: jest.fn(() => ({
            get: jest.fn()
          }))
        }))
      }))
    }))
  }
}));

async function testFirebaseTaskIdMapping() {
  console.log('🧪 TESTING FIREBASE-BASED TASKID MAPPING');
  console.log('=' .repeat(50));
  
  const agent = new PMOProjectsTaskAgent();
  const specificPmoId = 'c76670a7-bc7b-44ea-9905-189a4bcf36c8';
  const testUserId = '<EMAIL>';
  
  // Mock Firebase responses
  const { adminDb } = require('./components/firebase-admin');
  
  // Mock PMO document with generatedProjects array
  const mockPMODoc = {
    exists: true,
    data: () => ({
      generatedProjects: [
        {
          id: 'oldProject1',
          createdAt: '2025-06-27T10:00:00.000Z'
        },
        {
          id: 'ywgeXa9Ppz33JwxTjw0M', // Most recent project
          createdAt: '2025-06-28T07:52:40.531Z'
        }
      ]
    })
  };
  
  // Mock tasks collection response
  const mockTasksSnapshot = {
    docs: [
      { id: 'task1_firebase_id' },
      { id: 'task2_firebase_id' },
      { id: 'task3_firebase_id' }
    ]
  };
  
  // Setup mocks
  adminDb.collection().doc().collection().doc().get.mockResolvedValue(mockPMODoc);
  adminDb.collection().doc().collection().where().get.mockResolvedValue(mockTasksSnapshot);
  
  console.log('📋 TEST SCENARIO:');
  console.log(`  - PMO ID: ${specificPmoId}`);
  console.log(`  - User ID: ${testUserId}`);
  console.log(`  - Expected Project ID: ywgeXa9Ppz33JwxTjw0M`);
  console.log(`  - Expected Task IDs: [task1_firebase_id, task2_firebase_id, task3_firebase_id]`);
  console.log('');
  
  // Test content with various TaskId patterns
  const testContent = `
# Strategic Analysis Report

## Task Assignments

Task ID: QA_001 - Quality Assurance Review
TaskId: COMPLIANCE_002 - Compliance Check
ID: RPT_003 - Reporting Task
Task QA_004 - Additional Quality Check

## Implementation Details

The QA_005 task requires coordination with COMPLIANCE_006.
Task-QA-007 should be completed before COMPLIANCE-008.
Final review QA_009 and QA-010 tasks.
  `;
  
  try {
    console.log('🔄 TESTING _getActualProjectIdFromPMO...');
    
    // Test the private method directly
    const actualProjectData = await agent._getActualProjectIdFromPMO(specificPmoId, testUserId);
    
    if (actualProjectData) {
      console.log('✅ SUCCESS: Retrieved actual project data');
      console.log(`  - Project ID: ${actualProjectData.projectId}`);
      console.log(`  - Task IDs: [${actualProjectData.taskIds.join(', ')}]`);
      
      // Verify correct project ID
      if (actualProjectData.projectId === 'ywgeXa9Ppz33JwxTjw0M') {
        console.log('✅ CORRECT: Most recent project ID retrieved');
      } else {
        console.log('❌ ERROR: Wrong project ID retrieved');
      }
      
      // Verify task IDs
      if (actualProjectData.taskIds.length === 3) {
        console.log('✅ CORRECT: All task IDs retrieved');
      } else {
        console.log('❌ ERROR: Wrong number of task IDs');
      }
    } else {
      console.log('❌ ERROR: No project data retrieved');
    }
    
    console.log('');
    console.log('🔄 TESTING _mapTaskIdsToProjectIds...');
    
    // Test the mapping function
    const mappedContent = await agent._mapTaskIdsToProjectIds(testContent, specificPmoId, testUserId);
    
    console.log('📄 MAPPED CONTENT:');
    console.log('-'.repeat(40));
    console.log(mappedContent);
    console.log('');
    
    // Analyze mapping results
    console.log('📊 MAPPING ANALYSIS:');
    console.log('-'.repeat(40));
    
    const originalTaskIds = ['QA_001', 'COMPLIANCE_002', 'RPT_003', 'QA_004', 'QA_005', 'COMPLIANCE_006', 'QA_007', 'COMPLIANCE-008', 'QA_009', 'QA-010'];
    const firebaseProjectId = 'ywgeXa9Ppz33JwxTjw0M';
    const firebaseTaskIds = ['task1_firebase_id', 'task2_firebase_id', 'task3_firebase_id'];
    
    let mappedCount = 0;
    let firebaseIdCount = 0;
    let unmappedTaskIds = [];
    
    for (const taskId of originalTaskIds) {
      if (!mappedContent.includes(taskId)) {
        mappedCount++;
      } else {
        unmappedTaskIds.push(taskId);
      }
    }
    
    // Check for Firebase IDs in content
    if (mappedContent.includes(firebaseProjectId)) {
      firebaseIdCount++;
    }
    
    for (const taskId of firebaseTaskIds) {
      if (mappedContent.includes(taskId)) {
        firebaseIdCount++;
      }
    }
    
    console.log(`✅ Original TaskIds mapped: ${mappedCount}/${originalTaskIds.length}`);
    console.log(`✅ Firebase IDs found: ${firebaseIdCount}`);
    console.log(`❌ Unmapped TaskIds: [${unmappedTaskIds.join(', ')}]`);
    
    if (mappedCount === originalTaskIds.length && firebaseIdCount > 0) {
      console.log('🎉 FIREBASE TASKID MAPPING SUCCESSFUL!');
      console.log('   All TaskIds have been properly mapped to actual Firebase document IDs.');
      console.log('   ✅ Data Flow: PMO Collection → generatedProjects[latest] → Firebase Project ID');
    } else {
      console.log('⚠️  FIREBASE TASKID MAPPING ISSUES DETECTED!');
      console.log('   Some TaskIds may not have been properly mapped to Firebase IDs.');
    }
    
  } catch (error) {
    console.error('❌ ERROR DURING FIREBASE TASKID MAPPING TEST:');
    console.error(error);
  }
}

// Run the test
if (require.main === module) {
  testFirebaseTaskIdMapping();
}

module.exports = { testFirebaseTaskIdMapping };
