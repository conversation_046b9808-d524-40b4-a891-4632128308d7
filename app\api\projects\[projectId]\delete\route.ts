/**
 * API route for deleting a project
 *
 * This route handles requests to delete a project and all its associated data.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]/authOptions';
import { ProjectService } from '../../../../../lib/services/projectService';

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get project ID from params
    const { projectId } = await params;
    if (!projectId) {
      return NextResponse.json(
        { error: 'Missing project ID' },
        { status: 400 }
      );
    }

    // Get user ID from session (using email as ID)
    const userId = session.user.email || 'anonymous-user';

    // Delete the project
    const result = await ProjectService.deleteProject(projectId, userId);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || result.message },
        { status: 400 }
      );
    }

    // Return success response
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in delete project API route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}
