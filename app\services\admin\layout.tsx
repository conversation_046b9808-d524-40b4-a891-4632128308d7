'use client';

import React, { ReactNode, useEffect } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import {
  LayoutDashboard,
  Calendar,
  Users,
  Settings,
  BarChart2,
  Menu,
  X,
  LogOut,
  UserCheck
} from 'lucide-react';
import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useAuth } from '../../context/AuthContext';
import Image from 'next/image';

interface AdminLayoutProps {
  children: ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { data: session } = useSession();
  const { user, loading, isAuthorized, signOut, checkAuthorization } = useAuth();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user && pathname !== '/services/admin/login') {
      router.push('/services/admin/login');
    }
  }, [user, loading, pathname, router]);

  // Check authorization when user changes
  useEffect(() => {
    const checkAccess = async () => {
      // Special case for system admin via NextAuth session
      if (session?.user?.email) {
        console.log('User detected in NextAuth session, checking authorization');
        // Authorization check will be handled by the backend
      }

      if (user && !loading) {
        console.log('Authorization check:', { user: user.email, isAuthorized });

        // Check if user has admin role
        if (user.role === 'admin') {
          console.log('Admin user detected, authorization granted');
          return;
        }

        // Check if user is authorized
        if (!isAuthorized && pathname !== '/services/admin/login' && pathname !== '/services/admin/unauthorized') {
          // User is authenticated but not authorized
          console.log('Redirecting to unauthorized page');
          router.push('/services/admin/unauthorized');
        }
      }
    };

    checkAccess();
  }, [session, user, loading, isAuthorized, pathname, router]);

  const navItems = [
    { name: 'Dashboard', href: '/services/admin', icon: <LayoutDashboard className="w-5 h-5" /> },
    { name: 'Project Planner', href: '/services/admin/planner', icon: <Calendar className="w-5 h-5" /> },
    { name: 'Team', href: '/services/admin/team', icon: <Users className="w-5 h-5" /> },
    { name: 'Analytics', href: '/services/admin/analytics', icon: <BarChart2 className="w-5 h-5" /> },
    { name: 'Marketing Access', href: '/services/admin/marketing-access', icon: <UserCheck className="w-5 h-5" /> },
    { name: 'Settings', href: '/services/admin/settings', icon: <Settings className="w-5 h-5" /> },
  ];

  const externalNavItems = [
    { name: 'Home Page', href: '/', icon: <LayoutDashboard className="w-5 h-5" /> },
    { name: 'Script Reader', href: '/scriptReader', icon: <Calendar className="w-5 h-5" /> },
  ];

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/services/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  // If not logged in and not on login page, don't render anything (will redirect)
  if (!user && pathname !== '/services/admin/login') {
    return null;
  }

  // Don't show layout on login page
  if (pathname === '/services/admin/login') {
    return <>{children}</>;
  }

  return (
    <div className="flex h-screen bg-ike-dark-purple text-gray-100">
      {/* Mobile sidebar toggle */}
      <div className="fixed top-0 left-0 z-40 md:hidden p-4">
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-md bg-gray-800 shadow-md text-gray-200"
        >
          {isSidebarOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-ike-dark-purple shadow-lg transform transition-transform duration-300 ease-in-out md:translate-x-0 ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
        {/* <Link href="/" className="flex items-center space-x-1 mt-2 ml-6 text-purple-300 hover:text-gray-600 transition-colors duration-200">
              <Image
                src="/logo.png"
                alt="Company Logo"
                width={50}
                height={50}
                className="hidden sm:block"
                style={{ width: '35%', height: 'auto' }}
              />

            </Link> */}

            <Link href="/">
            <img
              src="/logo5b.png"
              alt="ike Logo"
              className="h-12 w-auto mt-2 ml-6 -mb-3 md:mb-0 md:mr-0 hover:opacity-80 transition-opacity duration-300"
            />
          </Link>


          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            <div className="mb-6">
              <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Admin</h3>
              <div className="mt-2 space-y-2">
                {navItems.map((item) => {
                  const isActive = pathname === item.href ||
                    (item.href !== '/services/admin' && pathname?.startsWith(item.href));

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={`flex items-center px-4 py-3 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-ike-purple text-purple-300'
                          : 'text-gray-300 hover:bg-gray-700'
                      }`}
                    >
                      <span className={`${isActive ? 'text-purple-300' : 'text-gray-400'}`}>
                        {item.icon}
                      </span>
                      <span className="ml-3 font-medium">{item.name}</span>
                    </Link>
                  );
                })}
              </div>
            </div>

            <div>
              <h3 className="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Main Site</h3>
              <div className="mt-2 space-y-2">
                {externalNavItems.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center px-4 py-3 rounded-lg transition-colors text-gray-300 hover:bg-gray-700"
                  >
                    <span className="text-gray-400">
                      {item.icon}
                    </span>
                    <span className="ml-3 font-medium">{item.name}</span>
                  </Link>
                ))}
              </div>
            </div>
          </nav>

          <div className="p-4 border-t border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-purple-900 rounded-full flex items-center justify-center text-purple-300 overflow-hidden">
                  {session?.user?.image ? (
                    <img
                      src={session.user.image}
                      alt={user?.name || session.user.name || 'User'}
                      className="h-10 w-10 rounded-full object-cover"
                      referrerPolicy="no-referrer"
                    />
                  ) : user?.avatar && user.avatar !== '/avatars/default.png' && user.avatar !== '/avatars/admin.png' ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="h-10 w-10 rounded-full object-cover"
                      referrerPolicy="no-referrer"
                    />
                  ) : (
                    <span className="font-medium">{user?.name?.charAt(0) || session?.user?.name?.charAt(0) || 'A'}</span>
                  )}
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-200">{user?.name || session?.user?.name || 'Admin User'}</p>
                  <p className="text-xs text-gray-400">{user?.email || session?.user?.email || '<EMAIL>'}</p>
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="p-2 rounded-full hover:bg-gray-700 text-gray-400 hover:text-gray-200"
                title="Logout"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 md:ml-64">
        <main className="p-4 md:p-6 min-h-screen">
          {children}
        </main>
      </div>

      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-20 bg-black bg-opacity-70 md:hidden"
          onClick={toggleSidebar}
        ></div>
      )}
    </div>
  );
}
