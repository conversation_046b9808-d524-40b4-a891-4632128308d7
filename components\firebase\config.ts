import { initializeApp, getApps, getApp } from "firebase/app"
import { getAuth } from "firebase/auth"
import { getFirestore } from "firebase/firestore"
import { getStorage } from "firebase/storage"
import { Analytics, getAnalytics } from "firebase/analytics"

// Default Firebase configuration values as fallbacks
const defaultConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

// Firebase configuration with fallbacks to default values
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || defaultConfig.apiKey,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || defaultConfig.authDomain,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || defaultConfig.projectId,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || defaultConfig.storageBucket,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || defaultConfig.messagingSenderId,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || defaultConfig.appId,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
}

// Log Firebase config for debugging
console.log('Firebase config (components/firebase/config.ts):', {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? 'Set' : 'Not set',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN ? 'Set' : 'Not set',
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID ? 'Set' : 'Not set'
});

// Initialize Firebase with check for existing apps
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp()

// Initialize Firebase services
const auth = getAuth(app)
const db = getFirestore(app)
const storage = getStorage(app)

// Initialize Analytics only on client side
let analytics: Analytics | null = null
if (typeof window !== "undefined") {
  analytics = getAnalytics(app)
}

export { app, auth, db, storage, analytics }