'use client';

import { useEffect, useRef } from 'react';
import { Card, CardContent } from 'components/ui/card';
import { Brain, Lightbulb, Search, PenTool, Share2, Bar<PERSON>hart, MessageSquare } from 'lucide-react';

interface AgentCanvasProps {
  agentThoughts: Record<string, string[]>;
}

const AgentCanvas = ({ agentThoughts }: AgentCanvasProps) => {
  const canvasRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the bottom when new thoughts are added
  useEffect(() => {
    if (canvasRef.current) {
      canvasRef.current.scrollTop = canvasRef.current.scrollHeight;
    }

    // Debug: Log agent thoughts
    console.log('AgentCanvas: Updated agent thoughts:', agentThoughts);
  }, [agentThoughts]);

  // Get agent icon based on agent ID
  const getAgentIcon = (agentId: string, isMessage: boolean = false) => {
    if (isMessage) {
      return <MessageSquare className="h-5 w-5" />;
    }

    switch (agentId) {
      case 'strategic-director':
        return <Brain className="h-5 w-5" />;
      case 'research-insights':
        return <Search className="h-5 w-5" />;
      case 'content-creator':
        return <PenTool className="h-5 w-5" />;
      case 'social-media-orchestrator':
        return <Share2 className="h-5 w-5" />;
      case 'analytics-reporting':
        return <BarChart className="h-5 w-5" />;
      default:
        return <Lightbulb className="h-5 w-5" />;
    }
  };

  // Get agent name based on agent ID
  const getAgentName = (agentId: string) => {
    return agentId
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get agent color based on agent ID
  const getAgentColor = (agentId: string, isMessage: boolean = false) => {
    if (isMessage) {
      return 'bg-indigo-900/20 border-l-4 border-indigo-500 text-indigo-300';
    }

    switch (agentId) {
      case 'strategic-director':
        return 'bg-blue-900/20 border-l-4 border-blue-500 text-blue-300';
      case 'research-insights':
        return 'bg-purple-900/20 border-l-4 border-purple-500 text-purple-300';
      case 'content-creator':
        return 'bg-green-900/20 border-l-4 border-green-500 text-green-300';
      case 'social-media-orchestrator':
        return 'bg-orange-900/20 border-l-4 border-orange-500 text-orange-300';
      case 'analytics-reporting':
        return 'bg-red-900/20 border-l-4 border-red-500 text-red-300';
      default:
        return 'bg-gray-800/20 border-l-4 border-gray-500 text-gray-300';
    }
  };

  // Flatten all thoughts into a chronological array
  const getAllThoughtsChronologically = () => {
    const allThoughts: Array<{
      agentId: string;
      thought: string;
      index: number;
      isMessage?: boolean;
      messageType?: 'sent' | 'received';
    }> = [];

    Object.entries(agentThoughts).forEach(([agentId, thoughts]) => {
      thoughts.forEach((thought, index) => {
        // Check if this is a message (starts with "Sending to" or "Received from")
        const isSentMessage = thought.startsWith('Sending to');
        const isReceivedMessage = thought.startsWith('Received from');
        const isMessage = isSentMessage || isReceivedMessage;

        allThoughts.push({
          agentId,
          thought,
          index,
          isMessage,
          messageType: isSentMessage ? 'sent' : (isReceivedMessage ? 'received' : undefined)
        });
      });
    });

    // Sort by agent ID and index as a simple approximation of chronological order
    // In a real implementation, each thought would have a timestamp
    return allThoughts.sort((a, b) => {
      if (a.index === b.index) {
        return a.agentId.localeCompare(b.agentId);
      }
      return a.index - b.index;
    });
  };

  const allThoughts = getAllThoughtsChronologically();

  return (
    <div
      ref={canvasRef}
      className="h-[600px] overflow-y-auto p-4 bg-gray-900/10 rounded-md border border-gray-700/30"
    >
      <div className="space-y-4">
        {allThoughts.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center text-gray-400">
            <Lightbulb className="h-12 w-12 mb-4 opacity-20 text-purple-400" />
            <p>No agent thoughts yet. Start a campaign to see agent reasoning.</p>
          </div>
        ) : (
          allThoughts.map((item, idx) => (
            <div
              key={`${item.agentId}-${item.index}`}
              className={`flex items-start space-x-3 p-4 rounded-lg shadow-md ${getAgentColor(item.agentId, item.isMessage)} transition-all duration-300 animate-fadeIn`}
              style={{ animationDelay: `${idx * 100}ms` }}
            >
              <div className={`p-2 rounded-full bg-gray-800/30 ${getAgentColor(item.agentId, item.isMessage).split(' ')[1]}`}>
                {getAgentIcon(item.agentId, item.isMessage)}
              </div>
              <div className="flex-1">
                <div className="font-medium">
                  {item.isMessage
                    ? (item.messageType === 'sent' ? `${getAgentName(item.agentId)} (Sending)` : `${getAgentName(item.agentId)} (Receiving)`)
                    : getAgentName(item.agentId)}
                </div>
                <p className="mt-1">{item.thought}</p>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default AgentCanvas;
