'use client';

import React, { useState, useEffect } from 'react';
import { usePlanner } from '../../../app/services/context/PlannerContext';
import { BacklogItem, BacklogComment } from '../types';
import { X, ThumbsUp, Check, AlertTriangle, ArrowRight, Send } from 'lucide-react';
import { format } from 'date-fns';

interface BacklogItemDetailsProps {
  projectId: string;
  backlogItem: BacklogItem;
  onClose: () => void;
  currentUser: string;
  isAdmin: boolean;
}

const BacklogItemDetails: React.FC<BacklogItemDetailsProps> = ({
  projectId,
  backlogItem,
  onClose,
  currentUser,
  isAdmin
}) => {
  const {
    backlogComments,
    fetchBacklogComments,
    addBacklogComment,
    voteForBacklogItem,
    approveBacklogItem,
    rejectBacklogItem,
    convertToTask,
    loading
  } = usePlanner();

  const [comment, setComment] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    const loadComments = async () => {
      await fetchBacklogComments(projectId, backlogItem.id);
    };

    loadComments();
  }, [projectId, backlogItem.id, fetchBacklogComments]);

  const comments = backlogComments[backlogItem.id] || [];

  const handleAddComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!comment.trim()) return;

    try {
      await addBacklogComment(projectId, backlogItem.id, comment, currentUser);
      setComment('');
    } catch (err: any) {
      setError(err.message || 'Failed to add comment');
    }
  };

  const handleVote = async () => {
    try {
      await voteForBacklogItem(projectId, backlogItem.id);
    } catch (err: any) {
      setError(err.message || 'Failed to vote for item');
    }
  };

  const handleApprove = async () => {
    try {
      await approveBacklogItem(projectId, backlogItem.id);
    } catch (err: any) {
      setError(err.message || 'Failed to approve item');
    }
  };

  const handleReject = async () => {
    try {
      await rejectBacklogItem(projectId, backlogItem.id);
    } catch (err: any) {
      setError(err.message || 'Failed to reject item');
    }
  };

  const handleConvertToTask = async () => {
    try {
      await convertToTask(projectId, backlogItem.id);
      onClose(); // Close the modal after conversion
    } catch (err: any) {
      setError(err.message || 'Failed to convert to task');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Proposed': return 'bg-blue-900/50 text-blue-300';
      case 'Approved': return 'bg-green-900/50 text-green-300';
      case 'Rejected': return 'bg-red-900/50 text-red-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-900/50 text-red-300';
      case 'High': return 'bg-orange-900/50 text-orange-300';
      case 'Medium': return 'bg-yellow-900/50 text-yellow-300';
      case 'Low': return 'bg-green-900/50 text-green-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">{backlogItem.title}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-200">
            <X size={20} />
          </button>
        </div>

        <div className="flex-grow overflow-y-auto p-4 space-y-4 custom-scrollbar">
          {error && (
            <div className="bg-red-900/20 text-red-300 p-3 rounded-md text-sm">
              {error}
            </div>
          )}

          <div className="flex flex-wrap gap-2 mb-4">
            <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold ${getStatusColor(backlogItem.status)}`}>
              {backlogItem.status}
            </span>
            <span className={`inline-flex rounded-full px-3 py-1 text-sm font-semibold ${getPriorityColor(backlogItem.priority)}`}>
              {backlogItem.priority}
            </span>
            {backlogItem.category && (
              <span className="inline-flex rounded-full px-3 py-1 text-sm font-semibold bg-gray-700 text-gray-300">
                {backlogItem.category}
              </span>
            )}
            <span className="inline-flex rounded-full px-3 py-1 text-sm font-semibold bg-purple-900/50 text-purple-300">
              <ThumbsUp size={16} className="mr-1" /> {backlogItem.votes} votes
            </span>
          </div>

          <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-700">
            <h3 className="text-sm font-medium text-gray-300 mb-2">Description</h3>
            <p className="text-white whitespace-pre-wrap">
              {backlogItem.description || 'No description provided.'}
            </p>
          </div>

          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-300">
              Created by {backlogItem.createdBy} on {format(new Date(backlogItem.createdAt), 'MMM d, yyyy')}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleVote}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-blue-300 bg-blue-900/50 hover:bg-blue-800"
              >
                <ThumbsUp size={16} className="mr-1" /> Vote
              </button>

              {isAdmin && backlogItem.status === 'Proposed' && (
                <>
                  <button
                    onClick={handleApprove}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-300 bg-green-900/50 hover:bg-green-800"
                  >
                    <Check size={16} className="mr-1" /> Approve
                  </button>
                  <button
                    onClick={handleReject}
                    className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-red-300 bg-red-900/50 hover:bg-red-800"
                  >
                    <AlertTriangle size={16} className="mr-1" /> Reject
                  </button>
                </>
              )}

              {isAdmin && backlogItem.status === 'Approved' && (
                <button
                  onClick={handleConvertToTask}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-purple-300 bg-purple-900/50 hover:bg-purple-800"
                >
                  <ArrowRight size={16} className="mr-1" /> Convert to Task
                </button>
              )}
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-medium text-white mb-4">Comments</h3>

            <div className="space-y-4 max-h-60 overflow-y-auto mb-4 custom-scrollbar">
              {comments.length === 0 ? (
                <p className="text-gray-400 text-sm">No comments yet.</p>
              ) : (
                comments.map((comment) => (
                  <div key={comment.id} className="bg-gray-800/50 p-3 rounded-lg border border-gray-700">
                    <div className="flex justify-between items-start">
                      <span className="font-medium text-white">{comment.createdBy}</span>
                      <span className="text-xs text-gray-400">
                        {format(new Date(comment.createdAt), 'MMM d, yyyy h:mm a')}
                      </span>
                    </div>
                    <p className="mt-1 text-gray-300">{comment.text}</p>
                  </div>
                ))
              )}
            </div>

            <form onSubmit={handleAddComment} className="mt-4">
              <div className="flex">
                <input
                  type="text"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="flex-grow border border-gray-700 bg-gray-700 text-white rounded-l-md shadow-sm py-2 px-3 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                />
                <button
                  type="submit"
                  disabled={!comment.trim() || loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"
                >
                  <Send size={16} />
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BacklogItemDetails;
