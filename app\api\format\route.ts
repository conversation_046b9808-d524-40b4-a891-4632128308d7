import { NextRequest, NextResponse } from "next/server";
import { webScraperTool } from "lib/tools/web-scraper";
import { contentFormatterTool } from "lib/tools/content-formatter";

export async function POST(request: NextRequest) {
  try {
    const { urls } = await request.json() as { urls: string[] };
    if (!urls || !Array.isArray(urls)) throw new Error("Invalid URLs");
    let formattedContent = "";
    for (const url of urls) {
      const { content: rawContent } = await webScraperTool.scrapeContent(url);
      const processed = await contentFormatterTool.formatContent(rawContent, url);
      formattedContent += `# Content from ${url}\n\n${processed}\n\n`;
    }
    return NextResponse.json({ formattedContent });
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
