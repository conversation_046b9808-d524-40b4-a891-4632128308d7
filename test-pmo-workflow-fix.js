/**
 * Test script to verify the PMO workflow integration fix
 * This test checks that only one PMO record is created per codebase documentation request
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, orderBy, where } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

async function testPMOWorkflowFix() {
  console.log('🔧 Testing PMO Workflow Integration Fix');
  console.log('=====================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Test user email
    const testUserEmail = '<EMAIL>';

    console.log(`\n📋 Step 1: Checking recent PMO records for user: ${testUserEmail}`);

    // Get PMO records from user-specific collection
    const pmoCollectionRef = collection(db, 'users', testUserEmail, 'PMO');
    const recordsQuery = query(
      pmoCollectionRef, 
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(recordsQuery);
    console.log(`   Found ${snapshot.docs.length} total PMO records`);

    // Filter for codebase documentation records
    const codebaseDocRecords = snapshot.docs.filter(doc => {
      const data = doc.data();
      return data.title && data.title.toLowerCase().includes('codebase documentation');
    });

    console.log(`\n🔍 Step 2: Found ${codebaseDocRecords.length} codebase documentation records`);

    if (codebaseDocRecords.length > 0) {
      console.log('\n📊 Recent Codebase Documentation Records:');
      
      // Group records by creation time to identify duplicates
      const recordsByTime = {};
      
      codebaseDocRecords.slice(0, 10).forEach((doc, index) => {
        const data = doc.data();
        const createdAt = data.createdAt ? new Date(data.createdAt.seconds * 1000) : null;
        const timeKey = createdAt ? createdAt.toISOString().substring(0, 16) : 'unknown'; // Group by minute
        
        if (!recordsByTime[timeKey]) {
          recordsByTime[timeKey] = [];
        }
        recordsByTime[timeKey].push({
          id: doc.id,
          title: data.title,
          status: data.status,
          agentIds: data.agentIds || [],
          teamRationale: data.teamSelectionRationale || 'None',
          createdAt: createdAt
        });

        console.log(`   ${index + 1}. ID: ${doc.id}`);
        console.log(`      Title: ${data.title}`);
        console.log(`      Status: ${data.status || 'No status'}`);
        console.log(`      Team Assignment: ${data.agentIds ? data.agentIds.join(', ') : 'None'}`);
        console.log(`      Team Rationale: ${data.teamSelectionRationale ? 'Present' : 'None'}`);
        console.log(`      Created: ${createdAt ? createdAt.toISOString() : 'No date'}`);
        console.log('');
      });

      // Check for duplicate records (created within the same minute)
      console.log('\n🔍 Step 3: Checking for duplicate records...');
      let duplicatesFound = false;
      
      Object.entries(recordsByTime).forEach(([timeKey, records]) => {
        if (records.length > 1) {
          duplicatesFound = true;
          console.log(`   ⚠️  Found ${records.length} records created around ${timeKey}:`);
          records.forEach(record => {
            console.log(`      - ${record.id} (Team: ${record.agentIds.join(', ') || 'None'})`);
          });
        }
      });

      if (!duplicatesFound) {
        console.log('   ✅ No duplicate records found - PMO workflow fix appears to be working!');
      }

      // Check for proper team assignments
      console.log('\n🎯 Step 4: Checking team assignments...');
      const recordsWithProperTeams = codebaseDocRecords.filter(doc => {
        const data = doc.data();
        return data.agentIds && data.agentIds.includes('Ag007') && data.teamSelectionRationale;
      });

      console.log(`   ✅ ${recordsWithProperTeams.length} records have proper CodebaseDocumentation team assignment (Ag007)`);
      console.log(`   ⚠️  ${codebaseDocRecords.length - recordsWithProperTeams.length} records missing proper team assignment`);

      // Summary
      console.log('\n📈 Summary:');
      console.log(`   - Total codebase documentation records: ${codebaseDocRecords.length}`);
      console.log(`   - Records with proper team assignment: ${recordsWithProperTeams.length}`);
      console.log(`   - Duplicate records detected: ${duplicatesFound ? 'Yes' : 'No'}`);
      
      if (recordsWithProperTeams.length > 0 && !duplicatesFound) {
        console.log('   🎉 PMO workflow integration appears to be working correctly!');
      } else if (duplicatesFound) {
        console.log('   ⚠️  Duplicate records suggest the fix may not be fully effective yet');
      } else {
        console.log('   ⚠️  No records with proper team assignments found');
      }

    } else {
      console.log('\n⚠️  No codebase documentation records found.');
      console.log('   This could mean:');
      console.log('   - No requests have been made since the fix');
      console.log('   - Records are being stored elsewhere');
      console.log('   - The fix needs verification');
    }

    console.log('\n✅ PMO Workflow Fix Test Complete');

  } catch (error) {
    console.error('\n❌ Error testing PMO workflow fix:', error);
  }
}

// Run the test
testPMOWorkflowFix().then(() => {
  console.log('\n🎉 Test execution finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test execution failed:', error);
  process.exit(1);
});
