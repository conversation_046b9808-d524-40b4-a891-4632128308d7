'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Sparkles, X, FileText, FolderOpen, Tag, AlertCircle } from 'lucide-react';
import { PMOFormInput, PMORecordPriority } from '../../lib/agents/pmo/PMOInterfaces';
import { createPMORecordFromForm } from '../../lib/firebase/pmoCollection';
import { useAuth } from '../../app/context/AuthContext';

// Types for files and categories
interface UserFile {
  id: string;
  name: string;
  category: string;
  namespace?: string;
}

interface UserCategory {
  id: string;
  name: string;
  documentCount?: number;
}

interface PMOAssessmentFormProps {
  userFiles: UserFile[];
  userCategories: UserCategory[];
  isFetchingFilesCategories?: boolean;
  onSubmit?: (pmoId: string) => void;
  onCancel: () => void;
  initialData?: Partial<PMOFormInput>;
}

const PMOAssessmentForm: React.FC<PMOAssessmentFormProps> = ({
  userFiles,
  userCategories,
  isFetchingFilesCategories = false,
  onSubmit,
  onCancel,
  initialData
}) => {
  const router = useRouter();
  const { user } = useAuth();
  
  // Form state
  const [title, setTitle] = useState(initialData?.title || '');
  const [description, setDescription] = useState(initialData?.description || '');
  const [priority, setPriority] = useState<PMORecordPriority>(initialData?.priority || 'Medium');
  const [category, setCategory] = useState(initialData?.category || 'Unknown');
  const [sourceFile, setSourceFile] = useState(initialData?.sourceFile || '');
  const [fileName, setFileName] = useState(initialData?.fileName || '');
  const [customContext, setCustomContext] = useState(initialData?.contextOptions?.customContext || '');
  const [selectedFileId, setSelectedFileId] = useState<string | undefined>(
    initialData?.contextOptions?.fileIds?.[0]
  );
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(
    initialData?.contextOptions?.categoryIds?.[0]
  );
  const [pmoAssessment, setPMOAssessment] = useState('');
  
  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isGeneratingAssessment, setIsGeneratingAssessment] = useState(false);
  
  // Quick start prompts
  const quickStartPrompts = [
    'Launch a new internal tool for tracking employee feedback.',
    'Organize a company-wide hackathon for Q3.',
    'Develop a marketing campaign for our new SaaS product feature.',
    'Research and implement a new CRM system for the sales team.',
    'Improve the onboarding process for new software engineers.'
  ];
  
  // Handle quick start prompt selection
  const handleQuickStartPrompt = (prompt: string) => {
    setTitle(prompt);
    setDescription(`Please help with the following task: ${prompt}`);
  };
  
  // Handle description optimization
  const handleOptimizeDescription = async () => {
    if (!description.trim()) {
      setError('Please enter a description to optimize');
      return;
    }
    
    setIsOptimizing(true);
    setError(null);
    
    try {
      const response = await fetch('/api/optimize-prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: description })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to optimize description');
      }
      
      const data = await response.json();
      setDescription(data.optimizedPrompt);
    } catch (err: any) {
      console.error('Error optimizing description:', err);
      setError(err.message || 'Failed to optimize description');
    } finally {
      setIsOptimizing(false);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.email) {
      setError('User not authenticated');
      return;
    }
    
    if (!title.trim()) {
      setError('Please enter a title');
      return;
    }
    
    if (!description.trim()) {
      setError('Please enter a description');
      return;
    }
    
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Create the form data
      const formData = {
        title,
        description,
        priority,
        category,
        sourceFile,
        fileName,
        customContext,
        selectedFileId,
        selectedCategory,
        pmoAssessment
      };
      
      // Create the PMO record
      const pmoId = await createPMORecordFromForm(user.email, formData);
      
      // Call the onSubmit callback if provided
      if (onSubmit) {
        onSubmit(pmoId);
      }
      
      // Redirect to the PMO record page
      router.push(`/services/pmo/${pmoId}`);
    } catch (err: any) {
      console.error('Error submitting PMO form:', err);
      setError(err.message || 'Failed to submit PMO form');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle PMO assessment generation
  const handleGenerateAssessment = async () => {
    if (!user?.email) {
      setError('User not authenticated');
      return;
    }
    
    if (!title.trim()) {
      setError('Please enter a title');
      return;
    }
    
    if (!description.trim()) {
      setError('Please enter a description');
      return;
    }
    
    setIsGeneratingAssessment(true);
    setError(null);
    
    try {
      // Create the form data
      const formData: PMOFormInput = {
        title,
        description,
        priority,
        category,
        sourceFile,
        fileName,
        contextOptions: {
          customContext,
          fileIds: selectedFileId ? [selectedFileId] : undefined,
          categoryIds: selectedCategory ? [selectedCategory] : undefined
        }
      };
      
      // Call the API to generate the PMO assessment
      const response = await fetch('/api/pmo-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ formData, userId: user.email })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate PMO assessment');
      }
      
      const data = await response.json();
      setPMOAssessment(data.pmoAssessment);
    } catch (err: any) {
      console.error('Error generating PMO assessment:', err);
      setError(err.message || 'Failed to generate PMO assessment');
    } finally {
      setIsGeneratingAssessment(false);
    }
  };
  
  return (
    <div className="bg-gray-900 rounded-lg p-6 shadow-lg border border-gray-700">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold text-white">Create PMO Request</h2>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-white transition-colors"
          aria-label="Close"
        >
          <X className="w-6 h-6" />
        </button>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-900/30 border border-red-700 rounded-md flex items-start">
          <AlertCircle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        {/* PMO Title */}
        <div className="mb-6">
          <label htmlFor="pmo-title" className="block text-sm font-medium text-gray-300 mb-2">
            PMO Title <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            id="pmo-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter a title for your PMO request"
            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            required
          />
        </div>
        
        {/* Quick Start Prompts */}
        <div className="mb-6">
          <p className="text-sm font-medium text-gray-300 mb-2">Quick Start Prompts:</p>
          <div className="flex flex-wrap gap-2">
            {quickStartPrompts.map((prompt, index) => (
              <button
                key={index}
                type="button"
                onClick={() => handleQuickStartPrompt(prompt)}
                className="px-3 py-2 bg-gray-800 hover:bg-gray-700 border border-gray-700 rounded-md text-sm text-gray-300 transition-colors"
              >
                {prompt}
              </button>
            ))}
          </div>
        </div>
        
        {/* Refined Request Description */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-300">
              Refined Request Description <span className="text-red-400">*</span>
            </label>
            <button
              type="button"
              onClick={handleOptimizeDescription}
              disabled={isOptimizing || !description.trim()}
              className="flex items-center text-xs px-2 py-1 bg-purple-700 hover:bg-purple-600 rounded text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isOptimizing ? (
                <>
                  <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                  Optimizing...
                </>
              ) : (
                <>
                  <Sparkles className="w-3 h-3 mr-1" />
                  Optimize
                </>
              )}
            </button>
          </div>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe the task or project you need help with..."
            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 min-h-[150px]"
            required
          />
        </div>
        
        {/* Priority, Category, Source File, File Name */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Priority */}
          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-300 mb-2">
              Priority
            </label>
            <select
              id="priority"
              value={priority}
              onChange={(e) => setPriority(e.target.value as PMORecordPriority)}
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
              <option value="Critical">Critical</option>
            </select>
          </div>
          
          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-2">
              Category
            </label>
            <input
              type="text"
              id="category"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              placeholder="e.g., Marketing, Development, Research"
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          
          {/* Source File */}
          <div>
            <label htmlFor="source-file" className="block text-sm font-medium text-gray-300 mb-2">
              Source File (Optional)
            </label>
            <input
              type="text"
              id="source-file"
              value={sourceFile}
              onChange={(e) => setSourceFile(e.target.value)}
              placeholder="Namespace of file"
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          
          {/* File Name */}
          <div>
            <label htmlFor="file-name" className="block text-sm font-medium text-gray-300 mb-2">
              File Name (Optional)
            </label>
            <input
              type="text"
              id="file-name"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              placeholder="Name of the file"
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
        </div>
        
        {/* Context Options */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-200 mb-3">Context Options</h3>
          <p className="text-sm text-gray-400 mb-4">
            All context options are optional. You can provide custom context in the text box and combine it with either a document or category selection.
          </p>
          
          {/* Custom Context */}
          <div className="mb-4">
            <label htmlFor="custom-context" className="block text-sm font-medium text-gray-300 mb-2">
              Custom Context (Optional)
            </label>
            <textarea
              id="custom-context"
              value={customContext}
              onChange={(e) => setCustomContext(e.target.value)}
              placeholder="Enter custom context for the agent (optional)..."
              className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 min-h-[100px]"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Files */}
            <div>
              <label htmlFor="file-select" className="block text-sm font-medium text-gray-300 mb-2">
                <FileText className="w-4 h-4 inline-block mr-1" /> Files
              </label>
              <select
                id="file-select"
                value={selectedFileId || ''}
                onChange={(e) => setSelectedFileId(e.target.value || undefined)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                disabled={isFetchingFilesCategories}
              >
                <option value="">Select a file (optional)</option>
                {userFiles.map((file) => (
                  <option key={file.id} value={file.id}>
                    {file.name}
                  </option>
                ))}
              </select>
            </div>
            
            {/* Categories */}
            <div>
              <label htmlFor="category-select" className="block text-sm font-medium text-gray-300 mb-2">
                <Tag className="w-4 h-4 inline-block mr-1" /> Categories
              </label>
              <select
                id="category-select"
                value={selectedCategory || ''}
                onChange={(e) => setSelectedCategory(e.target.value || undefined)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                disabled={isFetchingFilesCategories}
              >
                <option value="">Select a category (optional)</option>
                {userCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name} {category.documentCount ? `(${category.documentCount})` : ''}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
        
        {/* PMO Assessment */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <label htmlFor="pmo-assessment" className="block text-sm font-medium text-gray-300">
              PMO Assessment
            </label>
            <button
              type="button"
              onClick={handleGenerateAssessment}
              disabled={isGeneratingAssessment || !title.trim() || !description.trim()}
              className="flex items-center text-xs px-2 py-1 bg-purple-700 hover:bg-purple-600 rounded text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isGeneratingAssessment ? (
                <>
                  <Sparkles className="w-3 h-3 mr-1 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="w-3 h-3 mr-1" />
                  Generate Assessment
                </>
              )}
            </button>
          </div>
          <textarea
            id="pmo-assessment"
            value={pmoAssessment}
            onChange={(e) => setPMOAssessment(e.target.value)}
            placeholder="The PMO assessment will be generated here..."
            className="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 min-h-[200px]"
            readOnly={!pmoAssessment}
          />
        </div>
        
        {/* Form Actions */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 bg-gray-700 hover:bg-gray-600 rounded-md text-white transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || !title.trim() || !description.trim()}
            className="px-6 py-3 bg-purple-600 hover:bg-purple-500 rounded-md text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            {isSubmitting ? (
              <>
                <Sparkles className="w-5 h-5 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              'Process Request'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PMOAssessmentForm;
