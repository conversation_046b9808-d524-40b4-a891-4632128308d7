/**
 * Extended type definitions for chart configurations
 * These types extend the base types from chart-tool.ts to include
 * additional properties needed for compatibility with different components
 */

import { ChartConfig as BaseChartConfig, ChartType, CHART_TYPES } from './chart-tool';

// Extended column type for tables
export interface TableColumn {
  header: string;
  accessorKey: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  // Additional properties for compatibility
  title?: string;
  dataKey?: string;
}

// Extended table chart config
export interface TableChartConfig {
  chartType: 'table';
  title: string;
  subtitle?: string;
  columns: TableColumn[];
  data: Record<string, any>[];
  pagination?: boolean;
  rowsPerPage?: number;
  explanation?: string;
  colors?: string[];
  legend?: boolean;
  tooltip?: boolean;
  grid?: boolean;
}

// Extended chart config that includes all possible properties
export type ExtendedChartConfig = BaseChartConfig & {
  columns?: TableColumn[];
  data: Record<string, any>[];
};
