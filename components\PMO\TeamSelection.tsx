'use client';

import React, { useState } from 'react';
import { 
  Users, 
  Check, 
  Info, 
  X, 
  ChevronDown, 
  ChevronUp,
  Sparkles
} from 'lucide-react';
import { AgenticTeamId } from '../../lib/agents/pmo/PMOInterfaces';
import { 
  getTeamName, 
  getTeamDescription, 
  getTeamCapabilities, 
  TeamAgentCapabilities 
} from '../../lib/agents/pmo/TeamAgentInterfaces';

interface TeamSelectionProps {
  selectedTeams: AgenticTeamId[];
  onChange: (teams: AgenticTeamId[]) => void;
  disabled?: boolean;
  showAuto?: boolean;
}

const TeamSelection: React.FC<TeamSelectionProps> = ({ 
  selectedTeams, 
  onChange, 
  disabled = false,
  showAuto = true
}) => {
  const [showInfo, setShowInfo] = useState<AgenticTeamId | null>(null);
  const [isAutoSelecting, setIsAutoSelecting] = useState(false);
  
  // Toggle team selection
  const toggleTeam = (teamId: AgenticTeamId) => {
    if (disabled) return;
    
    const isSelected = selectedTeams.includes(teamId);
    
    if (isSelected) {
      onChange(selectedTeams.filter(id => id !== teamId));
    } else {
      onChange([...selectedTeams, teamId]);
    }
  };
  
  // Toggle team info
  const toggleInfo = (teamId: AgenticTeamId, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowInfo(showInfo === teamId ? null : teamId);
  };
  
  // Auto-select teams based on task description
  const autoSelectTeams = async () => {
    if (disabled) return;
    
    setIsAutoSelecting(true);
    
    // In a real implementation, this would call the PMO Agent to analyze the task
    // For now, we'll just simulate a delay and select random teams
    setTimeout(() => {
      // Randomly select 1-3 teams
      const numTeams = Math.floor(Math.random() * 3) + 1;
      const allTeams = Object.values(AgenticTeamId);
      const shuffled = [...allTeams].sort(() => 0.5 - Math.random());
      const randomTeams = shuffled.slice(0, numTeams);
      
      onChange(randomTeams);
      setIsAutoSelecting(false);
    }, 1500);
  };
  
  // Render capability indicator
  const renderCapability = (capability: boolean) => {
    return capability ? (
      <span className="text-green-400">
        <Check className="w-4 h-4" />
      </span>
    ) : (
      <span className="text-red-400">
        <X className="w-4 h-4" />
      </span>
    );
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-300">Select Teams</h3>
        
        {showAuto && (
          <button
            type="button"
            onClick={autoSelectTeams}
            disabled={disabled || isAutoSelecting}
            className={`text-xs flex items-center px-2 py-1 rounded-md ${
              disabled || isAutoSelecting
                ? 'bg-gray-700 text-gray-500 cursor-not-allowed'
                : 'bg-purple-600/30 text-purple-300 hover:bg-purple-600/40'
            } transition-colors`}
          >
            {isAutoSelecting ? (
              <>
                <Sparkles className="w-3 h-3 mr-1 animate-pulse" />
                Analyzing...
              </>
            ) : (
              <>
                <Sparkles className="w-3 h-3 mr-1" />
                Auto-Select
              </>
            )}
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-1 gap-2">
        {Object.values(AgenticTeamId).map(teamId => {
          const isSelected = selectedTeams.includes(teamId);
          const teamName = getTeamName(teamId);
          const teamDescription = getTeamDescription(teamId);
          const teamCapabilities = getTeamCapabilities(teamId);
          
          return (
            <div key={teamId} className="relative">
              <div
                onClick={() => toggleTeam(teamId)}
                className={`flex items-center justify-between p-3 rounded-md cursor-pointer transition-colors ${
                  isSelected
                    ? 'bg-purple-600/20 border border-purple-500/50'
                    : 'bg-gray-700 border border-gray-600 hover:border-gray-500'
                } ${disabled ? 'opacity-70 cursor-not-allowed' : ''}`}
              >
                <div className="flex items-center">
                  <div className={`w-5 h-5 rounded-md flex items-center justify-center mr-3 ${
                    isSelected ? 'bg-purple-500' : 'bg-gray-600'
                  }`}>
                    {isSelected && <Check className="w-3 h-3 text-white" />}
                  </div>
                  <span className="text-sm font-medium text-white">{teamName}</span>
                </div>
                
                <div className="flex items-center">
                  <button
                    type="button"
                    onClick={(e) => toggleInfo(teamId, e)}
                    className="p-1 text-gray-400 hover:text-white transition-colors"
                  >
                    {showInfo === teamId ? (
                      <ChevronUp className="w-4 h-4" />
                    ) : (
                      <ChevronDown className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* Team Info Panel */}
              {showInfo === teamId && (
                <div className="mt-1 p-3 bg-gray-800 rounded-md border border-gray-700 text-sm">
                  <p className="text-gray-300 mb-3">{teamDescription}</p>
                  
                  <h4 className="text-xs font-medium text-gray-400 mb-2">Capabilities:</h4>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canGenerateImages)}
                      <span className="ml-2 text-gray-300">Generate Images</span>
                    </div>
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canGenerateDocuments)}
                      <span className="ml-2 text-gray-300">Generate Documents</span>
                    </div>
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canGenerateCharts)}
                      <span className="ml-2 text-gray-300">Generate Charts</span>
                    </div>
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canPerformResearch)}
                      <span className="ml-2 text-gray-300">Perform Research</span>
                    </div>
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canWriteCode)}
                      <span className="ml-2 text-gray-300">Write Code</span>
                    </div>
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canAnalyzeData)}
                      <span className="ml-2 text-gray-300">Analyze Data</span>
                    </div>
                    <div className="flex items-center">
                      {renderCapability(teamCapabilities.canCreatePresentations)}
                      <span className="ml-2 text-gray-300">Create Presentations</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
      
      {selectedTeams.length === 0 && !disabled && (
        <p className="text-xs text-yellow-400 flex items-center mt-2">
          <Info className="w-3 h-3 mr-1" />
          Please select at least one team
        </p>
      )}
    </div>
  );
};

export default TeamSelection;
