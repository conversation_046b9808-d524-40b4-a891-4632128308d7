/**
 * genericDocumentContentExtractorTool.ts
 *
 * This tool provides comprehensive document content extraction and analysis capabilities.
 * It processes document content to extract meaning, structure, and key information
 * before passing it to other agents for relevance assessment.
 *
 * The tool uses a sophisticated prompt to analyze documents thoroughly, extracting:
 * - Document type and purpose
 * - Content structure and organization
 * - Key information points and arguments
 * - Contextual meaning and inferences
 * - Main takeaways and conclusions
 */

// Import LlmProvider from llm-tool.ts to ensure consistency
import { LlmProvider } from './llm-tool';
import { fetchDocumentChunksByChunkIds } from "lib/fetchDocumentChunksByChunkIds";
import { FirestoreStore } from "lib/FirestoreStore";
import { adminDb } from "../../components/firebase-admin";

/**
 * Fetches namespaces for a given category
 *
 * @param userId - User ID for accessing user-specific document collections
 * @param category - Category to fetch namespaces for
 * @returns Array of namespaces for the category
 */
async function fetchNamespacesForCategory(userId: string, category: string): Promise<string[]> {
  try {
    const snapshot = await adminDb.collection('users')
      .doc(userId)
      .collection('files')
      .where('category', '==', category)
      .get();

    return snapshot.docs
      .map(doc => doc.data().namespace)
      .filter((namespace): namespace is string => !!namespace);
  } catch (error) {
    console.error("Error fetching namespaces:", error);
    return [];
  }
}

export interface DocumentContentExtractorOptions {
  documentContent: string;
  documentTitle?: string;
  documentId?: string;
  modelProvider?: LlmProvider;
  modelName?: string;
  modelOptions?: Record<string, any>; // Optional model configuration options
  userQuery?: string; // Optional user query for relevance assessment
  performVectorSimilarity?: boolean; // Whether to perform vector similarity assessment
  userId?: string; // User ID for accessing user-specific document collections
  originalContent?: string; // Original content before vector similarity processing
  category?: string; // Category for category-based search
}

export interface DocumentContentExtractorResult {
  success: boolean;
  extractedContent?: string;
  structuredAnalysis?: {
    documentType?: string;
    primarySubject?: string;
    corePurpose?: string;
    keyInformationPoints?: string[];
    keyArguments?: string[];
    intendedAudience?: string;
    mainTakeaways?: string[];
    callsToAction?: string[];
  };
  relevanceScore?: number; // Score indicating relevance to user query (0-1)
  relevantSections?: string[]; // Sections of the document most relevant to the query
  vectorSimilarityResults?: {
    score: number;
    matchedText: string;
    section: string;
  }[];
  error?: string;
}

/**
 * Tool for extracting comprehensive content and meaning from documents
 */
export const genericDocumentContentExtractorTool = {
  /**
   * Process a document to extract its content and meaning
   *
   * @param options - Options including document content and processing parameters
   * @returns Extraction result with comprehensive analysis
   */
  async process(options: DocumentContentExtractorOptions): Promise<DocumentContentExtractorResult> {
    try {
      console.log(`GenericDocumentContentExtractor: Processing document${options.documentTitle ? ` "${options.documentTitle}"` : ''} with modelName: ${options.modelName}, modelProvider: ${options.modelProvider}`);
      console.log(`GenericDocumentContentExtractor: Document contents : ${options.documentContent}`);

      let prompt: string;
      let response: string;

      // If vector similarity is requested, perform it first
      if (options.performVectorSimilarity && options.userQuery && options.userQuery.trim().length > 0) {
        // Create a temporary result object to store vector similarity results
        const tempResult: DocumentContentExtractorResult = {
          success: true,
          extractedContent: '',
        };

        // Check if we have a category but no documentId
        if (options.category && !options.documentId) {
          console.log(`GenericDocumentContentExtractor: Using category-based vector similarity for category "${options.category}"`);

          // Get the namespaces for the category
          const { adminDb } = await import('../../components/firebase-admin');
          const filesRef = adminDb.collection(`users/${options.userId}/files`);
          const filesSnapshot = await filesRef.where('category', '==', options.category).get();

          if (!filesSnapshot.empty) {
            const namespaces = filesSnapshot.docs.map(doc => doc.data().namespace).filter(Boolean);
            console.log(`GenericDocumentContentExtractor: Found ${namespaces.length} namespaces for category "${options.category}":`, namespaces);

            // Use the first namespace for vector similarity
            if (namespaces.length > 0) {
              console.log(`GenericDocumentContentExtractor: Using namespace "${namespaces[0]}" for vector similarity`);

              // Create a modified options object with the namespace
              const namespaceOptions = {
                ...options,
                documentId: namespaces[0]
              };

              // Perform vector similarity with the namespace
              await this.performVectorSimilarityAnalysis(namespaceOptions, tempResult, options.documentContent);
            } else {
              console.log(`GenericDocumentContentExtractor: No namespaces found for category "${options.category}", using standard content extraction`);
              await this.performVectorSimilarityAnalysis(options, tempResult, options.documentContent);
            }
          } else {
            console.log(`GenericDocumentContentExtractor: No files found for category "${options.category}", using standard content extraction`);
            await this.performVectorSimilarityAnalysis(options, tempResult, options.documentContent);
          }
        } else {
          // Standard vector similarity with documentId
          await this.performVectorSimilarityAnalysis(options, tempResult, options.documentContent);
        }

        // If we found relevant sections through vector similarity, use them instead of the original content
        if (tempResult.relevantSections && tempResult.relevantSections.length > 0) {
          console.log(`GenericDocumentContentExtractor: Using ${tempResult.relevantSections.length} relevant sections from vector similarity for LLM processing`);

          // Create a modified options object with the relevant sections as the document content
          const modifiedOptions = {
            ...options,
            documentContent: tempResult.relevantSections.join('\n\n---\n\n')
          };

          // Create the prompt with the relevant sections
          prompt = this.createExtractionPrompt(modifiedOptions);
        } else {
          // If no relevant sections found, use the original content
          prompt = this.createExtractionPrompt(options);
        }
      } else {
        // If no vector similarity requested, use the original content
        prompt = this.createExtractionPrompt(options);
      }

      let provider = options.modelProvider;
      let modelName = options.modelName;

      // 1. Parse provider from modelName if in "provider/model" format
      if (modelName && modelName.includes('/') && !provider) {
        const parts = modelName.split('/');
        provider = parts[0].toLowerCase() as LlmProvider; // Ensure provider is lowercase
        modelName = parts[1];
        console.log(`GenericDocumentContentExtractor: Parsed provider/model format: ${provider}/${modelName}`);
      }

      // Log the prompt being sent to the LLM
      console.log(`GenericDocumentContentExtractor: PROMPT BEING SENT TO LLM:`);
      console.log(`---PROMPT START---\n${prompt}\n---PROMPT END---`);

      // 2. Explicit Provider or Parsed Provider Handling
      if (provider) {
        console.log(`GenericDocumentContentExtractor: Using specified/parsed provider: ${provider}`);
        switch (provider) {
          case 'anthropic':
            const { processWithAnthropic } = await import('./anthropic-ai');
            response = await processWithAnthropic({
              prompt,
              model: modelName || "claude-sonnet-4-0", // Updated default
              modelOptions: { temperature: 0.2, maxTokens: 4096 } // Adjusted maxTokens
            });
            break;
          case 'google':
            const { processWithGoogleAI } = await import('./google-ai');
            response = await processWithGoogleAI({
              prompt,
              model: modelName || "gemini-1.5-pro-latest" // Updated default
            });
            break;
          case 'groq':
            const { processWithGroq } = await import('./groq-ai');
            if (modelName && (modelName.startsWith('gpt-') || modelName.startsWith('o3-') || modelName.startsWith('claude-'))) {
              console.warn(`GenericDocumentContentExtractor: Attempted to use non-Groq model "${modelName}" with Groq. Falling back to default Groq model.`);
              modelName = "deepseek-r1-distill-llama-70b"; // Updated default Groq model
            }
            response = await processWithGroq({
              prompt,
              model: modelName || "deepseek-r1-distill-llama-70b",
              modelOptions: { temperature: 0.2, maxTokens: 8000 } // Adjusted maxTokens
            });
            break;
          case 'openai':
            const { processWithOpenAI } = await import('./openai');
            const isO3Model = (modelName || "").startsWith('o3');
            response = await processWithOpenAI({
              prompt,
              model: modelName || "gpt-4o",
              modelOptions: {
                ...(isO3Model ? {} : { temperature: 0.2 }),
                maxTokens: 4096 // Adjusted maxTokens
              }
            });
            break;
          default:
            console.warn(`GenericDocumentContentExtractor: Unknown provider "${provider}" specified. Falling back to Anthropic.`);
            const { processWithAnthropic: defaultProc } = await import('./anthropic-ai');
            response = await defaultProc({
              prompt,
              model: "claude-sonnet-4-0", // Fallback model
              modelOptions: { temperature: 0.2, maxTokens: 4096 }
            });
        }
      } else {
        // 3. No Provider Specified - Infer from modelName or use default chain
        console.log(`GenericDocumentContentExtractor: No provider specified. Attempting to infer from modelName: "${modelName}"`);
        if (modelName) {
          if (modelName.startsWith('gpt-') || modelName.startsWith('o3-')) {
            console.log(`GenericDocumentContentExtractor: Inferred OpenAI model. Using OpenAI.`);
            const { processWithOpenAI } = await import('./openai');
            const isO3Model = modelName.startsWith('o3');
            response = await processWithOpenAI({
              prompt, model: modelName,
              modelOptions: { ... (isO3Model ? {} : { temperature: 0.2 }), maxTokens: 4096 }
            });
          } else if (modelName.startsWith('claude-')) {
            console.log(`GenericDocumentContentExtractor: Inferred Anthropic model. Using Anthropic.`);
            const { processWithAnthropic } = await import('./anthropic-ai');
            response = await processWithAnthropic({
              prompt, model: modelName,
              modelOptions: { temperature: 0.2, maxTokens: 4096 }
            });
          } else if (modelName.includes('llama') || modelName.includes('mixtral') || modelName.includes('gemma') || modelName.includes('deepseek')) {
            console.log(`GenericDocumentContentExtractor: Inferred Groq-compatible model. Using Groq.`);
            const { processWithGroq } = await import('./groq-ai');
            response = await processWithGroq({
              prompt, model: modelName,
              modelOptions: { temperature: 0.2, maxTokens: 8000 }
            });
          } else if (modelName.startsWith('gemini-')) {
            console.log(`GenericDocumentContentExtractor: Inferred Google model. Using Google AI.`);
            const { processWithGoogleAI } = await import('./google-ai');
            response = await processWithGoogleAI({ prompt, model: modelName });
          } else {
            console.log(`GenericDocumentContentExtractor: Model name "${modelName}" not specifically recognized. Defaulting to Google AI with the provided model name.`);
            // This path might still lead to errors if google-ai can't handle the modelName
            const { processWithGoogleAI } = await import('./google-ai');
            response = await processWithGoogleAI({ prompt, model: modelName });
          }
        } else {
          // No modelName and no provider - use a sensible default (e.g., Google or Anthropic Haiku for cost/speed)
          console.log(`GenericDocumentContentExtractor: No provider or modelName. Defaulting to Google AI (gemini-1.5-flash-latest).`);
          const { processWithGoogleAI } = await import('./google-ai');
          response = await processWithGoogleAI({ prompt, model: "gemini-2.5-pro" });
        }
      }

      // Extract structured analysis from the response
      const structuredAnalysis = this.extractStructuredAnalysis(response);

      // Create the result object
      const result: DocumentContentExtractorResult = {
        success: true,
        extractedContent: response, // Keep the full LLM response for flexibility
        structuredAnalysis
      };

      // If vector similarity is requested and not already performed, do it now
      if (options.performVectorSimilarity && options.userQuery && options.userQuery.trim().length > 0) {
        await this.performVectorSimilarityAnalysis(options, result, response);

        // If we found relevant sections through vector similarity, use them instead of the original content
        if (result.vectorSimilarityResults && result.vectorSimilarityResults.length > 0) {
          console.log(`GenericDocumentContentExtractor: Using ${result.vectorSimilarityResults.length} vector similarity results for LLM processing`);

          // Log detailed information about the vector similarity results
          console.log(`GenericDocumentContentExtractor: Vector similarity results:`, JSON.stringify({
            query: options.userQuery,
            documentId: options.documentId,
            averageRelevanceScore: result.relevanceScore,
            resultCount: result.vectorSimilarityResults.length,
            topResults: result.vectorSimilarityResults.slice(0, 3).map(vsr => ({
              section: vsr.section,
              score: vsr.score,
              matchedTextPreview: vsr.matchedText.substring(0, 100) + (vsr.matchedText.length > 100 ? '...' : '')
            }))
          }, null, 2));

          // Create a new prompt with the relevant sections from vector similarity
          const relevantSections = result.vectorSimilarityResults.map(vsr => {
            // Log each section's content for debugging
            console.log(`GenericDocumentContentExtractor: SECTION CONTENT - ${vsr.section || 'Unnamed Section'} (Relevance: ${(vsr.score * 100).toFixed(1)}%)`);
            console.log(`---SECTION CONTENT START---\n${vsr.matchedText}\n---SECTION CONTENT END---`);

            return `[Section: ${vsr.section || 'Unnamed Section'} (Relevance: ${(vsr.score * 100).toFixed(1)}%)]\n${vsr.matchedText}`;
          }).join('\n\n---\n\n');

          // Create a new prompt with the relevant sections
          const vectorPrompt = `You are a specialized Document Content Extractor AI focused on extracting ONLY information that is directly relevant to a specific query. Your task is to analyze the document and extract ONLY the information needed to address the query.

USER QUERY: "${options.userQuery}"

INSTRUCTIONS:
1. Focus exclusively on finding and extracting information from the document that directly helps answer the query.
2. Ignore any content that is not relevant to the query.
3. Do NOT perform a comprehensive document analysis - focus only on query-relevant information.
4. Structure your response as follows:
   a. Directly relevant information (facts, data, arguments, etc. that address the query). Present this clearly.
   b. If the document contains multiple distinct pieces of information relevant to the query, list them.
   c. Briefly summarize how this extracted information directly answers or contributes to answering the query.

Document Content (Most Relevant Sections Based on Vector Similarity):
${options.documentTitle ? `Title: ${options.documentTitle}\n` : ''}
${options.documentId ? `Document ID: ${options.documentId}\n` : ''}
---
${relevantSections}
---

IMPORTANT: Your response should be concise and focused ONLY on information relevant to the query. Do not include comprehensive document analysis or information that doesn't directly help answer the query. If no relevant information is found, state that clearly.`;

          // Log the vector prompt being sent to the LLM
          console.log(`GenericDocumentContentExtractor: VECTOR PROMPT BEING SENT TO LLM:`);
          console.log(`---VECTOR PROMPT START---\n${vectorPrompt}\n---VECTOR PROMPT END---`);

          // Process the vector prompt with the same provider/model
          let vectorResponse: string;
          try {
            // Use the same provider and model as the original request
            if (provider) {
              console.log(`GenericDocumentContentExtractor: Using specified provider ${provider} for vector similarity results`);
              switch (provider) {
                case 'anthropic':
                  const { processWithAnthropic } = await import('./anthropic-ai');
                  vectorResponse = await processWithAnthropic({
                    prompt: vectorPrompt,
                    model: modelName || "claude-3-sonnet-20240229",
                    modelOptions: { temperature: 0.2, maxTokens: 4096 }
                  });
                  break;
                case 'google':
                  const { processWithGoogleAI } = await import('./google-ai');
                  vectorResponse = await processWithGoogleAI({
                    prompt: vectorPrompt,
                    model: modelName || "gemini-1.5-pro-latest"
                  });
                  break;
                case 'groq':
                  const { processWithGroq } = await import('./groq-ai');
                  vectorResponse = await processWithGroq({
                    prompt: vectorPrompt,
                    model: modelName || "deepseek-r1-distill-llama-70b",
                    modelOptions: { temperature: 0.2, maxTokens: 8000 }
                  });
                  break;
                case 'openai':
                  const { processWithOpenAI } = await import('./openai');
                  const isO3Model = (modelName || "").startsWith('o3');
                  vectorResponse = await processWithOpenAI({
                    prompt: vectorPrompt,
                    model: modelName || "gpt-4o",
                    modelOptions: {
                      ...(isO3Model ? {} : { temperature: 0.2 }),
                      maxTokens: 4096
                    }
                  });
                  break;
                default:
                  console.warn(`GenericDocumentContentExtractor: Unknown provider "${provider}" for vector processing. Using original response.`);
                  vectorResponse = response;
              }
            } else {
              // No provider specified - use the same inference logic as the original request
              if (modelName) {
                if (modelName.startsWith('gpt-') || modelName.startsWith('o3-')) {
                  const { processWithOpenAI } = await import('./openai');
                  const isO3Model = modelName.startsWith('o3');
                  vectorResponse = await processWithOpenAI({
                    prompt: vectorPrompt, model: modelName,
                    modelOptions: { ... (isO3Model ? {} : { temperature: 0.2 }), maxTokens: 4096 }
                  });
                } else if (modelName.startsWith('claude-')) {
                  const { processWithAnthropic } = await import('./anthropic-ai');
                  vectorResponse = await processWithAnthropic({
                    prompt: vectorPrompt, model: modelName,
                    modelOptions: { temperature: 0.2, maxTokens: 4096 }
                  });
                } else if (modelName.includes('llama') || modelName.includes('mixtral') || modelName.includes('gemma') || modelName.includes('deepseek')) {
                  const { processWithGroq } = await import('./groq-ai');
                  vectorResponse = await processWithGroq({
                    prompt: vectorPrompt, model: modelName,
                    modelOptions: { temperature: 0.2, maxTokens: 8000 }
                  });
                } else if (modelName.startsWith('gemini-')) {
                  const { processWithGoogleAI } = await import('./google-ai');
                  vectorResponse = await processWithGoogleAI({ prompt: vectorPrompt, model: modelName });
                } else {
                  console.log(`GenericDocumentContentExtractor: Model name "${modelName}" not recognized for vector processing. Using original response.`);
                  vectorResponse = response;
                }
              } else {
                console.log(`GenericDocumentContentExtractor: No provider or modelName for vector processing. Using original response.`);
                vectorResponse = response;
              }
            }

            // If we successfully processed the vector prompt, use the vector response
            if (vectorResponse && vectorResponse !== response) {
              console.log(`GenericDocumentContentExtractor: Successfully processed vector similarity results`);

              // Log the vector response preview
              console.log(`GenericDocumentContentExtractor: Vector response preview:`, JSON.stringify({
                query: options.userQuery,
                documentId: options.documentId,
                responsePreview: vectorResponse.substring(0, 200) + (vectorResponse.length > 200 ? '...' : ''),
                responseLength: vectorResponse.length,
                originalResponseLength: response.length,
                provider: provider || 'auto-detected',
                model: modelName || 'default'
              }, null, 2));

              // Log the full vector response
              console.log(`GenericDocumentContentExtractor: FULL VECTOR RESPONSE:`);
              console.log(`---VECTOR RESPONSE START---\n${vectorResponse}\n---VECTOR RESPONSE END---`);

              result.extractedContent = vectorResponse;
              result.structuredAnalysis = this.extractStructuredAnalysis(vectorResponse);
            }
          } catch (vectorError) {
            console.error("GenericDocumentContentExtractor: Error processing vector similarity results:", vectorError);
            // Keep the original response if there was an error
          }
        }
      }

      return result;

    } catch (processingError) {
      console.error("GenericDocumentContentExtractor: Error during primary processing, attempting fallback chain.", processingError);
      let fallbackResponse: string | undefined;
      const fallbackErrorMessages: string[] = [(processingError instanceof Error ? processingError.message : String(processingError))];

      try {
        console.log("GenericDocumentContentExtractor: Fallback 1: Trying Anthropic Claude sonnet...");
        const { processWithAnthropic } = await import('./anthropic-ai');
        fallbackResponse = await processWithAnthropic({
          prompt: this.createExtractionPrompt(options), // Recreate prompt
          model: "claude-sonnet-4-20250514",
          modelOptions: { temperature: 0.2, maxTokens: 4096 }
        });
      } catch (anthropicError) {
        fallbackErrorMessages.push(anthropicError instanceof Error ? anthropicError.message : String(anthropicError));
        console.error("GenericDocumentContentExtractor: Anthropic fallback failed.", anthropicError);
        try {
          console.log("GenericDocumentContentExtractor: Fallback 2: Trying Groq Llama3...");
          const { processWithGroq } = await import('./groq-ai');
          fallbackResponse = await processWithGroq({
            prompt: this.createExtractionPrompt(options), // Recreate prompt
            model: "llama-3.3-70b-versatile", // Use a smaller Llama model for fallback
            modelOptions: { temperature: 0.2, maxTokens: 8000 }
          });
        } catch (groqError) {
          fallbackErrorMessages.push(groqError instanceof Error ? groqError.message : String(groqError));
          console.error("GenericDocumentContentExtractor: Groq fallback failed.", groqError);
        }
      }

      if (fallbackResponse) {
        console.log("GenericDocumentContentExtractor: Successfully processed with a fallback provider.");
        const structuredAnalysis = this.extractStructuredAnalysis(fallbackResponse);
        const result: DocumentContentExtractorResult = {
          success: true,
          extractedContent: fallbackResponse,
          structuredAnalysis,
          error: `Primary processing failed. Used fallback. Errors: ${fallbackErrorMessages.join('; ')}` // Include error info
        };
        if (options.performVectorSimilarity && options.userQuery && options.userQuery.trim().length > 0) {
          await this.performVectorSimilarityAnalysis(options, result, fallbackResponse);
        }
        return result;
      } else {
        console.error("GenericDocumentContentExtractor: All processing attempts failed.", fallbackErrorMessages);
        return {
          success: false,
          error: `All processing attempts failed. Errors: ${fallbackErrorMessages.join('; ')}`
        };
      }
    }
  },

  async performVectorSimilarityAnalysis(
    options: DocumentContentExtractorOptions,
    result: DocumentContentExtractorResult,
    documentText: string // The text to analyze (could be original or fallback)
  ): Promise<void> {
    try {
      console.log(`GenericDocumentContentExtractor: Performing vector similarity assessment for query "${options.userQuery}"`);
      const { ContentSelector } = await import('../../app/src/processContent/selectContent');
      const { getEmbedding } = await import('./embedding-tool');
      const queryEmbedding = await getEmbedding(options.userQuery!); // userQuery is checked before calling

      // Check if we're dealing with custom input text (no userId, documentId, or category)
      const isCustomInputText = !options.userId || (!options.documentId && !options.category);

      if (isCustomInputText) {
        console.log(`GenericDocumentContentExtractor: Using simplified vector similarity for custom input text`);
        // Use the simplified vector similarity approach for custom input text
        const sections = this.splitIntoSections(documentText);
        const sectionEmbeddings = await Promise.all(
          sections.map(async section => {
            try {
              return { ...section, embedding: await getEmbedding(section.content) };
            } catch (e) { console.error("Error embedding section:", e); return null; }
          })
        );
        const validSections = sectionEmbeddings.filter(s => s && s.embedding) as ({ title: string; content: string; embedding: number[] })[];

        if (validSections.length > 0) {
          const scoredSections = validSections.map(section => ({
            ...section,
            score: this.calculateCosineSimilarity(queryEmbedding, section.embedding)
          })).sort((a, b) => b.score - a.score);

          const topSections = scoredSections.slice(0, 3).filter(s => s.score > 0.5); // Only consider somewhat relevant sections

          if (topSections.length > 0) {
            result.relevanceScore = topSections[0].score;
            result.relevantSections = topSections.map(s => s.content);
            result.vectorSimilarityResults = topSections.map(s => ({
              score: s.score, matchedText: s.content, section: s.title
            }));

            // Log the simplified vector similarity search results
            console.log(`GenericDocumentContentExtractor: Custom input text vector search results:`, JSON.stringify({
              query: options.userQuery,
              totalSections: validSections.length,
              topSectionsCount: topSections.length,
              topSections: topSections.slice(0, 3).map(s => ({
                title: s.title,
                score: s.score,
                contentPreview: s.content.substring(0, 100) + (s.content.length > 100 ? '...' : '')
              }))
            }, null, 2));

            // Log each section's content for debugging
            topSections.forEach((section, index) => {
              console.log(`GenericDocumentContentExtractor: CUSTOM INPUT SECTION ${index + 1}/${topSections.length} - ${section.title} (Relevance: ${(section.score * 100).toFixed(1)}%)`);
              console.log(`---SECTION CONTENT START---\n${section.content}\n---SECTION CONTENT END---`);
            });

            // Update the document content with the vector similarity results
            if (result.vectorSimilarityResults && result.vectorSimilarityResults.length > 0) {
              const relevantSections = result.vectorSimilarityResults.map(vsr =>
                `[Section: ${vsr.section || 'Unnamed Section'} (Relevance: ${(vsr.score * 100).toFixed(1)}%)]\n${vsr.matchedText}`
              ).join('\n\n---\n\n');

              // Store the original content in case we need it later
              if (!options.originalContent) {
                (options as any).originalContent = options.documentContent;
              }

              // Update the document content with the relevant sections
              const newContent = `MOST RELEVANT SECTIONS BASED ON VECTOR SIMILARITY:\n\n${relevantSections}`;
              options.documentContent = newContent;
              console.log(`GenericDocumentContentExtractor: Updated document content with vector similarity results from custom input text`);

              // Log the final content that will be sent to the LLM
              console.log(`GenericDocumentContentExtractor: FINAL CONTENT BEING SENT TO LLM (custom input text):`);
              console.log(`---FINAL CONTENT START---\n${newContent}\n---FINAL CONTENT END---`);
            }
          } else {
            console.log("GenericDocumentContentExtractor: Custom input text vector similarity found no relevant sections.");
          }
        } else {
          console.log("GenericDocumentContentExtractor: No valid sections to embed for custom input text similarity.");
        }
      } else if (options.userId) {
        const contentSelector = new ContentSelector(options.userId);

        // Get namespaces - either from documentId or from category
        let namespaces: string[] = [];

        if (options.documentId) {
          // If documentId is provided, use it as the namespace
          namespaces = [options.documentId];
          console.log(`GenericDocumentContentExtractor: Using documentId "${options.documentId}" as namespace`);
        } else if (options.category) {
          // If category is provided but no documentId, fetch namespaces for the category
          console.log(`GenericDocumentContentExtractor: Fetching namespaces for category "${options.category}"`);
          namespaces = await fetchNamespacesForCategory(options.userId, options.category);
          console.log(`GenericDocumentContentExtractor: Found ${namespaces.length} namespaces for category "${options.category}":`, namespaces);
        }

        if (namespaces.length > 0) {
          const minimalTokenManager = {
            getTokenConfig: () => ({ maxTokens: 8000, reservedTokens: 1000, model: 'text-embedding-ada-002' }), // Example embedding model
            trackTokenUsage: () => {},
            getRemainingTokens: () => 7000 // Example
          };
          // Remove the fourth argument (options.userQuery) as it's not expected by selectContent
          const contentResult = await contentSelector.selectContent(
            queryEmbedding, namespaces, minimalTokenManager as any
          );
          if (contentResult && contentResult.content.length > 0) { // Ensure content is not empty
            result.relevanceScore = contentResult.metadata.averageRelevance;
            result.relevantSections = [contentResult.content];

            // Log the raw sources for debugging
            console.log(`GenericDocumentContentExtractor: Raw sources from ContentSelector:`, JSON.stringify(
              contentResult.metadata.sources.slice(0, 3).map(source => ({
                doc_id: source.doc_id,
                chunkId: (source as any).chunkId,
                metadata: (source as any).metadata,
                title: source.title,
                relevance: source.relevance
              })), null, 2)
            );

            // Extract chunk IDs from the sources
            const chunkIds = contentResult.metadata.sources
              .filter(source => source.doc_id)
              .map(source => {
                // Check if the source has a chunkId property
                if ((source as any).chunkId) {
                  console.log(`GenericDocumentContentExtractor: Found chunkId property: ${(source as any).chunkId}`);
                  return (source as any).chunkId;
                }

                // Check if the source has a metadata.chunkId property
                if ((source as any).metadata && (source as any).metadata.chunkId) {
                  console.log(`GenericDocumentContentExtractor: Found metadata.chunkId property: ${(source as any).metadata.chunkId}`);
                  return (source as any).metadata.chunkId;
                }

                // If not, try to extract it from the doc_id (which might be in the format namespace_chunkNumber)
                const docIdParts = source.doc_id.split('_');
                if (docIdParts.length > 1) {
                  console.log(`GenericDocumentContentExtractor: Using doc_id as chunkId: ${source.doc_id}`);
                  return source.doc_id; // The doc_id is already in the format of a chunkId
                }

                console.log(`GenericDocumentContentExtractor: Could not extract chunkId from source with doc_id: ${source.doc_id}`);
                return null;
              })
              .filter(chunkId => chunkId !== null) as string[];

            console.log(`GenericDocumentContentExtractor: Extracted ${chunkIds.length} chunk IDs from vector similarity results:`, chunkIds);

            // Fetch the actual content of the chunks from Firestore
            if (chunkIds.length > 0 && options.userId) {
              try {
                console.log(`GenericDocumentContentExtractor: Fetching chunk content from Firestore for chunks:`, chunkIds);

                // Create a FirestoreStore instance with the user's collection path
                const firestoreStore = new FirestoreStore({
                  collectionPath: `users/${options.userId}/byteStoreCollection`
                });

                // Fetch the chunks using the FirestoreStore instance
                const chunks = await fetchDocumentChunksByChunkIds(chunkIds, firestoreStore);

                console.log(`GenericDocumentContentExtractor: Retrieved ${chunks.length} chunks from Firestore`);

                // Log the actual chunk data for debugging
                console.log(`GenericDocumentContentExtractor: Chunk details:`, JSON.stringify(chunks.map(chunk => ({
                  id: chunk.id,
                  contentPreview: chunk.pageContent.substring(0, 100) + (chunk.pageContent.length > 100 ? '...' : '')
                })), null, 2));

                // Log the full content of each chunk (for detailed debugging)
                chunks.forEach((chunk, index) => {
                  console.log(`GenericDocumentContentExtractor: FULL CHUNK CONTENT [${index + 1}/${chunks.length}] - ID: ${chunk.id}`);
                  console.log(`---CHUNK CONTENT START---\n${chunk.pageContent}\n---CHUNK CONTENT END---`);
                });

                // Log the total content size being processed
                const totalContentSize = chunks.reduce((total, chunk) => total + (chunk.pageContent?.length || 0), 0);
                console.log(`GenericDocumentContentExtractor: Total content size from chunks: ${totalContentSize} characters`);
                console.log(`GenericDocumentContentExtractor: Average chunk size: ${Math.round(totalContentSize / chunks.length)} characters`);

                // Map the chunks to the sources
                const sourcesWithContent = contentResult.metadata.sources.map(source => {
                  const sourceChunkId = (source as any).chunkId || source.doc_id;
                  const matchingChunk = chunks.find(chunk => chunk.id === sourceChunkId);

                  return {
                    ...source,
                    text_content: matchingChunk ? matchingChunk.pageContent : (source as any).text_content || ''
                  };
                });

                // Update the vector similarity results with the actual content
                result.vectorSimilarityResults = sourcesWithContent.map(source => ({
                  score: source.relevance || 0,
                  matchedText: source.text_content || contentResult.content,
                  section: source.title || source.doc_id || 'Unknown Section'
                }));

                console.log(`GenericDocumentContentExtractor: Updated vector similarity results with actual chunk content`);
              } catch (error) {
                console.error(`GenericDocumentContentExtractor: Error fetching chunk content from Firestore:`, error);
                // Fall back to the original content if there was an error
                result.vectorSimilarityResults = contentResult.metadata.sources.map(source => ({
                  score: source.relevance || 0,
                  matchedText: (source as any).text_content || contentResult.content,
                  section: source.title || source.doc_id || 'Unknown Section'
                }));
              }
            } else {
              // If no chunk IDs or no userId, use the original content
              result.vectorSimilarityResults = contentResult.metadata.sources.map(source => ({
                score: source.relevance || 0,
                matchedText: (source as any).text_content || contentResult.content,
                section: source.title || source.doc_id || 'Unknown Section'
              }));
            }

            // Log the vector similarity search results from ContentSelector
            console.log(`GenericDocumentContentExtractor: ContentSelector vector search results:`, JSON.stringify({
              query: options.userQuery,
              documentId: options.documentId,
              averageRelevance: contentResult.metadata.averageRelevance,
              resultCount: contentResult.metadata.sources.length,
              chunkIds: chunkIds,
              topSources: result.vectorSimilarityResults.slice(0, 3).map(vsr => ({
                section: vsr.section,
                score: vsr.score,
                matchedTextPreview: vsr.matchedText.substring(0, 100) + (vsr.matchedText.length > 100 ? '...' : '')
              }))
            }, null, 2));

            // Update the document content with the vector similarity results
            if (result.vectorSimilarityResults && result.vectorSimilarityResults.length > 0) {
              const relevantSections = result.vectorSimilarityResults.map(vsr =>
                `[Section: ${vsr.section || 'Unnamed Section'} (Relevance: ${(vsr.score * 100).toFixed(1)}%)]\n${vsr.matchedText}`
              ).join('\n\n---\n\n');

              // Store the original content in case we need it later
              if (!options.originalContent) {
                (options as any).originalContent = options.documentContent;
              }

              // Update the document content with the relevant sections
              const newContent = `MOST RELEVANT SECTIONS BASED ON VECTOR SIMILARITY:\n\n${relevantSections}`;
              options.documentContent = newContent;
              console.log(`GenericDocumentContentExtractor: Updated document content with vector similarity results from ContentSelector`);

              // Log the final content that will be sent to the LLM
              console.log(`GenericDocumentContentExtractor: FINAL CONTENT BEING SENT TO LLM:`);
              console.log(`---FINAL CONTENT START---\n${newContent}\n---FINAL CONTENT END---`);
            }
          } else {
             console.log("GenericDocumentContentExtractor: Vector similarity via ContentSelector yielded no content.");
          }
        } else {
             console.log("GenericDocumentContentExtractor: No documentId provided for ContentSelector based vector similarity.");
        }
      } else {
        console.log("GenericDocumentContentExtractor: Using simplified vector similarity (no userId).");
        const sections = this.splitIntoSections(documentText);
        const sectionEmbeddings = await Promise.all(
          sections.map(async section => {
            try {
              return { ...section, embedding: await getEmbedding(section.content) };
            } catch (e) { console.error("Error embedding section:", e); return null; }
          })
        );
        const validSections = sectionEmbeddings.filter(s => s && s.embedding) as ({ title: string; content: string; embedding: number[] })[];
        if (validSections.length > 0) {
            const scoredSections = validSections.map(section => ({
                ...section,
                score: this.calculateCosineSimilarity(queryEmbedding, section.embedding)
            })).sort((a, b) => b.score - a.score);

            const topSections = scoredSections.slice(0, 3).filter(s => s.score > 0.5); // Only consider somewhat relevant sections
            if (topSections.length > 0) {
                result.relevanceScore = topSections[0].score;
                result.relevantSections = topSections.map(s => s.content);
                result.vectorSimilarityResults = topSections.map(s => ({
                    score: s.score, matchedText: s.content, section: s.title
                }));

                // Log the simplified vector similarity search results
                console.log(`GenericDocumentContentExtractor: Simplified vector search results:`, JSON.stringify({
                    query: options.userQuery,
                    totalSections: validSections.length,
                    topSectionsCount: topSections.length,
                    topSections: topSections.slice(0, 3).map(s => ({
                        title: s.title,
                        score: s.score,
                        contentPreview: s.content.substring(0, 100) + (s.content.length > 100 ? '...' : '')
                    }))
                }, null, 2));

                // Update the document content with the vector similarity results
                if (result.vectorSimilarityResults && result.vectorSimilarityResults.length > 0) {
                  const relevantSections = result.vectorSimilarityResults.map(vsr => {
                    // Log each section's content for debugging (simplified approach)
                    console.log(`GenericDocumentContentExtractor: SIMPLIFIED SECTION CONTENT - ${vsr.section || 'Unnamed Section'} (Relevance: ${(vsr.score * 100).toFixed(1)}%)`);
                    console.log(`---SIMPLIFIED SECTION CONTENT START---\n${vsr.matchedText}\n---SIMPLIFIED SECTION CONTENT END---`);

                    return `[Section: ${vsr.section || 'Unnamed Section'} (Relevance: ${(vsr.score * 100).toFixed(1)}%)]\n${vsr.matchedText}`;
                  }).join('\n\n---\n\n');

                  // Store the original content in case we need it later
                  if (!options.originalContent) {
                    (options as any).originalContent = options.documentContent;
                  }

                  // Update the document content with the relevant sections
                  const newContent = `MOST RELEVANT SECTIONS BASED ON VECTOR SIMILARITY:\n\n${relevantSections}`;
                  options.documentContent = newContent;
                  console.log(`GenericDocumentContentExtractor: Updated document content with vector similarity results from simplified similarity`);

                  // Log the final content that will be sent to the LLM
                  console.log(`GenericDocumentContentExtractor: FINAL CONTENT BEING SENT TO LLM (simplified approach):`);
                  console.log(`---FINAL CONTENT START---\n${newContent}\n---FINAL CONTENT END---`);
                }
            } else {
                console.log("GenericDocumentContentExtractor: Simplified vector similarity found no relevant sections.");
            }
        } else {
            console.log("GenericDocumentContentExtractor: No valid sections to embed for simplified similarity.");
        }
      }
    } catch (vectorError) {
      console.error("GenericDocumentContentExtractor: Error performing vector similarity assessment:", vectorError);
      result.error = (result.error ? result.error + "; " : "") + "Vector similarity failed: " + (vectorError instanceof Error ? vectorError.message : String(vectorError));
    }
  },

  createExtractionPrompt(options: DocumentContentExtractorOptions): string {
    if (options.userQuery && options.userQuery.trim().length > 0) {
      return this.createFocusedExtractionPrompt(options);
    }
    return `You are a highly sophisticated Document Content and Meaning Extractor AI. Your primary objective is to analyze the following document comprehensively and provide a detailed breakdown of its content and inferred meaning. Assume you have zero prior knowledge about this document's origin, subject matter, author, or intended audience. Your analysis should be based solely on the information provided.

Please structure your output to cover the following aspects in detail:

I. Document Identification & Overview:
1. Document Type (Best Guess): Based on structure, language, and content, what type of document does this appear to be (e.g., email, report, academic paper, legal contract, news article, creative writing, technical manual, meeting minutes, policy document, personal letter, code snippet with comments, etc.)? Justify your guess.
2. Primary Subject/Topic: What is the central subject or topic of this document? Be as specific as possible.
3. Core Purpose/Objective: What does the document aim to achieve or communicate? (e.g., inform, persuade, instruct, request, command, entertain, document, propose, analyze, etc.)

II. Content Extraction & Structure:
1. Text Summarization: If feasible for the interface, produce a summary of the full text. If not, confirm you have processed the entirety of the provided text.
2. Structural Elements: Identify and describe any discernible structural elements (e.g., title, headings, subheadings, abstract, introduction, body paragraphs, conclusion, bullet points, numbered lists, salutations, closings, signatures, footnotes, appendices, code blocks). How do these elements contribute to the document's organization and readability?
3. Key Information Points/Data: List the most critical pieces of information, facts, data, or claims presented in the document. For each, briefly explain its significance within the document's context.
4. Key Arguments/Theses (if applicable): If the document presents arguments or a central thesis, identify them clearly. What evidence or reasoning is provided to support them?

III. Meaning & Contextual Inference:
1. Intended Audience (Inferred): Based on language, tone, complexity, and content, who is the likely intended audience for this document? (e.g., general public, technical experts, internal team, specific individual, legal professionals, etc.)
2. Tone and Style: Describe the overall tone (e.g., formal, informal, neutral, persuasive, urgent, cautionary, humorous, academic, technical) and writing style. Provide examples from the text to support your assessment.
3. Keywords and Key Terminology: List significant keywords and specialized terminology used. If the meaning of any term is defined or can be strongly inferred from the context, please provide that definition.
4. Implicit Information & Assumptions: Are there any underlying assumptions made by the author that are not explicitly stated? What information is implied rather than directly articulated?
5. Relationships Between Sections/Ideas: How do different parts of the document relate to each other? Is there a logical flow, a chronological order, a cause-and-effect relationship, or a comparative structure?

IV. Analysis & Interpretation:
1. Main Takeaways/Conclusions: What are the 2-5 most important takeaways or conclusions a reader should derive from this document?
2. Calls to Action/Next Steps (if any): Does the document explicitly or implicitly suggest any actions to be taken, decisions to be made, or further steps?
3. Potential Significance/Impact: Based solely on the document's content, what could be its potential significance or impact for the inferred audience or on the subject matter it discusses?
4. Points of Ambiguity or Unclear Information: Are there any parts of the document that are ambiguous, unclear, or seem to be missing crucial context (that cannot be inferred from the document itself)? Please list them.
5. Questions Raised: What questions does the document raise that are not answered within it?

V. Summary:
1. Concise Summary: Provide a brief (3-5 sentences) summary that encapsulates the essence of the document, its purpose, and its main points.
2. Extended Summary: Provide a more detailed summary (1-2 paragraphs) that covers the key aspects identified in your analysis.

Document Content:
${options.documentTitle ? `Title: ${options.documentTitle}\n` : ''}
${options.documentId ? `Document ID: ${options.documentId}\n` : ''}
---
${options.documentContent}
---

Present your findings clearly, using the section headings and numbering provided above. Be thorough and objective. If certain aspects are not applicable to the given document, please state 'Not Applicable' with a brief justification.`;
  },

  createFocusedExtractionPrompt(options: DocumentContentExtractorOptions): string {
    // Check if we have vector similarity results in the options
    const vectorResults = (options as any).vectorSimilarityResults;

    // If we have vector similarity results, use them to create a more focused prompt
    if (vectorResults && Array.isArray(vectorResults) && vectorResults.length > 0) {
      console.log(`GenericDocumentContentExtractor: Using vector similarity results in prompt`);

      // Extract the relevant sections from the vector similarity results
      const relevantSections = vectorResults.map(result =>
        `[Section: ${result.section || 'Unnamed Section'} (Relevance: ${(result.score * 100).toFixed(1)}%)]\n${result.matchedText}`
      ).join('\n\n---\n\n');

      return `You are a specialized Document Content Extractor AI focused on extracting ONLY information that is directly relevant to a specific query. Your task is to analyze the document and extract ONLY the information needed to address the query.

USER QUERY: "${options.userQuery}"

INSTRUCTIONS:
1. Focus exclusively on finding and extracting information from the document that directly helps answer the query.
2. Ignore any content that is not relevant to the query.
3. Do NOT perform a comprehensive document analysis - focus only on query-relevant information.
4. Structure your response as follows:
   a. Directly relevant information (facts, data, arguments, etc. that address the query). Present this clearly.
   b. If the document contains multiple distinct pieces of information relevant to the query, list them.
   c. Briefly summarize how this extracted information directly answers or contributes to answering the query.

Document Content (Most Relevant Sections Based on Vector Similarity):
${options.documentTitle ? `Title: ${options.documentTitle}\n` : ''}
${options.documentId ? `Document ID: ${options.documentId}\n` : ''}
---
${relevantSections}
---

IMPORTANT: Your response should be concise and focused ONLY on information relevant to the query. Do not include comprehensive document analysis or information that doesn't directly help answer the query. If no relevant information is found, state that clearly.`;
    }

    // If no vector similarity results, use the original prompt
    return `You are a specialized Document Content Extractor AI focused on extracting ONLY information that is directly relevant to a specific query. Your task is to analyze the document and extract ONLY the information needed to address the query.

USER QUERY: "${options.userQuery}"

INSTRUCTIONS:
1. Focus exclusively on finding and extracting information from the document that directly helps answer the query.
2. Ignore any content that is not relevant to the query.
3. Do NOT perform a comprehensive document analysis - focus only on query-relevant information.
4. Structure your response as follows:
   a. Directly relevant information (facts, data, arguments, etc. that address the query). Present this clearly.
   b. If the document contains multiple distinct pieces of information relevant to the query, list them.
   c. Briefly summarize how this extracted information directly answers or contributes to answering the query.

Document Content:
${options.documentTitle ? `Title: ${options.documentTitle}\n` : ''}
${options.documentId ? `Document ID: ${options.documentId}\n` : ''}
---
${options.documentContent}
---

IMPORTANT: Your response should be concise and focused ONLY on information relevant to the query. Do not include comprehensive document analysis or information that doesn't directly help answer the query. If no relevant information is found, state that clearly.`;
  },

  extractStructuredAnalysis(response: string): DocumentContentExtractorResult['structuredAnalysis'] {
    const getMatch = (regex: RegExp) => {
        const match = response.match(regex);
        return match ? match[1].trim() : undefined;
    };
    const getListMatches = (regex: RegExp) => {
        const sectionMatch = response.match(regex);
        return sectionMatch
            ? sectionMatch[1].split(/\n\s*[\d.)*-•]+\s+/).map(item => item.trim()).filter(item => item.length > 0)
            : undefined;
    };

    return {
        // Remove 's' flag which is only available in ES2018+ and use case-insensitive 'i' flag only
        documentType: getMatch(/I\.\s*Document Identification & Overview.*?1\.\s*Document Type.*?:(.*?)(?:\n[ \t]*2\.|\nII\.|\Z)/i),
        primarySubject: getMatch(/I\.\s*Document Identification & Overview.*?2\.\s*Primary Subject\/Topic.*?:(.*?)(?:\n[ \t]*3\.|\nII\.|\Z)/i),
        corePurpose: getMatch(/I\.\s*Document Identification & Overview.*?3\.\s*Core Purpose\/Objective.*?:(.*?)(?:\nII\.|\Z)/i),
        intendedAudience: getMatch(/III\.\s*Meaning & Contextual Inference.*?1\.\s*Intended Audience.*?:(.*?)(?:\n[ \t]*2\.|\nIV\.|\Z)/i),
        mainTakeaways: getListMatches(/IV\.\s*Analysis & Interpretation.*?1\.\s*Main Takeaways\/Conclusions.*?:([\s\S]*?)(?:\n[ \t]*2\.|\nIV\.\s*Analysis & Interpretation.*?\n[ \t]*[2-9]\.|\nV\.|\Z)/i),
        callsToAction: getListMatches(/IV\.\s*Analysis & Interpretation.*?2\.\s*Calls to Action\/Next Steps.*?:([\s\S]*?)(?:\n[ \t]*3\.|\nIV\.\s*Analysis & Interpretation.*?\n[ \t]*[3-9]\.|\nV\.|\Z)/i)
    };
  },

  splitIntoSections(content: string): Array<{title: string, content: string}> {
    const sectionRegex = /^(#{1,4})\s+(.*?)\s*$/gm; // Matches lines starting with #, ##, ###, ####
    const sections: Array<{title: string, content: string, startIndex: number, level: number}> = [];
    let match;
    while ((match = sectionRegex.exec(content)) !== null) {
      sections.push({
        title: match[2].trim(),
        content: '',
        startIndex: match.index,
        level: match[1].length
      });
    }

    if (sections.length === 0) {
      return [{ title: 'Full Document', content: content.trim() }];
    }

    const resultSections: Array<{title: string, content: string}> = [];
    for (let i = 0; i < sections.length; i++) {
      const currentSection = sections[i];
      const nextSectionIndex = (i + 1 < sections.length) ? sections[i+1].startIndex : content.length;
      const sectionContent = content.substring(currentSection.startIndex, nextSectionIndex).trim();

      // Remove the header line itself from the content of the section
      const headerEndIndex = sectionContent.indexOf('\n');
      const contentWithoutHeader = headerEndIndex !== -1 ? sectionContent.substring(headerEndIndex + 1) : '';

      resultSections.push({
        title: currentSection.title,
        content: contentWithoutHeader.trim()
      });
    }
    return resultSections.filter(s => s.content.length > 0); // Only return sections with actual content
  },

  calculateCosineSimilarity(vec1: number[], vec2: number[]): number {
    if (!vec1 || !vec2 || vec1.length !== vec2.length || vec1.length === 0) {
      // console.warn("Cosine similarity: Invalid input vectors.", vec1, vec2);
      return 0;
    }
    let dotProduct = 0;
    let mag1 = 0;
    let mag2 = 0;
    for (let i = 0; i < vec1.length; i++) {
      dotProduct += (vec1[i] || 0) * (vec2[i] || 0);
      mag1 += (vec1[i] || 0) ** 2;
      mag2 += (vec2[i] || 0) ** 2;
    }
    mag1 = Math.sqrt(mag1);
    mag2 = Math.sqrt(mag2);
    if (mag1 === 0 || mag2 === 0) return 0;
    const similarity = dotProduct / (mag1 * mag2);
    return Math.max(0, Math.min(1, similarity)); // Clamp between 0 and 1
  },

  getToolDefinition() {
    return {
      type: "function" as const,
      function: {
        name: "genericDocumentContentExtractorTool",
        description: "Extracts comprehensive content and meaning from documents. Can also focus extraction based on a user query and perform relevance assessment.",
        parameters: {
          type: "object" as const,
          properties: {
            documentContent: {
              type: "string" as const,
              description: "The content of the document to analyze."
            },
            documentTitle: {
              type: "string" as const,
              description: "Optional title of the document."
            },
            documentId: {
              type: "string" as const,
              description: "Optional document identifier, used for context and vector similarity if userId is also provided."
            },
            modelProvider: {
                type: "string" as const,
                enum: ["anthropic", "google", "groq", "openai"],
                description: "Optional specific LLM provider to use."
            },
            modelName: {
                type: "string" as const,
                description: "Optional specific model name. Can be in 'provider/model_name' format (e.g., 'openai/gpt-4o') or just 'model_name' if provider is also set or can be inferred."
            },
            userQuery: {
              type: "string" as const,
              description: "Optional user query. If provided, extraction focuses on query-relevant info, and vector similarity assessment may be performed."
            },
            performVectorSimilarity: {
              type: "boolean" as const,
              default: false,
              description: "Whether to perform vector similarity assessment against the userQuery. Requires userQuery to be set."
            },
            userId: {
              type: "string" as const,
              description: "User ID, required if performVectorSimilarity is true and you want to search user's existing vector store via ContentSelector."
            }
          },
          required: ["documentContent"]
        }
      }
    };
  }
};

// Example of how LlmProvider might be defined if not imported
// export type LlmProvider = 'anthropic' | 'google' | 'groq' | 'openai';