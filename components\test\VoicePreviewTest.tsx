"use client";

import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Mic, Volume2, Loader2 } from 'lucide-react';

interface VoicePreviewTestProps {
  voiceId: string;
  voiceName: string;
  previewText: string;
}

export default function VoicePreviewTest({ voiceId, voiceName, previewText }: VoicePreviewTestProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const testVoicePreview = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);
    setIsPlaying(false);

    try {
      console.log(`[VOICE_TEST] Testing voice preview for ${voiceName} (${voiceId})`);

      const response = await fetch('/api/test-voice-preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          voiceId,
          text: previewText
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);

      console.log(`[VOICE_TEST] Audio blob created, size: ${audioBlob.size} bytes`);

      const audio = new Audio(audioUrl);
      
      audio.onended = () => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
        setSuccess(`Successfully played ${voiceName} voice preview!`);
      };

      audio.onerror = (e) => {
        console.error('[VOICE_TEST] Audio playback error:', e);
        setError('Failed to play audio');
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
      };

      await audio.play();
      setIsPlaying(true);
      console.log(`[VOICE_TEST] Audio playback started for ${voiceName}`);

    } catch (error) {
      console.error('[VOICE_TEST] Error:', error);
      setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border border-gray-300 rounded-lg bg-white dark:bg-gray-800 dark:border-gray-600">
      <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">
        Voice Test: {voiceName}
      </h3>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
        Voice ID: {voiceId}
      </p>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Preview Text: "{previewText}"
      </p>

      <Button
        onClick={testVoicePreview}
        disabled={isLoading || isPlaying}
        className="w-full mb-4"
      >
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Generating...
          </>
        ) : isPlaying ? (
          <>
            <Volume2 className="h-4 w-4 mr-2" />
            Playing...
          </>
        ) : (
          <>
            <Mic className="h-4 w-4 mr-2" />
            Test Voice Preview
          </>
        )}
      </Button>

      {error && (
        <div className="p-3 bg-red-100 border border-red-300 rounded text-red-700 text-sm">
          <strong>Error:</strong> {error}
        </div>
      )}

      {success && (
        <div className="p-3 bg-green-100 border border-green-300 rounded text-green-700 text-sm">
          <strong>Success:</strong> {success}
        </div>
      )}
    </div>
  );
}
