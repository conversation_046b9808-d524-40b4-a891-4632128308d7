// app/api/pmo-process-document/route.ts
import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions'; // Ensure this path is correct
import { processPMODocument } from '../../../lib/pmo/processPMODocument'; // Ensure this path is correct

interface ProcessPMODocumentRequest {
  title: string;
  content: string;
  pmoId: string;
  userId: string;
  category?: string;
  metadata?: Record<string, any>;
}

export async function POST(req: Request) {
  console.log("====== pmo-process-document API START ======"); // 1. Entry log
  try {
    console.log("Attempting to get server session...");
    const session = await getServerSession(authOptions);
    console.log("Session:", session ? "Obtained" : "Not found/Error");

    console.log("Attempting to parse request body...");
    const body = await req.json() as ProcessPMODocumentRequest;
    console.log("Request body parsed:", body);

    const {
      title,
      content,
      pmoId,
      userId, // This comes from the client's body
      category = 'PMO',
      metadata = {}
    } = body;

    if (!title || !content || !pmoId) {
      console.error("Missing required parameters:", { title, content, pmoId });
      return NextResponse.json(
        { error: 'Missing required parameters: title, content, or pmoId' },
        { status: 400 }
      );
    }

    let validUserId = userId; // From client body
    if (!validUserId && session?.user?.email) {
      console.log("Using session user email as userId:", session.user.email);
      validUserId = session.user.email;
    }

    if (!validUserId) {
      console.error("No valid userId found. Body userId:", userId, "Session:", session);
      return NextResponse.json(
        { error: 'No user ID provided and no authenticated user found' },
        { status: 401 } // 401 for unauthorized
      );
    }
    console.log("Final validUserId:", validUserId);

    console.log("Calling processPMODocument with params:", { title, pmoId, userId: validUserId, category, metadataKeys: Object.keys(metadata) });
    // Note: Don't log full 'content' if it's very large, it can clutter logs.
    // Log a snippet or its length: console.log("Content length:", content.length);

    const result = await processPMODocument({
      title,
      content,
      pmoId,
      userId: validUserId,
      category,
      metadata
    });
    console.log("processPMODocument result:", result);

    if (result.success) {
      console.log("Success response:", { documentId: result.documentId, downloadUrl: result.downloadUrl });
      return NextResponse.json({
        success: true,
        documentId: result.documentId,
        downloadUrl: result.downloadUrl
      });
    } else {
      console.error("processPMODocument returned error:", result.error);
      return NextResponse.json(
        {
          success: false,
          error: result.error || 'Failed to process PMO document (from processPMODocument)'
        },
        { status: 500 }
      );
    }
  } catch (error: any) { // Catch any error
    console.error('====== FATAL ERROR in pmo-process-document API ======', error);
    console.error("Error message:", error.message);
    console.error("Error stack:", error.stack);

    // Special check for JSON parsing errors
    if (error instanceof SyntaxError && error.message.includes("JSON")) {
        console.error("This might be a JSON parsing error from req.json(). Ensure client sends valid JSON.");
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected server error occurred'
      },
      { status: 500 }
    );
  } finally {
    console.log("====== pmo-process-document API END ======");
  }
}