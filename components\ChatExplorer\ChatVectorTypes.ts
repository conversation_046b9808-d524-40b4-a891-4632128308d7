// components/ChatExplorer/ChatVectorTypes.ts

/**
 * ChatVectorTypes.ts
 * 
 * Purpose: This file defines the types and interfaces used in the Chat Vector system.
 * It includes definitions for chat messages, vector metadata, and various helper types
 * and functions used in processing and managing chat data and vector operations.
 * 
 * The types defined here are crucial for maintaining type safety and consistency
 * across the chat and vector processing functionalities of the application.
 */

import type { RecordMetadata } from '@pinecone-database/pinecone';
import type { ChatData } from '@/types';

// Define the structure of a chat message
export interface ChatMessage {
  id: string;
  text: string;
  role: 'user' | 'ai';
  createdAt: {
    seconds: number;
    nanoseconds: number;
  };
  fileDocumentId: string;
  fileName: string;
  category: string;
  userId: string;
}

// Define the structure of a timestamp
export interface Timestamp {
  seconds: number;
  nanoseconds: number;
}

// Define the metadata structure for vectors, extending Pinecone's RecordMetadata
export interface VectorMetadata extends RecordMetadata {
  category: string | 'Unknown';  // Making it explicit that 'Unknown' is a valid value
  summary: string;
  namespace: string;
  documentNamespace: string;
  chatId: string;
  userMessageId: string;
  aiMessageId: string;
  timestamp: number;
  fileDocumentId: string;
  [key: string]: any;
}

// Define the context for chat vector operations
export interface ChatVectorContext {
  selectedDocId: string;
  documentNamespace: string;
  category?: string;  // Making category optional
}

// Define the result structure for chat interactions
export interface ChatInteractionResult {
  vectorId: string;
  namespace: string;
  status: string;
}

// Define the structure for vector service errors
export interface VectorServiceError extends Error {
  status?: number;
  code?: string;
  details?: string;
}

// Define the structure for chat vector requests
export interface ChatVectorRequest {
  userId: string;
  category?: string;  // Making category optional
  chatId: string;
  documentNamespace: string;
  userMessage: ChatMessage;
  aiMessage: ChatMessage;
}

// Define constants
export const DEFAULT_CATEGORY = 'Unknown' as const;

// Define the structure for API responses in chat vector operations
export interface ChatVectorResponse {
  success: boolean;
  vectorId?: string;
  namespace?: string;
  status?: string;
  error?: string;
}

// Define the structure for validation results
export interface ValidationResult {
  isValid: boolean;
  errors?: string[];
}

export interface SearchResult {
  id: string;
  score: number;
  metadata: {
    fileUrl(fileUrl: any): unknown;
    timestamp: string;  // String representation of milliseconds since epoch
    summary: string;
    category: string;
    documentNamespace: string;
    chatId: string;
    userMessageId: string;
    aiMessageId: string;
    text: string;
    namespace?: string;
    fileDocumentId?: string;
    fileName?: string;
  };
}

// Define a utility type for handling partial contexts
export type PartialChatVectorContext = Partial<ChatVectorContext>;

// Type guard function to check if an object is a ChatMessage
export function isChatMessage(obj: any): obj is ChatMessage {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.text === 'string' &&
    (obj.role === 'user' || obj.role === 'ai') &&
    typeof obj.createdAt === 'object' &&
    typeof obj.createdAt.seconds === 'number' &&
    typeof obj.createdAt.nanoseconds === 'number' &&
    typeof obj.fileDocumentId === 'string'
  );
}

// Type guard function to check if an object is ChatData
export function isChatData(obj: any): obj is ChatData {
  return (
    obj &&
    typeof obj.id === 'string' &&
    typeof obj.text === 'string' &&
    (obj.role === 'user' || obj.role === 'ai') &&
    (
      obj.createdAt instanceof Date ||
      (typeof obj.createdAt === 'object' &&
        typeof obj.createdAt.seconds === 'number' &&
        typeof obj.createdAt.nanoseconds === 'number')
    )
  );
}