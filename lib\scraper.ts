import puppeteer from "puppeteer"
import * as cheerio from "cheerio"

/**
 * Scrapes a webpage and extracts all URLs
 */
export async function scrapeUrls(url: string): Promise<string[]> {
  if (!url) throw new Error("URL is required")

  // Validate and normalize the URL
  const normalizedUrl = normalizeUrl(url)

  // Launch a headless browser
  const browser = await puppeteer.launch({
    headless: true,
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
  })

  try {
    const page = await browser.newPage()

    // Set a user agent to avoid being blocked
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    )

    // Navigate to the URL with a timeout
    await page.goto(normalizedUrl, {
      waitUntil: "networkidle2",
      timeout: 30000,
    })

    // Extract all links from the page
    const links = await page.evaluate(() => {
      const anchors = Array.from(document.querySelectorAll("a"))
      return anchors.map((anchor) => anchor.href).filter((href) => href && href.startsWith("http"))
    })

    // Filter and normalize the links
    const baseUrl = new URL(normalizedUrl)
    const uniqueLinks = [...new Set(links)]
      .filter((link) => {
        try {
          const linkUrl = new URL(link)
          // Only include links from the same domain
          return linkUrl.hostname === baseUrl.hostname
        } catch {
          return false
        }
      })
      .map((link) => link.split("#")[0]) // Remove hash fragments
      .filter(Boolean)

    return [...new Set(uniqueLinks)] // Remove duplicates
  } finally {
    await browser.close()
  }
}

/**
 * Normalizes a URL by ensuring it has a protocol
 */
function normalizeUrl(url: string): string {
  if (!url) throw new Error("URL is required")

  try {
    // Try to create a URL object to validate
    new URL(url)
    return url
  } catch {
    // If it fails, assume it needs https://
    if (!url.startsWith("http://") && !url.startsWith("https://")) {
      return `https://${url}`
    }
    return url
  }
}

/**
 * Scrapes content from a URL
 */
export async function scrapeContent(url: string): Promise<string> {
  if (!url) throw new Error("URL is required")

  const browser = await puppeteer.launch({
    headless: true,
    args: ["--no-sandbox", "--disable-setuid-sandbox"],
  })

  try {
    const page = await browser.newPage()

    // Set a user agent to avoid being blocked
    await page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    )

    // Navigate to the URL with a timeout
    await page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 30000,
    })

    // Get the page HTML
    const html = await page.content()

    // Use Cheerio to parse and extract the text content
    const $ = cheerio.load(html)

    // Remove script, style, and other non-content elements
    $("script, style, meta, link, noscript, iframe, svg").remove()

    // Get the page title
    const title = $("title").text().trim()

    // Extract main content
    const mainContent = $("main, article, #content, .content, .main, #main").first()

    let content = ""

    if (mainContent.length > 0) {
      // If we found a main content area, use that
      content = mainContent.text()
    } else {
      // Otherwise, get the body content
      content = $("body").text()
    }

    // Clean up the text
    content = content.replace(/\s+/g, " ").replace(/\n+/g, "\n").trim()

    return `# ${title}\n\n${content}`
  } finally {
    await browser.close()
  }
}

