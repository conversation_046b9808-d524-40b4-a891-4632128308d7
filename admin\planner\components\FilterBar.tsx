'use client';

import React from 'react';
import { Filter, Search, X } from 'lucide-react';
import { Resource } from '../types';

interface FilterBarProps {
  filter: {
    status: string;
    category: string;
    search: string;
    assignedTo: string;
  };
  setFilter: React.Dispatch<React.SetStateAction<{
    status: string;
    category: string;
    search: string;
    assignedTo: string;
  }>>;
  categories: string[];
  resources: Resource[];
}

const FilterBar: React.FC<FilterBarProps> = ({
  filter,
  setFilter,
  categories,
  resources
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilter(prev => ({ ...prev, [name]: value }));
  };

  const clearFilters = () => {
    setFilter({
      status: 'all',
      category: 'all',
      search: '',
      assignedTo: 'all'
    });
  };

  const isFiltering = 
    filter.status !== 'all' || 
    filter.category !== 'all' || 
    filter.search !== '' || 
    filter.assignedTo !== 'all';

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
      <div className="flex items-center justify-between mb-3">
        <h2 className="text-lg font-medium text-gray-700 flex items-center">
          <Filter className="w-5 h-5 mr-2 text-purple-600" />
          Filter Tasks
        </h2>
        
        {isFiltering && (
          <button
            onClick={clearFilters}
            className="text-sm text-red-600 hover:text-red-800 flex items-center"
          >
            <X className="w-4 h-4 mr-1" />
            Clear Filters
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Status
          </label>
          <select
            name="status"
            value={filter.status}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded-md bg-white"
          >
            <option value="all">All Statuses</option>
            <option value="Not Started">Not Started</option>
            <option value="In Progress">In Progress</option>
            <option value="Reviewed">Reviewed</option>
            <option value="Complete">Complete</option>
          </select>
        </div>
        
        {/* Category Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            name="category"
            value={filter.category}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded-md bg-white"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>
        
        {/* Assigned To Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Assigned To
          </label>
          <select
            name="assignedTo"
            value={filter.assignedTo}
            onChange={handleChange}
            className="w-full p-2 border border-gray-300 rounded-md bg-white"
          >
            <option value="all">All Resources</option>
            {resources.map(resource => (
              <option key={resource.id} value={resource.id}>
                {resource.name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Search Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              name="search"
              value={filter.search}
              onChange={handleChange}
              placeholder="Search tasks..."
              className="w-full p-2 pl-10 border border-gray-300 rounded-md"
            />
          </div>
        </div>
      </div>
      
      {isFiltering && (
        <div className="mt-3 pt-3 border-t border-gray-200 text-sm text-gray-600">
          <span className="font-medium">Active filters:</span>{' '}
          {filter.status !== 'all' && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-2">
              Status: {filter.status}
            </span>
          )}
          {filter.category !== 'all' && (
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs mr-2">
              Category: {filter.category}
            </span>
          )}
          {filter.assignedTo !== 'all' && (
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs mr-2">
              Assigned to: {resources.find(r => r.id === filter.assignedTo)?.name}
            </span>
          )}
          {filter.search && (
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs mr-2">
              Search: "{filter.search}"
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterBar;
