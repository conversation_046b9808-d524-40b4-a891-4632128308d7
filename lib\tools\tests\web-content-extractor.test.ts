import { webContentExtractorTool, extractAndFormatContent } from '../web-content-extractor';

/**
 * This is a simple test file to verify that the web content extractor tool works correctly.
 * You can run this test with:
 * 
 * ```
 * npx ts-node lib/tools/tests/web-content-extractor.test.ts
 * ```
 */

async function testWebContentExtractor() {
  console.log('Testing web content extractor...');
  
  const testUrl = 'https://example.com';
  
  try {
    // Test the class-based implementation
    console.log(`Testing extraction from ${testUrl}...`);
    const extractionResult = await webContentExtractorTool.extractContent(testUrl);
    
    console.log('Extraction result:');
    console.log(`URL: ${extractionResult.url}`);
    console.log(`Title: ${extractionResult.title}`);
    console.log(`Metadata: ${JSON.stringify(extractionResult.metadata, null, 2)}`);
    console.log('Formatted content:');
    console.log(extractionResult.formattedContent);
    
    // Test the standalone function
    console.log('\nTesting standalone function...');
    const formattedContent = await extractAndFormatContent(testUrl);
    console.log('Formatted content from standalone function:');
    console.log(formattedContent);
    
    // Test multiple URLs
    console.log('\nTesting multiple URLs...');
    const urls = ['https://example.com', 'https://example.org'];
    const combinedContent = await webContentExtractorTool.combineContent(urls);
    console.log('Combined content:');
    console.log(combinedContent);
    
    console.log('\nTests completed successfully!');
  } catch (error) {
    console.error('Error during testing:', error);
  }
}

// Run the test
testWebContentExtractor();
