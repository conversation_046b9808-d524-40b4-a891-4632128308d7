/**
 * Transcript Utilities for PMO Agent Meeting Room
 * 
 * This module provides utilities for processing, formatting, and saving
 * meeting transcripts from PMO Agent conversations.
 */

import { TranscriptMessage } from '../../components/PMO/TranscriptPanel';
import {
  uploadToKnowledgeBaseWithDeduplication,
  computeRagIndex,
  isDocumentIndexedForAgent
} from '../../components/scriptreaderAI/uploadKnowledgebase';
import { updateAgentKnowledgeBase } from '../../components/scriptreaderAI/elevenlabs';

export interface TranscriptMetadata {
  agentName: string;
  agentType: string;
  documentTitle?: string;
  documentId?: string;
  category?: string;
  startTime: Date;
  endTime: Date;
  totalMessages: number;
  userMessages: number;
  agentMessages: number;
}

export interface SaveTranscriptOptions {
  userId: string;
  agentId?: string;
  metadata: TranscriptMetadata;
  uploadToKnowledgeBase?: boolean;
  forceUpload?: boolean;
  onProgress?: (step: string, message: string, progress?: number) => void;
}

export interface SaveTranscriptResult {
  success: boolean;
  pdfUrl?: string;
  fileName?: string;
  knowledgeBaseId?: string;
  error?: string;
}

/**
 * Generate PDF via API endpoint (browser-compatible)
 */
async function generateTranscriptPDF(
  title: string,
  content: string,
  fileName: string,
  category: string,
  metadata: Record<string, string>
): Promise<{ success: boolean; fileUrl?: string; error?: string }> {
  try {
    const response = await fetch('/api/transcript/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title,
        content,
        fileName,
        category,
        metadata
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.error || `HTTP ${response.status}`);
    }

    return result;
  } catch (error) {
    console.error('[TRANSCRIPT_PDF] Error generating PDF via API:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate PDF'
    };
  }
}

/**
 * Format transcript messages into readable text content
 */
export function formatTranscriptContent(
  dialogue: TranscriptMessage[], 
  metadata: TranscriptMetadata
): string {
  const header = `# Meeting Transcript

**Agent:** ${metadata.agentName} (${metadata.agentType})
**Date:** ${metadata.startTime.toLocaleDateString()}
**Time:** ${metadata.startTime.toLocaleTimeString()} - ${metadata.endTime.toLocaleTimeString()}
**Duration:** ${Math.round((metadata.endTime.getTime() - metadata.startTime.getTime()) / 60000)} minutes
**Total Messages:** ${metadata.totalMessages}
${metadata.documentTitle ? `**Document Context:** ${metadata.documentTitle}` : ''}

---

## Conversation

`;

  const conversationContent = dialogue.map((message, index) => {
    const timestamp = message.timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
    
    const speaker = message.role.toLowerCase() === 'user' ? 'User' : metadata.agentName;
    
    return `### ${speaker} (${timestamp})

${message.content}

`;
  }).join('\n');

  const footer = `---

## Summary

- **Total Messages:** ${metadata.totalMessages}
- **User Messages:** ${metadata.userMessages}
- **Agent Messages:** ${metadata.agentMessages}
- **Meeting Duration:** ${Math.round((metadata.endTime.getTime() - metadata.startTime.getTime()) / 60000)} minutes
- **Generated:** ${new Date().toLocaleString()}

*This transcript was automatically generated from a PMO Agent meeting session.*
`;

  return header + conversationContent + footer;
}

/**
 * Generate a filename for the transcript PDF
 */
export function generateTranscriptFileName(metadata: TranscriptMetadata): string {
  const date = metadata.startTime.toISOString().split('T')[0]; // YYYY-MM-DD
  const time = metadata.startTime.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS
  
  const documentPart = metadata.documentTitle 
    ? `-${metadata.documentTitle.replace(/[^a-zA-Z0-9]/g, '-').substring(0, 30)}`
    : '';
  
  const agentPart = `-${metadata.agentName.replace(/[^a-zA-Z0-9]/g, '-')}`;
  
  return `${date}-${time}${documentPart}${agentPart}.pdf`;
}

/**
 * Extract metadata from transcript and meeting context
 */
export function extractTranscriptMetadata(
  dialogue: TranscriptMessage[],
  agentName: string,
  agentType: string,
  documentContext?: any,
  startTime?: Date
): TranscriptMetadata {
  const now = new Date();
  const start = startTime || (dialogue.length > 0 ? dialogue[0].timestamp : now);
  const end = dialogue.length > 0 ? dialogue[dialogue.length - 1].timestamp : now;

  const userMessages = dialogue.filter(msg => msg.role.toLowerCase() === 'user').length;
  const agentMessages = dialogue.length - userMessages;

  // Extract document information and category with priority logic
  let documentTitle: string | undefined;
  let documentId: string | undefined;
  let category: string | undefined;

  console.log('[TRANSCRIPT_METADATA] Extracting category from document context', {
    hasDocumentContext: !!documentContext,
    selectedDocuments: documentContext?.selectedDocuments?.length || 0,
    totalDocuments: documentContext?.documents?.length || 0,
    agentType
  });

  if (documentContext?.selectedDocuments?.length > 0 && documentContext?.documents?.length > 0) {
    // Find the first selected document to extract category
    const selectedDoc = documentContext.documents.find(
      (doc: any) => documentContext.selectedDocuments.includes(doc.id)
    );

    if (selectedDoc) {
      documentTitle = selectedDoc.title;
      documentId = selectedDoc.id;

      // Priority for category extraction:
      // 1. Document's explicit category field
      // 2. Document's metadata category
      // 3. Agent type as fallback
      category = selectedDoc.category ||
                selectedDoc.metadata?.category ||
                selectedDoc.type ||
                agentType;

      console.log('[TRANSCRIPT_METADATA] Found selected document', {
        documentTitle,
        documentId,
        extractedCategory: category,
        documentCategory: selectedDoc.category,
        documentMetadataCategory: selectedDoc.metadata?.category,
        documentType: selectedDoc.type,
        fallbackAgentType: agentType
      });
    }
  }

  // If no category found from documents, use agent type
  if (!category) {
    category = agentType;
    console.log('[TRANSCRIPT_METADATA] Using agent type as category fallback:', category);
  }

  // Ensure category is properly formatted for storage
  const finalCategory = category.replace(/[^a-zA-Z0-9\s-]/g, '').trim() || 'General';

  console.log('[TRANSCRIPT_METADATA] Final metadata extracted', {
    agentName,
    agentType,
    documentTitle,
    documentId,
    finalCategory,
    totalMessages: dialogue.length,
    userMessages,
    agentMessages
  });

  return {
    agentName,
    agentType,
    documentTitle,
    documentId,
    category: finalCategory,
    startTime: start,
    endTime: end,
    totalMessages: dialogue.length,
    userMessages,
    agentMessages
  };
}

/**
 * Save transcript as PDF and optionally upload to knowledge base
 */
export async function saveTranscript(
  dialogue: TranscriptMessage[],
  options: SaveTranscriptOptions
): Promise<SaveTranscriptResult> {
  try {
    console.log('[TRANSCRIPT] Starting transcript save process', {
      messageCount: dialogue.length,
      userId: options.userId,
      agentId: options.agentId,
      uploadToKnowledgeBase: options.uploadToKnowledgeBase,
      hasMetadata: !!options.metadata
    });

    options.onProgress?.('start', 'Initializing transcript save process...', 0);

    if (dialogue.length === 0) {
      return {
        success: false,
        error: 'No transcript content to save'
      };
    }

    // Format transcript content
    options.onProgress?.('format', 'Formatting transcript content...', 10);
    const content = formatTranscriptContent(dialogue, options.metadata);
    const fileName = generateTranscriptFileName(options.metadata);

    console.log('[TRANSCRIPT] Generated content and filename', {
      contentLength: content.length,
      fileName
    });

    // Generate PDF via API endpoint
    options.onProgress?.('pdf', 'Generating PDF document...', 25);
    const pdfResult = await generateTranscriptPDF(
      `Meeting Transcript - ${options.metadata.agentName}`,
      content,
      fileName,
      options.metadata.category || 'General',
      {
        agentName: options.metadata.agentName,
        agentType: options.metadata.agentType,
        documentTitle: options.metadata.documentTitle || '',
        startTime: options.metadata.startTime.toISOString(),
        endTime: options.metadata.endTime.toISOString(),
        totalMessages: options.metadata.totalMessages.toString(),
        userMessages: options.metadata.userMessages.toString(),
        agentMessages: options.metadata.agentMessages.toString()
      }
    );

    if (!pdfResult.success) {
      return {
        success: false,
        error: `Failed to generate PDF: ${pdfResult.error}`
      };
    }

    console.log('[TRANSCRIPT] PDF generated successfully', {
      fileUrl: pdfResult.fileUrl
    });

    let knowledgeBaseId: string | undefined;

    // Upload to knowledge base if requested and agent ID is available
    console.log('[TRANSCRIPT] Upload conditions check:', {
      uploadToKnowledgeBase: options.uploadToKnowledgeBase,
      agentId: options.agentId,
      hasFileUrl: !!pdfResult.fileUrl
    });

    if (options.uploadToKnowledgeBase && options.agentId && pdfResult.fileUrl) {
      try {
        options.onProgress?.('upload', 'Uploading to knowledge base...', 50);
        console.log('[TRANSCRIPT] Uploading to knowledge base', {
          agentId: options.agentId,
          fileName
        });

        // Check if already indexed
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
        if (apiKey) {
          // Use a simpler, more direct approach like the working create-agent route
          let actualAgentId = options.agentId;

          // If this is a user-generated ID, resolve the actual ElevenLabs agent ID
          if (options.agentId.includes('@') && options.agentId.includes('-pmo-')) {
            console.log('[TRANSCRIPT] Detected PMO agent ID, optimizing lookup...');

            try {
              // OPTIMIZATION: For PMO agents, skip database lookup and go directly to ElevenLabs
              // This eliminates the 1+ second delay from database retry logic since PMO agents
              // are consistently not stored in the database during creation
              console.log('[TRANSCRIPT] Searching ElevenLabs directly (optimized path)...');
              const { verifyAgentInElevenLabsByName } = await import('../elevenlabs/agentValidation');
              const elevenLabsResult = await verifyAgentInElevenLabsByName(options.agentId, apiKey);

              if (elevenLabsResult.exists && elevenLabsResult.agentData) {
                actualAgentId = elevenLabsResult.agentData.agent_id;
                console.log(`[TRANSCRIPT] Found agent in ElevenLabs: ${options.agentId} -> ${actualAgentId}`);

                // Store in database for future use to enable database lookup optimization later
                try {
                  const { storeUserAgent } = await import('../firebase/userAgents');
                  await storeUserAgent({
                    userId: options.userId,
                    agentId: actualAgentId,
                    agentName: options.agentId,
                    voiceId: elevenLabsResult.agentData.voice_id || 'default',
                    metadata: {
                      source: 'transcript-save-optimized',
                      originalUserGeneratedId: options.agentId,
                      optimizedLookup: true
                    }
                  });
                  console.log(`[TRANSCRIPT] Agent stored in database for future optimization`);
                } catch (storeError) {
                  console.warn(`[TRANSCRIPT] Could not store agent in database:`, storeError);
                  // Continue anyway - we have the agent ID
                }
              } else {
                console.warn(`[TRANSCRIPT] PMO agent not found in ElevenLabs: ${options.agentId}`);
                return {
                  success: true,
                  fileName,
                  pdfUrl: pdfResult.fileUrl,
                  error: 'PMO agent not found for knowledge base upload. Transcript saved as PDF only.'
                };
              }
            } catch (resolveError) {
              console.error('[TRANSCRIPT] Error resolving PMO agent ID:', resolveError);
              return {
                success: true,
                fileName,
                pdfUrl: pdfResult.fileUrl,
                error: 'Could not resolve PMO agent for knowledge base upload. Transcript saved as PDF only.'
              };
            }
          } else if (options.agentId.includes('@') || options.agentId.includes('-')) {
            // For non-PMO user-generated IDs, use traditional database lookup first
            console.log('[TRANSCRIPT] Detected non-PMO user-generated ID, checking database first...');
            try {
              const { getUserAgentByUserGeneratedId } = await import('../firebase/userAgents');
              const userAgent = await getUserAgentByUserGeneratedId(options.userId, options.agentId);

              if (userAgent) {
                actualAgentId = userAgent.agentId;
                console.log(`[TRANSCRIPT] Found non-PMO agent in database: ${options.agentId} -> ${actualAgentId}`);
              } else {
                console.log('[TRANSCRIPT] Non-PMO agent not in database, using provided ID as-is');
                // For non-PMO agents, the provided ID might already be the actual ElevenLabs ID
                actualAgentId = options.agentId;
              }
            } catch (dbError) {
              console.warn('[TRANSCRIPT] Database lookup failed for non-PMO agent, using provided ID:', dbError);
              actualAgentId = options.agentId;
            }
          }

          const isAlreadyIndexed = await isDocumentIndexedForAgent(
            actualAgentId,
            fileName,
            apiKey
          );

          if (!isAlreadyIndexed || options.forceUpload) {
            // Upload with deduplication
            options.onProgress?.('upload', 'Processing document upload...', 60);
            const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
              pdfResult.fileUrl,
              fileName,
              'application/pdf',
              apiKey,
              options.forceUpload
            );

            console.log('[TRANSCRIPT] Upload result', uploadResult);

            // Trigger RAG indexing
            options.onProgress?.('indexing', 'Starting RAG indexing for searchability...', 70);
            const ragResult = await computeRagIndex(
              uploadResult.id,
              apiKey,
              false
            );

            console.log('[TRANSCRIPT] RAG indexing result', ragResult);
            options.onProgress?.('indexing', 'RAG indexing completed successfully', 85);

            // Update agent's knowledge base association
            options.onProgress?.('agent-update', 'Updating agent knowledge base...', 90);
            try {
              await updateAgentKnowledgeBase(actualAgentId, uploadResult.id, apiKey);
              console.log('[TRANSCRIPT] Agent knowledge base updated successfully');
              options.onProgress?.('agent-update', 'Agent knowledge base updated successfully', 95);
            } catch (agentUpdateError) {
              console.warn('[TRANSCRIPT] Failed to update agent knowledge base:', agentUpdateError);
              options.onProgress?.('agent-update', 'Warning: Agent knowledge base update failed', 95);
              // Don't fail the operation if agent update fails
            }

            knowledgeBaseId = uploadResult.id;
          } else {
            console.log('[TRANSCRIPT] Document already indexed, skipping upload');
            options.onProgress?.('upload', 'Document already exists in knowledge base', 95);
          }
        }
      } catch (kbError) {
        console.error('[TRANSCRIPT] Knowledge base upload failed:', kbError);
        options.onProgress?.('error', `Knowledge base upload failed: ${kbError instanceof Error ? kbError.message : 'Unknown error'}`, 50);
        // Don't fail the entire operation if KB upload fails
      }
    }

    options.onProgress?.('complete', 'Transcript saved successfully!', 100);

    return {
      success: true,
      pdfUrl: pdfResult.fileUrl,
      fileName,
      knowledgeBaseId
    };

  } catch (error) {
    console.error('[TRANSCRIPT] Error saving transcript:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Create a summary of the transcript for quick reference
 */
export function createTranscriptSummary(
  dialogue: TranscriptMessage[],
  metadata: TranscriptMetadata
): string {
  const duration = Math.round((metadata.endTime.getTime() - metadata.startTime.getTime()) / 60000);
  
  return `Meeting with ${metadata.agentName} on ${metadata.startTime.toLocaleDateString()} ` +
         `(${duration} min, ${metadata.totalMessages} messages)`;
}
