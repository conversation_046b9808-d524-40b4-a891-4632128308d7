/**
 * DateTime Tool
 *
 * A simple tool that provides current date and time functionality for agents.
 * This tool allows agents to accurately timestamp their reports and analyses.
 */

/**
 * Interface for date formatting options
 */
export interface DateTimeFormatOptions {
  /** Format style: 'full', 'long', 'medium', 'short', or custom format string */
  format?: 'full' | 'long' | 'medium' | 'short' | string;
  /** Include time with the date */
  includeTime?: boolean;
  /** Use 24-hour format for time */
  use24HourFormat?: boolean;
  /** Include day of week */
  includeDayOfWeek?: boolean;
  /** Timezone to use (defaults to local timezone) */
  timeZone?: string;
}

/**
 * DateTime Tool class
 */
export class DateTimeTool {
  /**
   * Get the current date and time
   * @param options - Formatting options
   * @returns Formatted date and time string
   */
  getCurrentDateTime(options: DateTimeFormatOptions = {}): string {
    const now = new Date();
    const {
      format = 'medium',
      includeTime = true,
      use24HourFormat = false,
      includeDayOfWeek = false,
      timeZone
    } = options;

    // Handle predefined formats
    if (format === 'full' || format === 'long' || format === 'medium' || format === 'short') {
      const dateOptions: Intl.DateTimeFormatOptions = {
        dateStyle: format as Intl.DateTimeFormatOptions['dateStyle']
      };

      if (includeTime) {
        dateOptions.timeStyle = format as Intl.DateTimeFormatOptions['timeStyle'];
      }

      if (timeZone) {
        dateOptions.timeZone = timeZone;
      }

      return new Intl.DateTimeFormat('en-US', dateOptions).format(now);
    }

    // Handle custom format
    return this.formatCustomDateTime(now, {
      format,
      includeTime,
      use24HourFormat,
      includeDayOfWeek,
      timeZone
    });
  }

  /**
   * Format a date using a custom format string
   * @param date - The date to format
   * @param options - Formatting options
   * @returns Formatted date string
   */
  private formatCustomDateTime(date: Date, options: DateTimeFormatOptions): string {
    const {
      format,
      includeTime,
      use24HourFormat,
      includeDayOfWeek,
      timeZone
    } = options;

    // Apply timezone if specified
    let localDate = date;
    if (timeZone) {
      const dateString = date.toLocaleString('en-US', { timeZone });
      localDate = new Date(dateString);
    }

    // Get date components
    const day = localDate.getDate();
    const month = localDate.getMonth() + 1;
    const year = localDate.getFullYear();
    const hours = localDate.getHours();
    const minutes = localDate.getMinutes();
    const seconds = localDate.getSeconds();

    // Format date
    let result = format || 'MM/DD/YYYY'; // Provide a default format if none is specified

    // Replace date placeholders
    result = result.replace('YYYY', year.toString());
    result = result.replace('YY', (year % 100).toString().padStart(2, '0'));
    result = result.replace('MM', month.toString().padStart(2, '0'));
    result = result.replace('M', month.toString());
    result = result.replace('DD', day.toString().padStart(2, '0'));
    result = result.replace('D', day.toString());

    // Add day of week if requested
    if (includeDayOfWeek) {
      const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayOfWeek = daysOfWeek[localDate.getDay()];
      result = `${dayOfWeek}, ${result}`;
    }

    // Add time if requested
    if (includeTime) {
      let hoursFormatted = hours;
      let period = '';

      if (!use24HourFormat) {
        hoursFormatted = hours % 12;
        if (hoursFormatted === 0) hoursFormatted = 12;
        period = hours < 12 ? ' AM' : ' PM';
      }

      const timeString = `${hoursFormatted}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}${period}`;
      result = `${result} ${timeString}`;
    }

    return result;
  }

  /**
   * Get the current date
   * @param options - Formatting options
   * @returns Formatted date string
   */
  getCurrentDate(options: Omit<DateTimeFormatOptions, 'includeTime'> = {}): string {
    return this.getCurrentDateTime({ ...options, includeTime: false });
  }

  /**
   * Get the current time
   * @param options - Formatting options
   * @returns Formatted time string
   */
  getCurrentTime(options: Omit<DateTimeFormatOptions, 'includeDayOfWeek'> = {}): string {
    const now = new Date();
    const { use24HourFormat = false, timeZone } = options;

    const timeOptions: Intl.DateTimeFormatOptions = {
      timeStyle: 'medium',
      hour12: !use24HourFormat
    };

    if (timeZone) {
      timeOptions.timeZone = timeZone;
    }

    return new Intl.DateTimeFormat('en-US', timeOptions).format(now);
  }
}

// Export a singleton instance
export const dateTimeTool = new DateTimeTool();
