# CreateProjectAgent Implementation

## Overview

This implementation provides a complete solution for automatically creating projects and tasks from Strategic Director Agent outputs. The system uses Groq deepseek LLM to intelligently extract actionable projects and tasks from agent outputs stored in Firebase.

## Architecture

### Core Components

1. **CreateProjectAgent** (`lib/agents/createProjectAgent.ts`)
   - Extracts project recommendations from Agent_Output using Groq deepseek LLM
   - Creates projects in Firebase projects collection
   - Orchestrates the complete workflow
   - Updates PMO records with project IDs (array support)

2. **pmoProjectsTaskAgent** (`lib/agents/pmoProjectsTaskAgent.ts`)
   - Extracts specific tasks from Agent_Output using Groq deepseek LLM
   - Creates tasks in Firebase tasks collection
   - Assigns all tasks to ADMIN user (`<EMAIL>`) with HIGH priority
   - Links tasks to projects via projectId

3. **API Endpoint** (`app/api/create-projects-from-agent-output/route.ts`)
   - RESTful API for triggering project creation
   - Handles authentication and validation
   - Returns detailed creation results

## Key Features

### ✅ Requirements Met

- **PMO projectId Array Support**: PMO records can have multiple projects via `projectIds` array
- **Task Extraction from Agent_Output**: Uses Groq deepseek LLM to parse agent outputs
- **ADMIN User Assignment**: All tasks assigned to `<EMAIL>`
- **HIGH Priority**: All tasks created with HIGH priority
- **Firebase Integration**: Uses existing Firebase collections (projects, tasks, pmo_records)
- **Groq Consistency**: Uses `lib/tools/groq-ai` for LLM processing

### 🔄 Workflow

1. **Input**: Agent_Output document ID (requestId) and optional PMO ID
2. **Project Extraction**: Groq analyzes agent output for project recommendations
3. **Project Creation**: Creates projects in Firebase with proper metadata
4. **Task Extraction**: Groq extracts specific tasks from the same agent output
5. **Task Creation**: Creates tasks linked to projects with ADMIN assignment
6. **PMO Update**: Updates PMO record with new project IDs (appends to array)

## Usage

### API Endpoint

```typescript
POST /api/create-projects-from-agent-output

Body:
{
  "requestId": "agent_output_document_id",  // Required
  "pmoId": "pmo_record_id",                // Optional
  "userId": "<EMAIL>"             // Optional (defaults to authenticated user)
}

Response:
{
  "success": true,
  "message": "Successfully created 2 projects with 15 tasks",
  "data": {
    "projectsCreated": [
      {
        "projectId": "project_id_1",
        "projectName": "Marketing Campaign Project",
        "description": "Comprehensive marketing campaign...",
        "tasksCreated": 8
      }
    ],
    "totalProjects": 2,
    "totalTasksCreated": 15,
    "pmoUpdated": true,
    "analysis": "Extracted 2 actionable projects...",
    "requestId": "agent_output_document_id",
    "pmoId": "pmo_record_id",
    "userId": "<EMAIL>"
  }
}
```

### Programmatic Usage

```typescript
import { createProjectAgent } from 'lib/agents/createProjectAgent';

const result = await createProjectAgent.createProjectsFromAgentOutput(
  'agent_output_id',
  '<EMAIL>',
  'optional_pmo_id'
);

if (result.success) {
  console.log(`Created ${result.projectsCreated.length} projects`);
  console.log(`Created ${result.totalTasksCreated} tasks`);
}
```

## Data Flow

### Input: Agent_Output Document
```json
{
  "id": "agent_output_123",
  "agentType": "strategic-director",
  "category": "Marketing",
  "result": {
    "output": "Strategic recommendations...",
    "thinking": "Analysis process..."
  },
  "pmoMetadata": {
    "teamName": "Marketing",
    "category": "Strategic Planning"
  }
}
```

### Output: Projects Collection
```json
{
  "name": "Marketing Campaign Project",
  "description": "Comprehensive marketing campaign...",
  "startDate": "2025-01-15",
  "endDate": "2025-04-15",
  "owner": "<EMAIL>",
  "members": ["<EMAIL>", "<EMAIL>"],
  "categories": ["Marketing", "Design"],
  "status": "Active",
  "pmoId": "pmo_record_id",
  "generatedFromAgentOutput": true
}
```

### Output: Tasks Collection
```json
{
  "projectId": "project_id",
  "title": "Create brand messaging framework",
  "description": "Develop comprehensive brand messaging...",
  "category": "Marketing",
  "status": "Not Started",
  "assignedTo": ["<EMAIL>"],
  "priority": "High",
  "dueDate": "2025-01-22",
  "createdBy": "pmo-projects-task-agent"
}
```

### Output: Updated PMO Record
```json
{
  "projectIds": ["existing_project_1", "new_project_1", "new_project_2"],
  "generatedProjects": [
    {
      "id": "new_project_1",
      "name": "Marketing Campaign Project",
      "tasksCreated": 8,
      "createdAt": "2025-01-15T10:00:00Z"
    }
  ],
  "projectGenerationStatus": "completed",
  "projectGenerationDate": "2025-01-15T10:00:00Z"
}
```

## Configuration

### Environment Variables
```bash
GROQ_API_KEY=your_groq_api_key
FIREBASE_ADMIN_SDK_CONFIG=your_firebase_config
```

### Dependencies
- Groq SDK for LLM processing
- Firebase Admin SDK for database operations
- Zod for schema validation
- Next.js for API endpoints

## Testing

Run the test suite:
```bash
npm test lib/agents/tests/createProjectAgent.test.ts
```

The test suite includes:
- Project extraction from mock agent outputs
- Task creation with proper assignments
- API endpoint simulation
- Error handling scenarios

## Error Handling

The system includes comprehensive error handling:
- Invalid agent output formats
- Missing Firebase documents
- Groq API failures
- Authentication errors
- Validation errors

## Security

- Authentication required for all API calls
- User session validation
- Input sanitization and validation
- Firebase security rules enforcement

## Performance

- Batch operations for multiple projects/tasks
- Efficient Firebase queries
- Groq API optimization with appropriate timeouts
- Streaming support for real-time updates

## Future Enhancements

1. **Streaming Support**: Real-time progress updates during creation
2. **Batch Processing**: Handle multiple agent outputs simultaneously
3. **Template System**: Predefined project/task templates
4. **Analytics**: Track creation success rates and performance metrics
5. **Webhooks**: Notify external systems of project creation events

## Troubleshooting

### Common Issues

1. **"Agent output not found"**: Verify requestId exists in Agent_Output collection
2. **"No projects extracted"**: Check agent output quality and content
3. **"Task creation failed"**: Verify Firebase permissions and task schema
4. **"PMO update failed"**: Check PMO record exists and is accessible

### Debug Mode

Enable detailed logging:
```typescript
const agent = new CreateProjectAgent({
  includeExplanation: true,
  streamResponse: true,
  onStreamUpdate: (update) => console.log(update)
});
```

## Support

For issues or questions:
1. Check the test suite for usage examples
2. Review Firebase console for data integrity
3. Verify Groq API key and quotas
4. Check application logs for detailed error messages
