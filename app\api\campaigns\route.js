import { NextResponse } from "next/server";
import { campaignManager, createMarketingCampaign } from "lib/campaign-manager";

/**
 * API route for campaign management
 */
export async function POST(request) {
  try {
    const { action, campaignId, campaignData, userId, documentIds } = await request.json();
    
    let result;
    
    // Handle different actions
    switch (action) {
      case "createCampaign":
        if (!campaignData || !userId) {
          return NextResponse.json({ 
            error: "Campaign data and user ID are required" 
          }, { status: 400 });
        }
        
        if (documentIds && documentIds.length > 0) {
          // Create a full marketing campaign with documents
          result = await createMarketingCampaign(
            campaignData,
            documentIds,
            userId
          );
        } else {
          // Create just the campaign without documents
          result = await campaignManager.createCampaign(
            campaignData,
            userId
          );
        }
        break;
        
      case "getCampaign":
        if (!campaignId) {
          return NextResponse.json({ 
            error: "Campaign ID is required" 
          }, { status: 400 });
        }
        
        result = await campaignManager.getCampaign(campaignId);
        break;
        
      case "getUserCampaigns":
        if (!userId) {
          return NextResponse.json({ 
            error: "User ID is required" 
          }, { status: 400 });
        }
        
        result = await campaignManager.getUserCampaigns(userId);
        break;
        
      case "deleteCampaign":
        if (!campaignId) {
          return NextResponse.json({ 
            error: "Campaign ID is required" 
          }, { status: 400 });
        }
        
        result = await campaignManager.deleteCampaign(campaignId);
        break;
        
      default:
        return NextResponse.json({ 
          error: "Invalid action. Supported actions: createCampaign, getCampaign, getUserCampaigns, deleteCampaign" 
        }, { status: 400 });
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in campaigns API:", error);
    return NextResponse.json({ 
      success: false,
      error: error.message || "An error occurred while processing campaign data"
    }, { status: 500 });
  }
}
