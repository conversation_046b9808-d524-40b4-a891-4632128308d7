import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  doc,
  setDoc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Collection references
const usersCollection = collection(db, 'users');
const projectsCollection = collection(db, 'projects');
const tasksCollection = collection(db, 'tasks');

// User functions
export const getUsers = async () => {
  const snapshot = await getDocs(usersCollection);
  return snapshot.docs.map(doc => {
    const data = doc.data();

    // Ensure all required User fields are present
    return {
      id: doc.id,
      name: data.name || 'Unknown User',
      email: data.email || '<EMAIL>',
      role: data.role || 'user',
      avatar: data.avatar || '/avatars/default.png',
      availability: data.availability || 'Full-time',
      isAuthorized: data.isAuthorized !== undefined ? data.isAuthorized : false,
      ...data
    };
  });
};

export const getUserById = async (userId) => {
  const docRef = doc(usersCollection, userId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    const data = docSnap.data();

    // Check if the document has all the required fields for a User
    if (!data.name || !data.email || !data.role || !data.avatar || !data.availability) {
      console.warn(`User document ${userId} is missing required fields`);

      // Create a default user object with the required fields
      return {
        id: docSnap.id,
        name: data.name || 'Unknown User',
        email: data.email || '<EMAIL>',
        role: data.role || 'user',
        avatar: data.avatar || '/avatars/default.png',
        availability: data.availability || 'Full-time',
        isAuthorized: data.isAuthorized !== undefined ? data.isAuthorized : false,
        ...data
      };
    }

    return {
      id: docSnap.id,
      ...data
    };
  } else {
    return null;
  }
};

export const addUser = async (userData) => {
  return await addDoc(usersCollection, {
    ...userData,
    createdAt: new Date()
  });
};

export const updateUser = async (userId, userData) => {
  const userRef = doc(usersCollection, userId);
  return await updateDoc(userRef, {
    ...userData,
    updatedAt: new Date()
  });
};

// Project functions
export const getProjects = async (userEmail = null) => {
  // If no user email is provided, return an empty array
  if (!userEmail) {
    console.log('No user email provided, returning empty projects array');
    return [];
  }

  // System admin can see all projects
  const isSystemAdmin = userEmail === '<EMAIL>';

  // Get all projects
  const projectsQuery = query(
    projectsCollection,
    orderBy('createdAt', 'desc')
  );

  const snapshot = await getDocs(projectsQuery);
  let projects = snapshot.docs.map(doc => {
    const data = doc.data();

    // Convert Firestore timestamps to Date objects
    const startDate = data.startDate instanceof Timestamp ? data.startDate.toDate() : new Date(data.startDate || Date.now());
    const endDate = data.endDate instanceof Timestamp ? data.endDate.toDate() : new Date(data.endDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const createdAt = data.createdAt instanceof Timestamp ? data.createdAt.toDate() : data.createdAt;
    const updatedAt = data.updatedAt instanceof Timestamp ? data.updatedAt.toDate() : data.updatedAt;

    // Ensure all required Project fields are present
    return {
      id: doc.id,
      name: data.name || 'Untitled Project',
      description: data.description || '',
      startDate,
      endDate,
      owner: data.owner || '',
      members: Array.isArray(data.members) ? data.members : [],
      categories: Array.isArray(data.categories) ? data.categories : [],
      status: data.status || 'Active',
      createdAt,
      updatedAt
    };
  });

  // If user is system admin, return all projects
  if (isSystemAdmin) {
    return projects;
  }

  // Otherwise, filter projects based on user access
  // First get the user's ID
  const userQuery = query(usersCollection, where('email', '==', userEmail));
  const userSnapshot = await getDocs(userQuery);

  if (userSnapshot.empty) {
    return [];
  }

  const userId = userSnapshot.docs[0].id;

  // Filter projects where user is owner or member
  return projects.filter(project => {
    const isOwner = project.owner === userEmail;
    const isMember = project.members && Array.isArray(project.members) && project.members.includes(userId);

    return isOwner || isMember;
  });
};

export const getProjectById = async (projectId) => {
  const docRef = doc(projectsCollection, projectId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...docSnap.data()
    };
  } else {
    return null;
  }
};

export const addProject = async (projectData) => {
  return await addDoc(projectsCollection, {
    ...projectData,
    createdAt: new Date()
  });
};

export const updateProject = async (projectId, projectData) => {
  const projectRef = doc(projectsCollection, projectId);
  return await updateDoc(projectRef, {
    ...projectData,
    updatedAt: new Date()
  });
};

export const deleteProject = async (projectId) => {
  // First, delete all tasks associated with this project
  const tasksQuery = query(tasksCollection, where('projectId', '==', projectId));
  const taskSnapshot = await getDocs(tasksQuery);

  const deletePromises = taskSnapshot.docs.map(taskDoc =>
    deleteDoc(doc(tasksCollection, taskDoc.id))
  );

  await Promise.all(deletePromises);

  // Then delete the project
  const projectRef = doc(projectsCollection, projectId);
  return await deleteDoc(projectRef);
};

// Task functions
export const getTasks = async (projectId = null) => {
  let tasksQuery;

  if (projectId) {
    tasksQuery = query(
      tasksCollection,
      where('projectId', '==', projectId),
      orderBy('createdAt', 'desc')
    );
  } else {
    tasksQuery = query(
      tasksCollection,
      orderBy('createdAt', 'desc')
    );
  }

  const snapshot = await getDocs(tasksQuery);
  return snapshot.docs.map(doc => {
    const data = doc.data();

    // Convert Firestore timestamps to Date objects
    const startDate = data.startDate instanceof Timestamp ? data.startDate.toDate() : new Date(data.startDate || Date.now());
    const dueDate = data.dueDate instanceof Timestamp ? data.dueDate.toDate() : new Date(data.dueDate || Date.now() + 7 * 24 * 60 * 60 * 1000);
    const createdAt = data.createdAt instanceof Timestamp ? data.createdAt.toDate() : data.createdAt;
    const updatedAt = data.updatedAt instanceof Timestamp ? data.updatedAt.toDate() : data.updatedAt;

    // Ensure all required Task fields are present
    return {
      id: doc.id,
      projectId: data.projectId || '',
      title: data.title || 'Untitled Task',
      description: data.description || '',
      category: data.category || 'General',
      status: data.status || 'Not Started',
      startDate,
      dueDate,
      assignedTo: Array.isArray(data.assignedTo) ? data.assignedTo : [],
      priority: data.priority || 'Medium',
      dependencies: Array.isArray(data.dependencies) ? data.dependencies : [],
      notes: data.notes || '',
      attachments: Array.isArray(data.attachments) ? data.attachments : [],
      createdBy: data.createdBy || '',
      createdAt,
      updatedAt
    };
  });
};

export const getTaskById = async (taskId) => {
  const docRef = doc(tasksCollection, taskId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...docSnap.data()
    };
  } else {
    return null;
  }
};

export const addTask = async (taskData) => {
  return await addDoc(tasksCollection, {
    ...taskData,
    createdAt: new Date()
  });
};

export const updateTask = async (taskId, taskData) => {
  const taskRef = doc(tasksCollection, taskId);
  return await updateDoc(taskRef, {
    ...taskData,
    updatedAt: new Date()
  });
};

export const deleteTask = async (taskId) => {
  const taskRef = doc(tasksCollection, taskId);
  return await deleteDoc(taskRef);
};

// Authentication
export const loginUser = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    throw error;
  }
};

export const getCurrentUser = () => {
  return auth.currentUser;
};

export const signOut = async () => {
  return await auth.signOut();
};

// Initialize with system admin user
export const initializeUsers = async () => {
  // Check if users already exist
  const snapshot = await getDocs(usersCollection);
  if (snapshot.docs.length === 0) {
    // Add admin user
    await setDoc(doc(usersCollection, 'admin'), {
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      avatar: '/avatars/admin.png',
      availability: 'Full-time',
      isAuthorized: true,
      createdAt: new Date()
    });
  } else {
    // Make sure the system admin is always authorized
    const adminQuery = query(usersCollection, where('email', '==', '<EMAIL>'));
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      // Create the admin user if it doesn't exist
      await addDoc(usersCollection, {
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        avatar: '/avatars/admin.png',
        availability: 'Full-time',
        isAuthorized: true,
        createdAt: new Date()
      });
    } else {
      // Update the admin user to ensure it's authorized
      const adminDoc = adminSnapshot.docs[0];
      await updateDoc(doc(usersCollection, adminDoc.id), {
        role: 'admin',
        isAuthorized: true
      });
    }
  }
};

// User access management functions
export const checkIsSystemAdmin = async (email) => {
  // Only <EMAIL> is the system admin
  return email === '<EMAIL>';
};

export const addUserAccess = async (email) => {
  // Check if user already exists
  const userQuery = query(usersCollection, where('email', '==', email));
  const userSnapshot = await getDocs(userQuery);

  if (!userSnapshot.empty) {
    // User exists, update to grant access
    const userDoc = userSnapshot.docs[0];
    await updateDoc(doc(usersCollection, userDoc.id), {
      isAuthorized: true,
      updatedAt: new Date()
    });
    return userDoc.id;
  } else {
    // Create new user with access
    const newUser = {
      name: email.split('@')[0], // Use part of email as name
      email,
      role: 'user',
      avatar: '/avatars/default.png',
      availability: 'Full-time',
      isAuthorized: true,
      createdAt: new Date()
    };

    const docRef = await addDoc(usersCollection, newUser);
    return docRef.id;
  }
};

export const removeUserAccess = async (userId) => {
  // Don't allow removing the system admin
  const userDoc = await getDoc(doc(usersCollection, userId));
  if (userDoc.exists() && userDoc.data().email === '<EMAIL>') {
    throw new Error('Cannot remove access from the system administrator');
  }

  // Update user to revoke access
  await updateDoc(doc(usersCollection, userId), {
    isAuthorized: false,
    updatedAt: new Date()
  });

  return userId;
};

export const checkUserAuthorized = async (email) => {
  // System admin is always authorized
  if (email === '<EMAIL>') {
    return true;
  }

  // Check if user exists and is authorized
  const userQuery = query(usersCollection, where('email', '==', email));
  const userSnapshot = await getDocs(userQuery);

  if (userSnapshot.empty) {
    return false;
  }

  const userData = userSnapshot.docs[0].data();
  return userData.isAuthorized === true;
};

export { db, auth };
