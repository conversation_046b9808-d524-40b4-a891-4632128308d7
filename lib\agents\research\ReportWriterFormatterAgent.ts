// research/ReportWriterFormatterAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { SynthesizedFindings, FormattingGuidelines, ReportDraft } from './ResearchInterfaces';

export class ReportWriterFormatterAgent extends ResearchAgent {
    constructor(
        id: string = 'report-writer',
        name: string = 'Report Writer & Formatter',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'o3-2025-04-16'
    ) {
        const role = 'Report Writing and Formatting Specialist';
        const description = `I take synthesized research findings and craft well-structured, clearly written reports, summaries, or presentations according to specified formatting guidelines.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Draft a report based on synthesized findings and guidelines
     * @param findings - The synthesized information
     * @param guidelines - Formatting and structure requirements
     * @param parentTaskId - The overall research task ID
     * @returns The drafted report
     */
    async draftReport(
        findings: SynthesizedFindings,
        guidelines: FormattingGuidelines,
        parentTaskId: string
    ): Promise<ReportDraft> {
        console.log(`[${this.name}] Drafting ${guidelines.format} for task ${parentTaskId}`);

        const reportTitle = `Research ${guidelines.format}: Findings on Sub-Task ${findings.subTaskId}`; // Example title

        // Prepare context for LLM generation
        const findingsContext = `
          Synthesized Summary: ${findings.summary}
          Key Themes: ${findings.keyThemes.join(', ')}
          Confidence Level: ${findings.confidenceLevel}
          Key Analyzed Sources Count: ${findings.analyzedSources.length}
          ${findings.contradictions?.length ? `Noted Contradictions: ${findings.contradictions.join('; ')}` : ''}
          ${findings.gaps?.length ? `Noted Gaps: ${findings.gaps.join('; ')}` : ''}
        `;
        // Maybe include top findings from analyzedSources if needed

        const formattingInstructions = `
          Required Format: ${guidelines.format}
          ${guidelines.styleGuide ? `Citation Style: ${guidelines.styleGuide}` : ''}
          ${guidelines.length ? `Approximate Length: ${guidelines.length}` : ''}
          ${guidelines.sections ? `Required Sections: ${guidelines.sections.join(', ')}` : ''}
        `;

        const prompt = `
          Based on the following synthesized research findings and formatting guidelines, write the research output.
          Ensure the language is clear, objective, and professional.
          Structure the output logically according to the required format and sections.
          If drafting a report, include an introduction, body sections based on themes, and a conclusion.
          Reference the synthesized findings accurately.
          If a citation style is specified, make placeholders or attempt basic formatting for a bibliography section based on the source URLs available in the analysis (list URLs).

          Synthesized Findings:
          ${findingsContext}

          Formatting Guidelines:
          ${formattingInstructions}

          Generate the full content for the ${guidelines.format}. Output only the content itself.
        `;

        const reportContent = await this.processRequest(prompt);

        // TODO: Implement more robust citation/bibliography generation based on findings.analyzedSources
        const bibliography = findings.analyzedSources.map(s => s.sourceUrl);

        const draft: ReportDraft = {
            draftId: `draft-${findings.synthesisId}-${Date.now()}`,
            synthesisId: findings.synthesisId,
            parentTaskId,
            title: reportTitle,
            content: reportContent, // Assume LLM returns markdown or plain text
            bibliography: bibliography, // Basic list for now
            version: 1,
        };

        console.log(`[${this.name}] Draft ${draft.draftId} created.`);
        return draft;
    }

    /**
     * Enhanced professional writing and formatting delegated from ResearchAgentManager
     */
    async createProfessionalDocument(taskData: {
        instruction: string;
        content: any;
        documentType: string;
        formattingRequirements: string[];
        audienceProfile: string;
        context: any;
    }): Promise<any> {
        console.log(`[${this.name}] Creating professional document: ${taskData.documentType}`);

        try {
            const documentCreationPrompt = `
            You are a Report Writer & Formatter creating professional documentation for a research project.

            DELEGATION CONTEXT:
            You have been delegated this task by the ResearchAgentManager as part of a coordinated research team effort.

            DOCUMENT CREATION REQUIREMENTS:
            Task: ${taskData.instruction}
            Document Type: ${taskData.documentType}
            Formatting Requirements: ${taskData.formattingRequirements.join(', ')}
            Target Audience: ${taskData.audienceProfile}

            CONTENT TO FORMAT:
            ${JSON.stringify(taskData.content, null, 2)}

            PROJECT CONTEXT:
            ${JSON.stringify(taskData.context, null, 2)}

            YOUR SPECIALIZED EXPERTISE:
            As the Report Writer & Formatter, you excel at:
            - Professional document structuring and organization
            - Clear, compelling writing that engages target audiences
            - Advanced formatting and presentation techniques
            - Citation management and bibliography creation
            - Visual content integration and layout optimization

            Return a comprehensive JSON response with:
            {
              "professionalDocument": "complete formatted document content",
              "documentStructure": ["list of main sections and organization"],
              "writingQuality": ["list of writing excellence features"],
              "formattingFeatures": ["list of formatting and presentation elements"],
              "qualityIndicators": ["list of quality assurance markers"],
              "teamCoordination": ["list of coordination elements"],
              "audienceAlignment": ["list of audience-specific adaptations"],
              "citationManagement": ["list of citation and reference elements"],
              "visualIntegration": ["list of visual content integration"],
              "deliveryReadiness": "assessment of document readiness for delivery"
            }

            Focus on leveraging your writing and formatting expertise to create a professional, compelling document.
            `;

            const response = await this.processRequest(documentCreationPrompt);
            const documentResult = JSON.parse(response);

            console.log(`[${this.name}] Professional document created successfully`);
            return {
                success: true,
                documentResult,
                specialistAgent: this.name,
                delegatedBy: 'ResearchAgentManager',
                completedAt: new Date()
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error in document creation:`, error);
            return {
                success: false,
                error: error.message,
                fallbackDocument: {
                    professionalDocument: 'Basic document structure with standard formatting',
                    documentStructure: ['Executive Summary', 'Main Content', 'Conclusions', 'References'],
                    writingQuality: ['Clear language', 'Logical flow', 'Professional tone'],
                    formattingFeatures: ['Standard headings', 'Bullet points', 'Basic citations'],
                    qualityIndicators: ['Grammar checked', 'Spell checked', 'Format consistent'],
                    teamCoordination: ['Ready for review', 'Sources documented'],
                    audienceAlignment: ['Professional language', 'Appropriate detail level'],
                    citationManagement: ['Basic citations', 'Reference list'],
                    visualIntegration: ['Tables formatted', 'Charts referenced'],
                    deliveryReadiness: 'Ready for quality review'
                }
            };
        }
    }

    /**
     * Enhanced task handling with comprehensive LLM processing
     * Handles incoming tasks/messages for report writing with role-specific expertise
     */
    async handleTask(messageContent: string, metadata: Record<string, any>): Promise<void> {
        if (metadata.subTaskId && metadata.instruction && metadata.synthesis && metadata.formattingGuidelines) {
            console.log(`[${this.name}] Received task: ${metadata.subTaskId} - ${metadata.instruction}`);
            const findings: SynthesizedFindings = metadata.synthesis;
            const guidelines: FormattingGuidelines = metadata.formattingGuidelines;
            const parentTaskId = metadata.parentTaskId || 'unknown_parent';

            const draft = await this.draftReport(findings, guidelines, parentTaskId);

            // Send draft back to Lead or directly to QA?
            const leadAgentId = 'research-lead'; // Placeholder
            await this.sendMessage(
                leadAgentId,
                `Draft ready for review for sub-task ${metadata.subTaskId}`,
                {
                    type: 'draft_ready',
                    subTaskId: metadata.subTaskId,
                    draft: draft // Attach draft
                }
            );
             await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

        } else {
            console.warn(`[${this.name}] Received message missing required data for writing:`, metadata);
        }
    }
}