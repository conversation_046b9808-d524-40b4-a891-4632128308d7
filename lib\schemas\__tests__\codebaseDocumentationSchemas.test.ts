/**
 * Test suite for codebase documentation Zod schemas
 * Validates schema behavior, error handling, and type safety
 */

import {
  CodebaseAnalysisResultSchema,
  SubAgentAssignmentSchema,
  SubAgentResultSchema,
  DocumentationAssessmentSchema,
  DynamicSubAgentSchema,
  ChartRecommendationSchema,
  CodebaseDocumentationRequestSchema,
  PMOFormInputSchema,
  CodebaseDocumentationOrchestratorOptionsSchema,
  StreamUpdateSchema,
  TeamAssignmentSchema,
  validateCodebaseAnalysis,
  validateSubAgentAssignment,
  validateSubAgentResult,
  validateDocumentationRequest,
  validatePMOFormInput,
  safeValidateCodebaseAnalysis,
  safeValidateSubAgentAssignment,
  safeValidateSubAgentResult,
  safeValidateDocumentationRequest,
  safeValidatePMOFormInput,
  formatValidationErrors,
  getDefaultCodebaseAnalysis,
  getDefaultDocumentationRequest
} from '../codebaseDocumentationSchemas';

describe('Codebase Documentation Schemas', () => {
  
  describe('CodebaseAnalysisResultSchema', () => {
    it('should validate valid codebase analysis data', () => {
      const validData = {
        totalFiles: 100,
        totalLines: 5000,
        languages: ['TypeScript', 'JavaScript'],
        complexity: 'medium' as const,
        mainDirectories: ['src', 'lib'],
        keyFiles: ['package.json', 'tsconfig.json'],
        frameworks: ['React', 'Next.js'],
        dependencies: ['react', 'next', 'zod']
      };

      const result = CodebaseAnalysisResultSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should apply default values for optional fields', () => {
      const minimalData = {
        totalFiles: 10,
        totalLines: 500,
        complexity: 'low' as const
      };

      const result = CodebaseAnalysisResultSchema.parse(minimalData);
      expect(result.languages).toEqual([]);
      expect(result.mainDirectories).toEqual([]);
      expect(result.keyFiles).toEqual([]);
      expect(result.frameworks).toEqual([]);
      expect(result.dependencies).toEqual([]);
    });

    it('should reject negative numbers', () => {
      const invalidData = {
        totalFiles: -1,
        totalLines: 500,
        complexity: 'low' as const
      };

      expect(() => CodebaseAnalysisResultSchema.parse(invalidData)).toThrow();
    });
  });

  describe('SubAgentAssignmentSchema', () => {
    it('should validate valid sub-agent assignment', () => {
      const validData = {
        agentId: 'test-agent-1',
        agentName: 'Test Agent',
        assignment: 'Analyze the codebase structure and provide documentation',
        priority: 'high' as const,
        estimatedComplexity: 'moderate' as const,
        requiredPaths: ['/src', '/lib'],
        specialization: 'Code Analysis'
      };

      const result = SubAgentAssignmentSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should reject assignment with short description', () => {
      const invalidData = {
        agentId: 'test-agent-1',
        agentName: 'Test Agent',
        assignment: 'Short',
        priority: 'high' as const,
        estimatedComplexity: 'moderate' as const,
        requiredPaths: ['/src'],
        specialization: 'Code Analysis'
      };

      expect(() => SubAgentAssignmentSchema.parse(invalidData)).toThrow();
    });
  });

  describe('CodebaseDocumentationRequestSchema', () => {
    it('should validate valid documentation request', () => {
      const validData = {
        userId: 'user123',
        selectedPaths: ['/src/components', '/src/utils'],
        description: 'Generate comprehensive documentation for the React components',
        customContext: 'Focus on component props and usage examples',
        documentationScope: 'partial' as const,
        outputFormat: 'markdown' as const,
        includeArchitecture: true,
        includeApiDocs: false,
        includeDataFlow: true
      };

      const result = CodebaseDocumentationRequestSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should apply default values', () => {
      const minimalData = {
        userId: 'user123',
        selectedPaths: ['/src'],
        description: 'Generate documentation for the source code'
      };

      const result = CodebaseDocumentationRequestSchema.parse(minimalData);
      expect(result.documentationScope).toBe('full');
      expect(result.outputFormat).toBe('markdown');
      expect(result.includeArchitecture).toBe(true);
      expect(result.includeApiDocs).toBe(true);
      expect(result.includeDataFlow).toBe(true);
    });

    it('should reject empty paths array', () => {
      const invalidData = {
        userId: 'user123',
        selectedPaths: [],
        description: 'Generate documentation'
      };

      expect(() => CodebaseDocumentationRequestSchema.parse(invalidData)).toThrow();
    });

    it('should reject short description', () => {
      const invalidData = {
        userId: 'user123',
        selectedPaths: ['/src'],
        description: 'Short'
      };

      expect(() => CodebaseDocumentationRequestSchema.parse(invalidData)).toThrow();
    });
  });

  describe('PMOFormInputSchema', () => {
    it('should validate valid PMO form input', () => {
      const validData = {
        title: 'Codebase Documentation Project',
        description: 'Comprehensive documentation generation for the main application',
        priority: 'High' as const,
        category: 'Documentation',
        sourceFile: 'Selected paths: /src, /lib',
        fileName: 'Documentation-2024-01-15',
        customContext: 'Focus on user-facing features',
        pmoAssessment: 'This project aims to create comprehensive documentation covering all major components and features of the application. The documentation will help new developers understand the codebase structure and facilitate maintenance.'
      };

      const result = PMOFormInputSchema.parse(validData);
      expect(result).toEqual(validData);
    });

    it('should apply default values', () => {
      const minimalData = {
        title: 'Test Project',
        description: 'Test description for the project',
        pmoAssessment: 'This is a comprehensive assessment that meets the minimum length requirement for PMO evaluation and review.'
      };

      const result = PMOFormInputSchema.parse(minimalData);
      expect(result.priority).toBe('Medium');
      expect(result.category).toBe('Documentation');
    });

    it('should reject short PMO assessment', () => {
      const invalidData = {
        title: 'Test Project',
        description: 'Test description',
        pmoAssessment: 'Too short'
      };

      expect(() => PMOFormInputSchema.parse(invalidData)).toThrow();
    });
  });

  describe('Validation utility functions', () => {
    it('should validate using utility functions', () => {
      const validAnalysis = {
        totalFiles: 50,
        totalLines: 2500,
        complexity: 'low' as const
      };

      const result = validateCodebaseAnalysis(validAnalysis);
      expect(result.totalFiles).toBe(50);
      expect(result.languages).toEqual([]);
    });

    it('should handle safe validation with success', () => {
      const validData = {
        userId: 'user123',
        selectedPaths: ['/src'],
        description: 'Generate documentation for the application'
      };

      const result = safeValidateDocumentationRequest(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.userId).toBe('user123');
      }
    });

    it('should handle safe validation with failure', () => {
      const invalidData = {
        userId: '',
        selectedPaths: [],
        description: 'Short'
      };

      const result = safeValidateDocumentationRequest(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.errors.length).toBeGreaterThan(0);
      }
    });

    it('should format validation errors correctly', () => {
      const invalidData = {
        userId: '',
        selectedPaths: [],
        description: 'Short'
      };

      const result = safeValidateDocumentationRequest(invalidData);
      if (!result.success) {
        const formattedErrors = formatValidationErrors(result.error);
        expect(formattedErrors).toBeInstanceOf(Array);
        expect(formattedErrors.length).toBeGreaterThan(0);
        expect(formattedErrors[0]).toHaveProperty('field');
        expect(formattedErrors[0]).toHaveProperty('message');
        expect(formattedErrors[0]).toHaveProperty('code');
      }
    });
  });

  describe('Default value functions', () => {
    it('should return default codebase analysis', () => {
      const defaultAnalysis = getDefaultCodebaseAnalysis();
      expect(defaultAnalysis.totalFiles).toBe(0);
      expect(defaultAnalysis.totalLines).toBe(0);
      expect(defaultAnalysis.complexity).toBe('low');
      expect(defaultAnalysis.languages).toEqual([]);
    });

    it('should return default documentation request', () => {
      const defaultRequest = getDefaultDocumentationRequest(
        'user123',
        ['/src'],
        'Generate documentation'
      );
      expect(defaultRequest.userId).toBe('user123');
      expect(defaultRequest.selectedPaths).toEqual(['/src']);
      expect(defaultRequest.documentationScope).toBe('full');
      expect(defaultRequest.outputFormat).toBe('markdown');
    });
  });

  describe('StreamUpdateSchema', () => {
    it('should validate stream update data', () => {
      const validData = {
        stage: 'processing',
        message: 'Analyzing codebase structure',
        progress: 45,
        data: { currentFile: 'src/index.ts' },
        error: undefined
      };

      const result = StreamUpdateSchema.parse(validData);
      expect(result).toEqual(validData);
    });
  });

  describe('TeamAssignmentSchema', () => {
    it('should validate team assignment data', () => {
      const validData = {
        teamId: 'team-001',
        teamName: 'Development Team',
        rationale: 'This team has expertise in the technologies used in this project',
        capabilities: ['React Development', 'TypeScript', 'Documentation']
      };

      const result = TeamAssignmentSchema.parse(validData);
      expect(result).toEqual(validData);
    });
  });
});
