import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';
import { streamText } from "ai";
import { openai } from "@ai-sdk/openai";

// Define interfaces for SEO data structures
interface SeoMetadata {
  title: string;
  metaDescription: string | null;
  canonical?: string | null;
  robots?: string | null;
}

interface HeadingStructure {
  h1: string[];
  h2: string[];
  h3: string[];
  h4: string[];
  h5: string[];
  h6: string[];
}

interface ImageData {
  src?: string;
  alt: string;
  hasAlt: boolean;
}

interface LinkData {
  href: string;
  text: string;
  isExternal: boolean;
  hasText: boolean;
}

interface SeoData {
  title: string;
  metaDescription: string | null;
  headings: HeadingStructure;
  images: ImageData[];
  links: LinkData[];
}

interface PerformanceMetrics {
  loadTime: number;
  size: number;
  metrics: {
    firstContentfulPaint: number;
    domContentLoaded: number;
  };
}

interface ContentQuality {
  wordCount: number;
  paragraphCount: number;
  avgParagraphLength: number;
  readabilityScore: number;
  contentDepth: 'thin' | 'basic' | 'comprehensive' | 'in-depth';
}

interface KeywordOccurrences {
  title: number;
  metaDescription: number;
  h1: number;
  h2: number;
  content: number;
}

interface KeywordAnalysisItem {
  occurrences: KeywordOccurrences | number;
  density: number;
  optimization: 'excellent' | 'good' | 'fair' | 'poor' | 'optimal' | 'high' | 'excessive' | 'low';
}

interface KeywordAnalysis {
  [keyword: string]: KeywordAnalysisItem;
}

interface MobileFriendliness {
  isMobileFriendly: boolean;
  hasViewportMeta?: boolean;
  hasHorizontalScroll?: boolean;
  smallTapTargets?: number;
  smallFontElements?: number;
  error?: string;
}

interface SeoIssue {
  type: 'critical' | 'warning';
  category: 'metadata' | 'structure' | 'accessibility' | 'performance' | 'mobile' | 'content';
  message: string;
}

interface SeoRecommendation {
  recommendation: string;
}

interface SeoAnalysisResult {
  success: boolean;
  url?: string;
  metadata?: SeoMetadata;
  performance?: PerformanceMetrics;
  structure?: {
    headings: HeadingStructure;
    images: ImageData[];
    links: LinkData[];
  };
  contentQuality?: ContentQuality;
  keywordAnalysis?: KeywordAnalysis;
  mobileFriendliness?: MobileFriendliness;
  issues?: SeoIssue[];
  recommendations?: SeoRecommendation[];
  error?: string;
}

interface ContentAnalysisResult {
  success: boolean;
  contentStats?: {
    wordCount: number;
    characterCount: number;
    paragraphCount: number;
    readabilityScore: number;
  };
  contentQuality?: ContentQuality;
  keywordAnalysis?: KeywordAnalysis;
  recommendations?: string[];
  error?: string;
}

/**
 * SEO Analyzer service for analyzing and optimizing content for search engines
 */
class SeoAnalyzer {
  /**
   * Analyze a URL for SEO performance
   * @param {string} url - The URL to analyze
   * @returns {Promise<Object>} - SEO analysis results
   */
  async analyzeUrl(url: string): Promise<SeoAnalysisResult> {
    try {
      // Launch browser
      const browser = await puppeteer.launch({
        headless: true,
        args: ["--no-sandbox", "--disable-setuid-sandbox"],
      });

      try {
        // Open page
        const page = await browser.newPage();

        // Set user agent
        await page.setUserAgent(
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        );

        // Navigate to URL
        await page.goto(url, {
          waitUntil: "networkidle2",
          timeout: 30000,
        });

        // Get page HTML
        const html = await page.content();

        // Get page metadata
        const title = await page.title();
        const metaDescription = await page.$eval('meta[name="description"]', el => el.content).catch(() => null);

        // Get page performance metrics
        const performanceMetrics = await page.metrics();

        // Get page load timing
        // @ts-ignore - window.performance.timing is deprecated but still works
        const timing = JSON.parse(
          // @ts-ignore - window.performance.timing is deprecated but still works
          await page.evaluate(() => JSON.stringify(window.performance.timing))
        );

        // Parse HTML with Cheerio
        const $ = cheerio.load(html);

        // Extract SEO data
        const seoData = this._extractSeoData($, title, metaDescription);

        // Calculate page load time
        // @ts-ignore - timing is deprecated but still works
        const loadTime = timing.loadEventEnd - timing.navigationStart;

        // Check mobile friendliness
        const isMobileFriendly = await this._checkMobileFriendliness(page);

        // Analyze content quality
        const contentQuality = await this._analyzeContentQuality($);

        // Analyze keyword usage
        const keywordAnalysis = await this._analyzeKeywords($, title, metaDescription);

        return {
          success: true,
          url,
          metadata: {
            title,
            metaDescription,
            canonical: $('link[rel="canonical"]').attr('href') || null,
            robots: $('meta[name="robots"]').attr('content') || null,
          },
          performance: {
            loadTime: loadTime,
            size: html.length,
            metrics: {
              firstContentfulPaint: (performanceMetrics as any).FirstContentfulPaint || 0,
              domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
            },
          },
          structure: {
            headings: seoData.headings,
            images: seoData.images,
            links: seoData.links,
          },
          contentQuality,
          keywordAnalysis,
          mobileFriendliness: isMobileFriendly,
          issues: this._identifyIssues(seoData, loadTime, isMobileFriendly, contentQuality),
          recommendations: await this._generateRecommendations(
            seoData,
            loadTime,
            isMobileFriendly,
            contentQuality,
            keywordAnalysis,
            url
          ),
        };
      } finally {
        await browser.close();
      }
    } catch (error: unknown) {
      console.error('SEO analysis error:', error);
      return {
        success: false,
        url,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Analyze content for SEO optimization
   * @param {string} content - The content to analyze
   * @param {string} targetKeywords - Target keywords for the content
   * @returns {Promise<Object>} - Content analysis results
   */
  async analyzeContent(content: string, targetKeywords: string = ''): Promise<ContentAnalysisResult> {
    try {
      // Create a virtual HTML document
      const $ = cheerio.load(`<html><body>${content}</body></html>`);

      // Extract text content
      const textContent = $('body').text();

      // Analyze content quality
      const contentQuality = await this._analyzeContentQuality($);

      // Analyze keyword usage
      const keywordAnalysis = await this._analyzeKeywordsInText(textContent, targetKeywords);

      // Get AI-powered recommendations
      const recommendations = await this._getContentOptimizationRecommendations(
        textContent,
        targetKeywords,
        contentQuality,
        keywordAnalysis
      );

      return {
        success: true,
        contentStats: {
          wordCount: textContent.split(/\s+/).length,
          characterCount: textContent.length,
          paragraphCount: $('p').length,
          readabilityScore: this._calculateReadabilityScore(textContent),
        },
        contentQuality,
        keywordAnalysis,
        recommendations,
      };
    } catch (error: unknown) {
      console.error('Content analysis error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Extract SEO data from HTML
   * @param {Object} $ - Cheerio instance
   * @param {string} title - Page title
   * @param {string} metaDescription - Meta description
   * @returns {Object} - Extracted SEO data
   * @private
   */
  _extractSeoData($: cheerio.CheerioAPI, title: string, metaDescription: string | null): SeoData {
    // Extract headings
    const headings: HeadingStructure = {
      h1: [],
      h2: [],
      h3: [],
      h4: [],
      h5: [],
      h6: [],
    };

    $('h1, h2, h3, h4, h5, h6').each((_i, el) => {
      const tagName = $(el).prop('tagName')?.toLowerCase() as keyof HeadingStructure;
      if (tagName in headings) {
        headings[tagName].push($(el).text().trim());
      }
    });

    // Extract images
    const images: ImageData[] = [];
    $('img').each((_i, el) => {
      images.push({
        src: $(el).attr('src'),
        alt: $(el).attr('alt') || '',
        hasAlt: !!$(el).attr('alt'),
      });
    });

    // Extract links
    const links: LinkData[] = [];
    $('a').each((_i, el) => {
      const href = $(el).attr('href');
      if (href) {
        links.push({
          href,
          text: $(el).text().trim(),
          isExternal: href.startsWith('http') && !href.includes(window.location.hostname || ''),
          hasText: !!$(el).text().trim(),
        });
      }
    });

    return {
      title,
      metaDescription,
      headings,
      images,
      links,
    };
  }

  /**
   * Check if a page is mobile-friendly
   * @param {Object} page - Puppeteer page object
   * @returns {Promise<Object>} - Mobile friendliness assessment
   * @private
   */
  async _checkMobileFriendliness(page: any): Promise<MobileFriendliness> {
    try {
      // Set mobile viewport
      await page.setViewport({
        width: 375,
        height: 667,
        isMobile: true,
      });

      // Check for viewport meta tag
      const hasViewportMeta = await page.$('meta[name="viewport"]').then((res: any) => !!res);

      // Check for horizontal scrolling
      const hasHorizontalScroll = await page.evaluate(() => {
        return document.body.scrollWidth > window.innerWidth;
      });

      // Check tap target size
      const smallTapTargets = await page.evaluate(() => {
        const tappableElements = Array.from(document.querySelectorAll('a, button, input, select, textarea'));
        return tappableElements.filter(el => {
          const rect = el.getBoundingClientRect();
          return (rect.width < 48 || rect.height < 48);
        }).length;
      });

      // Check font size
      const smallFontElements = await page.evaluate(() => {
        const textElements = Array.from(document.querySelectorAll('p, span, div, a, button, li'));
        return textElements.filter(el => {
          const fontSize = window.getComputedStyle(el).fontSize;
          return parseFloat(fontSize) < 12;
        }).length;
      });

      return {
        isMobileFriendly: hasViewportMeta && !hasHorizontalScroll && smallTapTargets < 5 && smallFontElements < 5,
        hasViewportMeta,
        hasHorizontalScroll,
        smallTapTargets,
        smallFontElements,
      };
    } catch (error: unknown) {
      console.error('Mobile friendliness check error:', error);
      return {
        isMobileFriendly: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Analyze content quality
   * @param {Object} $ - Cheerio instance
   * @returns {Promise<Object>} - Content quality assessment
   * @private
   */
  async _analyzeContentQuality($: cheerio.CheerioAPI): Promise<ContentQuality> {
    // Extract text content
    const textContent = $('body').text();

    // Calculate word count
    const wordCount = textContent.split(/\s+/).filter(Boolean).length;

    // Calculate paragraph count
    const paragraphCount = $('p').length;

    // Calculate average paragraph length
    const paragraphLengths = $('p').map((_i, el) => $(el).text().split(/\s+/).filter(Boolean).length).get();
    const avgParagraphLength = paragraphLengths.length > 0
      ? paragraphLengths.reduce((sum, len) => sum + len, 0) / paragraphLengths.length
      : 0;

    // Calculate readability score
    const readabilityScore = this._calculateReadabilityScore(textContent);

    return {
      wordCount,
      paragraphCount,
      avgParagraphLength,
      readabilityScore,
      contentDepth: this._assessContentDepth(wordCount),
    };
  }

  /**
   * Calculate readability score (Flesch-Kincaid)
   * @param {string} text - Text content
   * @returns {number} - Readability score
   * @private
   */
  _calculateReadabilityScore(text: string): number {
    // Simple implementation of Flesch-Kincaid readability test
    const sentences = text.split(/[.!?]+/).filter(Boolean);
    const words = text.split(/\s+/).filter(Boolean);
    const syllables = this._countSyllables(text);

    if (sentences.length === 0 || words.length === 0) {
      return 0;
    }

    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;

    // Flesch-Kincaid formula
    return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);
  }

  /**
   * Count syllables in text (approximate)
   * @param {string} text - Text content
   * @returns {number} - Syllable count
   * @private
   */
  _countSyllables(text: string): number {
    // Simple syllable counting heuristic
    const words = text.toLowerCase().split(/\s+/).filter(Boolean);
    let count = 0;

    for (const word of words) {
      // Count vowel groups as syllables
      const syllables = word.replace(/(?:[^laeiouy]|ed|[^laeiouy]e)$/, '')
        .replace(/^y/, '')
        .match(/[aeiouy]{1,2}/g);

      // Add syllable count or minimum 1 syllable per word
      count += syllables ? syllables.length : 1;
    }

    return count;
  }

  /**
   * Assess content depth based on word count
   * @param {number} wordCount - Word count
   * @returns {string} - Content depth assessment
   * @private
   */
  _assessContentDepth(wordCount: number): 'thin' | 'basic' | 'comprehensive' | 'in-depth' {
    if (wordCount < 300) {
      return 'thin';
    } else if (wordCount < 600) {
      return 'basic';
    } else if (wordCount < 1200) {
      return 'comprehensive';
    } else {
      return 'in-depth';
    }
  }

  /**
   * Analyze keyword usage in HTML
   * @param {Object} $ - Cheerio instance
   * @param {string} title - Page title
   * @param {string} metaDescription - Meta description
   * @returns {Promise<Object>} - Keyword analysis
   * @private
   */
  async _analyzeKeywords($: cheerio.CheerioAPI, title: string, metaDescription: string | null): Promise<KeywordAnalysis> {
    // Extract text content
    const textContent = $('body').text();

    // Extract potential keywords
    const keywords = await this._extractPotentialKeywords(textContent, title);

    // Analyze keyword usage
    const keywordAnalysis: KeywordAnalysis = {};

    for (const keyword of keywords) {
      const keywordRegex = new RegExp(keyword, 'gi');

      // Count occurrences
      const titleCount = (title.match(keywordRegex) || []).length;
      const metaDescriptionCount = metaDescription ? (metaDescription.match(keywordRegex) || []).length : 0;
      const contentCount = (textContent.match(keywordRegex) || []).length;
      const h1Count = $('h1').text().match(keywordRegex) ? (($('h1').text().match(keywordRegex) || []).length) : 0;
      const h2Count = $('h2').text().match(keywordRegex) ? (($('h2').text().match(keywordRegex) || []).length) : 0;

      // Calculate keyword density
      const wordCount = textContent.split(/\s+/).filter(Boolean).length;
      const density = wordCount > 0 ? (contentCount / wordCount) * 100 : 0;

      keywordAnalysis[keyword] = {
        occurrences: {
          title: titleCount,
          metaDescription: metaDescriptionCount,
          h1: h1Count,
          h2: h2Count,
          content: contentCount,
        },
        density,
        optimization: this._assessKeywordOptimization(
          titleCount,
          metaDescriptionCount,
          h1Count,
          contentCount,
          density
        ),
      };
    }

    return keywordAnalysis;
  }

  /**
   * Analyze keyword usage in text
   * @param {string} text - Text content
   * @param {string} targetKeywords - Target keywords
   * @returns {Promise<Object>} - Keyword analysis
   * @private
   */
  async _analyzeKeywordsInText(text: string, targetKeywords: string): Promise<KeywordAnalysis> {
    // Extract keywords
    const keywords = targetKeywords
      ? targetKeywords.split(',').map(k => k.trim())
      : await this._extractPotentialKeywords(text);

    // Analyze keyword usage
    const keywordAnalysis: KeywordAnalysis = {};

    for (const keyword of keywords) {
      const keywordRegex = new RegExp(keyword, 'gi');

      // Count occurrences
      const contentCount = (text.match(keywordRegex) || []).length;

      // Calculate keyword density
      const wordCount = text.split(/\s+/).filter(Boolean).length;
      const density = wordCount > 0 ? (contentCount / wordCount) * 100 : 0;

      keywordAnalysis[keyword] = {
        occurrences: contentCount,
        density,
        optimization: this._assessKeywordDensity(density),
      };
    }

    return keywordAnalysis;
  }

  /**
   * Extract potential keywords from text
   * @param {string} text - Text content
   * @param {string} title - Optional title to help with extraction
   * @returns {Promise<Array<string>>} - Extracted keywords
   * @private
   */
  async _extractPotentialKeywords(text: string, title: string = ''): Promise<string[]> {
    try {
      // Use LLM to extract potential keywords
      const prompt = `
Extract the 5 most important SEO keywords or phrases from the following content.
Consider the title (if provided) as an important signal for the main topic.
Return only the keywords, one per line, without numbering or additional text.

Title: ${title}

Content:
${text.slice(0, 2000)}
`;

      // Use streamText to generate the response @ts-expect-error - await is needed here even though TypeScript doesn't think so
      const { textStream } = await streamText({
        model: openai("gpt-4o"),
        prompt,
      });

      let result = "";
      for await (const delta of textStream) {
        result += delta;
      }

      // Parse the result
      return result
        .split('\n')
        .map(keyword => keyword.trim())
        .filter(Boolean);
    } catch (error: unknown) {
      console.error('Error extracting keywords:', error);

      // Fallback to simple extraction
      const words = text.toLowerCase().split(/\s+/).filter(Boolean);
      const wordFrequency: Record<string, number> = {};

      for (const word of words) {
        if (word.length > 3) {
          wordFrequency[word] = (wordFrequency[word] || 0) + 1;
        }
      }

      return Object.entries(wordFrequency)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([word]) => word);
    }
  }

  /**
   * Assess keyword optimization
   * @param {number} titleCount - Occurrences in title
   * @param {number} metaDescriptionCount - Occurrences in meta description
   * @param {number} h1Count - Occurrences in H1
   * @param {number} contentCount - Occurrences in content
   * @param {number} density - Keyword density
   * @returns {string} - Optimization assessment
   * @private
   */
  _assessKeywordOptimization(titleCount: number, metaDescriptionCount: number, h1Count: number, contentCount: number, density: number): 'excellent' | 'good' | 'fair' | 'poor' {
    if (titleCount > 0 && metaDescriptionCount > 0 && h1Count > 0 && contentCount >= 3 && density >= 0.5 && density <= 2.5) {
      return 'excellent';
    } else if ((titleCount > 0 || metaDescriptionCount > 0) && contentCount >= 2 && density >= 0.2 && density <= 3) {
      return 'good';
    } else if (contentCount >= 1) {
      return 'fair';
    } else {
      return 'poor';
    }
  }

  /**
   * Assess keyword density
   * @param {number} density - Keyword density
   * @returns {string} - Density assessment
   * @private
   */
  _assessKeywordDensity(density: number): 'optimal' | 'high' | 'excessive' | 'low' {
    if (density >= 0.5 && density <= 2.5) {
      return 'optimal';
    } else if (density > 2.5 && density <= 4) {
      return 'high';
    } else if (density > 4) {
      return 'excessive';
    } else {
      return 'low';
    }
  }

  /**
   * Identify SEO issues
   * @param {Object} seoData - SEO data
   * @param {number} loadTime - Page load time
   * @param {Object} mobileFriendliness - Mobile friendliness assessment
   * @param {Object} contentQuality - Content quality assessment
   * @returns {Array<Object>} - Identified issues
   * @private
   */
  _identifyIssues(seoData: SeoData, loadTime: number, mobileFriendliness: MobileFriendliness, contentQuality: ContentQuality): SeoIssue[] {
    const issues: SeoIssue[] = [];

    // Title issues
    if (!seoData.title) {
      issues.push({
        type: 'critical',
        category: 'metadata',
        message: 'Missing page title',
      });
    } else if (seoData.title.length < 30) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Title is too short (less than 30 characters)',
      });
    } else if (seoData.title.length > 60) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Title is too long (more than 60 characters)',
      });
    }

    // Meta description issues
    if (!seoData.metaDescription) {
      issues.push({
        type: 'critical',
        category: 'metadata',
        message: 'Missing meta description',
      });
    } else if (seoData.metaDescription.length < 70) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Meta description is too short (less than 70 characters)',
      });
    } else if (seoData.metaDescription.length > 160) {
      issues.push({
        type: 'warning',
        category: 'metadata',
        message: 'Meta description is too long (more than 160 characters)',
      });
    }

    // Heading issues
    if (seoData.headings.h1.length === 0) {
      issues.push({
        type: 'critical',
        category: 'structure',
        message: 'Missing H1 heading',
      });
    } else if (seoData.headings.h1.length > 1) {
      issues.push({
        type: 'warning',
        category: 'structure',
        message: 'Multiple H1 headings (should have only one)',
      });
    }

    // Image issues
    const imagesWithoutAlt = seoData.images.filter((img: ImageData) => !img.hasAlt);
    if (imagesWithoutAlt.length > 0) {
      issues.push({
        type: 'warning',
        category: 'accessibility',
        message: `${imagesWithoutAlt.length} images missing alt text`,
      });
    }

    // Performance issues
    if (loadTime > 3000) {
      issues.push({
        type: loadTime > 5000 ? 'critical' : 'warning',
        category: 'performance',
        message: `Slow page load time (${Math.round(loadTime / 100) / 10}s)`,
      });
    }

    // Mobile friendliness issues
    if (!mobileFriendliness.isMobileFriendly) {
      issues.push({
        type: 'critical',
        category: 'mobile',
        message: 'Page is not mobile-friendly',
      });

      if (!mobileFriendliness.hasViewportMeta) {
        issues.push({
          type: 'critical',
          category: 'mobile',
          message: 'Missing viewport meta tag',
        });
      }

      if (mobileFriendliness.hasHorizontalScroll) {
        issues.push({
          type: 'warning',
          category: 'mobile',
          message: 'Page has horizontal scrolling on mobile',
        });
      }

      if (mobileFriendliness.smallTapTargets && mobileFriendliness.smallTapTargets > 0) {
        issues.push({
          type: 'warning',
          category: 'mobile',
          message: `${mobileFriendliness.smallTapTargets} tap targets too small`,
        });
      }

      if (mobileFriendliness.smallFontElements && mobileFriendliness.smallFontElements > 0) {
        issues.push({
          type: 'warning',
          category: 'mobile',
          message: `${mobileFriendliness.smallFontElements} elements with font size too small`,
        });
      }
    }

    // Content issues
    if (contentQuality.wordCount < 300) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Thin content (less than 300 words)',
      });
    }

    if (contentQuality.readabilityScore < 30) {
      issues.push({
        type: 'warning',
        category: 'content',
        message: 'Content is difficult to read (low readability score)',
      });
    }

    return issues;
  }

  /**
   * Generate SEO recommendations
   * @param {Object} seoData - SEO data
   * @param {number} loadTime - Page load time
   * @param {Object} mobileFriendliness - Mobile friendliness assessment
   * @param {Object} contentQuality - Content quality assessment
   * @param {Object} keywordAnalysis - Keyword analysis
   * @param {string} url - Page URL
   * @returns {Promise<Array<Object>>} - Recommendations
   * @private
   */
  async _generateRecommendations(seoData: SeoData, loadTime: number, mobileFriendliness: MobileFriendliness, contentQuality: ContentQuality, keywordAnalysis: KeywordAnalysis, url: string): Promise<SeoRecommendation[]> {
    try {
      // Use LLM to generate recommendations
      const keywordInfo = Object.entries(keywordAnalysis)
        .map(([keyword, analysis]) => {
          const occurrences = typeof analysis.occurrences === 'number'
            ? analysis.occurrences
            : analysis.occurrences.content;
          return `"${keyword}": ${occurrences} occurrences, ${analysis.density.toFixed(2)}% density, optimization: ${analysis.optimization}`;
        })
        .join('\n');

      const prompt = `
Generate 5-7 specific, actionable SEO recommendations for the following webpage.
Focus on the most impactful improvements based on the provided data.
Format each recommendation as a brief, actionable item.

URL: ${url}

Page Title: ${seoData.title || 'Missing'}
Meta Description: ${seoData.metaDescription || 'Missing'}
H1 Heading: ${seoData.headings.h1[0] || 'Missing'}

Content Stats:
- Word Count: ${contentQuality.wordCount}
- Readability Score: ${contentQuality.readabilityScore.toFixed(1)}
- Content Depth: ${contentQuality.contentDepth}

Performance:
- Load Time: ${(loadTime / 1000).toFixed(2)}s

Mobile Friendliness: ${mobileFriendliness.isMobileFriendly ? 'Good' : 'Poor'}

Keyword Analysis:
${keywordInfo}

Return only the numbered recommendations, one per line, without additional commentary.
`;

      // Use streamText to generate the response
      // @ts-ignore - await is needed here
      const { textStream } = await streamText({
        model: openai("gpt-4o"),
        prompt,
      });

      let result = "";
      for await (const delta of textStream) {
        result += delta;
      }

      // Parse the result
      return result
        .split('\n')
        .map(line => line.trim())
        .filter(line => /^\d+\./.test(line))
        .map(line => ({
          recommendation: line.replace(/^\d+\.\s*/, ''),
        }));
    } catch (error) {
      console.error('Error generating recommendations:', error);

      // Return basic recommendations
      return [
        {
          recommendation: 'Ensure the page has a descriptive title between 30-60 characters',
        },
        {
          recommendation: 'Add a meta description between 70-160 characters',
        },
        {
          recommendation: 'Include a single H1 heading that contains your primary keyword',
        },
        {
          recommendation: 'Add alt text to all images for better accessibility and SEO',
        },
        {
          recommendation: 'Improve page load speed to enhance user experience and SEO',
        },
      ];
    }
  }

  /**
   * Get content optimization recommendations
   * @param {string} content - Text content
   * @param {string} targetKeywords - Target keywords
   * @param {Object} contentQuality - Content quality assessment
   * @param {Object} keywordAnalysis - Keyword analysis
   * @returns {Promise<Array<string>>} - Recommendations
   * @private
   */
  async _getContentOptimizationRecommendations(content: string, targetKeywords: string, contentQuality: ContentQuality, keywordAnalysis: KeywordAnalysis): Promise<string[]> {
    try {
      // Use LLM to generate recommendations
      const keywordInfo = Object.entries(keywordAnalysis)
        .map(([keyword, analysis]) => {
          const occurrences = typeof analysis.occurrences === 'number'
            ? analysis.occurrences
            : analysis.occurrences.content;
          return `"${keyword}": ${occurrences} occurrences, ${analysis.density.toFixed(2)}% density, optimization: ${analysis.optimization}`;
        })
        .join('\n');

      const prompt = `
Generate 5 specific, actionable recommendations to optimize this content for SEO.
Focus on improving keyword usage, readability, and content structure.

Target Keywords: ${targetKeywords || 'Not specified'}

Content Stats:
- Word Count: ${contentQuality.wordCount}
- Readability Score: ${contentQuality.readabilityScore.toFixed(1)}
- Content Depth: ${contentQuality.contentDepth}

Keyword Analysis:
${keywordInfo}

Content Preview:
${content.slice(0, 500)}...

Return only the numbered recommendations, one per line, without additional commentary.
`;

      // Use streamText to generate the response
      // @ts-ignore - await is needed here
      const { textStream } = await streamText({
        model: openai("gpt-4o"),
        prompt,
      });

      let result = "";
      for await (const delta of textStream) {
        result += delta;
      }

      // Parse the result
      return result
        .split('\n')
        .map(line => line.trim())
        .filter(line => /^\d+\./.test(line))
        .map(line => line.replace(/^\d+\.\s*/, ''));
    } catch (error) {
      console.error('Error generating content recommendations:', error);

      // Return basic recommendations
      return [
        'Increase content length to at least 300 words for better search visibility',
        'Include target keywords in headings and first paragraph',
        'Break up text with subheadings (H2, H3) for better readability',
        'Add internal and external links to authoritative sources',
        'Include images with descriptive alt text related to your keywords',
      ];
    }
  }
}

// Export a singleton instance
export const seoAnalyzer = new SeoAnalyzer();

/**
 * Generate a comprehensive SEO report for a URL
 * @param {string} url - The URL to analyze
 * @returns {Promise<Object>} - SEO report data
 */
export async function generateSeoReport(url: string): Promise<{ success: boolean; reportDate?: Date; url?: string; analysis?: SeoAnalysisResult; error?: string }> {
  try {
    const analysis = await seoAnalyzer.analyzeUrl(url);

    return {
      success: true,
      reportDate: new Date(),
      url,
      analysis,
    };
  } catch (error: unknown) {
    console.error('Error generating SEO report:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
