"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "components/ui/tabs"
import { <PERSON><PERSON> } from "components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "components/ui/card"
import { Loader2, FileText, BarChart2, ImageIcon, MessageSquare, PlayCircle, RefreshCw } from "lucide-react"
import AgentCanvas from "components/marketing/AgentCanvas"
import AgentOutputViewer from "components/marketing/AgentOutputViewer"
import AgentControls from "components/marketing/AgentControls"


export default function MarketingAgentsPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState("dashboard")
  const [agentThoughts, setAgentThoughts] = useState<Record<string, string[]>>({
    "strategic-director": [],
    "research-insights": [],
    "content-creator": [],
    "social-media-orchestrator": [],
    "analytics-reporting": [],
  })


  const [agentOutputs, setAgentOutputs] = useState<
    Array<{
      id: string
      agentId: string
      type: "document" | "chart" | "image" | "text"
      title: string
      content: any
      timestamp: Date
    }>
  >([])
  const [campaignData, setCampaignData] = useState<any>(null)

  // Initialize agents
  useEffect(() => {
    if (session?.user) {
      // In a real implementation, we would initialize the agents here
      console.log("Initializing marketing agents...")
    }
  }, [session])

  // Fetch agent messages from Firebase
  useEffect(() => {
    if (campaignData && session?.user) {
      const fetchAgentMessages = async () => {
        try {
          console.log('Fetching agent messages from Firebase...');
          const response = await fetch('/api/agent-messages');

          if (response.ok) {
            const data = await response.json();
            console.log('Fetched agent messages:', data.messages);

            // Process messages
            data.messages.forEach((message: any) => {
              const fromAgentId = message.fromAgentId;
              const toAgentId = message.toAgentId;
              const content = message.content;

              // Add thought for the sending agent
              addAgentThought(fromAgentId, `Sending to ${getAgentName(toAgentId)}: ${content}`);

              // Add thought for the receiving agent
              addAgentThought(toAgentId, `Received from ${getAgentName(fromAgentId)}: ${content}`);
            });
          } else {
            console.error('Failed to fetch agent messages:', response.statusText);
          }
        } catch (error) {
          console.error('Error fetching agent messages:', error);
        }
      };

      fetchAgentMessages();

      // Set up polling for new messages
      const interval = setInterval(fetchAgentMessages, 5000); // Poll every 5 seconds

      return () => {
        clearInterval(interval);
      };
    }
  }, [campaignData, session])

  // Helper function to get agent name from ID
  const getAgentName = (agentId: string): string => {
    return agentId
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Function to start a new marketing campaign
  const startMarketingCampaign = async (campaignDetails: any) => {
    setLoading(true)

    try {
      // Add initial thoughts
      addAgentThought("strategic-director", "Analyzing campaign requirements and product details...")

      // Simulate API call to create marketing campaign
      const response = await fetch("/api/marketing-agents", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "createMarketingCampaign",
          params: campaignDetails,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to create marketing campaign")
      }

      const data = await response.json()
      setCampaignData(data)

      // Simulate agent thoughts and outputs
      simulateAgentActivities(campaignDetails)
    } catch (error) {
      console.error("Error starting marketing campaign:", error)
    } finally {
      setLoading(false)
    }
  }

  // Function to add a thought from an agent
  const addAgentThought = (agentId: string, thought: string) => {
    console.log(`Adding thought for agent ${agentId}: ${thought}`);
    setAgentThoughts((prev) => {
      const updatedThoughts = {
        ...prev,
        [agentId]: [...(prev[agentId] || []), thought],
      };
      console.log('Updated agent thoughts:', updatedThoughts);
      return updatedThoughts;
    });
  }

  // Function to add an output from an agent
  const addAgentOutput = (output: {
    agentId: string
    type: "document" | "chart" | "image" | "text"
    title: string
    content: any
  }) => {
    setAgentOutputs((prev) => [
      ...prev,
      {
        id: `output-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        ...output,
      },
    ])
  }

  // Simulate agent activities for demonstration
  const simulateAgentActivities = (campaignDetails: any) => {
    // Strategic Director thoughts and outputs
    setTimeout(() => {
      addAgentThought("strategic-director", `Analyzing product: ${campaignDetails.productName}`)
    }, 1000)

    setTimeout(() => {
      addAgentThought("strategic-director", "Identifying unique value propositions and competitive advantages...")
    }, 3000)

    setTimeout(() => {
      addAgentOutput({
        agentId: "strategic-director",
        type: "document",
        title: "Product Analysis Report",
        content: {
          productName: campaignDetails.productName,
          uniqueSellingPoints: [
            "Advanced AI integration",
            "Seamless multi-device compatibility",
            "Intuitive user interface",
          ],
          targetMarket: campaignDetails.targetAudience,
          swotAnalysis: {
            strengths: ["Innovative technology", "User-friendly design"],
            weaknesses: ["New market entrant", "Premium pricing"],
            opportunities: ["Growing smart home market", "Increasing tech adoption"],
            threats: ["Established competitors", "Rapid technological changes"],
          },
        },
      })
    }, 5000)

    // Research & Insights thoughts and outputs
    setTimeout(() => {
      addAgentThought("research-insights", "Conducting market research and competitive analysis...")
    }, 2000)

    setTimeout(() => {
      addAgentThought("research-insights", `Researching target audience: ${campaignDetails.targetAudience}`)
    }, 4000)

    setTimeout(() => {
      addAgentOutput({
        agentId: "research-insights",
        type: "chart",
        title: "Market Segment Analysis",
        content: {
          chartType: "pie",
          data: [
            { label: "Early Adopters", value: 35 },
            { label: "Tech Enthusiasts", value: 25 },
            { label: "Smart Home Users", value: 20 },
            { label: "Convenience Seekers", value: 20 },
          ],
        },
      })
    }, 7000)

    // Content Creator thoughts and outputs
    setTimeout(() => {
      addAgentThought("content-creator", "Developing content strategy based on product analysis and market research...")
    }, 6000)

    setTimeout(() => {
      addAgentThought("content-creator", "Creating compelling messaging that highlights product benefits...")
    }, 8000)

    setTimeout(() => {
      addAgentOutput({
        agentId: "content-creator",
        type: "document",
        title: "Marketing Content Strategy",
        content: {
          keyMessages: [
            "Simplify your life with intelligent home automation",
            "Experience the future of smart living today",
            "Control your entire home with just your voice",
          ],
          contentThemes: [
            "Everyday convenience",
            "Cutting-edge technology",
            "Seamless integration",
            "Personalized experiences",
          ],
          contentTypes: ["Blog posts", "Social media content", "Video scripts", "Email campaigns"],
        },
      })
    }, 10000)

    // Social Media Orchestrator thoughts and outputs
    setTimeout(() => {
      addAgentThought(
        "social-media-orchestrator",
        "Developing platform-specific strategies for optimal content distribution...",
      )
    }, 9000)

    setTimeout(() => {
      addAgentThought("social-media-orchestrator", "Creating social media calendar and hashtag strategy...")
    }, 11000)

    setTimeout(() => {
      addAgentOutput({
        agentId: "social-media-orchestrator",
        type: "image",
        title: "Social Media Content Preview",
        content: {
          imageUrl: "https://via.placeholder.com/600x400?text=Social+Media+Preview",
          altText: "Social media content preview for campaign",
        },
      })
    }, 13000)

    // Analytics & Reporting thoughts and outputs
    setTimeout(() => {
      addAgentThought("analytics-reporting", "Setting up KPI tracking framework for campaign performance...")
    }, 12000)

    setTimeout(() => {
      addAgentThought("analytics-reporting", "Establishing baseline metrics and performance targets...")
    }, 14000)

    setTimeout(() => {
      addAgentOutput({
        agentId: "analytics-reporting",
        type: "chart",
        title: "Campaign KPI Dashboard",
        content: {
          chartType: "bar",
          data: [
            { label: "Brand Awareness", target: 100, current: 0 },
            { label: "Engagement Rate", target: 5, current: 0 },
            { label: "Conversion Rate", target: 2.5, current: 0 },
            { label: "Lead Generation", target: 500, current: 0 },
          ],
        },
      })
    }, 16000)
  }

  // Add state for refresh button
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true)
    console.log("Manual refresh triggered")
    try {
      // Simulate refresh
      await new Promise((resolve) => setTimeout(resolve, 1500))
      console.log("Manual refresh completed")
    } catch (error) {
      console.error("Error during manual refresh:", error)
    } finally {
      setIsRefreshing(false)
    }
  }

  return (
    <div className="min-h-screen bg-ike-dark-purple text-gray-100">
      <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <img
                src="/logo5b.png"
                alt="ike Logo"
                className="h-12 w-auto ml-3 mr-4 hover:opacity-80 transition-opacity duration-300"
              />
              <h1 className="text-2xl font-bold text-white">Marketing Agents Dashboard</h1>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing || loading}
                className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed shadow-md"
              >
                <RefreshCw className={`w-5 h-5 mr-2 ${isRefreshing ? "animate-spin" : ""}`} />
                {isRefreshing ? "Refreshing..." : "Refresh Data"}
              </button>

              {loading ? (
                <div className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md shadow-md">
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                <button
                  onClick={() => setActiveTab("dashboard")}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center shadow-md"
                >
                  <PlayCircle className="w-5 h-5 mr-2" />
                  New Campaign
                </button>
              )}
            </div>
          </div>
        <div className="bg-gray-900 rounded-lg shadow-lg p-6 md:p-8">


          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-3 w-[400px] bg-gray-800 text-gray-300 rounded-md">
              <TabsTrigger
                value="dashboard"
                className="data-[state=active]:bg-purple-600 data-[state=active]:text-white"
              >
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="canvas" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                Agent Canvas
              </TabsTrigger>
              <TabsTrigger value="outputs" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
                Outputs
              </TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-gray-700 rounded-lg shadow-md p-6 border-l-4 border-purple-500">
                  <h2 className="text-lg font-medium text-gray-300">Campaigns</h2>
                  <p className="text-3xl font-bold text-gray-100 mt-2">{campaignData ? 1 : 0}</p>
                  <p className="text-sm text-gray-400 mt-1">{campaignData ? "1 active" : "No active campaigns"}</p>
                </div>

                <div className="bg-gray-700 rounded-lg shadow-md p-6 border-l-4 border-blue-500">
                  <h2 className="text-lg font-medium text-gray-300">Agents</h2>
                  <p className="text-3xl font-bold text-gray-100 mt-2">{Object.keys(agentThoughts).length}</p>
                  <p className="text-sm text-gray-400 mt-1">Working together</p>
                </div>

                <div className="bg-gray-700 rounded-lg shadow-md p-6 border-l-4 border-green-500">
                  <h2 className="text-lg font-medium text-gray-300">Outputs</h2>
                  <p className="text-3xl font-bold text-gray-100 mt-2">{agentOutputs.length}</p>
                  <p className="text-sm text-gray-400 mt-1">{agentOutputs.length} items generated</p>
                </div>

                <div className="bg-gray-700 rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
                  <h2 className="text-lg font-medium text-gray-300">Status</h2>
                  <p className="text-3xl font-bold text-gray-100 mt-2">{loading ? "Active" : "Ready"}</p>
                  <p className="text-sm text-gray-400 mt-1">{loading ? "Processing campaign" : "Ready to start"}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <Card className="bg-gray-800 border-gray-700 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-gray-100 text-xl">Marketing Campaign</CardTitle>
                    <CardDescription className="text-gray-400">Create and manage marketing campaigns</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <AgentControls onStartCampaign={startMarketingCampaign} />
                  </CardContent>
                </Card>

                <Card className="bg-gray-800 border-gray-700 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-gray-100 text-xl">Agent Canvas</CardTitle>
                    <CardDescription className="text-gray-400">
                      Visualize agent thoughts and reasoning process
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <AgentCanvas agentThoughts={agentThoughts} />
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 gap-6">
                <Card className="bg-gray-800 border-gray-700 shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-gray-100 text-xl">Recent Outputs</CardTitle>
                    <CardDescription className="text-gray-400">Latest outputs from marketing agents</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {agentOutputs.slice(-3).map((output) => (
                        <Card key={output.id} className="overflow-hidden bg-gray-700 border-gray-600">
                          <CardHeader className="p-4">
                            <div className="flex items-center">
                              {output.type === "document" && <FileText className="h-4 w-4 mr-2 text-purple-400" />}
                              {output.type === "chart" && <BarChart2 className="h-4 w-4 mr-2 text-blue-400" />}
                              {output.type === "image" && <ImageIcon className="h-4 w-4 mr-2 text-green-400" />}
                              {output.type === "text" && <MessageSquare className="h-4 w-4 mr-2 text-yellow-400" />}
                              <CardTitle className="text-sm text-gray-100">{output.title}</CardTitle>
                            </div>
                          </CardHeader>
                          <CardContent className="p-4 pt-0">
                            <AgentOutputViewer output={output} />
                          </CardContent>
                        </Card>
                      ))}

                      {agentOutputs.length === 0 && (
                        <p className="col-span-3 text-center text-gray-400">
                          No outputs yet. Start a campaign to see agent outputs.
                        </p>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      onClick={() => setActiveTab("outputs")}
                      disabled={agentOutputs.length === 0}
                      className="bg-purple-600 text-white hover:bg-purple-700 border-none transition-colors"
                    >
                      View All Outputs
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="canvas">
              <Card className="bg-gray-800 border-gray-700 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-gray-100 text-xl">Agent Communication</CardTitle>
                  <CardDescription className="text-gray-400">View agent messages and communication</CardDescription>
                </CardHeader>
                <CardContent>
                  <AgentCanvas agentThoughts={agentThoughts} />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="outputs">
              <Card className="bg-gray-800 border-gray-700 shadow-lg">
                <CardHeader>
                  <CardTitle className="text-gray-100 text-xl">Agent Outputs</CardTitle>
                  <CardDescription className="text-gray-400">
                    Documents, charts, and other outputs created by agents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {agentOutputs.map((output) => (
                      <Card key={output.id} className="overflow-hidden bg-gray-700 border-gray-600">
                        <CardHeader className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              {output.type === "document" && <FileText className="h-4 w-4 mr-2 text-purple-400" />}
                              {output.type === "chart" && <BarChart2 className="h-4 w-4 mr-2 text-blue-400" />}
                              {output.type === "image" && <ImageIcon className="h-4 w-4 mr-2 text-green-400" />}
                              {output.type === "text" && <MessageSquare className="h-4 w-4 mr-2 text-yellow-400" />}
                              <CardTitle className="text-sm text-gray-100">{output.title}</CardTitle>
                            </div>
                            <span className="text-xs text-gray-400">{output.timestamp.toLocaleTimeString()}</span>
                          </div>
                          <CardDescription className="text-xs text-gray-400">
                            Created by:{" "}
                            {output.agentId
                              .split("-")
                              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                              .join(" ")}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="p-4 pt-0">
                          <AgentOutputViewer output={output} />
                        </CardContent>
                      </Card>
                    ))}

                    {agentOutputs.length === 0 && (
                      <p className="col-span-3 text-center text-gray-400">
                        No outputs yet. Start a campaign to see agent outputs.
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}



