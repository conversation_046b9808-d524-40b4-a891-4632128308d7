# Enhanced Markdown Content Guidelines

This document outlines the formatting guidelines for generating markdown content that will be rendered using the EnhancedMarkdownContent component.

## General Principles

1. Use clean, simple markdown formatting
2. Avoid overly complex nested structures
3. Be consistent with formatting throughout the document
4. Focus on readability and clarity

## Headings

- Use `#` for main titles (H1)
- Use `##` for section headings (H2)
- Use `###` for subsection headings (H3)
- Use `####` for minor section headings (H4)
- Always include a space after the `#` symbols
- Leave a blank line before and after headings

Example:
```markdown
## Main Section Heading

Content goes here...

### Subsection Heading

More content...
```

## Lists

### Unordered Lists
- Use `-` for bullet points (preferred over `*` or `+`)
- Indent nested lists with 2 spaces
- Leave a blank line before and after lists

Example:
```markdown
- First item
- Second item
  - Nested item 1
  - Nested item 2
- Third item
```

### Ordered Lists
- Use `1.` format for numbered lists
- Numbers will be automatically rendered correctly regardless of the actual numbers used
- Indent nested lists with 2 spaces
- Leave a blank line before and after lists

Example:
```markdown
1. First step
2. Second step
   1. Substep A
   2. Substep B
3. Third step
```

## Emphasis

- Use `**bold**` for strong emphasis or important points
- Use `*italic*` for mild emphasis or terminology
- Avoid overusing emphasis
- Use bold for character names in dialogue

Example:
```markdown
This is **important** information about *terminology*.
```

## Code Blocks

- Use triple backticks (```) for code blocks
- Specify the language after the opening backticks for syntax highlighting
- Use single backticks for inline code

Example:
```markdown
```javascript
function example() {
  return "Hello world";
}
```

Use the `example()` function to display a greeting.
```

## Links

- Use `[text](url)` format for links
- External links will automatically get an external link icon
- Use descriptive link text

Example:
```markdown
Visit [our documentation](https://example.com/docs) for more information.
```

## Blockquotes

- Use `>` for blockquotes
- Use multiple `>` symbols for nested blockquotes
- Leave a blank line before and after blockquotes

Example:
```markdown
> This is a quotation from a source.
> 
> > This is a nested quotation.
```

## Special Formatting for Dialogue

- Use bold for character names followed by a colon
- Line numbers should be bold and can be included with character names

Example:
```markdown
**1. Character Name**: This is their dialogue.

**2. Another Character**: This is a response.
```

## Tables

- Use standard markdown table syntax
- Keep tables simple and readable
- Align columns as needed using `:` in the separator row

Example:
```markdown
| Name | Role | Department |
|:-----|:----:|----------:|
| John | Lead | Marketing |
| Jane | Developer | Engineering |
```
