import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    screens: {
      'xs': '480px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    },
    extend: {
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      colors: {
        'ike-purple': '#4E2955',
        'ike-dark-purple': '#110613',
        'ike-message-bg' : '#321937',
        'ike-message-ai' : '#411E47',
        'ike-purple_b': '#3E2044'
      },
      keyframes: {
        voiceWave: {
          '0%': { opacity: '0.8', transform: 'scale(0.2)' },
          '100%': { opacity: '0', transform: 'scale(2)' }
        }
      },
      animation: {
        'voiceWave': 'voiceWave 2s infinite',
        'voiceWave-delayed': 'voiceWave 2s infinite 0.5s'
      }
    },
  },
  plugins: [],
};
export default config;