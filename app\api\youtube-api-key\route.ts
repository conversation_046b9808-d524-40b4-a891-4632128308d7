import { NextResponse } from 'next/server';

/**
 * API endpoint to securely provide the YouTube API key to the client
 * This prevents exposing the API key directly in client-side code
 */
export async function GET() {
  try {
    // Get API key from environment variables
    const apiKey = process.env.YOUTUBE_API_KEY;

    if (!apiKey) {
      console.warn('YouTube API key not found in environment variables');
      return NextResponse.json(
        { error: 'API key not configured' },
        { status: 500 }
      );
    }

    // Return the API key
    return NextResponse.json({ apiKey });
  } catch (error) {
    console.error('Error retrieving YouTube API key:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve API key' },
      { status: 500 }
    );
  }
}
