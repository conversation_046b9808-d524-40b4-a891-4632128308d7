'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  PlusCircle,
  ArrowLeft,
  Calendar,
  Users,
  Clock,
  Edit,
  Search,
  Filter,
  CheckCircle,
  AlertTriangle,
  Trash2,
  <PERSON>rk<PERSON>
} from 'lucide-react';
import DeleteProjectButton from './DeleteProjectButton';
import { useSession } from 'next-auth/react';
import { usePlanner } from '../../../../context/PlannerContext';
import TabsContextDebugger from '../../../../components/TabsContextDebugger';
import { Project, Task, TaskStatus } from '../../../../../admin/planner/types';
import { getCategoryColorClass } from '../../../../utils/categoryColors';
import ProjectForm from '../../../../../admin/planner/components/ProjectForm';
import TaskForm from '../../../../../admin/planner/components/TaskForm';
import ProjectDocumentUploader from '../../../../../admin/planner/components/ProjectDocumentUploader';
import ProjectDocumentList from '../../../../../admin/planner/components/ProjectDocumentList';
import BacklogTab from '../../../../../admin/planner/components/BacklogTab';
import ProjectSummary from '../../../../../admin/planner/components/ProjectSummary';
import CalendarTab from '../../../../../admin/planner/components/CalendarTab';
import ProjectCommentsTabWrapper from '../../../../../admin/planner/components/ProjectCommentsTabWrapper';
import CreateTasksModal from '../../../../../admin/planner/components/CreateTasksModal';
import TaskDetailsModal from '../../../../../admin/planner/components/TaskDetailsModal';

interface ProjectDetailProps {
  params: Promise<{
    projectId: string;
  }>;
}

/**
 * Utility function to normalize array data consistently
 * @param data - The data to normalize (any type)
 * @returns A properly formatted string array
 */
const normalizeArrayData = (data: any): string[] => {
  if (!data) return [];

  if (Array.isArray(data)) {
    return data;
  } else if (typeof data === 'string') {
    try {
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      return data.split(',').map(c => c.trim());
    } catch (e) {
      return data.split(',').map(c => c.trim());
    }
  } else if (typeof data === 'object') {
    return Object.values(data);
  }

  return [];
};

export default function ProjectDetail({ params }: ProjectDetailProps) {
  const router = useRouter();
  const { projects, tasks, users, loading, createTask, updateTask, updateProjectData, refreshData, removeTask } = usePlanner();

  const [project, setProject] = useState<Project | null>(null);
  const [projectTasks, setProjectTasks] = useState<Task[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<TaskStatus | 'all'>('all');
  const [activeTab, setActiveTab] = useState<'tasks' | 'documents' | 'backlog' | 'calendar' | 'comments'>('tasks');
  const [projectId, setProjectId] = useState<string | null>(null);

  // Modal states
  const [isProjectFormOpen, setIsProjectFormOpen] = useState(false);
  const [isTaskFormOpen, setIsTaskFormOpen] = useState(false);
  const [isCreateTasksModalOpen, setIsCreateTasksModalOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null); // For task details modal
  const [documentRefreshKey, setDocumentRefreshKey] = useState(0);

  // Get the current user's session
  const { data: session } = useSession();
  const [accessDenied, setAccessDenied] = useState(false);

  // Handle async params
  useEffect(() => {
    params.then(({ projectId }) => {
      setProjectId(projectId);
    });
  }, [params]);

  // Check for editTask query parameter
  useEffect(() => {
    // Get the editTask query parameter from the URL
    const params = new URLSearchParams(window.location.search);
    const editTaskId = params.get('editTask');

    if (editTaskId && tasks.length > 0) {
      // Find the task with the matching ID
      const taskToEdit = tasks.find(t => t.id === editTaskId);

      if (taskToEdit) {
        // Set the editing task and open the task form
        setEditingTask(taskToEdit);
        setIsTaskFormOpen(true);
      }
    }
  }, [tasks]);

  // Load project and tasks
  useEffect(() => {
    if (!loading && session?.user?.email && projectId) {
      const foundProject = projects.find(p => p.id === projectId);

      if (foundProject) {
        // Check if the current user is an admin
        const isAdmin = users.find(u =>
          u.email === session.user?.email && u.role === 'admin'
        );

        // Check if the current user is the project owner or a member
        const isOwner = foundProject.owner === session.user?.email;

        // Get the current user's ID
        const currentUser = users.find(u => u.email === session.user?.email);
        const isMember = currentUser && normalizeArrayData(foundProject.members).includes(currentUser.id);

        if (isAdmin || isOwner || isMember) {
          // Normalize project data before setting state
          const normalizedProject = {
            ...foundProject,
            categories: normalizeArrayData(foundProject.categories),
            members: normalizeArrayData(foundProject.members)
          };

          setProject(normalizedProject);

          const filteredTasks = tasks.filter(t => t.projectId === projectId);
          setProjectTasks(filteredTasks);
        } else {
          setAccessDenied(true);
        }
      } else {
        // Project not found, redirect back to projects list
        router.push('/services/admin/planner');
      }
    }
  }, [loading, projects, tasks, projectId, router, session, users]);

  // Get user name by ID
  const getUserName = (userId: string): string => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : 'Unknown User';
  };

  // Format date for display
  const formatDate = (date: Date | string): string => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get status color
  const getStatusColor = (status: TaskStatus): string => {
    // Normalize status to handle variations
    const normalizedStatus = status.toLowerCase();

    if (normalizedStatus === 'not started') {
      return 'bg-gray-700 text-gray-300';
    } else if (normalizedStatus === 'in progress') {
      return 'bg-blue-900/50 text-blue-300';
    } else if (normalizedStatus === 'reviewed') {
      return 'bg-yellow-900/50 text-yellow-300';
    } else if (normalizedStatus === 'complete' || normalizedStatus === 'completed') {
      return 'bg-green-900/50 text-green-300';
    } else {
      return 'bg-gray-700 text-gray-300';
    }
  };

  // Handle project update
  const handleUpdateProject = async (updatedProject: Omit<Project, 'id'>) => {
    if (!project || !projectId) return;

    try {
      // Create a clean update object with all fields explicitly included
      // and normalize array data consistently
      const processedProject = {
        name: updatedProject.name,
        description: updatedProject.description,
        startDate: updatedProject.startDate,
        endDate: updatedProject.endDate,
        owner: updatedProject.owner,
        status: updatedProject.status,
        categories: normalizeArrayData(updatedProject.categories),
        members: normalizeArrayData(updatedProject.members)
      };

      // Update the project in the database
      await updateProjectData(projectId, processedProject);

      // Close the form first to improve UX responsiveness
      setIsProjectFormOpen(false);

      // Refresh data to ensure UI is updated
      await refreshData();

      // Get the updated project from the projects array
      const refreshedProject = projects.find(p => p.id === projectId);

      if (refreshedProject) {
        // Normalize the refreshed project data
        const normalizedProject = {
          ...refreshedProject,
          categories: normalizeArrayData(refreshedProject.categories),
          members: normalizeArrayData(refreshedProject.members)
        };

        setProject(normalizedProject);
      } else {
        // If not found in the refreshed data, manually update the local state
        const updatedProjectWithId = {
          ...processedProject,
          id: projectId
        };
        setProject(updatedProjectWithId as Project);
      }
    } catch (error) {
      console.error('Error updating project:', error);
    }
  };

  // Handle task creation
  const handleCreateTask = async (taskData: Omit<Task, 'id'>) => {
    if (!projectId) return;

    try {
      // Ensure the task is associated with this project
      const task = {
        ...taskData,
        projectId
      };

      await createTask(task);
      setIsTaskFormOpen(false);
      setEditingTask(null);

      // Refresh the task list
      const filteredTasks = tasks.filter(t => t.projectId === projectId);
      setProjectTasks(filteredTasks);
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  // Handle task update
  const handleUpdateTask = async (taskData: Task) => {
    if (!projectId) return;

    try {
      await updateTask(taskData.id, taskData);
      setIsTaskFormOpen(false);
      setEditingTask(null);

      // Refresh the task list
      const filteredTasks = tasks.filter(t => t.projectId === projectId);
      setProjectTasks(filteredTasks);
    } catch (error) {
      console.error('Error updating task:', error);
    }
  };

  // Handle task deletion
  const handleDeleteTask = async (taskId: string, event: React.MouseEvent) => {
    if (!projectId) return;

    // Stop the click event from propagating to the parent (which would open the edit modal)
    event.stopPropagation();

    // Confirm before deleting
    if (confirm('Are you sure you want to delete this task?')) {
      try {
        await removeTask(taskId);
        // Refresh the task list
        const filteredTasks = tasks.filter(t => t.projectId === projectId);
        setProjectTasks(filteredTasks);
      } catch (error) {
        console.error('Error deleting task:', error);
      }
    }
  };

  // Check if user can delete a task (admin or creator)
  const canDeleteTask = (task: Task): boolean => {
    if (!session?.user?.email) return false;

    // Check if user is an admin
    const isAdmin = users.find(u =>
      u.email === session.user?.email && u.role === 'admin'
    );

    // Check if user is the creator of the task
    const currentUser = users.find(u => u.email === session.user?.email);
    const isCreator = task.createdBy === currentUser?.id;

    return !!isAdmin || isCreator;
  };

  // Helper function to normalize task status
  const normalizeTaskStatus = (status: string): TaskStatus => {
    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus === 'not started') return 'Not Started';
    if (normalizedStatus === 'in progress') return 'In Progress';
    if (normalizedStatus === 'reviewed') return 'Reviewed';
    if (normalizedStatus === 'complete' || normalizedStatus === 'completed') return 'Complete';
    return status as TaskStatus; // Default fallback
  };

  // Filter tasks based on search and status filter
  const filteredTasks = projectTasks.filter(task => {
    const matchesSearch =
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Normalize both the task status and the filter status for comparison
    const normalizedTaskStatus = normalizeTaskStatus(task.status);
    const normalizedFilterStatus = statusFilter === 'all' ? 'all' : normalizeTaskStatus(statusFilter);

    const matchesStatus = normalizedFilterStatus === 'all' || normalizedTaskStatus === normalizedFilterStatus;

    return matchesSearch && matchesStatus;
  });

  // Group tasks by status
  const tasksByStatus = {
    'Not Started': filteredTasks.filter(t => normalizeTaskStatus(t.status) === 'Not Started'),
    'In Progress': filteredTasks.filter(t => normalizeTaskStatus(t.status) === 'In Progress'),
    'Reviewed': filteredTasks.filter(t => normalizeTaskStatus(t.status) === 'Reviewed'),
    'Complete': filteredTasks.filter(t => normalizeTaskStatus(t.status) === 'Complete')
  };

  if (accessDenied) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <div className="container mx-auto px-4 py-6">
          {/* Tabs Context Debugger */}
          <TabsContextDebugger />

          <div className="flex items-center justify-center mt-20">
            <div className="bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full text-center">
              <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
              <p className="text-gray-300 mb-6">
                You don't have permission to view this project. Only project members and administrators can access this project.
              </p>
              <button
                onClick={() => router.push('/services/admin/planner')}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white transition-colors"
              >
                Return to Projects
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading || !project || !projectId) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <div className="container mx-auto px-4 py-6">
          {/* Tabs Context Debugger */}
          <TabsContextDebugger />

          <div className="flex items-center justify-center mt-20">
            <div className="text-xl text-gray-300">Loading project...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <div className="container mx-auto px-4 py-6">


        {/* Back button */}
        <button
          onClick={() => router.push('/services/admin/planner')}
          className="flex items-center text-gray-400 hover:text-white mb-6"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Projects
        </button>

        {/* Project header */}
        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <div>
              <h1 className="text-2xl font-bold text-white">{project.name}</h1>
              <p className="text-gray-400 mt-1">{project.description}</p>
            </div>

            <div className="mt-4 md:mt-0 flex items-center space-x-2">
              <button
                className="flex items-center px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                onClick={() => setIsProjectFormOpen(true)}
              >
                <Edit className="w-4 h-4 mr-2" />
                Edit Project
              </button>

              <button
                className="flex items-center px-3 py-1.5 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                onClick={() => setIsTaskFormOpen(true)}
              >
                <PlusCircle className="w-4 h-4 mr-2" />
                Add Task
              </button>

              <button
                className="flex items-center px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                onClick={() => setIsCreateTasksModalOpen(true)}
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Create Tasks with AI
              </button>

              <DeleteProjectButton projectId={projectId} projectName={project.name} />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="flex items-center text-gray-400">
              <Calendar className="w-5 h-5 mr-2 text-purple-400" />
              <div>
                <p className="text-sm">Timeline</p>
                <p className="text-white">{formatDate(project.startDate)} - {formatDate(project.endDate)}</p>
              </div>
            </div>

            <div className="flex items-center text-gray-400">
              <Users className="w-5 h-5 mr-2 text-purple-400" />
              <div>
                <p className="text-sm">Owner</p>
                <p className="text-white">{getUserName(project.owner)}</p>
              </div>
            </div>

            <div className="flex items-center text-gray-400">
              <Clock className="w-5 h-5 mr-2 text-purple-400" />
              <div>
                <p className="text-sm">Status</p>
                <p className="text-white">{project.status}</p>
              </div>
            </div>
          </div>

          {/* Categories section */}
          <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-300 mb-2">Categories</h3>
            {/* Display categories using the normalized data */}
            {project.categories?.length ? (
              <div className="flex flex-wrap gap-2">
                {project.categories.map((category, index) => (
                  <span
                    key={`${category}-${index}`}
                    className={`${getCategoryColorClass(category)} px-3 py-1.5 rounded-full text-xs font-medium shadow-sm`}
                  >
                    {category}
                  </span>
                ))}
              </div>
            ) : (
              <div className="text-gray-400">No categories</div>
            )}
          </div>
        </div>

        {/* AI Generated Project Summary */}
        <ProjectSummary project={project} tasks={projectTasks} />

        {/* Tab Navigation */}
        <div className="flex flex-wrap border-b border-gray-700 mb-6 overflow-x-hidden">
          <button
            className={`flex-1 min-w-0 px-2 sm:px-4 py-2 text-xs sm:text-sm md:text-base font-medium ${activeTab === 'tasks' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
            onClick={() => setActiveTab('tasks')}
          >
            <span className="truncate">Tasks</span>
          </button>
          <button
            className={`flex-1 min-w-0 px-2 sm:px-4 py-2 text-xs sm:text-sm md:text-base font-medium ${activeTab === 'backlog' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
            onClick={() => setActiveTab('backlog')}
          >
            <span className="truncate">Backlog</span>
          </button>
          <button
            className={`flex-1 min-w-0 px-2 sm:px-4 py-2 text-xs sm:text-sm md:text-base font-medium ${activeTab === 'calendar' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
            onClick={() => setActiveTab('calendar')}
          >
            <span className="truncate">Calendar</span>
          </button>
          <button
            className={`flex-1 min-w-0 px-2 sm:px-4 py-2 text-xs sm:text-sm md:text-base font-medium ${activeTab === 'documents' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
            onClick={() => setActiveTab('documents')}
          >
            <span className="truncate">Docs</span>
          </button>
          <button
            className={`flex-1 min-w-0 px-2 sm:px-4 py-2 text-xs sm:text-sm md:text-base font-medium ${activeTab === 'comments' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
            onClick={async () => {
              setActiveTab('comments');
              // Force refresh data to ensure we have the latest tasks
              try {
                await refreshData();
                // Then filter tasks for this project
                const filteredTasks = tasks.filter(t => t.projectId === projectId);
                setProjectTasks(filteredTasks);
                console.log(`Filtered ${filteredTasks.length} tasks for project ${projectId}`);
              } catch (err) {
                console.error('Error refreshing data for Comments tab:', err);
              }
            }}
          >
            Comments
          </button>
        </div>

        {/* Tasks Tab */}
        {activeTab === 'tasks' && (
          <>
            {/* Filters */}
            <div className="bg-gray-800 rounded-lg p-4 mb-6">
              <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                <div className="relative flex-grow">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search tasks..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-700 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Filter className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as TaskStatus | 'all')}
                    className="block w-full pl-10 pr-8 py-2 border border-gray-700 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="all">All Statuses</option>
                    <option value="Not Started">Not Started</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Reviewed">Reviewed</option>
                    <option value="Complete">Complete</option>
                  </select>
                </div>
              </div>
            </div>

            {filteredTasks.length === 0 ? (
              <div className="bg-gray-800 rounded-lg shadow-md p-8 text-center">
                <CheckCircle className="w-16 h-16 mx-auto text-gray-600 mb-4" />
                <h2 className="text-xl font-medium text-gray-300 mb-2">No tasks found</h2>
                <p className="text-gray-400 mb-6">
                  {searchQuery || statusFilter !== 'all'
                    ? 'No tasks match your search criteria. Try different filters.'
                    : 'Get started by adding your first task to this project.'}
                </p>
                {!searchQuery && statusFilter === 'all' && (
                  <button
                    onClick={() => setIsTaskFormOpen(true)}
                    className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                  >
                    <PlusCircle className="w-5 h-5 mr-2" />
                    Add Task
                  </button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                {(['Not Started', 'In Progress', 'Reviewed', 'Complete'] as const).map(status => (
                  <div key={status} className="bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div className={`p-3 ${getStatusColor(status)} flex justify-between items-center`}>
                      <h3 className="font-medium">{status}</h3>
                      <span className="bg-gray-800/30 text-white px-2 py-0.5 rounded-full text-xs">
                        {tasksByStatus[status].length}
                      </span>
                    </div>

                    <div className="p-4 space-y-3 max-h-[calc(100vh-300px)] overflow-y-auto custom-scrollbar">
                      {tasksByStatus[status].length === 0 ? (
                        <div className="text-center py-4 text-gray-500">
                          <p>No tasks</p>
                        </div>
                      ) : (
                        tasksByStatus[status].map(task => (
                          <div
                            key={task.id}
                            className="bg-gray-700 rounded-md p-3 hover:bg-gray-650 cursor-pointer"
                            onClick={() => {
                              setSelectedTask(task);
                            }}
                          >
                            <div className="flex justify-between items-start">
                              <h4 className="font-medium text-white mb-1">{task.title}</h4>
                              {canDeleteTask(task) && (
                                <button
                                  onClick={(e) => handleDeleteTask(task.id, e)}
                                  className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                                  title="Delete task"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                            <p className="text-gray-400 text-sm line-clamp-2 mb-2">{task.description}</p>

                            <div className="flex justify-between items-center text-xs text-gray-400">
                              <div className="flex items-center">
                                <Calendar className="w-3 h-3 mr-1" />
                                <span>{formatDate(task.dueDate)}</span>
                              </div>

                              {task.assignedTo && task.assignedTo.length > 0 && (
                                <div className="flex -space-x-1">
                                  {task.assignedTo.slice(0, 3).map((userId, index) => (
                                    <div
                                      key={index}
                                      className="w-6 h-6 rounded-full bg-purple-900 flex items-center justify-center text-purple-200 text-xs border border-gray-700"
                                      title={getUserName(userId)}
                                    >
                                      {getUserName(userId).charAt(0)}
                                    </div>
                                  ))}
                                  {task.assignedTo.length > 3 && (
                                    <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-white text-xs border border-gray-700">
                                      +{task.assignedTo.length - 3}
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    <div className="p-3 border-t border-gray-700">
                      <button
                        onClick={() => {
                          setIsTaskFormOpen(true);
                          // Pre-set the status for the new task
                          setEditingTask({ status, projectId } as Task);
                        }}
                        className="w-full flex items-center justify-center py-1 text-sm text-gray-400 hover:text-white"
                      >
                        <PlusCircle className="w-4 h-4 mr-1" />
                        Add Task
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* Backlog Tab */}
        {activeTab === 'backlog' && (
          <BacklogTab projectId={projectId} users={users} />
        )}

        {/* Calendar Tab */}
        {activeTab === 'calendar' && (
          <CalendarTab projectId={projectId} users={users} />
        )}

        {/* Documents Tab */}
        {activeTab === 'documents' && (
          <div className="bg-gray-800 rounded-lg shadow-md p-6">
            <h2 className="text-xl font-medium text-white mb-4">Project Documents</h2>
            <ProjectDocumentUploader
              projectId={projectId}
              onUploadComplete={() => setDocumentRefreshKey(prev => prev + 1)}
            />
            <div className="mt-6">
              <ProjectDocumentList
                projectId={projectId}
                onDeleteDocument={() => setDocumentRefreshKey(prev => prev + 1)}
              />
            </div>
          </div>
        )}

        {/* Comments Tab */}
        {activeTab === 'comments' && (
          <ProjectCommentsTabWrapper projectId={projectId} users={users} />
        )}
      </div>
        {/* Tabs Context Debugger */}
        <TabsContextDebugger />
      {/* Project form modal */}
      {isProjectFormOpen && project && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg shadow-xl p-4 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-3 text-white sticky top-0 bg-gray-800 py-1 z-10">Edit Project</h2>
            <ProjectForm
              initialValues={project}
              users={users}
              onSubmit={handleUpdateProject}
              onCancel={() => setIsProjectFormOpen(false)}
            />
          </div>
        </div>
      )}

      {/* Task form modal */}
      {isTaskFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 rounded-lg shadow-xl p-4 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-3 text-white sticky top-0 bg-gray-800 py-1 z-10">
              {editingTask && editingTask.id ? 'Edit Task' : 'Add Task'}
            </h2>
            <TaskForm
              initialValues={editingTask || { projectId }}
              resources={users}
              tasks={projectTasks}
              projectCategories={project.categories}
              onSubmit={editingTask && editingTask.id ? handleUpdateTask : handleCreateTask}
              onCancel={() => {
                setIsTaskFormOpen(false);
                setEditingTask(null);
              }}
              onDelete={(taskId) => {
                if (confirm('Are you sure you want to delete this task?')) {
                  removeTask(taskId);
                  setIsTaskFormOpen(false);
                  setEditingTask(null);
                }
              }}
              canDelete={editingTask && editingTask.id ? canDeleteTask(editingTask) : false}
            />
          </div>
        </div>
      )}

      {/* Create Tasks with AI modal */}
      {isCreateTasksModalOpen && project && (
        <CreateTasksModal
          project={project}
          onClose={() => setIsCreateTasksModalOpen(false)}
          onTasksCreated={async () => {
            // Refresh the task list
            await refreshData();
            const filteredTasks = tasks.filter(t => t.projectId === projectId);
            setProjectTasks(filteredTasks);
          }}
        />
      )}

      {/* Task details modal */}
      {selectedTask && (
        <TaskDetailsModal
          task={selectedTask}
          onClose={() => setSelectedTask(null)}
          onEdit={() => {
            // Close the details modal and open the edit modal
            const taskToEdit = selectedTask;
            setSelectedTask(null);
            setEditingTask(taskToEdit);
            setIsTaskFormOpen(true);
          }}
        />
      )}
    </div>
  );
}