'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>older<PERSON>pen,
  FileText,
  ChevronRight,
  ChevronDown,
  Folder,
  File,
  Plus,
  Search,
  Sparkles,
  Code,
  BookOpen,
  Settings,
  RefreshCw,
  Edit
} from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { useAuth } from '../../app/context/AuthContext';
import { toast } from '../ui/use-toast';

interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  children?: FileSystemItem[];
  expanded?: boolean;
  // The 'selected' property from the original is not used in the state logic,
  // as selection is managed by the `selectedPaths` array. Removing for clarity.
}

// Helper function to recursively find all file paths within a directory item
const collectAllFilePaths = (item: FileSystemItem): string[] => {
  const paths: string[] = [];
  if (item.type === 'file') {
    paths.push(item.path);
  } else if (item.type === 'directory' && item.children) {
    for (const child of item.children) {
      paths.push(...collectAllFilePaths(child));
    }
  }
  return paths;
};

// Helper function to find a specific item in the file system tree by its path
const findItemInTree = (path: string, tree: FileSystemItem[]): FileSystemItem | null => {
  for (const item of tree) {
    if (item.path === path) {
      return item;
    }
    if (item.children) {
      const found = findItemInTree(path, item.children);
      if (found) return found;
    }
  }
  return null;
};


interface SystemsDocumentationTabProps {}

export default function SystemsDocumentationTab({}: SystemsDocumentationTabProps) {
  const { user } = useAuth();
  const [selectedPaths, setSelectedPaths] = useState<string[]>([]);
  const [documentTitle, setDocumentTitle] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<{
    stage: string;
    message: string;
    progress?: number;
  } | null>(null);
  const [fileSystemTree, setFileSystemTree] = useState<FileSystemItem[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPath, setCurrentPath] = useState('c:\\backup');
  const [customPath, setCustomPath] = useState('');
  const [showCustomPathInput, setShowCustomPathInput] = useState(false);
  const [isLoadingFileSystem, setIsLoadingFileSystem] = useState(false);

  // Load file system from API
  const loadFileSystem = async (path: string) => {
    setIsLoadingFileSystem(true);
    try {
      const response = await fetch(`/api/codebase-documentation/file-system?rootPath=${encodeURIComponent(path)}&maxDepth=3&includeHidden=false`);
      if (!response.ok) {
        throw new Error('Failed to load file system');
      }
      const data = await response.json();
      if (data.success) {
        setFileSystemTree(data.fileSystem || []);
        setCurrentPath(data.rootPath);
      } else {
        throw new Error(data.error || 'Failed to load file system');
      }
    } catch (error: any) {
      console.error('Error loading file system:', error);
      toast({
        title: "File System Error",
        description: error.message || "Failed to load file system",
        variant: "destructive"
      });
      // Fallback to empty tree
      setFileSystemTree([]);
    } finally {
      setIsLoadingFileSystem(false);
    }
  };

  // Load initial file system
  useEffect(() => {
    loadFileSystem(currentPath);
  }, []);

  // Handle path change
  const handlePathChange = (newPath: string) => {
    setCurrentPath(newPath);
    loadFileSystem(newPath);
  };

  // Handle custom path submission
  const handleCustomPathSubmit = () => {
    if (customPath.trim()) {
      handlePathChange(customPath.trim());
      setShowCustomPathInput(false);
      setCustomPath('');
    }
  };

  const toggleExpanded = (path: string) => {
    const updateTree = (items: FileSystemItem[]): FileSystemItem[] => {
      return items.map(item => {
        if (item.path === path) {
          return { ...item, expanded: !item.expanded };
        }
        if (item.children) {
          return { ...item, children: updateTree(item.children) };
        }
        return item;
      });
    };
    setFileSystemTree(updateTree(fileSystemTree));
  };

  const toggleSelected = (path: string) => {
    setSelectedPaths(prev => 
      prev.includes(path) 
        ? prev.filter(p => p !== path)
        : [...prev, path]
    );
  };

  const renderFileSystemItem = (item: FileSystemItem, level: number = 0) => {
    const isSelected = selectedPaths.includes(item.path);
    const hasChildren = item.children && item.children.length > 0;
    
    return (
      <div key={item.path} className="select-none">
        <div 
          className={`flex items-center py-1 px-2 hover:bg-gray-700/50 cursor-pointer rounded ${
            isSelected ? 'bg-purple-600/20 border-l-2 border-purple-500' : ''
          }`}
          style={{ paddingLeft: `${level * 20 + 8}px` }}
          onClick={() => toggleSelected(item.path)}
        >
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(item.path);
              }}
              className="mr-1 p-0.5 hover:bg-gray-600 rounded"
            >
              {item.expanded ? (
                <ChevronDown className="w-3 h-3 text-gray-400" />
              ) : (
                <ChevronRight className="w-3 h-3 text-gray-400" />
              )}
            </button>
          )}
          
          {!hasChildren && <div className="w-4 mr-1" />}
          
          {item.type === 'directory' ? (
            <Folder className="w-4 h-4 text-blue-400 mr-2" />
          ) : (
            <File className="w-4 h-4 text-gray-400 mr-2" />
          )}
          
          <span className={`text-sm ${isSelected ? 'text-purple-200' : 'text-gray-300'}`}>
            {item.name}
          </span>
        </div>
        
        {hasChildren && item.expanded && item.children && (
          <div>
            {item.children.map(child => renderFileSystemItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const handleSubmit = async () => {
    if (!user?.email) {
      toast({
        title: "Authentication Required",
        description: "Please log in to submit documentation requests.",
        variant: "destructive"
      });
      return;
    }

    if (!documentTitle.trim()) {
      toast({
        title: "Missing Document Title",
        description: "Please provide a title for the documentation.",
        variant: "destructive"
      });
      return;
    }

    if (selectedPaths.length === 0) {
      toast({
        title: "No Paths Selected",
        description: "Please select at least one file or directory to analyze.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    setIsGenerating(true);
    setGenerationProgress({
      stage: 'initializing',
      message: 'Preparing documentation request...'
    });

    try {
      // Expand directories into a full list of file paths
      const expandedPathSet = new Set<string>();
      for (const selectedPath of selectedPaths) {
        const item = findItemInTree(selectedPath, fileSystemTree);
        if (item) {
          const files = collectAllFilePaths(item);
          files.forEach(file => expandedPathSet.add(file));
        } else {
          // If it's not in the tree, it might be a file selected from a previous state, assume it's a file path
          expandedPathSet.add(selectedPath);
        }
      }
      const finalPaths = Array.from(expandedPathSet);
      
      if (finalPaths.length === 0) {
        toast({ title: "No Files Found", description: "The selected directories contain no files to analyze.", variant: "destructive" });
        setIsSubmitting(false);
        setIsGenerating(false);
        return;
      }

      // Generate codebase analysis with streaming
      const requestBody = {
        userId: user.email,
        rootPath: currentPath,
        selectedPaths: finalPaths, // Use the corrected, expanded list
        description: `Comprehensive codebase analysis for: ${documentTitle.trim()}`,
        customContext: undefined,
        category: documentTitle.trim(),
        documentationScope: 'full',
        outputFormat: 'markdown',
        includeArchitecture: true,
        includeApiDocs: true,
        includeDataFlow: true
      };

      console.log('✅ Frontend sending CORRECTED request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch('/api/codebase-documentation/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error('Failed to start documentation generation');
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('Failed to read response stream');
      }

      let finalResult: any = null;

      while (true) {
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));

              setGenerationProgress({
                stage: data.stage,
                message: data.message,
                progress: data.progress
              });

              if (data.stage === 'complete') {
                finalResult = data.data;
              } else if (data.stage === 'error') {
                throw new Error(data.error || 'Documentation generation failed');
              }
            } catch (parseError) {
              console.warn('Failed to parse streaming data:', parseError);
            }
          }
        }
      }

      toast({
        title: "Documentation Generated Successfully",
        description: finalResult?.pmoRecordId
          ? `Your codebase documentation has been generated. PMO Record ID: ${finalResult.pmoRecordId}`
          : `Your codebase documentation has been generated successfully.`,
      });

      // Reset form
      setSelectedPaths([]);

      // Clear progress after a short delay
      setTimeout(() => {
        setGenerationProgress(null);
        setIsGenerating(false);
      }, 2000);

    } catch (error: any) {
      console.error('Error generating documentation:', error);
      toast({
        title: "Documentation Generation Failed",
        description: error.message || "Failed to generate documentation.",
        variant: "destructive"
      });
      setGenerationProgress(null);
      setIsGenerating(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-orange-600/20 rounded-lg">
            <BookOpen className="w-6 h-6 text-orange-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white">Systems Documentation</h2>
            <p className="text-gray-400 text-sm">Generate comprehensive documentation for your codebase</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* File System Browser */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-medium text-white flex items-center">
                <FolderOpen className="w-5 h-5 mr-2 text-orange-400" />
                Codebase Explorer
              </h3>
              <Button
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => loadFileSystem(currentPath)}
                disabled={isLoadingFileSystem}
              >
                <RefreshCw className={`w-3 h-3 mr-1 ${isLoadingFileSystem ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>

            {/* Path Selection Controls */}
            <div className="mb-4 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-300">Current Path:</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCustomPathInput(!showCustomPathInput)}
                  className="text-orange-400 hover:text-orange-300 hover:bg-gray-700 text-xs h-auto py-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Change
                </Button>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <span>📁</span>
                <span className="font-mono text-gray-300 bg-gray-900 px-2 py-1 rounded border border-gray-600 flex-1 text-xs">
                  {currentPath}
                </span>
              </div>

              {/* Quick Drive Selection */}
              <div className="flex flex-wrap gap-1">
                <span className="text-xs text-gray-400 mr-2">Quick:</span>
                {['C:\\', 'D:\\', 'E:\\'].map((drive) => (
                  <Button
                    key={drive}
                    variant="outline"
                    size="sm"
                    onClick={() => handlePathChange(drive)}
                    className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 px-2 py-1 h-auto"
                    disabled={isLoadingFileSystem}
                  >
                    {drive}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePathChange('c:\\backup')}
                  className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 px-2 py-1 h-auto"
                  disabled={isLoadingFileSystem}
                >
                  Backup
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePathChange('c:\\backup\\ike-project 9.4')}
                  className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 px-2 py-1 h-auto"
                  disabled={isLoadingFileSystem}
                >
                  Project
                </Button>
              </div>

              {/* Custom Path Input */}
              {showCustomPathInput && (
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={customPath}
                    onChange={(e) => setCustomPath(e.target.value)}
                    placeholder="Enter custom path (e.g., C:\Users\<USER>\Projects)"
                    className="flex-1 bg-gray-900 border border-gray-600 rounded px-3 py-2 text-sm text-gray-300 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleCustomPathSubmit();
                      }
                    }}
                  />
                  <Button
                    onClick={handleCustomPathSubmit}
                    disabled={!customPath.trim() || isLoadingFileSystem}
                    className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-2 text-xs"
                  >
                    Go
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowCustomPathInput(false);
                      setCustomPath('');
                    }}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700 px-3 py-2 text-xs"
                  >
                    Cancel
                  </Button>
                </div>
              )}

              {/* Loading Indicator */}
              {isLoadingFileSystem && (
                <div className="flex items-center gap-2 text-sm text-orange-400">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-400"></div>
                  <span>Loading file system...</span>
                </div>
              )}
            </div>

            <Input
              placeholder="Search files and directories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400"
            />
          </div>
          
          <div className="p-4 max-h-96 overflow-y-auto">
            {fileSystemTree.map(item => renderFileSystemItem(item))}
          </div>
          
          {selectedPaths.length > 0 && (
            <div className="p-4 border-t border-gray-700">
              <p className="text-sm text-gray-400 mb-2">Selected paths ({selectedPaths.length}):</p>
              <div className="space-y-1 max-h-20 overflow-y-auto">
                {selectedPaths.map(path => (
                  <div key={path} className="text-xs text-purple-300 bg-purple-900/20 px-2 py-1 rounded">
                    {path}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Documentation Request Form */}
        <div className="bg-gray-800 rounded-lg border border-gray-700">
          <div className="p-4 border-b border-gray-700">
            <h3 className="text-lg font-medium text-white flex items-center">
              <Code className="w-5 h-5 mr-2 text-orange-400" />
              Documentation Request
            </h3>
          </div>
          
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Document Title <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                placeholder="Enter a title for this documentation (will be used as category)"
                value={documentTitle}
                onChange={(e) => setDocumentTitle(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
            </div>


            
            {/* Progress Indicator */}
            {generationProgress && (
              <div className="mb-4 p-4 bg-gradient-to-r from-gray-700 to-gray-600 rounded-lg border border-orange-500/30 shadow-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-white">
                    {generationProgress.message}
                  </span>
                  {generationProgress.progress !== undefined && (
                    <span className="text-sm font-semibold text-orange-300">
                      {generationProgress.progress}%
                    </span>
                  )}
                </div>
                {generationProgress.progress !== undefined && (
                  <div className="w-full bg-gray-800 rounded-full h-3 mb-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-orange-500 to-orange-400 h-3 rounded-full transition-all duration-700 ease-out shadow-sm"
                      style={{ width: `${generationProgress.progress}%` }}
                    />
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Settings className="w-4 h-4 mr-2 animate-spin text-orange-400" />
                    <span className="text-xs text-gray-300 capitalize font-medium">
                      {generationProgress.stage.replace('-', ' ')}
                    </span>
                  </div>
                  {generationProgress.stage === 'complete' && (
                    <div className="flex items-center text-green-400">
                      <Sparkles className="w-4 h-4 mr-1" />
                      <span className="text-xs font-medium">Complete!</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || isGenerating || selectedPaths.length === 0 || !documentTitle.trim()}
              className="w-full bg-orange-600 hover:bg-orange-700 text-white disabled:opacity-50"
            >
              {isSubmitting || isGenerating ? (
                <>
                  <Settings className="w-4 h-4 mr-2 animate-spin" />
                  {isGenerating ? 'Generating Analysis...' : 'Submitting Request...'}
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Codebase Analysis
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}