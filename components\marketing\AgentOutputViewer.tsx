'use client';

import { useState } from 'react';
import { Card, CardContent } from 'components/ui/card';
import { Button } from 'components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from 'components/ui/dialog';
import { FileText, BarChart2, Image, Maximize2 } from 'lucide-react';
import MarkdownRenderer from '../MarkdownRenderer';

interface AgentOutput {
  id: string;
  agentId: string;
  type: 'document' | 'chart' | 'image' | 'text';
  title: string;
  content: any;
  timestamp: Date;
}

interface AgentOutputViewerProps {
  output: AgentOutput;
}

const AgentOutputViewer = ({ output }: AgentOutputViewerProps) => {
  const [isOpen, setIsOpen] = useState(false);

  // Render document output
  const renderDocument = (content: any) => {
    return (
      <div className="space-y-2">
        {content.productName && (
          <div>
            <h4 className="font-semibold text-sm">Product Name</h4>
            <p className="text-sm">{content.productName}</p>
          </div>
        )}

        {content.uniqueSellingPoints && (
          <div>
            <h4 className="font-semibold text-sm">Unique Selling Points</h4>
            <ul className="list-disc pl-5 text-sm">
              {content.uniqueSellingPoints.slice(0, 3).map((point: string, idx: number) => (
                <li key={idx}>{point}</li>
              ))}
              {content.uniqueSellingPoints.length > 3 && (
                <li>...and {content.uniqueSellingPoints.length - 3} more</li>
              )}
            </ul>
          </div>
        )}

        {content.keyMessages && (
          <div>
            <h4 className="font-semibold text-sm">Key Messages</h4>
            <ul className="list-disc pl-5 text-sm">
              {content.keyMessages.slice(0, 3).map((message: string, idx: number) => (
                <li key={idx}>{message}</li>
              ))}
              {content.keyMessages.length > 3 && (
                <li>...and {content.keyMessages.length - 3} more</li>
              )}
            </ul>
          </div>
        )}

        {content.contentThemes && (
          <div>
            <h4 className="font-semibold text-sm">Content Themes</h4>
            <p className="text-sm">{content.contentThemes.slice(0, 3).join(', ')}{content.contentThemes.length > 3 ? '...' : ''}</p>
          </div>
        )}

        {content.swotAnalysis && (
          <div>
            <h4 className="font-semibold text-sm">SWOT Analysis</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <p className="font-medium">Strengths</p>
                <p>{content.swotAnalysis.strengths.slice(0, 2).join(', ')}{content.swotAnalysis.strengths.length > 2 ? '...' : ''}</p>
              </div>
              <div>
                <p className="font-medium">Weaknesses</p>
                <p>{content.swotAnalysis.weaknesses.slice(0, 2).join(', ')}{content.swotAnalysis.weaknesses.length > 2 ? '...' : ''}</p>
              </div>
              <div>
                <p className="font-medium">Opportunities</p>
                <p>{content.swotAnalysis.opportunities.slice(0, 2).join(', ')}{content.swotAnalysis.opportunities.length > 2 ? '...' : ''}</p>
              </div>
              <div>
                <p className="font-medium">Threats</p>
                <p>{content.swotAnalysis.threats.slice(0, 2).join(', ')}{content.swotAnalysis.threats.length > 2 ? '...' : ''}</p>
              </div>
            </div>
          </div>
        )}

        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2 bg-purple-600 text-white hover:bg-purple-700 border-none shadow-sm"
          onClick={() => setIsOpen(true)}
        >
          <Maximize2 className="h-3 w-3 mr-1" />
          View Full Document
        </Button>
      </div>
    );
  };

  // Render chart output
  const renderChart = (content: any) => {
    // In a real implementation, we would use a chart library like Chart.js or Recharts
    // For this example, we'll just render a placeholder
    return (
      <div className="space-y-2">
        <div className="aspect-video bg-gray-700/50 rounded-md flex items-center justify-center">
          <BarChart2 className="h-8 w-8 text-blue-300" />
        </div>
        <p className="text-xs text-center text-gray-400">
          {content.chartType} chart with {content.data?.length || 0} data points
        </p>
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2 bg-purple-600 text-white hover:bg-purple-700 border-none shadow-sm"
          onClick={() => setIsOpen(true)}
        >
          <Maximize2 className="h-3 w-3 mr-1" />
          View Full Chart
        </Button>
      </div>
    );
  };

  // Render image output
  const renderImage = (content: any) => {
    return (
      <div className="space-y-2">
        <div className="aspect-video bg-gray-700/50 rounded-md overflow-hidden">
          {content.imageUrl ? (
            <img
              src={content.imageUrl}
              alt={content.altText || 'Agent output image'}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Image className="h-8 w-8 text-green-300" />
            </div>
          )}
        </div>
        <p className="text-xs text-center text-gray-400">
          {content.altText || 'Image generated by agent'}
        </p>
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2 bg-purple-600 text-white hover:bg-purple-700 border-none shadow-sm"
          onClick={() => setIsOpen(true)}
        >
          <Maximize2 className="h-3 w-3 mr-1" />
          View Full Image
        </Button>
      </div>
    );
  };

  // Render text output
  const renderText = (content: string) => {
    // Check if content is likely a research report (contains specific sections)
    const isResearchReport = typeof content === 'string' &&
      (content.includes('**This response was generated by the **Research & Insights Agent**') ||
       content.includes('# Research Report') ||
       content.includes('## Executive Summary'));

    return (
      <div className="space-y-2">
        {isResearchReport ? (
          // For research reports, show the full content without truncation
          <div className="text-sm max-h-[600px] overflow-y-auto custom-scrollbar">
            <MarkdownRenderer content={content} />
          </div>
        ) : (
          // For other text, keep the line clamp but increase to 10 lines
          <p className="text-sm line-clamp-10">{content}</p>
        )}
        <Button
          variant="outline"
          size="sm"
          className="w-full mt-2 bg-purple-600 text-white hover:bg-purple-700 border-none shadow-sm"
          onClick={() => setIsOpen(true)}
        >
          <Maximize2 className="h-3 w-3 mr-1" />
          View Full Text
        </Button>
      </div>
    );
  };

  // Render full document in dialog
  const renderFullDocument = (content: any) => {
    return (
      <div className="space-y-4 max-h-[70vh] overflow-y-auto p-4">
        {content.productName && (
          <div>
            <h3 className="text-lg font-semibold">Product Name</h3>
            <p>{content.productName}</p>
          </div>
        )}

        {content.uniqueSellingPoints && (
          <div>
            <h3 className="text-lg font-semibold">Unique Selling Points</h3>
            <ul className="list-disc pl-5">
              {content.uniqueSellingPoints.map((point: string, idx: number) => (
                <li key={idx}>{point}</li>
              ))}
            </ul>
          </div>
        )}

        {content.targetMarket && (
          <div>
            <h3 className="text-lg font-semibold">Target Market</h3>
            <p>{typeof content.targetMarket === 'string' ? content.targetMarket : content.targetMarket.join(', ')}</p>
          </div>
        )}

        {content.keyMessages && (
          <div>
            <h3 className="text-lg font-semibold">Key Messages</h3>
            <ul className="list-disc pl-5">
              {content.keyMessages.map((message: string, idx: number) => (
                <li key={idx}>{message}</li>
              ))}
            </ul>
          </div>
        )}

        {content.contentThemes && (
          <div>
            <h3 className="text-lg font-semibold">Content Themes</h3>
            <ul className="list-disc pl-5">
              {content.contentThemes.map((theme: string, idx: number) => (
                <li key={idx}>{theme}</li>
              ))}
            </ul>
          </div>
        )}

        {content.contentTypes && (
          <div>
            <h3 className="text-lg font-semibold">Content Types</h3>
            <ul className="list-disc pl-5">
              {content.contentTypes.map((type: string, idx: number) => (
                <li key={idx}>{type}</li>
              ))}
            </ul>
          </div>
        )}

        {content.swotAnalysis && (
          <div>
            <h3 className="text-lg font-semibold">SWOT Analysis</h3>
            <div className="grid grid-cols-2 gap-4 mt-2">
              <Card className="bg-gray-800/30 border-gray-700/30">
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-2 text-blue-300">Strengths</h4>
                  <ul className="list-disc pl-5">
                    {content.swotAnalysis.strengths.map((item: string, idx: number) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
              <Card className="bg-gray-800/30 border-gray-700/30">
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-2 text-red-300">Weaknesses</h4>
                  <ul className="list-disc pl-5">
                    {content.swotAnalysis.weaknesses.map((item: string, idx: number) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
              <Card className="bg-gray-800/30 border-gray-700/30">
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-2 text-green-300">Opportunities</h4>
                  <ul className="list-disc pl-5">
                    {content.swotAnalysis.opportunities.map((item: string, idx: number) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
              <Card className="bg-gray-800/30 border-gray-700/30">
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-2 text-orange-300">Threats</h4>
                  <ul className="list-disc pl-5">
                    {content.swotAnalysis.threats.map((item: string, idx: number) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render full chart in dialog
  const renderFullChart = (content: any) => {
    // In a real implementation, we would use a chart library like Chart.js or Recharts
    // For this example, we'll just render a placeholder
    return (
      <div className="space-y-4">
        <div className="aspect-[4/3] bg-gray-700/50 rounded-md flex items-center justify-center">
          <BarChart2 className="h-16 w-16 text-blue-300" />
        </div>
        <div>
          <h3 className="text-lg font-semibold">Chart Data</h3>
          <div className="mt-2 border border-gray-600/50 rounded-md overflow-hidden">
            <table className="w-full text-sm">
              <thead className="bg-gray-700/50">
                <tr>
                  <th className="px-4 py-2 text-left text-gray-300">Label</th>
                  <th className="px-4 py-2 text-right text-gray-300">Value</th>
                  {content.data?.[0]?.target !== undefined && (
                    <th className="px-4 py-2 text-right text-gray-300">Target</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {content.data?.map((item: any, idx: number) => (
                  <tr key={idx} className="border-t border-gray-600/50">
                    <td className="px-4 py-2">{item.label}</td>
                    <td className="px-4 py-2 text-right text-blue-300">{item.value}</td>
                    {item.target !== undefined && (
                      <td className="px-4 py-2 text-right text-green-300">{item.target}</td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  };

  // Render full image in dialog
  const renderFullImage = (content: any) => {
    return (
      <div className="space-y-4">
        <div className="rounded-md overflow-hidden">
          {content.imageUrl ? (
            <img
              src={content.imageUrl}
              alt={content.altText || 'Agent output image'}
              className="w-full h-auto"
            />
          ) : (
            <div className="aspect-video bg-gray-700/50 flex items-center justify-center">
              <Image className="h-16 w-16 text-green-300" />
            </div>
          )}
        </div>
        {content.altText && (
          <p className="text-sm text-center text-muted-foreground">
            {content.altText}
          </p>
        )}
      </div>
    );
  };

  // Render output based on type
  const renderOutput = () => {
    switch (output.type) {
      case 'document':
        return renderDocument(output.content);
      case 'chart':
        return renderChart(output.content);
      case 'image':
        return renderImage(output.content);
      case 'text':
        return renderText(output.content);
      default:
        return <p>Unsupported output type</p>;
    }
  };

  // Render full output in dialog based on type
  const renderFullOutput = () => {
    switch (output.type) {
      case 'document':
        return renderFullDocument(output.content);
      case 'chart':
        return renderFullChart(output.content);
      case 'image':
        return renderFullImage(output.content);
      case 'text':
        // Check if content is likely a research report
        const isResearchReport = typeof output.content === 'string' &&
          (output.content.includes('**This response was generated by the **Research & Insights Agent**') ||
           output.content.includes('# Research Report') ||
           output.content.includes('## Executive Summary'));

        return isResearchReport ?
          <div className="p-4 max-h-[70vh] overflow-y-auto">
            <MarkdownRenderer content={output.content} />
          </div> :
          <p className="p-4">{output.content}</p>;
      default:
        return <p>Unsupported output type</p>;
    }
  };

  return (
    <>
      {renderOutput()}

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-3xl bg-gray-800/90 border-gray-700/50 text-gray-100">
          <DialogHeader>
            <DialogTitle className="text-purple-300">{output.title}</DialogTitle>
          </DialogHeader>
          {renderFullOutput()}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AgentOutputViewer;
