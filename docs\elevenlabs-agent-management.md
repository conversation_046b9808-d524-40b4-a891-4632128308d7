# ElevenLabs Agent Management System

This document describes the enhanced ElevenLabs agent management system that prevents duplicate agents and provides proper knowledge base management.

## Overview

The system implements a complete workflow for managing ElevenLabs conversational AI agents:

1. **Agent Existence Checking** - Prevents duplicate agents by checking both database and ElevenLabs
2. **Knowledge Base Management** - Handles document uploads with deduplication
3. **RAG Indexing** - Ensures documents are properly indexed for retrieval
4. **Agent Association** - Links documents to agents for conversational AI

## Key Components

### 1. User Agent Storage (`lib/firebase/userAgents.ts`)

Manages the relationship between users and their ElevenLabs agents in Firebase:

```typescript
interface UserAgent {
  id: string;
  userId: string;
  agentId: string; // ElevenLabs agent ID
  agentName: string;
  voiceId: string;
  status: 'active' | 'inactive' | 'error';
  createdAt: Date;
  updatedAt: Date;
  lastVerified?: Date;
  knowledgeBaseId?: string;
  conversationId?: string;
  metadata?: Record<string, any>;
}
```

### 2. Agent Validation (`lib/elevenlabs/agentValidation.ts`)

Provides utilities for checking agent existence and validating configuration:

- `checkUserAgentExists()` - Complete existence checking flow
- `verifyAgentInElevenLabs()` - Verify agent exists in ElevenLabs
- `validateAndFixUserAgentConfig()` - Ensure proper WebSocket configuration

### 3. Knowledge Base Upload (`components/scriptreaderAI/uploadKnowledgebase.ts`)

Enhanced document upload with deduplication:

- `uploadToKnowledgeBaseWithDeduplication()` - Upload with duplicate checking
- `computeRagIndex()` - RAG indexing with polling
- `checkDocumentExists()` - Check for existing documents

## API Endpoints

### Create Agent: `POST /api/elevenlabs/create-agent`

Enhanced to implement the complete existence checking flow:

```typescript
// Request
{
  "agentId": "user-pmo-agent",
  "name": "PMO Assistant",
  "voiceId": "voice_id_here",
  "prompt": "You are a PMO assistant...",
  "knowledgeBase": [...]
}

// Response (existing agent found)
{
  "success": true,
  "agentId": "existing_agent_id",
  "message": "Existing agent found and validated",
  "wasExisting": true,
  "configurationIssues": [],
  "configurationFixed": false
}

// Response (new agent created)
{
  "success": true,
  "agentId": "new_agent_id",
  "message": "PMO agent created successfully",
  "wasExisting": false,
  "userAgentId": "database_record_id"
}
```

### Upload Document: `POST /api/elevenlabs/upload-document`

Complete workflow for document upload and indexing:

```typescript
// Request
{
  "fileUrl": "https://example.com/document.pdf",
  "fileName": "project-requirements.pdf",
  "fileType": "application/pdf",
  "forceUpload": false,
  "forceReindex": false
}

// Response
{
  "success": true,
  "message": "Document uploaded, indexed, and associated with your agent successfully",
  "documentId": "doc_id_here",
  "wasExisting": false,
  "ragIndexed": true,
  "agentUpdated": true,
  "agentId": "agent_id_here"
}
```

### Check Document Status: `GET /api/elevenlabs/upload-document?fileName=document.pdf`

Check if a document is already indexed for the user's agent:

```typescript
// Response
{
  "fileName": "document.pdf",
  "agentId": "agent_id_here",
  "isIndexed": true,
  "userAgent": {
    "id": "user_agent_record_id",
    "agentName": "PMO Assistant",
    "documentsUploaded": 5,
    "lastKnowledgeBaseUpdate": "2025-01-19T10:30:00Z"
  }
}
```

## Usage Examples

### 1. Create or Get Agent

```typescript
const response = await fetch('/api/elevenlabs/create-agent', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    agentId: 'user-pmo-agent',
    name: 'PMO Assistant',
    voiceId: 'voice_id_here',
    prompt: 'You are a helpful PMO assistant...'
  })
});

const result = await response.json();

if (result.wasExisting) {
  console.log('Using existing agent:', result.agentId);
} else {
  console.log('Created new agent:', result.agentId);
}
```

### 2. Upload Document with Deduplication

```typescript
const uploadResponse = await fetch('/api/elevenlabs/upload-document', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    fileUrl: 'https://example.com/document.pdf',
    fileName: 'project-requirements.pdf',
    fileType: 'application/pdf'
  })
});

const uploadResult = await uploadResponse.json();

if (uploadResult.wasExisting) {
  console.log('Document already existed in knowledge base');
} else {
  console.log('Document uploaded and indexed successfully');
}
```

### 3. Check Document Status

```typescript
const statusResponse = await fetch('/api/elevenlabs/upload-document?fileName=document.pdf');
const status = await statusResponse.json();

if (status.isIndexed) {
  console.log('Document is already indexed for your agent');
} else {
  console.log('Document needs to be uploaded');
}
```

## Benefits

### 1. Prevents Duplicate Agents
- Checks database for existing agent records
- Verifies agents still exist in ElevenLabs
- Returns existing agent if valid, creates new one only if needed

### 2. Document Deduplication
- Checks for existing documents before upload
- Prevents duplicate documents in knowledge base
- Tracks document indexing status per agent

### 3. Proper Configuration
- Validates agent configuration for WebSocket communication
- Automatically fixes common configuration issues
- Ensures text response transmission works properly

### 4. Comprehensive Tracking
- Stores agent relationships in database
- Tracks document upload history
- Provides status checking endpoints

## Error Handling

The system includes comprehensive error handling:

- **Database Errors**: Continue operation, log errors
- **ElevenLabs API Errors**: Provide detailed error messages
- **Configuration Issues**: Attempt automatic fixes
- **Upload Failures**: Retry logic and fallback options

## Future Enhancements

1. **Batch Document Upload**: Support multiple document uploads
2. **Document Versioning**: Track document versions and updates
3. **Agent Templates**: Pre-configured agent templates for different use cases
4. **Analytics**: Track usage patterns and performance metrics
5. **Webhook Integration**: Real-time notifications for agent events

## Troubleshooting

### Common Issues

1. **Agent Not Found**: Check if agent was deleted from ElevenLabs
2. **Upload Failures**: Verify file URL accessibility and format support
3. **RAG Indexing Timeout**: Increase timeout or check document size
4. **Configuration Issues**: Check API key and permissions

### Debug Logging

All operations include detailed console logging with `[ELEVENLABS]` prefix for easy filtering and debugging.
