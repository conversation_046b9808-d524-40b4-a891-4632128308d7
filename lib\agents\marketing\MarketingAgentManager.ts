/**
 * Marketing Agent Manager
 *
 * Manages and coordinates all marketing agents in the system.
 * Provides a central point for agent creation, communication, and task management.
 */

import { MarketingAgent } from './MarketingAgent';
import { StrategicDirectorAgent } from './StrategicDirectorAgent';
import { ResearchInsightsAgent } from './ResearchInsightsAgent';
import { ContentCreatorAgent } from './ContentCreatorAgent';
import { SocialMediaOrchestratorAgent } from './SocialMediaOrchestratorAgent';
import { AnalyticsReportingAgent } from './AnalyticsReportingAgent';
import { AgentMemoryManager } from '../AgentMemoryManager';
import { LlmProvider } from '../../tools/llm-tool';

export interface MarketingAgentTeam {
  strategicDirector: StrategicDirectorAgent;
  researchInsights: ResearchInsightsAgent;
  contentCreator: ContentCreatorAgent;
  socialMediaOrchestrator: SocialMediaOrchestratorAgent;
  analyticsReporting: AnalyticsReportingAgent;
}

export interface MarketingAgentManagerConfig {
  userId: string;
  defaultLlmProvider?: LlmProvider;
  defaultLlmModel?: string;
}

/**
 * Manages and coordinates all marketing agents
 */
export class MarketingAgentManager {
  private agents: Map<string, MarketingAgent> = new Map();
  private memoryManager: AgentMemoryManager;
  private userId: string;
  private defaultLlmProvider: LlmProvider;
  private defaultLlmModel: string;

  constructor(config: MarketingAgentManagerConfig) {
    this.userId = config.userId;
    this.defaultLlmProvider = config.defaultLlmProvider || 'openai';
    this.defaultLlmModel = config.defaultLlmModel || 'gpt-4o';
    this.memoryManager = new AgentMemoryManager(this.userId);
  }

  /**
   * Initialize the marketing agent team
   */
  async initializeMarketingTeam(): Promise<MarketingAgentTeam> {
    // Create Strategic Director Agent
    const strategicDirector = new StrategicDirectorAgent(
      'strategic-director',
      'Strategic Director',
      this.userId,
      this.defaultLlmProvider,
      this.defaultLlmModel
    );

    // Create Research & Insights Agent
    const researchInsights = new ResearchInsightsAgent(
      'research-insights',
      'Research & Insights',
      this.userId,
      this.defaultLlmProvider,
      this.defaultLlmModel
    );

    // Create Content Creator Agent
    const contentCreator = new ContentCreatorAgent(
      'content-creator',
      'Content Creator',
      this.userId,
      this.defaultLlmProvider,
      this.defaultLlmModel
    );

    // Create Social Media Orchestrator Agent
    const socialMediaOrchestrator = new SocialMediaOrchestratorAgent(
      'social-media-orchestrator',
      'Social Media Orchestrator',
      this.userId,
      this.defaultLlmProvider,
      this.defaultLlmModel
    );

    // Create Analytics & Reporting Agent
    const analyticsReporting = new AnalyticsReportingAgent(
      'analytics-reporting',
      'Analytics & Reporting',
      this.userId,
      this.defaultLlmProvider,
      this.defaultLlmModel
    );

    // Register all agents
    this.registerAgent(strategicDirector);
    this.registerAgent(researchInsights);
    this.registerAgent(contentCreator);
    this.registerAgent(socialMediaOrchestrator);
    this.registerAgent(analyticsReporting);

    return {
      strategicDirector,
      researchInsights,
      contentCreator,
      socialMediaOrchestrator,
      analyticsReporting
    };
  }

  /**
   * Register an agent with the manager
   */
  registerAgent(agent: MarketingAgent): void {
    const agentInfo = agent.getInfo();
    this.agents.set(agentInfo.id, agent);
    console.log(`Agent registered: ${agentInfo.name} (${agentInfo.id})`);
  }

  /**
   * Get an agent by ID
   */
  getAgent(agentId: string): MarketingAgent | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Get all registered agents
   */
  getAllAgents(): MarketingAgent[] {
    return Array.from(this.agents.values());
  }

  /**
   * Send a message from one agent to another
   */
  async sendMessage(fromAgentId: string, toAgentId: string, content: string, metadata?: Record<string, any>): Promise<void> {
    const fromAgent = this.agents.get(fromAgentId);

    if (!fromAgent) {
      throw new Error(`Agent with ID ${fromAgentId} not found`);
    }

    await fromAgent.sendMessage(toAgentId, content, metadata);
  }

  /**
   * Process messages for all agents
   */
  async processMessages(): Promise<void> {
    for (const agent of this.agents.values()) {
      const agentInfo = agent.getInfo();
      const messages = await agent.receiveMessages();

      console.log(`Processing ${messages.length} messages for agent ${agentInfo.name}`);

      for (const message of messages) {
        // Process each message
        const response = await agent.processRequest(message.content);

        // Send response back to the sender if needed
        if (message.metadata?.requiresResponse) {
          await agent.sendMessage(message.from, response, {
            inResponseTo: message.metadata?.messageId,
            isResponse: true
          });
        }
      }
    }
  }

  /**
   * Create a new marketing campaign
   */
  async createMarketingCampaign(
    productName: string,
    productDescription: string,
    campaignObjectives: string[],
    targetAudience: string,
    _budget: number, // Using underscore prefix to indicate it's not used
    _duration: number // in days, using underscore prefix to indicate it's not used
  ): Promise<string> {
    const strategicDirector = this.agents.get('strategic-director') as StrategicDirectorAgent;

    if (!strategicDirector) {
      throw new Error('Strategic Director agent not found');
    }

    // Step 1: Analyze the product
    const productAnalysis = await strategicDirector.analyzeProduct(
      productName,
      productDescription,
      `Target audience: ${targetAudience}`
    );

    // Step 2: Research the market
    const researchAgent = this.agents.get('research-insights') as ResearchInsightsAgent;
    const marketResearch = await researchAgent.conductMarketResearch(
      productName,
      targetAudience,
      productAnalysis.competitiveAdvantages.join(', ')
    );

    // Step 3: Generate marketing strategy
    // Note: generateMarketingStrategy has been commented out to reduce complexity
    // Using generateLongFormDocument instead
    const strategyContent = await strategicDirector.generateLongFormDocument(
      `Marketing strategy for ${productName} based on product analysis and market research`,
      'markdown',
      `Campaign Objectives: ${campaignObjectives.join(', ')}\nProduct Analysis ID: ${productAnalysis.id}\nMarket Research: ${marketResearch.substring(0, 500)}...`
    );

    // Create a strategy object with an ID that can be used by subsequent steps
    const strategy = {
      id: `strategy-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      name: `${productName} Marketing Strategy`,
      content: strategyContent,
      createdAt: new Date()
    };

    // Step 4: Coordinate tasks between agents
    await strategicDirector.coordinateAgentTasks(
      'research-insights',
      'content-creator',
      'social-media-orchestrator',
      'analytics-reporting',
      strategy.id
    );

    // Step 5: Generate strategy document
    // Note: generateStrategyDocument has been commented out to reduce complexity
    // Skipping this step as it's not essential for the core functionality

    // Return the strategy ID
    return strategy.id;
  }

  /**
   * Get the memory manager
   */
  getMemoryManager(): AgentMemoryManager {
    return this.memoryManager;
  }
}
