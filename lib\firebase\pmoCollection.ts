// lib/firebase/pmoCollection.ts
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db, retryOperation } from '../../app/lib/firebase/config';
import {
  PMORecord,
  PMORecordStatus,
  PMORecordPriority
} from '../agents/pmo/PMOInterfaces';
import { v4 as uuidv4 } from 'uuid';

// Helper function to convert Date objects to Firestore Timestamps
// and filter out undefined values which are not allowed in Firestore
const prepareForFirestore = (data: any): any => {
  if (data === undefined) return null; // Convert undefined to null for Firestore
  if (data === null) return null;

  if (Array.isArray(data)) {
    return data.map(item => prepareForFirestore(item));
  }

  if (data instanceof Date) {
    return Timestamp.fromDate(data);
  }

  if (typeof data === 'object') {
    const result: any = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const value = prepareForFirestore(data[key]);
        // Only add the property if it's not undefined
        if (value !== undefined) {
          result[key] = value;
        }
      }
    }
    return result;
  }

  return data;
};

// Helper function to convert Firestore Timestamps to Date objects
const convertTimestamps = (data: any): any => {
  if (!data) return data;

  if (Array.isArray(data)) {
    return data.map(item => convertTimestamps(item));
  }

  if (data instanceof Timestamp) {
    return data.toDate();
  }

  if (typeof data === 'object') {
    const result: any = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = convertTimestamps(data[key]);
      }
    }
    return result;
  }

  return data;
};

/**
 * Get PMO records for a specific user
 * @param userId - The user's email
 * @returns Array of PMO records
 */
export const getPMORecords = async (userId: string): Promise<PMORecord[]> => {
  try {
    return await retryOperation(async () => {
      if (!userId) {
        console.warn("getPMORecords: userId is undefined or null. Returning empty array.");
        return [];
      }

      console.log(`getPMORecords: Fetching records for user ${userId}`);

      // Get the PMO collection for this user
      const pmoCollectionRef = collection(db, 'users', userId, 'PMO');
      const recordsQuery = query(
        pmoCollectionRef,
        orderBy('createdAt', 'desc')
      );

      try {
        const snapshot = await getDocs(recordsQuery);
        console.log(`getPMORecords: Retrieved ${snapshot.docs.length} records`);

        const records = snapshot.docs.map(doc => {
          try {
            const data = doc.data();
            const convertedData = convertTimestamps(data);
            return {
              id: doc.id,
              ...convertedData
            } as PMORecord;
          } catch (docError) {
            console.error(`Error processing document ${doc.id}:`, docError);
            // Return a minimal valid record to prevent the entire operation from failing
            return {
              id: doc.id,
              title: `Error: Could not process record ${doc.id}`,
              description: 'There was an error processing this record.',
              status: 'Draft' as PMORecordStatus,
              priority: 'Medium' as PMORecordPriority,
              createdAt: new Date(),
              updatedAt: new Date(),
              createdBy: userId,
              projectIds: [],
              taskIds: [],
              category: 'Unknown',
              agentIds: [],
              dueDate: undefined,
              sourceFile: undefined,
              fileName: `Error Record ${doc.id}`,
              customContext: undefined,
              contextFiles: undefined,
              contextCategories: undefined
            } as PMORecord;
          }
        });

        return records;
      } catch (queryError: unknown) {
        console.error('Error executing Firestore query:', queryError);
        const errorMessage = queryError instanceof Error ? queryError.message : String(queryError);
        throw new Error(`Failed to query PMO records: ${errorMessage}`);
      }
    });
  } catch (error) {
    console.error('Error getting PMO records:', error);
    // Return an empty array instead of throwing to prevent UI from getting stuck
    return [];
  }
};

/**
 * Get a specific PMO record
 * @param userId - The user's email
 * @param pmoId - The PMO record ID
 * @returns The PMO record or null if not found
 */
export const getPMORecord = async (userId: string, pmoId: string): Promise<PMORecord | null> => {
  try {
    const docRef = doc(db, 'users', userId, 'PMO', pmoId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      const data = docSnap.data();
      const convertedData = convertTimestamps(data);
      return {
        id: docSnap.id,
        ...convertedData
      } as PMORecord;
    }

    return null;
  } catch (error) {
    console.error('Error getting PMO record:', error);
    throw error;
  }
};

/**
 * Add a new PMO record
 * @param userId - The user's email
 * @param recordData - The PMO record data
 * @returns The ID of the newly created PMO record
 */
export const addPMORecord = async (
  userId: string,
  recordData: Omit<PMORecord, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    // Generate a new UUID for the PMO record
    const pmoId = uuidv4();

    // Get the PMO collection for this user
    const pmoCollectionRef = collection(db, 'users', userId, 'PMO');

    // Create the document with the generated ID
    const docRef = doc(pmoCollectionRef, pmoId);

    // Prepare the data for Firestore
    const preparedData = {
      ...prepareForFirestore(recordData),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    // Set the document data
    await setDoc(docRef, preparedData);

    return pmoId;
  } catch (error) {
    console.error('Error adding PMO record:', error);
    throw error;
  }
};

/**
 * Update a PMO record
 * @param userId - The user's email
 * @param pmoId - The PMO record ID
 * @param recordData - The PMO record data to update
 */
export const updatePMORecord = async (
  userId: string,
  pmoId: string,
  recordData: Partial<PMORecord>
): Promise<void> => {
  try {
    const docRef = doc(db, 'users', userId, 'PMO', pmoId);

    // Prepare the data for Firestore
    const preparedData = {
      ...prepareForFirestore(recordData),
      updatedAt: serverTimestamp()
    };

    await updateDoc(docRef, preparedData);
  } catch (error) {
    console.error('Error updating PMO record:', error);
    throw error;
  }
};

/**
 * Delete a PMO record
 * @param userId - The user's email or record ID if filterFn is provided
 * @param pmoId - The PMO record ID or a filter function to find the record to delete
 */
export const deletePMORecord = async (
  userId: string,
  pmoIdOrFilterFn: string | ((record: PMORecord) => boolean)
): Promise<void> => {
  try {
    // If pmoIdOrFilterFn is a function, we need to get all records and find the one to delete
    if (typeof pmoIdOrFilterFn === 'function') {
      const records = await getPMORecords(userId);
      const recordToDelete = records.find(pmoIdOrFilterFn);

      if (!recordToDelete) {
        throw new Error('Record not found');
      }

      const docRef = doc(db, 'users', userId, 'PMO', recordToDelete.id);
      await deleteDoc(docRef);
    } else {
      // If pmoIdOrFilterFn is a string, it's the record ID
      const docRef = doc(db, 'users', userId, 'PMO', pmoIdOrFilterFn);
      await deleteDoc(docRef);
    }
  } catch (error) {
    console.error('Error deleting PMO record:', error);
    throw error;
  }
};

/**
 * Create a PMO record from UI form data
 * @param userId - The user's email
 * @param formData - The form data from the UI
 * @returns The ID of the newly created PMO record
 */
export const createPMORecordFromForm = async (
  userId: string,
  formData: {
    title: string;
    description: string;
    priority: PMORecordPriority;
    category?: string;
    sourceFile?: string;
    fileName?: string;
    customContext?: string;
    selectedFileId?: string;
    selectedCategory?: string;
    pmoAssessment?: string;
  }
): Promise<string> => {
  try {
    // Determine the main category - prioritize selectedCategory if it's a PMO format category
    let mainCategory = formData.category || 'Unknown';
    if (formData.selectedCategory) {
      // Check if the selected category follows PMO format (PMO - title - uuid)
      const pmoPattern = /^PMO\s*-\s*.+\s*-\s*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      if (pmoPattern.test(formData.selectedCategory)) {
        // Use the selected PMO category as the main category to avoid creating duplicates
        mainCategory = formData.selectedCategory;
      }
    }

    // Create the PMO record data
    const pmoRecord: Omit<PMORecord, 'id' | 'createdAt' | 'updatedAt'> = {
      title: formData.title,
      description: formData.description,
      status: 'Draft' as PMORecordStatus,
      priority: formData.priority,
      createdBy: userId,
      projectIds: [],
      taskIds: [],
      category: mainCategory,
      sourceFile: formData.sourceFile || null,
      fileName: formData.fileName || `${formData.title} - Requirements Specification`,
      customContext: formData.customContext || null,
      contextFiles: formData.selectedFileId ? [formData.selectedFileId] : null,
      contextCategories: formData.selectedCategory ? [formData.selectedCategory] : null,
      pmoAssessment: formData.pmoAssessment || null,
      agentIds: [],
      dueDate: null
    };

    // Add the PMO record
    return await addPMORecord(userId, pmoRecord);
  } catch (error) {
    console.error('Error creating PMO record from form:', error);
    throw error;
  }
};
