// app/api/preview/route.ts
import { NextResponse } from "next/server";
import { webScraperTool } from "lib/tools/web-scraper";

/**
 * API route handler for fetching preview content from URLs
 */
export async function POST(request: Request) {
  try {
    const { urls } = await request.json();
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return NextResponse.json(
        { error: "Invalid or missing URLs" },
        { status: 400 }
      );
    }
    // Process each URL and extract a preview snippet
    const contentPreviewMap: Record<string, string> = {};

    await Promise.all(
      urls.map(async (url) => {
        try {
          // Use the web scraper tool to fetch content
          const { content: rawContent, title } = await webScraperTool.scrapeContent(url);

          // Extract a preview snippet (first 1000 characters)
          // Using a simplified preview for demonstration
          let preview = rawContent.substring(0, 1000);

          // Clean up the preview text
          preview = preview
            .replace(/<[^>]*>/g, '') // Remove HTML tags
            .replace(/\s+/g, ' ')    // Replace multiple spaces with a single space
            .trim();                 // Trim whitespace

          // Add ellipsis if content was truncated
          if (rawContent.length > 1000) {
            preview += '...';
          }

          // Include the title in the preview if available
          contentPreviewMap[url] = title ? `<strong>${title}</strong><br>${preview}` : preview;
        } catch (error: any) {
          console.error(`Error fetching preview for ${url}:`, error);
          contentPreviewMap[url] = `Error fetching content: ${error.message || 'Unknown error'}`;
        }
      })
    );
    return NextResponse.json(contentPreviewMap);
  } catch (error: any) {
    console.error("Error in preview API:", error);
    return NextResponse.json(
      { error: "Failed to generate previews", message: error.message || 'Unknown error' },
      { status: 500 }
    );
  }
}
