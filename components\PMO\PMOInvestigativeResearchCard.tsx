'use client';

import React, { useState } from 'react';
import { Search, FileText, Users, Clock, TrendingUp, ChevronRight } from "lucide-react";
import InvestigativeResearchInterface from './InvestigativeResearchInterface';

interface PMOInvestigativeResearchCardProps {
  pmoId?: string;
  onInvestigationComplete?: (result: any) => void;
}

const PMOInvestigativeResearchCard: React.FC<PMOInvestigativeResearchCardProps> = ({
  pmoId,
  onInvestigationComplete
}) => {
  const [showInterface, setShowInterface] = useState(false);

  const features = [
    {
      icon: Users,
      title: 'Multi-Perspective Analysis',
      description: 'Specialized journalist AI agents provide diverse investigative perspectives'
    },
    {
      icon: Search,
      title: 'Deep Investigation',
      description: 'Comprehensive research using advanced LLM comparison methodology'
    },
    {
      icon: FileText,
      title: 'Professional Reports',
      description: 'Generate publication-ready investigative reports with citations'
    },
    {
      icon: TrendingUp,
      title: 'Evidence-Based',
      description: 'Systematic evaluation and assessment of findings across multiple models'
    }
  ];

  const investigationTypes = [
    { name: 'Financial Investigation', description: 'Corporate finance, market analysis, regulatory compliance' },
    { name: 'Political Investigation', description: 'Policy analysis, government operations, electoral systems' },
    { name: 'Technology Investigation', description: 'Tech trends, cybersecurity, digital transformation' },
    { name: 'Social Affairs Investigation', description: 'Community impact, human rights, demographic analysis' },
    { name: 'Corporate Investigation', description: 'Business practices, governance, industry analysis' },
    { name: 'Environmental Investigation', description: 'Climate change, sustainability, environmental impact' },
    { name: 'General Investigative', description: 'Deep-dive investigative reporting and analysis' },
    { name: 'Feature Investigation', description: 'Long-form narrative storytelling with comprehensive context' }
  ];

  if (showInterface) {
    return (
      <InvestigativeResearchInterface
        pmoId={pmoId}
        onInvestigationComplete={(result) => {
          if (onInvestigationComplete) {
            onInvestigationComplete(result);
          }
          setShowInterface(false);
        }}
        onClose={() => setShowInterface(false)}
      />
    );
  }

  return (
    <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
      <div className="p-6 border-b border-zinc-700">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-semibold text-white flex items-center">
              <Search className="mr-3 h-6 w-6 text-purple-400" />
              Investigative Research Agent
            </h2>
            <p className="text-zinc-400 mt-2">
              Conduct comprehensive investigative research using specialized AI journalist agents
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-zinc-500">PMO Research Tool</div>
            <div className="text-xs text-purple-400">Multi-LLM Analysis</div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Key Features */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Key Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {features.map((feature, index) => (
              <div key={index} className="flex items-start p-4 bg-zinc-800/50 rounded-lg border border-zinc-700">
                <feature.icon className="h-5 w-5 text-purple-400 mr-3 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-white">{feature.title}</h4>
                  <p className="text-xs text-zinc-400 mt-1">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Investigation Types */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Available Investigation Types</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {investigationTypes.map((type, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-zinc-800/30 rounded-lg border border-zinc-700 hover:border-zinc-600 transition-colors">
                <div>
                  <h4 className="text-sm font-medium text-white">{type.name}</h4>
                  <p className="text-xs text-zinc-400 mt-1">{type.description}</p>
                </div>
                <ChevronRight className="h-4 w-4 text-zinc-500" />
              </div>
            ))}
          </div>
        </div>

        {/* Process Overview */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Investigation Process</h3>
          <div className="space-y-3">
            <div className="flex items-center p-3 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                1
              </div>
              <div>
                <h4 className="text-sm font-medium text-white">Criteria Generation</h4>
                <p className="text-xs text-zinc-400">AI generates comprehensive evaluation criteria for the investigation</p>
              </div>
            </div>
            
            <div className="flex items-center p-3 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                2
              </div>
              <div>
                <h4 className="text-sm font-medium text-white">Prompt Optimization</h4>
                <p className="text-xs text-zinc-400">Research question is refined for maximum investigative effectiveness</p>
              </div>
            </div>
            
            <div className="flex items-center p-3 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                3
              </div>
              <div>
                <h4 className="text-sm font-medium text-white">Multi-Journalist Investigation</h4>
                <p className="text-xs text-zinc-400">Specialized journalist AI agents conduct parallel investigations</p>
              </div>
            </div>
            
            <div className="flex items-center p-3 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                4
              </div>
              <div>
                <h4 className="text-sm font-medium text-white">Comparative Assessment</h4>
                <p className="text-xs text-zinc-400">Editorial AI evaluates and compares all investigative findings</p>
              </div>
            </div>
            
            <div className="flex items-center p-3 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium mr-3">
                5
              </div>
              <div>
                <h4 className="text-sm font-medium text-white">Report Consolidation</h4>
                <p className="text-xs text-zinc-400">Optional: Combine best elements into unified investigative report</p>
              </div>
            </div>
          </div>
        </div>

        {/* Technical Specifications */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Technical Specifications</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-zinc-800/50 rounded-lg border border-zinc-700">
              <h4 className="text-sm font-medium text-white mb-2">LLM Providers</h4>
              <div className="space-y-1 text-xs text-zinc-400">
                <div>• OpenAI (GPT-4o, o3-2025-04-16)</div>
                <div>• Anthropic (Claude Sonnet 4.0)</div>
                <div>• Google (Gemini 2.5 Pro)</div>
                <div>• Groq (Llama models)</div>
              </div>
            </div>
            
            <div className="p-4 bg-zinc-800/50 rounded-lg border border-zinc-700">
              <h4 className="text-sm font-medium text-white mb-2">Journalist Agents</h4>
              <div className="space-y-1 text-xs text-zinc-400">
                <div>• Up to 6 specialized personas</div>
                <div>• Domain-specific expertise</div>
                <div>• Unique investigation styles</div>
                <div>• Model optimization per agent</div>
              </div>
            </div>
            
            <div className="p-4 bg-zinc-800/50 rounded-lg border border-zinc-700">
              <h4 className="text-sm font-medium text-white mb-2">Output Formats</h4>
              <div className="space-y-1 text-xs text-zinc-400">
                <div>• Structured investigation reports</div>
                <div>• PDF export capability</div>
                <div>• PMO integration ready</div>
                <div>• Citation and source tracking</div>
              </div>
            </div>
          </div>
        </div>

        {/* Usage Statistics */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-white mb-4">Typical Investigation Metrics</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <Clock className="h-6 w-6 text-purple-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">5-15 min</div>
              <div className="text-xs text-zinc-400">Average Duration</div>
            </div>
            
            <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <Users className="h-6 w-6 text-purple-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">3-6</div>
              <div className="text-xs text-zinc-400">Journalist Agents</div>
            </div>
            
            <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <FileText className="h-6 w-6 text-purple-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">5-15</div>
              <div className="text-xs text-zinc-400">Pages Generated</div>
            </div>
            
            <div className="text-center p-4 bg-zinc-800/30 rounded-lg border border-zinc-700">
              <TrendingUp className="h-6 w-6 text-purple-400 mx-auto mb-2" />
              <div className="text-lg font-semibold text-white">95%</div>
              <div className="text-xs text-zinc-400">Success Rate</div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <button
            onClick={() => setShowInterface(true)}
            className="inline-flex items-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            <Search className="mr-2 h-5 w-5" />
            Start New Investigation
          </button>
          <p className="text-xs text-zinc-500 mt-2">
            Launch the investigative research interface to begin your analysis
          </p>
        </div>
      </div>
    </div>
  );
};

export default PMOInvestigativeResearchCard;
