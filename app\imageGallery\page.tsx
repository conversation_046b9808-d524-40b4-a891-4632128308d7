'use client';

import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from 'components/firebase';
import ImageModal from 'components/image-generation/ImageModal';
import Image from 'next/image';
import { ArrowLeft, ImageIcon, Download } from 'lucide-react';

interface ImageData {
  url: string;
  description: string;
  originalPrompt?: string;
  id: string;
}

export default function ImageGalleryPage() {
  const { data: session } = useSession();
  const userId = session?.user?.email ?? null;
  const [images, setImages] = useState<ImageData[]>([]);
  const [selectedImage, setSelectedImage] = useState<ImageData | null>(null);
  const [showSignOutModal, setShowSignOutModal] = useState(false);

  // Function to force download an image to local drive
  const forceDownload = (e: React.MouseEvent, imageUrl: string, filename?: string) => {
    e.preventDefault();
    e.stopPropagation();

    // Extract a filename from the URL if not provided
    if (!filename) {
      // Try to get a meaningful filename from the URL
      const urlParts = imageUrl.split('/');
      filename = urlParts[urlParts.length - 1].split('?')[0]; // Remove query parameters

      // If filename doesn't have an extension or has .png, change to .jpg
      if (!filename.includes('.') || filename.endsWith('.png')) {
        // Remove .png extension if present
        if (filename.endsWith('.png')) {
          filename = filename.substring(0, filename.length - 4);
        }
        filename += '.jpg';
      }
    }

    // Create a new XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('GET', imageUrl, true);
    xhr.responseType = 'blob';

    xhr.onload = function() {
      if (this.status === 200) {
        // Create a blob from the response with JPEG MIME type
        const blob = new Blob([this.response], { type: 'image/jpeg' });

        // Create a temporary URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = url;
        link.download = filename || 'image.png';

        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
      }
    };

    xhr.send();
  };

  useEffect(() => {
    if (!userId) return;

    const fetchImages = async () => {
      try {
        const qRef = query(
          collection(db, 'users', userId, 'files'),
          where('category', '==', 'My Images'),
          orderBy('createdAt', 'desc')
        );
        const snapshot = await getDocs(qRef);

        const temp: ImageData[] = [];
        snapshot.forEach((docSnap) => {
          const data = docSnap.data();
          temp.push({
            url: data.downloadUrl || data.imageUrl, // Use imageUrl as fallback
            description: data.originalPrompt || data.description || 'No description available',
            originalPrompt: data.originalPrompt,
            id: docSnap.id,
          });
        });

        setImages(temp);
      } catch (error) {
        console.error('Error fetching images for gallery:', error);
      }
    };

    fetchImages();
  }, [userId]);

  if (!userId) {
    return <div className="text-white">Please log in to view your images.</div>;
  }

  return (
    <div className="min-h-screen bg-ike-message-bg">
      <div className="flex">
        <div className="flex-1 container mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4 -mt-10">
            <ImageIcon className="w-18 h-18  text-blue-200 " />
              <h1 className="text-3xl font-bold text-blue-200">My Gallery</h1>

            </div>
            {session?.user && (
              <div className="flex items-center">
                <span className="text-white mr-2">{session.user.name}</span>
                <button
                  onClick={() => setShowSignOutModal(true)}
                  className="flex items-center"
                >
                  <Image
                    src={session.user.image || '/placeholder-user.jpg'}
                    alt="User profile"
                    width={40}
                    height={40}
                    className="rounded-full"
                  />
                </button>
              </div>
            )}
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {images.map((img) => (
              <div key={img.id} className="flex flex-col items-center relative group" onClick={() => setSelectedImage(img)}>
                {/* Download button positioned at the top center of the image */}
                <div className="absolute -top-3 left-0 right-0 flex justify-center opacity-0 group-hover:opacity-100 transition-opacity z-10">
                  <button
                    onClick={(e) => forceDownload(e, img.url, `image-${img.id}.jpg`)}
                    className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded-full shadow-md text-xs"
                    aria-label="Download image"
                  >
                    <Download size={14} />
                    Download
                  </button>
                </div>

                <img
                  src={img.url}
                  alt={img.originalPrompt || img.description}
                  className="w-full h-48 object-cover border border-gray-300 rounded cursor-pointer"
                />
                <span className="text-sm mt-2 truncate text-blue-200">
                  {img.description.length > 26
                    ? `${img.description.substring(0, 26)}...`
                    : img.description}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
      {/* Add SignOut modal here */}
    </div>
  );
}

