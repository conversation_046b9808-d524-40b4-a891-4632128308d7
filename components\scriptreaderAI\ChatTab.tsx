"use client";

import React, { useState, useRef, useEffect, useCallback } from "react"; // Added React import
import { Menu, Plus, Loader } from "lucide-react";
import {
  collection,
  addDoc,
  query,
  orderBy,
  limit,
  getDocs,
  doc,
  getDoc,
  updateDoc,
  where,
  serverTimestamp,
  Timestamp, // Import Timestamp type if using it directly
} from "firebase/firestore";
import { useSession, signIn } from "next-auth/react";
import { ChatMessages } from "./ChatMessages";
import { EnhancedInputBox } from "./InputBox";
import { ChatHistory } from "./ChatHistory";
import useUpload, { StatusText } from "./useUpload";
import { v4 as uuidv4 } from "uuid";
import { db } from "components/firebase";

// --- Interfaces ---
interface ChatMessage {
  id?: string;          // Firestore document ID
  tempId?: string;      // Temporary tracking ID before Firestore persistence
  role: "user" | "assistant";
  content: string;
  timestamp: string;     // ISO string format for consistency
  audioUrl?: string;
  fileDocumentId?: string; // The ID of the related file document
}

// Props interface for ChatTab, accepting necessary IDs and namespace
interface ChatTabProps {
  chatId: string | null;      // The ID of the currently active chat document
  namespace: string | null; // Namespace associated with the chat/file (might be redundant with fileId)
  fileId: string | null;    // The ID of the currently associated file document
}

// --- Component ---
function ChatTab({ chatId: propChatId, namespace, fileId: propFileId }: ChatTabProps) {
  // --- Authentication ---
  const { data: session, status: sessionStatus } = useSession();
  const userId = session?.user?.email || "";
  const isAuthenticated = sessionStatus === "authenticated";
  const isAuthLoading = sessionStatus === "loading";

  // --- State ---
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false); // Loading messages or waiting for AI
  const [isHistoryVisible, setIsHistoryVisible] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [chatId, setChatId] = useState<string | null>(propChatId || null); // Internal state, updated by prop
  const [isRecording, setIsRecording] = useState(false);
  const [isUploading, setIsUploading] = useState<boolean>(false); // Uploading *new* file via chat tab
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [uploadStatusText, setUploadStatusText] = useState<string>("");
  const [selectedFileNamespace, setSelectedFileNamespace] = useState<string | null>(namespace || null); // Track namespace from prop
  const [fileDocumentId, setFileDocumentId] = useState<string | null>(propFileId || null); // Initialize with fileId prop
  const [fileName, setFileName] = useState<string | null>(null); // Store fetched file name
  const [isStreaming, setIsStreaming] = useState<boolean>(false); // If AI response is streaming (optional)

  // --- Refs ---
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null); // For scrolling to bottom
  const isMounted = useRef(true); // Track component mount status
  const fileInputRef = useRef<HTMLInputElement>(null); // For file upload trigger
  const previousChatIdRef = useRef<string | null>(null); // Track previous chat ID to prevent redundant loads
  const messagesProcessingRef = useRef(false); // Prevent concurrent message loads

  // --- Custom Hooks ---
  const { handleUpload, progress, status, error: uploadError } = useUpload(); // File upload hook

  // --- Effects ---

  // Effect: Synchronize internal state with props from Reader-modal
  useEffect(() => {
    // Update internal chatId if the prop changes
    if (propChatId !== chatId) {
      setChatId(propChatId);
    }
    // Update fileDocumentId if the fileId prop changes
    if (propFileId !== fileDocumentId) {
      setFileDocumentId(propFileId);
      setFileName(null); // Reset file name when file ID changes, trigger refetch
    }
    // Update namespace if prop changes
    if (namespace !== selectedFileNamespace) {
      setSelectedFileNamespace(namespace);
    }
  }, [propChatId, propFileId, namespace]); // React to changes in incoming props

   // Effect: Fetch file name when fileDocumentId changes (and is valid)
   useEffect(() => {
       const fetchFileName = async () => {
           // Only fetch if we have a fileDocumentId, a userId, and haven't fetched the name yet
           if (fileDocumentId && userId && !fileName) {
               console.log(`[ChatTab Effect] Fetching file name for fileDocumentId: ${fileDocumentId}`);
               try {
                   const fileRef = doc(db, `users/${userId}/files/${fileDocumentId}`);
                   const fileSnapshot = await getDoc(fileRef);
                   if (fileSnapshot.exists() && isMounted.current) {
                       setFileName(fileSnapshot.data().name || "Untitled Script");
                   } else if(isMounted.current){
                       console.warn(`File document not found for ID: ${fileDocumentId}`);
                       setFileName("Unknown Script"); // Indicate if file not found
                   }
               } catch (err) {
                   console.error("Error fetching file name:", err);
                    if(isMounted.current) setFileName("Error fetching name");
               }
           } else if (!fileDocumentId && fileName) {
               // Clear file name if fileDocumentId becomes null
               setFileName(null);
           }
       };
       fetchFileName();
   }, [fileDocumentId, userId, fileName]); // Re-run if fileDocumentId, userId changes, or fileName becomes null unexpectedly

  // Effect: Load messages when chatId changes and component is mounted
  useEffect(() => {
    // Condition: must have userId, chatId, chatId must be different from previous, and not already loading
    if (chatId && userId && chatId !== previousChatIdRef.current && !messagesProcessingRef.current) {
      console.log(`[ChatTab Effect] Chat ID changed to ${chatId}. Loading messages.`);
      loadMessagesForChat(chatId);
      previousChatIdRef.current = chatId; // Update ref *after* starting load
    } else if (!chatId && previousChatIdRef.current) {
        // If chatId becomes null (e.g., no chat selected), clear messages and ref
        console.log("[ChatTab Effect] Chat ID is null. Clearing messages.");
        if (isMounted.current) setChatMessages([]);
        previousChatIdRef.current = null;
    }
  }, [chatId, userId]); // Dependencies: chatId, userId

  // Effect: Handle file upload status updates from useUpload hook
  useEffect(() => {
    if (!isMounted.current) return; // Don't update state if unmounted
    if (status === StatusText.ERROR && uploadError) {
      setError(`Upload error: ${uploadError}`); setIsUploading(false); setUploadProgress(null); setUploadStatusText("Upload Failed!");
    } else if (status === StatusText.UPLOADING) {
      setIsUploading(true); setUploadProgress(progress || 0); setUploadStatusText("Uploading...");
    } else if (status === StatusText.PROCESSING) {
      setIsUploading(true); setUploadProgress(100); setUploadStatusText("Processing...");
    } else if (status === StatusText.COMPLETED) {
      setIsUploading(false); setUploadProgress(null); setUploadStatusText("Upload complete!");
      // Parent (Reader-modal) should handle refetching file list & setting active tab based on upload hook completion
      setTimeout(() => { if (isMounted.current) setUploadStatusText(""); }, 3000); // Clear status message after delay
    }
  }, [status, progress, uploadError]);

  // Effect: Handle specific auth-related errors
  useEffect(() => {
    if (sessionStatus === "unauthenticated" && !isAuthLoading && error?.includes("Transcription failed")) {
      if (isMounted.current) setError("Please sign in to use voice recording.");
    }
  }, [sessionStatus, isAuthLoading, error]);

  // Effect: Component mount/unmount cleanup
  useEffect(() => {
    isMounted.current = true;
    return () => { isMounted.current = false; console.log("[ChatTab] Unmounted"); }; // Cleanup function
  }, []); // Empty dependency array means this runs only on mount and unmount

  // --- Data Fetching & Processing Functions ---

  const loadMessagesForChat = useCallback(async (currentChatId: string) => {
    if (!userId || !currentChatId || !isMounted.current) return;
    if (messagesProcessingRef.current) { console.warn("Message loading already in progress."); return; }

    messagesProcessingRef.current = true;
    console.log(`[ChatTab LoadMessages] Loading for chat: ${currentChatId}`);
    if (isMounted.current) { setIsLoading(true); setError(null); }

    try {
      const messagesRef = collection(db, `users/${userId}/chats/${currentChatId}/messages`);
      const messagesQuery = query(messagesRef, orderBy("createdAt", "asc"), limit(100)); // Added limit for performance
      const messagesSnapshot = await getDocs(messagesQuery);

      const messages: ChatMessage[] = messagesSnapshot.docs.map((doc) => {
        const data = doc.data();
        const timestamp = data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : new Date().toISOString();
        return {
          id: doc.id,
          tempId: data.tempId, // Include temporary ID if present
          role: data.role,
          content: data.text || "", // Map 'text' field from Firestore to 'content'
          timestamp: timestamp,
          audioUrl: data.audioUrl,
          // Use file ID from message if available, otherwise use current component context
          fileDocumentId: data.fileDocumentId || fileDocumentId,
        };
      });

      console.log(`[ChatTab LoadMessages] Loaded ${messages.length} messages.`);
      if (isMounted.current) {
         // Replace state entirely with new, deduplicated messages
         setChatMessages(deduplicateMessages(messages));
      }
    } catch (err) {
      console.error("Error loading messages:", err);
      if (isMounted.current) setError(`Failed to load messages: ${err instanceof Error ? err.message : "Unknown error"}`);
    } finally {
       if (isMounted.current) setIsLoading(false);
       messagesProcessingRef.current = false; // Release lock
    }
  }, [userId, fileDocumentId]); // fileDocumentId might be needed to enrich messages

  const deduplicateMessages = (messages: ChatMessage[]): ChatMessage[] => {
    const uniqueMessages = new Map<string, ChatMessage>();
    messages.forEach(message => {
        // Prioritize Firestore ID, then temp ID, then fallback
        const key = message.id || message.tempId || `${message.role}-${message.content.slice(0, 20)}-${message.timestamp}`;
        if (!uniqueMessages.has(key)) {
            uniqueMessages.set(key, message);
        }
    });
    return Array.from(uniqueMessages.values());
  };

  const fetchMostRecentFile = useCallback(async (): Promise<{id: string, namespace: string, name: string} | null> => {
    // (Implementation remains the same as provided before)
     if (!userId) return null; try { const filesRef = collection(db, `users/${userId}/files`); const q = query(filesRef, orderBy("createdAt", "desc"), limit(1)); const querySnapshot = await getDocs(q); if (!querySnapshot.empty) { const d = querySnapshot.docs[0]; const data = d.data(); return { id: d.id, namespace: data.namespace || d.id, name: data.name || "Untitled" }; } return null; } catch (err) { console.error("Err fetch recent file:", err); return null; }
  }, [userId]);


  // --- Core Actions ---

  const createNewChat = useCallback(async (associatedFileId?: string, associatedFileName?: string) => {
    if (!userId) { if(isMounted.current) setError("Please sign in to create a chat."); return null; }
    console.log("[ChatTab] Creating new chat...");
    if (!isMounted.current) return null;

    let newChatDocId: string | null = null;
    setIsLoading(true); // Indicate activity

    try {
      let targetFileId = associatedFileId || fileDocumentId; // Prioritize explicitly passed ID
      let targetFileName = associatedFileName || fileName;
      let targetNamespace = namespace; // Use namespace from props if available

      if (!targetFileId) {
          const recentFile = await fetchMostRecentFile();
          if (recentFile) {
              targetFileId = recentFile.id; targetFileName = recentFile.name; targetNamespace = recentFile.namespace;
          } else {
              console.warn("Creating new chat without file association."); // Allow creating general chats
          }
      }

      const chatsRef = collection(db, `users/${userId}/chats`);
      const chatData = { createdAt: serverTimestamp(), userId: userId, firstMessage: "New Chat", lastUpdated: serverTimestamp(), fileNamespace: targetNamespace || null, fileDocumentId: targetFileId || null };
      const docRef = await addDoc(chatsRef, chatData);
      newChatDocId = docRef.id;

      if (isMounted.current) {
        setChatId(newChatDocId); setChatMessages([]); // Switch to new chat, clear messages
        setFileDocumentId(targetFileId); setFileName(targetFileName); setSelectedFileNamespace(targetNamespace); // Update context
        setError(null);
      }
      console.log(`[ChatTab] New chat created: ${newChatDocId}`);
      return newChatDocId;

    } catch (err) {
      console.error("Failed to create new chat:", err);
      if (isMounted.current) setError("Failed to create chat: " + (err instanceof Error ? err.message : "Unknown"));
      return null;
    } finally {
      if (isMounted.current) setIsLoading(false);
    }
  }, [userId, fileDocumentId, fileName, namespace, fetchMostRecentFile]);

  const handleChatSelect = useCallback(async (selectedChatId: string) => {
    if (!selectedChatId || !userId || !isMounted.current) return;
    console.log(`[ChatTab] Selecting chat: ${selectedChatId}`);
    // Avoid re-selecting the same chat unnecessarily
    if (selectedChatId === chatId) { setIsHistoryVisible(false); return; }

    setIsLoading(true); setError(null); setFileName(null); setFileDocumentId(null); setSelectedFileNamespace(null); // Reset context
    setChatId(selectedChatId); // Set internal chatId immediately
    setIsHistoryVisible(false); // Close history panel

    try {
      const chatRef = doc(db, `users/${userId}/chats/${selectedChatId}`);
      const chatSnapshot = await getDoc(chatRef);
      if (chatSnapshot.exists()) {
        const chatData = chatSnapshot.data();
        if (isMounted.current) {
            // Set file context based on the selected chat document
            setFileDocumentId(chatData.fileDocumentId || null);
            setSelectedFileNamespace(chatData.fileNamespace || null);
            // File name will be fetched by the useEffect watching fileDocumentId
        }
      } else {
         console.warn(`Selected chat document ${selectedChatId} not found.`);
         if(isMounted.current) setError("Selected chat not found.");
      }
      // Load messages is triggered by the useEffect watching 'chatId'
    } catch (error) {
      console.error("Error during chat selection:", error);
      if (isMounted.current) setError("Failed to load selected chat details");
      // Reset chatId if loading fails badly? Maybe not, allow user to retry loading messages.
    } finally {
       // setIsLoading(false) // Let loadMessagesForChat handle this
    }
  }, [userId, chatId]); // Added chatId to dependency to avoid re-selecting same

  const sendMessage = useCallback(async (messageText: string) => {
      if (!messageText.trim() || !chatId || isLoading || !userId || !isMounted.current) return;

      const currentFileDocId = fileDocumentId; // Use state which is updated by props/selection
      console.log(`[ChatTab SendMessage] Chat: ${chatId}, File: ${currentFileDocId || 'N/A'}`);
      const tempUserMessageId = `temp-${uuidv4()}`;
      const userMessage: ChatMessage = {
        role: "user",
        content: messageText,
        timestamp: new Date().toISOString(),
        tempId: tempUserMessageId,
        fileDocumentId: currentFileDocId || undefined
      };

      setChatMessages(prev => deduplicateMessages([...prev, userMessage]));
      setIsLoading(true); setError(null);

      try {
        const messagesCollRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
        // Add user message
        const userMessageRef = await addDoc(messagesCollRef, { role: "user", text: messageText, createdAt: serverTimestamp(), userId: userId, fileDocumentId: currentFileDocId, tempId: tempUserMessageId });
        if (isMounted.current) setChatMessages(prev => prev.map(m => m.tempId === tempUserMessageId ? { ...m, id: userMessageRef.id, tempId: undefined } : m));

        // Call API
        const response = await fetch("/api/processMessage", { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ userId, chatId, messageText, fileDocumentId: currentFileDocId }) });
        if (!response.ok) throw new Error(`API request failed: ${response.status}`);
        const data = await response.json();

        if (data.success && isMounted.current) { // Check mount status before processing response
            const tempAssistantMessageId = `temp-${uuidv4()}`;
            // *** CORRECTED ASSISTANT MESSAGE DEFINITION ***
            const assistantMessage: ChatMessage = { role: "assistant", content: data.content, timestamp: new Date().toISOString(), audioUrl: data.audioUrl, tempId: tempAssistantMessageId, fileDocumentId: currentFileDocId || undefined };

            setChatMessages(prev => deduplicateMessages([...prev, assistantMessage])); // Add assistant msg to UI

            // Add assistant message to Firestore
            const assistantMessageRef = await addDoc(messagesCollRef, { role: "assistant", text: data.content, createdAt: serverTimestamp(), audioUrl: data.audioUrl, fileDocumentId: currentFileDocId, tempId: tempAssistantMessageId });
            if (isMounted.current) setChatMessages(prev => prev.map(m => m.tempId === tempAssistantMessageId ? { ...m, id: assistantMessageRef.id, tempId: undefined } : m)); // Update UI with real ID

            // Update chat metadata
            await updateDoc(doc(db, `users/${userId}/chats/${chatId}`), { lastUpdated: serverTimestamp(), ...(chatMessages.length === 0 && { firstMessage: messageText }) });
        } else if (!data.success) { throw new Error(data.error || "Unknown API error"); }

      } catch (err) {
        console.error("Error sending message:", err);
        if (isMounted.current) { setError(err instanceof Error ? err.message : "An error occurred"); setChatMessages(prev => prev.filter(m => m.tempId !== tempUserMessageId)); } // Remove optimistic msg on error
      } finally {
          if (isMounted.current) setIsLoading(false);
      }
  }, [chatId, userId, isLoading, fileDocumentId, chatMessages.length]); // Dependencies

  // --- Voice Recording Callbacks ---
  const startRecording = useCallback(async () => { if (!isMounted.current) return; try { const stream = await navigator.mediaDevices.getUserMedia({ audio: true }); const recorder = new MediaRecorder(stream, { mimeType: 'audio/webm' }); mediaRecorderRef.current = recorder; const chunks: Blob[] = []; recorder.ondataavailable = (e) => chunks.push(e.data); recorder.onstop = () => { const blob = new Blob(chunks, { type: recorder.mimeType }); processAudioBlob(blob); stream.getTracks().forEach(t => t.stop()); }; recorder.start(); setIsRecording(true); } catch (error) { console.error("Rec start err:", error); setError("Failed to start recording. Check mic permissions."); } }, []);
  const stopRecording = useCallback(() => { if (mediaRecorderRef.current) { mediaRecorderRef.current.stop(); if (isMounted.current) setIsRecording(false); } }, []);
  const processAudioBlob = useCallback(async (blob: Blob) => { if (!isMounted.current) return; if (sessionStatus === "unauthenticated") { if (confirm("Sign in to use voice recording?")) { signIn('google', { callbackUrl: window.location.href }); } return; } setIsLoading(true); setError(null); const formData = new FormData(); formData.append("audio", blob, `voice.webm`); try { const response = await fetch("/api/transcribeAudio", { method: "POST", credentials: 'include', body: formData }); if (response.status === 401) throw new Error("Authentication failed. Please sign in again."); if (!response.ok) { const errData = await response.json().catch(() => ({})); throw new Error(errData.error || `Server error: ${response.status}`); } const data = await response.json(); if (data.success) sendMessage(data.text); else throw new Error(data.error || "Transcription failed."); } catch (error) { console.error("Audio proc error:", error); if(isMounted.current) setError(`Transcription failed: ${error instanceof Error ? error.message : "Unknown"}`); } finally { if(isMounted.current) setIsLoading(false); } }, [sessionStatus, sendMessage]);
  const handleMicClick = useCallback(() => { if (sessionStatus === "unauthenticated") { if (confirm("Sign in to use voice recording?")) { signIn('google', { callbackUrl: window.location.href }); } return; } if (!userId) { setError("Please sign in."); return; } if (isRecording) stopRecording(); else startRecording(); }, [sessionStatus, userId, isRecording, startRecording, stopRecording]);

  // --- File Upload Callbacks ---
  const handleAttachFile = useCallback(() => { fileInputRef.current?.click(); }, []);
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !userId || !isMounted.current) { if (fileInputRef.current) fileInputRef.current.value = ""; return; }
    setIsUploading(true); setError(null); setUploadStatusText("Starting upload...");
    try {
      const docId = uuidv4(); // ID for the new file document
      await handleUpload(file, null, userId, docId); // Use the upload hook
      // After upload initiated (hook handles progress/completion):
      // Create a NEW chat specifically for this uploaded file
      await createNewChat(docId, file.name); // Pass ID and name to createNewChat
    } catch (err) {
      console.error("Upload failed:", err);
      if (isMounted.current) { setError(err instanceof Error ? err.message : "Upload failed"); setIsUploading(false); setUploadStatusText("Upload failed"); }
    } finally { if (fileInputRef.current) fileInputRef.current.value = ""; }
  }, [userId, handleUpload, createNewChat]);

  // --- Display Logic ---
  const getHeaderDisplayText = () => {
    const fileDisplay = fileName ? fileName : fileDocumentId ? `Script: ${fileDocumentId.substring(0, 6)}..` : "";
    const chatDisplay = chatId ? `Chat: ${chatId.substring(0, 6)}..` : "";
    if (fileDisplay && chatDisplay) return `${fileDisplay} | ${chatDisplay}`;
    return fileDisplay || chatDisplay || "No Chat Selected";
  };

  // --- Render ---
  return (
    <div className="flex h-full relative bg-gray-800 text-gray-200">
      {/* Auth Warning Banner */}
       {sessionStatus === "unauthenticated" && !isAuthLoading && (
        <div className="absolute top-0 right-0 bg-yellow-600 text-white px-3 py-1 text-xs rounded-bl-md z-20 flex items-center shadow">
          <span>Voice requires sign-in</span>
          <button onClick={() => signIn('google', { callbackUrl: window.location.href })} className="ml-2 bg-white text-yellow-700 px-2 py-0.5 rounded text-xs font-medium hover:bg-gray-100">
            Sign in
          </button>
        </div>
      )}

      {/* Chat History Sidebar (conditional rendering) */}
      <ChatHistory
        isVisible={isHistoryVisible}
        onClose={() => setIsHistoryVisible(false)}
        currentChatId={chatId}
        onChatSelect={handleChatSelect}
        userId={userId}
        namespace={selectedFileNamespace}
      />

      {/* Main Chat Area */}
      <div className="w-full flex flex-col h-full overflow-hidden">
        {/* Header */}
        <div className="p-3 flex items-center justify-between border-b border-gray-700 bg-gray-850 shrink-0">
          <div className="flex items-center space-x-2">
            <button onClick={() => setIsHistoryVisible(!isHistoryVisible)} className="p-2 rounded-md hover:bg-gray-700 transition-colors" title="Toggle History">
              <Menu className="w-5 h-5 text-gray-300" />
            </button>
            <button onClick={() => createNewChat()} className="flex items-center space-x-1 px-3 py-1.5 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm" title="New Chat">
              <Plus className="w-4 h-4" />
              <span>New Chat</span>
            </button>
          </div>
          <span className="text-gray-400 text-xs truncate max-w-[200px] sm:max-w-xs md:max-w-sm" title={getHeaderDisplayText()}>
            {getHeaderDisplayText()}
          </span>
        </div>

         {/* Error Display */}
         {error && ( <div className="bg-red-900/50 border border-red-700 text-red-200 text-center p-2 text-sm relative mx-2 my-1 rounded"> <span>{error}</span> <button onClick={() => setError(null)} className="absolute top-0.5 right-1.5 text-red-300 hover:text-red-100 text-xl font-light" aria-label="Dismiss error">×</button> </div> )}

         {/* Message Display Area */}
        <div className="flex-1 overflow-y-auto relative bg-gray-800">
            {isUploading ? (
                 <div className="flex flex-col items-center justify-center h-full p-4 text-gray-400"> <Loader className="w-8 h-8 text-purple-500 animate-spin mb-4" /> <p className="text-gray-300 mb-2">{uploadStatusText}</p> {uploadProgress !== null && ( <div className="w-64 bg-gray-700 rounded-full h-2"><div className="bg-purple-500 h-2 rounded-full transition-all" style={{ width: `${uploadProgress}%` }}></div></div> )}</div>
            ) : isLoading && chatMessages.length === 0 ? (
                 <div className="flex flex-col items-center justify-center h-full"> <div className="flex space-x-2 mb-3"><div className="loading-dot" style={{ animationDelay: "0ms" }}></div><div className="loading-dot" style={{ animationDelay: "150ms" }}></div><div className="loading-dot" style={{ animationDelay: "300ms" }}></div></div><p className="text-gray-400 text-sm">Loading...</p></div>
            ) : chatMessages.length === 0 && !error && !isLoading ? (
                 <div className="flex flex-col items-center justify-center h-full p-4 text-gray-400 text-center"><p className="text-lg font-semibold">Chat with Scene Mate</p><p className="mt-1 text-sm">Ask about the script or rehearse lines.</p></div>
            ) : (
                 <ChatMessages chatMessages={chatMessages} error={null} isLoading={isLoading && chatMessages.length > 0} isStreaming={isStreaming} messagesEndRef={messagesEndRef} />
            )}
         </div>

        {/* Input Area */}
        <div className="p-2 border-t border-gray-700 bg-gray-850 text-sm shrink-0">
          <EnhancedInputBox
            onSendMessage={sendMessage}
            isLoading={isLoading || isUploading} // Disable if loading OR uploading
            onAttachFile={handleAttachFile}
            onMicClick={handleMicClick}
            isRecording={isRecording}
            placeholder={chatId ? `Ask about "${fileName || 'script'}"...` : "Select or create a chat"}
            disabled={!chatId || isUploading} // Disable only if no chat or uploading
          />
        </div>

        {/* Hidden file input */}
        <input type="file" ref={fileInputRef} className="hidden" onChange={handleFileUpload} accept=".pdf,.docx,.doc,.txt,.md,.jpg,.jpeg,.png" disabled={isLoading || isUploading} />
      </div>

       {/* Loading dots style */}
       <style jsx>{`
         .loading-dot { width: 0.7rem; height: 0.7rem; background-color: #8B5CF6; border-radius: 50%; animation: bounce 1.4s infinite ease-in-out both; }
         @keyframes bounce { 0%, 80%, 100% { transform: scale(0); } 40% { transform: scale(1.0); } }
       `}</style>
    </div>
  );
}

export default ChatTab;