/**
 * Test file for StrategicTaskFormatter markdown formatting utilities
 * 
 * This test verifies that the new markdown formatting methods work correctly
 * for delegation structures and structured gap analysis.
 * 
 * Run with: npx ts-node lib/utils/tests/strategic-task-formatter.test.ts
 */

import { StrategicTaskFormatter } from '../strategic-task-formatter';

/**
 * Test data for delegation structure formatting
 */
const mockDelegationData = {
  status: "Information Gaps Identified",
  summary: "Strategic analysis requires additional information about the SaaS knowledge explorer product, including market positioning, customer data, and technical specifications.",
  urgencyLevel: "HIGH",
  totalGaps: 2,
  identifiedGaps: [
    {
      gapTitle: "Product Details",
      gapDescription: "Missing specific details about the SaaS knowledge explorer product, including its actual name, key features, and pricing.",
      assignedTeam: "Business Analysis Team",
      priority: "HIGH",
      specificRequirements: [
        "product name",
        "key features",
        "pricing model"
      ],
      deliverable: "Complete product specification document",
      timeline: "3 days",
      rationale: "Business Analysis Team has expertise in product requirements gathering and specification documentation."
    },
    {
      gapTitle: "Marketing Assets and Collateral",
      gapDescription: "Lack of information about existing marketing assets and collateral.",
      assignedTeam: "Content Team",
      priority: "LOW",
      specificRequirements: [
        "marketing assets inventory",
        "existing collateral audit",
        "brand guidelines documentation"
      ],
      deliverable: "Marketing assets and collateral inventory",
      timeline: "1 week",
      rationale: "The Content Team is responsible for content strategy and documentation, making them the best team for this task."
    }
  ]
};

/**
 * Test data for structured gap analysis formatting
 */
const mockStructuredGapData = {
  status: "Information Gaps Identified",
  summary: "Marketing campaign strategy for SaaS Knowledge Explorer Solution requires comprehensive product analysis and documentation before proceeding with strategic recommendations.",
  totalGaps: 2,
  identifiedGaps: [
    {
      category: "Product Information",
      description: "Comprehensive product analysis and documentation",
      priority: "HIGH",
      assignedTeam: "Business Analysis Team",
      specificRequirements: [
        "Document all product features and capabilities",
        "Identify unique selling points and differentiators",
        "Create technical specifications document",
        "Analyze integration capabilities and requirements"
      ],
      deliverable: "Complete Product Analysis Document",
      timeline: "1 week"
    },
    {
      category: "Market Intelligence",
      description: "Market research and competitive analysis",
      priority: "MEDIUM",
      assignedTeam: "Research Team",
      specificRequirements: [
        "Conduct comprehensive market research",
        "Analyze competitive landscape",
        "Identify target market segments",
        "Assess market size and growth potential"
      ],
      deliverable: "Market Research and Competitive Analysis Report",
      timeline: "2 weeks"
    }
  ]
};

/**
 * Test the delegation structure markdown formatting
 */
function testDelegationStructureFormatting() {
  console.log('🧪 Testing Delegation Structure Markdown Formatting');
  console.log('=' .repeat(60));
  
  try {
    const formattedMarkdown = StrategicTaskFormatter.formatDelegationStructureMarkdown(mockDelegationData);
    
    console.log('✅ Delegation structure formatting successful!');
    console.log('\n📄 Generated Markdown:');
    console.log('-'.repeat(40));
    console.log(formattedMarkdown);
    console.log('-'.repeat(40));
    
    // Verify key elements are present
    const hasStatus = formattedMarkdown.includes('Strategic Director Assessment:');
    const hasUrgency = formattedMarkdown.includes('Urgency Level:');
    const hasTeamAssignments = formattedMarkdown.includes('Team Assignments');
    const hasPriorityEmojis = formattedMarkdown.includes('🔴') || formattedMarkdown.includes('🟢');
    const hasTeamEmojis = formattedMarkdown.includes('📊') || formattedMarkdown.includes('📝');
    const hasSummary = formattedMarkdown.includes('Summary');
    
    console.log('\n🔍 Validation Results:');
    console.log(`   Status section: ${hasStatus ? '✅' : '❌'}`);
    console.log(`   Urgency level: ${hasUrgency ? '✅' : '❌'}`);
    console.log(`   Team assignments: ${hasTeamAssignments ? '✅' : '❌'}`);
    console.log(`   Priority emojis: ${hasPriorityEmojis ? '✅' : '❌'}`);
    console.log(`   Team emojis: ${hasTeamEmojis ? '✅' : '❌'}`);
    console.log(`   Summary section: ${hasSummary ? '✅' : '❌'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Delegation structure formatting failed:', error);
    return false;
  }
}

/**
 * Test the structured gap analysis markdown formatting
 */
function testStructuredGapAnalysisFormatting() {
  console.log('\n🧪 Testing Structured Gap Analysis Markdown Formatting');
  console.log('=' .repeat(60));
  
  try {
    const formattedMarkdown = StrategicTaskFormatter.formatStructuredGapAnalysisMarkdown(mockStructuredGapData);
    
    console.log('✅ Structured gap analysis formatting successful!');
    console.log('\n📄 Generated Markdown:');
    console.log('-'.repeat(40));
    console.log(formattedMarkdown);
    console.log('-'.repeat(40));
    
    // Verify key elements are present
    const hasTitle = formattedMarkdown.includes('Structured Information Gap Analysis');
    const hasTotalGaps = formattedMarkdown.includes('Total Gaps Identified:');
    const hasExecutiveSummary = formattedMarkdown.includes('Executive Summary');
    const hasGapDetails = formattedMarkdown.includes('Information Gap Details');
    const hasNextSteps = formattedMarkdown.includes('Next Steps');
    const hasTeamCoordination = formattedMarkdown.includes('Team Coordination');
    const hasPriorityEmojis = formattedMarkdown.includes('🔴') || formattedMarkdown.includes('🟡');
    
    console.log('\n🔍 Validation Results:');
    console.log(`   Title section: ${hasTitle ? '✅' : '❌'}`);
    console.log(`   Total gaps: ${hasTotalGaps ? '✅' : '❌'}`);
    console.log(`   Executive summary: ${hasExecutiveSummary ? '✅' : '❌'}`);
    console.log(`   Gap details: ${hasGapDetails ? '✅' : '❌'}`);
    console.log(`   Next steps: ${hasNextSteps ? '✅' : '❌'}`);
    console.log(`   Team coordination: ${hasTeamCoordination ? '✅' : '❌'}`);
    console.log(`   Priority emojis: ${hasPriorityEmojis ? '✅' : '❌'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Structured gap analysis formatting failed:', error);
    return false;
  }
}

/**
 * Test the static information request response
 */
function testStaticInformationRequestResponse() {
  console.log('\n🧪 Testing Static Information Request Response');
  console.log('=' .repeat(60));
  
  try {
    const missingInfo = "Insufficient information about target market and customer segments";
    const staticResponse = StrategicTaskFormatter.generateStaticInformationRequestResponse(missingInfo);
    
    console.log('✅ Static information request response successful!');
    console.log('\n📄 Generated Response:');
    console.log('-'.repeat(40));
    console.log(staticResponse.substring(0, 500) + '...');
    console.log('-'.repeat(40));
    
    // Verify key elements are present
    const hasAssessment = staticResponse.includes('Strategic Director Assessment:');
    const hasTeamDelegation = staticResponse.includes('Team Delegation');
    const hasResearchTeam = staticResponse.includes('Research Team');
    const hasBusinessAnalysis = staticResponse.includes('Business Analysis Team');
    const hasSalesTeam = staticResponse.includes('Sales Team');
    
    console.log('\n🔍 Validation Results:');
    console.log(`   Assessment header: ${hasAssessment ? '✅' : '❌'}`);
    console.log(`   Team delegation: ${hasTeamDelegation ? '✅' : '❌'}`);
    console.log(`   Research team: ${hasResearchTeam ? '✅' : '❌'}`);
    console.log(`   Business analysis team: ${hasBusinessAnalysis ? '✅' : '❌'}`);
    console.log(`   Sales team: ${hasSalesTeam ? '✅' : '❌'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Static information request response failed:', error);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Strategic Task Formatter Test Suite');
  console.log('=' .repeat(60));
  console.log('Testing markdown formatting utilities for Strategic Director Agent');
  console.log('');
  
  const results = [];
  
  // Run individual tests
  results.push(testDelegationStructureFormatting());
  results.push(testStructuredGapAnalysisFormatting());
  results.push(testStaticInformationRequestResponse());
  
  // Summary
  console.log('\n🏁 Test Suite Summary');
  console.log('=' .repeat(60));
  const passed = results.filter(r => r).length;
  const total = results.length;
  
  console.log(`Tests passed: ${passed}/${total}`);
  console.log(`Success rate: ${Math.round((passed/total) * 100)}%`);
  
  if (passed === total) {
    console.log('🎉 All tests passed! The markdown formatting utilities are working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the output above.');
  }
  
  console.log('\n📝 Summary of new utilities:');
  console.log('   - formatDelegationStructureMarkdown(): Formats delegation structures with team assignments');
  console.log('   - formatStructuredGapAnalysisMarkdown(): Formats structured gap analysis with team coordination');
  console.log('   - generateStaticInformationRequestResponse(): Provides fallback delegation response');
  console.log('   - All utilities include priority emojis, team emojis, and consistent markdown formatting');
}

// Run the tests
runAllTests();
