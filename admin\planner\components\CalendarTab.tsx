'use client';

import React, { useState, useEffect, useRef } from 'react';
import { usePlanner } from '../../../app/services/context/PlannerContext';
import { User, Task, PersonalItem, CalendarFilter, CalendarViewType, CalendarEvent } from '../types';
import { useSession } from 'next-auth/react';
import { Filter, Plus, RefreshCw, AlertTriangle } from 'lucide-react';
import CalendarView from './CalendarView';
import PersonalItemForm from './PersonalItemForm';
import GanttChartView from './GanttChartView';
import CalendarDebug from './CalendarDebug';
import TaskDetailsModal from './TaskDetailsModal';
import { getPersonalItems, getTasks as getProjectTasks } from '../../../app/lib/firebase/planner';

interface CalendarTabProps {
  projectId: string;
  users: User[];
}

const CalendarTab: React.FC<CalendarTabProps> = ({ projectId, users }) => {
  const { data: session } = useSession();
  const {
    tasks, // We'll keep this for reference but use our own projectTasks state
    loading,
    error
  } = usePlanner();

  // Local state for personal items and component behavior
  const [personalItems, setPersonalItems] = useState<PersonalItem[]>([]);
  const [projectTasks, setProjectTasks] = useState<Task[]>([]); // Added state for project-specific tasks
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<PersonalItem | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [calendarView, setCalendarView] = useState<CalendarViewType>('month');
  const [showGanttView, setShowGanttView] = useState(false);
  const [filter, setFilter] = useState<CalendarFilter>({
    showUserTasks: true,
    showUserItems: true,
    showAllProjectTasks: true // Default to showing all project tasks
  });
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);

  // Fetch personal items from Firebase
  const fetchPersonalItems = async () => {
    if (!session?.user?.email) {
      console.log('No user email available, skipping personal items fetch');
      return [];
    }

    try {
      console.log(`Fetching personal items for user ${session.user.email}`);
      const items = await getPersonalItems(session.user.email);
      console.log(`Retrieved ${items.length} personal items`);
      setPersonalItems(items);
      return items;
    } catch (err) {
      console.error('Error fetching personal items:', err);
      return [];
    }
  };

  // Fetch tasks for the specific project directly from Firebase
  const fetchProjectTasks = async () => {
    try {
      console.log(`Fetching tasks for project ${projectId}`);

      // Skip the direct query approach since it's failing with permission errors
      // Instead, get all tasks and filter client-side
      console.log('Using client-side filtering approach');

      try {
        // Get all tasks
        const allTasks = await getProjectTasks(); // No projectId means get all tasks
        console.log(`Retrieved ${allTasks.length} total tasks, filtering for project ${projectId}`);

        // Filter tasks client-side using multiple approaches
        const filteredTasks = allTasks.filter(task => {
          if (!task.projectId || !projectId) return false;

          const taskProjId = String(task.projectId).trim().toLowerCase();
          const currentProjId = String(projectId).trim().toLowerCase();

          // Log detailed comparison for debugging
          console.log(`Comparing task ${task.id}: ${taskProjId} vs ${currentProjId}`);

          // Try multiple matching approaches
          // 1. Exact match
          if (taskProjId === currentProjId) {
            console.log(`Task ${task.id} matched with exact comparison`);
            return true;
          }

          // 2. Contains match
          if (taskProjId.includes(currentProjId) || currentProjId.includes(taskProjId)) {
            console.log(`Task ${task.id} matched with contains comparison`);
            return true;
          }

          // 3. Clean and compare (remove non-alphanumeric characters)
          const cleanTaskId = taskProjId.replace(/[^a-z0-9]/g, '');
          const cleanCurrentId = currentProjId.replace(/[^a-z0-9]/g, '');

          if (cleanTaskId === cleanCurrentId) {
            console.log(`Task ${task.id} matched with cleaned comparison`);
            return true;
          }

          return false;
        });

        console.log(`Filtered ${filteredTasks.length} tasks client-side for project ${projectId}`);

        // Log the first few filtered tasks for debugging
        if (filteredTasks.length > 0) {
          console.log('First few filtered tasks:');
          filteredTasks.slice(0, 3).forEach(task => {
            console.log(`- Task ${task.id}: ${task.title}, projectId=${task.projectId}`);
          });
          setLocalError(null); // Clear error if we found tasks
        } else {
          console.log('No tasks found after filtering');
          // Check if there are any tasks with this projectId at all
          const anyMatchingTasks = allTasks.some(task => {
            return String(task.projectId).includes(projectId) ||
                   String(projectId).includes(task.projectId);
          });

          if (anyMatchingTasks) {
            setLocalError(`Tasks exist for this project but couldn't be filtered correctly. Please check the project ID.`);
          } else {
            setLocalError(`No tasks found for project ${projectId}. Please check if tasks exist for this project.`);
          }
        }

        setProjectTasks(filteredTasks);
        return filteredTasks;
      } catch (err: any) {
        console.error('Error fetching all tasks:', err);
        setLocalError(`Could not fetch tasks: ${err?.message || 'Unknown error'}. Please try again later.`);
        setProjectTasks([]);
        return [];
      }
    } catch (err: any) {
      console.error('Error in fetchProjectTasks:', err);
      setLocalError(`Failed to load tasks: ${err?.message || 'Unknown error'}`);
      setProjectTasks([]);
      return [];
    }
  };

  // Load all data (tasks and personal items)
  const loadData = async () => {
    setLocalLoading(true);
    setLocalError(null);

    try {
      // Set a timeout to prevent infinite loading state
      loadingTimeoutRef.current = setTimeout(() => {
        setLocalLoading(false);
        setLocalError('Loading calendar data timed out. Please try refreshing.');
      }, 15000); // 15 second timeout

      console.log('CalendarTab: Loading data...');
      await Promise.all([
        fetchPersonalItems(),
        fetchProjectTasks() // Fetch project tasks directly
      ]);
      console.log(`CalendarTab: After loading, project tasks count = ${projectTasks.length}`);
      processEventsData();
    } catch (err: any) {
      console.error('Error loading calendar data:', err);
      setLocalError('Failed to load calendar data. Please try again.');
    } finally {
      // Clear the timeout if the operation completes
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      setLocalLoading(false);
    }
  };

  // Force refresh all data
  const forceRefresh = async () => {
    setLocalLoading(true);
    setLocalError(null);

    try {
      // Set a timeout to prevent infinite loading state
      loadingTimeoutRef.current = setTimeout(() => {
        setLocalLoading(false);
        setLocalError('Force refresh timed out. Please try again.');
      }, 15000); // 15 second timeout

      console.log('CalendarTab: Force refreshing all data...');
      console.log('CalendarTab: Current projectId before refresh:', projectId);

      // Fetch both personal items and project tasks in parallel
      await Promise.all([
        fetchPersonalItems(),
        fetchProjectTasks() // Fetch project tasks directly
      ]);

      console.log(`CalendarTab: After refresh, project tasks count = ${projectTasks.length}`);

      // Debug: Log a few tasks after refresh
      if (projectTasks.length > 0) {
        console.log('CalendarTab: Sample tasks after refresh:');
        projectTasks.slice(0, 3).forEach(task => {
          console.log(`Task ${task.id}: title=${task.title}, projectId=${task.projectId}`);
        });
      }

      processEventsData();
    } catch (err) {
      console.error('Error refreshing data:', err);
      setLocalError('Failed to refresh data. Please try again.');
    } finally {
      // Clear the timeout if the operation completes
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      setLocalLoading(false);
    }
  };

  // Initial data load on component mount
  useEffect(() => {
    if (session?.user?.email) {
      console.log('CalendarTab: Initial load with session', session.user.email);
      console.log('CalendarTab: Current projectId:', projectId, '(type:', typeof projectId, ')');
      console.log('CalendarTab: Current projectId length:', projectId.length);
      console.log('CalendarTab: Current projectId char codes:', [...projectId].map(c => c.charCodeAt(0)));
      forceRefresh();
    }

    // Cleanup function to clear timeout if component unmounts
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [projectId, session]);

  // Fetch tasks when projectId changes
  useEffect(() => {
    console.log('CalendarTab: projectId changed to:', projectId, '(type:', typeof projectId, ')');
    console.log('CalendarTab: projectId length:', projectId.length);
    console.log('CalendarTab: projectId char codes:', [...projectId].map(c => c.charCodeAt(0)));

    // Fetch tasks for the new project
    if (projectId) {
      console.log('CalendarTab: Fetching tasks for new project:', projectId);
      setLocalLoading(true);

      // Set a timeout to prevent infinite loading state
      loadingTimeoutRef.current = setTimeout(() => {
        setLocalLoading(false);
        setLocalError('Loading tasks timed out. Please try refreshing.');
      }, 15000); // 15 second timeout

      fetchProjectTasks()
        .finally(() => {
          // Clear the timeout if the operation completes
          if (loadingTimeoutRef.current) {
            clearTimeout(loadingTimeoutRef.current);
            loadingTimeoutRef.current = null;
          }
          setLocalLoading(false);
        });
    }
  }, [projectId]);

  // Process tasks and personal items into calendar events
  const processEventsData = () => {
    try {
      console.log('CalendarTab: Processing events data...');
      console.log('CalendarTab: Current projectId from props:', projectId);
      console.log('CalendarTab: Total project tasks available:', projectTasks.length);

      const events: CalendarEvent[] = [];
      const currentUserEmail = session?.user?.email;

      // Debug: Log first few tasks to check their projectId values
      if (projectTasks.length > 0) {
        console.log('CalendarTab: Sample project tasks:');
        projectTasks.slice(0, 3).forEach(task => {
          console.log(`Task ${task.id}: title=${task.title}, projectId=${task.projectId}`);
        });
      } else {
        console.log('CalendarTab: No tasks found for this project');
      }

      console.log(`Using ${projectTasks.length} tasks for project ${projectId}`);

      // Debug: Log the tasks
      if (projectTasks.length > 0) {
        console.log('CalendarTab: Project tasks:');
        projectTasks.slice(0, 3).forEach(task => {
          console.log(`Task ${task.id}: ${task.title} - projectId=${task.projectId}`);
        });
      } else {
        console.log('CalendarTab: No tasks found for this project');
      }

      // Add tasks to events based on filter settings
      projectTasks.forEach(task => {
        let isAssignedToCurrentUser = false;

        if (currentUserEmail && task.assignedTo) {
          if (!Array.isArray(task.assignedTo)) {
            console.warn(`Task ${task.id} has invalid assignedTo format:`, task.assignedTo);
            return;
          }

          isAssignedToCurrentUser = task.assignedTo.some((assignee: any) => {
            if (typeof assignee === 'string') {
              return assignee.toLowerCase() === currentUserEmail.toLowerCase();
            }
            return false;
          });
        }

        // Determine if task should be shown based on filters
        const showTask = filter.showAllProjectTasks || (filter.showUserTasks && isAssignedToCurrentUser);

        if (!showTask) {
          console.log(`Task ${task.id} skipped due to filter settings`);
          return;
        }

        if (!task.startDate || !task.dueDate) {
          console.warn(`Task ${task.id} missing dates: startDate=${task.startDate}, dueDate=${task.dueDate}`);
          return;
        }

        const startDate = task.startDate instanceof Date ? task.startDate : new Date(task.startDate);
        const endDate = task.dueDate instanceof Date ? task.dueDate : new Date(task.dueDate);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          console.warn(`Invalid date for task ${task.id}`, task);
          return;
        }

        events.push({
          id: task.id,
          title: task.title + (isAssignedToCurrentUser ? ' (Assigned to you)' : ''),
          start: startDate,
          end: endDate,
          color: getTaskColor(task.priority, task.status),
          type: 'task',
          priority: task.priority,
          status: task.status,
          projectId: task.projectId
        });

        console.log(`Added task to calendar: ${task.id} - ${task.title}`);
      });

      // Add personal items to events if filter is enabled
      if (filter.showUserItems && personalItems.length > 0) {
        console.log(`Processing ${personalItems.length} personal items`);
        personalItems.forEach(item => {
          // Create start date with time if available
          let startDate = item.startDate instanceof Date ? new Date(item.startDate) : new Date(item.startDate);
          let endDate = item.endDate instanceof Date ? new Date(item.endDate) : new Date(item.endDate);

          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            console.warn(`Invalid date for personal item ${item.id}`, item);
            return;
          }

          // Apply time if available
          if (item.startTime) {
            const [startHours, startMinutes] = item.startTime.split(':').map(Number);
            startDate.setHours(startHours, startMinutes, 0, 0);
          }

          if (item.endTime) {
            const [endHours, endMinutes] = item.endTime.split(':').map(Number);
            endDate.setHours(endHours, endMinutes, 0, 0);
          }

          events.push({
            id: item.id,
            title: item.title,
            start: startDate,
            end: endDate,
            color: item.color || '#9333ea',
            type: 'personal'
          });

          console.log(`Added personal item to calendar: ${item.id} - ${item.title} (${startDate.toLocaleString()} - ${endDate.toLocaleString()})`);
        });
      }

      console.log(`Total events to display: ${events.length}`);

      // Update state only if events have changed
      const currentEventsJson = JSON.stringify(calendarEvents);
      const newEventsJson = JSON.stringify(events);
      if (currentEventsJson !== newEventsJson) {
        console.log('Events changed, updating state');
        setCalendarEvents(events);
      } else {
        console.log('Events unchanged, skipping state update');
      }
    } catch (err) {
      console.error('Error processing calendar events:', err);
    }
  };

  // Determine task color based on priority and status
  const getTaskColor = (priority: string, status: string): string => {
    if (status === 'Complete') return '#10b981'; // Green for completed tasks
    switch (priority) {
      case 'Critical':
        return '#ef4444'; // Red
      case 'High':
        return '#f97316'; // Orange
      case 'Medium':
        return '#3b82f6'; // Blue
      case 'Low':
        return '#6b7280'; // Gray
      default:
        return '#3b82f6'; // Default to blue
    }
  };

  // Handle filter changes
  const handleFilterChange = (filterKey: keyof CalendarFilter) => {
    setFilter(prev => ({
      ...prev,
      [filterKey]: !prev[filterKey]
    }));
  };

  // Reprocess events when filters, tasks, or personal items change
  useEffect(() => {
    console.log('Filter changed, processing events');
    if (projectTasks.length > 0 || personalItems.length > 0) {
      processEventsData();
    }
  }, [filter, projectId]);

  useEffect(() => {
    console.log(`Project tasks changed (${projectTasks.length} tasks), processing events`);
    if (projectTasks.length > 0 || personalItems.length > 0) {
      processEventsData();
    }
  }, [projectTasks, projectId]);

  useEffect(() => {
    console.log(`Personal items changed (${personalItems.length} items), processing events`);
    if (projectTasks.length > 0 || personalItems.length > 0) {
      processEventsData();
    }
  }, [personalItems, projectId]);

  // Handlers for UI interactions
  const handleAddItem = () => {
    setEditingItem(null);
    setShowAddForm(true);
  };

  const handleEditItem = (item: PersonalItem) => {
    setEditingItem(item);
    setShowAddForm(true);
  };

  const handleFormSubmit = () => {
    setShowAddForm(false);
    setEditingItem(null);
    loadData();
  };

  const handleViewChange = (view: CalendarViewType) => {
    setCalendarView(view);
    setShowGanttView(false);
  };

  const handleToggleGanttView = () => {
    setShowGanttView(prev => !prev);
  };

  return (
    <div className="space-y-6">
      <CalendarDebug />

      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Project Calendar</h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleAddItem}
            className="flex items-center gap-2 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
          >
            <Plus size={18} />
            Add Personal Item
          </button>
          <button
            onClick={loadData}
            className="p-2 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600"
            title="Refresh calendar data"
            disabled={localLoading || loading}
          >
            <RefreshCw size={18} className={loading || localLoading ? 'animate-spin' : ''} />
          </button>
          <button
            onClick={forceRefresh}
            className="p-2 bg-blue-700 text-gray-200 rounded-md hover:bg-blue-600"
            title="Force refresh all data"
            disabled={localLoading || loading}
          >
            <RefreshCw size={18} className={`${loading || localLoading ? 'animate-spin' : ''} -rotate-90`} />
          </button>
        </div>
      </div>

      {(localError || error) && (
        <div className="bg-red-900/30 border border-red-500 text-red-200 px-4 py-3 rounded-md flex items-start">
          <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
          <div className="flex flex-col">
            <span className="font-medium">{localError || error}</span>
            {localError && localError.includes('No tasks found') && (
              <div className="mt-2 text-sm">
                <p>Possible solutions:</p>
                <ul className="list-disc pl-5 mt-1">
                  <li>Check if tasks have been created for this project</li>
                  <li>Verify you have permission to access tasks for this project</li>
                  <li>Try refreshing the page</li>
                </ul>
              </div>
            )}
            {localError && localError.includes('timed out') && (
              <div className="mt-2">
                <button
                  onClick={() => forceRefresh()}
                  className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center justify-center mt-2"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex flex-wrap justify-between items-center mb-4">
          {/* View selector */}
          <div className="flex space-x-2 mb-2 sm:mb-0">
            <button
              onClick={() => handleViewChange('day')}
              className={`px-3 py-1.5 rounded-md ${calendarView === 'day' && !showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
            >
              Day
            </button>
            <button
              onClick={() => handleViewChange('week')}
              className={`px-3 py-1.5 rounded-md ${calendarView === 'week' && !showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
            >
              Week
            </button>
            <button
              onClick={() => handleViewChange('month')}
              className={`px-3 py-1.5 rounded-md ${calendarView === 'month' && !showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
            >
              Month
            </button>
            <button
              onClick={handleToggleGanttView}
              className={`px-3 py-1.5 rounded-md ${showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
            >
              Gantt
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap items-center space-x-3">
            <span className="text-gray-400 flex items-center">
              <Filter size={16} className="mr-1" />
              Filters:
            </span>
            <label className="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={filter.showUserTasks}
                onChange={() => handleFilterChange('showUserTasks')}
                className="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-500 focus:ring-purple-500"
              />
              <span className="ml-2 text-gray-300">My Tasks</span>
            </label>
            <label className="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={filter.showUserItems}
                onChange={() => handleFilterChange('showUserItems')}
                className="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-500 focus:ring-purple-500"
              />
              <span className="ml-2 text-gray-300">My Items</span>
            </label>
            <label className="inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={filter.showAllProjectTasks}
                onChange={() => handleFilterChange('showAllProjectTasks')}
                className="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-500 focus:ring-purple-500"
              />
              <span className="ml-2 text-gray-300">All Project Tasks</span>
            </label>
          </div>
        </div>

        {/* Calendar or Gantt view */}
        <div className="mt-4">
          {showGanttView ? (
            <>
              {console.log('CalendarTab: Rendering GanttChartView with projectId:', projectId)}
              {console.log('CalendarTab: Filtering tasks for Gantt view, total tasks:', tasks.length)}
              {console.log('CalendarTab: Filtered tasks for Gantt:', tasks.filter(task => {
                const matches = String(task.projectId).trim() === String(projectId).trim();
                console.log(`Task ${task.id}: projectId=${task.projectId}, matches=${matches}`);
                return matches;
              }).length)}

              {/* Try alternative filtering approaches for Gantt view */}
              {console.log('CalendarTab: Trying alternative filtering for Gantt:')}
              {console.log('Approach 1 (includes):', tasks.filter(task => {
                if (!task.projectId) return false;
                const taskId = String(task.projectId).trim();
                const currentId = String(projectId).trim();
                return taskId.includes(currentId) || currentId.includes(taskId);
              }).length)}
              {console.log('Approach 2 (loose equality):', tasks.filter(task => {
                if (!task.projectId) return false;
                return String(task.projectId).trim() == String(projectId).trim();
              }).length)}

              <GanttChartView
                projectId={projectId}
                tasks={projectTasks}
              />
            </>
          ) : (
            <>
              {(localLoading || loading) && (
                <div className="absolute inset-0 flex justify-center items-center bg-gray-900/50 z-10 rounded-lg">
                  <div className="bg-gray-800 p-6 rounded-lg shadow-lg flex items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500 mr-3"></div>
                    <span className="text-gray-200 text-lg">Loading calendar...</span>
                  </div>
                </div>
              )}
              <div className="relative min-h-[600px]">
                <CalendarView
                  events={calendarEvents}
                  view={calendarView}
                  onEventClick={(event: CalendarEvent) => {
                    if (event.type === 'personal') {
                      const item = personalItems.find((item: PersonalItem) => item.id === event.id);
                      if (item) handleEditItem(item);
                    } else if (event.type === 'task') {
                      const task = projectTasks.find((task: Task) => task.id === event.id);
                      if (task) setSelectedTask(task);
                    }
                  }}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* Personal item form modal */}
      {showAddForm && (
        <PersonalItemForm
          initialValues={editingItem}
          onClose={() => {
            setShowAddForm(false);
            setEditingItem(null);
          }}
          onSubmit={handleFormSubmit}
          currentUser={session?.user?.email || ''}
          users={users}
        />
      )}

      {/* Task details modal */}
      {selectedTask && (
        <TaskDetailsModal
          task={selectedTask}
          onClose={() => setSelectedTask(null)}
          onEdit={() => {
            // Here you would typically open the task edit form
            // For now, we'll just close the modal
            setSelectedTask(null);
          }}
        />
      )}

      {/* Debug component - visible only in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 border-t border-gray-700 pt-4">
          <CalendarDebug />
        </div>
      )}
    </div>
  );
};

export default CalendarTab;