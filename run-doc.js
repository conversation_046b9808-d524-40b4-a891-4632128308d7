/**
 * run-doc.js
 *
 * Simple script to run process-document.ts with the correct memory settings
 */

const { spawnSync } = require('child_process');
const path = require('path');

// Get the file path from command line arguments
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error('Please provide a file path to process');
  process.exit(1);
}

const filePath = args[0];
console.log(`Processing file: ${filePath}`);
console.log(`Using 8GB (8192MB) memory limit with aggressive chunking`);

// Run ts-node with increased memory and project option
const result = spawnSync('npx', [
  '--node-options=--max-old-space-size=8192',
  'ts-node',
  '--transpileOnly',
  '--project', 'scripts/tsconfig.json',
  'scripts/process-document.ts',
  filePath,
  ...args.slice(1)
], {
  stdio: 'inherit',
  shell: true
});

// Exit with the same code
process.exit(result.status);
