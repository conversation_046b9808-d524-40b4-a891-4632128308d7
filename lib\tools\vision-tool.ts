/**
 * Vision Tool for analyzing images with various vision models
 * Supports OpenAI, Google Gemini, and Groq models
 */

import OpenAI from 'openai';
import { GoogleGenAI } from "@google/genai";
import { createGroqClient } from "../llms/groq";
import { v4 as uuidv4 } from 'uuid';

// Define types for vision processing
export type VisionModel = 'gpt-4o' | 'gpt-4.1-mini' | 'gemini-2.5-flash' | 'meta-llama/llama-4-maverick-17b-128e-instruct';
export type VisionTask = 'describe' | 'analyze' | 'extract-text' | 'custom';

export interface VisionProcessingOptions {
  imageUrl: string;
  prompt?: string;
  model?: VisionModel;
  task?: VisionTask;
  userEmail?: string;
  userId?: string;
}

export interface VisionProcessingResult {
  content: string;
  model: VisionModel;
  imageUrl: string;
  jobId: string;
  namespace: string;
  timestamp: string;
}

export class VisionTool {
  private openaiApiKey: string | undefined;
  private geminiApiKey: string | undefined;
  private defaultModel: VisionModel;

  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "analyzeImage",
    description: "Analyze images using various vision models including GPT-4o, GPT-4.1-mini, Gemini, and Llama-4-Maverick.",
    parameters: {
      type: "object",
      properties: {
        imageUrl: {
          type: "string",
          description: "URL of the image to analyze."
        },
        prompt: {
          type: "string",
          description: "Custom prompt for image analysis. If not provided, a default prompt based on the task will be used."
        },
        model: {
          type: "string",
          description: "The vision model to use.",
          enum: ["gpt-4o", "gpt-4.1-mini", "gemini-2.5-flash", "meta-llama/llama-4-maverick-17b-128e-instruct"],
          default: "gpt-4o"
        },
        task: {
          type: "string",
          description: "The type of analysis to perform.",
          enum: ["describe", "analyze", "extract-text", "custom"],
          default: "describe"
        },
        userId: {
          type: "string",
          description: "User ID for Firebase storage integration."
        }
      },
      required: ["imageUrl"]
    },
    returns: {
      type: "object",
      properties: {
        content: {
          type: "string",
          description: "The analysis result"
        },
        model: {
          type: "string",
          description: "The model used for analysis"
        },
        imageUrl: {
          type: "string",
          description: "URL of the analyzed image"
        },
        jobId: {
          type: "string",
          description: "Unique identifier for the analysis job"
        },
        namespace: {
          type: "string",
          description: "Namespace for the analysis in the storage system"
        }
      }
    },
    examples: [
      {
        input: { imageUrl: "https://example.com/image.jpg", task: "describe" },
        output: "Returns a detailed description of the image"
      },
      {
        input: { imageUrl: "https://example.com/document.jpg", task: "extract-text", model: "gpt-4o" },
        output: "Returns the text extracted from the document image"
      }
    ]
  };

  constructor() {
    this.openaiApiKey = process.env.OPENAI_API_KEY;
    this.geminiApiKey = process.env.GEMINI_API_KEY;
    this.defaultModel = 'gpt-4o';
  }

  /**
   * Process an image with vision models
   * @param options - Vision processing options
   * @returns - The vision processing result
   */
  async processImage(options: VisionProcessingOptions): Promise<VisionProcessingResult> {
    const {
      imageUrl,
      prompt,
      model = this.defaultModel,
      task = 'describe',
      userEmail,
      userId
    } = options;

    if (!imageUrl) {
      throw new Error('Image URL is required for vision processing');
    }

    // Generate a unique job ID for this request
    const jobId = `vision-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    const namespace = uuidv4();

    // Determine the prompt to use based on the task
    const finalPrompt = prompt || this.getDefaultPrompt(task);

    // Process the image based on the model
    let content: string;

    if (model === 'gpt-4o' || model === 'gpt-4.1-mini') {
      content = await this.processWithOpenAI(imageUrl, finalPrompt, model);
    } else if (model === 'gemini-2.5-flash') {
      content = await this.processWithGemini(imageUrl, finalPrompt);
    } else if (model === 'meta-llama/llama-4-maverick-17b-128e-instruct') {
      content = await this.processWithGroq(imageUrl, finalPrompt, userEmail);
    } else {
      throw new Error(`Unsupported model: ${model}`);
    }

    // Store metadata in Firebase if userId is provided
    if (userId) {
      await this.storeMetadataInFirebase(userId, jobId, namespace, imageUrl, model, content, task);
    }

    return {
      content,
      model,
      imageUrl,
      jobId,
      namespace,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get a default prompt based on the task
   * @param task - The vision task
   * @returns - A default prompt for the task
   */
  private getDefaultPrompt(task: VisionTask): string {
    switch (task) {
      case 'describe':
        return 'Describe this image in detail. Include information about the main subjects, setting, colors, mood, and any notable elements.';
      case 'analyze':
        return `Analyze this image comprehensively:
1. Main subjects and composition
2. Visual elements (colors, lighting, style)
3. Context and setting
4. Mood and atmosphere
5. Technical aspects (if relevant)
6. Potential symbolism or meaning`;
      case 'extract-text':
        return 'Extract and transcribe all text visible in this image. Maintain the original formatting where possible.';
      case 'custom':
        return 'Describe what you see in this image.';
      default:
        return 'Describe what you see in this image.';
    }
  }

  /**
   * Process an image with OpenAI's vision models
   * @param imageUrl - URL of the image to process
   * @param prompt - The prompt to use
   * @param model - The OpenAI model to use
   * @returns - The processing result
   */
  private async processWithOpenAI(imageUrl: string, prompt: string, model: 'gpt-4o' | 'gpt-4.1-mini'): Promise<string> {
    if (!this.openaiApiKey) {
      throw new Error('OpenAI API key is not configured');
    }

    const openai = new OpenAI({ apiKey: this.openaiApiKey });

    try {
      const response = await openai.chat.completions.create({
        model: model,
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              { type: 'image_url', image_url: { url: imageUrl } }
            ]
          }
        ],
        max_tokens: 1000
      });

      return response.choices[0]?.message?.content || 'No content generated';
    } catch (error: any) {
      console.error('OpenAI vision processing error:', error);
      throw new Error(`Failed to process image with OpenAI: ${error.message}`);
    }
  }

  /**
   * Process an image with Google's Gemini model
   * @param imageUrl - URL of the image to process
   * @param prompt - The prompt to use
   * @returns - The processing result
   */
  private async processWithGemini(imageUrl: string, prompt: string): Promise<string> {
    if (!this.geminiApiKey) {
      throw new Error('Google API key is not configured. Please set the GEMINI_API_KEY environment variable.');
    }

    try {
      // Initialize the Google GenAI client
      const ai = new GoogleGenAI({ apiKey: this.geminiApiKey });

      // Fetch the image data
      const response = await fetch(imageUrl);
      const imageArrayBuffer = await response.arrayBuffer();
      const base64ImageData = Buffer.from(imageArrayBuffer).toString('base64');

      // Generate content with the image
      const result = await ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: [
          {
            inlineData: {
              mimeType: response.headers.get('content-type') || 'image/jpeg',
              data: base64ImageData,
            },
          },
          { text: prompt }
        ],
      });

      // Extract text from the response
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts) {
        // Find the text part in the response
        for (const part of result.candidates[0].content.parts) {
          if (part.text) {
            return part.text;
          }
        }
      }

      return 'No content generated';
    } catch (error: any) {
      console.error('Gemini vision processing error:', error);
      throw new Error(`Failed to process image with Gemini: ${error.message}`);
    }
  }

  /**
   * Process an image with Groq's Llama-4-Maverick model
   * @param imageUrl - URL of the image to process
   * @param prompt - The prompt to use
   * @param userEmail - User email for API key selection
   * @returns - The processing result
   */
  private async processWithGroq(imageUrl: string, prompt: string, userEmail?: string): Promise<string> {
    try {
      // Initialize Groq client with the user's email if available
      const groq = createGroqClient({ userEmail: userEmail || '<EMAIL>' });

      const chatCompletion = await groq.chat.completions.create({
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: prompt
              },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl
                }
              }
            ]
          }
        ],
        model: "meta-llama/llama-4-maverick-17b-128e-instruct",
        temperature: 0.7,
        max_tokens: 1024,
      });

      if (!chatCompletion?.choices?.[0]?.message?.content) {
        throw new Error("Invalid response format from Groq API");
      }

      return chatCompletion.choices[0].message.content;
    } catch (error: any) {
      console.error('Groq vision processing error:', error);
      throw new Error(`Failed to process image with Groq: ${error.message}`);
    }
  }

  /**
   * Store metadata in Firebase
   * @param userId - User ID for Firebase storage
   * @param jobId - Job ID for the analysis
   * @param namespace - Namespace for the analysis
   * @param imageUrl - URL of the analyzed image
   * @param model - Model used for analysis
   * @param content - Analysis content
   * @param task - Analysis task
   */
  private async storeMetadataInFirebase(
    userId: string,
    jobId: string,
    namespace: string,
    imageUrl: string,
    model: string,
    content: string,
    task: string
  ): Promise<void> {
    try {
      // Dynamically import Firebase admin to avoid issues with server components
      const { adminDb } = await import('components/firebase-admin');

      // Store metadata in Firestore
      await adminDb.collection('users').doc(userId).collection('visionAnalysis').doc(jobId).set({
        userId,
        jobId,
        namespace,
        imageUrl,
        model,
        task,
        content,
        createdAt: new Date(),
        status: 'completed'
      });

      // Create record in files collection with namespace
      await adminDb.collection('users').doc(userId).collection('files').doc(namespace).set({
        category: 'Image Analysis',
        createdAt: new Date(),
        downloadUrl: imageUrl,
        isImage: true,
        isAnalysis: true,
        name: `Image Analysis (${task})`,
        namespace: namespace,
        ref: `uploads/${userId}/vision/${jobId}.json`,
        type: 'application/json',
        jobId: jobId,
        model: model,
        task: task
      });

      console.log(`Vision analysis metadata stored for job: ${jobId} with namespace: ${namespace}`);
    } catch (error: any) {
      console.error('Error storing vision analysis metadata:', error);
      // Don't throw here, as we still want to return the analysis result
    }
  }
}

// Export a singleton instance
export const visionTool = new VisionTool();
