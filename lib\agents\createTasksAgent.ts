/**
 * Create Tasks Agent
 *
 * This agent automates the creation of tasks for a project by:
 * 1. Using the PromptAgent to optimize the initial request
 * 2. Using the generateTasksListTool to generate a list of tasks
 * 3. Using the createTasksTool to create the tasks in the system
 * 4. Using the chartTool to visualize the task list
 */

import { promptAgent, PromptAgentResult } from './prompt/PromptAgent';
import { generateTasksListTool, GenerateTasksInput, GenerateTasksResponse, TaskItem } from 'lib/tools/generateTasksListTool';
import { createTasksTool, CreateTasksInput, CreateTasksResponse } from 'lib/tools/createTasksTool';
import { chartTool, ChartGenerationResult, CHART_TYPES } from 'lib/tools';
import { Project, Task } from 'admin/planner/types';

export interface CreateTasksAgentOptions {
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CreateTasksStreamUpdate) => void;
}

export interface CreateTasksStreamUpdate {
  stage: 'optimizing-prompt' | 'generating-tasks' | 'creating-tasks' | 'visualizing-tasks' | 'complete';
  data?: any;
  message?: string;
}

export interface CreateTasksAgentResult {
  success: boolean;
  originalPrompt: string;
  optimizedPrompt: string;
  reasoning: string;
  tasks: Task[];
  creationResults: CreateTasksResponse;
  taskListVisualization?: ChartGenerationResult;
  error?: string;
}

export class CreateTasksAgent {
  private options: CreateTasksAgentOptions;

  constructor(options: CreateTasksAgentOptions = {}) {
    this.options = {
      includeExplanation: options.includeExplanation ?? true,
      streamResponse: options.streamResponse ?? false,
      onStreamUpdate: options.onStreamUpdate
    };
  }

  /**
   * Generate and create tasks for a project
   * @param projectId - ID of the project to create tasks for
   * @param project - Project details
   * @param prompt - User's prompt describing the tasks to create
   * @returns - Result of the task creation process
   */
  async createTasks(
    projectId: string,
    project: Project,
    prompt: string
  ): Promise<CreateTasksAgentResult> {
    try {
      if (!projectId || !project || !prompt) {
        throw new Error("Project ID, project details, and prompt are required");
      }

      console.log(`CreateTasksAgent: Processing request for project "${project.name}" (${projectId})`);

      // Step 1: Optimize the prompt using PromptAgent
      this._streamUpdate('optimizing-prompt', null, 'Optimizing your request...');
      console.log("CreateTasksAgent: Optimizing prompt...");

      const optimizeResult = await this._optimizePrompt(project, prompt);

      if (!optimizeResult.success) {
        throw new Error(`Failed to optimize prompt: ${optimizeResult.error}`);
      }

      console.log("CreateTasksAgent: Prompt optimized successfully");

      // Step 2: Generate tasks list using generateTasksListTool
      this._streamUpdate('generating-tasks', null, 'Generating task list...');
      console.log("CreateTasksAgent: Generating tasks list...");

      const generateTasksInput: GenerateTasksInput = {
        projectId,
        projectTitle: project.name,
        projectDescription: project.description,
        projectCategories: project.categories || ['General'],
        startDate: this._formatDate(project.startDate),
        endDate: this._formatDate(project.endDate),
        additionalContext: optimizeResult.optimizedPrompt
      };

      const generateTasksResult = await this._generateTasksList(generateTasksInput);

      console.log(`CreateTasksAgent: Generated ${generateTasksResult.tasks.length} tasks`);

      // Step 3: Create tasks in the system using createTasksTool
      this._streamUpdate('creating-tasks', {
        reasoning: generateTasksResult.reasoning,
        tasks: generateTasksResult.tasks
      }, 'Creating tasks in the system...');

      console.log("CreateTasksAgent: Creating tasks in the system...");

      const createTasksInput: CreateTasksInput = {
        projectId,
        tasks: generateTasksResult.tasks
      };

      const createTasksResult = await this._createTasks(createTasksInput);

      console.log(`CreateTasksAgent: Created ${createTasksResult.results.filter(r => r.success).length} tasks successfully`);

      // Step 4: Generate a table visualization of the tasks
      this._streamUpdate('visualizing-tasks', null, 'Generating task list visualization...');
      console.log("CreateTasksAgent: Generating task list visualization...");

      const tasks = this._mapTaskItemsToTasks(generateTasksResult.tasks, projectId);
      const taskListVisualization = await this._generateTaskListVisualization(project.name, tasks);

      console.log("CreateTasksAgent: Task list visualization generated");

      // Step 5: Return the complete result
      const result: CreateTasksAgentResult = {
        success: true,
        originalPrompt: prompt,
        optimizedPrompt: optimizeResult.optimizedPrompt,
        reasoning: generateTasksResult.reasoning,
        tasks,
        creationResults: createTasksResult,
        taskListVisualization
      };

      this._streamUpdate('complete', result, 'Task creation complete!');

      return result;
    } catch (error) {
      console.error("CreateTasksAgent: Error creating tasks:", error);

      return {
        success: false,
        originalPrompt: prompt,
        optimizedPrompt: '',
        reasoning: '',
        tasks: [],
        creationResults: {
          results: [],
          summary: 'Task creation failed'
        },
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Optimize the prompt using PromptAgent
   * @private
   * @param project - Project details
   * @param prompt - Original prompt
   * @returns - Optimized prompt result
   */
  private async _optimizePrompt(project: Project, prompt: string): Promise<PromptAgentResult> {
    const enhancedPrompt = `
Project Name: ${project.name}
Description: ${project.description}
Timeline: ${new Date(project.startDate).toLocaleDateString()} to ${new Date(project.endDate).toLocaleDateString()}
Categories: ${project.categories ? project.categories.join(', ') : 'None'}

Additional Instructions: ${prompt || 'No additional instructions provided.'}

Based on the above project details, please create a comprehensive list of tasks that would be needed to complete this project successfully. For each task, provide:

1. A clear, concise title
2. A detailed description of what needs to be done
3. An estimated due date
4. A suggested status (Not Started, In Progress, Reviewed, or Complete)
5. Any categories/tags that would be relevant
6. Dependencies where appropriate
7. Appropriate priorities
8. Relevant notes for execution

Format your response as a numbered list with clear sections for each task.
`;

    return await promptAgent.optimizePrompt(enhancedPrompt);
  }

  /**
   * Generate tasks list using generateTasksListTool
   * @private
   * @param input - Input for the generateTasksListTool
   * @returns - Generated tasks list
   */
  private async _generateTasksList(input: GenerateTasksInput): Promise<GenerateTasksResponse> {
    return await generateTasksListTool.execute(input);
  }

  /**
   * Create tasks in the system using createTasksTool
   * @private
   * @param input - Input for the createTasksTool
   * @returns - Task creation results
   */
  private async _createTasks(input: CreateTasksInput): Promise<CreateTasksResponse> {
    return await createTasksTool.execute(input);
  }

  /**
   * Map TaskItem array to Task array
   * @private
   * @param taskItems - Array of TaskItem from generateTasksListTool
   * @param projectId - Project ID
   * @returns - Array of Task objects
   */
  private _mapTaskItemsToTasks(taskItems: TaskItem[], projectId: string): Task[] {
    return taskItems.map((task, index) => {
      // Convert priority to status if status is not available
      const taskStatus = (task as any).status || 'Not Started';

      // Create a Task object that matches the Task interface
      const mappedTask: Task = {
        id: `temp-${Date.now()}-${index}`,
        title: task.title,
        description: task.description,
        projectId,
        // Required fields from Task interface
        category: task.category,
        status: this._normalizeStatus(taskStatus),
        startDate: new Date(task.startDate), // Convert string to Date
        dueDate: new Date(task.dueDate),   // Convert string to Date
        assignedTo: [],
        priority: task.priority,
        dependencies: task.dependencies || [],
        // Optional fields
        notes: task.notes,
        createdBy: 'ai',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return mappedTask;
    });
  }

  /**
   * Normalize status string to valid status values
   * @private
   * @param status - Raw status string
   * @returns - Normalized status
   */
  private _normalizeStatus(status: string): 'Not Started' | 'In Progress' | 'Reviewed' | 'Complete' {
    const normalized = status.toLowerCase();

    if (normalized.includes('not') || normalized.includes('todo') || normalized.includes('to do') || normalized.includes('backlog')) {
      return 'Not Started';
    } else if (normalized.includes('progress') || normalized.includes('ongoing') || normalized.includes('doing')) {
      return 'In Progress';
    } else if (normalized.includes('review') || normalized.includes('testing') || normalized.includes('validation')) {
      return 'Reviewed';
    } else if (normalized.includes('complete') || normalized.includes('done') || normalized.includes('finish')) {
      return 'Complete';
    }

    return 'Not Started';
  }

  /**
   * Generate a table visualization of the tasks
   * @private
   * @param projectName - Name of the project
   * @param tasks - List of tasks
   * @returns - Chart generation result
   */
  private async _generateTaskListVisualization(
    projectName: string,
    tasks: Task[]
  ): Promise<ChartGenerationResult> {
    // Prepare data for the table
    const tableData = tasks.map((task, index) => {
      // Format dates for display
      const startDateStr = task.startDate instanceof Date
        ? task.startDate.toISOString().split('T')[0]
        : String(task.startDate);

      const dueDateStr = task.dueDate instanceof Date
        ? task.dueDate.toISOString().split('T')[0]
        : String(task.dueDate);

      // Format dependencies
      const dependenciesStr = task.dependencies && task.dependencies.length > 0
        ? task.dependencies.join(', ')
        : 'None';

      return {
        id: index + 1,
        title: task.title,
        priority: task.priority, // Using actual priority
        status: task.status,
        startDate: startDateStr,
        dueDate: dueDateStr,
        category: task.category || 'None',
        dependencies: dependenciesStr
      };
    });

    // Create a prompt for the table visualization
    const chartPrompt = `
Generate a table visualization titled "Task List for ${projectName}" with the following data:
${JSON.stringify(tableData, null, 2)}

The table should have the following columns:
- ID
- Title
- Priority (color-coded: Critical=Red, High=Orange, Medium=Yellow, Low=Green)
- Status (color-coded: Complete=Green, In Progress=Yellow, Reviewed=Blue, Not Started=Gray)
- Start Date
- Due Date
- Category
- Dependencies

Sort the tasks by start date.
Include a detailed explanation of the task breakdown.
`;

    // Generate the chart using the prompt with OpenAI Model 03-2025-04-16
    return await chartTool.generateChart({
      prompt: chartPrompt,
      chartType: CHART_TYPES.TABLE,
      model: "o3-2025-04-16",
      provider: "openai"
    });
  }

  /**
   * Format a date as YYYY-MM-DD
   * @private
   * @param date - Date to format (can be Date object or string)
   * @returns - Formatted date string
   */
  private _formatDate(date: Date | string): string {
    try {
      // If it's already a string in YYYY-MM-DD format, return it
      if (typeof date === 'string') {
        // Check if it's already in YYYY-MM-DD format
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          return date;
        }
        // Convert string to Date object
        date = new Date(date);
      }

      // Ensure it's a valid Date object
      if (!(date instanceof Date) || isNaN(date.getTime())) {
        console.warn('Invalid date provided to _formatDate:', date);
        // Return current date as fallback
        return new Date().toISOString().split('T')[0];
      }

      // Format the date as YYYY-MM-DD
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Error formatting date:', error);
      // Return current date as fallback
      return new Date().toISOString().split('T')[0];
    }
  }

  /**
   * Send a stream update if streaming is enabled
   * @private
   * @param stage - Current stage of the process
   * @param data - Data to include in the update
   * @param message - Message to include in the update
   */
  private _streamUpdate(stage: CreateTasksStreamUpdate['stage'], data: any, message: string): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        data,
        message
      });
    }
  }
}

// Export a singleton instance
export const createTasksAgent = new CreateTasksAgent();