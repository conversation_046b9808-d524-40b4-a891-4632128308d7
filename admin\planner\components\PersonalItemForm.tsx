'use client';

import React, { useState, useEffect } from 'react';
import { PersonalItem, User } from '../types';
import { Calendar, Clock, AlertTriangle, Trash2, X } from 'lucide-react';
import { addPersonalItem, updatePersonalItem, deletePersonalItem } from '../../../app/lib/firebase/planner';
import { isSameDay } from 'date-fns';

interface PersonalItemFormProps {
  initialValues?: PersonalItem | null;
  onClose: () => void;
  onSubmit: () => void;
  currentUser: string;
  users: User[];
}

const PersonalItemForm: React.FC<PersonalItemFormProps> = ({
  initialValues,
  onClose,
  onSubmit,
  currentUser,
  users
}) => {
  // Using Firebase functions directly
  const [formValues, setFormValues] = useState<Partial<PersonalItem>>({
    title: '',
    description: '',
    startDate: new Date(),
    endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Default to 1 day from now
    startTime: '09:00', // Default start time (9 AM)
    endTime: '17:00', // Default end time (5 PM)
    color: '#9333ea', // Default purple color
    ...initialValues
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get current user email
  const getUserEmail = (): string => {
    return currentUser || '';
  };

  // Format date for input
  const formatDateForInput = (date: Date | undefined): string => {
    if (!date) return '';
    return new Date(date).toISOString().split('T')[0];
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle date change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'startDate' | 'endDate') => {
    const { value } = e.target;
    if (!value) return;

    const date = new Date(value);
    setFormValues(prev => ({
      ...prev,
      [field]: date
    }));
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formValues.title?.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formValues.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    if (!formValues.endDate) {
      newErrors.endDate = 'End date is required';
    } else if (formValues.startDate && formValues.endDate && formValues.endDate < formValues.startDate) {
      newErrors.endDate = 'End date must be after start date';
    }

    if (!formValues.startTime) {
      newErrors.startTime = 'Start time is required';
    }

    if (!formValues.endTime) {
      newErrors.endTime = 'End time is required';
    }

    // Check if start and end times are valid when on the same day
    if (formValues.startDate && formValues.endDate &&
        formValues.startTime && formValues.endTime &&
        isSameDay(formValues.startDate, formValues.endDate)) {
      const [startHour, startMinute] = formValues.startTime.split(':').map(Number);
      const [endHour, endMinute] = formValues.endTime.split(':').map(Number);

      if (startHour > endHour || (startHour === endHour && startMinute >= endMinute)) {
        newErrors.endTime = 'End time must be after start time';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      const userEmail = getUserEmail();

      if (!userEmail) {
        throw new Error('User email not found');
      }

      if (initialValues?.id) {
        // Update existing item
        await updatePersonalItem(initialValues.id, userEmail, {
          ...formValues
        });
      } else {
        // Create new item
        await addPersonalItem({
          ...formValues,
          userId: userEmail,
          createdAt: new Date()
        } as Omit<PersonalItem, 'id'>);
      }

      onSubmit();
    } catch (error) {
      console.error('Error saving personal item:', error);
      setErrors(prev => ({
        ...prev,
        form: 'Failed to save item. Please try again.'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle item deletion
  const handleDelete = async () => {
    if (!initialValues?.id) return;

    if (!confirm('Are you sure you want to delete this item?')) return;

    setIsSubmitting(true);

    try {
      await deletePersonalItem(initialValues.id, getUserEmail());
      onSubmit();
    } catch (error) {
      console.error('Error deleting personal item:', error);
      setErrors(prev => ({
        ...prev,
        form: 'Failed to delete item. Please try again.'
      }));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Available colors
  const colorOptions = [
    { value: '#9333ea', label: 'Purple' },
    { value: '#3b82f6', label: 'Blue' },
    { value: '#10b981', label: 'Green' },
    { value: '#f97316', label: 'Orange' },
    { value: '#ef4444', label: 'Red' },
    { value: '#6b7280', label: 'Gray' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">
            {initialValues ? 'Edit Personal Item' : 'Add Personal Item'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={20} />
          </button>
        </div>

        {errors.form && (
          <div className="mb-4 p-3 bg-red-900/30 border border-red-500 rounded-md text-red-200 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span>{errors.form}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Title*
            </label>
            <input
              type="text"
              name="title"
              value={formValues.title || ''}
              onChange={handleInputChange}
              className={`w-full p-2 border rounded-md bg-gray-700 text-white ${errors.title ? 'border-red-500' : 'border-gray-600'}`}
              placeholder="Enter item title"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-500 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1" />
                {errors.title}
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Description
            </label>
            <textarea
              name="description"
              value={formValues.description || ''}
              onChange={handleInputChange}
              rows={3}
              className="w-full p-2 border rounded-md bg-gray-700 text-white border-gray-600"
              placeholder="Enter item description (optional)"
            />
          </div>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Start Date*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-300" />
              </div>
              <input
                type="date"
                value={formatDateForInput(formValues.startDate)}
                onChange={(e) => handleDateChange(e, 'startDate')}
                className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.startDate ? 'border-red-500' : 'border-gray-600'}`}
              />
            </div>
            {errors.startDate && (
              <p className="mt-1 text-sm text-red-500 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1" />
                {errors.startDate}
              </p>
            )}
          </div>

          {/* End Date */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              End Date*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-300" />
              </div>
              <input
                type="date"
                value={formatDateForInput(formValues.endDate)}
                onChange={(e) => handleDateChange(e, 'endDate')}
                className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.endDate ? 'border-red-500' : 'border-gray-600'}`}
              />
            </div>
            {errors.endDate && (
              <p className="mt-1 text-sm text-red-500 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1" />
                {errors.endDate}
              </p>
            )}
          </div>

          {/* Start Time */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Start Time*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Clock className="h-5 w-5 text-gray-300" />
              </div>
              <input
                type="time"
                name="startTime"
                value={formValues.startTime || '09:00'}
                onChange={handleInputChange}
                className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.startTime ? 'border-red-500' : 'border-gray-600'}`}
              />
            </div>
            {errors.startTime && (
              <p className="mt-1 text-sm text-red-500 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1" />
                {errors.startTime}
              </p>
            )}
          </div>

          {/* End Time */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              End Time*
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Clock className="h-5 w-5 text-gray-300" />
              </div>
              <input
                type="time"
                name="endTime"
                value={formValues.endTime || '17:00'}
                onChange={handleInputChange}
                className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.endTime ? 'border-red-500' : 'border-gray-600'}`}
              />
            </div>
            {errors.endTime && (
              <p className="mt-1 text-sm text-red-500 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1" />
                {errors.endTime}
              </p>
            )}
          </div>

          {/* Color */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Color
            </label>
            <div className="grid grid-cols-6 gap-2">
              {colorOptions.map(color => (
                <div
                  key={color.value}
                  className={`w-full aspect-square rounded-md cursor-pointer border-2 ${formValues.color === color.value ? 'border-white' : 'border-transparent'}`}
                  style={{ backgroundColor: color.value }}
                  onClick={() => setFormValues(prev => ({ ...prev, color: color.value }))}
                  title={color.label}
                ></div>
              ))}
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-between pt-4">
            <div>
              {initialValues && (
                <button
                  type="button"
                  onClick={handleDelete}
                  className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                  disabled={isSubmitting}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Delete
                </button>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : initialValues ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PersonalItemForm;
