/**
 * Investigative Research Agent System - Main Export File
 *
 * This file exports all the components of the Investigative Research Agent system
 * for easy integration with the PMO ecosystem and other parts of the application.
 */

// Import types and classes for local use
import type { InvestigativeAgentManagerConfig, PMOInvestigationRequest } from './InvestigativeResearchAgentManager';
import type { InvestigationRequest } from './InvestigativeResearchAgent';
import { InvestigativeResearchAgentManager } from './InvestigativeResearchAgentManager';
import { PMOInvestigativeIntegration } from './PMOInvestigativeIntegration';

// Core Agent Classes
export {
  InvestigativeResearchAgent,
  type JournalistPersona,
  type InvestigationRequest,
  type JournalistResponse,
  type InvestigationResult,
  type InvestigationProgressStep
} from './InvestigativeResearchAgent';

export {
  InvestigativeResearchAgentManager,
  type InvestigativeAgentManagerConfig,
  type InvestigationPreview,
  type PMOInvestigationRequest,
  type InvestigationProgress
} from './InvestigativeResearchAgentManager';

// PMO Integration
export {
  PMOInvestigativeIntegration,
  type PMOInvestigativeTaskRequest,
  type PMOInvestigativeOutput
} from './PMOInvestigativeIntegration';

// Team Agent Integration
export { InvestigativeResearchTeamAgent } from './InvestigativeResearchTeamAgent';

// Utility Functions and Constants

// Re-export constants from client-safe constants file
export {
  DEFAULT_JOURNALIST_MODELS,
  DEFAULT_SYSTEM_MODELS,
  DEFAULT_COMPARISON_MODELS,
  AVAILABLE_COMPARISON_MODELS,
  SYSTEM_CAPABILITIES,
  InvestigationType,
  getInvestigationTypeFromString,
  getRecommendedJournalistCount,
  calculateInvestigationComplexity
} from './constants';

/**
 * Factory function to create an Investigative Research Agent Manager
 */
export function createInvestigativeResearchAgent(userId: string, config?: Partial<InvestigativeAgentManagerConfig>) {
  return new InvestigativeResearchAgentManager({
    userId,
    defaultLlmProvider: config?.defaultLlmProvider || 'openai',
    defaultLlmModel: config?.defaultLlmModel || 'gpt-4o',
    ...config
  });
}

/**
 * Factory function to create PMO Investigative Integration
 */
export function createPMOInvestigativeIntegration(userId: string) {
  return new PMOInvestigativeIntegration(userId);
}

/**
 * Utility function to validate investigation request
 */
export function validateInvestigationRequest(request: Partial<InvestigationRequest>): string[] {
  const errors: string[] = [];

  if (!request.prompt || request.prompt.trim().length === 0) {
    errors.push('Investigation prompt is required');
  }

  if (!request.investigationType) {
    errors.push('Investigation type is required');
  }

  if (!request.selectedJournalists || request.selectedJournalists.length === 0) {
    errors.push('At least one journalist must be selected');
  }

  if (request.selectedJournalists && request.selectedJournalists.length > 6) {
    errors.push('Maximum of 6 journalists can be selected');
  }

  if (!request.userId) {
    errors.push('User ID is required');
  }

  return errors;
}

/**
 * Utility function to validate PMO investigation request
 */
export function validatePMOInvestigationRequest(request: Partial<PMOInvestigationRequest>): string[] {
  const errors: string[] = [];

  if (!request.pmoId) {
    errors.push('PMO ID is required');
  }

  if (!request.title || request.title.trim().length === 0) {
    errors.push('Investigation title is required');
  }

  if (!request.investigationType) {
    errors.push('Investigation type is required');
  }

  if (!request.userId) {
    errors.push('User ID is required');
  }

  if (!request.priority) {
    errors.push('Priority is required');
  }

  return errors;
}



/**
 * Utility function to format investigation duration
 */
export function formatInvestigationDuration(startTime: Date, endTime: Date): string {
  const durationMs = endTime.getTime() - startTime.getTime();
  const durationMinutes = Math.round(durationMs / (1000 * 60));
  
  if (durationMinutes < 1) {
    return 'Less than 1 minute';
  } else if (durationMinutes < 60) {
    return `${durationMinutes} minute${durationMinutes === 1 ? '' : 's'}`;
  } else {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    return `${hours}h ${minutes}m`;
  }
}



/**
 * Export version information
 */
export const INVESTIGATIVE_RESEARCH_AGENT_VERSION = '1.0.0';
export const INVESTIGATIVE_RESEARCH_AGENT_BUILD_DATE = new Date().toISOString();


