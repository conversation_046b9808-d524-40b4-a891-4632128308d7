// research/InformationRetrievalAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { ExtractionResult, ExtractionOptions, webContentExtractorTool } from '../../tools/web-content-extractor'; // Use tool instance
import { internetSearchTool, SearchOptions as InternetSearchOptions, SearchResultItem as InternetResultItem } from '../../tools/internet-search'; // Use tool instance
import { academicSearchTool, AcademicSearchOptions, AcademicSearchResultItem } from '../../tools/academic-search'; // Use tool instance
import { SearchQuery, RetrievalResult } from './ResearchInterfaces';
import { pdfGeneratorTool, PdfContent, PdfGenerationOptions } from '../../tools/pdf-generator';
import { SavePdfToByteStoreResult } from '../../tools/storage-tool';
import { markdownRendererTool } from '../../tools/markdown-renderer-tool';

export class InformationRetrievalAgent extends ResearchAgent {
    // Tools are now accessed via their singleton instances if exported that way
    // No need to instantiate them here unless dependency injection is preferred.

    constructor(
        id: string = 'info-retriever',
        name: string = 'Information Retriever',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'o3-2025-04-16'
    ) {
        const role = 'Information Retrieval Specialist';
        const description = `I find and retrieve raw information from specified sources (web URLs, web search, academic databases, PMO documents) based on search queries. I provide the collected data with source citations and perform document analysis.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Execute a search query using the appropriate tool and retrieve information
     * @param query - The search query details
     * @returns The retrieval result(s)
     */
    async executeSearch(query: SearchQuery): Promise<RetrievalResult[]> {
        console.log(`[${this.name}] Executing search: "${query.queryText}" (Type: ${query.sourceType}, SubTask: ${query.subTaskId})`);
        let results: RetrievalResult[] = [];
        const numResults = query.constraints?.numResults as number | undefined || 10; // Default or from constraints

        try {
            // A. Direct URL Extraction
            if ((query.sourceType === 'web' || query.sourceType === 'news') && (query.queryText.startsWith('http://') || query.queryText.startsWith('https://'))) {
                console.log(`[${this.name}] Using WebContentExtractorTool for URL: ${query.queryText}`);
                const extractionOptions: ExtractionOptions = {
                    format: 'markdown',
                    ...(query.constraints || {})
                };
                // Ensure researchWeb returns ExtractionResult for single URL
                const webResult = await this.researchWeb(query.queryText, extractionOptions) as ExtractionResult;

                 if (!webResult || webResult.title === "Error") {
                    console.warn(`[${this.name}] Failed to extract content from URL: ${query.queryText}`);
                     // Create a failure result
                     results.push(this.createFailedResult(query, `Failed to extract content from URL: ${webResult?.metadata?.error || 'Unknown reason'}`));
                 } else {
                    const reliability = await this.assessSourceReliability(webResult.url, webResult.metadata);
                    results.push({
                        resultId: `ret-url-${query.queryId}-${Date.now()}`,
                        queryId: query.queryId,
                        subTaskId: query.subTaskId,
                        sourceUrl: webResult.url,
                        title: webResult.title,
                        rawContent: webResult.rawContent.substring(0, 5000),
                        formattedContent: webResult.formattedContent, // Markdown content from extractor
                        metadata: webResult.metadata,
                        retrievedAt: new Date(),
                        initialReliabilityScore: reliability
                    });
                 }

            // B. Web/News Search Query
            } else if (query.sourceType === 'web' || query.sourceType === 'news') {
                console.log(`[${this.name}] Using InternetSearchTool for query: ${query.queryText}`);
                const searchOptions: InternetSearchOptions = { numResults };
                const searchResponse = await internetSearchTool.search(query.queryText, searchOptions);

                if (searchResponse.success === false || !searchResponse.results) {
                     console.warn(`[${this.name}] Internet search failed: ${searchResponse.metadata.error}`);
                     results.push(this.createFailedResult(query, `Internet search failed: ${searchResponse.metadata.error}`));
                } else {
                    results = searchResponse.results.map((item: InternetResultItem, index: number) => ({
                        resultId: `ret-inet-${query.queryId}-${index}-${Date.now()}`,
                        queryId: query.queryId,
                        subTaskId: query.subTaskId,
                        sourceUrl: item.link,
                        title: item.title,
                        rawContent: item.snippet || '', // Use snippet as raw content for search results
                        formattedContent: `### [${item.title}](${item.link})\n${item.snippet || 'No description available.'}`, // Simple formatted version
                        metadata: { // Add search-specific metadata if needed
                             searchRank: (index + 1).toString(),
                             searchSource: searchResponse.metadata.source,
                             ...item // Include original item fields if desired
                        },
                        retrievedAt: new Date(),
                        // Reliability assessment might be less meaningful here or need different logic
                        initialReliabilityScore: 0.5 // Placeholder - could analyze domain etc.
                    }));
                }


            // C. Academic Search Query
            } else if (query.sourceType === 'academic') {
                console.log(`[${this.name}] Using AcademicSearchTool for query: ${query.queryText}`);
                const searchOptions: AcademicSearchOptions = {
                    numResults,
                    isOa: query.constraints?.isOa as boolean | undefined // Pass OA constraint if present
                };
                const searchResponse = await academicSearchTool.search(query.queryText, searchOptions);

                if (searchResponse.success === false || !searchResponse.results) {
                     console.warn(`[${this.name}] Academic search failed: ${searchResponse.metadata.error}`);
                     results.push(this.createFailedResult(query, `Academic search failed: ${searchResponse.metadata.error}`));
                } else {
                    results = searchResponse.results.map((item: AcademicSearchResultItem, index: number) => {
                        const authors = item.authors?.join(', ') || 'N/A';
                        const journalInfo = item.journal ? `${item.journal}, ${item.year || 'N/A'}` : (item.year || 'N/A');
                        const formattedBiblio = `**Authors: <AUTHORS>

                        return {
                            resultId: `ret-acad-${query.queryId}-${index}-${Date.now()}`,
                            queryId: query.queryId,
                            subTaskId: query.subTaskId,
                            sourceUrl: item.bestOaUrl || (item.doi ? `https://doi.org/${item.doi}` : '#no-link'), // Prefer OA link, fallback to DOI
                            title: item.title || 'No Title',
                            rawContent: item.snippet || '', // Snippet or maybe abstract if available in full DOI object (not currently fetched)
                            formattedContent: `### ${item.title || 'No Title'}\n${formattedBiblio}\n${item.snippet ? `*Match:* ${item.snippet}\n` : ''}`, // Formatted summary
                            metadata: { // Include bibliographic metadata
                                 searchRank: (index + 1).toString(),
                                 searchSource: searchResponse.metadata.source,
                                 doi: item.doi || null,
                                 authors: item.authors?.join('; ') || null, // Semicolon separated for easier parsing?
                                 journal: item.journal || null,
                                 year: item.year?.toString() || null,
                                 isOa: item.isOa.toString(),
                                 bestOaUrl: item.bestOaUrl || null,
                                 score: item.score?.toString() || null
                            },
                            retrievedAt: new Date(),
                            initialReliabilityScore: item.isOa ? 0.7 : 0.6 // Slightly higher base for academic? Adjust as needed.
                        };
                    });
                }

            // D. Unsupported Type
            } else {
                console.warn(`[${this.name}] Unsupported source type for search query: ${query.sourceType}`);
                results.push(this.createFailedResult(query, `Unsupported source type: ${query.sourceType}`));
            }
        } catch (error: any) {
            console.error(`[${this.name}] Failed to execute search query ${query.queryId}:`, error);
            results.push(this.createFailedResult(query, `Unhandled error during search: ${error.message}`));
        }

        console.log(`[${this.name}] Search ${query.queryId} processed, returning ${results.length} results.`);
        return results;
    }

     /** Helper to create a consistent failure result */
    private createFailedResult(query: SearchQuery, errorMessage: string): RetrievalResult {
        return {
            resultId: `ret-fail-${query.queryId}-${Date.now()}`,
            queryId: query.queryId,
            subTaskId: query.subTaskId,
            sourceUrl: '#error',
            title: `Search Failed: ${query.queryText.substring(0, 50)}...`,
            rawContent: errorMessage,
            formattedContent: `**Error:** ${errorMessage}`,
            metadata: { error: errorMessage, query: query.queryText, type: query.sourceType.toString() },
            retrievedAt: new Date(),
            initialReliabilityScore: 0
        };
    }


    /**
     * Perform a basic assessment of source reliability (example)
     * Keep the previous logic, may apply mostly to direct URL scrapes.
     * Reliability for search results might need different heuristics (e.g., based on domain ranking, journal impact factor for academic).
     */
    async assessSourceReliability(url: string, metadata: Record<string, string | null> | undefined): Promise<number> {
       // Simple heuristic example: Check domain, presence of author, publication date
        // A more robust version would use LLM or external tools/databases.
        let score = 0.5; // Base score

        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname.replace(/^www\./, ''); // Normalize www

            // Domain-based scoring (example)
            if (domain.match(/\.(gov|edu)$/i)) score = 0.8;
            else if (domain.match(/\.(org)$/i) && !domain.match(/wikipedia\.org/i)) score = 0.65;
            else if (domain.match(/wikipedia\.org/i)) score = 0.3;
            else if (domain.match(/bbc\.com|nytimes\.com|reuters\.com|apnews\.com|wsj\.com|nature\.com|sciencemag\.org/i)) score = 0.75; // Reputable news/science
            else if (domain.match(/medium\.com|blogspot\.com|wordpress\.com/i)) score = 0.4; // Blog platforms


            // Metadata-based scoring (if available, from web scrape)
            if (metadata) {
                if (metadata['author'] || metadata['article:author'] || metadata['dc.creator']) score = Math.min(1, score + 0.1);
                if (metadata['article:published_time'] || metadata['datePublished'] || metadata['dc.date']) score = Math.min(1, score + 0.05);
                // Add more checks? e.g., presence of contact info, citations etc.
            }

        } catch {
            score = 0.2; // Invalid URL? Low reliability.
        }

        return Math.max(0.1, Math.min(0.9, score)); // Clamp between 0.1 and 0.9 for smoother scale
    }

    /**
     * Analyze PMO documents (DELEGATED from ResearchLeadAgent)
     */
    async analyzePMODocuments(taskData: {
        instruction: string;
        searchQuery: string;
        analysisType: string;
        pmoId: string;
        userRequest: string;
        pmoRecord: any;
        extractionRequirements: string[];
    }): Promise<any> {
        console.log(`[${this.name}] Analyzing PMO documents for: ${taskData.pmoId}`);

        try {
            // Simulate document retrieval and analysis
            // In a real implementation, this would use the QueryDocumentsAgent
            const analysisPrompt = `
            You are an Information Retrieval Specialist analyzing PMO documentation.

            PMO ANALYSIS REQUEST:
            PMO ID: ${taskData.pmoId}
            Search Query: ${taskData.searchQuery}
            User Request: ${taskData.userRequest}
            Analysis Type: ${taskData.analysisType}

            ${taskData.pmoRecord ? `PMO Record Data:
            Title: ${taskData.pmoRecord.title || 'N/A'}
            Description: ${taskData.pmoRecord.description || 'N/A'}
            Priority: ${taskData.pmoRecord.priority || 'N/A'}
            Status: ${taskData.pmoRecord.status || 'N/A'}
            Assessment: ${taskData.pmoRecord.pmoAssessment || 'N/A'}
            ` : 'No PMO record found in database'}

            EXTRACTION REQUIREMENTS:
            ${taskData.extractionRequirements.join('\n')}

            Based on the PMO information available, extract and structure the following:

            Return JSON format:
            {
              "tasks": ["list of specific actionable tasks identified"],
              "assessment": "comprehensive PMO assessment and analysis",
              "requirements": "detailed requirements and specifications",
              "documentSources": ["list of document sources analyzed"],
              "confidence": "high|medium|low",
              "recommendations": ["strategic recommendations based on analysis"]
            }

            Focus on actionable tasks, research requirements, and strategic objectives.
            `;

            const response = await this.processRequest(analysisPrompt);
            const analysisResult = JSON.parse(response);

            console.log(`[${this.name}] PMO document analysis completed for ${taskData.pmoId}`);
            return {
                success: true,
                analysis: analysisResult,
                analyzedBy: this.name,
                analyzedAt: new Date(),
                pmoId: taskData.pmoId
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error analyzing PMO documents:`, error);
            return {
                success: false,
                error: error.message,
                fallbackAnalysis: {
                    tasks: [`Analyze PMO ${taskData.pmoId}`, 'Gather requirements', 'Develop implementation plan'],
                    assessment: 'PMO document analysis requires implementation',
                    requirements: `Requirements analysis needed for ${taskData.pmoId}`,
                    documentSources: ['PMO database'],
                    confidence: 'low',
                    recommendations: ['Implement comprehensive PMO document analysis system']
                }
            };
        }
    }

    /**
     * Enhanced data collection and source analysis delegated from ResearchAgentManager
     */
    async performDataCollection(taskData: {
        instruction: string;
        searchQueries: string[];
        sourceTypes: string[];
        analysisRequirements: string[];
        context: any;
    }): Promise<any> {
        console.log(`[${this.name}] Performing comprehensive data collection`);

        try {
            const dataCollectionPrompt = `
            You are an Information Retrieval Specialist coordinating comprehensive data collection for a research project.

            DELEGATION CONTEXT:
            You have been delegated this task by the ResearchAgentManager as part of a coordinated research team effort.

            DATA COLLECTION REQUIREMENTS:
            Task: ${taskData.instruction}
            Search Queries: ${taskData.searchQueries.join(', ')}
            Source Types: ${taskData.sourceTypes.join(', ')}
            Analysis Requirements: ${taskData.analysisRequirements.join(', ')}

            PROJECT CONTEXT:
            ${JSON.stringify(taskData.context, null, 2)}

            YOUR SPECIALIZED ROLE:
            As the Information Retrieval Specialist, you excel at:
            - Identifying optimal data sources for research objectives
            - Developing comprehensive search strategies
            - Evaluating source credibility and reliability
            - Extracting relevant information from diverse sources
            - Organizing collected data for analysis by DataAnalystSynthesizerAgent

            COMPREHENSIVE DATA COLLECTION PLAN:
            Create a detailed data collection strategy that includes:

            1. SOURCE IDENTIFICATION STRATEGY
               - Primary sources (academic papers, official documents, industry reports)
               - Secondary sources (news articles, expert opinions, case studies)
               - Tertiary sources (encyclopedias, databases, aggregated data)
               - Web sources (company websites, government portals, professional networks)

            2. SEARCH METHODOLOGY
               - Keyword optimization strategies for each source type
               - Boolean search techniques for comprehensive coverage
               - Citation tracking for authoritative sources
               - Cross-referencing strategies for validation

            3. QUALITY ASSESSMENT FRAMEWORK
               - Source credibility evaluation criteria
               - Bias detection and mitigation strategies
               - Currency and relevance assessment
               - Authority and expertise verification

            4. DATA EXTRACTION PROTOCOL
               - Structured data extraction templates
               - Key information identification methods
               - Citation and reference management
               - Metadata collection for traceability

            5. COORDINATION WITH TEAM MEMBERS
               - Data organization for DataAnalystSynthesizerAgent analysis
               - Source documentation for ReportWriterFormatterAgent
               - Quality indicators for QualityAssuranceReviewerAgent review

            Return a comprehensive JSON response with:
            {
              "dataCollectionStrategy": "detailed strategy description",
              "sourceIdentification": ["list of specific sources to target"],
              "searchMethodology": ["list of search techniques and approaches"],
              "qualityFramework": ["list of quality assessment criteria"],
              "extractionProtocol": ["list of data extraction methods"],
              "teamCoordination": ["list of coordination points with other team members"],
              "expectedOutcomes": ["list of expected data collection outcomes"],
              "timeline": "estimated completion timeline",
              "qualityIndicators": ["list of success metrics"]
            }

            Focus on leveraging your expertise in information retrieval to create a comprehensive, methodical approach.
            `;

            const response = await this.processRequest(dataCollectionPrompt);
            const dataCollectionPlan = JSON.parse(response);

            console.log(`[${this.name}] Data collection strategy created successfully`);
            return {
                success: true,
                dataCollectionPlan,
                specialistAgent: this.name,
                delegatedBy: 'ResearchAgentManager',
                completedAt: new Date()
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error in data collection planning:`, error);
            return {
                success: false,
                error: error.message,
                fallbackPlan: {
                    dataCollectionStrategy: 'Basic web search and document retrieval',
                    sourceIdentification: ['Academic databases', 'Industry reports', 'Government sources'],
                    searchMethodology: ['Keyword search', 'Citation tracking'],
                    qualityFramework: ['Source authority', 'Publication date', 'Peer review status'],
                    extractionProtocol: ['Manual extraction', 'Structured templates'],
                    teamCoordination: ['Provide data to analyst', 'Support report writing'],
                    expectedOutcomes: ['Comprehensive data set', 'Source documentation'],
                    timeline: '3-5 days',
                    qualityIndicators: ['Source diversity', 'Data completeness']
                }
            };
        }
    }

    /**
     * Enhanced task handling with comprehensive LLM processing
     * Handles incoming tasks/messages for information retrieval with role-specific expertise
     */
    async handleTask(_messageContent: string, metadata: Record<string, any>): Promise<void> {
        // Handle PMO document analysis delegation
        if (metadata.taskType === 'analyzePMODocuments') {
            console.log(`[${this.name}] Handling PMO document analysis task`);
            const pmoTaskData = {
                instruction: metadata.instruction || 'Retrieve and analyze PMO documentation',
                searchQuery: metadata.searchQuery || 'PMO tasks requirements assessment',
                analysisType: metadata.analysisType || 'structured_extraction',
                pmoId: metadata.pmoId || 'unknown',
                userRequest: metadata.userRequest || '',
                pmoRecord: metadata.pmoRecord || null,
                extractionRequirements: metadata.extractionRequirements || [
                    'Identify specific actionable tasks',
                    'Extract PMO assessment and analysis',
                    'Gather detailed requirements and specifications'
                ]
            };
            const result = await this.analyzePMODocuments(pmoTaskData);
            // Send result back to ResearchLeadAgent
            await this.sendMessage('research-lead', 'PMO document analysis complete', {
                type: 'pmo_analysis_complete',
                result
            });
            return;
        }

        // Original information retrieval handling
        if (metadata?.subTaskId && metadata?.instruction) {
            console.log(`[${this.name}] Received task: ${metadata.subTaskId} - "${metadata.instruction}"`);

            // --- Improved Instruction Parsing (Example using simple keywords) ---
            let queryText = metadata.instruction;
            let sourceType: 'web' | 'academic' | 'news' = 'web'; // Default to web search

            if (metadata.instruction.match(/search academic|find papers|academic literature/i)) {
                sourceType = 'academic';
                queryText = metadata.instruction.replace(/search academic|find papers|academic literature/i, '').replace(/^ for /i,'').trim();
            } else if (metadata.instruction.match(/search news|find news|recent articles/i)) {
                sourceType = 'news'; // Handled by internet search tool
                 queryText = metadata.instruction.replace(/search news|find news|recent articles/i, '').replace(/^ for /i,'').trim();
            } else if (metadata.instruction.match(/search web|find websites|look up online/i)) {
                 sourceType = 'web';
                 queryText = metadata.instruction.replace(/search web|find websites|look up online/i, '').replace(/^ for /i,'').trim();
            } else if (metadata.instruction.startsWith('http://') || metadata.instruction.startsWith('https://')) {
                 sourceType = 'web'; // Direct URL
                 queryText = metadata.instruction;
            } else {
                 // Fallback: Assume web search if no keywords match
                 sourceType = 'web';
                 queryText = metadata.instruction.replace(/^Find information on: /i, '').trim(); // Basic cleanup
            }
             // --- End Parsing Example ---


            const searchQuery: SearchQuery = {
                queryId: `query-${metadata.subTaskId}-${Date.now()}`,
                subTaskId: metadata.subTaskId,
                queryText: queryText,
                sourceType: sourceType,
                constraints: metadata.constraints || {} // Pass constraints if provided
            };

            const retrievalResults = await this.executeSearch(searchQuery);

            // Generate a PDF report of the retrieval results
            const pdfResult = await this.generateRetrievalReport(retrievalResults, searchQuery);
            console.log(`[${this.name}] Generated PDF report for query: ${searchQuery.queryText}`);

            // Send results back to the Lead (placeholder ID)
            const leadAgentId = 'research-lead'; // Should come from task/plan context if possible
            await this.sendMessage(
                leadAgentId,
                `Retrieval complete for sub-task ${metadata.subTaskId}`,
                {
                    type: 'retrieval_complete',
                    subTaskId: metadata.subTaskId,
                    results: retrievalResults, // Attach results
                    pdfReport: pdfResult // Include the PDF report
                }
            );

            // Mark originating task as complete
             // Ensure the correct ID (might be parent task ID or sub-task ID depending on workflow)
            const taskIdToComplete = metadata.taskId || metadata.subTaskId;
            if(taskIdToComplete) {
                await this.updateTaskStatus(taskIdToComplete, 'completed');
            } else {
                console.warn(`[${this.name}] Could not determine task ID to mark as complete from metadata:`, metadata);
            }

        } else {
            console.warn(`[${this.name}] Received message without clear task structure:`, metadata);
        }
    }

    /**
     * Generate a PDF report of the retrieval results
     * @param results - The retrieval results
     * @param query - The search query
     * @returns Promise with the PDF result
     */
    async generateRetrievalReport(results: RetrievalResult[], query: SearchQuery): Promise<Buffer | SavePdfToByteStoreResult> {
        console.log(`[${this.name}] Generating PDF report for ${results.length} results from query: ${query.queryText}`);

        // Create content sections for the PDF
        const pdfContents: PdfContent[] = [
            {
                title: `Research Results: ${query.queryText}`,
                content: `This report contains information retrieved for the query: "${query.queryText}"

Source type: ${query.sourceType}
Number of results: ${results.length}
Query ID: ${query.queryId}
Sub-task ID: ${query.subTaskId}`
            },
            {
                title: 'Summary of Results',
                content: `${results.length} sources were found for this query. The following sections contain details about each source.`
            }
        ];

        // Add a section for each result
        results.forEach((result, index) => {
            // Prepare content with proper markdown formatting
            const contentToUse = result.formattedContent || result.rawContent.substring(0, 1000) + (result.rawContent.length > 1000 ? '...' : '');

            // Process the content through the markdown renderer
            const formattedContent = markdownRendererTool.preprocessMarkdown(
                `**URL:** ${result.sourceUrl}

**Retrieved at:** ${result.retrievedAt.toLocaleString()}

**Reliability Score:** ${result.initialReliabilityScore || 'Not assessed'}

**Content:**
${contentToUse}`
            );

            pdfContents.push({
                title: `Source ${index + 1}: ${result.title}`,
                content: formattedContent
            });
        });

        // Generate the PDF with byteStore integration
        const pdfOptions: PdfGenerationOptions = {
            title: `Research Results: ${query.queryText}`,
            subtitle: 'Information Retrieval Report',
            date: new Date().toLocaleDateString(),
            saveToByteStore: true,
            agentId: this.id,
            agentName: this.name,
            category: 'Research Agent',
            queryId: query.queryId,
            subTaskId: query.subTaskId,
            documentType: 'research_retrieval_report'
        };

        return await this.generatePdf(`Research Results: ${query.queryText}`, pdfContents, pdfOptions, true);
    }
}