/**
 * Test script to verify codebase documentation outputs appear in PMO OUTPUT tab
 * This checks both the agent output storage and retrieval
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, orderBy, where } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

async function testCodebaseDocOutputFix() {
  console.log('🔧 Testing Codebase Documentation Output Fix');
  console.log('=============================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Test user email
    const testUserEmail = '<EMAIL>';

    console.log(`\n📋 Step 1: Checking Agent_Output collection for user: ${testUserEmail}`);

    // Get agent outputs from user-specific collection
    const agentOutputsRef = collection(db, 'users', testUserEmail, 'Agent_Output');
    const outputsQuery = query(
      agentOutputsRef, 
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(outputsQuery);
    console.log(`   Found ${snapshot.docs.length} total agent outputs`);

    // Filter for codebase documentation outputs
    const codebaseDocOutputs = snapshot.docs.filter(doc => {
      const data = doc.data();
      return data.agentType && (
        data.agentType === 'CodebaseDocumentationOrchestrator' ||
        data.agentType === 'codebase-documentation'
      );
    });

    console.log(`\n🔍 Step 2: Found ${codebaseDocOutputs.length} codebase documentation outputs`);

    if (codebaseDocOutputs.length > 0) {
      console.log('\n📊 Codebase Documentation Outputs:');
      
      codebaseDocOutputs.slice(0, 5).forEach((doc, index) => {
        const data = doc.data();
        const createdAt = data.createdAt ? new Date(data.createdAt.seconds * 1000) : null;
        
        console.log(`\n   ${index + 1}. Output ID: ${doc.id}`);
        console.log(`      Agent Type: ${data.agentType}`);
        console.log(`      Title: ${data.title}`);
        console.log(`      Content Length: ${data.content ? data.content.length : 0} characters`);
        console.log(`      File URL: ${data.fileUrl || 'None'}`);
        console.log(`      Created: ${createdAt ? createdAt.toISOString() : 'No date'}`);
        
        // Check metadata
        if (data.metadata) {
          console.log(`      PMO Record ID: ${data.metadata.pmoRecordId || 'None'}`);
          console.log(`      Selected Paths: ${data.metadata.selectedPaths ? data.metadata.selectedPaths.length : 0} paths`);
          console.log(`      Documentation Scope: ${data.metadata.documentationScope || 'None'}`);
          console.log(`      Output Format: ${data.metadata.outputFormat || 'None'}`);
        }
        
        // Show content preview
        if (data.content) {
          const preview = data.content.substring(0, 200) + '...';
          console.log(`      Content Preview: ${preview}`);
        }
      });

      console.log('\n🎯 Step 3: PMO OUTPUT Tab Integration Test Results:');
      console.log('   With the fixes applied:');
      console.log('   ✅ PMO OUTPUT tab now includes CodebaseDocumentationOrchestrator in agent type queries');
      console.log('   ✅ getAgentTypeInfo function includes CodebaseDocumentationOrchestrator case');
      console.log('   ✅ Streaming endpoint uses consistent CodebaseDocumentationOrchestrator agent type');
      console.log('   ✅ Agent outputs include comprehensive metadata for PMO integration');
      
      console.log('\n📈 Expected Behavior:');
      console.log('   - Codebase documentation outputs should appear in PMO OUTPUT tab');
      console.log('   - Outputs should display with "Codebase Documentation" label and indigo color');
      console.log('   - Team should show as "Codebase Documentation Team"');
      console.log('   - Content should be viewable and downloadable');
      console.log('   - Metadata should include PMO record ID and documentation details');

    } else {
      console.log('\n⚠️  No codebase documentation outputs found.');
      console.log('   To test the fix:');
      console.log('   1. Create a new codebase documentation request');
      console.log('   2. Wait for the documentation generation to complete');
      console.log('   3. Check the PMO OUTPUT tab for the completed documentation');
      console.log('   4. Verify the output appears with proper labeling and metadata');
    }

    // Test agent type consistency
    console.log('\n🔄 Step 4: Agent Type Consistency Check:');
    const agentTypes = codebaseDocOutputs.map(doc => doc.data().agentType);
    const uniqueAgentTypes = [...new Set(agentTypes)];
    
    console.log(`   Found agent types: ${uniqueAgentTypes.join(', ')}`);
    
    if (uniqueAgentTypes.length === 1 && uniqueAgentTypes[0] === 'CodebaseDocumentationOrchestrator') {
      console.log('   ✅ Agent type consistency: All outputs use CodebaseDocumentationOrchestrator');
    } else if (uniqueAgentTypes.includes('codebase-documentation')) {
      console.log('   ⚠️  Found legacy agent type "codebase-documentation" - should be updated');
    } else {
      console.log('   ✅ Agent type consistency: Good');
    }

    console.log('\n✅ Codebase Documentation Output Fix Test Complete');

  } catch (error) {
    console.error('\n❌ Error testing codebase documentation output fix:', error);
  }
}

// Run the test
testCodebaseDocOutputFix().then(() => {
  console.log('\n🎉 Test execution finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test execution failed:', error);
  process.exit(1);
});
