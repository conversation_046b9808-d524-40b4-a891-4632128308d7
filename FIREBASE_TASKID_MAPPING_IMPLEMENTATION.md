# Firebase TaskId Mapping Implementation

## Overview

This document describes the implementation of the corrected TaskId mapping system in the PMOProjectsTaskAgent. The system now uses actual Firebase Project IDs and Task IDs instead of generating synthetic PMO format IDs.

## Problem Statement

The previous implementation was generating synthetic PMO Project IDs like "PMO-QA-COMPLIANCE-c76670a7" instead of using the actual Project IDs from Firebase collections. This caused inconsistencies between the task notes and the actual Firebase document structure.

## Correct Data Flow

### 1. PMO Collection Lookup
- Query PMO collection using PMO ID: `c76670a7-bc7b-44ea-9905-189a4bcf36c8`
- Locate the `generatedProjects` array field within this PMO record

### 2. Project ID Extraction
- Find the most recent project in the `generatedProjects` array by sorting by `createdAt` timestamp
- For the example case: locate entry with `createdAt: "2025-06-28T07:52:40.531Z"`
- Extract the actual Project ID from this entry: `ywgeXa9Ppz33JwxTjw0M`

### 3. Task Collection Query
- Query the `tasks` collection where `projectId` field equals `ywgeXa9Ppz33JwxTjw0M`
- This returns all actual task IDs that belong to the project

### 4. Correct Display Format
```
PMO Record ID: c76670a7-bc7b-44ea-9905-189a4bcf36c8
Firebase Project ID: ywgeXa9Ppz33JwxTjw0M
Task IDs: [actual Firebase task IDs from query results]
```

## Implementation Changes

### 1. Updated `_mapTaskIdsToProjectIds` Method

**File:** `lib/agents/pmoProjectsTaskAgent.ts`

**Key Changes:**
- Changed method signature to be async and accept `userId` parameter
- Now queries actual Firebase collections instead of generating synthetic IDs
- Uses `adminDb` for Firebase queries
- Returns actual Firebase document IDs

```typescript
private async _mapTaskIdsToProjectIds(content: string, pmoId?: string, userId?: string): Promise<string>
```

### 2. New `_getActualProjectIdFromPMO` Method

**File:** `lib/agents/pmoProjectsTaskAgent.ts`

**Purpose:** Query Firebase collections to get actual Project ID and Task IDs

**Process:**
1. Query PMO collection to get `generatedProjects` array
2. Sort projects by `createdAt` timestamp to find most recent
3. Extract the actual Project ID
4. Query tasks collection to get all task IDs for this project
5. Return both Project ID and Task IDs

### 3. Updated `_generateEnhancedNotes` Method

**File:** `lib/agents/pmoProjectsTaskAgent.ts`

**Changes:**
- Added `userId` parameter to method signature
- Extracts `userId` from agentOutput metadata if not provided
- Calls the updated `_mapTaskIdsToProjectIds` method with proper parameters
- Updates metadata footer to show "Firebase Project ID" instead of "PMO Project ID"

### 4. Updated API Routes

**Files:** 
- `app/api/project-creation-commit/route.ts`
- `app/api/project-creation-preview/route.ts`

**Changes:**
- Pass `effectiveUserId` to `_generateEnhancedNotes` method calls
- Ensure proper user context for Firebase queries

### 5. Enhanced TaskDetailsModal

**File:** `admin/planner/components/TaskDetailsModal.tsx`

**Changes:**
- Updated `getPMOMetadata` to include `actualProjectId` from `task.projectId`
- Enhanced logging to show actual Firebase Project ID
- Updated debugging output for the specific PMO ID case

## Verification Steps

### 1. Firebase Collection Structure
- Confirm PMO collection contains `generatedProjects` array with `createdAt` timestamps
- Verify Project ID `ywgeXa9Ppz33JwxTjw0M` exists in projects collection
- Validate that tasks collection contains records with matching `projectId` field

### 2. Data Flow Testing
- Test PMO collection query with PMO ID `c76670a7-bc7b-44ea-9905-189a4bcf36c8`
- Verify most recent project extraction by `createdAt` timestamp
- Confirm task collection query returns actual task IDs

### 3. TaskId Mapping Verification
- Verify that Agent_Output TaskIds are replaced with actual Firebase IDs
- Confirm task notes reference real Firebase document IDs
- Test with various TaskId patterns (QA_001, COMPLIANCE_002, etc.)

## Error Handling

### 1. Missing Data Scenarios
- PMO document not found
- No `generatedProjects` array
- Empty projects array
- No tasks found for project

### 2. Fallback Behavior
- Returns original content if mapping fails
- Logs warnings for missing data
- Uses default admin user if userId not available

## Testing

### 1. Unit Tests
- Test `_getActualProjectIdFromPMO` with mock Firebase data
- Test `_mapTaskIdsToProjectIds` with various content patterns
- Verify error handling for missing data scenarios

### 2. Integration Tests
- Test full data flow from PMO collection to task creation
- Verify actual Firebase queries work correctly
- Test with real PMO ID `c76670a7-bc7b-44ea-9905-189a4bcf36c8`

## Benefits

### 1. Data Consistency
- Task notes now reference actual Firebase document IDs
- Eliminates synthetic PMO format IDs that don't exist in Firebase
- Maintains referential integrity across collections

### 2. Improved Debugging
- Clear data flow from PMO → Project → Tasks
- Actual Firebase IDs make debugging easier
- Enhanced logging shows real document relationships

### 3. Future-Proof Architecture
- Uses actual Firebase document structure
- Supports multiple projects per PMO
- Scales with Firebase collection growth

## Migration Notes

### 1. Backward Compatibility
- Existing tasks with synthetic PMO IDs will continue to work
- New tasks will use actual Firebase IDs
- No data migration required

### 2. Monitoring
- Enhanced logging helps track the transition
- TaskDetailsModal shows both legacy and actual IDs during transition
- Console output clearly indicates data flow

## Conclusion

The implementation now correctly uses actual Firebase Project IDs and Task IDs instead of generating synthetic PMO format IDs. This ensures data consistency, improves debugging capabilities, and maintains proper referential integrity across Firebase collections.

The system follows the correct data flow:
**PMO Collection → generatedProjects[latest] → Firebase Project ID → Task Collection → Actual Task IDs**
