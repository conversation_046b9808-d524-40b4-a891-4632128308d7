/**
 * OpenAI integration for the LLM tool
 */

// Import the OpenAI SDK
import OpenAI from "openai";

// Define interfaces for OpenAI processing
export interface OpenAIProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: Record<string, any>;
}

/**
 * Process content with OpenAI
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use (default: "o3")
 * @param options.modelOptions - Additional model-specific options
 * @returns The generated content
 */
export async function processWithOpenAI(options: OpenAIProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "o3-2025-04-16",
      modelOptions = {}
    } = options;

    console.log(`Processing with OpenAI model: ${model}`);

    // Get the API key from environment variables
    const openaiApiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';

    if (!openaiApiKey) {
      throw new Error('OPENAI_API_KEY environment variable is not set');
    }

    // Initialize the OpenAI client
    const openai = new OpenAI({
      apiKey: openaiApiKey,
      dangerouslyAllowBrowser: true // Enable browser environment support
    });

    // Extract maxTokens and temperature from modelOptions to handle them separately
    const { maxTokens, temperature, ...otherModelOptions } = modelOptions;

    // Prepare parameters based on model
    const isO3Model = model.startsWith('o3');

    // Create the chat completion request with appropriate parameters based on model
    const chatCompletion = await openai.chat.completions.create({
      model: model,
      messages: [{ role: 'user', content: prompt }],
      // Only include temperature for non-o3 models or if it's explicitly set to 1 for o3 models
      ...(isO3Model ? (temperature === 1 ? { temperature: 1 } : {}) : { temperature: temperature || 0.7 }),
      // Use the appropriate token parameter based on model
      ...(isO3Model
        ? { max_completion_tokens: maxTokens || 7000 }
        : { max_tokens: maxTokens || 7000 }),
      // Include other options, but filter out any that might cause issues with o3 models
      ...(isO3Model ? {} : otherModelOptions)
    });

    // Return the text content from the completion
    return chatCompletion.choices[0]?.message?.content || "";
  } catch (error: any) {
    console.error("Error processing content with OpenAI:", error);
    return `Error from OpenAI: ${error.message || "Unknown error"}`;
  }
}

/**
 * Get available OpenAI models
 * @returns List of available models
 */
export function getOpenAIModels(): string[] {
  return [
    "o3-2025-04-16",
    "o3-pro-2025-06-10",
    "o3-mini-2025-01-31",
    "gpt-4o",
    "gpt-4-turbo",
    "gpt-4",
    "gpt-3.5-turbo"
  ];
}
