/**
 * Codebase Indexing Tool with Adaptive Context Analysis
 *
 * This tool intelligently analyzes codebases by using adaptive truncation strategies
 * to preserve important context while maintaining efficiency. Key features:
 *
 * - High-quality, file-level LLM analysis for accurate summaries and metadata.
 * - Raw code chunking for clean, searchable vector embeddings.
 * - Adaptive context limits based on file type (1200-2500 characters)
 * - Smart truncation at natural breakpoints (imports, comments, statements)
 * - Fallback to path-based analysis for robust classification
 * - Cost-optimized LLM usage with Gemini 2.5 Flash
 */

import { StorageTool } from './storage-tool';
import { vectorEmbeddingTool } from './vector-embeddings';
import * as fs from 'fs/promises';
import { Dirent } from 'fs';
import path from 'path';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { v4 as uuidv4 } from 'uuid';
import { processWithGoogleAI } from './google-ai';
import { processWithGroq } from './groq-ai';
import { codebaseIndexingReportGenerator } from './codebase-indexing-report-generator';
import { CodebaseFileAnalysis } from '../interfaces/CodebaseIndexingReport';

export interface CodebaseIndexingOptions {
  rootPath: string;
  userId: string;
  projectName: string;
  selectedPaths?: string[]; // Specific paths selected by user for targeted analysis
  excludePatterns?: string[];
  includeExtensions?: string[];
  chunkSize?: number;
  chunkOverlap?: number;
  verbose?: boolean; // Added for optional detailed logging
  maxFiles?: number; // Maximum number of files to process (for legacy method)
}

export interface CodebaseIndexingResult {
  success: boolean;
  totalFiles: number;
  totalChunks: number;
  documentId: string;
  chunkIds?: string[];
  error?: string;
}

// Define enriched metadata interface for code chunks
export interface EnrichedCodeChunk {
  content: string; // This is now PURE code, no headers.
  metadata: {
    title: string;       // fileName
    filePath: string;
    language: string;
    chunkIndex: number;
    projectName: string;
    type: 'code_chunk';
    indexedAt: string;

    // --- STANDARDIZED CHUNK IDENTIFICATION ---
    doc_id: string;      // Document ID for the codebase session
    chunk_id: string;    // Standardized format: ${docId}_${index + 1}

    // --- ENRICHED METADATA (now based on full-file analysis) ---
    codeSummary: string;
    imports: string[];
    exports: string[];
    codeEntityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown';
    definedEntities: string[];
    apiEndpoints: string[];
    fileId: string; // UUID for the file (used as namespace)
  }
}

export class CodebaseIndexingTool {
  private storageTool: StorageTool;
  private applicationContext: string | null = null; // Cache for application context
  private costTrackingData: { originalSize: number, truncatedSize: number }[] = []; // Track adaptive truncation savings
  private defaultExcludePatterns = [
    'node_modules',
    '.git',
    '.next',
    'dist',
    'build',
    '.vscode',
    'coverage',
    '.nuxt',
    '.output',
    '__pycache__',
    '.env',
    '.env.local',
    '.env.production',
    '.env.development',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db',
    '.cache',
    'tmp',
    'temp'
  ];

  private defaultIncludeExtensions = [
    '.ts', '.tsx', '.js', '.jsx',
    '.py', '.java', '.cpp', '.c',
    '.cs', '.go', '.rs', '.php',
    '.rb', '.swift', '.kt', '.scala',
    '.md', '.txt', '.json', '.yaml', '.yml'
  ];

  constructor() {
    this.storageTool = new StorageTool();
  }

  /**
   * Index codebase directly to vector embeddings with enriched metadata
   * This is the new enhanced version for Path A: Codebase Onboarding & Enrichment
   */
  async indexCodebaseDirect(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    const processingStartTime = Date.now();
    const verbose = options.verbose ?? false; // Use verbose flag

    try {
      this.costTrackingData = [];
      console.log(`🚀 Starting enriched codebase indexing for ${options.projectName}`);

      console.log(`🧠 Analyzing application context...`);
      await this.analyzeApplicationContext(options.rootPath);

      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions,
        verbose,
        options.selectedPaths
      );

      console.log(`📁 Found ${files.length} code files to index for enrichment`);

      if (files.length === 0) {
        return {
          success: false,
          totalFiles: 0,
          totalChunks: 0,
          documentId: '',
          error: 'No files found to index'
        };
      }

      if (!vectorEmbeddingTool.isInitialized()) {
        await vectorEmbeddingTool.initialize();
      }

      const allChunks: EnrichedCodeChunk[] = [];
      const fileAnalysisData: CodebaseFileAnalysis[] = [];

      for (let i = 0; i < files.length; i++) {
        const filePath = files[i];
        const fileProcessingStart = Date.now();
        const relativePath = path.relative(options.rootPath, filePath);

        try {
          const fileId = uuidv4();
          const progressPercent = ((i + 1) / files.length * 100).toFixed(1);
          console.log(`🧠 Processing file ${i + 1}/${files.length} (${progressPercent}%): ${relativePath}`);

          const fileStats = await fs.stat(filePath);
          const fileName = path.basename(filePath);

          const enrichedChunks = await this.processAndEnrichFile(filePath, options, fileId);
          allChunks.push(...enrichedChunks);

          if (enrichedChunks.length > 0) {
            const firstChunk = enrichedChunks[0];
            const fileAnalysis: CodebaseFileAnalysis = {
              filePath: relativePath,
              fileName,
              language: firstChunk.metadata.language,
              fileSize: fileStats.size,
              chunkCount: enrichedChunks.length,
              llmSummary: firstChunk.metadata.codeSummary,
              codeEntityType: firstChunk.metadata.codeEntityType,
              definedEntities: firstChunk.metadata.definedEntities,
              imports: firstChunk.metadata.imports,
              exports: firstChunk.metadata.exports,
              apiEndpoints: firstChunk.metadata.apiEndpoints,
              processingTimeMs: Date.now() - fileProcessingStart,
              success: true,
              chunks: enrichedChunks.map((chunk, index) => ({
                chunkId: `${fileId}_${index + 1}`,
                chunkIndex: index,
                contentPreview: chunk.content.substring(0, 200),
                summary: chunk.metadata.codeSummary
              }))
            };
            fileAnalysisData.push(fileAnalysis);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.warn(`⚠️ Failed to process file ${relativePath}: ${errorMessage}`);
          const fileName = path.basename(filePath);
          let fileSize = 0;
          try {
            const stats = await fs.stat(filePath);
            fileSize = stats.size;
          } catch (statError) {}

          const fileAnalysis: CodebaseFileAnalysis = {
            filePath: relativePath,
            fileName,
            language: 'Unknown',
            fileSize,
            chunkCount: 0,
            llmSummary: 'Failed to process',
            codeEntityType: 'Unknown',
            definedEntities: [], imports: [], exports: [], apiEndpoints: [],
            processingTimeMs: Date.now() - fileProcessingStart,
            success: false, errorMessage, chunks: []
          };
          fileAnalysisData.push(fileAnalysis);
        }
      }

      console.log(`🧠 Generated a total of ${allChunks.length} enriched chunks. Starting vector embedding.`);

      const chunkIds: string[] = [];
      const batchSize = 20; 
      const docId = uuidv4();
      let globalChunkIndex = 0;

      for (let i = 0; i < allChunks.length; i += batchSize) {
        const batch = allChunks.slice(i, i + batchSize);
        console.log(`📦 Uploading batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(allChunks.length/batchSize)} to vector store.`);

        const upsertPromises = batch.map(async (chunk, batchIndex) => {
          const chunkId = `${docId}_${globalChunkIndex + batchIndex + 1}`;
          const updatedMetadata = { ...chunk.metadata, doc_id: docId, chunk_id: chunkId };
          const sanitizedMetadata = this.sanitizeMetadataForPinecone(updatedMetadata);
          await vectorEmbeddingTool.createEmbedding(
            chunk.content,
            chunkId,
            sanitizedMetadata,
            chunk.metadata.fileId 
          );
          return chunkId;
        });

        const createdIds = await Promise.all(upsertPromises);
        chunkIds.push(...createdIds);
        globalChunkIndex += batch.length;
      }

      if (this.costTrackingData.length > 0) {
        this.logCostOptimization(this.costTrackingData);
        this.costTrackingData = [];
      }

      console.log(`✅ Successfully indexed and enriched codebase directly!`);
      console.log(`📊 Total files: ${files.length}`);
      console.log(`📄 Total chunks: ${allChunks.length}`);

      try {
        console.log(`📋 Generating codebase indexing completion report...`);
        const indexingResult: CodebaseIndexingResult = {
          success: true, totalFiles: files.length, totalChunks: allChunks.length, documentId: docId, chunkIds: chunkIds
        };
        const report = await codebaseIndexingReportGenerator.generateReport(
          indexingResult, options.projectName, options.userId, fileAnalysisData, processingStartTime
        );
        const reportPdfUrl = await codebaseIndexingReportGenerator.saveReport(report);
        console.log(`📄 Indexing completion report saved: ${reportPdfUrl}`);
        return indexingResult;
      } catch (reportError) {
        console.warn(`⚠️ Failed to generate completion report:`, reportError);
        return {
          success: true, totalFiles: files.length, totalChunks: allChunks.length, documentId: docId, chunkIds: chunkIds
        };
      }
    } catch (error) {
      console.error('❌ Error in direct codebase indexing:', error);
      return {
        success: false, totalFiles: 0, totalChunks: 0, documentId: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * **PHASE 1: Application Context Analysis**
   */
  private async analyzeApplicationContext(rootPath: string): Promise<void> {
    if (this.applicationContext) {
      console.log(`✅ Using cached application context`);
      return;
    }

    try {
      const entryPoints = [
        path.join(rootPath, 'app', 'page.tsx'), path.join(rootPath, 'app', 'page.jsx'),
        path.join(rootPath, 'src', 'app', 'page.tsx'), path.join(rootPath, 'src', 'app', 'page.jsx'),
        path.join(rootPath, 'pages', 'index.tsx'), path.join(rootPath, 'pages', 'index.jsx'),
        path.join(rootPath, 'src', 'pages', 'index.tsx'), path.join(rootPath, 'src', 'pages', 'index.jsx'),
        path.join(rootPath, 'src', 'App.tsx'), path.join(rootPath, 'src', 'App.jsx'),
        path.join(rootPath, 'App.tsx'), path.join(rootPath, 'App.jsx')
      ];

      let entryPointContent = '';
      let entryPointPath = '';

      for (const entryPoint of entryPoints) {
        try {
          entryPointContent = await fs.readFile(entryPoint, 'utf-8');
          entryPointPath = path.relative(rootPath, entryPoint);
          console.log(`📍 Found application entry point: ${entryPointPath}`);
          break;
        } catch (error) { /* continue */ }
      }

      if (!entryPointContent) {
        console.log(`⚠️  No main entry point found, using generic application context`);
        this.applicationContext = "Generic web application - specific context unavailable";
        return;
      }

      const prompt = `
You are analyzing the main entry point of a software application to understand its overall purpose and architecture.
**File**: ${entryPointPath}
**Task**: Provide a comprehensive analysis of this application's purpose, architecture, and domain.
**Analysis Requirements:**
1. **Application Purpose**: What is the main goal/function of this application?
2. **Architecture Pattern**: What framework/architecture is being used? (Next.js, React, etc.)
3. **Domain/Industry**: What business domain does this application serve?
4. **Key Features**: What are the main features/capabilities based on imports and components?
5. **User Interface**: What type of UI/UX patterns are evident?
6. **Data Flow**: How does data appear to flow through the application?
**Instructions:**
- Focus on imports, component structure, routing, and any comments
- Provide a 3-4 sentence summary that captures the essence of the application
Respond with ONLY a JSON object:
{
    "applicationPurpose": "Brief description of what this application does",
    "architecture": "Framework and architectural patterns used",
    "domain": "Business domain/industry this serves",
    "keyFeatures": ["feature1", "feature2", "feature3"],
    "summary": "3-4 sentence comprehensive summary of the application's purpose and architecture"
}
**APPLICATION ENTRY POINT CODE:**
\`\`\`typescript
${entryPointContent}
\`\`\`
      `;

      console.log(`🧠 Analyzing application context with Google Gemini 2.5 Pro...`);
      const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-pro" });
      const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
      const analysis = JSON.parse(jsonString);

      this.applicationContext = `
Application Purpose: ${analysis.applicationPurpose || 'Unknown'}
Architecture: ${analysis.architecture || 'Unknown'}
Domain: ${analysis.domain || 'Unknown'}
Key Features: ${Array.isArray(analysis.keyFeatures) ? analysis.keyFeatures.join(', ') : 'Unknown'}
Summary: ${analysis.summary || 'No summary available'}
      `.trim();

      console.log(`✅ Application context analysis complete`);
      console.log(`📋 Context: ${this.applicationContext.split('\n')[0]}...`);
    } catch (error) {
      console.warn(`⚠️  Application context analysis failed:`, error);
      this.applicationContext = "Application context analysis failed - using generic context";
    }
  }

  async indexCodebase(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    return this.indexCodebaseDirect(options);
  }

  async indexCodebaseLegacy(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    const verbose = options.verbose ?? false;
    try {
      console.log(`Starting codebase indexing for ${options.projectName}`);
      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;
      
      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions,
        verbose
        // No selectedPaths - this method scans the entire rootPath
      );
      
      console.log(`Found ${files.length} code files to index`);

      // Allow configuration of max files, but default to processing all files
      const maxFiles = options.maxFiles || files.length;
      const filesToProcess = files.length > maxFiles ? files.slice(0, maxFiles) : files;
      if (files.length > maxFiles) {
        console.log(`⚠️ Large codebase detected (${files.length} files). Processing first ${maxFiles} files to prevent timeout.`);
        console.log(`💡 To process all files, increase the maxFiles option or use the direct indexing method.`);
      }

      const batchSize = 10;
      let totalChunks = 0;
      const allContent: string[] = [];
      for (let i = 0; i < filesToProcess.length; i += batchSize) {
        const batch = filesToProcess.slice(i, i + batchSize);
        const batchContent = await this.processBatch(batch, options.rootPath);
        allContent.push(...batchContent);
        totalChunks += batchContent.length;
        console.log(`Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(filesToProcess.length/batchSize)}`);
      }
      
      const combinedContent = allContent.join('\n\n---FILE_SEPARATOR---\n\n');
      console.log('Saving to RAG system...');
      const savePromise = this.storageTool.savePdfToByteStore(Buffer.from(combinedContent, 'utf-8'), `${options.projectName}_codebase`, combinedContent, 'Codebase Documentation', { projectName: options.projectName, indexedAt: new Date().toISOString(), totalFiles: files.length, rootPath: options.rootPath, type: 'codebase_index' });
      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Indexing timeout after 10 minutes')), 10 * 60 * 1000));
      const result = await Promise.race([savePromise, timeoutPromise]) as any;

      console.log(`✅ Successfully indexed codebase!`);
      return { success: true, totalFiles: files.length, totalChunks: result.totalChunks, documentId: result.documentId };
    } catch (error) {
      console.error('Error indexing codebase:', error);
      return { success: false, totalFiles: 0, totalChunks: 0, documentId: '', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Diagnostic method to analyze what files would be indexed without actually processing them.
   * Useful for troubleshooting indexing issues and understanding file discovery behavior.
   */
  async diagnoseIndexing(options: CodebaseIndexingOptions): Promise<{
    totalFilesFound: number;
    indexableFiles: string[];
    skippedDirectories: string[];
    errorPaths: string[];
    filesByExtension: Record<string, number>;
    summary: string;
  }> {
    const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
    const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

    console.log(`🔍 Running indexing diagnostic for: ${options.rootPath}`);
    console.log(`📋 Exclude patterns: ${excludePatterns.join(', ')}`);
    console.log(`📋 Include extensions: ${includeExtensions.join(', ')}`);

    const files = await this.findCodeFiles(options.rootPath, excludePatterns, includeExtensions, true);
    // Diagnostic method scans entire rootPath, no selectedPaths needed

    // Analyze file extensions
    const filesByExtension: Record<string, number> = {};
    for (const file of files) {
      const ext = path.extname(file).toLowerCase();
      filesByExtension[ext] = (filesByExtension[ext] || 0) + 1;
    }

    const summary = `Found ${files.length} indexable files across ${Object.keys(filesByExtension).length} different extensions.`;

    return {
      totalFilesFound: files.length,
      indexableFiles: files.map(f => path.relative(options.rootPath, f)),
      skippedDirectories: [], // This would be populated by findCodeFiles in verbose mode
      errorPaths: [], // This would be populated by findCodeFiles in verbose mode
      filesByExtension,
      summary
    };
  }

  /**
   * Find all code files, now supporting targeted scanning via `selectedPaths`.
   */
  private async findCodeFiles(
    rootPath: string,
    excludePatterns: string[],
    includeExtensions: string[],
    verbose: boolean = false,
    selectedPaths?: string[]
  ): Promise<string[]> {
    const includeExtSet = new Set(includeExtensions.map(ext => ext.toLowerCase()));
    const files: Set<string> = new Set(); // Use a Set to prevent duplicates
    const skippedDirectories: string[] = [];
    const errorPaths: string[] = [];
    const normalizedRootPath = path.resolve(rootPath);

    const traverse = async (currentPath: string, currentRelativePath: string): Promise<void> => {
      let items: Dirent[];
      try {
        items = await fs.readdir(currentPath, { withFileTypes: true });
      } catch (error) {
        const errorMsg = `Cannot read directory: ${currentPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        errorPaths.push(errorMsg);
        if (verbose) console.warn(`⚠️  ${errorMsg}`);
        return;
      }

      const directoryPromises: Promise<void>[] = [];
      for (const item of items) {
        const itemName = item.name;
        const fullPath = path.join(currentPath, itemName);
        const relativePath = path.join(currentRelativePath, itemName);
        if (item.isDirectory()) {
          const shouldExclude = excludePatterns.some(pattern => {
            const normalizedPattern = path.normalize(pattern).toLowerCase();
            const normalizedRelativePath = path.normalize(relativePath).toLowerCase();
            if (normalizedRelativePath === normalizedPattern) return true;
            if (normalizedRelativePath.startsWith(normalizedPattern + path.sep)) return true;
            const pathSegments = normalizedRelativePath.split(path.sep);
            return pathSegments.includes(normalizedPattern);
          });
          if (shouldExclude) {
            skippedDirectories.push(relativePath);
            if (verbose) console.log(`⏭️  Skipping excluded directory: ${relativePath}`);
            continue;
          }
          directoryPromises.push(traverse(fullPath, relativePath));
        } else if (item.isFile()) {
          const ext = path.extname(itemName).toLowerCase();
          if (includeExtSet.has(ext)) {
            if (verbose) console.log(`📄 Found code file: ${relativePath}`);
            files.add(fullPath);
          }
        }
      }
      await Promise.all(directoryPromises);
    };

    const pathsToScan = (selectedPaths && selectedPaths.length > 0) ? selectedPaths : [normalizedRootPath];

    if (verbose) {
      console.log(`🚀 Starting file discovery. Scanning ${pathsToScan.length} target location(s).`);
      console.log(selectedPaths && selectedPaths.length > 0 ? `🎯 Targeted paths:` : `📂 Full directory scan:`, pathsToScan);
    }

    const discoveryPromises = pathsToScan.map(async (startPath) => {
      const normalizedStartPath = path.resolve(startPath);
      try {
        const stats = await fs.stat(normalizedStartPath);
        if (stats.isDirectory()) {
          const relativeToRoot = path.relative(normalizedRootPath, normalizedStartPath);
          await traverse(normalizedStartPath, relativeToRoot);
        } else if (stats.isFile()) {
          const ext = path.extname(normalizedStartPath).toLowerCase();
          if (includeExtSet.has(ext)) {
            files.add(normalizedStartPath);
          }
        }
      } catch (error) {
        const errorMsg = `Cannot access target path: ${normalizedStartPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        errorPaths.push(errorMsg);
        if (verbose) console.warn(`⚠️  ${errorMsg}`);
      }
    });
    await Promise.all(discoveryPromises);
    
    const finalFiles = Array.from(files);
    console.log(`✅ File discovery complete. Found ${finalFiles.length} unique code files.`);
    return finalFiles;
  }
  
  private async processBatch(filePaths: string[], rootPath: string): Promise<string[]> {
    const contents: string[] = [];
    for (const filePath of filePaths) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const relativePath = path.relative(rootPath, filePath);
        const fileContent = `\n=== FILE: ${relativePath} ===\nLanguage: ${this.getLanguageFromExtension(path.extname(filePath))}\nPath: ${relativePath}\nSize: ${content.length} characters\n\n${content}\n\n=== END FILE: ${relativePath} ===\n`;
        contents.push(fileContent);
      } catch (error) {
        console.warn(`Could not read file: ${filePath}`);
      }
    }
    return contents;
  }

  private getLanguageFromExtension(ext: string): string {
    const languageMap: Record<string, string> = {
      '.ts': 'TypeScript', '.tsx': 'TypeScript React', '.js': 'JavaScript', '.jsx': 'JavaScript React',
      '.py': 'Python', '.java': 'Java', '.cpp': 'C++', '.c': 'C', '.cs': 'C#', '.go': 'Go', '.rs': 'Rust',
      '.php': 'PHP', '.rb': 'Ruby', '.swift': 'Swift', '.kt': 'Kotlin', '.scala': 'Scala', '.md': 'Markdown',
      '.txt': 'Text', '.json': 'JSON', '.yaml': 'YAML', '.yml': 'YAML'
    };
    return languageMap[ext] || 'Unknown';
  }

  /**
   * Calculates estimated cost savings from adaptive truncation
   */
  private calculateCostSavings(originalLength: number, truncatedLength: number): { savedTokens: number, savedCost: number } {
    const CHARS_PER_TOKEN = 4;
    const COST_PER_MILLION_TOKENS = 0.30; // Gemini 2.5 Flash input cost

    const originalTokens = Math.ceil(originalLength / CHARS_PER_TOKEN);
    const truncatedTokens = Math.ceil(truncatedLength / CHARS_PER_TOKEN);
    const savedTokens = originalTokens - truncatedTokens;
    const savedCost = (savedTokens / 1_000_000) * COST_PER_MILLION_TOKENS;

    return { savedTokens, savedCost };
  }

  /**
   * Logs cost optimization statistics for the indexing process
   */
  private logCostOptimization(files: { originalSize: number, truncatedSize: number }[]): void {
    const totalOriginal = files.reduce((sum, f) => sum + f.originalSize, 0);
    const totalTruncated = files.reduce((sum, f) => sum + f.truncatedSize, 0);
    const { savedTokens, savedCost } = this.calculateCostSavings(totalOriginal, totalTruncated);

    const reductionPercent = totalOriginal > 0 ? ((totalOriginal - totalTruncated) / totalOriginal * 100).toFixed(1) : "0.0";

    console.log(`\n💰 Adaptive Truncation Cost Optimization:`);
    console.log(`   📊 Content reduced by ${reductionPercent}% (${totalOriginal.toLocaleString()} → ${totalTruncated.toLocaleString()} chars)`);
    console.log(`   🪙 Tokens saved: ${savedTokens.toLocaleString()}`);
    console.log(`   💵 Estimated cost savings: $${savedCost.toFixed(4)}`);
    console.log(`   📈 Cost per file: ~$${files.length > 0 ? (savedCost / files.length).toFixed(6) : '0.000000'}`);
  }

  /**
   * NEW: Performs a single, high-quality analysis on an entire file.
   * This provides an authoritative summary for reports and consistent metadata for all chunks of the file.
   */
  private async analyzeFileWithLLM(filePath: string, language: string, content: string): Promise<{ summary: string; entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown'; definedEntities: string[] }> {
    const prompt = `
You are an expert software architect analyzing a source code file. Provide a comprehensive, high-level analysis.
**Application Context:**
${this.applicationContext || 'Application context not available'}
**File Information:**
- File Path: ${filePath}
- Language: ${language}
**Analysis Requirements:**
1.  **Summary**: Provide a detailed summary (3-4 sentences) of the file's primary role, its main functionalities, and how it fits into the overall application architecture. Be accurate and base your summary ONLY on the provided code. Do not guess or infer functionality not present.
2.  **Entity Type**: Classify the file's primary purpose from: Component, Function, Class, Hook, Configuration, Util, Unknown.
3.  **Defined Entities**: List the most important functions, components, classes, interfaces, or constants defined in this file.
Respond with ONLY a valid JSON object:
{
    "summary": "Detailed and accurate summary of the entire file...",
    "entityType": "Component",
    "definedEntities": ["EntityName1", "importantFunction"]
}
**FULL FILE CONTENT:**
\`\`\`${language}
${content}
\`\`\`
    `;

    try {
      const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-flash" });
      const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
      const parsed = JSON.parse(jsonString);
      return this.validateAndSanitizeAnalysisResponse(parsed);
    } catch (error) {
      console.warn(`CodebaseIndexingTool: Full file analysis failed for ${filePath}:`, error);
      return this.getDefaultAnalysisResponse();
    }
  }
  
  /**
   * REFACTORED: Processes and enriches a file using the new file-level analysis workflow.
   */
  private async processAndEnrichFile(filePath: string, options: CodebaseIndexingOptions, fileId: string): Promise<EnrichedCodeChunk[]> {
    let content: string;
    try {
      content = await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      console.warn(`⚠️  Failed to read file: ${path.relative(options.rootPath, filePath)} - ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }

    const relativePath = path.relative(options.rootPath, filePath);
    const fileName = path.basename(filePath);
    const language = this.getLanguageFromExtension(path.extname(filePath));

    const trimmedContent = content.trim();
    if (!trimmedContent || trimmedContent.length < 20) {
      if (options.verbose) {
        console.log(`⏭️  Skipping file with insufficient content: ${relativePath} (${trimmedContent.length} chars)`);
      }
      return [];
    }

    // 1. Perform a single, high-level analysis on the ENTIRE file content.
    console.log(`🔬 Performing full-file analysis for: ${fileName}`);
    const fileLevelAnalysis = await this.analyzeFileWithLLM(relativePath, language, content);
    
    // 2. Extract other metadata from the full file content.
    const imports = this.extractImports(content);
    const exports = this.extractExports(content);
    const apiEndpoints = this.extractApiEndpoints(content);

    // 3. Split ONLY the raw code content for vectorization. No header is prepended.
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: options.chunkSize || 2500,
      chunkOverlap: options.chunkOverlap || 300,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });
    const codeChunks = await textSplitter.splitText(content);
    
    // Filter out tiny, meaningless chunks.
    const meaningfulCodeChunks = codeChunks.filter(chunk => chunk.trim().length > 50);
    
    if (meaningfulCodeChunks.length === 0) {
        console.warn(`⚠️ No meaningful code chunks generated for ${relativePath}, likely a very small file. Skipping.`);
        return [];
    }

    // 4. Create enriched chunks. The metadata is now separate from the content and consistent for all chunks from this file.
    const enrichedChunks: EnrichedCodeChunk[] = meaningfulCodeChunks.map((chunkContent, index) => {
      return {
        content: chunkContent, // The content is PURE code.
        metadata: {
          title: fileName,
          filePath: relativePath,
          language: language,
          chunkIndex: index,
          projectName: options.projectName,
          type: 'code_chunk' as const,
          indexedAt: new Date().toISOString(),
          doc_id: '',
          chunk_id: '',
          fileId: fileId,
          // Use the authoritative file-level analysis for all chunks.
          codeSummary: fileLevelAnalysis.summary,
          codeEntityType: fileLevelAnalysis.entityType,
          definedEntities: fileLevelAnalysis.definedEntities,
          imports: imports,
          exports: exports,
          apiEndpoints: apiEndpoints,
        }
      };
    });

    return enrichedChunks;
  }

  private validateAndSanitizeAnalysisResponse(parsed: any): { summary: string; entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown'; definedEntities: string[] } {
    const validEntityTypes = ['Component', 'Function', 'Class', 'Hook', 'Configuration', 'Util', 'Unknown'];
    return {
      summary: (typeof parsed?.summary === 'string' && parsed.summary.trim()) ? parsed.summary.trim() : "Code analysis summary not available.",
      entityType: (typeof parsed?.entityType === 'string' && validEntityTypes.includes(parsed.entityType)) ? parsed.entityType as any : 'Unknown',
      definedEntities: Array.isArray(parsed?.definedEntities) ? parsed.definedEntities.filter((e: any) => typeof e === 'string' && e.trim()).map((e: string) => e.trim()) : []
    };
  }
  
  private getDefaultAnalysisResponse(): { summary: string; entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown'; definedEntities: string[] } {
    return {
      summary: "No summary available due to LLM analysis failures.",
      entityType: "Unknown",
      definedEntities: []
    };
  }

  private sanitizeMetadataForPinecone(metadata: any): any {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(metadata)) {
      if (value === null || value === undefined) {
        if (key === 'codeEntityType') sanitized[key] = 'Unknown';
        else if (Array.isArray(value) || key.includes('Entities') || key.includes('imports') || key.includes('exports') || key.includes('apiEndpoints')) sanitized[key] = [];
        else sanitized[key] = '';
      } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (Array.isArray(value)) {
        sanitized[key] = value.filter(item => typeof item === 'string' && item.trim()).map(item => String(item).trim());
      } else if (typeof value === 'object') {
        sanitized[key] = JSON.stringify(value);
      } else {
        sanitized[key] = String(value);
      }
    }
    return sanitized;
  }

  private extractImports(content: string): string[] {
    const importRegex = /import(?:(?:(?:[ \n\t]+([^ \n\t\n]+)[ \n\t]*,?)?(?:[ \n\t]*\{(?:[ \n\t]*[^ \n\t\{\}]+[ \n\t]*,?)+\})?[ \n\t]*)from[ \n\t]*(?:['"])([^'"\n]+)(?:['"]))/g;
    return [...content.matchAll(importRegex)].map(match => match[2]);
  }

  private extractExports(content: string): string[] {
    const exportRegex = /export (?:const|let|var|function|class|default) (\w+)/g;
    return [...content.matchAll(exportRegex)].map(match => match[1]);
  }

  private extractApiEndpoints(content: string): string[] {
    const apiRegex = /(?:fetch|axios\.(?:get|post|put|delete|patch))\s*\(\s*['"`]([^'"`]+)['"`]/g;
    return [...content.matchAll(apiRegex)].map(match => match[1]);
  }
}

export const codebaseIndexingTool = new CodebaseIndexingTool();