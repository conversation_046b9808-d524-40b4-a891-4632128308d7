#!/usr/bin/env node

/**
 * Verify PMO State Script
 * 
 * This script verifies the current state of a PMO document and its subcollections
 * to help debug why taskIds array might not be updating.
 * 
 * Usage:
 *   node scripts/verify-pmo-state.js [userId] [pmoId]
 */

const { adminDb } = require('../components/firebase-admin');
const { getHierarchicalPMOData } = require('../lib/firebase/pmoHierarchical');

// Configuration
const DEFAULT_USER_ID = '<EMAIL>';

/**
 * Verify PMO document state and subcollections
 */
async function verifyPMOState(userId, pmoId) {
  console.log('\n🔍 VERIFYING PMO STATE');
  console.log('======================');
  console.log(`User ID: ${userId}`);
  console.log(`PMO ID: ${pmoId}`);
  console.log('');

  try {
    // 1. Check PMO document
    console.log('1️⃣ Checking PMO document...');
    const pmoDocRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('PMO')
      .doc(pmoId);

    const pmoDoc = await pmoDocRef.get();
    
    if (!pmoDoc.exists) {
      console.log('❌ PMO document does not exist');
      return;
    }

    const pmoData = pmoDoc.data();
    console.log('✅ PMO document found');
    console.log(`   Document ID: ${pmoDoc.id}`);
    console.log(`   Created: ${pmoData?.createdAt?.toDate?.() || pmoData?.createdAt || 'Unknown'}`);
    console.log(`   Updated: ${pmoData?.updatedAt?.toDate?.() || pmoData?.updatedAt || 'Unknown'}`);
    
    // Check legacy arrays
    const projectIds = pmoData?.projectIds || [];
    const taskIds = pmoData?.taskIds || [];
    
    console.log(`   Legacy projectIds: [${projectIds.join(', ')}] (${projectIds.length} items)`);
    console.log(`   Legacy taskIds: [${taskIds.join(', ')}] (${taskIds.length} items)`);

    // 2. Check projects subcollection
    console.log('\n2️⃣ Checking projects subcollection...');
    const projectsCollection = pmoDocRef.collection('projects');
    const projectsSnapshot = await projectsCollection.get();
    
    console.log(`✅ Found ${projectsSnapshot.size} projects in subcollection`);
    
    if (projectsSnapshot.size > 0) {
      for (const projectDoc of projectsSnapshot.docs) {
        const projectId = projectDoc.id;
        const projectData = projectDoc.data();
        
        console.log(`   📁 Project: ${projectId}`);
        console.log(`      Name: ${projectData?.name || 'Unknown'}`);
        console.log(`      Status: ${projectData?.status || 'Unknown'}`);
        console.log(`      Created: ${projectData?.createdAt?.toDate?.() || projectData?.createdAt || 'Unknown'}`);
        
        // Check if project ID is in legacy array
        const inLegacyArray = projectIds.includes(projectId);
        console.log(`      In legacy projectIds array: ${inLegacyArray ? '✅' : '❌'}`);
        
        // Check tasks subcollection for this project
        const tasksCollection = projectDoc.ref.collection('tasks');
        const tasksSnapshot = await tasksCollection.get();
        
        console.log(`      Tasks in subcollection: ${tasksSnapshot.size}`);
        
        if (tasksSnapshot.size > 0) {
          const projectTaskIds = tasksSnapshot.docs.map(doc => doc.id);
          console.log(`      Task IDs: [${projectTaskIds.join(', ')}]`);
          
          // Check if these task IDs are in the legacy array
          const tasksInLegacyArray = projectTaskIds.filter(taskId => taskIds.includes(taskId));
          const tasksNotInLegacyArray = projectTaskIds.filter(taskId => !taskIds.includes(taskId));
          
          if (tasksInLegacyArray.length > 0) {
            console.log(`      ✅ Tasks in legacy array: [${tasksInLegacyArray.join(', ')}]`);
          }
          if (tasksNotInLegacyArray.length > 0) {
            console.log(`      ❌ Tasks NOT in legacy array: [${tasksNotInLegacyArray.join(', ')}]`);
          }
        }
        console.log('');
      }
    }

    // 3. Check hierarchical data retrieval
    console.log('3️⃣ Testing hierarchical data retrieval...');
    const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);
    
    if (hierarchicalResult.success && hierarchicalResult.data) {
      console.log(`✅ Hierarchical data retrieved successfully`);
      console.log(`   Projects found: ${hierarchicalResult.data.length}`);
      
      for (const project of hierarchicalResult.data) {
        console.log(`   📁 ${project.projectId}: ${project.taskIds.length} tasks`);
        if (project.taskIds.length > 0) {
          console.log(`      Task IDs: [${project.taskIds.slice(0, 5).join(', ')}${project.taskIds.length > 5 ? '...' : ''}]`);
        }
      }
    } else {
      console.log(`❌ Failed to get hierarchical data: ${hierarchicalResult.error}`);
    }

    // 4. Check for orphaned tasks in legacy array
    console.log('\n4️⃣ Checking for orphaned tasks...');
    if (taskIds.length > 0) {
      console.log(`Checking ${taskIds.length} task IDs from legacy array...`);
      
      for (const taskId of taskIds) {
        // Check if task exists in global collection
        const globalTaskDoc = await adminDb.collection('tasks').doc(taskId).get();
        const existsInGlobal = globalTaskDoc.exists;
        
        // Check if task exists in any subcollection
        let foundInSubcollection = false;
        for (const projectDoc of projectsSnapshot.docs) {
          const taskDoc = await projectDoc.ref.collection('tasks').doc(taskId).get();
          if (taskDoc.exists) {
            foundInSubcollection = true;
            break;
          }
        }
        
        console.log(`   Task ${taskId}:`);
        console.log(`      In global collection: ${existsInGlobal ? '✅' : '❌'}`);
        console.log(`      In subcollections: ${foundInSubcollection ? '✅' : '❌'}`);
        
        if (!existsInGlobal && !foundInSubcollection) {
          console.log(`      ⚠️ ORPHANED: Task exists in legacy array but not in any collection`);
        }
      }
    }

    // 5. Summary
    console.log('\n📊 SUMMARY');
    console.log('===========');
    console.log(`PMO Document: ✅ Exists`);
    console.log(`Legacy projectIds: ${projectIds.length} items`);
    console.log(`Legacy taskIds: ${taskIds.length} items`);
    console.log(`Projects subcollection: ${projectsSnapshot.size} items`);
    
    let totalSubcollectionTasks = 0;
    for (const projectDoc of projectsSnapshot.docs) {
      const tasksSnapshot = await projectDoc.ref.collection('tasks').get();
      totalSubcollectionTasks += tasksSnapshot.size;
    }
    console.log(`Total tasks in subcollections: ${totalSubcollectionTasks} items`);
    
    if (taskIds.length !== totalSubcollectionTasks) {
      console.log(`⚠️ MISMATCH: Legacy array has ${taskIds.length} tasks, subcollections have ${totalSubcollectionTasks} tasks`);
    } else {
      console.log(`✅ CONSISTENT: Legacy array and subcollections have same number of tasks`);
    }

  } catch (error) {
    console.error('❌ Error during PMO state verification:', error);
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log('Usage:');
    console.log('  node scripts/verify-pmo-state.js [userId] [pmoId]');
    console.log('');
    console.log('Examples:');
    console.log('  node scripts/verify-pmo-state.js <EMAIL> c76670a7-bc7b-44ea-9905-189a4bcf36c8');
    process.exit(1);
  }

  const userId = args[0] || DEFAULT_USER_ID;
  const pmoId = args[1];
  
  if (!pmoId) {
    console.error('❌ PMO ID is required');
    process.exit(1);
  }
  
  await verifyPMOState(userId, pmoId);
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  verifyPMOState
};
