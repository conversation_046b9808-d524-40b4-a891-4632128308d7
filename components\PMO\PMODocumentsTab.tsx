'use client';

import React, { useState, useEffect } from 'react';
import { collection, query, orderBy, onSnapshot, where, getDocs, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../components/firebase';
import { FileIcon, Download, ExternalLink, MessageSquare, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../components/ui/alert-dialog";
import { Button } from '../../components/ui/button';
import { Skeleton } from '../../components/ui/skeleton';
import { toast } from '../../components/ui/use-toast';

interface PMODocument {
  id: string;
  name?: string;
  title?: string;
  type?: string;
  size?: number;
  downloadUrl?: string;
  createdAt?: any; // Firebase timestamp
  generatedBy?: string;
  agentName?: string;
  category?: string;
  namespace?: string;
}

export default function PMODocumentsTab() {
  const { data: session } = useSession();
  const router = useRouter();
  const [documents, setDocuments] = useState<PMODocument[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [documentToDelete, setDocumentToDelete] = useState<PMODocument | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);

    // Get the system admin email from environment or use default
    const sysAdmin = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';

    // Reference to the files collection
    const filesCollection = collection(db, `users/${sysAdmin}/files`);

    // UUIDv4 regex pattern
    const uuidv4Regex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

    // Helper function to check if category matches PMO pattern
    const isPMOCategory = (category: string): boolean => {
      if (!category) return false;

      // Check for exact matches first
      if (category === 'PMO' || category === 'PMO Generated Documents') {
        return true;
      }

      // Check for pattern: starts with "PMO" and ends with UUIDv4
      if (category.startsWith('PMO')) {
        // Extract the last part that should be a UUID
        const parts = category.split(' - ');
        if (parts.length >= 3) {
          const lastPart = parts[parts.length - 1];
          return uuidv4Regex.test(lastPart);
        }
      }

      return false;
    };

    // Query for all files and filter client-side since Firestore doesn't support regex
    const allFilesQuery = query(
      filesCollection,
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(
      allFilesQuery,
      (snapshot) => {
        const allDocs = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        } as PMODocument));

        // Filter for PMO documents based on category pattern
        const pmoDocuments = allDocs.filter(doc => isPMOCategory(doc.category || ''));
        setDocuments(pmoDocuments);
        setLoading(false);
      },
      (err) => {
        console.error('Error fetching PMO documents:', err);
        setError('Failed to load PMO documents');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (!bytes || isNaN(bytes)) return '0 bytes';
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return 'Unknown date';

    try {
      // Handle different timestamp formats
      const date = timestamp.toDate ? timestamp.toDate() :
                  timestamp.seconds ? new Date(timestamp.seconds * 1000) :
                  new Date(timestamp);

      return format(date, 'MMM d, yyyy');
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  };

  const handleChatWithDocument = async (document: PMODocument) => {
    if (!session?.user?.email) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to chat with documents.",
        variant: "destructive"
      });
      return;
    }

    try {
      const userEmail = session.user.email;
      const chatsRef = collection(db, 'users', userEmail, 'chats');

      // Find or create a chat for this document
      // Query for chats with this document
      const chatQuery = query(
        chatsRef,
        where('fileDocumentId', '==', document.id),
        orderBy('createdAt', 'desc')
      );

      const chatSnapshot = await getDocs(chatQuery);
      let chatId: string;

      if (!chatSnapshot.empty) {
        // Use the last chat for this file
        chatId = chatSnapshot.docs[0].id;
      } else {
        // Create new chat for this file
        const chatData = {
          userId: userEmail,
          createdAt: serverTimestamp(),
          lastUpdated: serverTimestamp(),
          firstMessage: document.title || "PMO Document Chat",
          fileDocumentId: document.id,
          fileNamespace: document.namespace || document.id,
          category: document.category || 'PMO'
        };

        const newChatRef = await addDoc(chatsRef, chatData);
        chatId = newChatRef.id;
      }

      router.push(`/chat/${chatId}`);
    } catch (error) {
      console.error('Error handling document chat:', error);
      toast({
        title: "Error",
        description: "Failed to start chat with document. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteClick = (document: PMODocument) => {
    setDocumentToDelete(document);
    setShowDeleteDialog(true);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete || !session?.user?.email) {
      setShowDeleteDialog(false);
      return;
    }

    setIsDeleting(true);
    try {
      const sysAdmin = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';

      // Call the API to delete the document and associated data
      const response = await fetch('/api/deleteDocumentAndChats', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: sysAdmin,
          namespace: documentToDelete.namespace || documentToDelete.id,
        }),
      });

      if (response.ok) {
        toast({
          title: "Document deleted",
          description: "The document and associated chats have been deleted.",
        });

        // Remove from local state
        setDocuments(prev => prev.filter(doc => doc.id !== documentToDelete.id));
      } else {
        throw new Error('Failed to delete document');
      }
    } catch (error) {
      console.error('Error deleting document:', error);
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setDocumentToDelete(null);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-64" />
        {[1, 2, 3].map(i => (
          <Skeleton key={i} className="h-20 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-800 rounded-md text-red-800 dark:text-red-300">
        <p className="font-medium">{error}</p>
        <Button
          variant="outline"
          className="mt-2"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div>
      {documents.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <p>No PMO documents generated yet</p>
        </div>
      ) : (
        <div className="space-y-2">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center justify-between p-3 bg-card rounded-lg border">
              <div className="flex items-center space-x-3">
                <FileIcon className="w-5 h-5 text-red-400" />
                <div>
                  <p className="font-medium">{doc.title || doc.name || 'Unnamed document'}</p>
                  <div className="flex flex-wrap gap-x-4 gap-y-1 text-xs text-muted-foreground">
                    <span>{formatFileSize(doc.size || 0)}</span>
                    <span>{formatDate(doc.createdAt)}</span>
                    <span>By: {doc.agentName || doc.generatedBy || 'PMO Agent'}</span>
                    <span>FileId: {doc.namespace || doc.id}</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                {doc.downloadUrl ? (
                  <>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => window.open(doc.downloadUrl, '_blank')}
                      title="View"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      asChild
                      title="Download"
                    >
                      <a
                        href={doc.downloadUrl}
                        download={doc.name || `${doc.title || 'document'}.pdf`}
                        type="application/pdf"
                      >
                        <Download className="w-4 h-4" />
                      </a>
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleChatWithDocument(doc)}
                      title="Chat with document"
                    >
                      <MessageSquare className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(doc)}
                      title="Delete document"
                    >
                      <Trash2 className="w-4 h-4 text-red-400" />
                    </Button>
                  </>
                ) : (
                  <span className="p-1.5 text-muted-foreground" title="Download URL not available">
                    <ExternalLink className="w-4 h-4" />
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this document? This will also remove all associated chats and cannot be undone.
              <div className="mt-2 p-2 bg-muted rounded-md">
                <div className="font-medium">{documentToDelete?.title || documentToDelete?.name}</div>
                <div className="text-xs text-muted-foreground">Created: {documentToDelete?.createdAt ? formatDate(documentToDelete.createdAt) : 'Unknown'}</div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteDocument}
              disabled={isDeleting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
