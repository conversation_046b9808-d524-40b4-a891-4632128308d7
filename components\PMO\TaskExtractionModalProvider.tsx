"use client"

import React, { useEffect } from 'react'
import { useTaskExtractionModal, TaskExtractionResult } from '../../hooks/useTaskExtractionModal'
import TaskExtractionSuccessModal from './TaskExtractionSuccessModal'

interface TaskExtractionModalProviderProps {
  children: React.ReactNode
}

/**
 * Provider component that listens for task extraction success events
 * and displays the success modal when tasks are extracted
 */
export const TaskExtractionModalProvider: React.FC<TaskExtractionModalProviderProps> = ({ children }) => {
  const { isOpen, result, showModal, hideModal } = useTaskExtractionModal()

  useEffect(() => {
    // Listen for task extraction success events
    const handleTaskExtractionSuccess = (event: CustomEvent<TaskExtractionResult>) => {
      console.log('TaskExtractionModalProvider: Received task extraction success event', event.detail)
      showModal(event.detail)
    }

    // Add event listener
    window.addEventListener('taskExtractionSuccess', handleTaskExtractionSuccess as EventListener)

    // Cleanup
    return () => {
      window.removeEventListener('taskExtractionSuccess', handleTaskExtractionSuccess as EventListener)
    }
  }, [showModal])

  return (
    <>
      {children}
      {result && (
        <TaskExtractionSuccessModal
          isOpen={isOpen}
          onClose={hideModal}
          result={result}
          projectName={result.projectName}
          agentType={result.agentType}
        />
      )}
    </>
  )
}

export default TaskExtractionModalProvider
