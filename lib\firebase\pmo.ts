// lib/firebase/pmo.ts
import {
  collection,
  doc,
  setDoc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db, retryOperation } from '../../app/lib/firebase/config'; // Adjusted path
import {
  PMORecord,
  PMORecordStatus,
  PMORecordPriority, // Assuming PMORecordPriority is defined here or imported correctly
  PMOTaskAssignment,
  AgenticTeamId
} from '../agents/pmo/PMOInterfaces'; // Adjusted path
import { handleDomainAuthError, isDomainAuthError } from '../../app/lib/firebase/domainAuth'; // Adjusted path
import { v4 as uuidv4 } from 'uuid';

// Collection references
const pmoRecordsCollection = collection(db, 'pmoRecords');
const pmoTaskAssignmentsCollection = collection(db, 'pmoTaskAssignments');

// Helper function to get task assignments collection for a PMO record
const getPMOTaskAssignmentsCollection = (pmoRecordId: string) => {
  return collection(db, `pmoRecords/${pmoRecordId}/taskAssignments`);
};

// Helper function to prepare data for Firestore (handle Date objects)
const prepareForFirestore = (data: any): any => {
  if (!data) return data;
  const result: any = {};
  Object.keys(data).forEach(key => {
    const value = data[key];
    if (value instanceof Date) {
      result[key] = Timestamp.fromDate(value);
    } else if (Array.isArray(value)) {
      result[key] = value.map(item =>
        item instanceof Date ? Timestamp.fromDate(item) :
        typeof item === 'object' && item !== null ? prepareForFirestore(item) :
        item
      );
    } else if (typeof value === 'object' && value !== null) {
      result[key] = prepareForFirestore(value);
    } else {
      result[key] = value;
    }
  });
  return result;
};

// Helper function to convert Firestore Timestamps back to Date objects
const convertTimestamps = (data: any): any => {
  if (!data) return data;
  const result: any = { ...data };
  Object.keys(result).forEach(key => {
    const value = result[key];
    if (value && typeof value === 'object' && 'toDate' in value && typeof value.toDate === 'function') {
      result[key] = value.toDate();
    } else if (Array.isArray(value)) {
      result[key] = value.map(item =>
        item && typeof item === 'object' && 'toDate' in item && typeof item.toDate === 'function'
          ? item.toDate()
          : typeof item === 'object' && item !== null
            ? convertTimestamps(item)
            : item
      );
    } else if (typeof value === 'object' && value !== null) {
      result[key] = convertTimestamps(value);
    }
  });
  return result;
};

// PMO Record functions
export const getPMORecords = async (userEmail?: string): Promise<PMORecord[]> => {
  try {
    return await retryOperation(async () => {
      if (!userEmail) {
        console.warn("getPMORecords: userEmail is undefined or null. Returning empty array.");
        return [];
      }
      const isSystemAdmin = userEmail === '<EMAIL>';
      const recordsQuery = query(
        pmoRecordsCollection,
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(recordsQuery);
      let records = snapshot.docs.map(doc => {
        const data = doc.data();
        const convertedData = convertTimestamps(data);
        return {
          id: doc.id,
          ...convertedData
        } as PMORecord;
      });
      if (isSystemAdmin) {
        return records;
      }
      return records.filter(record => record.createdBy === userEmail);
    });
  } catch (error) {
    console.error('Error fetching PMO records with retry:', error);
    if (isDomainAuthError(error)) {
      handleDomainAuthError(error);
    }
    return [];
  }
};

export const getPMORecordById = async (recordId: string): Promise<PMORecord | null> => {
  const docRef = doc(pmoRecordsCollection, recordId);
  const docSnap = await getDoc(docRef);
  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...convertTimestamps(docSnap.data())
    } as PMORecord;
  } else {
    return null;
  }
};

/**
 * Adds a new PMO record to Firestore.
 * `createdAt` and `updatedAt` will be set by this function.
 * @param recordData Data for the PMO record, excluding `id`, `createdAt`, and `updatedAt`.
 * @returns The ID of the newly created PMO record.
 */
export const addPMORecord = async (recordData: Omit<PMORecord, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  const docRef = await addDoc(pmoRecordsCollection, {
    ...prepareForFirestore(recordData), // Ensure all Date objects are converted to Timestamps
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp()
  });
  return docRef.id;
};

/**
 * Creates a new PMO record from UI data and saves it to Firestore.
 * This function translates UI form data into the PMORecord structure.
 * @param createdByEmail - The email of the user creating the record.
 * @param uiData - Data collected from the PMO creation form.
 * @returns The ID of the newly created PMO record.
 */
export const createPMORecordFromUIData = async (
  createdByEmail: string,
  uiData: {
    pmoTitle: string; // This should come from `projectName` in the form
    description: string; // Optimized description
    modelProvider: string;
    selectedModel: string;
    customContext?: string;
    selectedDocumentId?: string; // namespace
    selectedCategory?: string;
  }
): Promise<string> => {
  if (!createdByEmail) throw new Error("User email (createdBy) is required to create a PMO record.");
  if (!uiData.pmoTitle) throw new Error("PMO Title is required.");
  if (!uiData.description) throw new Error("Description is required.");

  // Construct the data for PMORecord, excluding id, createdAt, updatedAt as addPMORecord handles them
  const recordDataToSave: Omit<PMORecord, 'id' | 'createdAt' | 'updatedAt'> = {
    title: uiData.pmoTitle,
    description: uiData.description,
    projectIds: [], // Initialize as empty
    taskIds: [],    // Initialize as empty
    summary: "",    // Initialize as empty or null
    dueDate: null,  // Not collected by the current form
    agentIds: [],   // Not collected by the current form
    category: 'Unknown', // Default category

    createdBy: createdByEmail,
    status: 'Pending' as PMORecordStatus, // Default status
    priority: 'Medium' as PMORecordPriority, // Default priority
    // Note: modelDetails is not part of PMORecord interface, storing in contextUsed instead
  };

  // Map context data to PMORecord properties
  if (uiData.customContext) {
    recordDataToSave.customContext = uiData.customContext;
  }
  if (uiData.selectedDocumentId) {
    recordDataToSave.contextFiles = [uiData.selectedDocumentId];
  }
  if (uiData.selectedCategory) {
    recordDataToSave.contextCategories = [uiData.selectedCategory];
  }

  // Use the existing addPMORecord function
  return addPMORecord(recordDataToSave);
};


export const updatePMORecord = async (recordId: string, recordData: Partial<PMORecord>): Promise<void> => {
  const recordRef = doc(pmoRecordsCollection, recordId);
  await updateDoc(recordRef, {
    ...prepareForFirestore(recordData),
    updatedAt: serverTimestamp()
  });
};

export const deletePMORecord = async (recordId: string): Promise<void> => {
  const recordRef = doc(pmoRecordsCollection, recordId);
  await deleteDoc(recordRef);
};

// PMO Task Assignment functions
export const getPMOTaskAssignments = async (pmoRecordId: string): Promise<PMOTaskAssignment[]> => {
  const assignmentsQuery = query(
    getPMOTaskAssignmentsCollection(pmoRecordId),
    orderBy('assignedAt', 'desc')
  );
  const snapshot = await getDocs(assignmentsQuery);
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...convertTimestamps(doc.data())
  } as PMOTaskAssignment));
};

export const addPMOTaskAssignment = async (assignmentData: Omit<PMOTaskAssignment, 'id'>): Promise<string> => {
  const docRef = await addDoc(getPMOTaskAssignmentsCollection(assignmentData.pmoRecordId), {
    ...prepareForFirestore(assignmentData),
    assignedAt: serverTimestamp()
  });
  return docRef.id;
};

export const updatePMOTaskAssignment = async (
  pmoRecordId: string,
  assignmentId: string,
  assignmentData: Partial<PMOTaskAssignment>
): Promise<void> => {
  const assignmentRef = doc(getPMOTaskAssignmentsCollection(pmoRecordId), assignmentId);
  await updateDoc(assignmentRef, prepareForFirestore(assignmentData));
};

export const deletePMOTaskAssignment = async (pmoRecordId: string, assignmentId: string): Promise<void> => {
  const assignmentRef = doc(getPMOTaskAssignmentsCollection(pmoRecordId), assignmentId);
  await deleteDoc(assignmentRef);
};