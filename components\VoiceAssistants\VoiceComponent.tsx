import React, { useEffect, useState } from "react";
import { useConversation } from "@11labs/react";
import { Mi<PERSON>, MicOff, Volume2, VolumeX } from "lucide-react";

interface VoiceChatProps {
  onVoiceInput: (text: string) => void;
  onVoiceResponse: (text: string) => void;
  isProcessing: boolean;
}

const VoiceChat: React.FC<VoiceChatProps> = ({
  onVoiceInput,
  onVoiceResponse,
  isProcessing
}) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isListening, setIsListening] = useState(false);

  const conversation = useConversation({
    onConnect: () => {
      console.log("Connected to ElevenLabs");
    },
    onDisconnect: () => {
      console.log("Disconnected from ElevenLabs");
    },
    onMessage: (message) => {
      if (typeof message === "string") {
        // <PERSON>le transcribed text from user's voice input
        onVoiceInput(message);
      } else if (message && typeof message === "object" && 'message' in message) {
        // Handle AI response that needs to be spoken
        const responseText = message.message || message.toString();
        onVoiceResponse(responseText);
      }
      // Assuming 'error' is a parameter or variable that should be defined
      const handleError = (error: any) => {
        setErrorMessage(typeof error === "string" ? error : error.message);
        console.error("Error:", error);
      };
      // Call handleError with the appropriate error object
    },
  });

  const { status, isSpeaking } = conversation;

  useEffect(() => {
    const requestMicPermission = async () => {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        setHasPermission(true);
      } catch (error) {
        setErrorMessage("Microphone access denied");
        console.error("Error accessing microphone:", error);
      }
    };

    requestMicPermission();
  }, []);

  const handleStartConversation = async () => {
    try {
      setIsListening(true);
      const conversationId = await conversation.startSession({
        agentId: process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID!,
      });
      console.log("Started conversation:", conversationId);
    } catch (error) {
      setErrorMessage("Failed to start conversation");
      console.error("Error starting conversation:", error);
    }
  };

  const handleEndConversation = async () => {
    try {
      setIsListening(false);
      await conversation.endSession();
    } catch (error) {
      setErrorMessage("Failed to end conversation");
      console.error("Error ending conversation:", error);
    }
  };

  const toggleMute = async () => {
    try {
      await conversation.setVolume({ volume: isMuted ? 1 : 0 });
      setIsMuted(!isMuted);
    } catch (error) {
      setErrorMessage("Failed to change volume");
      console.error("Error changing volume:", error);
    }
  };

  return (
    <div className="flex items-center justify-center gap-3 mt-5">
      <button
        onClick={toggleMute}
        disabled={status !== "connected"}
        className={`p-2 rounded-full ${
          status === "connected" 
            ? "bg-amber-600 hover:bg-amber-700" 
            : "bg-gray-600"
        } transition-colors`}
        title={isMuted ? "Unmute" : "Mute"}
      >
        {isMuted ? (
          <VolumeX className="h-4 w-4 text-white" />
        ) : (
          <Volume2 className="h-4 w-4 text-white" />
        )}
      </button>

      {status === "connected" ? (
        <button
          onClick={handleEndConversation}
          className="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-full transition-colors"
          disabled={isProcessing}
        >
          <MicOff className="h-4 w-4" />
          Stop Listening
        </button>
      ) : (
        <button
          onClick={handleStartConversation}
          className="flex items-center gap-2 px-4 py-1 bg-green-600 hover:bg-green-700 text-white rounded-full transition-colors"
          disabled={!hasPermission || isProcessing}
        >
          <Mic className="h-4 w-4" />
          Start Listening
        </button>
      )}

      {status === "connected" && (
        <div className="text-sm">
          {isSpeaking ? (
            <span className="text-green-500">Speaking...</span>
          ) : isListening ? (
            <span className="text-amber-500">Listening...</span>
          ) : null}
        </div>
      )}

      {errorMessage && (
        <div className="text-sm text-red-500">{errorMessage}</div>
      )}

      {!hasPermission && (
        <div className="text-sm text-yellow-500">
          Please allow microphone access
        </div>
      )}
    </div>
  );
};

export default VoiceChat;