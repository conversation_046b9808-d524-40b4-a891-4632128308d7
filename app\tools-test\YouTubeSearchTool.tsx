'use client';

import { useState } from 'react';
import { Loader2, Search, ExternalLink, Filter, ChevronDown, ChevronUp } from 'lucide-react';
import MarkdownRenderer from 'components/MarkdownRenderer';
import {
  directYouTubeSearch,
  SearchResponse,
  YouTubeSearchResult,
  YouTubeSearchFilters,
  getDateFromTimeAgo,
  formatDateForYouTubeAPI,
  searchYouTubeChannels,
  YouTubeChannel
} from './directYouTubeSearch';

export default function YouTubeSearchTool() {
  // State for the search query and results
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [maxResults, setMaxResults] = useState<number>(3);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  // State for filters
  const [videoDuration, setVideoDuration] = useState<'any' | 'short' | 'medium' | 'long'>('any');
  const [publishedTimeframe, setPublishedTimeframe] = useState<string>('any');
  const [sortOrder, setSortOrder] = useState<'relevance' | 'date' | 'rating' | 'viewCount'>('relevance');

  // State for channel search
  const [channelSearchQuery, setChannelSearchQuery] = useState<string>('');
  const [channelSearchResults, setChannelSearchResults] = useState<YouTubeChannel[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<YouTubeChannel | null>(null);
  const [isSearchingChannel, setIsSearchingChannel] = useState<boolean>(false);
  const [showChannelSearch, setShowChannelSearch] = useState<boolean>(false);

  // Helper function to get date filters based on timeframe selection
  const getDateFilters = (): { publishedAfter?: string, publishedBefore?: string } => {
    switch (publishedTimeframe) {
      case 'today':
        return { publishedAfter: formatDateForYouTubeAPI(getDateFromTimeAgo(1, 'days')) };
      case 'this_week':
        return { publishedAfter: formatDateForYouTubeAPI(getDateFromTimeAgo(7, 'days')) };
      case 'this_month':
        return { publishedAfter: formatDateForYouTubeAPI(getDateFromTimeAgo(1, 'months')) };
      case 'this_year':
        return { publishedAfter: formatDateForYouTubeAPI(getDateFromTimeAgo(1, 'years')) };
      case 'last_hour':
        const oneHourAgo = new Date();
        oneHourAgo.setHours(oneHourAgo.getHours() - 1);
        return { publishedAfter: formatDateForYouTubeAPI(oneHourAgo) };
      default:
        return {}; // No date filters for 'any'
    }
  };

  // Function to search for YouTube channels
  const handleChannelSearch = async () => {
    if (!channelSearchQuery) return;

    setIsSearchingChannel(true);

    try {
      const channels = await searchYouTubeChannels(channelSearchQuery);
      setChannelSearchResults(channels);
    } catch (error) {
      console.error('Error searching for channels:', error);
    } finally {
      setIsSearchingChannel(false);
    }
  };

  // Function to select a channel
  const handleSelectChannel = (channel: YouTubeChannel) => {
    setSelectedChannel(channel);
    setShowChannelSearch(false);
  };

  // Function to clear the selected channel
  const clearSelectedChannel = () => {
    setSelectedChannel(null);
  };

  // Handle search submission
  const handleSearch = async () => {
    if (!searchQuery) return;

    setLoading(true);
    setError(null);
    setSearchResults(null);

    try {
      // Prepare search filters
      const dateFilters = getDateFilters();

      const searchFilters: YouTubeSearchFilters = {
        maxResults,
        videoDuration: videoDuration === 'any' ? undefined : videoDuration,
        publishedAfter: dateFilters.publishedAfter,
        publishedBefore: dateFilters.publishedBefore,
        order: sortOrder,
        // Include channel ID if a channel is selected
        ...(selectedChannel ? {
          channelId: selectedChannel.id,
          channelTitle: selectedChannel.title
        } : {})
      };

      // First try the direct client-side approach
      const searchResults = await directYouTubeSearch(searchQuery, searchFilters);

      if (searchResults.success && searchResults.results.length > 0) {
        setSearchResults(searchResults);
      } else if (searchResults.metadata.error) {
        // If direct search fails, try the server-side API as fallback
        console.warn('Direct YouTube search failed, falling back to server API:', searchResults.metadata.error);

        // Call the YouTube search API through our server
        const response = await fetch(`/api/youtube-search?q=${encodeURIComponent(searchQuery)}&maxResults=${maxResults}`);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || errorData.details || `Error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        // Check if we have items in the response
        if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
          throw new Error('No videos found matching your search criteria');
        }

        // Transform the API response to match our SearchResponse interface
        const videoResults: YouTubeSearchResult[] = data.items.map((item: any) => ({
          title: item.snippet.title,
          description: item.snippet.description,
          videoId: item.id.videoId,
          thumbnailUrl: item.snippet.thumbnails.high?.url || '',
          channelTitle: item.snippet.channelTitle,
          publishedAt: item.snippet.publishedAt,
          statistics: item.statistics || {
            viewCount: '0',
            likeCount: '0',
            commentCount: '0'
          },
          contentDetails: item.contentDetails || {
            duration: 'PT0M0S'
          }
        }));

        setSearchResults({
          success: true,
          results: videoResults,
          metadata: {
            source: 'youtube_api_server',
            resultCount: videoResults.length,
            enhancedQuery: searchQuery
          }
        });
      } else {
        throw new Error('No videos found matching your search criteria');
      }
    } catch (err) {
      console.error('YouTube search error:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Format the date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format view count with commas and abbreviations
  const formatViewCount = (viewCount: string = '0') => {
    const count = parseInt(viewCount, 10);
    if (count >= 1000000000) {
      return `${(count / 1000000000).toFixed(1)}B views`;
    } else if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K views`;
    } else {
      return `${count} views`;
    }
  };

  // Format like count with commas and abbreviations
  const formatLikeCount = (likeCount: string = '0') => {
    const count = parseInt(likeCount, 10);
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    } else {
      return count.toString();
    }
  };

  // Parse ISO 8601 duration format (PT1H2M3S) to readable format
  const formatDuration = (duration: string = 'PT0M0S') => {
    // Handle empty or invalid duration
    if (!duration || !duration.startsWith('PT')) {
      return '0:00';
    }

    const matches = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!matches) return '0:00';

    const hours = matches[1] ? parseInt(matches[1], 10) : 0;
    const minutes = matches[2] ? parseInt(matches[2], 10) : 0;
    const seconds = matches[3] ? parseInt(matches[3], 10) : 0;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  };

  // Get YouTube video URL
  const getVideoUrl = (videoId: string): string => {
    return `https://www.youtube.com/watch?v=${videoId}`;
  };

  // Get YouTube channel URL
  const getChannelUrl = (channelTitle: string): string => {
    return `https://www.youtube.com/results?search_query=${encodeURIComponent(channelTitle)}`;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* YouTube Search Input Card */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <Search className="mr-2 h-5 w-5 text-red-500" />
            YouTube Search Tool
          </h2>
          <p className="text-sm text-zinc-400">
            Search for videos on YouTube
          </p>
        </div>
        <div className="p-4 space-y-4">
          <div className="space-y-2">
            <label htmlFor="youtube-search" className="block text-sm font-medium text-zinc-300">Search Query</label>
            <div className="flex gap-2">
              <input
                id="youtube-search"
                type="text"
                placeholder="Enter your search query..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !loading && searchQuery) {
                    handleSearch();
                  }
                }}
              />
              <select
                value={maxResults.toString()}
                onChange={(e) => setMaxResults(parseInt(e.target.value))}
                className="w-[100px] px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
              >
                <option value="3">3 videos</option>
                <option value="5">5 videos</option>
                <option value="8">8 videos</option>
                <option value="10">10 videos</option>
              </select>
            </div>

            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center text-sm text-zinc-400 hover:text-zinc-300 mt-2"
            >
              <Filter className="h-4 w-4 mr-1" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
              {showFilters ? <ChevronUp className="h-4 w-4 ml-1" /> : <ChevronDown className="h-4 w-4 ml-1" />}
            </button>

            {showFilters && (
              <div className="mt-3 p-3 bg-zinc-800/50 rounded-md border border-zinc-700 space-y-3">
                {/* Channel search section */}
                <div>
                  <label className="block text-sm font-medium text-zinc-300 mb-1">Channel</label>

                  {selectedChannel ? (
                    <div className="flex items-center justify-between bg-zinc-800 p-2 rounded-md border border-zinc-700">
                      <div className="flex items-center">
                        {selectedChannel.thumbnailUrl && (
                          <img
                            src={selectedChannel.thumbnailUrl}
                            alt={selectedChannel.title}
                            className="w-8 h-8 rounded-full mr-2"
                          />
                        )}
                        <div>
                          <p className="text-sm font-medium text-white">{selectedChannel.title}</p>
                        </div>
                      </div>
                      <button
                        onClick={clearSelectedChannel}
                        className="text-zinc-400 hover:text-red-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  ) : (
                    <div>
                      <button
                        type="button"
                        onClick={() => setShowChannelSearch(!showChannelSearch)}
                        className="w-full flex items-center justify-between px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white text-sm"
                      >
                        <span>Search for a channel</span>
                        {showChannelSearch ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                      </button>

                      {showChannelSearch && (
                        <div className="mt-2 p-2 bg-zinc-800 border border-zinc-700 rounded-md">
                          <div className="flex gap-2 mb-2">
                            <input
                              type="text"
                              placeholder="Enter channel name..."
                              value={channelSearchQuery}
                              onChange={(e) => setChannelSearchQuery(e.target.value)}
                              className="flex-1 px-3 py-1 bg-zinc-700 border border-zinc-600 rounded-md text-white text-sm placeholder-zinc-400 focus:outline-none focus:ring-1 focus:ring-red-500"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && !isSearchingChannel && channelSearchQuery) {
                                  handleChannelSearch();
                                }
                              }}
                            />
                            <button
                              onClick={handleChannelSearch}
                              disabled={isSearchingChannel || !channelSearchQuery}
                              className={`px-3 py-1 rounded-md text-sm ${isSearchingChannel || !channelSearchQuery ? 'bg-zinc-600 text-zinc-400' : 'bg-red-600 text-white hover:bg-red-700'}`}
                            >
                              {isSearchingChannel ? 'Searching...' : 'Search'}
                            </button>
                          </div>

                          {channelSearchResults.length > 0 && (
                            <div className="max-h-[200px] overflow-y-auto">
                              {channelSearchResults.map((channel) => (
                                <div
                                  key={channel.id}
                                  onClick={() => handleSelectChannel(channel)}
                                  className="flex items-center p-2 hover:bg-zinc-700 cursor-pointer rounded-md"
                                >
                                  {channel.thumbnailUrl && (
                                    <img
                                      src={channel.thumbnailUrl}
                                      alt={channel.title}
                                      className="w-8 h-8 rounded-full mr-2"
                                    />
                                  )}
                                  <div>
                                    <p className="text-sm font-medium text-white">{channel.title}</p>
                                    <p className="text-xs text-zinc-400 truncate">{channel.description}</p>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <label htmlFor="video-duration" className="block text-sm font-medium text-zinc-300 mb-1">Video Duration</label>
                  <select
                    id="video-duration"
                    value={videoDuration}
                    onChange={(e) => setVideoDuration(e.target.value as any)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
                  >
                    <option value="any">Any duration</option>
                    <option value="short">Short (&lt; 4 minutes)</option>
                    <option value="medium">Medium (4-20 minutes)</option>
                    <option value="long">Long (&gt; 20 minutes)</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="published-date" className="block text-sm font-medium text-zinc-300 mb-1">Upload Date</label>
                  <select
                    id="published-date"
                    value={publishedTimeframe}
                    onChange={(e) => setPublishedTimeframe(e.target.value)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
                  >
                    <option value="any">Any time</option>
                    <option value="last_hour">Last hour</option>
                    <option value="today">Today</option>
                    <option value="this_week">This week</option>
                    <option value="this_month">This month</option>
                    <option value="this_year">This year</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="sort-order" className="block text-sm font-medium text-zinc-300 mb-1">Sort By</label>
                  <select
                    id="sort-order"
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as any)}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 text-sm"
                  >
                    <option value="relevance">Relevance</option>
                    <option value="date">Upload date</option>
                    <option value="viewCount">View count</option>
                    <option value="rating">Rating</option>
                  </select>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="p-4 border-t border-zinc-700">
          <button
            onClick={handleSearch}
            disabled={loading || !searchQuery}
            className={`w-full flex items-center justify-center px-4 py-2 rounded-md ${loading || !searchQuery ? 'bg-zinc-700 text-zinc-400 cursor-not-allowed' : 'bg-red-600 text-white hover:bg-red-700'}`}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                <Search className="mr-2 h-4 w-4" />
                Search YouTube
              </>
            )}
          </button>
        </div>
      </div>

      {/* Results Card */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <h2 className="text-xl font-semibold text-white">Search Results</h2>
          <p className="text-sm text-zinc-400">
            YouTube video search results
          </p>
        </div>
        <div className="p-4">
          {loading ? (
            <div className="flex items-center justify-center h-[300px]">
              <Loader2 className="h-8 w-8 animate-spin text-red-500" />
            </div>
          ) : error ? (
            <div className="bg-red-900/20 p-4 rounded-md text-red-400">
              <p className="font-semibold">Error</p>
              <p>{error}</p>
              {error.includes('API key') && (
                <div className="mt-4 text-sm">
                  <p className="font-semibold">Missing YouTube API Key</p>
                  <p>To use this feature, you need to add a YouTube API key to your environment variables.</p>
                  <ol className="list-decimal ml-5 mt-2 space-y-1">
                    <li>Get a YouTube Data API key from the <a href="https://console.cloud.google.com/apis/library/youtube.googleapis.com" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Google Cloud Console</a></li>
                    <li>Add it to your <code className="bg-zinc-800 px-1 py-0.5 rounded">.env.local</code> file as <code className="bg-zinc-800 px-1 py-0.5 rounded">YOUTUBE_API_KEY=your_api_key</code></li>
                    <li>Restart your development server</li>
                  </ol>
                </div>
              )}

              {error.includes('referer') && (
                <div className="mt-4 text-sm">
                  <p className="font-semibold">API Key HTTP Referrer Restriction</p>
                  <p>Your YouTube API key has HTTP referrer restrictions that are blocking requests.</p>
                  <ol className="list-decimal ml-5 mt-2 space-y-1">
                    <li>Go to the <a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">Google Cloud Console Credentials page</a></li>
                    <li>Find your API key and click "Edit"</li>
                    <li>Under "Application restrictions", select "HTTP referrers (websites)"</li>
                    <li>Add the following referrers:
                      <ul className="list-disc ml-5 mt-1">
                        <li><code className="bg-zinc-800 px-1 py-0.5 rounded">http://localhost:*</code></li>
                        <li><code className="bg-zinc-800 px-1 py-0.5 rounded">http://127.0.0.1:*</code></li>
                        <li>Add your production domain if needed</li>
                      </ul>
                    </li>
                    <li>Click "Save" and wait a few minutes for changes to propagate</li>
                    <li>Refresh this page and try again</li>
                  </ol>
                  <p className="mt-4 text-yellow-400">
                    <strong>Note:</strong> We've updated the search to use a client-side approach that should work even with referrer restrictions.
                    If you're still seeing this error, please try refreshing the page and clearing your browser cache.
                  </p>
                </div>
              )}

              {error.includes('quota') && (
                <div className="mt-4 text-sm">
                  <p className="font-semibold">API Quota Exceeded</p>
                  <p>Your YouTube API quota has been exceeded for today.</p>
                  <p className="mt-2">The YouTube Data API has a default quota of 10,000 units per day. Different operations consume different amounts of quota. You can try again tomorrow when the quota resets.</p>
                </div>
              )}
            </div>
          ) : searchResults && searchResults.success ? (
            <div className="h-[500px] overflow-y-auto space-y-4">
              {/* Show channel info if a channel filter was applied */}
              {searchResults.metadata.channelId && searchResults.metadata.channelTitle && (
                <div className="bg-zinc-800/50 p-3 rounded-md border border-zinc-700 mb-4">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm text-zinc-400">Showing videos from channel:</p>
                      <p className="text-white font-medium">{searchResults.metadata.channelTitle}</p>
                    </div>
                    <button
                      onClick={clearSelectedChannel}
                      className="px-3 py-1 text-xs bg-zinc-700 hover:bg-zinc-600 text-zinc-300 rounded-md"
                    >
                      Clear Channel Filter
                    </button>
                  </div>
                </div>
              )}

              {searchResults.results.length > 0 ? (
                searchResults.results.map((video) => (
                  <div key={video.videoId} className="border border-zinc-700 rounded-md overflow-hidden hover:border-red-500 transition-colors">
                    <div className="flex flex-col sm:flex-row">
                      <div className="sm:w-1/3 h-32 relative group cursor-pointer" onClick={() => window.open(getVideoUrl(video.videoId), '_blank')}>
                        <img
                          src={video.thumbnailUrl}
                          alt={video.title}
                          className="w-full h-full object-cover"
                        />
                        {/* Duration badge */}
                        <div className="absolute bottom-1 right-1 bg-black bg-opacity-80 text-white text-xs px-1 py-0.5 rounded">
                          {formatDuration(video.contentDetails?.duration)}
                        </div>
                        {/* Play overlay */}
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                          <div className="w-12 h-12 flex items-center justify-center bg-red-600 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white" className="w-6 h-6">
                              <path d="M8 5v14l11-7z" />
                            </svg>
                          </div>
                        </div>
                      </div>
                      <div className="sm:w-2/3 p-3">
                        <a
                          href={getVideoUrl(video.videoId)}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:underline font-medium line-clamp-2"
                        >
                          {video.title}
                        </a>

                        {/* Channel and metadata */}
                        <div className="flex items-center mt-1 text-xs text-zinc-400">
                          <a
                            href={getChannelUrl(video.channelTitle)}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="hover:text-zinc-300 hover:underline"
                          >
                            {video.channelTitle}
                          </a>
                          <span className="mx-1">•</span>
                          <span>{formatViewCount(video.statistics?.viewCount)}</span>
                          <span className="mx-1">•</span>
                          <span>{formatDate(video.publishedAt)}</span>
                        </div>

                        {/* Description */}
                        <p className="text-zinc-400 text-sm mt-1 line-clamp-2">{video.description}</p>

                        {/* Stats */}
                        <div className="flex items-center mt-2 text-xs text-zinc-500 space-x-3">
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4 mr-1">
                              <path d="M7.493 18.75c-.425 0-.82-.236-.975-.632A7.48 7.48 0 016 15.375c0-1.75.599-3.358 1.602-4.634.151-.192.373-.309.6-.397.473-.183.89-.514 1.212-.924a9.042 9.042 0 012.861-2.4c.723-.384 1.35-.956 1.653-1.715a4.498 4.498 0 00.322-1.672V3a.75.75 0 01.75-.75 2.25 2.25 0 012.25 2.25c0 1.152-.26 2.243-.723 3.218-.266.558.107 1.282.725 1.282h3.126c1.026 0 1.945.694 2.054 1.715.045.422.068.85.068 1.285a11.95 11.95 0 01-2.649 7.521c-.388.482-.987.729-1.605.729H14.23c-.483 0-.964-.078-1.423-.23l-3.114-1.04a4.501 4.501 0 00-1.423-.23h-.777zM2.331 10.977a11.969 11.969 0 00-.831 4.398 12 12 0 00.52 3.507c.26.85 1.084 1.368 1.973 1.368H4.9c.445 0 .72-.498.523-.898a8.963 8.963 0 01-.924-3.977c0-1.708.476-3.305 1.302-4.666.245-.403-.028-.959-.5-.959H4.25c-.832 0-1.612.453-1.918 1.227z" />
                            </svg>
                            {formatLikeCount(video.statistics?.likeCount)}
                          </div>
                          <div className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4 mr-1">
                              <path fillRule="evenodd" d="M4.804 21.644A6.707 6.707 0 006 21.75a6.721 6.721 0 003.583-1.029c.774.182 1.584.279 2.417.279 5.322 0 9.75-3.97 9.75-9 0-5.03-4.428-9-9.75-9s-9.75 3.97-9.75 9c0 2.409 1.025 4.587 2.674 *************.277.428.254.543a3.73 3.73 0 01-.814 1.686.75.75 0 00.44 1.223zM8.25 10.875a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25zM10.875 12a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zm4.875-1.125a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25z" clipRule="evenodd" />
                            </svg>
                            {formatLikeCount(video.statistics?.commentCount)}
                          </div>
                          <div className="flex-grow text-right">
                            <a
                              href={getVideoUrl(video.videoId)}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-red-500 hover:text-red-400"
                            >
                              Watch on YouTube
                              <ExternalLink size={12} className="ml-1" />
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center h-[300px] text-zinc-400">
                  <p>No videos found matching your search</p>
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-[300px] text-zinc-400">
              <p>Enter a search query to find YouTube videos</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
