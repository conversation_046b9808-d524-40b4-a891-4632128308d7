/**
 * documentProcessing.ts
 *
 * Comprehensive document processing with memory optimization
 */

import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { TextLoader } from "langchain/document_loaders/fs/text";
import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import { Document } from "langchain/document";
import * as XLSX from 'xlsx';
import {
  getDetailedMemoryUsage,
  forceMemoryCleanup,
  processTextInChunks
} from './utils/enhancedMemoryManager';

// Document chunk interface
export interface DocumentChunk {
  pageContent: string;
  metadata: Record<string, any>;
}

// Processing options interface
export interface DocumentProcessingOptions {
  chunkSize?: number;
  chunkOverlap?: number;
  aggressiveMemoryManagement?: boolean;
  streamProcessing?: boolean;
  maxConcurrentChunks?: number;
}

// Default processing options
const DEFAULT_OPTIONS: DocumentProcessingOptions = {
  chunkSize: 1000,
  chunkOverlap: 200,
  aggressiveMemoryManagement: true,
  streamProcessing: true,
  maxConcurrentChunks: 5
};

/**
 * Clean object by removing undefined/null values
 */
function cleanObject(obj: Record<string, any>): Record<string, any> {
  return Object.fromEntries(
    Object.entries(obj)
      .filter(([_, v]) => v != null)
      .map(([k, v]) => [k, typeof v === 'object' && v !== null ? cleanObject(v) : v])
  );
}

/**
 * Process text files using TextLoader with memory optimization
 */
async function processText(
  file: File | Blob | { arrayBuffer: () => Promise<ArrayBuffer> },
  options: DocumentProcessingOptions = DEFAULT_OPTIONS
): Promise<DocumentChunk[]> {
  try {
    getDetailedMemoryUsage('Before text processing');

    // Ensure file is a Blob
    const blob = file instanceof Blob ? file : new Blob([await file.arrayBuffer()], { type: 'text/plain' });

    // Use stream processing if enabled
    if (options.streamProcessing) {
      // Read file in chunks
      const text = await blob.text();

      // Log text size
      console.log(`Processing text of length ${text.length} characters`);

      // Determine optimal chunk size based on text length
      let initialChunkSize = 100000; // Default size for initial chunking

      // For very large texts, use smaller initial chunks
      if (text.length > 1000000) { // 1MB of text
        initialChunkSize = 50000;
        console.log(`Very large text detected (${text.length} chars), using smaller initial chunks of ${initialChunkSize}`);
      }

      // Process text in chunks to avoid memory issues
      const chunks: DocumentChunk[] = [];
      await processTextInChunks(text, async (textChunk, index) => {
        console.log(`Processing text chunk ${index+1} (${textChunk.length} chars)`);

        // Create a document from the chunk
        const doc = new Document({ pageContent: textChunk, metadata: { chunk_index: index } });

        // Determine optimal chunk size for splitting
        let chunkSize = options.chunkSize || DEFAULT_OPTIONS.chunkSize!;
        let chunkOverlap = options.chunkOverlap || DEFAULT_OPTIONS.chunkOverlap!;

        // For large text chunks, use more aggressive chunking
        if (textChunk.length > 10000) {
          const numChunks = Math.ceil(textChunk.length / 2000); // Aim for ~2000 chars per chunk
          chunkSize = Math.ceil(textChunk.length / numChunks);
          chunkOverlap = Math.min(chunkOverlap, 50); // Reduce overlap for large chunks
          console.log(`Large text chunk detected (${textChunk.length} chars), using chunk size ${chunkSize} with overlap ${chunkOverlap}`);
        }

        // Create splitter with appropriate chunk size
        const splitter = new RecursiveCharacterTextSplitter({
          chunkSize: chunkSize,
          chunkOverlap: chunkOverlap,
          separators: ["\n\n", "\n", ". ", " ", ""]
        });

        // Split the document
        const splitDocs = await splitter.splitDocuments([doc]);
        console.log(`Split text chunk ${index+1} into ${splitDocs.length} smaller chunks`);

        // Add to chunks
        splitDocs.forEach(splitDoc => {
          chunks.push({
            pageContent: splitDoc.pageContent,
            metadata: cleanObject({
              ...splitDoc.metadata,
              type: 'text_chunk'
            })
          });
        });

        // Force cleanup after each chunk if aggressive memory management is enabled
        if (options.aggressiveMemoryManagement) {
          getDetailedMemoryUsage(`After processing text chunk ${index+1}`);
          forceMemoryCleanup();

          // Small delay to allow GC to complete
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        return null;
      }, initialChunkSize); // Use appropriate chunk size for initial reading

      getDetailedMemoryUsage('After text processing');
      return chunks;
    } else {
      // Traditional processing
      const loader = new TextLoader(blob);
      const docs = await loader.load();

      if (docs.length === 0) {
        throw new Error('No content found in text file');
      }

      const splitter = new RecursiveCharacterTextSplitter({
        chunkSize: options.chunkSize || DEFAULT_OPTIONS.chunkSize!,
        chunkOverlap: options.chunkOverlap || DEFAULT_OPTIONS.chunkOverlap!,
        separators: ["\n\n", "\n", ". ", " ", ""]
      });

      const chunks = await splitter.splitDocuments(docs);

      // Force cleanup
      if (options.aggressiveMemoryManagement) {
        forceMemoryCleanup();
      }

      getDetailedMemoryUsage('After text processing');

      return chunks.map(chunk => ({
        pageContent: chunk.pageContent,
        metadata: cleanObject({
          ...chunk.metadata,
          type: 'text_chunk'
        })
      }));
    }
  } catch (error) {
    console.error('Text processing error:', error);
    throw error;
  }
}

/**
 * Process PDF files with memory optimization
 */
async function processPDF(
  file: File | Blob | { arrayBuffer: () => Promise<ArrayBuffer> },
  options: DocumentProcessingOptions = DEFAULT_OPTIONS
): Promise<DocumentChunk[]> {
  try {
    getDetailedMemoryUsage('Before PDF processing');

    // Ensure file is a Blob
    const blob = file instanceof Blob ? file : new Blob([await file.arrayBuffer()], { type: 'application/pdf' });

    // Create PDF loader
    const pdfLoader = new PDFLoader(blob, {
      splitPages: true // Process one page at a time
    });

    // Load PDF content
    const pdfPages = await pdfLoader.load();

    getDetailedMemoryUsage('After PDF loading');

    // Process pages in batches to avoid memory issues
    const allChunks: DocumentChunk[] = [];
    const batchSize = options.maxConcurrentChunks || DEFAULT_OPTIONS.maxConcurrentChunks!;

    for (let i = 0; i < pdfPages.length; i += batchSize) {
      console.log(`Processing PDF pages ${i+1} to ${Math.min(i+batchSize, pdfPages.length)} of ${pdfPages.length}`);

      // Get current batch of pages
      const pageBatch = pdfPages.slice(i, i + batchSize);

      // Process each page individually for better memory management
      for (let j = 0; j < pageBatch.length; j++) {
        const page = pageBatch[j];
        console.log(`Processing PDF page ${i+j+1} of ${pdfPages.length}`);

        // Create splitter with potentially smaller chunk size for very large pages
        const pageLength = page.pageContent.length;
        let chunkSize = options.chunkSize || DEFAULT_OPTIONS.chunkSize!;
        let chunkOverlap = options.chunkOverlap || DEFAULT_OPTIONS.chunkOverlap!;

        // For very large pages, use more aggressive chunking
        if (pageLength > 10000) {
          const numChunks = Math.ceil(pageLength / 2000); // Aim for ~2000 chars per chunk
          chunkSize = Math.ceil(pageLength / numChunks);
          chunkOverlap = Math.min(chunkOverlap, 50); // Reduce overlap for large pages
          console.log(`Large page detected (${pageLength} chars), using chunk size ${chunkSize} with overlap ${chunkOverlap}`);
        }

        const splitter = new RecursiveCharacterTextSplitter({
          chunkSize: chunkSize,
          chunkOverlap: chunkOverlap,
          separators: ["\n\n", "\n", ". ", " ", ""]
        });

        // Split page into chunks
        const pageChunks = await splitter.splitDocuments([page]);

        // Add to all chunks
        pageChunks.forEach(chunk => {
          allChunks.push({
            pageContent: chunk.pageContent,
            metadata: cleanObject({
              ...chunk.metadata,
              type: 'pdf_chunk'
            })
          });
        });

        // Force cleanup after each page if aggressive memory management is enabled
        if (options.aggressiveMemoryManagement) {
          getDetailedMemoryUsage(`After processing PDF page ${i+j+1}`);
          forceMemoryCleanup();

          // Small delay to allow GC to complete
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // Force cleanup after each batch
      if (options.aggressiveMemoryManagement) {
        getDetailedMemoryUsage(`After processing PDF batch ${i/batchSize + 1}`);
        forceMemoryCleanup();

        // Small delay to allow GC to complete
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    getDetailedMemoryUsage('After PDF processing');
    return allChunks;
  } catch (error) {
    console.error('PDF processing error:', error);
    throw error;
  }
}

/**
 * Process DOCX files with memory optimization
 */
async function processDocx(
  file: File | Blob | { arrayBuffer: () => Promise<ArrayBuffer> },
  options: DocumentProcessingOptions = DEFAULT_OPTIONS
): Promise<DocumentChunk[]> {
  try {
    getDetailedMemoryUsage('Before DOCX processing');

    // Ensure file is a Blob
    const blob = file instanceof Blob ? file : new Blob([await file.arrayBuffer()], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });

    // Create DOCX loader
    const docxLoader = new DocxLoader(blob);

    // Load DOCX content
    const docContent = await docxLoader.load();

    getDetailedMemoryUsage('After DOCX loading');

    // Process DOCX content in smaller batches for better memory management
    const allChunks: DocumentChunk[] = [];
    const batchSize = options.maxConcurrentChunks || DEFAULT_OPTIONS.maxConcurrentChunks!;

    // Calculate total content length for logging
    let totalContentLength = 0;
    docContent.forEach(doc => {
      totalContentLength += doc.pageContent.length;
    });

    console.log(`DOCX content total length: ${totalContentLength} characters`);

    // Process in batches of documents
    for (let i = 0; i < docContent.length; i += batchSize) {
      console.log(`Processing DOCX batch ${i+1} to ${Math.min(i+batchSize, docContent.length)} of ${docContent.length}`);

      // Get current batch
      const contentBatch = docContent.slice(i, i + batchSize);

      // Process each document in the batch individually
      for (let j = 0; j < contentBatch.length; j++) {
        const doc = contentBatch[j];
        console.log(`Processing DOCX document ${i+j+1} of ${docContent.length}`);

        // Determine chunk size based on content length
        const contentLength = doc.pageContent.length;
        let chunkSize = options.chunkSize || DEFAULT_OPTIONS.chunkSize!;
        let chunkOverlap = options.chunkOverlap || DEFAULT_OPTIONS.chunkOverlap!;

        // For very large content, use more aggressive chunking
        if (contentLength > 10000) {
          const numChunks = Math.ceil(contentLength / 2000); // Aim for ~2000 chars per chunk
          chunkSize = Math.ceil(contentLength / numChunks);
          chunkOverlap = Math.min(chunkOverlap, 50); // Reduce overlap for large content
          console.log(`Large content detected (${contentLength} chars), using chunk size ${chunkSize} with overlap ${chunkOverlap}`);
        }

        // Create splitter with appropriate chunk size
        const splitter = new RecursiveCharacterTextSplitter({
          chunkSize: chunkSize,
          chunkOverlap: chunkOverlap,
          separators: ["\n\n", "\n", ". ", " ", ""]
        });

        // Split document into chunks
        const docChunks = await splitter.splitDocuments([doc]);

        // Add to all chunks
        docChunks.forEach(chunk => {
          allChunks.push({
            pageContent: chunk.pageContent,
            metadata: cleanObject({
              ...chunk.metadata,
              type: 'docx_chunk'
            })
          });
        });

        // Force cleanup after each document if aggressive memory management is enabled
        if (options.aggressiveMemoryManagement) {
          getDetailedMemoryUsage(`After processing DOCX document ${i+j+1}`);
          forceMemoryCleanup();

          // Small delay to allow GC to complete
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // Force cleanup after each batch
      if (options.aggressiveMemoryManagement) {
        getDetailedMemoryUsage(`After processing DOCX batch ${i/batchSize + 1}`);
        forceMemoryCleanup();

        // Small delay to allow GC to complete
        await new Promise(resolve => setTimeout(resolve, 300));
      }
    }

    getDetailedMemoryUsage('After DOCX processing');
    return allChunks;
  } catch (error) {
    console.error('DOCX processing error:', error);
    throw error;
  }
}

/**
 * Process CSV files with memory optimization
 */
async function processCSV(
  file: File | Blob | { arrayBuffer: () => Promise<ArrayBuffer> },
  options: DocumentProcessingOptions = DEFAULT_OPTIONS
): Promise<DocumentChunk[]> {
  try {
    getDetailedMemoryUsage('Before CSV processing');

    // Read file content
    const arrayBuffer = file instanceof Blob
      ? await file.arrayBuffer()
      : await file.arrayBuffer();

    // Parse CSV
    const data = new Uint8Array(arrayBuffer);
    const workbook = XLSX.read(data, { type: 'array' });
    const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = XLSX.utils.sheet_to_json(firstSheet, { header: 1 }) as any[][];

    // Process rows in batches
    const chunks: DocumentChunk[] = [];
    const batchSize = 100; // Process 100 rows at a time

    for (let i = 0; i < rows.length; i += batchSize) {
      console.log(`Processing CSV rows ${i+1} to ${Math.min(i+batchSize, rows.length)} of ${rows.length}`);

      // Get current batch of rows
      const rowBatch = rows.slice(i, i + batchSize);

      // Process batch
      rowBatch.forEach((row, rowIndex) => {
        if (row && row.length > 0) {
          const rowContent = row.join(', ');
          chunks.push({
            pageContent: rowContent,
            metadata: {
              type: 'csv_chunk',
              row_index: i + rowIndex,
              total_rows: rows.length
            }
          });
        }
      });

      // Force cleanup after each batch if aggressive memory management is enabled
      if (options.aggressiveMemoryManagement) {
        forceMemoryCleanup();
      }
    }

    getDetailedMemoryUsage('After CSV processing');
    return chunks;
  } catch (error) {
    console.error('CSV processing error:', error);
    throw error;
  }
}

/**
 * Process Excel files with memory optimization
 */
async function processExcel(
  file: File | Blob | { arrayBuffer: () => Promise<ArrayBuffer> },
  options: DocumentProcessingOptions = DEFAULT_OPTIONS
): Promise<DocumentChunk[]> {
  try {
    getDetailedMemoryUsage('Before Excel processing');

    // Read file content
    const arrayBuffer = file instanceof Blob
      ? await file.arrayBuffer()
      : await file.arrayBuffer();

    // Parse Excel
    const data = new Uint8Array(arrayBuffer);
    const workbook = XLSX.read(data, { type: 'array' });

    // Process each sheet
    const chunks: DocumentChunk[] = [];

    for (const sheetName of workbook.SheetNames) {
      console.log(`Processing Excel sheet: ${sheetName}`);

      const sheet = workbook.Sheets[sheetName];
      const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 }) as any[][];

      // Process rows in batches
      const batchSize = 100; // Process 100 rows at a time

      for (let i = 0; i < rows.length; i += batchSize) {
        console.log(`Processing rows ${i+1} to ${Math.min(i+batchSize, rows.length)} of ${rows.length} in sheet ${sheetName}`);

        // Get current batch of rows
        const rowBatch = rows.slice(i, i + batchSize);

        // Process batch
        rowBatch.forEach((row, rowIndex) => {
          if (row && row.length > 0) {
            const rowContent = row.join(', ');
            chunks.push({
              pageContent: rowContent,
              metadata: {
                type: 'excel_chunk',
                sheet: sheetName,
                row_index: i + rowIndex,
                total_rows: rows.length
              }
            });
          }
        });

        // Force cleanup after each batch if aggressive memory management is enabled
        if (options.aggressiveMemoryManagement) {
          forceMemoryCleanup();
        }
      }
    }

    getDetailedMemoryUsage('After Excel processing');
    return chunks;
  } catch (error) {
    console.error('Excel processing error:', error);
    throw error;
  }
}

/**
 * Main document processing function with memory optimization
 */
export async function processDocument(
  file: File | { arrayBuffer: () => Promise<ArrayBuffer>, type?: string },
  docId: string,
  fileType: string,
  fileName: string,
  userId: string,
  category: string,
  additionalParameter: string = '',
  options: DocumentProcessingOptions = DEFAULT_OPTIONS
): Promise<DocumentChunk[]> {
  try {
    console.log(`Processing document: ${fileName} (${fileType})`);
    console.log(`Options: ${JSON.stringify(options)}`);

    getDetailedMemoryUsage('Before document processing');

    let chunks: DocumentChunk[] = [];

    // Process based on file type
    switch (fileType.toLowerCase()) {
      case "text/csv":
        chunks = await processCSV(file, options);
        break;

      case "application/vnd.ms-excel":
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        const excelBlob = new Blob([await file.arrayBuffer()], { type: file.type });
        chunks = await processExcel(excelBlob, options);
        break;

      case "text/plain":
      case "application/rtf":
        chunks = await processText(file, options);
        break;

      case "application/msword":
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        const docxBlob = new Blob([await file.arrayBuffer()], { type: file.type });
        chunks = await processDocx(docxBlob, options);
        break;

      case "application/pdf":
        const pdfBlob = new Blob([await file.arrayBuffer()], { type: file.type });
        chunks = await processPDF(pdfBlob, options);
        break;

      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }

    if (!chunks.length) {
      throw new Error('No content was extracted from the document');
    }

    // Add common metadata to all chunks
    const processedChunks = chunks.map((chunk, index) => ({
      pageContent: chunk.pageContent,
      metadata: cleanObject({
        ...chunk.metadata,
        doc_id: docId,
        chunk_id: `${docId}_${index + 1}`,
        file_name: fileName,
        file_type: fileType,
        position: index + 1,
        total_chunks: chunks.length,
        is_summary: chunk.metadata.is_summary || index === 0,
        processed_at: new Date().toISOString(),
        user_id: userId,
        category: category,
        additional_parameter: additionalParameter || undefined
      })
    }));

    // Final cleanup
    if (options.aggressiveMemoryManagement) {
      forceMemoryCleanup();
    }

    getDetailedMemoryUsage('After document processing');

    console.log(`Document processing complete. Generated ${processedChunks.length} chunks.`);
    return processedChunks;
  } catch (error) {
    console.error('Document processing error:', error);
    throw error;
  }
}