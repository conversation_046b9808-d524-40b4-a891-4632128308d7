'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ArrowLeft,
  Calendar,
  Clock,
  Users,
  AlertCircle,
  CheckCircle,
  XCircle,
  PauseCircle,
  Edit,
  Trash2,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '../../../context/AuthContext';
import { PMORecord, PMORecordStatus, PMORecordPriority, AgenticTeamId } from '../../../../lib/agents/pmo/PMOInterfaces';
import { getPMORecord, deletePMORecord } from '../../../../lib/firebase/pmoCollection';
import { Button } from '../../../../components/ui/button';
import MarkdownRenderer from 'components/MarkdownRenderer';

interface PMORecordPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function PMORecordPage({ params }: PMORecordPageProps) {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();

  const [record, setRecord] = useState<PMORecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirm, setDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [id, setId] = useState<string | null>(null);

  // Handle async params
  useEffect(() => {
    params.then(({ id }) => {
      setId(id);
    });
  }, [params]);

  // Get status icon based on record status
  const getStatusIcon = (status: PMORecordStatus) => {
    switch (status) {
      case 'Draft':
        return <PauseCircle className="w-5 h-5 text-gray-400" />;
      case 'In Progress':
        return <Clock className="w-5 h-5 text-blue-400" />;
      case 'Completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'Cancelled':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-400" />;
    }
  };

  // Get priority color class
  const getPriorityColorClass = (priority: PMORecordPriority) => {
    switch (priority) {
      case 'Low':
        return 'bg-green-500/20 text-green-400';
      case 'Medium':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'High':
        return 'bg-orange-500/20 text-orange-400';
      case 'Critical':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  // Get team name from ID
  const getTeamName = (teamId: AgenticTeamId) => {
    switch (teamId) {
      case AgenticTeamId.Marketing:
        return 'Marketing';
      case AgenticTeamId.Research:
        return 'Research';
      case AgenticTeamId.SoftwareDesign:
        return 'Software Design';
      case AgenticTeamId.Sales:
        return 'Sales';
      case AgenticTeamId.BusinessAnalysis:
        return 'Business Analysis';
      default:
        return 'Unknown Team';
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Fetch PMO record
  const fetchPMORecord = async () => {
    if (!user?.email || !id) return;

    setLoading(true);
    setError(null);

    try {
      const fetchedRecord = await getPMORecord(user.email, id);

      if (!fetchedRecord) {
        setError('PMO record not found');
      } else {
        setRecord(fetchedRecord);
      }
    } catch (err: any) {
      console.error('Error fetching PMO record:', err);
      setError(err.message || 'Failed to fetch PMO record');
    } finally {
      setLoading(false);
    }
  };

  // Initialize
  useEffect(() => {
    if (authLoading) return;

    if (!user) {
      router.push('/login');
      return;
    }

    if (!id) return;

    fetchPMORecord();
  }, [user, authLoading, router, id]);

  // Handle delete
  const handleDelete = async () => {
    if (!user?.email || !record) return;

    setIsDeleting(true);

    try {
      await deletePMORecord(user.email, record.id);
      router.push('/services/pmo');
    } catch (err: any) {
      console.error('Error deleting PMO record:', err);
      setError(err.message || 'Failed to delete PMO record');
      setIsDeleting(false);
      setDeleteConfirm(false);
    }
  };

  // Loading state
  if (authLoading || loading || !id) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-white text-xl flex items-center">
          <RefreshCw className="animate-spin mr-2" />
          Loading PMO record...
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button
              onClick={() => router.push('/services/pmo')}
              variant="outline"
              className="mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to PMO
            </Button>
          </div>

          <div className="bg-red-900/30 border border-red-700 rounded-lg p-6 text-center">
            <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2 text-red-300">Error</h2>
            <p className="text-red-200 mb-4">{error}</p>
            <Button
              onClick={fetchPMORecord}
              className="bg-red-700 hover:bg-red-600 text-white"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // No record state
  if (!record) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center mb-6">
            <Button
              onClick={() => router.push('/services/pmo')}
              variant="outline"
              className="mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to PMO
            </Button>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 text-center">
            <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">PMO Record Not Found</h2>
            <p className="text-gray-400 mb-4">The requested PMO record could not be found.</p>
            <Button
              onClick={() => router.push('/services/pmo')}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Return to PMO Dashboard
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6 gap-4">
          <div className="flex items-center">
            <Button
              onClick={() => router.push('/services/pmo')}
              variant="outline"
              className="mr-4"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <h1 className="text-2xl font-bold">
              {record.title}
              <span className="text-sm text-gray-400 ml-2">- {record.id}</span>
            </h1>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              onClick={() => router.push(`/services/pmo/${record.id}/edit`)}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>

            {deleteConfirm ? (
              <>
                <span className="text-sm text-red-400">Confirm delete?</span>
                <Button
                  onClick={handleDelete}
                  className="bg-red-600 hover:bg-red-700 text-white"
                  disabled={isDeleting}
                >
                  {isDeleting ? 'Deleting...' : 'Yes, Delete'}
                </Button>
                <Button
                  onClick={() => setDeleteConfirm(false)}
                  variant="outline"
                >
                  Cancel
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setDeleteConfirm(true)}
                variant="destructive"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            )}
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg border border-gray-700 overflow-hidden">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                {getStatusIcon(record.status)}
                <span className="ml-2 text-lg font-medium">{record.status}</span>
              </div>

              <span className={`text-sm font-medium px-3 py-1 rounded-full ${getPriorityColorClass(record.priority)}`}>
                {record.priority}
              </span>
            </div>

            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">
                {record.title}
                <span className="text-sm text-gray-400 ml-2">- {record.id}</span>
              </h2>
              <p className="text-gray-300">{record.description}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-2">Created</h3>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <span>{formatDate(record.createdAt)}</span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-2">Last Updated</h3>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 text-gray-500 mr-2" />
                  <span>{formatDate(record.updatedAt)}</span>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-2">Category</h3>
                <span>{record.category || 'Uncategorized'}</span>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-2">Assigned Teams</h3>
                <div className="flex items-center">
                  <Users className="w-4 h-4 text-gray-500 mr-2" />
                  <span>
                    {record.agentIds && record.agentIds.length > 0
                      ? record.agentIds.map(teamId => getTeamName(teamId)).join(', ')
                      : 'No teams assigned'}
                  </span>
                </div>
              </div>
            </div>

            {record.pmoAssessment && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">
                  PMO Assessment
                  <span className="text-sm text-gray-400 ml-2">
                    Project ID: {record.id}
                  </span>
                </h3>
                <div className="bg-gray-700/50 p-4 rounded-md whitespace-pre-wrap text-gray-300">
                  <MarkdownRenderer content={record.pmoAssessment} />
                </div>
              </div>
            )}

            {record.teamSelectionRationale && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Team Selection Rationale</h3>
                <div className="bg-gray-700/50 p-4 rounded-md text-gray-300">
                  {record.teamSelectionRationale}
                </div>
              </div>
            )}

            {record.resourceRecommendations && record.resourceRecommendations.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2">Recommended Resources</h3>
                <ul className="list-disc list-inside bg-gray-700/50 p-4 rounded-md text-gray-300">
                  {record.resourceRecommendations.map((resource, index) => (
                    <li key={index}>{resource}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
