export interface VoiceData {
  id: string;
  name: string;
  gender: 'female' | 'male';
  description: string;
  previewText: string;
}

export const AVAILABLE_VOICES: VoiceData[] = [
  // Female voices
  {
    id: 'rCuVrCHOUMY3OwyJBJym',
    name: '<PERSON>',
    gender: 'female',
    description: 'Warm and professional female voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  {
    id: 'QQutlXbwqnU9C4Zprxnn',
    name: '<PERSON>',
    gender: 'female',
    description: 'Clear and articulate female voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  {
    id: 'P7x743VjyZEOihNNygQ9',
    name: '<PERSON>',
    gender: 'female',
    description: 'Friendly and engaging female voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  // Male voices
  {
    id: 'kmSVBPu7loj4ayNinwWM',
    name: '<PERSON>',
    gender: 'male',
    description: 'Confident and clear male voice',
    previewText: 'Hi, this is <PERSON> from CastMate, your scene rehearsal partner'
  },
  {
    id: 'AeRdCCKzvd23BpJoofzx',
    name: '<PERSON>',
    gender: 'male',
    description: 'Smooth and professional male voice',
    previewText: 'Hi, this is Nathaniel from CastMate, your scene rehearsal partner'
  },
  {
    id: 'vVnXvLYPFjIyE2YrjUBE',
    name: 'Brad',
    gender: 'male',
    description: 'Energetic and dynamic male voice',
    previewText: 'Hi, this is Brad from CastMate, your scene rehearsal partner'
  }
];

/**
 * Generates a preview audio URL for a given voice using ElevenLabs TTS API
 */
export async function generateVoicePreview(voiceId: string, text: string): Promise<string> {
  try {
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_API_KEY || process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;

    console.log(`[VOICE_UTILS] Generating voice preview for voiceId: ${voiceId}`);
    console.log(`[VOICE_UTILS] Using API key: ${apiKey ? 'Present' : 'Missing'}`);
    console.log(`[VOICE_UTILS] Text length: ${text.length} characters`);

    if (!apiKey) {
      console.error('[VOICE_UTILS] No ElevenLabs API key found, falling back to browser TTS');
      return generateBrowserTTSPreview(text);
    }

    // Use our API route to avoid CORS issues
    const response = await fetch('/api/elevenlabs/voice-preview', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        voiceId: voiceId,
        text: text
      }),
    });

    console.log(`[VOICE_UTILS] Response status: ${response.status} ${response.statusText}`);
    console.log(`[VOICE_UTILS] Response headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      let errorMessage = 'Unknown error';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch {
        errorMessage = await response.text();
      }
      console.error(`[VOICE_UTILS] API Error Response:`, errorMessage);

      // If API fails, try fallback to browser TTS
      console.log(`[VOICE_UTILS] Falling back to browser TTS`);
      return generateBrowserTTSPreview(text);
    }

    const audioBlob = await response.blob();
    console.log(`[VOICE_UTILS] Audio blob size: ${audioBlob.size} bytes, type: ${audioBlob.type}`);

    // Validate the blob
    if (audioBlob.size === 0) {
      console.warn(`[VOICE_UTILS] Received empty audio blob, falling back to browser TTS`);
      return generateBrowserTTSPreview(text);
    }

    if (!audioBlob.type.includes('audio')) {
      console.warn(`[VOICE_UTILS] Unexpected blob type: ${audioBlob.type}, falling back to browser TTS`);
      return generateBrowserTTSPreview(text);
    }

    const audioUrl = URL.createObjectURL(audioBlob);
    console.log(`[VOICE_UTILS] Created audio URL: ${audioUrl}`);

    return audioUrl;
  } catch (error) {
    console.error('[VOICE_UTILS] Error generating voice preview, falling back to browser TTS:', error);
    return generateBrowserTTSPreview(text);
  }
}

/**
 * Fallback function to generate audio using browser's built-in TTS
 */
async function generateBrowserTTSPreview(text: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      // Check if browser supports Speech Synthesis
      if (!('speechSynthesis' in window)) {
        reject(new Error('Browser does not support text-to-speech'));
        return;
      }

      console.log(`[VOICE_UTILS] Using browser TTS for text: "${text.substring(0, 50)}..."`);

      // Create a simple audio tone as a placeholder since we can't easily capture browser TTS
      // This is a temporary solution - ideally we'd use Web Audio API to capture the speech
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(440, audioContext.currentTime); // A4 note
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 1);

      // For now, just speak the text and return a placeholder URL
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 0.8;

      speechSynthesis.speak(utterance);

      // Return a data URL for a short beep sound as a placeholder
      const beepDataUrl = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';

      resolve(beepDataUrl);
    } catch (error) {
      console.error('[VOICE_UTILS] Browser TTS fallback failed:', error);
      reject(error);
    }
  });
}

/**
 * Gets voice data by ID
 */
export function getVoiceById(voiceId: string): VoiceData | undefined {
  return AVAILABLE_VOICES.find(voice => voice.id === voiceId);
}

/**
 * Gets voices by gender
 */
export function getVoicesByGender(gender: 'female' | 'male'): VoiceData[] {
  return AVAILABLE_VOICES.filter(voice => voice.gender === gender);
}

/**
 * Validates if a voice ID exists in our predefined list
 */
export function isVoiceIdInList(voiceId: string): boolean {
  return AVAILABLE_VOICES.some(voice => voice.id === voiceId);
}

/**
 * Gets the first available voice ID as a fallback
 */
export function getFirstAvailableVoiceId(): string {
  return AVAILABLE_VOICES[0]?.id || 'rCuVrCHOUMY3OwyJBJym';
}

/**
 * Validates voice ID and provides fallback suggestions
 */
export function validateVoiceIdWithFallback(voiceId: string): {
  isValid: boolean;
  voiceId: string;
  fallbackUsed: boolean;
  originalVoiceId?: string;
} {
  if (isVoiceIdInList(voiceId)) {
    return {
      isValid: true,
      voiceId,
      fallbackUsed: false
    };
  }

  const fallbackVoiceId = getFirstAvailableVoiceId();
  return {
    isValid: false,
    voiceId: fallbackVoiceId,
    fallbackUsed: true,
    originalVoiceId: voiceId
  };
}
