/**
 * Test suite for timeout configuration
 * 
 * This test verifies that the timeout configuration system works correctly
 * and provides appropriate values for different scenarios.
 */

const { 
  BUSINESS_ANALYSIS_TIMEOUTS,
  PMO_NOTIFICATION_TIMEOUTS,
  calculateBusinessAnalysisTimeout,
  createTimeoutController,
  TimeoutError,
  TimeoutErrorType,
  validateTimeoutConfig
} = require('../lib/config/timeout-config');

describe('Timeout Configuration', () => {
  
  describe('Default Configuration Values', () => {
    test('Business Analysis timeouts should have reasonable defaults', () => {
      expect(BUSINESS_ANALYSIS_TIMEOUTS.requestTimeout).toBeGreaterThan(0);
      expect(BUSINESS_ANALYSIS_TIMEOUTS.connectionTimeout).toBeGreaterThan(0);
      expect(BUSINESS_ANALYSIS_TIMEOUTS.keepAliveTimeout).toBeGreaterThan(0);
      expect(BUSINESS_ANALYSIS_TIMEOUTS.maxRetries).toBeGreaterThanOrEqual(0);
      expect(BUSINESS_ANALYSIS_TIMEOUTS.retryDelay).toBeGreaterThan(0);
      
      // Check analysis type timeouts
      expect(BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts.system).toBeGreaterThan(0);
      expect(BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts.comprehensive).toBeGreaterThan(
        BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts.system
      );
    });

    test('PMO notification timeouts should have reasonable defaults', () => {
      expect(PMO_NOTIFICATION_TIMEOUTS.requestTimeout).toBeGreaterThan(0);
      expect(PMO_NOTIFICATION_TIMEOUTS.connectionTimeout).toBeGreaterThan(0);
      expect(PMO_NOTIFICATION_TIMEOUTS.keepAliveTimeout).toBeGreaterThan(0);
      expect(PMO_NOTIFICATION_TIMEOUTS.maxRetries).toBeGreaterThanOrEqual(0);
      expect(PMO_NOTIFICATION_TIMEOUTS.retryDelay).toBeGreaterThan(0);
    });

    test('PMO timeout should be longer than Business Analysis timeout', () => {
      // PMO timeout should accommodate BA processing time plus buffer
      expect(PMO_NOTIFICATION_TIMEOUTS.requestTimeout).toBeGreaterThan(
        BUSINESS_ANALYSIS_TIMEOUTS.requestTimeout
      );
    });
  });

  describe('Dynamic Timeout Calculation', () => {
    test('should calculate correct timeout for different analysis types', () => {
      const systemTimeout = calculateBusinessAnalysisTimeout('system', 1);
      const comprehensiveTimeout = calculateBusinessAnalysisTimeout('comprehensive', 1);
      
      expect(comprehensiveTimeout).toBeGreaterThan(systemTimeout);
      expect(systemTimeout).toBe(BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts.system);
    });

    test('should add time for additional analysts', () => {
      const oneAnalyst = calculateBusinessAnalysisTimeout('system', 1);
      const threeAnalysts = calculateBusinessAnalysisTimeout('system', 3);
      
      const expectedDifference = BUSINESS_ANALYSIS_TIMEOUTS.perAnalystTimeout * 2; // 3-1=2 additional
      expect(threeAnalysts).toBe(oneAnalyst + expectedDifference);
    });

    test('should handle edge cases', () => {
      // Zero analysts should use base timeout
      const zeroAnalysts = calculateBusinessAnalysisTimeout('system', 0);
      expect(zeroAnalysts).toBe(BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts.system);
      
      // Negative analysts should use base timeout
      const negativeAnalysts = calculateBusinessAnalysisTimeout('system', -1);
      expect(negativeAnalysts).toBe(BUSINESS_ANALYSIS_TIMEOUTS.analysisTypeTimeouts.system);
    });
  });

  describe('Timeout Controller', () => {
    test('should create timeout controller with cleanup function', () => {
      const { controller, timeoutId, cleanup } = createTimeoutController(1000);
      
      expect(controller).toBeInstanceOf(AbortController);
      expect(typeof timeoutId).toBe('object'); // NodeJS.Timeout
      expect(typeof cleanup).toBe('function');
      
      // Cleanup should not throw
      expect(() => cleanup()).not.toThrow();
    });

    test('should abort after specified timeout', (done) => {
      const { controller, cleanup } = createTimeoutController(100); // 100ms
      
      controller.signal.addEventListener('abort', () => {
        cleanup();
        done();
      });
    }, 200); // Test timeout of 200ms
  });

  describe('Timeout Error Handling', () => {
    test('should create TimeoutError with correct properties', () => {
      const error = new TimeoutError(
        TimeoutErrorType.REQUEST_TIMEOUT,
        30000,
        'Custom message'
      );
      
      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(TimeoutError);
      expect(error.type).toBe(TimeoutErrorType.REQUEST_TIMEOUT);
      expect(error.timeoutMs).toBe(30000);
      expect(error.message).toBe('Custom message');
      expect(error.name).toBe('TimeoutError');
    });

    test('should use default message when none provided', () => {
      const error = new TimeoutError(TimeoutErrorType.ABORT_TIMEOUT, 5000);
      
      expect(error.message).toContain('5000ms');
      expect(error.message).toContain('ABORT_TIMEOUT');
    });
  });

  describe('Configuration Validation', () => {
    test('should validate correct configuration', () => {
      expect(() => validateTimeoutConfig(BUSINESS_ANALYSIS_TIMEOUTS)).not.toThrow();
      expect(() => validateTimeoutConfig(PMO_NOTIFICATION_TIMEOUTS)).not.toThrow();
    });

    test('should reject invalid configuration', () => {
      const invalidConfig = {
        requestTimeout: -1,
        connectionTimeout: 30000,
        keepAliveTimeout: 60000,
        maxRetries: 2,
        retryDelay: 5000
      };
      
      expect(() => validateTimeoutConfig(invalidConfig)).toThrow('Request timeout must be positive');
    });

    test('should reject configuration with negative retries', () => {
      const invalidConfig = {
        requestTimeout: 30000,
        connectionTimeout: 30000,
        keepAliveTimeout: 60000,
        maxRetries: -1,
        retryDelay: 5000
      };
      
      expect(() => validateTimeoutConfig(invalidConfig)).toThrow('Max retries cannot be negative');
    });
  });

  describe('Environment Variable Integration', () => {
    test('should use environment variables when available', () => {
      // This test assumes environment variables might be set
      // In a real environment, you could mock process.env
      
      const originalTimeout = BUSINESS_ANALYSIS_TIMEOUTS.requestTimeout;
      expect(typeof originalTimeout).toBe('number');
      expect(originalTimeout).toBeGreaterThan(0);
    });
  });

  describe('Real-world Scenarios', () => {
    test('should provide adequate timeout for typical business analysis', () => {
      // Typical scenario: comprehensive analysis with 3 analysts
      const timeout = calculateBusinessAnalysisTimeout('comprehensive', 3);
      
      // Should be at least 60 minutes (3600000ms) for comprehensive analysis
      expect(timeout).toBeGreaterThanOrEqual(3600000);
      
      // Should be reasonable (less than 2 hours)
      expect(timeout).toBeLessThan(7200000);
    });

    test('should provide adequate timeout for PMO notification', () => {
      // PMO should accommodate the longest possible BA analysis plus buffer
      const longestBA = calculateBusinessAnalysisTimeout('comprehensive', 5);
      
      expect(PMO_NOTIFICATION_TIMEOUTS.requestTimeout).toBeGreaterThan(longestBA);
    });
  });
});

// Helper function to simulate timeout scenarios
function simulateTimeout(timeoutMs) {
  return new Promise((resolve, reject) => {
    const { controller, cleanup } = createTimeoutController(timeoutMs);
    
    controller.signal.addEventListener('abort', () => {
      cleanup();
      reject(new TimeoutError(TimeoutErrorType.ABORT_TIMEOUT, timeoutMs));
    });
    
    // Simulate work that takes longer than timeout
    setTimeout(() => {
      cleanup();
      resolve('completed');
    }, timeoutMs + 100);
  });
}

describe('Timeout Simulation', () => {
  test('should timeout when operation takes too long', async () => {
    await expect(simulateTimeout(50)).rejects.toThrow(TimeoutError);
  });
});
