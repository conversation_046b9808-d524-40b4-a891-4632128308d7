import { contentFormatterTool, processContentWithLLM } from '../content-formatter';

/**
 * This is a simple test file to verify that the content formatter is working correctly.
 * You can run this test with:
 * 
 * ```
 * npx ts-node lib/tools/tests/content-formatter.test.ts
 * ```
 */

async function testContentFormatter() {
  console.log('Testing content formatter...');
  
  const testContent = `
  <html>
    <head>
      <title>Test Page</title>
    </head>
    <body>
      <header>
        <nav>
          <ul>
            <li><a href="/">Home</a></li>
            <li><a href="/about">About</a></li>
            <li><a href="/contact">Contact</a></li>
          </ul>
        </nav>
      </header>
      <main>
        <h1>Product Documentation</h1>
        <p>This is a test page for the content formatter.</p>
        <h2>Features</h2>
        <ul>
          <li>Feature 1: Description of feature 1</li>
          <li>Feature 2: Description of feature 2</li>
          <li>Feature 3: Description of feature 3</li>
        </ul>
        <h2>Usage</h2>
        <p>Here's how to use the product:</p>
        <ol>
          <li>Step 1: Do this</li>
          <li>Step 2: Do that</li>
          <li>Step 3: Finish up</li>
        </ol>
      </main>
      <footer>
        <p>Copyright 2023</p>
      </footer>
    </body>
  </html>
  `;
  
  const testUrl = 'https://example.com/product-docs';
  
  try {
    // Test the class-based implementation
    console.log('Testing class-based implementation...');
    const formattedContent = await contentFormatterTool.formatContent(testContent, testUrl);
    console.log('Formatted content:');
    console.log(formattedContent);
    
    // Test the legacy function
    console.log('\nTesting legacy function...');
    const legacyFormattedContent = await processContentWithLLM(testContent, testUrl);
    console.log('Legacy formatted content:');
    console.log(legacyFormattedContent);
    
    console.log('\nTests completed successfully!');
  } catch (error) {
    console.error('Error during testing:', error);
  }
}

// Run the test
testContentFormatter();
