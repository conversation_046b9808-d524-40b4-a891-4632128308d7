/**
 * Image Generation Tool for creating images from text prompts
 * Uses OpenAI's DALL-E API and Google's Imagen to generate images
 * Integrates with Firebase for job management and storage
 */

// Import the Imagen tool
import { generateWithImagen } from "./imagen";
import { GenerateImageAgent } from "../../components/Agents/GenerateImageAgent";
import { GenerateImageTool as ComponentImageTool } from "../../components/tools/generateImageTool";
import OpenAI from 'openai';
import { v4 as uuidv4 } from 'uuid';

// We'll dynamically import Firebase admin modules to avoid issues with server components
// import { adminDb, adminStorage } from 'components/firebase-admin';

// Define types for image generation
export type ImageModel = 'dall-e-3' | 'dall-e-2' | 'gpt-image-1' | 'imagen-3.0-generate-002' | 'imagen-3.0-generate-001' | 'imagen-4.0-generate-preview-06-06' | 'imagen-4.0-ultra-generate-preview-06-06';
export type ImageSize = '1024x1024' | '1536x1024' | '1024x1536' | '1792x1024' | '1024x1792' | 'auto' | '512x512' | '256x256';
export type ImageStyle = 'vivid' | 'natural';
export type ImageQuality = 'low' | 'medium' | 'high' | 'auto' | 'standard' | 'hd';
export type ImageFormat = 'png' | 'jpeg' | 'webp';
export type ImageBackground = 'transparent' | 'opaque' | 'auto';
export type ImageCompression = number; // 0-100%

export interface ImageGenerationOptions {
  prompt: string;
  refinePrompt?: boolean;
  model?: ImageModel;
  size?: ImageSize;
  style?: ImageStyle;
  quality?: ImageQuality;
  format?: ImageFormat;
  background?: ImageBackground;
  compression?: ImageCompression;
  userId?: string; // Added userId for Firebase integration
}

export interface ImageGenerationResult {
  imageUrl: string;
  prompt: string;
  model: ImageModel;
  jobId: string;
  namespace: string;
  timestamp: string;
  savedToGallery?: boolean;
  galleryUrl?: string;
  message?: string;
}

interface OpenAIImageResponse {
  url: string;
  revised_prompt?: string;
  [key: string]: any;
}

export class GenerateImageTool {
  private apiKey: string | undefined;
  private baseUrl: string;
  private defaultModel: ImageModel;
  private defaultSize: ImageSize;
  private imageAgent: GenerateImageAgent | null = null;
  private componentImageTool: ComponentImageTool | null = null;

  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "generateImage",
    description: "Generate images from text descriptions using AI models like gpt-image-1, DALL-E, Imagen, and store them in Firebase.",
    parameters: {
      type: "object",
      properties: {
        prompt: {
          type: "string",
          description: "Text description of the image to generate."
        },
        refinePrompt: {
          type: "boolean",
          description: "Whether to refine the prompt with AI before generating the image.",
          default: true
        },
        model: {
          type: "string",
          description: "The image generation model to use.",
          enum: ["dall-e-3", "gpt-image-1", "dall-e-2", "imagen-3.0-generate-002", "imagen-3.0-generate-001", "imagen-4.0-generate-preview-06-06", "imagen-4.0-ultra-generate-preview-06-06"],
          default: "dall-e-3"
        },
        size: {
          type: "string",
          description: "The size of the generated image.",
          enum: ["1024x1024", "1536x1024", "1024x1536", "1792x1024", "1024x1792", "auto", "512x512", "256x256"],
          default: "1024x1024"
        },
        style: {
          type: "string",
          description: "The style of the generated image (DALL-E 3 only).",
          enum: ["vivid", "natural"],
          default: "vivid"
        },
        quality: {
          type: "string",
          description: "The quality of the generated image.",
          enum: ["low", "medium", "high", "auto", "standard", "hd"],
          default: "auto"
        },
        format: {
          type: "string",
          description: "The file format of the generated image.",
          enum: ["png", "jpeg", "webp"],
          default: "jpeg"
        },
        background: {
          type: "string",
          description: "The background type for the generated image.",
          enum: ["transparent", "opaque", "auto"],
          default: "auto"
        },
        compression: {
          type: "number",
          description: "Compression level (0-100%) for JPEG and WebP formats.",
          minimum: 0,
          maximum: 100
        },
        userId: {
          type: "string",
          description: "User ID for Firebase storage integration."
        }
      },
      required: ["prompt"]
    },
    returns: {
      type: "object",
      properties: {
        imageUrl: {
          type: "string",
          description: "URL of the generated image"
        },
        prompt: {
          type: "string",
          description: "The prompt used to generate the image (may be refined)"
        },
        model: {
          type: "string",
          description: "The model used to generate the image"
        },
        jobId: {
          type: "string",
          description: "Unique identifier for the image generation job"
        },
        namespace: {
          type: "string",
          description: "Namespace for the image in the storage system"
        }
      }
    },
    examples: [
      {
        input: { prompt: "A serene mountain landscape at sunset with a lake reflecting the sky", userId: "user123" },
        output: "Returns a URL to the generated image of a mountain landscape"
      },
      {
        input: {
          prompt: "A futuristic city with flying cars",
          model: "gpt-image-1",
          size: "1024x1024",
          userId: "user123"
        },
        output: "Returns a URL to a high-quality image of a futuristic city"
      }
    ]
  };

  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY;
    this.baseUrl = 'https://api.openai.com/v1';
    this.defaultModel = 'dall-e-3';
    this.defaultSize = '1024x1024';

    // Initialize the component image tool and agent if API key is available
    if (this.apiKey) {
      this.componentImageTool = new ComponentImageTool(this.apiKey);
      this.imageAgent = new GenerateImageAgent({ generateImageTool: this.componentImageTool });
    }
  }

  /**
   * Generate an image from a text prompt using Firebase job processing
   * @param options - Image generation options
   * @returns - The generated image result
   */
  async generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      model = this.defaultModel,
      userId,
      refinePrompt = true,
      size = this.defaultSize,
      style = 'vivid',
      quality = 'auto',
      format = 'jpeg',
      background = 'auto',
      compression
    } = options;

    if (!prompt) {
      throw new Error('Prompt is required for image generation');
    }

    // If userId is provided, use Firebase job processing
    if (userId && this.imageAgent) {
      return this.generateImageWithFirebase(
        prompt,
        userId,
        model,
        size,
        style,
        quality,
        format,
        background,
        compression
      );
    } else {
      // Fall back to direct generation without Firebase
      return this.generateImageDirect(options);
    }
  }

  /**
   * Generate an image using Firebase job processing
   * @param prompt - The prompt to generate an image from
   * @param userId - The user ID for Firebase storage
   * @param model - The model to use
   * @returns - The generated image result
   */
  private async generateImageWithFirebase(
    prompt: string,
    userId: string,
    model: ImageModel,
    size?: ImageSize,
    style?: ImageStyle,
    quality?: ImageQuality,
    format?: ImageFormat,
    background?: ImageBackground,
    compression?: ImageCompression
  ): Promise<ImageGenerationResult> {
    try {
      if (!this.imageAgent) {
        throw new Error('Image agent not initialized');
      }

      // Phase 1: Initialize the job in Firebase
      // This matches the implementation in initializeImageGeneration API
      const jobId = await this.imageAgent.initializeJob(
        prompt,
        userId,
        model || 'gpt-image-1',  // Ensure we have a default if undefined
        size || '1024x1024',     // Ensure we have a default if undefined
        style || 'vivid',        // Ensure we have a default if undefined
        quality || 'auto',       // Ensure we have a default if undefined
        format || 'jpeg',        // Ensure we have a default if undefined
        background || 'auto',    // Ensure we have a default if undefined
        compression
      );
      console.log(`Job initialized with ID: ${jobId}`);

      // Phase 2: Refine the prompt
      try {
        await this.imageAgent.refinePrompt(jobId, userId);
        console.log(`Prompt refined for job: ${jobId}`);
      } catch (refinementError) {
        console.warn('Prompt refinement failed, using original prompt:', refinementError);
        // Continue with the original prompt
      }

      // Phase 3: Generate the image directly using the imageAgent
      // Since we're in a server context, we can't use relative URLs for fetch
      console.log(`Using prompt for image generation for job: ${jobId}`);

      // Get the job document to retrieve the prompt
      const jobRef = await import('components/firebase-admin').then(
        module => module.adminDb.collection('users').doc(userId).collection('images').doc(jobId).get()
      );

      if (!jobRef.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }

      const jobData = jobRef.data();
      if (!jobData) {
        throw new Error(`No data found for job ${jobId}`);
      }

      // Use the refined prompt if available, otherwise use the original prompt
      const promptToUse = jobData.refinedPrompt || jobData.prompt;
      console.log(`Using prompt for image generation: ${promptToUse.substring(0, 100)}${promptToUse.length > 100 ? '...' : ''}`);

      // Check if we're using an Imagen model
      if (jobData.model && jobData.model.includes('imagen')) {
        console.log(`Using Google Imagen model for job: ${jobId}`);

        try {
          // Use the Imagen-specific implementation
          const imagenResult = await generateWithImagen({
            prompt: promptToUse,
            model: jobData.model as any, // Type assertion for compatibility
            numberOfImages: 1,
            userId,
            jobId
          });

          console.log(`Image generated with Imagen for job: ${jobId}`);

          // Process the image data
          if (!imagenResult.base64Image) {
            throw new Error('No image data returned from Imagen');
          }

          // Process and store the Imagen image in Firebase with the namespace from Imagen
          const processedResult = await this.processAndStoreImage(
            imagenResult.base64Image,
            userId,
            jobId,
            promptToUse,
            jobData.model as string,
            imagenResult.namespace // Pass the namespace from Imagen
          );

          console.log(`Imagen image processed and stored with namespace: ${processedResult.namespace}`);

          // Return the processed result
          return {
            imageUrl: processedResult.imageUrl,
            prompt: promptToUse,
            model: jobData.model,
            jobId,
            namespace: processedResult.namespace,
            timestamp: new Date().toISOString(),
            savedToGallery: true,
            galleryUrl: '/imageGallery',
            message: 'Image has been saved to your gallery'
          };
        } catch (imagenError: any) {
          console.error('Imagen generation failed:', imagenError);
          throw new Error(`Failed to generate image with Imagen: ${imagenError?.message || 'Unknown error'}`);
        }
      }

      // For OpenAI models, use the imageAgent
      const imageResponse = await this.imageAgent.generateImage({
        prompt: promptToUse,
        model: jobData.model,
        size: jobData.size,
        style: jobData.style,
        quality: jobData.quality,
        format: jobData.format,
        background: jobData.background,
        compression: jobData.compression
      });
      console.log(`Image generated for job: ${jobId} with model: ${jobData.model}`);

      if (!imageResponse.success || !imageResponse.base64Image) {
        throw new Error(imageResponse.error || 'Failed to generate image');
      }

      // Process the base64 image
      const base64Data = imageResponse.base64Image.replace(/^data:image\/\w+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');
      if (imageBuffer.length === 0) {
        throw new Error('Generated image buffer is empty');
      }

      // Upload to Firebase Storage
      const { adminStorage } = await import('components/firebase-admin');
      const bucket = adminStorage.bucket();
      const filePath = `users/${userId}/generated/${jobId}.png`;
      const file = bucket.file(filePath);

      await file.save(imageBuffer, {
        metadata: {
          contentType: 'image/png',
          metadata: {
            jobId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      console.log(`Image uploaded to Firebase Storage for job: ${jobId}`);

      // Generate signed URL
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      console.log(`Signed URL generated for job: ${jobId}`);

      // Update Firestore job status
      await import('components/firebase-admin').then(
        module => module.adminDb.collection('users').doc(userId).collection('images').doc(jobId).update({
          status: 'completed',
          imageUrl: downloadUrl,
          updatedAt: new Date(),
          processedAt: new Date()
        })
      );
      console.log(`Firestore job status updated for job: ${jobId}`);

      // Create record in files collection with namespace
      const namespace = uuidv4();
      await import('components/firebase-admin').then(
        module => module.adminDb.collection('users').doc(userId).collection('files').doc(namespace).set({
          category: 'My Images',
          createdAt: new Date(),
          downloadUrl: downloadUrl,
          isImage: true,
          name: promptToUse,
          namespace: namespace,
          ref: `uploads/${userId}/generated/${jobId}.png`,
          size: imageBuffer.length,
          type: 'image/png',
          jobId: jobId,
          description: jobData.originalPrompt || jobData.prompt, // Use originalPrompt as description
        })
      );
      console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);

      // Return the result in the expected format
      const result: ImageGenerationResult = {
        imageUrl: downloadUrl,
        prompt,
        model,
        jobId,
        namespace,
        timestamp: new Date().toISOString(),
        savedToGallery: true,
        galleryUrl: '/imageGallery',
        message: 'Image has been saved to your gallery'
      };

      return result;
    } catch (error: any) {
      console.error('Firebase image generation error:', error);
      throw new Error(`Failed to generate image with Firebase: ${error.message}`);
    }
  }

  /**
   * Generate an image directly without Firebase
   * @param options - Image generation options
   * @returns - The generated image result
   */
  private async generateImageDirect(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      refinePrompt = true,
      model = this.defaultModel,
      size = this.defaultSize,
      style = 'vivid',
      quality = 'auto',
      format = 'jpeg',
      background = 'auto',
      compression
    } = options;

    try {
      // Generate a unique job ID for this request
      const jobId = this._generateJobId();

      // Refine the prompt if requested
      let finalPrompt = prompt;
      if (refinePrompt && this.componentImageTool) {
        try {
          finalPrompt = await this.componentImageTool.refinePrompt(prompt);
          console.log('Prompt refined successfully');
        } catch (error) {
          console.warn('Prompt refinement failed, using original prompt:', error);
          // Continue with the original prompt
        }
      } else if (refinePrompt) {
        finalPrompt = await this._refinePrompt(prompt);
      }

      // Generate the image based on the model
      let imageData;

      if (model.includes('imagen')) {
        // Use Google's Imagen model
        console.log('Using Google Imagen model for image generation');
        const imagenResult = await generateWithImagen({
          prompt: finalPrompt,
          model: 'imagen-3.0-generate-002',
          numberOfImages: 1,
          userId: options.userId, // Pass userId if available
          jobId // Pass the jobId
        });

        imageData = {
          url: imagenResult.imageUrl,
          revised_prompt: finalPrompt,
          namespace: imagenResult.namespace // Store the namespace from Imagen result
        };
      } else if (this.componentImageTool && (model === 'gpt-image-1' || model === 'dall-e-3')) {
        // Use the component image tool for gpt-image-1 or dall-e-3
        console.log(`Using ${model} model via component tool`);
        const result = await this.componentImageTool.generateImageBase64(
          finalPrompt,
          model as any,
          size as any,
          quality as any,
          format as any,
          background as any,
          compression
        );

        if (!result.success || !result.base64Image) {
          throw new Error(result.error || 'Failed to generate image');
        }

        // Convert base64 to a data URL if it's not already in that format
        const base64Data = result.base64Image.startsWith('data:image/')
          ? result.base64Image
          : `data:image/png;base64,${result.base64Image}`;

        imageData = {
          url: base64Data,
          revised_prompt: finalPrompt
        };
      } else {
        // Use OpenAI's DALL-E model directly
        console.log('Using OpenAI DALL-E model for image generation');
        imageData = await this._generateImageWithOpenAI(
          finalPrompt,
          model,
          size,
          style,
          quality,
          format,
          background,
          compression
        );
      }

      // Use the namespace from Imagen result if available, otherwise generate a new UUID
      const namespace = imageData.namespace || uuidv4();
      console.log(`Using namespace for image: ${namespace}`);

      // Return the result in the expected format
      const result: ImageGenerationResult = {
        imageUrl: imageData.url,
        prompt: finalPrompt,
        model,
        jobId,
        namespace,
        timestamp: new Date().toISOString(),
        savedToGallery: false, // Direct generation doesn't save to gallery automatically
        message: 'Image generated successfully. Click "Save to Gallery" to save it to your gallery.'
      };

      return result;
    } catch (error: any) {
      console.error('Image generation error:', error);
      throw new Error(`Failed to generate image: ${error.message}`);
    }
  }

  /**
   * Generate an image using OpenAI's DALL-E API
   * @private
   * @param prompt - The prompt to generate an image from
   * @param model - The model to use
   * @param size - The size of the image
   * @param style - The style of the image (DALL-E 3 only)
   * @param quality - The quality of the image (DALL-E 3 only)
   * @returns - The generated image data
   */
  private async _generateImageWithOpenAI(
    prompt: string,
    model: ImageModel,
    size: ImageSize,
    style: ImageStyle,
    quality: ImageQuality,
    format: ImageFormat = 'png',
    background: ImageBackground = 'auto',
    compression?: ImageCompression
  ): Promise<OpenAIImageResponse> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    try {
      // Use the OpenAI client if available
      if (this.componentImageTool) {
        const openai = new OpenAI({ apiKey: this.apiKey });

        const requestBody: any = {
          model,
          prompt,
          n: 1,
          size
        };

        // Add output_format for gpt-image-1
        if (model === 'gpt-image-1') {
          requestBody.output_format = format; // Set the output format (png, jpeg, webp)
        } else {
          // Add response_format only for models that support it (dall-e-2, dall-e-3)
          requestBody.response_format = 'b64_json';
        }

        // Add model-specific parameters
        if (model === 'dall-e-3') {
          requestBody.quality = quality;
          requestBody.style = style;
        }

        // Add gpt-image-1 specific parameters
        if (model === 'gpt-image-1') {
          requestBody.quality = quality;
          requestBody.background = background;

          // Add output_compression if specified (for jpeg and webp)
          if (compression !== undefined && (format === 'jpeg' || format === 'webp')) {
            requestBody.output_compression = compression;
          }
        }

        console.log(`Generating image with OpenAI ${model} model`);
        const response = await openai.images.generate(requestBody);

        // Check if we have base64 data
        if (response.data[0].b64_json) {
          return {
            url: `data:image/png;base64,${response.data[0].b64_json}`,
            revised_prompt: response.data[0].revised_prompt
          };
        }
        // Fall back to URL if no base64 data
        else if (response.data[0].url) {
          return {
            url: response.data[0].url,
            revised_prompt: response.data[0].revised_prompt
          };
        } else {
          throw new Error('No image data returned from OpenAI');
        }
      } else {
        // Fall back to fetch API
        const requestBody: Record<string, any> = {
          model,
          prompt,
          n: 1,
          size
        };

        // Add output_format for gpt-image-1
        if (model === 'gpt-image-1') {
          requestBody.output_format = format; // Set the output format (png, jpeg, webp)
        } else {
          // Add response_format only for models that support it (dall-e-2, dall-e-3)
          requestBody.response_format = 'b64_json';
        }

        // Add model-specific parameters
        if (model === 'dall-e-3') {
          requestBody.quality = quality;
          requestBody.style = style;
        }

        // Add gpt-image-1 specific parameters
        if (model === 'gpt-image-1') {
          requestBody.quality = quality;
          requestBody.background = background;

          // Add output_compression if specified (for jpeg and webp)
          if (compression !== undefined && (format === 'jpeg' || format === 'webp')) {
            requestBody.output_compression = compression;
          }
        }

        console.log(`Generating image with OpenAI ${model} model via fetch API`);
        const response = await fetch(`${this.baseUrl}/images/generations`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
        }

        const data = await response.json();

        // Check if we have base64 data
        if (data.data[0].b64_json) {
          return {
            url: `data:image/png;base64,${data.data[0].b64_json}`,
            revised_prompt: data.data[0].revised_prompt
          };
        }
        // Fall back to URL if no base64 data
        else if (data.data[0].url) {
          return {
            url: data.data[0].url,
            revised_prompt: data.data[0].revised_prompt
          };
        } else {
          throw new Error('No image data returned from OpenAI');
        }
      }
    } catch (error) {
      console.error('OpenAI image generation error:', error);
      throw error;
    }
  }

  /**
   * Refine a prompt using an LLM to make it more detailed and suitable for image generation
   * @private
   * @param prompt - The original prompt
   * @returns - The refined prompt
   */
  private async _refinePrompt(prompt: string): Promise<string> {
    if (!this.apiKey) {
      return prompt;
    }

    try {
      const openai = new OpenAI({ apiKey: this.apiKey });

      const chatCompletion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at creating detailed, vivid prompts for image generation. Enhance the user\'s prompt to include more details about style, lighting, composition, and mood. Keep the core idea intact but make it more descriptive for better image generation results.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300
      });

      return chatCompletion.choices[0]?.message?.content || prompt;
    } catch (error) {
      console.warn('Prompt refinement failed, using original prompt:', error);
      return prompt;
    }
  }

  /**
   * Process and store a generated image
   * @param base64Image - The base64-encoded image data
   * @param userId - The user ID who generated the image
   * @param jobId - The job ID for the image generation
   * @param prompt - The prompt used to generate the image
   * @param model - The model used to generate the image
   * @param namespace - Optional namespace to use for the image (UUIDv4)
   * @returns - The result of the image processing
   */
  async processAndStoreImage(
    base64Image: string,
    userId: string,
    jobId: string,
    prompt: string,
    model: string = 'gpt-image-1',  // Default to gpt-image-1 but allow override
    namespace?: string  // Optional namespace parameter
  ): Promise<Record<string, any>> {
    try {
      // Handle potential data URI prefix
      const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');

      // Convert to buffer
      const imageBuffer = Buffer.from(base64Data, 'base64');
      if (imageBuffer.length === 0) {
        throw new Error('Generated image buffer is empty');
      }

      // Upload to Firebase Storage directly
      const { adminStorage, adminDb } = await import('components/firebase-admin');
      const bucket = adminStorage.bucket();
      const filePath = `users/${userId}/generated/${jobId}.png`;
      const file = bucket.file(filePath);

      await file.save(imageBuffer, {
        metadata: {
          contentType: 'image/png',
          metadata: {
            jobId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      console.log(`Image uploaded to Firebase Storage for job: ${jobId}`);

      // Generate signed URL
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      console.log(`Signed URL generated for job: ${jobId}`);

      // Update Firestore job status if it exists
      try {
        const jobRef = adminDb.collection('users').doc(userId).collection('images').doc(jobId);
        const jobDoc = await jobRef.get();

        if (jobDoc.exists) {
          await jobRef.update({
            status: 'completed',
            imageUrl: downloadUrl,
            updatedAt: new Date(),
            processedAt: new Date()
          });
          console.log(`Firestore job status updated for job: ${jobId}`);
        }
      } catch (updateError) {
        console.warn(`Could not update job status: ${updateError}`);
        // Continue even if job update fails
      }

      // Create record in files collection with namespace
      // Use the provided namespace if available, otherwise generate a new UUID
      const fileNamespace = namespace || uuidv4();
      console.log(`Using namespace for image in Firebase: ${fileNamespace}`);
      try {
        await adminDb.collection('users').doc(userId).collection('files').doc(fileNamespace).set({
          category: 'My Images',
          createdAt: new Date(),
          downloadUrl: downloadUrl,
          isImage: true,
          name: prompt.substring(0, 100), // Limit name length to avoid Firestore size issues
          namespace: fileNamespace,
          ref: `uploads/${userId}/generated/${jobId}.png`,
          size: imageBuffer.length,
          type: 'image/png',
          jobId: jobId,
          description: prompt.substring(0, 1000), // Limit description length (using original prompt)
          model: model
        });
        console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);
      } catch (error) {
        console.error(`Error saving to files collection: ${error}`);
        // Continue even if saving to files collection fails
        // We'll still return the image URL
      }

      return {
        imageUrl: downloadUrl,
        jobId,
        userId,
        prompt,
        namespace: fileNamespace,
        timestamp: new Date().toISOString(),
        savedToGallery: true,
        galleryUrl: '/imageGallery',
        message: 'Image has been saved to your gallery'
      };
    } catch (error: any) {
      console.error('Image processing error:', error);
      throw new Error(`Failed to process image: ${error.message}`);
    }
  }

  /**
   * Generate a unique job ID for an image generation request
   * @private
   * @returns - A unique job ID
   */
  private _generateJobId(): string {
    return `img_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof GenerateImageTool.description {
    return GenerateImageTool.description;
  }

  /**
   * Get all available tool methods with their descriptions
   * @returns Map of method names to their descriptions
   */
  getAvailableMethods(): Record<string, string> {
    return {
      generateImage: "Generate an image from a text prompt and store it in Firebase"
    };
  }
}

// Export a singleton instance
export const generateImageTool = new GenerateImageTool();