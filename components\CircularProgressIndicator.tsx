import React from 'react';

interface CircularProgressIndicatorProps {
  progress: number;
  label?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

const CircularProgressIndicator: React.FC<CircularProgressIndicatorProps> = ({
  progress,
  label,
  size = 'md',
  color = 'purple'
}) => {
  // Calculate the size based on the prop
  const sizeMap = {
    sm: 'w-32 h-32',
    md: 'w-48 h-48',
    lg: 'w-64 h-64'
  };

  // Calculate the font size based on the size prop
  const fontSizeMap = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  };

  // Calculate the label font size based on the size prop
  const labelFontSizeMap = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  // Calculate the color based on the prop
  const colorMap: Record<string, string> = {
    purple: 'text-purple-500',
    blue: 'text-blue-500',
    green: 'text-green-500',
    red: 'text-red-500'
  };

  const textColor = colorMap[color] || 'text-purple-500';

  return (
    <div className={`relative ${sizeMap[size]}`}>
      {/* Outer circle */}
      <div className="absolute inset-0 rounded-full border-4 border-zinc-700 opacity-25"></div>

      {/* Progress circle */}
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        <circle
          className="text-zinc-700"
          strokeWidth="4"
          stroke="currentColor"
          fill="transparent"
          r="48"
          cx="50"
          cy="50"
        />
        <circle
          className={`${textColor} transition-all duration-300 ease-in-out`}
          strokeWidth="4"
          strokeLinecap="round"
          stroke="currentColor"
          fill="transparent"
          r="48"
          cx="50"
          cy="50"
          strokeDasharray={`${progress * 3.02}, 302`}
          transform="rotate(-90 50 50)"
        />
      </svg>

      {/* Center content */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <p className={`${fontSizeMap[size]} font-bold text-white`}>{Math.round(progress)}%</p>
        {label && <p className={`${labelFontSizeMap[size]} ${textColor}`}>{label}</p>}
      </div>
    </div>
  );
};

export default CircularProgressIndicator;
