import { ReactElement, ReactNode } from 'react';
import { NextPage } from 'next';

export interface ChatData {
    category: string;
    fileName: string;
    id: string;
    text: string;
    userId: string;
    role: 'user' | 'ai'; // Explicitly define possible roles
    createdAt: {
      seconds: number;
      nanoseconds: number;
    };
    fileDocumentId?: string | null;
    stopped?: boolean // Add this line
    visualization: string | ''
  }

  
export interface EditedImageResponse {
  editedImageUrl: string; // URL of the stored edited image (after saving to your gallery)
  originalImageUrls?: string[]; // URLs of source images if needed
  prompt: string;
  model: string; // e.g., 'gpt-image-1'
  jobId: string; // A unique ID for this edit job
  namespace: string; // e.g., 'openai-edit'
  timestamp: string;
  savedToGallery: boolean;
  galleryNamespace?: string; // Namespace in your gallery
  base64ImageData?: string; // Temporary, for display before saving
}

export interface GeneratedVideoResponse {
  videoUrl: string; // URL of the stored video (after saving to your gallery)
  thumbnailUrl?: string; // Optional thumbnail
  prompt: string;
  sourceImageUrl?: string; // If image-to-video
  model: string; // e.g., 'veo-2.0-generate-001'
  operationId: string; // Veo operation ID
  jobId: string; // A unique ID for this video job
  namespace: string; // e.g., 'google-veo'
  timestamp: string;
  savedToGallery: boolean;
  galleryNamespace?: string;
  duration?: number;
  aspectRatio?: string;
  tempVideoUri?: string; // Temporary Veo URI
}

// For saving to gallery
export interface SaveEditedImagePayload {
  base64ImageData: string; // The b64_json from OpenAI
  prompt: string;
  model: string;
  originalImageCount: number; // Or some identifier for source
  // Potentially source image identifiers if you store them too
}

export interface SaveVideoPayload {
  veoVideoUri: string; // The temporary URI from Google Veo
  prompt: string;
  model: string;
  sourceImageUrl?: string;
  duration?: number;
  aspectRatio?: string;
}

  



export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
};
