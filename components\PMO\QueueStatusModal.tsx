"use client";

import React from 'react';
import { Button } from '../ui/button';
import { 
  X, 
  <PERSON>, 
  CheckCircle2, 
  XCircle, 
  Loader2,
  FileText,
  Upload
} from 'lucide-react';

interface DocumentProcessingResult {
  documentId: string;
  fileName: string;
  success: boolean;
  wasExisting?: boolean;
  ragIndexed?: boolean;
  agentUpdated?: boolean;
  error?: string;
}

interface QueueStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  queuedDocuments: string[];
  processingResults?: DocumentProcessingResult[];
  isProcessing: boolean;
  currentDocument?: string;
}

export default function QueueStatusModal({
  isOpen,
  onClose,
  queuedDocuments,
  processingResults = [],
  isProcessing,
  currentDocument
}: QueueStatusModalProps) {
  if (!isOpen) return null;

  const getDocumentStatus = (documentId: string) => {
    const result = processingResults.find(r => r.documentId === documentId);
    if (result) {
      return result.success ? 'completed' : 'error';
    }
    if (isProcessing && currentDocument === documentId) {
      return 'processing';
    }
    if (isProcessing) {
      const processedIds = processingResults.map(r => r.documentId);
      const currentIndex = queuedDocuments.indexOf(currentDocument || '');
      const documentIndex = queuedDocuments.indexOf(documentId);
      
      if (documentIndex < currentIndex || processedIds.includes(documentId)) {
        return 'completed';
      }
    }
    return 'queued';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-400" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-400" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-blue-400 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string, result?: DocumentProcessingResult) => {
    switch (status) {
      case 'completed':
        if (result?.wasExisting) {
          return 'Already uploaded';
        }
        return 'Upload completed';
      case 'error':
        return result?.error || 'Upload failed';
      case 'processing':
        return 'Uploading...';
      default:
        return 'Queued';
    }
  };

  const completedCount = processingResults.filter(r => r.success).length;
  const errorCount = processingResults.filter(r => !r.success).length;
  const totalCount = queuedDocuments.length;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-lg border border-gray-700 w-full max-w-md mx-4 max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <Upload className="h-5 w-5 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">
              Document Upload Status
            </h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
            disabled={isProcessing}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Progress Summary */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Progress</span>
            <span className="text-sm text-gray-300">
              {completedCount + errorCount} / {totalCount}
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${totalCount > 0 ? ((completedCount + errorCount) / totalCount) * 100 : 0}%` 
              }}
            />
          </div>
          {isProcessing && (
            <div className="flex items-center space-x-2 mt-2">
              <Loader2 className="h-4 w-4 text-purple-400 animate-spin" />
              <span className="text-sm text-purple-300">
                Processing documents...
              </span>
            </div>
          )}
        </div>

        {/* Document List */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {queuedDocuments.map((documentId) => {
            const status = getDocumentStatus(documentId);
            const result = processingResults.find(r => r.documentId === documentId);
            
            return (
              <div
                key={documentId}
                className={`
                  flex items-center space-x-3 p-3 rounded-lg border transition-all
                  ${status === 'completed' 
                    ? 'border-green-500/30 bg-green-500/10' 
                    : status === 'error'
                    ? 'border-red-500/30 bg-red-500/10'
                    : status === 'processing'
                    ? 'border-blue-500/30 bg-blue-500/10'
                    : 'border-gray-600 bg-gray-700/30'
                  }
                `}
              >
                <FileText className="h-4 w-4 text-gray-400 flex-shrink-0" />
                
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {result?.fileName || `Document ${documentId.slice(0, 8)}...`}
                  </p>
                  <p className={`text-xs ${
                    status === 'error' ? 'text-red-300' : 'text-gray-400'
                  }`}>
                    {getStatusText(status, result)}
                  </p>
                </div>
                
                <div className="flex-shrink-0">
                  {getStatusIcon(status)}
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-700">
          {isProcessing ? (
            <div className="text-center">
              <p className="text-sm text-gray-300 mb-2">
                Please wait while documents are being uploaded...
              </p>
              <p className="text-xs text-gray-400">
                This process may take a few moments depending on document size
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-green-300">Successful: {completedCount}</span>
                {errorCount > 0 && (
                  <span className="text-red-300">Failed: {errorCount}</span>
                )}
              </div>
              <Button
                onClick={onClose}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white"
              >
                {errorCount > 0 ? 'Close (Some uploads failed)' : 'Close'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
