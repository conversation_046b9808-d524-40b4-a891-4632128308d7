/**
 * Prompt Agent Test
 *
 * This file demonstrates how to use the PromptAgent.
 * You can run this test with:
 *
 * ```
 * npx ts-node lib/agents/prompt/tests/prompt-agent.test.ts
 * ```
 */

import { promptAgent, PromptAgent } from '../PromptAgent';

async function testPromptAgent() {
  console.log('=== Testing Prompt Agent ===\n');

  // Sample prompts to optimize
  const prompts = [
    "Write a blog post about artificial intelligence",
    "Create a marketing email for a new fitness app",
    "Explain quantum computing to a high school student"
  ];

  // Create a second agent instance with explanations enabled
  const promptAgentWithExplanations = new PromptAgent({
    includeExplanation: true
  });

  // Test each prompt with both agents
  for (const [index, prompt] of prompts.entries()) {
    // Test with default agent (no explanation)
    console.log(`\n--- Test ${index + 1}A: "${prompt}" (Without Explanation) ---\n`);

    try {
      // Optimize the prompt using the default PromptAgent (no explanation)
      console.log('Optimizing prompt without explanation...');
      const result = await promptAgent.optimizePrompt(prompt);

      if (!result.success) {
        throw new Error(`Failed to optimize prompt: ${result.error}`);
      }

      // Display the results
      console.log('Original Prompt:');
      console.log(`"${result.originalPrompt}"\n`);

      console.log('Generated Criteria:');
      result.criteria.forEach((criterion, i) => {
        console.log(`${i + 1}. ${criterion}`);
      });
      console.log();

      console.log('Optimized Prompt:');
      console.log(`"${result.optimizedPrompt}"\n`);

      // Should not have an explanation
      if (result.explanation) {
        console.log('Note: Explanation was included despite being disabled');
      } else {
        console.log('Note: No explanation was included as expected');
      }

      console.log(`--- Test ${index + 1}A Completed Successfully ---`);
    } catch (error) {
      console.error(`Test ${index + 1}A failed:`, error);
    }

    // Test with explanation agent
    console.log(`\n--- Test ${index + 1}B: "${prompt}" (With Explanation) ---\n`);

    try {
      // Optimize the prompt using the PromptAgent with explanations
      console.log('Optimizing prompt with explanation...');
      const result = await promptAgentWithExplanations.optimizePrompt(prompt);

      if (!result.success) {
        throw new Error(`Failed to optimize prompt: ${result.error}`);
      }

      // Display the results
      console.log('Original Prompt:');
      console.log(`"${result.originalPrompt}"\n`);

      console.log('Generated Criteria:');
      result.criteria.forEach((criterion, i) => {
        console.log(`${i + 1}. ${criterion}`);
      });
      console.log();

      console.log('Optimized Prompt:');
      console.log(`"${result.optimizedPrompt}"\n`);

      // Should have an explanation
      if (result.explanation) {
        console.log('Explanation of Changes:');
        console.log(result.explanation);
        console.log();
      } else {
        console.log('Note: No explanation was included despite being enabled');
      }

      console.log(`--- Test ${index + 1}B Completed Successfully ---`);
    } catch (error) {
      console.error(`Test ${index + 1}B failed:`, error);
    }
  }

  console.log('\n=== All Tests Completed ===');
}

// Run the test
testPromptAgent();
