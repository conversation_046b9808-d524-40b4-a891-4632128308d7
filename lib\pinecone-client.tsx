import { Pinecone } from '@pinecone-database/pinecone'; // Import Pinecone class

// Initialize Pinecone client directly
const pineconeClient = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || '', // Set your API key from environment variables
});

// Get the Pinecone index with the fixed name 'ikedia'
const getPineconeIndex = () => {
  // Always use 'ikedia' as the index name
  const indexName = 'ikedia';
  return pineconeClient.index(indexName);
};

// Export the client and helper function to be used in other parts of your application
export { pineconeClient, getPineconeIndex };
