/**
 * Test script for PMO-Marketing Integration
 * Run this to verify the integration between PMO and Marketing workflows
 */

const testPMOMarketingIntegration = async () => {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing PMO-Marketing Integration...\n');

  // Test data simulating a PMO notification
  const testPMOData = {
    pmoId: 'test-pmo-123',
    teamId: 'marketing-team',
    teamName: 'Marketing',
    projectTitle: 'Scene Mate Viral Campaign Test',
    projectDescription: 'Develop a comprehensive viral video ad campaign concept for Scene Mate app targeting aspiring actors aged 18-35.',
    pmoAssessment: 'The Marketing Team is best suited for this task as it involves marketing strategy, content creation, brand management, and market analysis.',
    teamSelectionRationale: 'Marketing strategy, content creation, brand management, and market analysis expertise required.',
    priority: 'High',
    category: 'PMO - Scene Mate Viral Campaign Test - test-pmo-123',
    userId: '<EMAIL>',
    metadata: {
      notifiedAt: new Date().toISOString(),
      recordTitle: 'Scene Mate Viral Campaign Test',
      recordDescription: 'Test project for PMO-Marketing integration',
      recordStatus: 'Draft',
      recordPriority: 'High',
      assignedTeam: 'Marketing',
      assignedTeamId: 'marketing-team',
      requiresStrategicPlan: true
    }
  };

  try {
    // Step 1: Test PMO Notify Team API (should auto-trigger marketing collaboration)
    console.log('📤 Step 1: Testing PMO Notify Team API...');
    const notifyResponse = await fetch(`${baseUrl}/api/pmo-notify-team`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPMOData)
    });

    if (!notifyResponse.ok) {
      throw new Error(`PMO Notify Team API failed: ${notifyResponse.status} ${notifyResponse.statusText}`);
    }

    const notifyResult = await notifyResponse.json();
    console.log('✅ PMO notification created successfully');
    console.log(`   Notification ID: ${notifyResult.notificationId}`);
    
    if (notifyResult.marketingCollaboration) {
      console.log('✅ Marketing collaboration auto-triggered');
      console.log(`   Request ID: ${notifyResult.marketingCollaboration.requestId}`);
    } else {
      console.log('⚠️  Marketing collaboration not auto-triggered (may need manual trigger)');
    }

    // Step 2: Test direct marketing collaboration trigger
    console.log('\n📤 Step 2: Testing direct marketing collaboration...');
    const marketingPrompt = `
# PMO Marketing Requirements Analysis

## Project Overview
**Title:** ${testPMOData.projectTitle}
**Priority:** ${testPMOData.priority}
**PMO ID:** ${testPMOData.pmoId}

## Project Description
${testPMOData.projectDescription}

## PMO Assessment
${testPMOData.pmoAssessment}

## Team Selection Rationale
${testPMOData.teamSelectionRationale}

## Marketing Team Objectives
Based on the PMO requirements above, please provide a comprehensive marketing strategy analysis that includes:

1. **Strategic Marketing Assessment** - Analyze the marketing implications and opportunities
2. **Target Audience Analysis** - Identify and profile the target market segments
3. **Competitive Landscape** - Research and analyze competitive positioning
4. **Marketing Channel Strategy** - Recommend optimal marketing channels and tactics
5. **Content Strategy** - Develop content themes and messaging framework
6. **Campaign Planning** - Outline campaign structure and timeline
7. **Success Metrics** - Define KPIs and measurement framework
8. **Resource Requirements** - Estimate budget and resource needs

Please ensure your analysis is comprehensive and actionable, providing specific recommendations that the marketing team can implement.
    `.trim();

    const marketingCollaborationData = {
      prompt: marketingPrompt,
      modelProvider: 'openai',
      modelName: 'gpt-4o',
      userId: testPMOData.userId,
      context: `PMO Context: Project: ${testPMOData.projectTitle}, Assessment: ${testPMOData.pmoAssessment}, Priority: ${testPMOData.priority}`,
      category: testPMOData.category,
      metadata: {
        source: 'PMO',
        pmoId: testPMOData.pmoId,
        notificationId: notifyResult.notificationId,
        autoTriggered: false,
        triggerTimestamp: new Date().toISOString()
      }
    };

    const collaborationResponse = await fetch(`${baseUrl}/api/marketing-agent-collaboration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(marketingCollaborationData)
    });

    if (!collaborationResponse.ok) {
      throw new Error(`Marketing collaboration API failed: ${collaborationResponse.status} ${collaborationResponse.statusText}`);
    }

    const collaborationResult = await collaborationResponse.json();
    console.log('✅ Marketing collaboration completed successfully');
    console.log(`   Request ID: ${collaborationResult.requestId}`);
    console.log(`   Conversation messages: ${collaborationResult.conversation?.length || 0}`);

    // Step 3: Test PMO-Marketing Integration API
    console.log('\n📤 Step 3: Testing PMO-Marketing Integration API...');
    
    // Test manual trigger
    const integrationResponse = await fetch(`${baseUrl}/api/pmo-marketing-integration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        notificationId: notifyResult.notificationId,
        action: 'trigger-marketing-collaboration'
      })
    });

    if (integrationResponse.ok) {
      const integrationResult = await integrationResponse.json();
      console.log('✅ Integration API trigger test successful');
      console.log(`   Request ID: ${integrationResult.requestId}`);
    } else {
      console.log('⚠️  Integration API trigger test failed (may be already triggered)');
    }

    // Test get notifications
    const getNotificationsResponse = await fetch(`${baseUrl}/api/pmo-marketing-integration`);
    if (getNotificationsResponse.ok) {
      const notificationsResult = await getNotificationsResponse.json();
      console.log('✅ Get notifications test successful');
      console.log(`   Found ${notificationsResult.count} notifications`);
    } else {
      console.log('❌ Get notifications test failed');
    }

    console.log('\n🎉 PMO-Marketing Integration Test Complete!');
    console.log('\nTest Results Summary:');
    console.log('✅ PMO notification creation');
    console.log('✅ Marketing collaboration workflow');
    console.log('✅ Data transformation and context preservation');
    console.log('✅ API integration endpoints');
    
    return {
      success: true,
      notificationId: notifyResult.notificationId,
      collaborationRequestId: collaborationResult.requestId
    };

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\nError details:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Export for use in other contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testPMOMarketingIntegration };
}

// Run test if this file is executed directly
if (typeof window === 'undefined' && require.main === module) {
  testPMOMarketingIntegration()
    .then(result => {
      console.log('\n📊 Final Result:', result);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}

// For browser environments
if (typeof window !== 'undefined') {
  window.testPMOMarketingIntegration = testPMOMarketingIntegration;
}
