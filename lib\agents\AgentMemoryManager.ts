/**
 * Agent Memory Manager
 *
 * Provides persistent storage and retrieval of agent memory and messages
 * using Firebase Firestore.
 */

import { collection, doc, setDoc, getDoc, getDocs, query, where, orderBy, limit as firestoreLimit, addDoc, serverTimestamp, Timestamp, DocumentData } from 'firebase/firestore';
import { db } from '../../components/firebase/config';
import { AgentMemory, AgentMessage, AgentTask } from './marketing/MarketingAgent';

export interface MemoryRecord {
  id: string;
  agentId: string;
  type: 'Agent_Response';
  data: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface MessageRecord {
  id: string;
  from: string;
  to: string;
  content: string;
  timestamp: Date;
  read: boolean;
  metadata?: Record<string, any>;
}

export interface TaskRecord {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  assignedTo: string;
  createdBy: string;
  createdAt: Date;
  dueDate?: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Manages persistent storage and retrieval of agent memory and messages
 */
export class AgentMemoryManager {
  private userId: string;

  constructor(userId: string) {
    this.userId = userId;
  }

  /**
   * Save agent memory to Firestore
   */
  async saveMemory(agentId: string, memory: AgentMemory): Promise<void> {
    try {
      // Save agent responses
      await this.saveMemoryType(agentId, 'Agent_Response', memory.Agent_Response);
    } catch (error) {
      console.error(`Error saving memory for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Save agent responses to memory
   */
  private async saveMemoryType(agentId: string, type: 'Agent_Response', data: Record<string, any>): Promise<void> {
    try {
      const memoryRef = doc(db, `users/${this.userId}/agents/${agentId}/memory/${type}`);

      // Sanitize data to remove undefined values which Firebase doesn't support
      const sanitizedData = this.sanitizeDataForFirestore(data);

      await setDoc(memoryRef, {
        agentId,
        type,
        data: sanitizedData,
        updatedAt: serverTimestamp()
      }, { merge: true });
    } catch (error) {
      console.error(`Error saving ${type} memory for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Sanitize data for Firestore by removing undefined values
   * Firebase doesn't support undefined values, so we need to remove them
   */
  private sanitizeDataForFirestore(data: any): any {
    if (data === null || data === undefined) {
      return null; // Convert undefined to null which Firestore supports
    }

    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeDataForFirestore(item));
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized: Record<string, any> = {};

      for (const [key, value] of Object.entries(data)) {
        if (value !== undefined) {
          sanitized[key] = this.sanitizeDataForFirestore(value);
        }
      }

      return sanitized;
    }

    return data;
  }

  /**
   * Load agent memory from Firestore
   */
  async loadMemory(agentId: string): Promise<AgentMemory> {
    try {
      // Initialize empty memory
      const memory: AgentMemory = {
        Agent_Response: {}
      };

      // Load agent responses
      const agentResponseRef = doc(db, `users/${this.userId}/agents/${agentId}/memory/Agent_Response`);
      const agentResponseDoc = await getDoc(agentResponseRef);

      if (agentResponseDoc.exists()) {
        memory.Agent_Response = agentResponseDoc.data().data || {};
      }

      return memory;
    } catch (error) {
      console.error(`Error loading memory for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Send a message from one agent to another
   */
  async sendMessage(fromAgentId: string, toAgentId: string, content: string, metadata?: Record<string, any>): Promise<string> {
    try {
      const messagesRef = collection(db, `users/${this.userId}/agent_messages`);

      const messageData = {
        from: fromAgentId,
        to: toAgentId,
        content,
        timestamp: serverTimestamp(),
        read: false,
        metadata: metadata || {}
      };

      const docRef = await addDoc(messagesRef, messageData);

      console.log(`Message sent from ${fromAgentId} to ${toAgentId}: ${content}`);

      return docRef.id;
    } catch (error) {
      console.error(`Error sending message from ${fromAgentId} to ${toAgentId}:`, error);
      throw error;
    }
  }

  /**
   * Get unread messages for an agent
   */
  async getUnreadMessages(agentId: string): Promise<AgentMessage[]> {
    try {
      const messagesRef = collection(db, `users/${this.userId}/agent_messages`);
      const q = query(
        messagesRef,
        where('to', '==', agentId),
        where('read', '==', false),
        orderBy('timestamp', 'asc')
      );

      const querySnapshot = await getDocs(q);

      const messages: AgentMessage[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        messages.push({
          from: data.from,
          to: data.to,
          content: data.content,
          timestamp: (data.timestamp as Timestamp).toDate(),
          metadata: data.metadata
        });

        // Mark message as read
        setDoc(doc.ref, { read: true }, { merge: true });
      });

      return messages;
    } catch (error) {
      console.error(`Error getting unread messages for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Get all messages for an agent
   */
  async getAllMessages(agentId: string, maxLimit = 50): Promise<AgentMessage[]> {
    try {
      const messagesRef = collection(db, `users/${this.userId}/agent_messages`);
      const q = query(
        messagesRef,
        where('to', '==', agentId),
        orderBy('timestamp', 'desc'),
        firestoreLimit(maxLimit)
      );

      const querySnapshot = await getDocs(q);

      const messages: AgentMessage[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        messages.push({
          from: data.from,
          to: data.to,
          content: data.content,
          timestamp: (data.timestamp as Timestamp).toDate(),
          metadata: data.metadata
        });
      });

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error(`Error getting all messages for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Create a task
   */
  async createTask(
    title: string,
    description: string,
    priority: 'low' | 'medium' | 'high',
    assignedTo: string,
    createdBy: string,
    dueDate?: Date,
    metadata?: Record<string, any>
  ): Promise<string> {
    try {
      const tasksRef = collection(db, `users/${this.userId}/agent_tasks`);

      const taskData = {
        title,
        description,
        status: 'pending',
        priority,
        assignedTo,
        createdBy,
        createdAt: serverTimestamp(),
        dueDate: dueDate ? Timestamp.fromDate(dueDate) : null,
        completedAt: null,
        metadata: metadata || {}
      };

      const docRef = await addDoc(tasksRef, taskData);

      // Notify the assigned agent
      await this.sendMessage(
        createdBy,
        assignedTo,
        `New task assigned: ${title}`,
        { taskId: docRef.id, type: 'task_assignment' }
      );

      return docRef.id;
    } catch (error) {
      console.error(`Error creating task:`, error);
      throw error;
    }
  }

  /**
   * Update a task's status
   */
  async updateTaskStatus(taskId: string, status: 'pending' | 'in-progress' | 'completed' | 'failed'): Promise<void> {
    try {
      const taskRef = doc(db, `users/${this.userId}/agent_tasks/${taskId}`);
      const taskDoc = await getDoc(taskRef);

      if (!taskDoc.exists()) {
        throw new Error(`Task with ID ${taskId} not found`);
      }

      const taskData = taskDoc.data();

      const updateData: Record<string, any> = {
        status,
        updatedAt: serverTimestamp()
      };

      if (status === 'completed') {
        updateData.completedAt = serverTimestamp();
      }

      await setDoc(taskRef, updateData, { merge: true });

      // Notify the task creator
      await this.sendMessage(
        taskData.assignedTo,
        taskData.createdBy,
        `Task "${taskData.title}" status updated to: ${status}`,
        { taskId, type: 'task_update' }
      );
    } catch (error) {
      console.error(`Error updating task status:`, error);
      throw error;
    }
  }

  /**
   * Get tasks assigned to an agent
   */
  async getAssignedTasks(agentId: string): Promise<AgentTask[]> {
    try {
      const tasksRef = collection(db, `users/${this.userId}/agent_tasks`);
      const q = query(
        tasksRef,
        where('assignedTo', '==', agentId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);

      const tasks: AgentTask[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        tasks.push({
          id: doc.id,
          title: data.title,
          description: data.description,
          status: data.status,
          priority: data.priority,
          assignedTo: data.assignedTo,
          createdBy: data.createdBy,
          createdAt: (data.createdAt as Timestamp).toDate(),
          dueDate: data.dueDate ? (data.dueDate as Timestamp).toDate() : undefined,
          completedAt: data.completedAt ? (data.completedAt as Timestamp).toDate() : undefined,
          metadata: data.metadata
        });
      });

      return tasks;
    } catch (error) {
      console.error(`Error getting assigned tasks for agent ${agentId}:`, error);
      throw error;
    }
  }

  /**
   * Get tasks created by an agent
   */
  async getCreatedTasks(agentId: string): Promise<AgentTask[]> {
    try {
      const tasksRef = collection(db, `users/${this.userId}/agent_tasks`);
      const q = query(
        tasksRef,
        where('createdBy', '==', agentId),
        orderBy('createdAt', 'desc')
      );

      const querySnapshot = await getDocs(q);

      const tasks: AgentTask[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        tasks.push({
          id: doc.id,
          title: data.title,
          description: data.description,
          status: data.status,
          priority: data.priority,
          assignedTo: data.assignedTo,
          createdBy: data.createdBy,
          createdAt: (data.createdAt as Timestamp).toDate(),
          dueDate: data.dueDate ? (data.dueDate as Timestamp).toDate() : undefined,
          completedAt: data.completedAt ? (data.completedAt as Timestamp).toDate() : undefined,
          metadata: data.metadata
        });
      });

      return tasks;
    } catch (error) {
      console.error(`Error getting created tasks for agent ${agentId}:`, error);
      throw error;
    }
  }
}
