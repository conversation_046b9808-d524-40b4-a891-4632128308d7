import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';
import { 
  uploadToKnowledgeBaseWithDeduplication, 
  computeRagIndex, 
  isDocumentIndexedForAgent 
} from '../../../../components/scriptreaderAI/uploadKnowledgebase';
import { getUserAgent, updateUserAgent } from '../../../../lib/firebase/userAgents';
import { updateAgentKnowledgeBase } from '../../../../components/scriptreaderAI/elevenlabs';

interface UploadDocumentRequest {
  fileUrl: string;
  fileName: string;
  fileType: string;
  forceUpload?: boolean;
  forceReindex?: boolean;
}

/**
 * API endpoint for uploading documents to user's ElevenLabs agent knowledge base
 * 
 * This endpoint implements the complete workflow:
 * 1. Check if user has an agent
 * 2. Upload document with deduplication
 * 3. Index for RAG
 * 4. Associate with agent
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;

    // Parse request body
    const requestBody = await request.json();
    console.log(`[UPLOAD_DOCUMENT] Received request:`, JSON.stringify(requestBody, null, 2));

    const {
      fileUrl,
      fileName,
      fileType,
      forceUpload = false,
      forceReindex = false
    }: UploadDocumentRequest = requestBody;

    // Validate required parameters
    if (!fileUrl || !fileName || !fileType) {
      return NextResponse.json({
        error: 'Missing required parameters: fileUrl, fileName, and fileType are required'
      }, { status: 400 });
    }

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    console.log(`[UPLOAD_DOCUMENT] Processing document upload for user: ${userId}`);

    // Step 1: Check if user has an agent
    const userAgent = await getUserAgent(userId);
    if (!userAgent) {
      return NextResponse.json({
        error: 'No agent found for user. Please create an agent first.'
      }, { status: 404 });
    }

    console.log(`[UPLOAD_DOCUMENT] Found user agent: ${userAgent.agentId}`);

    // Step 2: Check if document is already indexed for this agent
    const isAlreadyIndexed = await isDocumentIndexedForAgent(userAgent.agentId, fileName, apiKey);
    if (isAlreadyIndexed && !forceUpload) {
      console.log(`[UPLOAD_DOCUMENT] Document ${fileName} already indexed for agent ${userAgent.agentId}`);
      return NextResponse.json({
        success: true,
        message: `Document '${fileName}' is already indexed for your agent`,
        documentId: fileName, // This would need to be the actual document ID
        wasExisting: true,
        ragIndexed: true,
        agentUpdated: false
      });
    }

    // Step 3: Upload document with deduplication
    console.log(`[UPLOAD_DOCUMENT] Uploading document: ${fileName}`);
    const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
      fileUrl,
      fileName,
      fileType,
      apiKey,
      forceUpload
    );

    console.log(`[UPLOAD_DOCUMENT] Upload result:`, uploadResult);

    // Step 4: Trigger RAG indexing
    console.log(`[UPLOAD_DOCUMENT] Starting RAG indexing for document: ${uploadResult.id}`);
    const ragResult = await computeRagIndex(
      uploadResult.id,
      apiKey,
      forceReindex
    );

    console.log(`[UPLOAD_DOCUMENT] RAG indexing result:`, ragResult);

    // Step 5: Associate document with agent
    console.log(`[UPLOAD_DOCUMENT] Associating document with agent: ${userAgent.agentId}`);
    let agentUpdateResult;
    try {
      agentUpdateResult = await updateAgentKnowledgeBase(
        userAgent.agentId,
        uploadResult.id,
        apiKey
      );
      console.log(`[UPLOAD_DOCUMENT] Agent update result:`, agentUpdateResult);
    } catch (agentUpdateError) {
      console.error(`[UPLOAD_DOCUMENT] Error updating agent:`, agentUpdateError);
      // Continue anyway - document was uploaded and indexed
      agentUpdateResult = { success: false, error: agentUpdateError };
    }

    // Step 6: Update user agent metadata in database
    try {
      const currentDocCount = userAgent.metadata?.documentsUploaded || 0;
      await updateUserAgent(userAgent.id, {
        metadata: {
          ...userAgent.metadata,
          documentsUploaded: uploadResult.wasExisting ? currentDocCount : currentDocCount + 1,
          lastKnowledgeBaseUpdate: new Date()
        }
      });
      console.log(`[UPLOAD_DOCUMENT] Updated user agent metadata`);
    } catch (dbUpdateError) {
      console.error(`[UPLOAD_DOCUMENT] Error updating database:`, dbUpdateError);
      // Continue anyway - main operation was successful
    }

    // Return comprehensive response
    const response = {
      success: true,
      message: uploadResult.wasExisting 
        ? `Document '${fileName}' was already in knowledge base and has been indexed for your agent`
        : `Document '${fileName}' uploaded, indexed, and associated with your agent successfully`,
      documentId: uploadResult.id,
      wasExisting: uploadResult.wasExisting,
      ragIndexed: ragResult.status === 'succeeded',
      ragStatus: ragResult.status,
      agentUpdated: agentUpdateResult?.success || false,
      agentId: userAgent.agentId,
      details: {
        upload: uploadResult,
        ragIndexing: ragResult,
        agentUpdate: agentUpdateResult
      }
    };

    console.log(`[UPLOAD_DOCUMENT] Sending response:`, JSON.stringify(response, null, 2));
    return NextResponse.json(response);

  } catch (error: any) {
    console.error('[UPLOAD_DOCUMENT] Error processing document upload:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to upload document',
      details: error.stack
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check document status for user's agent
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('fileName');

    if (!fileName) {
      return NextResponse.json({
        error: 'fileName parameter is required'
      }, { status: 400 });
    }

    // Get user agent
    const userAgent = await getUserAgent(userId);
    if (!userAgent) {
      return NextResponse.json({
        error: 'No agent found for user'
      }, { status: 404 });
    }

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    // Check if document is indexed for agent
    const isIndexed = await isDocumentIndexedForAgent(userAgent.agentId, fileName, apiKey);

    return NextResponse.json({
      fileName,
      agentId: userAgent.agentId,
      isIndexed,
      userAgent: {
        id: userAgent.id,
        agentName: userAgent.agentName,
        documentsUploaded: userAgent.metadata?.documentsUploaded || 0,
        lastKnowledgeBaseUpdate: userAgent.metadata?.lastKnowledgeBaseUpdate
      }
    });

  } catch (error: any) {
    console.error('[UPLOAD_DOCUMENT] Error checking document status:', error);
    return NextResponse.json({
      error: error.message || 'Failed to check document status'
    }, { status: 500 });
  }
}
