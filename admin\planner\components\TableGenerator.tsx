'use client';

import React, { useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
} from '@tanstack/react-table';
import { ChevronDown, ChevronUp, ChevronsUpDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface Column {
  key: string;
  label: string;
}

interface TableConfig {
  title?: string;
  subtitle?: string;
  columns?: Column[];
  data: Array<Record<string, any>>;
  pagination?: boolean;
  rowsPerPage?: number;
  explanation?: string;
}

/**
 * TableGenerator component for rendering data tables
 */
export default function TableGenerator({ tableConfig }: { tableConfig?: TableConfig }) {
  // If no table config is provided, show a placeholder
  if (!tableConfig) {
    return (
      <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
        <div className="mx-auto mb-4 text-zinc-600 grid place-items-center">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="3" y1="15" x2="21" y2="15"></line>
            <line x1="9" y1="3" x2="9" y2="21"></line>
            <line x1="15" y1="3" x2="15" y2="21"></line>
          </svg>
        </div>
        <h3 className="text-lg font-semibold mb-2 text-zinc-300">No Table Data Available</h3>
        <p>Enter a prompt to generate a table visualization.</p>
      </div>
    );
  }

  // Extract table configuration
  const {
    title,
    subtitle,
    columns = [],
    data = [],
    pagination = true,
    rowsPerPage = 10,
    explanation
  } = tableConfig;

  // Generate columns if not provided
  const tableColumns = useMemo(() => {
    if (columns && columns.length > 0) {
      return columns.map(col => ({
        accessorKey: col.key,
        header: col.label || col.key,
        cell: (info: any) => info.getValue(),
      }));
    }

    // If no columns provided but we have data, generate from the first row
    if (data.length > 0) {
      return Object.keys(data[0]).map(key => ({
        accessorKey: key,
        header: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
        cell: (info: any) => info.getValue(),
      }));
    }

    return [];
  }, [columns, data]);

  // Initialize the table
  const table = useReactTable({
    data,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: rowsPerPage,
      },
    },
  });

  return (
    <div className="space-y-4">
      {/* Table title and subtitle */}
      {title && (
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white">{title}</h2>
          {subtitle && <p className="text-sm text-zinc-400">{subtitle}</p>}
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border border-zinc-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead className="bg-zinc-800 text-zinc-300 sticky top-0">
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      className="px-4 py-3 text-left font-semibold border-b border-zinc-700 uppercase text-xs tracking-wider"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="divide-y divide-zinc-700 bg-zinc-900">
              {table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map(row => (
                  <tr
                    key={row.id}
                    className="hover:bg-zinc-800 transition-colors"
                  >
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className="px-4 py-3 text-zinc-300">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={tableColumns.length}
                    className="px-4 py-6 text-center text-zinc-400"
                  >
                    No results found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {pagination && table.getPageCount() > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-zinc-400">
            Showing{' '}
            <span className="font-medium text-zinc-300">
              {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}
            </span>{' '}
            to{' '}
            <span className="font-medium text-zinc-300">
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getFilteredRowModel().rows.length
              )}
            </span>{' '}
            of{' '}
            <span className="font-medium text-zinc-300">
              {table.getFilteredRowModel().rows.length}
            </span>{' '}
            results
          </div>
          <div className="flex items-center space-x-2 text-zinc-300">
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronsLeft className="h-5 w-5" />
            </button>
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <span className="text-sm">
              <span className="font-medium">
                {table.getState().pagination.pageIndex + 1}
              </span>{' '}
              of{' '}
              <span className="font-medium">
                {table.getPageCount()}
              </span>
            </span>
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <ChevronsRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Explanation */}
      {explanation && (
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-800 rounded-md">
          <p className="text-sm text-blue-200">{explanation}</p>
        </div>
      )}
    </div>
  );
}
