import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { v4 as uuidv4 } from 'uuid';

// Import Firebase admin modules
import { adminStorage } from 'components/firebase-admin';

/**
 * API route for uploading images
 * This is used by the Vision tool to upload images for analysis
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Get server session and validate
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - No valid session" },
        { status: 401 }
      );
    }

    // Get the user ID from the session
    const userId = session.user.email;
    
    // Parse the form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: "No file provided" },
        { status: 400 }
      );
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, error: "File size exceeds 10MB limit" },
        { status: 400 }
      );
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: "File type not supported. Please upload a JPEG, PNG, WEBP, or GIF image." },
        { status: 400 }
      );
    }

    // Generate a unique ID for the file
    const fileId = uuidv4();
    
    // Convert the file to a Buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Upload to Firebase Storage
    const bucket = adminStorage.bucket();
    const filePath = `uploads/${userId}/vision/${fileId}`;
    const fileRef = bucket.file(filePath);
    
    await fileRef.save(buffer, {
      metadata: {
        contentType: file.type,
        metadata: {
          userId,
          originalName: file.name,
          uploadedAt: new Date().toISOString()
        }
      }
    });
    
    // Generate a signed URL for the uploaded file
    const [url] = await fileRef.getSignedUrl({
      action: 'read',
      expires: '03-01-2500' // Long expiration
    });
    
    return NextResponse.json({
      success: true,
      url,
      fileId,
      fileName: file.name
    });
    
  } catch (error: any) {
    console.error('Error uploading image:', error);
    return NextResponse.json(
      { success: false, error: error.message || 'Failed to upload image' },
      { status: 500 }
    );
  }
}

export async function OPTIONS(_req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}
