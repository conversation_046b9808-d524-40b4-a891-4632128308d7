/**
 * PMO Investigative Research Integration
 * 
 * Integrates the Investigative Research Agent with the PMO ecosystem.
 * Provides seamless integration with PMO workflows and agent output storage.
 */

import { InvestigativeResearchAgentManager, PMOInvestigationRequest } from './InvestigativeResearchAgentManager';
import { InvestigationResult, InvestigationType } from './InvestigativeResearchAgent';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { adminDb } from '../../../components/firebase-admin';

export interface PMOInvestigativeTaskRequest {
  pmoId: string;
  title: string;
  description: string;
  investigationType?: InvestigationType;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  userId: string;
  autoSelectJournalists?: boolean;
  consolidateReport?: boolean;
}

export interface PMOInvestigativeOutput {
  investigationId: string;
  pmoId: string;
  title: string;
  investigationType: InvestigationType;
  status: 'completed' | 'failed';
  result?: InvestigationResult;
  error?: string;
  createdAt: Date;
  completedAt?: Date;
  metadata: {
    journalistCount: number;
    hasConsolidation: boolean;
    modelConfiguration: any;
    keyFindings: string[];
    recommendations: string[];
  };
}

/**
 * PMO Investigative Research Integration Class
 */
export class PMOInvestigativeIntegration {
  private agentManager: InvestigativeResearchAgentManager;

  constructor(userId: string) {
    this.agentManager = new InvestigativeResearchAgentManager({
      userId,
      defaultLlmProvider: 'openai',
      defaultLlmModel: 'gpt-4o'
    });
  }

  /**
   * Process investigative research request from PMO
   */
  async processInvestigativeRequest(request: PMOInvestigativeTaskRequest): Promise<PMOInvestigativeOutput> {
    console.log(`Processing investigative request for PMO ${request.pmoId}: ${request.title}`);

    const investigationId = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = new Date();

    try {
      // Determine investigation type if not specified
      const investigationType = request.investigationType || await this.determineInvestigationType(request.title, request.description);

      // Get recommended journalists if auto-selection is enabled
      let selectedJournalistIds: string[] = [];
      if (request.autoSelectJournalists !== false) {
        const recommendedJournalists = this.agentManager.getRecommendedJournalists(investigationType);
        selectedJournalistIds = recommendedJournalists.slice(0, 3).map(j => j.id);
      }

      // Create PMO investigation request
      const pmoInvestigationRequest: PMOInvestigationRequest = {
        pmoId: request.pmoId,
        title: request.title,
        description: request.description,
        investigationType,
        selectedJournalistIds,
        consolidate: request.consolidateReport !== false,
        userId: request.userId,
        priority: request.priority
      };

      // Conduct the investigation
      const result = await this.agentManager.conductPMOInvestigation(pmoInvestigationRequest);

      // Create PMO output
      const pmoOutput: PMOInvestigativeOutput = {
        investigationId,
        pmoId: request.pmoId,
        title: request.title,
        investigationType,
        status: 'completed',
        result,
        createdAt: startTime,
        completedAt: new Date(),
        metadata: {
          journalistCount: result.journalistResponses.length,
          hasConsolidation: !!result.consolidatedReport,
          modelConfiguration: {
            criteriaModel: result.criteriaModel,
            optimizationModel: result.optimizationModel,
            assessmentModel: result.assessmentModel,
            consolidationModel: result.consolidationModel
          },
          keyFindings: result.keyFindings,
          recommendations: result.recommendations
        }
      };

      // Store in PMO Agent Output system
      await this.storePMOAgentOutput(pmoOutput);

      console.log(`Investigative research completed for PMO ${request.pmoId}`);
      return pmoOutput;

    } catch (error) {
      console.error(`Investigative research failed for PMO ${request.pmoId}:`, error);

      const pmoOutput: PMOInvestigativeOutput = {
        investigationId,
        pmoId: request.pmoId,
        title: request.title,
        investigationType: request.investigationType || InvestigationType.INVESTIGATIVE,
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
        createdAt: startTime,
        completedAt: new Date(),
        metadata: {
          journalistCount: 0,
          hasConsolidation: false,
          modelConfiguration: {},
          keyFindings: [],
          recommendations: []
        }
      };

      // Store failed output for tracking
      await this.storePMOAgentOutput(pmoOutput);

      throw error;
    }
  }

  /**
   * Determine investigation type based on title and description
   */
  private async determineInvestigationType(title: string, description: string): Promise<InvestigationType> {
    const content = `${title}\n\n${description}`.toLowerCase();

    // Simple keyword-based classification
    if (content.includes('financial') || content.includes('money') || content.includes('market') || content.includes('economic')) {
      return InvestigationType.FINANCIAL;
    }
    
    if (content.includes('political') || content.includes('government') || content.includes('policy') || content.includes('election')) {
      return InvestigationType.POLITICAL;
    }
    
    if (content.includes('technology') || content.includes('tech') || content.includes('cyber') || content.includes('digital')) {
      return InvestigationType.TECHNOLOGY;
    }
    
    if (content.includes('social') || content.includes('community') || content.includes('human rights') || content.includes('demographic')) {
      return InvestigationType.SOCIAL_AFFAIRS;
    }
    
    if (content.includes('corporate') || content.includes('business') || content.includes('company') || content.includes('industry')) {
      return InvestigationType.CORPORATE;
    }
    
    if (content.includes('environment') || content.includes('climate') || content.includes('sustainability') || content.includes('green')) {
      return InvestigationType.ENVIRONMENTAL;
    }
    
    if (content.includes('story') || content.includes('narrative') || content.includes('feature') || content.includes('profile')) {
      return InvestigationType.FEATURE;
    }

    // Default to general investigative
    return InvestigationType.INVESTIGATIVE;
  }

  /**
   * Store PMO agent output in Firebase
   */
  private async storePMOAgentOutput(output: PMOInvestigativeOutput): Promise<void> {
    try {
      const agentOutputData = {
        agentId: 'investigative-research-agent',
        teamId: AgenticTeamId.Research,
        teamName: 'Research',
        pmoId: output.pmoId,
        title: output.title,
        status: output.status,
        createdAt: output.createdAt,
        completedAt: output.completedAt || null,
        
        // Investigation-specific data
        investigationId: output.investigationId,
        investigationType: output.investigationType,
        
        // Results data
        content: output.result ? this.formatInvestigationContent(output.result) : null,
        keyFindings: output.metadata.keyFindings,
        recommendations: output.metadata.recommendations,
        
        // Metadata
        metadata: {
          ...output.metadata,
          agentType: 'investigative-research',
          outputType: 'investigation-report',
          error: output.error || null
        }
      };

      // Store in PMO Agent Outputs collection
      const docRef = await adminDb
        .collection('pmo_agent_outputs')
        .add(agentOutputData);

      console.log(`PMO agent output stored with ID: ${docRef.id}`);

    } catch (error) {
      console.error('Failed to store PMO agent output:', error);
      // Don't throw error as this is not critical for the investigation process
    }
  }

  /**
   * Format investigation result for PMO content storage
   */
  private formatInvestigationContent(result: InvestigationResult): string {
    let content = `# Investigative Research Report\n\n`;
    content += `**Investigation ID:** ${result.investigationId}\n`;
    content += `**Investigation Type:** ${result.investigationType.toUpperCase()}\n`;
    content += `**Conducted:** ${result.createdAt.toLocaleDateString()}\n\n`;

    content += `## Original Research Question\n${result.originalPrompt}\n\n`;
    content += `## Optimized Research Question\n${result.optimizedPrompt}\n\n`;

    content += `## Key Findings\n`;
    result.keyFindings.forEach((finding, index) => {
      content += `${index + 1}. ${finding}\n`;
    });
    content += `\n`;

    content += `## Recommendations\n`;
    result.recommendations.forEach((rec, index) => {
      content += `${index + 1}. ${rec}\n`;
    });
    content += `\n`;

    if (result.consolidatedReport) {
      content += `## Consolidated Investigation Report\n`;
      content += `${result.consolidatedReport}\n\n`;
    }

    content += `## Investigation Methodology\n`;
    content += `This investigation employed ${result.journalistResponses.length} specialized journalist AI agents:\n\n`;
    
    result.journalistResponses.forEach((response, index) => {
      content += `**${response.journalistName}** (${response.model} via ${response.provider})\n`;
      content += `- Investigation Angle: ${response.investigationAngle}\n`;
      content += `- Status: ${response.error ? 'Failed' : 'Completed'}\n\n`;
    });

    content += `## Technical Details\n`;
    content += `- **Criteria Model:** ${result.criteriaModel} (${result.criteriaProvider})\n`;
    content += `- **Optimization Model:** ${result.optimizationModel} (${result.optimizationProvider})\n`;
    content += `- **Assessment Model:** ${result.assessmentModel} (${result.assessmentProvider})\n`;
    if (result.consolidationModel) {
      content += `- **Consolidation Model:** ${result.consolidationModel} (${result.consolidationProvider})\n`;
    }

    return content;
  }

  /**
   * Get investigation history for a PMO
   */
  async getPMOInvestigationHistory(pmoId: string, limit: number = 10): Promise<PMOInvestigativeOutput[]> {
    try {
      const snapshot = await adminDb
        .collection('pmo_agent_outputs')
        .where('pmoId', '==', pmoId)
        .where('agentId', '==', 'investigative-research-agent')
        .orderBy('createdAt', 'desc')
        .limit(limit)
        .get();

      const history: PMOInvestigativeOutput[] = [];
      
      snapshot.forEach(doc => {
        const data = doc.data();
        history.push({
          investigationId: data.investigationId,
          pmoId: data.pmoId,
          title: data.title,
          investigationType: data.investigationType,
          status: data.status,
          createdAt: data.createdAt.toDate(),
          completedAt: data.completedAt ? data.completedAt.toDate() : undefined,
          metadata: data.metadata,
          error: data.metadata?.error
        });
      });

      return history;

    } catch (error) {
      console.error('Failed to retrieve PMO investigation history:', error);
      return [];
    }
  }

  /**
   * Check if PMO has investigative research capability
   */
  static isInvestigativeResearchAvailable(): boolean {
    return true; // Always available as part of the PMO system
  }

  /**
   * Get available investigation types for PMO
   */
  getAvailableInvestigationTypes() {
    return this.agentManager.getInvestigationTypes();
  }

  /**
   * Get available journalist personas for PMO
   */
  getAvailableJournalists() {
    return this.agentManager.getAvailableJournalists();
  }
}
