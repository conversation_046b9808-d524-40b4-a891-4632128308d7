import { deleteDocumentAndChatsByNamespace } from '../../../components/DocViewer/deleteDocumentsAndChatsByNamespace ';
import { NextRequest, NextResponse } from 'next/server';

// Export a named handler for POST method
export async function POST(req: NextRequest) {
  console.log(`[DELETE API] Received request to delete document and chats`);

  let userId, namespace;
  try {
    const body = await req.json();
    userId = body.userId;
    namespace = body.namespace;
    console.log(`[DELETE API] Request parameters - userId: ${userId}, namespace: ${namespace}`);

    if (!userId || !namespace) {
      console.log(`[DELETE API] Missing required parameters`);
      return NextResponse.json({ error: 'Missing required parameters: userId and namespace' }, { status: 400 });
    }
  } catch (parseError) {
    console.error(`[DELETE API] Error parsing request body:`, parseError);
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  try {
    console.log(`[DELETE API] Calling deleteDocumentAndChatsByNamespace`);
    await deleteDocumentAndChatsByNamespace(userId, namespace);
    console.log(`[DELETE API] Document and chats successfully deleted`);
    return NextResponse.json({ message: 'Document and chats successfully deleted.' }, { status: 200 });
  } catch (error) {
    console.error('[DELETE API] Error deleting document and chats:', error);
    return NextResponse.json({ error: 'Error deleting document and chats.' }, { status: 500 });
  }
}

// If you want to handle unsupported methods, you can use the following:
export function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      Allow: 'POST',
    },
  });
}
