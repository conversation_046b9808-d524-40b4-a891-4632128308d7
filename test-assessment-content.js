/**
 * Test script to examine the actual PMO assessment content
 * This will help us see the exact format and fix the regex pattern
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, orderBy, where } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

async function examineAssessmentContent() {
  console.log('🔍 Examining PMO Assessment Content');
  console.log('===================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Test user email
    const testUserEmail = '<EMAIL>';

    console.log(`\n📋 Getting codebase documentation record for user: ${testUserEmail}`);

    // Get PMO records from user-specific collection
    const pmoCollectionRef = collection(db, 'users', testUserEmail, 'PMO');
    const recordsQuery = query(
      pmoCollectionRef, 
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(recordsQuery);

    // Find the codebase documentation record
    const codebaseDocRecord = snapshot.docs.find(doc => {
      const data = doc.data();
      return data.title && data.title.toLowerCase().includes('codebase documentation');
    });

    if (codebaseDocRecord) {
      const data = codebaseDocRecord.data();
      
      console.log(`\n📄 Record ID: ${codebaseDocRecord.id}`);
      console.log(`📄 Title: ${data.title}`);
      console.log(`📄 Team Assignment: ${data.agentIds ? data.agentIds.join(', ') : 'None'}`);
      
      if (data.pmoAssessment) {
        console.log('\n📝 FULL PMO ASSESSMENT CONTENT:');
        console.log('================================');
        console.log(data.pmoAssessment);
        console.log('================================');
        
        // Test our regex patterns
        console.log('\n🔍 Testing Regex Patterns:');
        
        const patterns = [
          { name: 'Standard Teams format', regex: /\*\*Teams:\*\*\s*([A-Za-z\s]+?)\s*(?:\*\*Rationale:\*\*|$)/i },
          { name: 'Codebase Documentation Team with ID', regex: /\*\*([A-Za-z\s]+Team)\s*\([A-Za-z0-9]+\)\*\*:/i },
          { name: 'Team with asterisks', regex: /\*\*([A-Za-z\s]+Team)\*\*:/i },
          { name: 'Simple team mention', regex: /(Codebase Documentation Team|Marketing Team|Research Team|Software Design Team|Sales Team|Business Analysis Team|Investigative Research Team)/i },
          { name: 'Team Assignment section', regex: /### Team Assignment[^#]*?\*\*([A-Za-z\s]+Team)[^:]*:/i },
          { name: 'Any team with Ag007', regex: /\*\*([^*]+)\s*\(Ag007\)\*\*:/i }
        ];
        
        patterns.forEach(({ name, regex }) => {
          const match = data.pmoAssessment.match(regex);
          if (match) {
            console.log(`   ✅ ${name}: Found "${match[1] || match[0]}"`);
          } else {
            console.log(`   ❌ ${name}: No match`);
          }
        });
        
      } else {
        console.log('\n⚠️  No PMO assessment found in record');
      }
      
    } else {
      console.log('\n⚠️  No codebase documentation record found');
    }

    console.log('\n✅ Assessment Content Examination Complete');

  } catch (error) {
    console.error('\n❌ Error examining assessment content:', error);
  }
}

// Run the test
examineAssessmentContent().then(() => {
  console.log('\n🎉 Examination finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Examination failed:', error);
  process.exit(1);
});
