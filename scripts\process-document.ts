/**
 * process-document.ts
 *
 * Command-line script to process a document with memory optimization
 */

import { processDocument, DocumentProcessingOptions } from '../lib/documentProcessing';
import { getDetailedMemoryUsage } from '../lib/utils/enhancedMemoryManager';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
  console.error('Usage: node process-document.js <file-path> [options]');
  process.exit(1);
}

// Get file path
const filePath = args[0];
if (!fs.existsSync(filePath)) {
  console.error(`File not found: ${filePath}`);
  process.exit(1);
}

// Parse options
const options: DocumentProcessingOptions = {
  chunkSize: 1000,
  chunkOverlap: 200,
  aggressiveMemoryManagement: true,
  streamProcessing: true,
  maxConcurrentChunks: 5
};

// Parse additional command line options
for (let i = 1; i < args.length; i++) {
  const arg = args[i];
  if (arg.startsWith('--chunk-size=')) {
    options.chunkSize = parseInt(arg.split('=')[1]);
  } else if (arg.startsWith('--chunk-overlap=')) {
    options.chunkOverlap = parseInt(arg.split('=')[1]);
  } else if (arg === '--no-aggressive-memory') {
    options.aggressiveMemoryManagement = false;
  } else if (arg === '--no-stream-processing') {
    options.streamProcessing = false;
  } else if (arg.startsWith('--max-concurrent-chunks=')) {
    options.maxConcurrentChunks = parseInt(arg.split('=')[1]);
  }
}

// Get file info
const fileStats = fs.statSync(filePath);
const fileName = path.basename(filePath);
const fileType = getFileType(fileName);
const docId = uuidv4();
const userId = 'cli-user';
const category = 'cli-import';

console.log(`Processing file: ${fileName}`);
console.log(`File size: ${(fileStats.size / (1024 * 1024)).toFixed(2)} MB`);
console.log(`File type: ${fileType}`);
console.log(`Options: ${JSON.stringify(options)}`);

// Function to determine file type
function getFileType(fileName: string): string {
  const extension = path.extname(fileName).toLowerCase();
  switch (extension) {
    case '.pdf':
      return 'application/pdf';
    case '.docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case '.doc':
      return 'application/msword';
    case '.txt':
      return 'text/plain';
    case '.rtf':
      return 'application/rtf';
    case '.csv':
      return 'text/csv';
    case '.xls':
      return 'application/vnd.ms-excel';
    case '.xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    default:
      return 'application/octet-stream';
  }
}

// Process the document
async function main() {
  try {
    // Log initial memory usage
    getDetailedMemoryUsage('Initial');

    // Read file
    const fileBuffer = fs.readFileSync(filePath);
    const file = new File([fileBuffer], fileName, { type: fileType });

    // For large files, use more aggressive chunking
    if (fileStats.size > 10 * 1024 * 1024) { // 10MB threshold
      console.log(`Large file detected (${(fileStats.size / (1024 * 1024)).toFixed(2)} MB), using 64-chunk processing`);

      // Override options for large files
      options.chunkSize = Math.ceil(fileStats.size / 64); // Dynamic chunk size for 64 chunks
      options.aggressiveMemoryManagement = true;
      options.streamProcessing = true;
      options.maxConcurrentChunks = 1; // Process one chunk at a time for memory safety

      console.log(`Adjusted options for large file: ${JSON.stringify(options)}`);
    }

    // For very large files, use even more aggressive chunking
    if (fileStats.size > 50 * 1024 * 1024) { // 50MB threshold
      console.log(`Very large file detected (${(fileStats.size / (1024 * 1024)).toFixed(2)} MB), using 128-chunk processing`);

      // Override options for very large files
      options.chunkSize = Math.ceil(fileStats.size / 128); // Dynamic chunk size for 128 chunks
      options.chunkOverlap = Math.min(options.chunkOverlap || 200, 100); // Reduce overlap to save memory
      options.aggressiveMemoryManagement = true;
      options.streamProcessing = true;
      options.maxConcurrentChunks = 1; // Process one chunk at a time for memory safety

      console.log(`Adjusted options for very large file: ${JSON.stringify(options)}`);
    }

    // Process document
    const chunks = await processDocument(
      file,
      docId,
      fileType,
      fileName,
      userId,
      category,
      '',
      options
    );

    console.log(`Document processed successfully. Generated ${chunks.length} chunks.`);

    // Output results
    const outputPath = path.join(path.dirname(filePath), `${path.basename(filePath, path.extname(filePath))}_chunks.json`);
    fs.writeFileSync(outputPath, JSON.stringify(chunks, null, 2));
    console.log(`Chunks saved to: ${outputPath}`);

    // Final memory usage
    getDetailedMemoryUsage('Final');

  } catch (error) {
    console.error('Error processing document:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
