'use client';

import React, { useState, useEffect } from 'react';
import { Task, Resource } from '../types';
import { Calendar, ChevronLeft, ChevronRight, Edit } from 'lucide-react';
import TaskDetailsModal from './TaskDetailsModal';

interface TimelineProps {
  tasks: Task[];
  resources: Resource[];
  onEdit: (task: Task) => void;
}

const Timeline: React.FC<TimelineProps> = ({
  tasks,
  resources,
  onEdit
}) => {
  const [startDate, setStartDate] = useState<Date>(new Date('2025-04-08'));
  const [endDate, setEndDate] = useState<Date>(new Date('2025-05-20'));
  const [days, setDays] = useState<Date[]>([]);
  const [weeks, setWeeks] = useState<{ start: Date; end: Date }[]>([]);
  const [tasksByCategory, setTasksByCategory] = useState<Record<string, Task[]>>({});
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // Initialize timeline dates
  useEffect(() => {
    // Find the earliest start date and latest end date from tasks
    if (tasks.length > 0) {
      const earliestStart = new Date(Math.min(...tasks.map(t => t.startDate.getTime())));
      const latestEnd = new Date(Math.max(...tasks.map(t => t.dueDate.getTime())));

      // Add padding days
      earliestStart.setDate(earliestStart.getDate() - 2);
      latestEnd.setDate(latestEnd.getDate() + 2);

      setStartDate(earliestStart);
      setEndDate(latestEnd);
    }
  }, [tasks]);

  // Generate days and weeks for the timeline
  useEffect(() => {
    const daysList: Date[] = [];
    const weeksList: { start: Date; end: Date }[] = [];

    let currentDate = new Date(startDate);
    let weekStart = new Date(currentDate);

    while (currentDate <= endDate) {
      daysList.push(new Date(currentDate));

      // If it's the start of the week or the first day
      if (currentDate.getDay() === 0 || daysList.length === 1) {
        weekStart = new Date(currentDate);
      }

      // If it's the end of the week or the last day
      if (currentDate.getDay() === 6 || currentDate.getTime() === endDate.getTime()) {
        weeksList.push({
          start: new Date(weekStart),
          end: new Date(currentDate)
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    setDays(daysList);
    setWeeks(weeksList);
  }, [startDate, endDate]);

  // Group tasks by category
  useEffect(() => {
    const grouped: Record<string, Task[]> = {};

    tasks.forEach(task => {
      if (!grouped[task.category]) {
        grouped[task.category] = [];
      }
      grouped[task.category].push(task);
    });

    setTasksByCategory(grouped);
  }, [tasks]);

  // Format date for display
  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  // Get day of week
  const getDayOfWeek = (date: Date): string => {
    return new Date(date).toLocaleDateString('en-US', { weekday: 'short' });
  };

  // Check if a date is today
  const isToday = (date: Date): boolean => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear();
  };

  // Check if a date is a weekend
  const isWeekend = (date: Date): boolean => {
    const day = date.getDay();
    return day === 0 || day === 6;
  };

  // Calculate task position and width
  const getTaskStyle = (task: Task): React.CSSProperties => {
    const taskStart = new Date(task.startDate);
    const taskEnd = new Date(task.dueDate);

    // Calculate days from timeline start to task start
    const daysBeforeStart = Math.floor((taskStart.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    // Calculate task duration in days
    const taskDuration = Math.ceil((taskEnd.getTime() - taskStart.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    // Calculate left position and width as percentages
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    const leftPosition = (daysBeforeStart / totalDays) * 100;
    const width = (taskDuration / totalDays) * 100;

    return {
      left: `${leftPosition}%`,
      width: `${width}%`,
      position: 'absolute',
      height: '30px',
      borderRadius: '4px',
      zIndex: 10
    };
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-200 border-gray-400';
      case 'In Progress':
        return 'bg-blue-200 border-blue-400';
      case 'Reviewed':
        return 'bg-yellow-200 border-yellow-400';
      case 'Complete':
        return 'bg-green-200 border-green-400';
      default:
        return 'bg-gray-200 border-gray-400';
    }
  };

  // Get priority border
  const getPriorityBorder = (priority: string): string => {
    switch (priority) {
      case 'Low':
        return 'border-l-gray-400';
      case 'Medium':
        return 'border-l-blue-500';
      case 'High':
        return 'border-l-orange-500';
      case 'Critical':
        return 'border-l-red-500';
      default:
        return 'border-l-gray-400';
    }
  };

  // Get resource names for display
  const getResourceNames = (resourceIds: string[]): string => {
    return resourceIds
      .map(id => {
        const resource = resources.find(r => r.id === id);
        return resource ? `${resource.name}${resource.jobTitle ? ` (${resource.jobTitle})` : ''}` : 'Unknown';
      })
      .join(', ');
  };

  // Navigate timeline
  const navigateTimeline = (direction: 'prev' | 'next') => {
    const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    if (direction === 'prev') {
      const newStartDate = new Date(startDate);
      newStartDate.setDate(newStartDate.getDate() - daysDiff);

      const newEndDate = new Date(endDate);
      newEndDate.setDate(newEndDate.getDate() - daysDiff);

      setStartDate(newStartDate);
      setEndDate(newEndDate);
    } else {
      const newStartDate = new Date(startDate);
      newStartDate.setDate(newStartDate.getDate() + daysDiff);

      const newEndDate = new Date(endDate);
      newEndDate.setDate(newEndDate.getDate() + daysDiff);

      setStartDate(newStartDate);
      setEndDate(newEndDate);
    }
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Timeline header with navigation */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="font-bold text-gray-700">
            Timeline: {formatDate(startDate)} - {formatDate(endDate)}
          </h2>

        <div className="flex space-x-2">
          <button
            onClick={() => navigateTimeline('prev')}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          <button
            onClick={() => navigateTimeline('next')}
            className="p-2 rounded-md bg-gray-100 hover:bg-gray-200 transition-colors"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Timeline grid */}
      <div className="overflow-x-auto">
        <div className="min-w-max">
          {/* Week and day headers */}
          <div className="flex border-b border-gray-200">
            {/* Category column */}
            <div className="w-48 flex-shrink-0 p-2 font-medium text-gray-700 border-r border-gray-200">
              Category
            </div>

            {/* Weeks */}
            <div className="flex-grow">
              <div className="flex">
                {weeks.map((week, index) => (
                  <div
                    key={index}
                    className="border-r border-gray-200 py-1 px-2 text-xs font-medium text-gray-500 text-center"
                    style={{
                      width: `${(((week.end.getTime() - week.start.getTime()) / (1000 * 60 * 60 * 24) + 1) / days.length) * 100}%`
                    }}
                  >
                    Week {index + 1}: {formatDate(week.start)} - {formatDate(week.end)}
                  </div>
                ))}
              </div>

              {/* Days */}
              <div className="flex border-t border-gray-200">
                {days.map((day, index) => (
                  <div
                    key={index}
                    className={`flex-1 py-1 text-xs font-medium text-center ${
                      isToday(day)
                        ? 'bg-blue-50 text-blue-600'
                        : isWeekend(day)
                          ? 'bg-gray-50 text-gray-500'
                          : 'text-gray-500'
                    } ${index < days.length - 1 ? 'border-r border-gray-200' : ''}`}
                  >
                    <div>{getDayOfWeek(day)}</div>
                    <div>{day.getDate()}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Task rows by category */}
          {Object.entries(tasksByCategory).map(([category, categoryTasks]) => (
            <div key={category} className="border-b border-gray-200">
              {/* Category header */}
              <div className="flex">
                <div className="w-48 flex-shrink-0 p-3 font-medium text-gray-800 bg-gray-50 border-r border-gray-200">
                  {category}
                </div>

                {/* Timeline grid for this category */}
                <div className="flex-grow relative">
                  {/* Day columns */}
                  <div className="flex h-full absolute inset-0">
                    {days.map((day, index) => (
                      <div
                        key={index}
                        className={`flex-1 h-full ${
                          isToday(day)
                            ? 'bg-blue-50'
                            : isWeekend(day)
                              ? 'bg-gray-50'
                              : ''
                        } ${index < days.length - 1 ? 'border-r border-gray-200' : ''}`}
                      />
                    ))}
                  </div>

                  {/* Tasks */}
                  <div className="relative p-2" style={{ minHeight: '120px' }}>
                    {categoryTasks.map((task, taskIndex) => (
                      <div
                        key={task.id}
                        className={`absolute border ${getStatusColor(task.status)} border-l-4 ${getPriorityBorder(task.priority)} shadow-sm hover:shadow-md transition-shadow cursor-pointer`}
                        style={{
                          ...getTaskStyle(task),
                          top: `${taskIndex * 40}px`
                        }}
                        onClick={() => setSelectedTask(task)}
                        title={`${task.title} (${formatDate(task.startDate)} - ${formatDate(task.dueDate)})`}
                      >
                        <div className="px-2 py-1 text-xs font-medium truncate flex items-center justify-between">
                          <span className="truncate">{task.title}</span>
                          <Edit
                            className="w-3 h-3 text-gray-500"
                            onClick={(e) => {
                              e.stopPropagation();
                              onEdit(task);
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="p-3 border-t border-gray-200 bg-gray-50 flex flex-wrap gap-4">
        <div className="text-sm font-medium text-gray-700">Status:</div>
        <div className="flex space-x-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-200 border border-gray-400 rounded-sm mr-1"></div>
            <span className="text-xs text-gray-600">Not Started</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-200 border border-blue-400 rounded-sm mr-1"></div>
            <span className="text-xs text-gray-600">In Progress</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-yellow-200 border border-yellow-400 rounded-sm mr-1"></div>
            <span className="text-xs text-gray-600">Reviewed</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-200 border border-green-400 rounded-sm mr-1"></div>
            <span className="text-xs text-gray-600">Complete</span>
          </div>
        </div>

        <div className="text-sm font-medium text-gray-700 ml-6">Priority:</div>
        <div className="flex space-x-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-white border border-gray-300 border-l-4 border-l-gray-400 mr-1"></div>
            <span className="text-xs text-gray-600">Low</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-white border border-gray-300 border-l-4 border-l-blue-500 mr-1"></div>
            <span className="text-xs text-gray-600">Medium</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-white border border-gray-300 border-l-4 border-l-orange-500 mr-1"></div>
            <span className="text-xs text-gray-600">High</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-white border border-gray-300 border-l-4 border-l-red-500 mr-1"></div>
            <span className="text-xs text-gray-600">Critical</span>
          </div>
        </div>
      </div>
    </div>

    {/* Task details modal */}
    {selectedTask && (
      <TaskDetailsModal
        task={selectedTask}
        onClose={() => setSelectedTask(null)}
        onEdit={() => {
          // Close the details modal and open the edit modal
          const taskToEdit = selectedTask;
          setSelectedTask(null);
          onEdit(taskToEdit);
        }}
      />
    )}
    </>
  );
};

export default Timeline;
