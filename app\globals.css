@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom scrollbar styles */
  .custom-scrollbar::-webkit-scrollbar {
    width: 12px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 2px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(138, 75, 175, 0.8);
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3) inset;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(138, 75, 175, 1);
  }

  /* Firefox scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(138, 75, 175, 0.8) rgba(255, 255, 255, 0.1);
  }

  /* Vision analysis text styles */
  .prose-p\:text-base p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .prose-p\:leading-relaxed p {
    line-height: 1.7;
  }

  /* Toast animation */
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Custom slider styles */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #8b5cf6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-webkit-slider-thumb:hover {
    background: #7c3aed;
    transform: scale(1.1);
  }

  .slider::-webkit-slider-track {
    height: 4px;
    cursor: pointer;
    background: #4b5563;
    border-radius: 2px;
  }

  .slider::-moz-range-thumb {
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: #8b5cf6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .slider::-moz-range-track {
    height: 4px;
    cursor: pointer;
    background: #4b5563;
    border-radius: 2px;
    border: none;
  }

  .slider:disabled::-webkit-slider-thumb {
    background: #6b7280;
    cursor: not-allowed;
  }

  .slider:disabled::-webkit-slider-track {
    background: #374151;
    cursor: not-allowed;
  }
}
