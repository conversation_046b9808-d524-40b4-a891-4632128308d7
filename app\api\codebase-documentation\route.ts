import { NextResponse } from 'next/server';
import { CodebaseDocumentationOrchestratorAgent } from '../../../lib/agents/pmo/CodebaseDocumentationOrchestratorAgent';
import { createPMORecordFromForm, updatePMORecord } from '../../../lib/firebase/pmoCollection';
import { addAgentOutput } from '../../../lib/firebase/agentOutputs';
import {
  CodebaseDocumentationRequestSchema,
  CodebaseDocumentationRequest,
  safeValidateDocumentationRequest,
  formatValidationErrors
} from '../../../lib/schemas/codebaseDocumentationSchemas';

/**
 * Remove undefined values from an object recursively (Firebase doesn't allow undefined)
 */
function removeUndefinedValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return null;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => removeUndefinedValues(item)).filter(item => item !== undefined);
  }

  if (typeof obj === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value !== undefined) {
        cleaned[key] = removeUndefinedValues(value);
      }
    }
    return cleaned;
  }

  return obj;
}

// Interface is now imported from centralized schema file

/**
 * POST /api/codebase-documentation
 * 
 * Processes codebase documentation requests using the Codebase Documentation Orchestrator Agent
 */
export async function POST(request: Request) {
  try {
    const rawBody = await request.json();

    // Validate request using Zod schema
    const validationResult = safeValidateDocumentationRequest(rawBody);

    if (!validationResult.success) {
      console.error('Request validation failed:', validationResult.error.errors);
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: formatValidationErrors(validationResult.error)
        },
        { status: 400 }
      );
    }

    const body: CodebaseDocumentationRequest = validationResult.data;

    // Create PMO record for tracking
    const pmoFormInput = {
      title: `Codebase Documentation: ${body.selectedPaths.join(', ')}`,
      description: body.description,
      priority: 'Medium' as const,
      category: 'Documentation',
      sourceFile: `Selected paths: ${body.selectedPaths.join(', ')}`,
      fileName: `Codebase Documentation - ${new Date().toISOString().split('T')[0]}`,
      customContext: body.customContext || undefined,
      selectedFileId: undefined,
      selectedCategory: undefined,
      pmoAssessment: `Codebase documentation request for paths: ${body.selectedPaths.join(', ')}`
    };

    // Create PMO record
    const pmoRecordId = await createPMORecordFromForm(body.userId, pmoFormInput);

    // Initialize the Codebase Documentation Orchestrator Agent
    const orchestratorAgent = new CodebaseDocumentationOrchestratorAgent({
      userId: body.userId,
      includeExplanation: true,
      streamResponse: false, // For now, disable streaming in API
      codebasePaths: body.selectedPaths,
      documentationScope: body.documentationScope || 'full',
      outputFormat: body.outputFormat || 'markdown',
      includeArchitecture: body.includeArchitecture !== false,
      includeApiDocs: body.includeApiDocs !== false,
      includeDataFlow: body.includeDataFlow !== false,
      maxSubAgents: 9
    });

    // Process the documentation request
    const result = await orchestratorAgent.processDocumentationRequest(
      body.selectedPaths,
      body.description,
      body.customContext
    );

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to generate codebase documentation',
          pmoRecordId: pmoRecordId
        },
        { status: 500 }
      );
    }

    // Save the documentation output to Firebase
    try {
      // Prepare metadata with all possible fields
      const rawMetadata = {
        taskId: result.taskId,
        pmoRecordId: pmoRecordId,
        selectedPaths: body.selectedPaths,
        documentationScope: body.documentationScope,
        subAgentResults: result.subAgentResults,
        codebaseMetrics: result.codebaseMetrics,
        documentationArtifacts: result.documentationArtifacts
      };

      // Clean metadata to remove undefined values for Firebase compatibility
      const cleanedMetadata = removeUndefinedValues(rawMetadata);

      const agentOutputResult = await addAgentOutput({
        userId: body.userId,
        agentType: 'CodebaseDocumentationOrchestrator',
        title: `Codebase Documentation - ${body.selectedPaths.slice(0, 2).join(', ')}${body.selectedPaths.length > 2 ? '...' : ''}`,
        content: result.output,
        createdAt: new Date(),
        metadata: cleanedMetadata
      });

      // Store the agent output ID for reference
      result.outputDocumentIds = [agentOutputResult.id];

      // Update PMO record status to Complete
      try {
        await updatePMORecord(body.userId, pmoRecordId, {
          status: 'Completed'
        });
      } catch (pmoUpdateError) {
        console.error('Failed to update PMO record status:', pmoUpdateError);
        // Continue without failing the entire request
      }
    } catch (outputError) {
      console.error('Failed to save agent output:', outputError);
      // Continue without failing the entire request
    }

    return NextResponse.json({
      success: true,
      pmoRecordId: pmoRecordId,
      taskId: result.taskId,
      documentation: result.output,
      documentationArtifacts: result.documentationArtifacts,
      codebaseMetrics: result.codebaseMetrics,
      subAgentResults: result.subAgentResults?.map(subResult => ({
        agentType: subResult.agentType,
        assignment: subResult.assignment,
        success: subResult.success,
        outputLength: subResult.output.length,
        ...(subResult.error && { error: subResult.error })
      })),
      outputDocumentIds: result.outputDocumentIds
    });

  } catch (error: any) {
    console.error('Codebase documentation API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error processing codebase documentation request' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/codebase-documentation
 * 
 * Returns information about the codebase documentation service
 */
export async function GET() {
  return NextResponse.json({
    service: 'Codebase Documentation Orchestrator',
    version: '1.0.0',
    description: 'Generates comprehensive documentation for codebases using specialized sub-agents',
    supportedFormats: ['markdown', 'html', 'pdf'],
    supportedScopes: ['full', 'partial', 'specific'],
    subAgents: [
      'project-overview-analyst',
      'architecture-analyst',
      'component-mapper',
      'business-logic-analyst',
      'data-flow-analyst',
      'technical-environment-analyst',
      'dependencies-analyst',
      'api-documentation-specialist',
      'configuration-analyst'
    ],
    endpoints: {
      'POST /api/codebase-documentation': 'Generate codebase documentation',
      'GET /api/codebase-documentation': 'Service information',
      'GET /api/codebase-documentation/file-system': 'Browse file system (planned)',
      'POST /api/codebase-documentation/analyze': 'Analyze codebase structure (planned)'
    }
  });
}
