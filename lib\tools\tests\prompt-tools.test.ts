/**
 * Prompt Tools Test
 *
 * This file demonstrates how to use the prompt interpreter and optimizer tools.
 * You can run this test with:
 *
 * ```
 * npx ts-node lib/tools/tests/prompt-tools.test.ts
 * ```
 */

import { promptInterpreterTool } from '../promptInterpreterTool';
import { promptOptimizerTool } from '../promptOptimizerTool';

async function testPromptTools() {
  console.log('=== Testing Prompt Tools ===\n');

  // Sample prompt to optimize
  const originalPrompt = "Write a blog post about artificial intelligence";
  console.log(`Original Prompt: "${originalPrompt}"\n`);

  try {
    // Step 1: Interpret the prompt to get criteria
    console.log('Step 1: Interpreting prompt to generate criteria...');
    const interpretResult = await promptInterpreterTool.interpretPrompt({
      prompt: originalPrompt
    });

    if (!interpretResult.success) {
      throw new Error(`Failed to interpret prompt: ${interpretResult.error}`);
    }

    console.log('Generated Criteria:');
    interpretResult.criteria.forEach((criterion, index) => {
      console.log(`${index + 1}. ${criterion}`);
    });
    console.log();

    // Step 2A: Optimize the prompt using the criteria (without explanation)
    console.log('Step 2A: Optimizing prompt without explanation...');
    const optimizeResultNoExplanation = await promptOptimizerTool.optimizePrompt({
      originalPrompt,
      criteria: interpretResult.criteria,
      includeExplanation: false
    });

    if (!optimizeResultNoExplanation.success) {
      throw new Error(`Failed to optimize prompt: ${optimizeResultNoExplanation.error}`);
    }

    console.log('Optimized Prompt (without explanation):');
    console.log(`"${optimizeResultNoExplanation.optimizedPrompt}"\n`);

    // Step 2B: Optimize the prompt using the criteria (with explanation)
    console.log('Step 2B: Optimizing prompt with explanation...');
    const optimizeResult = await promptOptimizerTool.optimizePrompt({
      originalPrompt,
      criteria: interpretResult.criteria,
      includeExplanation: true
    });

    if (!optimizeResult.success) {
      throw new Error(`Failed to optimize prompt: ${optimizeResult.error}`);
    }

    console.log('Optimized Prompt:');
    console.log(`"${optimizeResult.optimizedPrompt}"\n`);

    if (optimizeResult.explanation) {
      console.log('Explanation of Changes:');
      console.log(optimizeResult.explanation);
      console.log();
    }

    console.log('=== Test Completed Successfully ===');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testPromptTools();
