import { NextResponse } from "next/server";
import { llmTool } from "../../../lib/tools/llm-tool";

/**
 * API route for LLM processing
 */
export async function POST(request) {
  try {
    const {
      prompt,
      context,
      model = "gpt-4o",
      provider = "openai",
      content,
      maxChunkSize = 4000,
      maxChunks = 10
    } = await request.json();

    // Validate required parameters
    if (!prompt && !content) {
      return NextResponse.json({
        success: false,
        error: "Either prompt or content is required"
      }, { status: 400 });
    }

    let result;

    // If content is provided, process it as large content
    if (content) {
      result = await llmTool.processLargeContent({
        content,
        prompt,
        context,
        model,
        provider,
        maxChunkSize,
        maxChunks
      });
    } else {
      // Otherwise, process as a single prompt
      result = await llmTool.processContent({
        prompt,
        context,
        model,
        provider
      });
    }

    return NextResponse.json({
      success: true,
      result
    });
  } catch (error) {
    console.error("Error in LLM API:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred during LLM processing"
    }, { status: 500 });
  }
}

/**
 * API route for getting available LLM models
 */
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get("provider") || "openai";

    const models = llmTool.getAvailableModels(provider);

    return NextResponse.json({
      success: true,
      provider,
      models
    });
  } catch (error) {
    console.error("Error getting LLM models:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred while retrieving LLM models"
    }, { status: 500 });
  }
}
