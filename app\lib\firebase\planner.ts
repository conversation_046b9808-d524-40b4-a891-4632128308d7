import {
  collection,
  doc,
  setDoc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db, retryOperation } from './config';
import { User, Project, Task, BacklogItem, BacklogComment, PersonalItem, TaskComment } from '../../../admin/planner/types';
import { handleDomainAuthError, isDomainAuthError } from './domainAuth';

// Collection references
const usersCollection = collection(db, 'users');
const projectsCollection = collection(db, 'projects');
const tasksCollection = collection(db, 'tasks');

// Helper function to get task comments collection
const getTaskCommentsCollection = (taskId: string) => {
  return collection(db, `tasks/${taskId}/comments`);
};

// Helper function to convert Firestore timestamps to Date objects
const convertTimestamps = (obj: any): any => {
  if (!obj) return obj;

  console.log('Converting timestamps for:', obj);

  // Handle arrays
  if (Array.isArray(obj)) {
    console.log('Converting timestamps for array:', obj);
    return obj.map(item => convertTimestamps(item));
  }

  // Handle objects
  if (typeof obj === 'object') {
    const result = { ...obj };

    Object.keys(result).forEach(key => {
      if (result[key] instanceof Timestamp) {
        result[key] = result[key].toDate();
      } else if (Array.isArray(result[key])) {
        // Process arrays
        console.log(`Converting timestamps for array property ${key}:`, result[key]);
        result[key] = result[key].map((item: any) =>
          item instanceof Timestamp ? item.toDate() : convertTimestamps(item)
        );
      } else if (result[key] && typeof result[key] === 'object') {
        result[key] = convertTimestamps(result[key]);
      }
    });

    return result;
  }

  // Return primitive values as is
  return obj;
};

// Helper function to prepare data for Firestore (convert Date to Timestamp)
const prepareForFirestore = (obj: any): any => {
  if (!obj) return obj;

  console.log('prepareForFirestore called with:', obj);

  // Handle arrays
  if (Array.isArray(obj)) {
    console.log('Processing array in prepareForFirestore');
    return obj.map(item => prepareForFirestore(item));
  }

  // Handle objects
  if (typeof obj === 'object') {
    const result = { ...obj };

    Object.keys(result).forEach(key => {
      if (result[key] instanceof Date) {
        result[key] = Timestamp.fromDate(result[key]);
      } else if (Array.isArray(result[key])) {
        // Process arrays
        console.log(`Processing array property ${key} in prepareForFirestore:`, result[key]);
        result[key] = result[key].map((item: any) => prepareForFirestore(item));
      } else if (result[key] && typeof result[key] === 'object' && !(result[key] instanceof Timestamp)) {
        result[key] = prepareForFirestore(result[key]);
      }
    });

    return result;
  }

  // Return primitive values as is
  return obj;
};

// User functions
export const getUsers = async (): Promise<User[]> => {
  try {
    return await retryOperation(async () => {
      const snapshot = await getDocs(usersCollection);

      const users = snapshot.docs.map(doc => ({
        id: doc.id,
        ...convertTimestamps(doc.data())
      } as User));

      return users;
    });
  } catch (error) {
    console.error('Error fetching users with retry:', error);

    // Check if this is a domain authorization error
    if (isDomainAuthError(error)) {
      handleDomainAuthError(error);
      console.error('Firebase domain authorization error detected when fetching users');
    }

    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
};

export const getUserById = async (userId: string): Promise<User | null> => {
  const docRef = doc(usersCollection, userId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...convertTimestamps(docSnap.data())
    } as User;
  } else {
    return null;
  }
};

export const addUser = async (userData: Omit<User, 'id'>): Promise<string> => {
  console.log('Adding user to Firestore:', userData);

  // Prepare data for Firestore
  const preparedData = {
    ...prepareForFirestore(userData),
    createdAt: serverTimestamp()
  };

  console.log('Prepared user data for Firestore:', preparedData);

  // Add to Firestore
  const docRef = await addDoc(usersCollection, preparedData);
  console.log(`User added to Firestore with ID: ${docRef.id}`);

  return docRef.id;
};

export const updateUser = async (userId: string, userData: Partial<User>): Promise<void> => {
  const userRef = doc(usersCollection, userId);
  await updateDoc(userRef, {
    ...prepareForFirestore(userData),
    updatedAt: serverTimestamp()
  });
};

// Project functions
export const getProjects = async (userEmail?: string): Promise<Project[]> => {
  try {
    return await retryOperation(async () => {
      // If no user email is provided, return an empty array
      if (!userEmail) {
        return [];
      }

      // System admin can see all projects
      const isSystemAdmin = userEmail === '<EMAIL>';

      // Get all projects
      const projectsQuery = query(
        projectsCollection,
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(projectsQuery);

      // Process each project document
      let projects = snapshot.docs.map(doc => {
        const data = doc.data();

        // Convert timestamps and return the project
        const convertedData = convertTimestamps(data);
        return {
          id: doc.id,
          ...convertedData
        } as Project;
      });

      // If user is system admin, return all projects
      if (isSystemAdmin) {
        return projects;
      }

      // Otherwise, filter projects based on user access
      // First get the user's ID
      const userQuery = query(usersCollection, where('email', '==', userEmail));
      const userSnapshot = await getDocs(userQuery);

      if (userSnapshot.empty) {
        return [];
      }

      const userId = userSnapshot.docs[0].id;

      // Filter projects where user is owner or member
      const filteredProjects = projects.filter(project => {
        const isOwner = project.owner === userEmail;
        const isMember = project.members && Array.isArray(project.members) && project.members.includes(userId);

        return isOwner || isMember;
      });

      return filteredProjects;
    });
  } catch (error) {
    console.error('Error fetching projects with retry:', error);

    // Check if this is a domain authorization error
    if (isDomainAuthError(error)) {
      handleDomainAuthError(error);
    }

    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
};

export const getProjectById = async (projectId: string): Promise<Project | null> => {
  const docRef = doc(projectsCollection, projectId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...convertTimestamps(docSnap.data())
    } as Project;
  } else {
    return null;
  }
};

export const addProject = async (projectData: Omit<Project, 'id'>): Promise<string> => {
  const docRef = await addDoc(projectsCollection, {
    ...prepareForFirestore(projectData),
    createdAt: serverTimestamp()
  });
  return docRef.id;
};

/**
 * Normalizes array data to ensure consistent format regardless of input type
 * @param data - The data to normalize (can be array, string, object, etc.)
 * @returns Normalized array of strings
 */
const normalizeArrayData = (data: any): string[] => {
  if (!data) return [];

  if (Array.isArray(data)) {
    return data;
  } else if (typeof data === 'string') {
    try {
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      return data.split(',').map(item => item.trim());
    } catch (e) {
      return data.split(',').map(item => item.trim());
    }
  } else if (typeof data === 'object') {
    return Object.values(data);
  }

  return [];
};

/**
 * Updates a project in Firestore with proper handling of all fields
 *
 * @param projectId - The ID of the project to update
 * @param projectData - The project data to update
 * @returns Promise resolving when update is complete
 */
export const updateProject = async (projectId: string, projectData: Partial<Project>): Promise<void> => {
  try {
    // Validate inputs
    if (!projectId) {
      throw new Error("Project ID is required");
    }

    if (!projectData) {
      throw new Error("Project data is required");
    }

    console.log("DIRECT UPDATE - Updating project with ID:", projectId);
    console.log("DIRECT UPDATE - Original project data:", projectData);
    console.log("DIRECT UPDATE - Description in original data:", projectData.description);

    // Reference to the project document
    const projectRef = doc(projectsCollection, projectId);

    // Create a clean update object with explicit handling of each field
    const updateData: any = {
      // Add updated timestamp
      updatedAt: serverTimestamp()
    };

    // Only include fields that are present in projectData
    if (projectData.name !== undefined) updateData.name = projectData.name;
    if (projectData.description !== undefined) updateData.description = projectData.description;
    if (projectData.startDate !== undefined) updateData.startDate = Timestamp.fromDate(new Date(projectData.startDate));
    if (projectData.endDate !== undefined) updateData.endDate = Timestamp.fromDate(new Date(projectData.endDate));
    if (projectData.owner !== undefined) updateData.owner = projectData.owner;
    if (projectData.status !== undefined) updateData.status = projectData.status;

    // Handle array fields with normalization
    if (projectData.categories !== undefined) {
      updateData.categories = normalizeArrayData(projectData.categories);
    }

    if (projectData.members !== undefined) {
      updateData.members = normalizeArrayData(projectData.members);
    }

    console.log("DIRECT UPDATE - Final update data being sent to Firestore:", updateData);
    console.log("DIRECT UPDATE - Description being saved:", updateData.description);

    // Perform the update operation directly without additional processing
    await updateDoc(projectRef, updateData);
    console.log('DIRECT UPDATE - Project updated successfully');

    // Verify the update
    const updatedDoc = await getDoc(projectRef);
    if (updatedDoc.exists()) {
      const updatedData = updatedDoc.data();
      console.log('DIRECT UPDATE - Updated project data:', updatedData);
      console.log('DIRECT UPDATE - Updated description:', updatedData.description);
    }
  } catch (error) {
    console.error("DIRECT UPDATE - Error updating project:", error);
    throw error; // Re-throw to allow handling by caller
  }
};

/**
 * Updates specific fields of a project in Firestore
 * Use this for partial updates to avoid overwriting existing data
 *
 * @param projectId - The ID of the project to update
 * @param fieldUpdates - Object containing only the fields to update
 * @returns Promise resolving when update is complete
 */
export const updateProjectFields = async (projectId: string, fieldUpdates: any): Promise<void> => {
  try {
    const projectRef = doc(projectsCollection, projectId);

    // Process any array fields that need normalization
    const processedUpdates = { ...fieldUpdates };

    if ('categories' in fieldUpdates) {
      processedUpdates.categories = normalizeArrayData(fieldUpdates.categories);
    }

    if ('members' in fieldUpdates) {
      processedUpdates.members = normalizeArrayData(fieldUpdates.members);
    }

    // Add update timestamp
    processedUpdates.updatedAt = serverTimestamp();

    // Prepare data for Firestore
    const dataForFirestore = prepareForFirestore(processedUpdates);

    await updateDoc(projectRef, dataForFirestore);
  } catch (error) {
    console.error("Error updating project fields:", error);
    throw error;
  }
};

export const deleteProject = async (projectId: string): Promise<void> => {
  // First, delete all tasks associated with this project
  const tasksQuery = query(tasksCollection, where('projectId', '==', projectId));
  const taskSnapshot = await getDocs(tasksQuery);

  const deletePromises = taskSnapshot.docs.map(taskDoc =>
    deleteDoc(doc(tasksCollection, taskDoc.id))
  );

  await Promise.all(deletePromises);

  // Then delete the project
  const projectRef = doc(projectsCollection, projectId);
  await deleteDoc(projectRef);
};

// Task functions
export const getTasks = async (projectId?: string): Promise<Task[]> => {
  try {
    return await retryOperation(async () => {
      let tasksQuery;

      if (projectId) {
        tasksQuery = query(
          tasksCollection,
          where('projectId', '==', projectId),
          orderBy('createdAt', 'desc')
        );
      } else {
        tasksQuery = query(
          tasksCollection,
          orderBy('createdAt', 'desc')
        );
      }

      const snapshot = await getDocs(tasksQuery);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...convertTimestamps(doc.data())
      } as Task));
    });
  } catch (error) {
    console.error('Error fetching tasks with retry:', error);

    // Check if this is a domain authorization error
    if (isDomainAuthError(error)) {
      handleDomainAuthError(error);
    }

    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
};

export const getTaskById = async (taskId: string): Promise<Task | null> => {
  const docRef = doc(tasksCollection, taskId);
  const docSnap = await getDoc(docRef);

  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...convertTimestamps(docSnap.data())
    } as Task;
  } else {
    return null;
  }
};

export const addTask = async (taskData: Omit<Task, 'id'>): Promise<string> => {
  const docRef = await addDoc(tasksCollection, {
    ...prepareForFirestore(taskData),
    createdAt: serverTimestamp()
  });
  return docRef.id;
};

export const updateTask = async (taskId: string, taskData: Partial<Task>): Promise<void> => {
  const taskRef = doc(tasksCollection, taskId);
  await updateDoc(taskRef, {
    ...prepareForFirestore(taskData),
    updatedAt: serverTimestamp()
  });
};

// Task comments functions
export const getTaskComments = async (taskId: string): Promise<TaskComment[]> => {
  try {
    return await retryOperation(async () => {
      const commentsQuery = query(
        getTaskCommentsCollection(taskId),
        orderBy('createdAt', 'asc')
      );

      console.log(`Fetching comments for task ${taskId}...`);
      const snapshot = await getDocs(commentsQuery);
      console.log(`Retrieved ${snapshot.docs.length} comments for task ${taskId}`);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...convertTimestamps(doc.data())
      } as TaskComment));
    });
  } catch (error) {
    console.error(`Error fetching comments for task ${taskId}:`, error);
    throw error;
  }
};

export const addTaskComment = async (taskId: string, content: string, createdBy: string): Promise<string> => {
  try {
    const commentData = {
      taskId,
      content,
      createdBy,
      createdAt: serverTimestamp()
    };

    const docRef = await addDoc(getTaskCommentsCollection(taskId), commentData);
    console.log(`Added comment ${docRef.id} to task ${taskId}`);
    return docRef.id;
  } catch (error) {
    console.error(`Error adding comment to task ${taskId}:`, error);
    throw error;
  }
};

export const deleteTask = async (taskId: string): Promise<void> => {
  const taskRef = doc(tasksCollection, taskId);
  await deleteDoc(taskRef);
};

// Personal Items functions

// Get personal items collection reference for a user
const getPersonalItemsCollection = (userEmail: string) => {
  return collection(db, 'users', userEmail, 'items');
};

// Get personal items for a user
export const getPersonalItems = async (userEmail: string): Promise<PersonalItem[]> => {
  try {
    return await retryOperation(async () => {
      console.log(`Fetching personal items for user ${userEmail}`);

      // Validate userEmail
      if (!userEmail) {
        console.error('Invalid userEmail provided to getPersonalItems');
        throw new Error('Invalid user email');
      }

      const personalItemsCollection = getPersonalItemsCollection(userEmail);
      const personalItemsQuery = query(
        personalItemsCollection,
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(personalItemsQuery);
      console.log(`Retrieved ${snapshot.docs.length} personal items for user ${userEmail}`);

      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...convertTimestamps(doc.data())
      } as PersonalItem));
    });
  } catch (error) {
    console.error(`Error fetching personal items for user ${userEmail}:`, error);

    // Check if this is a domain authorization error
    if (isDomainAuthError(error)) {
      handleDomainAuthError(error);
      console.error('Firebase domain authorization error detected when fetching personal items');
    }

    // Return empty array instead of throwing to prevent UI from breaking
    return [];
  }
};

// Add a personal item for a user
export const addPersonalItem = async (itemData: Omit<PersonalItem, 'id'>): Promise<string> => {
  try {
    console.log('Adding personal item to Firestore:', itemData);

    if (!itemData.userId) {
      throw new Error('User email is required for personal items');
    }

    // Prepare data for Firestore
    const preparedData = {
      ...prepareForFirestore(itemData),
      createdAt: serverTimestamp()
    };

    // Add to Firestore
    const docRef = await addDoc(getPersonalItemsCollection(itemData.userId), preparedData);
    console.log(`Personal item added with ID: ${docRef.id}`);

    return docRef.id;
  } catch (error) {
    console.error('Error adding personal item:', error);
    throw error;
  }
};

// Update a personal item
export const updatePersonalItem = async (itemId: string, userId: string, itemData: Partial<PersonalItem>): Promise<void> => {
  try {
    console.log(`Updating personal item ${itemId} for user ${userId}`);

    const itemRef = doc(getPersonalItemsCollection(userId), itemId);
    await updateDoc(itemRef, {
      ...prepareForFirestore(itemData),
      updatedAt: serverTimestamp()
    });

    console.log(`Personal item ${itemId} updated successfully`);
  } catch (error) {
    console.error(`Error updating personal item ${itemId}:`, error);
    throw error;
  }
};

// Delete a personal item
export const deletePersonalItem = async (itemId: string, userId: string): Promise<void> => {
  try {
    console.log(`Deleting personal item ${itemId} for user ${userId}`);

    const itemRef = doc(getPersonalItemsCollection(userId), itemId);
    await deleteDoc(itemRef);

    console.log(`Personal item ${itemId} deleted successfully`);
  } catch (error) {
    console.error(`Error deleting personal item ${itemId}:`, error);
    throw error;
  }
};

/**
 * Initialize a planner context for a specific user
 * This function creates a context object similar to what the usePlanner hook provides
 * but for server-side usage (like in API routes)
 * @param userId - The user's ID (email)
 * @returns - A planner context object with methods for managing projects and tasks
 */
export const initializePlannerContextForUser = async (userId: string): Promise<any> => {
  try {
    // Fetch data for the user
    const usersData = await getUsers();
    const projectsData = await getProjects(userId);
    const tasksData = await getTasks();

    // Create methods similar to those in the PlannerContext
    const createProject = async (project: Omit<Project, 'id'>): Promise<string> => {
      return await addProject(project);
    };

    // Use different variable names to avoid naming conflicts
    const updateProjectFn = async (projectId: string, project: Partial<Project>): Promise<void> => {
      await updateProject(projectId, project);
    };

    const createTask = async (task: Omit<Task, 'id'>): Promise<string> => {
      return await addTask(task);
    };

    const updateTaskFn = async (taskId: string, task: Partial<Task>): Promise<void> => {
      await updateTask(taskId, task);
    };

    // Return a context object with data and methods
    return {
      users: usersData,
      projects: projectsData,
      tasks: tasksData,
      createProject,
      updateProject: updateProjectFn,
      createTask,
      updateTask: updateTaskFn,
      // Add other methods as needed
    };
  } catch (error) {
    console.error('Error initializing planner context for user:', error);
    return null;
  }
};

// Initialize with system admin user
export const initializeUsers = async (): Promise<void> => {
  // Check if users already exist
  const snapshot = await getDocs(usersCollection);
  if (snapshot.docs.length === 0) {
    // Add admin user
    await setDoc(doc(usersCollection, 'admin'), {
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      avatar: '/avatars/admin.png',
      availability: 'Full-time',
      isAuthorized: true,
      createdAt: serverTimestamp()
    });
  } else {
    // Make sure the system admin is always authorized
    const adminQuery = query(usersCollection, where('email', '==', '<EMAIL>'));
    const adminSnapshot = await getDocs(adminQuery);

    if (adminSnapshot.empty) {
      // Create the admin user if it doesn't exist
      await addDoc(usersCollection, {
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        avatar: '/avatars/admin.png',
        availability: 'Full-time',
        isAuthorized: true,
        createdAt: serverTimestamp()
      });
    } else {
      // Update the admin user to ensure it's authorized
      const adminDoc = adminSnapshot.docs[0];
      await updateDoc(doc(usersCollection, adminDoc.id), {
        role: 'admin',
        isAuthorized: true
      });
    }
  }
};

// Project access management functions
export const checkUserHasProjectAccess = async (email: string, projectId: string): Promise<boolean> => {
  try {
    // System admin has access to all projects
    if (email === '<EMAIL>') {
      return true;
    }

    // Get the project
    const projectRef = doc(projectsCollection, projectId);
    const projectSnap = await getDoc(projectRef);

    if (!projectSnap.exists()) {
      return false;
    }

    const projectData = projectSnap.data();

    // Check if user is the owner
    if (projectData.owner === email) {
      return true;
    }

    // Check if user is a member
    // First get the user's ID
    const userQuery = query(usersCollection, where('email', '==', email));
    const userSnapshot = await getDocs(userQuery);

    if (userSnapshot.empty) {
      return false;
    }

    const userId = userSnapshot.docs[0].id;

    // Check if user is in the members array
    return projectData.members && Array.isArray(projectData.members) && projectData.members.includes(userId);
  } catch (error) {
    console.error('Error checking project access:', error);
    return false;
  }
};

// User access management functions
export const checkIsSystemAdmin = async (email: string): Promise<boolean> => {
  // Only <EMAIL> is the system admin
  return email === '<EMAIL>';
};

export const checkUserAuthorized = async (email: string): Promise<boolean> => {
  // System admin is always authorized
  if (email === '<EMAIL>') {
    return true;
  }

  // Check if user exists and is authorized
  const userQuery = query(usersCollection, where('email', '==', email));
  const userSnapshot = await getDocs(userQuery);

  if (userSnapshot.empty) {
    return false;
  }

  const userData = userSnapshot.docs[0].data();
  return userData.isAuthorized === true;
};

export const addUserAccess = async (email: string): Promise<string> => {
  // Check if user already exists
  const userQuery = query(usersCollection, where('email', '==', email));
  const userSnapshot = await getDocs(userQuery);

  if (!userSnapshot.empty) {
    // User exists, update to grant access
    const userDoc = userSnapshot.docs[0];
    await updateDoc(doc(usersCollection, userDoc.id), {
      isAuthorized: true,
      updatedAt: serverTimestamp()
    });
    return userDoc.id;
  } else {
    // Create new user with access
    const newUser = {
      name: email.split('@')[0], // Use part of email as name
      email,
      role: 'user',
      avatar: '/avatars/default.png',
      availability: 'Full-time',
      isAuthorized: true,
      createdAt: serverTimestamp()
    };

    const docRef = await addDoc(usersCollection, newUser);
    return docRef.id;
  }
};

export const removeUserAccess = async (userId: string): Promise<string> => {
  // Don't allow removing the system admin
  const userDoc = await getDoc(doc(usersCollection, userId));
  if (userDoc.exists() && userDoc.data().email === '<EMAIL>') {
    throw new Error('Cannot remove access from the system administrator');
  }

  // Update user to revoke access
  await updateDoc(doc(usersCollection, userId), {
    isAuthorized: false,
    updatedAt: serverTimestamp()
  });

  return userId;
};

// Backlog Items Collection Reference
const getBacklogItemsCollection = (projectId: string) => {
  return collection(db, 'projects', projectId, 'backlogItems');
};

// Backlog Comments Collection Reference
const getBacklogCommentsCollection = (projectId: string, backlogItemId: string) => {
  return collection(db, 'projects', projectId, 'backlogItems', backlogItemId, 'comments');
};

// Backlog Item Functions
export const getBacklogItems = async (projectId: string): Promise<BacklogItem[]> => {
  try {
    return await retryOperation(async () => {
      // Validate projectId
      if (!projectId) {
        console.error('Invalid projectId provided to getBacklogItems:', projectId);
        throw new Error('Invalid project ID');
      }

      console.log(`Fetching backlog items for project ${projectId}`);

      try {
        const backlogItemsCollection = getBacklogItemsCollection(projectId);
        console.log('Backlog items collection path:', backlogItemsCollection.path);

        const backlogItemsQuery = query(
          backlogItemsCollection,
          orderBy('createdAt', 'desc')
        );

        const snapshot = await getDocs(backlogItemsQuery);
        console.log(`Retrieved ${snapshot.docs.length} backlog items for project ${projectId}`);

        const backlogItems = snapshot.docs.map(doc => {
          const data = doc.data();
          console.log(`Processing backlog item ${doc.id}:`, data);
          const convertedData = convertTimestamps(data);
          return {
            id: doc.id,
            ...convertedData
          } as BacklogItem;
        });

        return backlogItems;
      } catch (innerError) {
        console.error(`Error in getBacklogItems inner operation for project ${projectId}:`, innerError);
        throw innerError;
      }
    });
  } catch (error) {
    console.error(`Error fetching backlog items for project ${projectId}:`, error);
    if (isDomainAuthError(error)) {
      handleDomainAuthError(error);
      console.error('Firebase domain authorization error detected when fetching backlog items');
    }
    // Return empty array but still throw the error to be handled by the caller
    throw error;
  }
};

export const addBacklogItem = async (projectId: string, backlogItem: Omit<BacklogItem, 'id'>): Promise<string> => {
  try {
    // Set initial status to Proposed
    const newBacklogItem = {
      ...backlogItem,
      status: 'Proposed',
      votes: 0,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    };

    const docRef = await addDoc(getBacklogItemsCollection(projectId), newBacklogItem);
    console.log(`Added backlog item with ID: ${docRef.id}`);
    return docRef.id;
  } catch (error) {
    console.error('Error adding backlog item:', error);
    throw error;
  }
};

export const updateBacklogItem = async (projectId: string, backlogItemId: string, updates: Partial<BacklogItem>): Promise<void> => {
  try {
    const backlogItemRef = doc(getBacklogItemsCollection(projectId), backlogItemId);
    await updateDoc(backlogItemRef, {
      ...updates,
      updatedAt: serverTimestamp()
    });
    console.log(`Updated backlog item ${backlogItemId}`);
  } catch (error) {
    console.error(`Error updating backlog item ${backlogItemId}:`, error);
    throw error;
  }
};

export const deleteBacklogItem = async (projectId: string, backlogItemId: string): Promise<void> => {
  try {
    // Delete all comments first
    const commentsQuery = query(getBacklogCommentsCollection(projectId, backlogItemId));
    const commentsSnapshot = await getDocs(commentsQuery);

    const deletePromises = commentsSnapshot.docs.map(commentDoc =>
      deleteDoc(doc(getBacklogCommentsCollection(projectId, backlogItemId), commentDoc.id))
    );

    await Promise.all(deletePromises);

    // Then delete the backlog item
    await deleteDoc(doc(getBacklogItemsCollection(projectId), backlogItemId));
    console.log(`Deleted backlog item ${backlogItemId}`);
  } catch (error) {
    console.error(`Error deleting backlog item ${backlogItemId}:`, error);
    throw error;
  }
};

export const approveBacklogItem = async (projectId: string, backlogItemId: string, approverEmail: string): Promise<void> => {
  try {
    const backlogItemRef = doc(getBacklogItemsCollection(projectId), backlogItemId);
    await updateDoc(backlogItemRef, {
      status: 'Approved',
      approvedBy: approverEmail,
      approvedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    console.log(`Approved backlog item ${backlogItemId}`);
  } catch (error) {
    console.error(`Error approving backlog item ${backlogItemId}:`, error);
    throw error;
  }
};

export const rejectBacklogItem = async (projectId: string, backlogItemId: string, approverEmail: string): Promise<void> => {
  try {
    const backlogItemRef = doc(getBacklogItemsCollection(projectId), backlogItemId);
    await updateDoc(backlogItemRef, {
      status: 'Rejected',
      approvedBy: approverEmail,
      approvedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    console.log(`Rejected backlog item ${backlogItemId}`);
  } catch (error) {
    console.error(`Error rejecting backlog item ${backlogItemId}:`, error);
    throw error;
  }
};

export const voteForBacklogItem = async (projectId: string, backlogItemId: string): Promise<void> => {
  try {
    const backlogItemRef = doc(getBacklogItemsCollection(projectId), backlogItemId);
    const backlogItemDoc = await getDoc(backlogItemRef);

    if (!backlogItemDoc.exists()) {
      throw new Error(`Backlog item ${backlogItemId} not found`);
    }

    const currentVotes = backlogItemDoc.data().votes || 0;

    await updateDoc(backlogItemRef, {
      votes: currentVotes + 1,
      updatedAt: serverTimestamp()
    });

    console.log(`Voted for backlog item ${backlogItemId}`);
  } catch (error) {
    console.error(`Error voting for backlog item ${backlogItemId}:`, error);
    throw error;
  }
};

export const addBacklogComment = async (projectId: string, backlogItemId: string, comment: Omit<BacklogComment, 'id'>): Promise<string> => {
  try {
    const newComment = {
      ...comment,
      createdAt: serverTimestamp()
    };

    const docRef = await addDoc(getBacklogCommentsCollection(projectId, backlogItemId), newComment);
    console.log(`Added comment with ID: ${docRef.id} to backlog item ${backlogItemId}`);
    return docRef.id;
  } catch (error) {
    console.error(`Error adding comment to backlog item ${backlogItemId}:`, error);
    throw error;
  }
};

export const getBacklogComments = async (projectId: string, backlogItemId: string): Promise<BacklogComment[]> => {
  try {
    const commentsQuery = query(
      getBacklogCommentsCollection(projectId, backlogItemId),
      orderBy('createdAt', 'asc')
    );

    const snapshot = await getDocs(commentsQuery);

    const comments = snapshot.docs.map(doc => {
      const data = doc.data();
      const convertedData = convertTimestamps(data);
      return {
        id: doc.id,
        ...convertedData
      } as BacklogComment;
    });

    return comments;
  } catch (error) {
    console.error(`Error fetching comments for backlog item ${backlogItemId}:`, error);
    return [];
  }
};

export const convertBacklogItemToTask = async (projectId: string, backlogItemId: string): Promise<string> => {
  try {
    // Get the backlog item
    const backlogItemRef = doc(getBacklogItemsCollection(projectId), backlogItemId);
    const backlogItemDoc = await getDoc(backlogItemRef);

    if (!backlogItemDoc.exists()) {
      throw new Error(`Backlog item ${backlogItemId} not found`);
    }

    const backlogItem = {
      id: backlogItemDoc.id,
      ...convertTimestamps(backlogItemDoc.data())
    } as BacklogItem;

    // Only approved backlog items can be converted to tasks
    if (backlogItem.status !== 'Approved') {
      throw new Error(`Backlog item ${backlogItemId} is not approved`);
    }

    // Create a new task from the backlog item
    const newTask: Omit<Task, 'id'> = {
      projectId,
      title: backlogItem.title,
      description: backlogItem.description,
      category: backlogItem.category || 'General',
      status: 'Not Started',
      startDate: new Date(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Default due date: 1 week from now
      assignedTo: [],
      priority: backlogItem.priority,
      dependencies: [],
      createdBy: backlogItem.createdBy,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Add the task
    const taskId = await addTask(newTask);

    // Update the backlog item to mark it as converted
    await updateDoc(backlogItemRef, {
      convertedToTaskId: taskId,
      updatedAt: serverTimestamp()
    });

    console.log(`Converted backlog item ${backlogItemId} to task ${taskId}`);
    return taskId;
  } catch (error) {
    console.error(`Error converting backlog item ${backlogItemId} to task:`, error);
    throw error;
  }
};
