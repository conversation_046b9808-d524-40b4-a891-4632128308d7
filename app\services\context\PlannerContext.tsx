'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  getUsers,
  getProjects,
  getTasks,
  addProject,
  updateProject,
  deleteProject,
  addTask,
  updateTask,
  deleteTask,
  getBacklogItems,
  addBacklogItem,
  updateBacklogItem,
  deleteBacklogItem,
  approveBacklogItem,
  rejectBacklogItem,
  voteForBacklogItem,
  convertBacklogItemToTask,
  getBacklogComments,
  addBacklogComment,
  getTaskComments,
  addTaskComment
} from '../../lib/firebase/planner';
import { User, Project, Task, BacklogItem, BacklogComment, TaskComment } from '../../../admin/planner/types';
import { useAuth } from './AuthContext';

interface PlannerContextType {
  users: User[];
  projects: Project[];
  tasks: Task[];
  backlogItems: Record<string, BacklogItem[]>; // Keyed by projectId
  backlogComments: Record<string, BacklogComment[]>; // Keyed by backlogItemId
  taskComments: Record<string, TaskComment[]>; // Keyed by taskId
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;

  // Project functions
  createProject: (project: Omit<Project, 'id'>) => Promise<string>;
  updateProject: (projectId: string, project: Partial<Project>) => Promise<void>;
  removeProject: (projectId: string) => Promise<void>;

  // Task functions
  createTask: (task: Omit<Task, 'id'>) => Promise<string>;
  updateTask: (taskId: string, task: Partial<Task>) => Promise<void>;
  removeTask: (taskId: string) => Promise<void>;

  // Backlog functions
  fetchBacklogItems: (projectId: string) => Promise<BacklogItem[]>;
  createBacklogItem: (projectId: string, backlogItem: Omit<BacklogItem, 'id' | 'votes' | 'status' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateBacklogItem: (projectId: string, backlogItemId: string, updates: Partial<BacklogItem>) => Promise<void>;
  removeBacklogItem: (projectId: string, backlogItemId: string) => Promise<void>;
  approveBacklogItem: (projectId: string, backlogItemId: string) => Promise<void>;
  rejectBacklogItem: (projectId: string, backlogItemId: string) => Promise<void>;
  voteForBacklogItem: (projectId: string, backlogItemId: string) => Promise<void>;
  convertToTask: (projectId: string, backlogItemId: string) => Promise<string>;

  // Backlog comment functions
  fetchBacklogComments: (projectId: string, backlogItemId: string) => Promise<BacklogComment[]>;
  addBacklogComment: (projectId: string, backlogItemId: string, text: string, createdBy: string) => Promise<string>;

  // Task comment functions
  fetchTaskComments: (taskId: string) => Promise<TaskComment[]>;
  addTaskComment: (taskId: string, content: string, createdBy: string) => Promise<string>;
}

const PlannerContext = createContext<PlannerContextType | undefined>(undefined);

export const usePlanner = () => {
  const context = useContext(PlannerContext);
  if (context === undefined) {
    throw new Error('usePlanner must be used within a PlannerProvider');
  }
  return context;
};

interface PlannerProviderProps {
  children: ReactNode;
}

export const PlannerProvider: React.FC<PlannerProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [backlogItems, setBacklogItems] = useState<Record<string, BacklogItem[]>>({});
  const [backlogComments, setBacklogComments] = useState<Record<string, BacklogComment[]>>({});
  const [taskComments, setTaskComments] = useState<Record<string, TaskComment[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch all data in parallel without excessive logging
        const [usersData, projectsData, tasksData] = await Promise.all([
          // Fetch users
          getUsers().catch(err => {
            console.error('Error fetching users:', err);
            return [] as User[];
          }),

          // Fetch projects
          getProjects(user.email).catch(err => {
            console.error('Error fetching projects:', err);
            setError('Failed to load projects. Please try refreshing the page.');
            return [] as Project[];
          }),

          // Fetch tasks
          getTasks().catch(err => {
            console.error('Error fetching tasks:', err);
            return [] as Task[];
          })
        ]);

        // Update state with all data at once
        setUsers(usersData);
        setProjects(projectsData);
        setTasks(tasksData);
      } catch (err: any) {
        console.error('Error fetching planner data:', err);
        setError(err.message || 'Failed to load planner data. Please try refreshing the page.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  const refreshData = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // Fetch all data in parallel without excessive logging
      const [usersData, projectsData, tasksData] = await Promise.all([
        // Fetch users
        getUsers().catch(err => {
          console.error('Error refreshing users:', err);
          return [] as User[];
        }),

        // Fetch projects
        getProjects(user.email).catch(err => {
          console.error('Error refreshing projects:', err);
          setError('Failed to refresh projects. Please try again.');
          return [] as Project[];
        }),

        // Fetch tasks
        getTasks().catch(err => {
          console.error('Error refreshing tasks:', err);
          return [] as Task[];
        })
      ]);

      // Update state with all data at once
      setUsers(usersData);
      setProjects(projectsData);
      setTasks(tasksData);
    } catch (err: any) {
      console.error('Error refreshing planner data:', err);
      setError(err.message || 'Failed to refresh planner data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const createProject = async (project: Omit<Project, 'id'>): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const projectId = await addProject(project);
      await refreshData();
      return projectId;
    } catch (err: any) {
      console.error('Error creating project:', err);
      setError(err.message || 'Failed to create project');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProjectData = async (projectId: string, project: Partial<Project>): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await updateProject(projectId, project);
      await refreshData();
    } catch (err: any) {
      console.error('Error updating project:', err);
      setError(err.message || 'Failed to update project');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removeProject = async (projectId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await deleteProject(projectId);
      await refreshData();
    } catch (err: any) {
      console.error('Error deleting project:', err);
      setError(err.message || 'Failed to delete project');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createTask = async (task: Omit<Task, 'id'>): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const taskId = await addTask(task);
      await refreshData();
      return taskId;
    } catch (err: any) {
      console.error('Error creating task:', err);
      setError(err.message || 'Failed to create task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateTaskData = async (taskId: string, task: Partial<Task>): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await updateTask(taskId, task);
      await refreshData();
    } catch (err: any) {
      console.error('Error updating task:', err);
      setError(err.message || 'Failed to update task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removeTask = async (taskId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await deleteTask(taskId);
      await refreshData();
    } catch (err: any) {
      console.error('Error deleting task:', err);
      setError(err.message || 'Failed to delete task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Backlog functions
  const fetchBacklogItems = async (projectId: string): Promise<BacklogItem[]> => {
    setLoading(true);
    setError(null);

    try {
      const items = await getBacklogItems(projectId);
      setBacklogItems(prev => ({
        ...prev,
        [projectId]: items
      }));
      return items;
    } catch (err: any) {
      console.error(`Error fetching backlog items for project ${projectId}:`, err);
      setError(err.message || 'Failed to fetch backlog items');
      return [];
    } finally {
      setLoading(false);
    }
  };

  const createBacklogItem = async (
    projectId: string,
    backlogItem: Omit<BacklogItem, 'id' | 'votes' | 'status' | 'createdAt' | 'updatedAt'>
  ): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const backlogItemId = await addBacklogItem(projectId, backlogItem as Omit<BacklogItem, 'id'>);
      await fetchBacklogItems(projectId);
      return backlogItemId;
    } catch (err: any) {
      console.error('Error creating backlog item:', err);
      setError(err.message || 'Failed to create backlog item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateBacklogItemData = async (
    projectId: string,
    backlogItemId: string,
    updates: Partial<BacklogItem>
  ): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await updateBacklogItem(projectId, backlogItemId, updates);
      await fetchBacklogItems(projectId);
    } catch (err: any) {
      console.error(`Error updating backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to update backlog item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removeBacklogItem = async (projectId: string, backlogItemId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await deleteBacklogItem(projectId, backlogItemId);
      await fetchBacklogItems(projectId);
    } catch (err: any) {
      console.error(`Error deleting backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to delete backlog item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const approveBacklogItemData = async (projectId: string, backlogItemId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      // Use the current user's email as the approver
      await approveBacklogItem(projectId, backlogItemId, user?.email || 'unknown');
      await fetchBacklogItems(projectId);
    } catch (err: any) {
      console.error(`Error approving backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to approve backlog item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const rejectBacklogItemData = async (projectId: string, backlogItemId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      // Use the current user's email as the approver
      await rejectBacklogItem(projectId, backlogItemId, user?.email || 'unknown');
      await fetchBacklogItems(projectId);
    } catch (err: any) {
      console.error(`Error rejecting backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to reject backlog item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const voteForBacklogItemData = async (projectId: string, backlogItemId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await voteForBacklogItem(projectId, backlogItemId);
      await fetchBacklogItems(projectId);
    } catch (err: any) {
      console.error(`Error voting for backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to vote for backlog item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const convertToTask = async (projectId: string, backlogItemId: string): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const taskId = await convertBacklogItemToTask(projectId, backlogItemId);
      await Promise.all([
        refreshData(),
        fetchBacklogItems(projectId)
      ]);
      return taskId;
    } catch (err: any) {
      console.error(`Error converting backlog item ${backlogItemId} to task:`, err);
      setError(err.message || 'Failed to convert backlog item to task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Backlog comment functions
  const fetchBacklogComments = async (projectId: string, backlogItemId: string): Promise<BacklogComment[]> => {
    setLoading(true);
    setError(null);

    try {
      const comments = await getBacklogComments(projectId, backlogItemId);
      setBacklogComments(prev => ({
        ...prev,
        [backlogItemId]: comments
      }));
      return comments;
    } catch (err: any) {
      console.error(`Error fetching comments for backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to fetch backlog comments');
      return [];
    } finally {
      setLoading(false);
    }
  };

  const addBacklogCommentData = async (
    projectId: string,
    backlogItemId: string,
    text: string,
    createdBy: string
  ): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const comment: Omit<BacklogComment, 'id'> = {
        backlogItemId,
        text,
        createdBy,
        createdAt: new Date()
      };

      const commentId = await addBacklogComment(projectId, backlogItemId, comment);
      await fetchBacklogComments(projectId, backlogItemId);
      return commentId;
    } catch (err: any) {
      console.error(`Error adding comment to backlog item ${backlogItemId}:`, err);
      setError(err.message || 'Failed to add comment');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    users,
    projects,
    tasks,
    backlogItems,
    backlogComments,
    taskComments,
    loading,
    error,
    refreshData,

    // Project functions
    createProject,
    updateProject: updateProjectData,
    removeProject,

    // Task functions
    createTask,
    updateTask: updateTaskData,
    removeTask,

    // Backlog functions
    fetchBacklogItems,
    createBacklogItem,
    updateBacklogItem: updateBacklogItemData,
    removeBacklogItem,
    approveBacklogItem: approveBacklogItemData,
    rejectBacklogItem: rejectBacklogItemData,
    voteForBacklogItem: voteForBacklogItemData,
    convertToTask,

    // Backlog comment functions
    fetchBacklogComments,
    addBacklogComment: addBacklogCommentData,

    // Task comment functions
    fetchTaskComments: async (taskId: string) => {
      setLoading(true);
      setError(null);

      try {
        const comments = await getTaskComments(taskId);
        setTaskComments(prev => ({
          ...prev,
          [taskId]: comments
        }));
        return comments;
      } catch (err: any) {
        console.error(`Error fetching comments for task ${taskId}:`, err);
        setError(err.message || 'Failed to fetch task comments');
        return [];
      } finally {
        setLoading(false);
      }
    },

    addTaskComment: async (taskId: string, content: string, createdBy: string) => {
      setLoading(true);
      setError(null);

      try {
        const commentId = await addTaskComment(taskId, content, createdBy);
        await getTaskComments(taskId).then(comments => {
          setTaskComments(prev => ({
            ...prev,
            [taskId]: comments
          }));
        });
        return commentId;
      } catch (err: any) {
        console.error('Error adding task comment:', err);
        setError(err.message || 'Failed to add comment');
        throw err;
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <PlannerContext.Provider value={value}>
      {children}
    </PlannerContext.Provider>
  );
};
