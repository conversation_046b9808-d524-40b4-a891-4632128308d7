import { NextRequest, NextResponse } from "next/server";
import { llmTool } from "../../../../lib/tools/llm-tool";
import { markdownRendererTool } from "../../../../lib/tools/markdown-renderer-tool";

/**
 * API route for processing content with web search
 */
export async function POST(request: NextRequest) {
  try {
    // Log the request body for debugging
    console.log('Web search request body:', await request.clone().text());

    // Parse the request body
    const requestBody = await request.json();

    // Check if the request contains a pre-formatted result (for testing)
    if (requestBody.formattedResults) {
      console.log('Received pre-formatted results');

      // Process the pre-formatted results with the markdown renderer tool
      const result = requestBody.formattedResults;
      const formattedResults = markdownRendererTool.preprocessMarkdown(result);

      return NextResponse.json({
        success: true,
        result,
        formattedResults
      });
    }

    const {
      prompt,
      context = "",
      model = "gpt-4.1",
      provider = "openai",
      searchContextSize = "medium",
      userLocation,
      forceWebSearch = false
    } = requestBody;

    // Validate required parameters
    if (!prompt) {
      return NextResponse.json({
        success: false,
        error: "Prompt is required"
      }, { status: 400 });
    }

    // Check if the Brave Search API key is set
    const searchApiKey = process.env.SEARCH_API || process.env.BRAVE_SEARCH_API_KEY;
    if (!searchApiKey) {
      console.warn('Warning: SEARCH_API or BRAVE_SEARCH_API_KEY environment variable is not set. Using mock data for search results.');

      // Return mock data for testing
      const mockResults = `Here are five notable marketing agencies in London:

1. Saatchi & Saatchi: Founded in 1970, Saatchi & Saatchi is a British multinational communications and advertising agency network headquartered in London. (en.wikipedia.org)

2. Dentsu International: A subsidiary of the Japanese advertising and public relations firm Dentsu, Dentsu International is a multinational media and digital marketing communications company headquartered in London. (en.wikipedia.org)

3. MullenLowe Global: Part of the Interpublic Group of Companies, MullenLowe Global is a marketing communications company headquartered in London. (en.wikipedia.org)

4. M&C Saatchi Group: Established in 1995, M&C Saatchi Group is an international communications company headquartered in London, with offices in several other countries. (en.wikipedia.org)

5. Wavemaker: Formed in 2017 through the merger of MEC and Maxus, Wavemaker is a media agency network headquartered in London, operating as part of GroupM and WPP plc. (en.wikipedia.org)

These agencies offer a range of marketing and advertising services to clients both in the UK and internationally.`;

      const formattedResults = markdownRendererTool.preprocessMarkdown(mockResults);

      return NextResponse.json({
        success: true,
        result: mockResults,
        formattedResults
      });
    }

    // Process the content with web search
    const result = await llmTool.processContent({
      prompt,
      context,
      model,
      provider,
      useWebSearch: true,
      webSearchOptions: {
        searchContextSize,
        userLocation,
        forceWebSearch
      }
    });

    // Process the result with the markdown renderer tool
    const formattedResults = markdownRendererTool.preprocessMarkdown(result);

    console.log('Web search result:', { result, formattedResults });

    // Ensure the response format is consistent
    return NextResponse.json({
      success: true,
      result,
      formattedResults
    });
  } catch (error: any) {
    console.error("Error processing web search:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "An error occurred during web search"
    }, { status: 500 });
  }
}