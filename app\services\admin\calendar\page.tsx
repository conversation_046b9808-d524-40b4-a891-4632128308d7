'use client';

import React, { useState, useEffect } from 'react';
import { usePlanner } from '../../../context/PlannerContext';
import { useSession } from 'next-auth/react';
import { Calendar, Filter, RefreshCw, AlertTriangle } from 'lucide-react';
import { CalendarEvent, CalendarViewType, Task, Project, PersonalItem } from '../../../../admin/planner/types';
import CalendarView from '../../../../admin/planner/components/CalendarView';
import GanttChartView from '../../../../admin/planner/components/GanttChartView';
import TabsContextDebugger from '../../../components/TabsContextDebugger';
import CalendarDebug from '../../../../admin/planner/components/CalendarDebug';
import { getPersonalItems } from '../../../lib/firebase/planner';

export default function AdminCalendarPage() {
  const { data: session } = useSession();
  const { projects, tasks, loading, error } = usePlanner();

  const [calendarView, setCalendarView] = useState<CalendarViewType>('month');
  const [showGanttView, setShowGanttView] = useState(false);
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [showOnlyMyTasks, setShowOnlyMyTasks] = useState(false);
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [personalItems, setPersonalItems] = useState<PersonalItem[]>([]);
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [showPersonalItems, setShowPersonalItems] = useState(true);

  // Function to fetch personal items
  const fetchPersonalItems = async () => {
    if (!session?.user?.email) {
      console.log('AdminCalendar: No user email available, skipping personal items fetch');
      return [];
    }

    try {
      console.log(`AdminCalendar: Fetching personal items for user ${session.user.email}`);
      const items = await getPersonalItems(session.user.email);
      console.log(`AdminCalendar: Retrieved ${items.length} personal items`);
      setPersonalItems(items);
      return items;
    } catch (err) {
      console.error('AdminCalendar: Error fetching personal items:', err);
      // Return empty array instead of throwing to prevent UI from breaking
      return [];
    }
  };

  // Load data on component mount or when filters change
  useEffect(() => {
    console.log('AdminCalendar: Filter or selection changed, reloading data');
    loadData();
  }, [selectedProject, showOnlyMyTasks, showPersonalItems]);

  // Update when tasks or projects change
  useEffect(() => {
    console.log(`AdminCalendar: Tasks changed (${tasks.length} tasks), processing events`);
    if (tasks.length > 0) {
      processEventsData();
    }
  }, [tasks]);

  useEffect(() => {
    console.log(`AdminCalendar: Projects changed (${projects.length} projects), processing events`);
    if (projects.length > 0 && tasks.length > 0) {
      processEventsData();
    }
  }, [projects]);

  useEffect(() => {
    console.log(`AdminCalendar: Personal items changed (${personalItems.length} items), processing events`);
    if (personalItems.length > 0) {
      processEventsData();
    }
  }, [personalItems]);

  // Initial load of personal items
  useEffect(() => {
    if (session?.user?.email) {
      fetchPersonalItems();
    }
  }, [session]);

  // Function to load data
  const loadData = async () => {
    setLocalLoading(true);
    setLocalError(null);

    try {
      console.log('AdminCalendar: Loading data...');

      // Always refresh personal items to ensure we have the latest data
      await fetchPersonalItems();

      // Log the current state of tasks
      console.log(`AdminCalendar: Current tasks count = ${tasks.length}`);
      if (tasks.length === 0) {
        console.log('AdminCalendar: No tasks found, you may need to refresh the page');
      }

      // Process tasks and personal items into calendar events
      processEventsData();
    } catch (err: any) {
      console.error('AdminCalendar: Error loading calendar data:', err);
      setLocalError('Failed to load calendar data. Please try again.');
    } finally {
      setLocalLoading(false);
    }
  };

  // Process tasks and personal items into calendar events
  const processEventsData = () => {
    const events: CalendarEvent[] = [];
    const currentUserEmail = session?.user?.email;

    console.log('AdminCalendar: Processing events, current user email =', currentUserEmail);
    console.log('AdminCalendar: Total tasks count =', tasks.length);
    console.log('AdminCalendar: Total personal items count =', personalItems.length);

    // First, log all tasks to debug
    tasks.forEach((task, index) => {
      console.log(`Task ${index}: ID=${task.id}, Title=${task.title}, ProjectID=${task.projectId}`);
    });

    // Filter tasks by selected project and user assignment if needed
    let filteredTasks = selectedProject === 'all'
      ? tasks
      : tasks.filter(task => {
          const matches = task.projectId === selectedProject;
          if (!matches && selectedProject !== 'all') {
            console.log(`Task ${task.id} skipped - wrong project (${task.projectId} vs ${selectedProject})`);
          }
          return matches;
        });

    console.log('AdminCalendar: Filtered tasks by project, count =', filteredTasks.length);

    // Further filter by user assignment if showOnlyMyTasks is true
    if (showOnlyMyTasks && currentUserEmail) {
      filteredTasks = filteredTasks.filter(task => {
        if (!task.assignedTo || !Array.isArray(task.assignedTo)) {
          console.log(`Task ${task.id} skipped - no assignedTo array`);
          return false;
        }

        const isAssigned = task.assignedTo.some(assignee => {
          // Check if assignee is a string (email or ID)
          if (typeof assignee === 'string') {
            const matches = assignee.toLowerCase() === currentUserEmail.toLowerCase();
            if (matches) {
              console.log(`Task ${task.id} is assigned to current user ${currentUserEmail}`);
            }
            return matches;
          }

          // Check if assignee is an object with email property
          if (typeof assignee === 'object' && assignee !== null) {
            const assigneeObj = assignee as any;
            const matches = assigneeObj.email && assigneeObj.email.toLowerCase() === currentUserEmail.toLowerCase();
            if (matches) {
              console.log(`Task ${task.id} is assigned to current user ${currentUserEmail} (object format)`);
            }
            return matches;
          }

          return false;
        });

        if (!isAssigned) {
          console.log(`Task ${task.id} skipped - not assigned to current user`);
        }
        return isAssigned;
      });

      console.log('AdminCalendar: Further filtered tasks by user assignment, count =', filteredTasks.length);
    }

    // Add tasks to events
    filteredTasks.forEach(task => {
      const project = projects.find(p => p.id === task.projectId);

      // Check if task is assigned to current user
      let isAssignedToCurrentUser = false;

      if (currentUserEmail && task.assignedTo && Array.isArray(task.assignedTo)) {
        isAssignedToCurrentUser = task.assignedTo.some(assignee => {
          // It could be an email directly (case insensitive)
          if (typeof assignee === 'string' && assignee.toLowerCase() === currentUserEmail.toLowerCase()) {
            return true;
          }

          // It could be an object with an email property
          if (typeof assignee === 'object' && assignee !== null) {
            const assigneeObj = assignee as any;
            if (assigneeObj.email && assigneeObj.email.toLowerCase() === currentUserEmail.toLowerCase()) {
              return true;
            }
          }

          return false;
        });
      }

      // Ensure dates are valid
      const startDate = task.startDate instanceof Date ? task.startDate : new Date(task.startDate);
      const endDate = task.dueDate instanceof Date ? task.dueDate : new Date(task.dueDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        console.warn(`AdminCalendar: Invalid date for task ${task.id}`, task);
        return; // Skip tasks with invalid dates
      }

      events.push({
        id: task.id,
        title: task.title + (isAssignedToCurrentUser ? ' (Assigned to you)' : ''),
        start: startDate,
        end: endDate,
        color: getTaskColor(task.priority, task.status),
        type: 'task',
        priority: task.priority,
        status: task.status,
        projectId: task.projectId,
        projectName: project?.name || 'Unknown Project'
      });

      console.log(`Added task to calendar: ${task.id} - ${task.title}`);
    });

    // Add personal items if enabled
    if (showPersonalItems && personalItems.length > 0 && currentUserEmail) {
      console.log(`AdminCalendar: Adding ${personalItems.length} personal items to calendar`);

      personalItems.forEach(item => {
        // Create start date with time if available
        let startDate = item.startDate instanceof Date ? new Date(item.startDate) : new Date(item.startDate);
        let endDate = item.endDate instanceof Date ? new Date(item.endDate) : new Date(item.endDate);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          console.warn(`AdminCalendar: Invalid date for personal item ${item.id}`, item);
          return; // Skip items with invalid dates
        }

        // Apply time if available
        if (item.startTime) {
          const [startHours, startMinutes] = item.startTime.split(':').map(Number);
          startDate.setHours(startHours, startMinutes, 0, 0);
        }

        if (item.endTime) {
          const [endHours, endMinutes] = item.endTime.split(':').map(Number);
          endDate.setHours(endHours, endMinutes, 0, 0);
        }

        events.push({
          id: item.id,
          title: item.title + ' (Personal)',
          start: startDate,
          end: endDate,
          color: item.color || '#9333ea', // Default to purple if no color specified
          type: 'personal'
        });

        console.log(`Added personal item to calendar: ${item.id} - ${item.title} (${startDate.toLocaleString()} - ${endDate.toLocaleString()})`);
      });
    }

    console.log('AdminCalendar: Total events to display =', events.length);

    // Only update state if events have changed to prevent unnecessary re-renders
    const currentEventsJson = JSON.stringify(calendarEvents);
    const newEventsJson = JSON.stringify(events);

    if (currentEventsJson !== newEventsJson) {
      console.log('AdminCalendar: Events changed, updating state');
      setCalendarEvents(events);
    } else {
      console.log('AdminCalendar: Events unchanged, skipping state update');
    }
  };

  // Get color based on task priority and status
  const getTaskColor = (priority: string, status: string): string => {
    if (status === 'Complete') return '#10b981'; // Green for completed tasks

    switch (priority) {
      case 'Critical':
        return '#ef4444'; // Red
      case 'High':
        return '#f97316'; // Orange
      case 'Medium':
        return '#3b82f6'; // Blue
      case 'Low':
        return '#6b7280'; // Gray
      default:
        return '#3b82f6'; // Default blue
    }
  };

  // Handle project filter change
  const handleProjectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedProject(e.target.value);
  };

  // Handle view change
  const handleViewChange = (view: CalendarViewType) => {
    setCalendarView(view);
    setShowGanttView(false);
  };

  // Toggle Gantt view
  const handleToggleGanttView = () => {
    setShowGanttView(prev => !prev);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-2xl font-bold mb-6 flex items-center">
          <Calendar className="mr-2 text-purple-400" />
          Project Calendar
        </h1>

        <div className="bg-gray-800 rounded-lg shadow-md p-6 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
            <div className="flex space-x-2">
              <button
                onClick={() => handleViewChange('day')}
                className={`px-3 py-1.5 rounded-md ${calendarView === 'day' && !showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              >
                Day
              </button>
              <button
                onClick={() => handleViewChange('week')}
                className={`px-3 py-1.5 rounded-md ${calendarView === 'week' && !showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              >
                Week
              </button>
              <button
                onClick={() => handleViewChange('month')}
                className={`px-3 py-1.5 rounded-md ${calendarView === 'month' && !showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              >
                Month
              </button>
              <button
                onClick={handleToggleGanttView}
                className={`px-3 py-1.5 rounded-md ${showGanttView ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'}`}
              >
                Gantt
              </button>
            </div>

            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Filter className="h-5 w-5 text-gray-400" />
                </div>
                <select
                  value={selectedProject}
                  onChange={handleProjectChange}
                  className="block w-full pl-10 pr-8 py-2 border border-gray-700 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="all">All Projects</option>
                  {projects.map(project => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>

              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={showOnlyMyTasks}
                  onChange={() => {
                    setShowOnlyMyTasks(!showOnlyMyTasks);
                    // Reload data when filter changes
                    setTimeout(loadData, 0);
                  }}
                  className="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-500 focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-300">My Tasks Only</span>
              </label>

              <label className="inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={showPersonalItems}
                  onChange={() => {
                    setShowPersonalItems(!showPersonalItems);
                    // Reload data when filter changes
                    setTimeout(loadData, 0);
                  }}
                  className="form-checkbox h-4 w-4 text-purple-600 rounded border-gray-500 focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-300">Show Personal Items</span>
              </label>

              <button
                onClick={loadData}
                className="p-2 bg-gray-700 text-gray-200 rounded-md hover:bg-gray-600 flex items-center"
                title="Refresh calendar data"
                disabled={localLoading || loading}
              >
                <RefreshCw size={18} className={loading || localLoading ? 'animate-spin mr-1' : 'mr-1'} />
                <span className="text-sm">Refresh</span>
              </button>
            </div>
          </div>

          {(localLoading || loading) && (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
              <span className="ml-2 text-gray-400">Loading calendar...</span>
            </div>
          )}

          {(localError || error) && (
            <div className="bg-red-900/30 border border-red-500 text-red-200 px-4 py-3 rounded-md flex items-start mb-6">
              <AlertTriangle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <span>{localError || error}</span>
            </div>
          )}

          {!localLoading && !loading && (
            <div className="mt-4">
              {showGanttView ? (
                selectedProject !== 'all' ? (
                  <GanttChartView
                    projectId={selectedProject}
                    tasks={tasks.filter(task => task.projectId === selectedProject)}
                  />
                ) : (
                  <div className="bg-gray-700 rounded-lg p-6 text-center">
                    <p className="text-gray-300">Please select a specific project to view the Gantt chart.</p>
                  </div>
                )
              ) : (
                <CalendarView
                  events={calendarEvents}
                  view={calendarView}
                  onEventClick={(event) => {
                    // Navigate to the task in its project
                    if (event.type === 'task' && event.projectId) {
                      window.location.href = `/services/admin/planner/${event.projectId}?editTask=${event.id}`;
                    }
                  }}
                />
              )}
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-lg font-medium mb-4">Legend</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-red-500 mr-2"></div>
              <span>Critical Priority</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-orange-500 mr-2"></div>
              <span>High Priority</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-blue-500 mr-2"></div>
              <span>Medium Priority</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-gray-500 mr-2"></div>
              <span>Low Priority</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-green-500 mr-2"></div>
              <span>Completed Task</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 rounded-full bg-purple-500 mr-2"></div>
              <span>Personal Item</span>
            </div>
          </div>
        </div>
      </div>

      {/* Debug Components */}
      <CalendarDebug />
      <TabsContextDebugger />
    </div>
  );
}
