'use client';

import React, { useState } from 'react';
import { Loader2, Send, Brain, Check, X, FileDown, FileText } from "lucide-react";
import MarkdownRenderer from '../../components/MarkdownRenderer';
import { LlmProvider } from '../../lib/tools/llm-tool';

interface ModelConfig {
  model: string;
  provider: LlmProvider;
}

interface ModelResponse {
  model: string;
  provider: LlmProvider;
  response: string | null;
  error: string | null;
}

interface ComparisonResult {
  criteria: string;
  criteriaModel: string;
  criteriaProvider: LlmProvider;
  optimizedPrompt: string;
  optimizationModel: string;
  optimizationProvider: LlmProvider;
  modelResponses: ModelResponse[];
  assessment: string;
  assessmentModel: string;
  assessmentProvider: LlmProvider;
  consolidatedResponse: string | null;
  consolidationModel: string | null;
  consolidationProvider: LlmProvider | null;
}

// Helper function to get the index of a step
const getStepIndex = (step: string): number => {
  const steps = ['idle', 'criteria', 'optimization', 'responses', 'assessment', 'consolidation', 'complete'];
  return steps.indexOf(step);
};

const LlmComparisonTool: React.FC = () => {
  // State for inputs
  const [prompt, setPrompt] = useState<string>('');
  const [context, setContext] = useState<string>('');
  const [criteriaModel, setCriteriaModel] = useState<string>('gemini-2.5-pro');
  const [optimizationModel, setOptimizationModel] = useState<string>('o3-2025-04-16');
  const [assessmentModel, setAssessmentModel] = useState<string>('claude-sonnet-4-0');
  const [consolidate, setConsolidate] = useState<boolean>(false);
  const [consolidationModel, setConsolidationModel] = useState<string>('o3-pro-2025-06-10');

  // Comparison models
  const [comparisonModels, setComparisonModels] = useState<ModelConfig[]>([
    { model: 'o3-2025-04-16', provider: 'openai' as LlmProvider },
    { model: 'claude-sonnet-4-0', provider: 'anthropic' as LlmProvider },
    { model: 'gemini-2.5-pro', provider: 'google' as LlmProvider }
  ]);

  // State for results
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<ComparisonResult | null>(null);
  const [activeTab, setActiveTab] = useState<'criteria' | 'responses' | 'assessment' | 'consolidated'>('criteria');

  // Progress state
  type ProgressStep = 'idle' | 'criteria' | 'optimization' | 'responses' | 'assessment' | 'consolidation' | 'complete';
  const [progressStep, setProgressStep] = useState<ProgressStep>('idle');
  const [progressPercentage, setProgressPercentage] = useState<number>(0);

  // Handle model selection change
  const handleModelChange = (index: number, field: 'model' | 'provider', value: string) => {
    const updatedModels = [...comparisonModels];
    if (field === 'model') {
      updatedModels[index] = {
        ...updatedModels[index],
        model: value
      };
    } else {
      // Ensure provider is a valid LlmProvider
      updatedModels[index] = {
        ...updatedModels[index],
        provider: value as LlmProvider
      };
    }
    setComparisonModels(updatedModels);
  };

  // Add a new model to compare
  const addModel = () => {
    if (comparisonModels.length < 5) {
      setComparisonModels([...comparisonModels, { model: 'claude-sonnet-4-0', provider: 'anthropic' as LlmProvider }]);
    }
  };

  // Remove a model from comparison
  const removeModel = (index: number) => {
    if (comparisonModels.length > 1) {
      const updatedModels = [...comparisonModels];
      updatedModels.splice(index, 1);
      setComparisonModels(updatedModels);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!prompt) return;

    setLoading(true);
    setError(null);
    setResult(null);
    setProgressStep('criteria');
    setProgressPercentage(10);

    // Create a controller to abort the fetch if needed
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      // Start the request
      const responsePromise = fetch('/api/llm-comparison', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          context,
          criteriaModel,
          optimizationModel,
          comparisonModels,
          assessmentModel,
          consolidate,
          consolidationModel
        }),
        signal
      });

      // Set up progress simulation
      const progressInterval = setInterval(() => {
        setProgressPercentage(prev => {
          if (prev >= 95) {
            clearInterval(progressInterval);
            return 95;
          }

          // Update progress step based on percentage
          if (prev < 20 && progressStep !== 'criteria') {
            setProgressStep('criteria');
          } else if (prev >= 20 && prev < 35 && progressStep !== 'optimization') {
            setProgressStep('optimization');
          } else if (prev >= 35 && prev < 65 && progressStep !== 'responses') {
            setProgressStep('responses');
          } else if (prev >= 65 && prev < 85 && progressStep !== 'assessment') {
            setProgressStep('assessment');
          } else if (prev >= 85 && consolidate && progressStep !== 'consolidation') {
            setProgressStep('consolidation');
          }

          return prev + (Math.random() * 2 + 0.5); // Random increment between 0.5 and 2.5
        });
      }, 800);

      // Wait for the response
      const response = await responsePromise;
      const data = await response.json();

      // Clear the progress interval
      clearInterval(progressInterval);

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process LLM comparison');
      }

      // Complete the progress
      setProgressPercentage(100);
      setProgressStep('complete');
      setResult(data);
      setActiveTab('criteria');
    } catch (err: any) {
      console.error('LLM comparison error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle PDF download
  const handleSaveToPdf = async () => {
    if (!result) return;

    try {
      const response = await fetch('/api/llm-comparison-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: prompt,
          criteria: result.criteria,
          optimizedPrompt: result.optimizedPrompt,
          modelResponses: result.modelResponses,
          assessment: result.assessment,
          consolidatedResponse: result.consolidatedResponse
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate PDF');
      }

      // Create a blob from the PDF data
      const blob = await response.blob();

      // Create a link element to download the PDF
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `llm-comparison-${Date.now()}.pdf`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err: any) {
      console.error('PDF generation error:', err);
      setError(err.message);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Input Card */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <Brain className="mr-2 h-5 w-5 text-purple-400" />
            Multi-LLM Comparison
          </h2>
          <p className="text-sm text-zinc-400">
            Compare responses from multiple language models
          </p>
        </div>
        <div className="p-4 space-y-4">
          <div className="space-y-2">
            <label htmlFor="prompt" className="block text-sm font-medium text-zinc-300">Prompt</label>
            <textarea
              id="prompt"
              rows={4}
              placeholder="Enter your prompt..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="context" className="block text-sm font-medium text-zinc-300">Context (Optional)</label>
            <textarea
              id="context"
              rows={3}
              placeholder="Additional context for the prompt..."
              value={context}
              onChange={(e) => setContext(e.target.value)}
              className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-zinc-300">Models to Compare</label>
            <div className="space-y-3">
              {comparisonModels.map((modelConfig, index) => (
                <div key={index} className="flex space-x-2">
                  <select
                    value={modelConfig.model}
                    onChange={(e) => handleModelChange(index, 'model', e.target.value)}
                    className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <optgroup label="OpenAI">
                      <option value="gpt-4o">GPT-4o</option>
                      <option value="gpt-4.1-2025-04-14">gpt-4.1-2025-04-14</option>
                      <option value="o3-2025-04-16">o3-2025-04-16</option>
                      <option value="o3-mini-2025-01-31">o3-mini</option>
                      <option value="o1-mini-2024-09-12">o1-mini</option>
                    </optgroup>
                    <optgroup label="Anthropic">
                      <option value="claude-sonnet-4-0">Claude Sonnet 4.0</option>
                      <option value="claude-opus-4-0">Claude Opus 4.0</option>
                    </optgroup>
                    <optgroup label="Google">
                      <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                      <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                    </optgroup>
                    <optgroup label="Groq">
                      <option value="llama-3.3-70b-versatile">Llama 3.3 70B</option>
                      <option value="deepseek-r1-distill-llama-70b">DeepSeek Distill Llama 70B</option>
                    </optgroup>
                  </select>

                  <select
                    value={modelConfig.provider}
                    onChange={(e) => handleModelChange(index, 'provider', e.target.value)}
                    className="w-28 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="openai">OpenAI</option>
                    <option value="anthropic">Anthropic</option>
                    <option value="google">Google</option>
                    <option value="groq">Groq</option>
                  </select>

                  <button
                    onClick={() => removeModel(index)}
                    disabled={comparisonModels.length <= 1}
                    className="p-2 bg-red-900/30 text-red-400 rounded-md hover:bg-red-900/50 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
              ))}

              <button
                onClick={addModel}
                disabled={comparisonModels.length >= 5}
                className="w-full px-3 py-2 bg-purple-900/30 text-purple-400 rounded-md hover:bg-purple-900/50 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
              >
                + Add Model (Max 5)
              </button>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="criteria-model" className="block text-sm font-medium text-zinc-300">Criteria Model</label>
              <select
                id="criteria-model"
                value={criteriaModel}
                onChange={(e) => setCriteriaModel(e.target.value)}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                <option value="llama-3.3-70b-versatile">Llama 3.3 70B</option>
              </select>
            </div>

            <div className="space-y-2">
              <label htmlFor="optimization-model" className="block text-sm font-medium text-zinc-300">Optimization Model</label>
              <select
                id="optimization-model"
                value={optimizationModel}
                onChange={(e) => setOptimizationModel(e.target.value)}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="gpt-4o">o3-2025-04-16</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="assessment-model" className="block text-sm font-medium text-zinc-300">Assessment Model</label>
              <select
                id="assessment-model"
                value={assessmentModel}
                onChange={(e) => setAssessmentModel(e.target.value)}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="claude-sonnet-4-0">Claude Sonnet 4.0</option>
                <option value="claude-opus-4-0">Claude Opus 4.0</option>
              </select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label htmlFor="consolidation-model" className="block text-sm font-medium text-zinc-300">Consolidation Model</label>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="consolidate"
                    checked={consolidate}
                    onChange={(e) => setConsolidate(e.target.checked)}
                    className="mr-2 h-4 w-4 rounded border-zinc-600 text-purple-600 focus:ring-purple-500"
                  />
                  <label htmlFor="consolidate" className="text-xs text-zinc-400">Consolidate</label>
                </div>
              </div>
              <select
                id="consolidation-model"
                value={consolidationModel}
                onChange={(e) => setConsolidationModel(e.target.value)}
                disabled={!consolidate}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 disabled:opacity-50"
              >
                <option value="o3-2025-04-16">o3-2025-04-16</option>
                <option value="gpt-4o">GPT-4o</option>
              </select>
            </div>
          </div>
        </div>
        <div className="p-4 border-t border-zinc-700">
          <button
            onClick={handleSubmit}
            disabled={loading || !prompt}
            className="w-full flex justify-center items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <Loader2 className="animate-spin mr-2 h-5 w-5" />
                Processing...
              </>
            ) : (
              <>
                <Send className="mr-2 h-5 w-5" />
                Compare Models
              </>
            )}
          </button>
        </div>
      </div>

      {/* Result Card */}
      <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
        <div className="p-4 border-b border-zinc-700">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-semibold text-white">Comparison Results</h2>
            {result && (
              <button
                onClick={handleSaveToPdf}
                className="flex items-center px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors text-sm"
              >
                <FileText className="mr-1.5 h-4 w-4" />
                Save to PDF
              </button>
            )}
          </div>
          {result && (
            <div className="mt-2 flex space-x-1">
              <button
                onClick={() => setActiveTab('criteria')}
                className={`px-3 py-1 text-sm rounded-md ${activeTab === 'criteria' ? 'bg-purple-600 text-white' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
              >
                Criteria
              </button>
              <button
                onClick={() => setActiveTab('responses')}
                className={`px-3 py-1 text-sm rounded-md ${activeTab === 'responses' ? 'bg-purple-600 text-white' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
              >
                Responses
              </button>
              <button
                onClick={() => setActiveTab('assessment')}
                className={`px-3 py-1 text-sm rounded-md ${activeTab === 'assessment' ? 'bg-purple-600 text-white' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
              >
                Assessment
              </button>
              {result.consolidatedResponse && (
                <button
                  onClick={() => setActiveTab('consolidated')}
                  className={`px-3 py-1 text-sm rounded-md ${activeTab === 'consolidated' ? 'bg-purple-600 text-white' : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'}`}
                >
                  Consolidated
                </button>
              )}
            </div>
          )}
        </div>
        <div className="p-0">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-[500px]">
              <div className="text-center mb-8">
                <div className="relative w-64 h-64">
                  {/* Outer circle */}
                  <div className="absolute inset-0 rounded-full border-4 border-zinc-700 opacity-25"></div>

                  {/* Progress circle */}
                  <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
                    <circle
                      className="text-zinc-700"
                      strokeWidth="4"
                      stroke="currentColor"
                      fill="transparent"
                      r="48"
                      cx="50"
                      cy="50"
                    />
                    <circle
                      className="text-purple-500 transition-all duration-300 ease-in-out"
                      strokeWidth="4"
                      strokeLinecap="round"
                      stroke="currentColor"
                      fill="transparent"
                      r="48"
                      cx="50"
                      cy="50"
                      strokeDasharray={`${progressPercentage * 3.02}, 302`}
                      transform="rotate(-90 50 50)"
                    />
                  </svg>

                  {/* Center content */}
                  <div className="absolute inset-0 flex flex-col items-center justify-center">
                    <p className="text-3xl font-bold text-white">{Math.round(progressPercentage)}%</p>
                    <p className="text-sm text-purple-400 capitalize">{progressStep}</p>
                  </div>
                </div>
              </div>

              <div className="w-full max-w-md px-4">
                {/* Step indicators */}
                <div className="grid grid-cols-5 gap-2 mb-6">
                  {['criteria', 'optimization', 'responses', 'assessment', consolidate ? 'consolidation' : 'complete'].map((step, index) => (
                    <div key={step} className="flex flex-col items-center">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${progressStep === step ? 'bg-purple-600 text-white' : progressStep === 'complete' || (index === 4 && !consolidate && progressStep === 'assessment') || getStepIndex(progressStep) > index ? 'bg-green-600 text-white' : 'bg-zinc-700 text-zinc-400'}`}
                      >
                        {getStepIndex(progressStep) > index || progressStep === 'complete' || (index === 4 && !consolidate && progressStep === 'assessment') ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          index + 1
                        )}
                      </div>
                      <span className="text-xs text-zinc-400 capitalize">{step}</span>
                    </div>
                  ))}
                </div>

                {/* Current step details */}
                <div className="bg-zinc-800 rounded-lg p-4 mb-4">
                  <h3 className="text-sm font-medium text-white mb-2 capitalize">
                    {progressStep === 'criteria' && (
                      <>Generating criteria with {criteriaModel}</>
                    )}
                    {progressStep === 'optimization' && (
                      <>Optimizing prompt with {optimizationModel}</>
                    )}
                    {progressStep === 'responses' && (
                      <>Generating responses from {comparisonModels.length} models</>
                    )}
                    {progressStep === 'assessment' && (
                      <>Assessing responses with {assessmentModel}</>
                    )}
                    {progressStep === 'consolidation' && (
                      <>Consolidating responses with {consolidationModel}</>
                    )}
                    {progressStep === 'complete' && (
                      <>Processing complete!</>
                    )}
                  </h3>
                  <p className="text-xs text-zinc-400">
                    {progressStep === 'criteria' && (
                      <>Establishing clear evaluation criteria for comparing the responses...</>
                    )}
                    {progressStep === 'optimization' && (
                      <>Refining your prompt to ensure the highest quality responses...</>
                    )}
                    {progressStep === 'responses' && (
                      <>Processing your prompt with multiple language models in parallel...</>
                    )}
                    {progressStep === 'assessment' && (
                      <>Analyzing and comparing the responses based on the established criteria...</>
                    )}
                    {progressStep === 'consolidation' && (
                      <>Creating a consolidated response that combines the best elements from each model...</>
                    )}
                    {progressStep === 'complete' && (
                      <>All steps completed successfully. Preparing results for display...</>
                    )}
                  </p>
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="p-4 bg-red-900/20 m-4 rounded-md text-red-400">
              <p className="font-semibold">Error</p>
              <p>{error}</p>
            </div>
          ) : !result ? (
            <div className="flex items-center justify-center h-[500px] text-zinc-500">
              <p>Submit a prompt to compare model responses</p>
            </div>
          ) : (
            <div className="h-[500px] overflow-y-auto">
              {activeTab === 'criteria' && (
                <div className="p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-medium text-white">Evaluation Criteria</h3>
                    <div className="flex items-center text-xs text-zinc-400">
                      <span className="px-2 py-1 bg-zinc-800 rounded-md mr-2">
                        {result.criteriaModel}
                      </span>
                      <span className="px-2 py-1 bg-zinc-800 rounded-md capitalize">
                        via {result.criteriaProvider}
                      </span>
                    </div>
                  </div>
                  <div className="mb-6">
                    <MarkdownRenderer content={result.criteria} />
                  </div>

                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-medium text-white">Optimized Prompt</h3>
                    <div className="flex items-center text-xs text-zinc-400">
                      <span className="px-2 py-1 bg-zinc-800 rounded-md mr-2">
                        {result.optimizationModel}
                      </span>
                      <span className="px-2 py-1 bg-zinc-800 rounded-md capitalize">
                        via {result.optimizationProvider}
                      </span>
                    </div>
                  </div>
                  <div className="bg-zinc-800 p-3 rounded-md border border-zinc-700">
                    <p className="text-amber-100 whitespace-pre-wrap">{result.optimizedPrompt}</p>
                  </div>
                </div>
              )}

              {activeTab === 'responses' && (
                <div>
                  {result.modelResponses.map((modelResponse, index) => (
                    <div key={index} className="border-b border-zinc-700 last:border-b-0">
                      <div className="p-4 bg-zinc-800/50">
                        <h3 className="text-lg font-medium text-white flex items-center">
                          <span className="mr-2">Model {index + 1}:</span>
                          <span className="text-purple-400">{modelResponse.model}</span>
                          <span className="mx-2 text-zinc-500">via</span>
                          <span className="text-blue-400">{modelResponse.provider}</span>
                        </h3>
                      </div>
                      <div className="p-4">
                        {modelResponse.error ? (
                          <div className="bg-red-900/20 p-3 rounded-md text-red-400">
                            <p>Error: {modelResponse.error}</p>
                          </div>
                        ) : (
                          <MarkdownRenderer content={modelResponse.response || ''} />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === 'assessment' && (
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-white">Assessment</h3>
                    <div className="flex items-center text-xs text-zinc-400">
                      <span className="px-2 py-1 bg-zinc-800 rounded-md mr-2">
                        {result.assessmentModel}
                      </span>
                      <span className="px-2 py-1 bg-zinc-800 rounded-md capitalize">
                        via {result.assessmentProvider}
                      </span>
                    </div>
                  </div>
                  <MarkdownRenderer content={result.assessment} />
                </div>
              )}

              {activeTab === 'consolidated' && result.consolidatedResponse && (
                <div className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-white">Consolidated Response</h3>
                    <div className="flex items-center text-xs text-zinc-400">
                      <span className="px-2 py-1 bg-zinc-800 rounded-md mr-2">
                        {result.consolidationModel}
                      </span>
                      <span className="px-2 py-1 bg-zinc-800 rounded-md capitalize">
                        via {result.consolidationProvider}
                      </span>
                    </div>
                  </div>
                  <MarkdownRenderer content={result.consolidatedResponse} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LlmComparisonTool;