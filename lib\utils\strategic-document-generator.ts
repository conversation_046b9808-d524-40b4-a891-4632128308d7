/**
 * Strategic Document Generator Utility
 * 
 * Centralized document generation and content formatting logic for Strategic Director Agent.
 * This module contains all document processing and content formatting methods.
 */

import { MarketingStrategy, ProductAnalysis } from './strategic-chart-generator';
import { dateTimeTool } from '../tools/dateTimeTool';

// Type definitions for document generation
export interface PdfContent {
  title: string;
  content: string;
}

export interface SavePdfToByteStoreResult {
  success: boolean;
  message: string;
  byteStoreKey?: string;
}

/**
 * Strategic Document Generator Class
 * Contains all document generation and content formatting methods
 */
export class StrategicDocumentGenerator {

  /**
   * Generate a comprehensive strategy document as PDF content
   */
  static async generateStrategyDocumentContent(
    strategy: MarketingStrategy,
    productAnalysis?: ProductAnalysis,
    chartConfigs?: {
      swotChart?: any;
      audienceChart?: any;
      channelChart?: any;
      timelineChart?: any;
      kpiChart?: any;
    },
    chartFallbacks?: {
      swot?: string;
      audience?: string;
      channels?: string;
      timeline?: string;
      kpis?: string;
    }
  ): Promise<PdfContent[]> {
    const pdfContents: PdfContent[] = [];

    // Executive Summary
    const executiveSummary = `This comprehensive marketing strategy document outlines the strategic approach for ${strategy.name}. 
    
**Value Proposition:** ${strategy.valueProposition || 'To be defined based on market analysis'}

**Key Strategic Messages:**
${(strategy.keyMessages || ['Strategic messaging to be developed']).map(msg => `• ${msg}`).join('\n')}

**Target Market:** ${JSON.stringify(strategy.targetAudience)}

**Strategic Objectives:** Drive market penetration, enhance brand awareness, and achieve sustainable growth through data-driven marketing initiatives.`;

    pdfContents.push({ title: 'Executive Summary', content: executiveSummary });

    // SWOT Analysis Section
    if (productAnalysis) {
      let swotContent = `**Product:** ${productAnalysis.name}

**Strengths:**
${productAnalysis.swotAnalysis.strengths.map(s => `• ${s}`).join('\n')}

**Weaknesses:**
${productAnalysis.swotAnalysis.weaknesses.map(w => `• ${w}`).join('\n')}

**Opportunities:**
${productAnalysis.swotAnalysis.opportunities.map(o => `• ${o}`).join('\n')}

**Threats:**
${productAnalysis.swotAnalysis.threats.map(t => `• ${t}`).join('\n')}`;

      pdfContents.push({ title: 'SWOT Analysis', content: swotContent });
      
      if (chartConfigs?.swotChart) {
        pdfContents.push({ title: 'SWOT Analysis Visualization', content: `<chart-data>${JSON.stringify(chartConfigs.swotChart)}</chart-data>` });
      } else if (chartFallbacks?.swot) {
        pdfContents.push({ title: 'SWOT Analysis (Text)', content: chartFallbacks.swot });
      }
    }

    // Target Audience Analysis
    let audienceContent = `**Demographics:**
${(strategy.targetAudience.demographics || ['To be researched']).map(d => `• ${d}`).join('\n')}

**Behavioral Characteristics:**
${(strategy.targetAudience.behaviors || ['To be analyzed']).map(b => `• ${b}`).join('\n')}`;

    if (strategy.targetAudience.segmentDetails && strategy.targetAudience.segmentDetails.length > 0) {
      audienceContent += `\n\n**Detailed Segments:**\n`;
      strategy.targetAudience.segmentDetails.forEach(segment => {
        audienceContent += `\n**${segment.name}:**\n${segment.characteristics.map(c => `• ${c}`).join('\n')}\n`;
      });
    }

    pdfContents.push({ title: 'Target Audience Analysis', content: audienceContent });
    
    if (chartConfigs?.audienceChart) {
      pdfContents.push({ title: 'Audience Segmentation Visualization', content: `<chart-data>${JSON.stringify(chartConfigs.audienceChart)}</chart-data>` });
    } else if (chartFallbacks?.audience) {
      pdfContents.push({ title: 'Audience Analysis (Text)', content: chartFallbacks.audience });
    }

    // Marketing Channels & Content Strategy
    let channelContent = (strategy.channels || []).map(ch => `• ${ch}`).join('\n\n');
    if (strategy.channelStrategy && Object.keys(strategy.channelStrategy).length > 0) {
      channelContent += `\n\n**Channel Strategy:**\n${Object.entries(strategy.channelStrategy).map(([c, s]) => `• **${c}:** ${s}`).join('\n')}`;
    }
    if (strategy.contentStrategy) {
      channelContent += `\n\n**Content Strategy:**\n${strategy.contentStrategy}`;
    }

    pdfContents.push({ title: 'Marketing Channels & Content Strategy', content: channelContent });
    
    if (chartConfigs?.channelChart) {
      pdfContents.push({ title: 'Channel Effectiveness Analysis', content: `<chart-data>${JSON.stringify(chartConfigs.channelChart)}</chart-data>` });
    } else if (chartFallbacks?.channels) {
      pdfContents.push({ title: 'Channel Effectiveness (Text)', content: chartFallbacks.channels });
    }

    // Implementation Timeline
    if (strategy.timeline) {
      let timelineContent = `**Project Duration:** ${strategy.timeline.startDate.toISOString().split('T')[0]} to ${strategy.timeline.endDate.toISOString().split('T')[0]}

**Key Milestones:**
${strategy.timeline.milestones.map(m => `• **${(m.date instanceof Date ? m.date : new Date(String(m.date))).toISOString().split('T')[0]}:** ${m.description}`).join('\n')}`;

      pdfContents.push({ title: 'Implementation Timeline', content: timelineContent });
      
      if (chartConfigs?.timelineChart) {
        pdfContents.push({ title: 'Timeline Visualization', content: `<chart-data>${JSON.stringify(chartConfigs.timelineChart)}</chart-data>` });
      } else if (chartFallbacks?.timeline) {
        pdfContents.push({ title: 'Timeline (Text)', content: chartFallbacks.timeline });
      }
    }

    // KPIs and Success Metrics
    if (strategy.kpis && strategy.kpis.length > 0) {
      const kpiContent = `**Key Performance Indicators:**

${strategy.kpis.map(kpi => `• ${kpi}`).join('\n')}

**Measurement Framework:**
• Monthly performance reviews
• Quarterly strategic assessments
• Annual strategy optimization
• Real-time dashboard monitoring`;

      pdfContents.push({ title: 'KPIs and Success Metrics', content: kpiContent });
      
      if (chartConfigs?.kpiChart) {
        pdfContents.push({ title: 'KPI Dashboard', content: `<chart-data>${JSON.stringify(chartConfigs.kpiChart)}</chart-data>` });
      } else if (chartFallbacks?.kpis) {
        pdfContents.push({ title: 'KPIs (Text)', content: chartFallbacks.kpis });
      }
    }

    // Conclusion and Next Steps
    const conclusion = `This strategic marketing plan provides a comprehensive framework for achieving business objectives through ${strategy.name}. 

**Immediate Next Steps:**
1. Finalize target audience research and validation
2. Develop detailed content calendar and creative assets
3. Establish measurement and tracking systems
4. Launch pilot campaigns for testing and optimization
5. Scale successful initiatives based on performance data

**Success Factors:**
• Consistent execution of strategic initiatives
• Regular performance monitoring and optimization
• Agile response to market changes and opportunities
• Strong cross-functional collaboration and communication

**Risk Mitigation:**
• Diversified channel approach to reduce dependency
• Continuous market monitoring for early trend detection
• Flexible budget allocation for rapid strategy pivots
• Regular competitive analysis and positioning updates`;

    pdfContents.push({ title: 'Conclusion and Next Steps', content: conclusion });

    return pdfContents;
  }

  /**
   * Generate a narrative marketing report
   */
  static async generateNarrativeReport(
    strategy: MarketingStrategy,
    productAnalysis?: ProductAnalysis,
    reportType: string = 'strategy_overview',
    audience: string = 'executive'
  ): Promise<string> {
    const reportDateTime = dateTimeTool.getCurrentDateTime({ format: 'full', includeTime: true, includeDayOfWeek: true });

    let prompt = `
    Generate a narrative marketing report.
    
    **Report Type:** ${reportType}
    **Target Audience:** ${audience}
    **Generated:** ${reportDateTime}
    
    **Strategy Details:**
    ${JSON.stringify(strategy, null, 2)}
    
    **Product Analysis:**
    ${productAnalysis ? JSON.stringify(productAnalysis, null, 2) : 'No product analysis available'}
    
    Create a comprehensive, well-structured narrative report that:
    1. Provides executive-level insights and recommendations
    2. Uses clear, professional language appropriate for ${audience} audience
    3. Includes specific data points and strategic rationale
    4. Offers actionable next steps and implementation guidance
    5. Addresses potential risks and mitigation strategies
    
    Format as a professional business report with clear sections and bullet points where appropriate.
    `;

    // This would typically call an LLM service, but for now return a structured template
    return `# Marketing Strategy Report: ${strategy.name}

**Generated:** ${reportDateTime}
**Report Type:** ${reportType}
**Target Audience:** ${audience}

## Executive Summary

This report provides a comprehensive analysis of the marketing strategy for ${strategy.name}, including strategic recommendations, implementation roadmap, and success metrics.

**Key Findings:**
• Value proposition: ${strategy.valueProposition || 'To be defined'}
• Target market alignment: ${JSON.stringify(strategy.targetAudience)}
• Channel strategy: ${(strategy.channels || []).join(', ')}

## Strategic Recommendations

Based on the analysis, we recommend the following strategic initiatives:

1. **Market Positioning:** Leverage identified strengths to capture market opportunities
2. **Channel Optimization:** Focus on high-performing channels while testing new opportunities
3. **Content Strategy:** Develop targeted content that resonates with key audience segments
4. **Performance Monitoring:** Implement comprehensive KPI tracking and optimization

## Implementation Roadmap

${strategy.timeline ? `
**Timeline:** ${strategy.timeline.startDate.toISOString().split('T')[0]} to ${strategy.timeline.endDate.toISOString().split('T')[0]}

**Key Milestones:**
${strategy.timeline.milestones.map(m => `• ${(m.date instanceof Date ? m.date : new Date(String(m.date))).toISOString().split('T')[0]}: ${m.description}`).join('\n')}
` : 'Timeline to be developed based on strategic priorities'}

## Success Metrics

${strategy.kpis ? `
**Key Performance Indicators:**
${strategy.kpis.map(kpi => `• ${kpi}`).join('\n')}
` : 'KPIs to be established based on strategic objectives'}

## Risk Assessment and Mitigation

**Identified Risks:**
• Market volatility and competitive pressure
• Resource allocation and budget constraints
• Technology and platform dependencies

**Mitigation Strategies:**
• Diversified approach across multiple channels
• Agile methodology for rapid strategy adjustments
• Continuous monitoring and optimization protocols

## Next Steps

1. **Immediate (0-30 days):** Finalize strategy details and resource allocation
2. **Short-term (1-3 months):** Launch pilot initiatives and establish tracking
3. **Medium-term (3-6 months):** Scale successful programs and optimize performance
4. **Long-term (6+ months):** Evaluate strategic outcomes and plan next phase

---
*This report was generated by the Strategic Director Agent on ${reportDateTime}*`;
  }

  /**
   * Format content for PDF generation
   */
  static formatContentForPdf(content: string): string {
    // Clean up content for PDF generation
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold text
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic text
      .replace(/^### (.*$)/gim, '<h3>$1</h3>') // H3 headers
      .replace(/^## (.*$)/gim, '<h2>$1</h2>') // H2 headers
      .replace(/^# (.*$)/gim, '<h1>$1</h1>') // H1 headers
      .replace(/^\• (.*$)/gim, '<li>$1</li>') // List items
      .replace(/\n\n/g, '</p><p>') // Paragraphs
      .replace(/^(.*)$/gim, '<p>$1</p>') // Wrap remaining text in paragraphs
      .replace(/<p><li>/g, '<ul><li>') // Start unordered lists
      .replace(/<\/li><\/p>/g, '</li></ul>') // End unordered lists
      .replace(/<p><h/g, '<h') // Fix header wrapping
      .replace(/<\/h([1-6])><\/p>/g, '</h$1>'); // Fix header closing
  }

  /**
   * Extract key insights from analysis content
   */
  static extractKeyInsights(analysisContent: string): string[] {
    const insights: string[] = [];
    
    // Extract insights based on common patterns
    const lines = analysisContent.split('\n');
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      // Look for recommendation patterns
      if (trimmed.match(/^(recommend|suggest|propose|advise)/i)) {
        insights.push(trimmed);
      }
      
      // Look for key finding patterns
      if (trimmed.match(/^(key finding|important|critical|significant)/i)) {
        insights.push(trimmed);
      }
      
      // Look for opportunity patterns
      if (trimmed.match(/^(opportunity|potential|advantage)/i)) {
        insights.push(trimmed);
      }
    }
    
    return insights.slice(0, 10); // Limit to top 10 insights
  }

  /**
   * Generate executive summary from detailed analysis
   */
  static generateExecutiveSummary(analysisContent: string, maxLength: number = 500): string {
    const keyInsights = this.extractKeyInsights(analysisContent);
    
    let summary = `Executive Summary:\n\n`;
    summary += `This strategic analysis provides comprehensive insights and recommendations based on thorough market research and competitive analysis.\n\n`;
    
    if (keyInsights.length > 0) {
      summary += `Key Findings:\n`;
      keyInsights.slice(0, 5).forEach((insight, index) => {
        summary += `${index + 1}. ${insight}\n`;
      });
    }
    
    summary += `\nStrategic recommendations focus on leveraging identified opportunities while mitigating potential risks through data-driven decision making and agile implementation approaches.`;
    
    // Truncate if necessary
    if (summary.length > maxLength) {
      summary = summary.substring(0, maxLength - 3) + '...';
    }
    
    return summary;
  }
}
