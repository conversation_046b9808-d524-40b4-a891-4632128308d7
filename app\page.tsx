"use client"

import React from "react"
import Image from "next/image"
import Link from "next/link"
import { FolderOpen, FileText, Tags, Search, MessageSquare, Upload } from "lucide-react"
import { useSession } from "next-auth/react"
import Header from "components/Header"
import VoiceFeatureCard from "components/VoiceAssistants/VoiceFeatureCard"

interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
}

function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-gray-200 text-ike-purple_b shadow-ike-purple_b rounded-lg shadow-lg overflow-hidden">
      <div className="p-6">
        <div className="flex items-center mb-4">
          {icon}
          <h3 className="text-2xl font-semibold ml-4">{title}</h3>
        </div>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  )
}

export default function LandingPage() {
  const { data: session, status } = useSession()

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <p className="text-xl font-semibold">Loading...</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section 
          className="py-18 relative bg-cover bg-center bg-no-repeat bg-fixed"
          style={{
            backgroundImage: "url('/feature-background.jpg')",
          }}
        >
          <div className="container mx-auto px-2 text-center text-white">
            <div className="flex justify-center mb-6">
              <div className="relative mt-5">
                <Image
                  src="/logo4.png"
                  alt="Company Logo"
                  width={550}
                  height={600}
                  className="opacity-90 transition-all duration-300"
                  style={{
                    filter: 'drop-shadow(0 0 10px rgba(255, 255, 255, 0.7))',
                  }}
                />
              </div>
            </div>
            <p className="text-3xl md:text-3xl text-blue-500 font-extrabold mb-2">Interactive Knowledge Explorer</p>
            <Link href="/fileManager" className="inline-block mt-5 -mb-20 hover:bg-gray-200 hover:text-ike-purple bg-ike-purple text-gray-200 font-bold py-4 px-8 rounded-lg text-lg transition duration-300">
              Get Started
            </Link>
          </div>
        </section>

        {/* Feature Sections */}
        <section className="py-14 bg-ike-purple">
          <div className="container mx-auto px-4">
            {/* Voice Feature Card - Full Width */}
            <div className="mb-16">
              <VoiceFeatureCard />
            </div>
            
            {/* Regular Feature Cards Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-12 lg:gap-16">
              <FeatureCard
                icon={<MessageSquare className="w-12 h-12 text-green-600" />}
                title="Chat with Your Documents"
                description="Engage in natural conversations with your documents using our advanced AI. Ask questions, get summaries, and extract insights effortlessly."
              />
              <FeatureCard
                icon={<FolderOpen className="w-12 h-12 text-purple-600" />}
                title="File Management"
                description="Effortlessly organize and access your documents with our intuitive file management system."
              />
              <FeatureCard
                icon={<FileText className="w-12 h-12 text-indigo-600" />}
                title="Document Processing"
                description="Extract valuable insights from your documents using advanced AI-powered processing techniques."
              />
              <FeatureCard
                icon={<Tags className="w-12 h-12 text-pink-600" />}
                title="Metadata Extraction"
                description="Automatically extract and organize key information from your documents for easy retrieval."
              />
              <FeatureCard
                icon={<Search className="w-12 h-12 text-blue-600" />}
                title="Advanced Search"
                description="Find exactly what you need with our powerful, context-aware search functionality."
              />
              <FeatureCard
                icon={<Upload className="w-12 h-12 text-yellow-500" />}
                title="Upload"
                description="Upload your knowledgebase and start interacting with your documents instantly."
              />
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="bg-gray-200 py-20">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl text-ike-purple font-bold mb-6">Ready to transform your document management?</h2>
            <p className="text-xl text-ike-purple mb-8">Join thousands of satisfied users and experience the power of IKE today.</p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link href="/fileManager" className="bg-ike-purple hover:bg-white hover:text-ike-purple text-white font-bold py-3 px-6 rounded-lg text-lg transition duration-300">
                Start Free Trial
              </Link>
              <Link href="/contact" className="bg-white text-ike-purple hover:bg-gray-100 font-bold py-3 px-6 rounded-lg text-lg border border-ike-purple transition duration-300">
                Contact Sales
              </Link>
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-ike-purple text-white py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0 flex items-center">
              <Link href="/" className="flex items-center space-x-2 text-indigo-950 hover:text-gray-600 transition-colors duration-200">
                <Image src="/logo3.png" alt="Company Logo" width={80} height={80} />
              </Link>
              <Link href="/" className="flex items-center space-x-2 text-indigo-950 hover:text-gray-600 transition-colors duration-200">
                <Image src="/favicon.png" alt="Company Logo" width={80} height={80} />
              </Link>
            </div>
            <nav className="flex flex-wrap justify-center md:justify-end space-x-4">
              <Link href="/about" className="hover:text-gray-300 transition duration-300">
                About
              </Link>
              <Link href="/features" className="hover:text-gray-300 transition duration-300">
                Features
              </Link>
              <Link href="/pricing" className="hover:text-gray-300 transition duration-300">
                Pricing
              </Link>
              <Link href="/contact" className="hover:text-gray-300 transition duration-300">
                Contact
              </Link>
            </nav>
          </div>
          <div className="mt-8 text-center text-sm">
            © {new Date().getFullYear()} Interactive Knowledge Enabler. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  )
}