'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  Bar<PERSON><PERSON>2,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  TrendingUp,
  Users,
  Calendar,
  CheckSquare,
  Clock,
  Tag,
  AlertTriangle,
  Filter
} from 'lucide-react';
import { usePlanner } from '../../context/PlannerContext';
import ContextDebugger from '../../../components/ContextDebugger';
import { Project, Task, User, TaskStatus, TaskPriority } from '../../../../admin/planner/types';

// Define color constants
const COLORS = {
  purple: '#9333ea',
  blue: '#3b82f6',
  green: '#22c55e',
  yellow: '#eab308',
  red: '#ef4444',
  indigo: '#6366f1',
  pink: '#ec4899',
  teal: '#14b8a6',
  orange: '#f97316',
  gray: '#6b7280',
};

const STATUS_COLORS = {
  'Not Started': COLORS.gray,
  'In Progress': COLORS.blue,
  'Reviewed': COLORS.yellow,
  'Complete': COLORS.green,
};

const PRIORITY_COLORS = {
  'Low': COLORS.green,
  'Medium': COLORS.blue,
  'High': COLORS.orange,
  'Critical': COLORS.red,
};

// Helper function to format dates
const formatDate = (date: Date | string): string => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

// Helper function to calculate days between dates
const daysBetween = (date1: Date, date2: Date): number => {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  const diffDays = Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
  return diffDays;
};

// Helper function to calculate days remaining
const daysRemaining = (endDate: Date): number => {
  const today = new Date();
  return daysBetween(today, endDate);
};

// Helper function to calculate project completion percentage
const calculateProjectCompletion = (tasks: Task[]): number => {
  if (tasks.length === 0) return 0;
  const completedTasks = tasks.filter(task => task.status === 'Complete').length;
  return Math.round((completedTasks / tasks.length) * 100);
};

// Helper function to calculate project health
const calculateProjectHealth = (
  tasks: Task[],
  startDate: Date,
  endDate: Date
): 'On Track' | 'At Risk' | 'Behind Schedule' => {
  const completion = calculateProjectCompletion(tasks);
  const totalDuration = daysBetween(new Date(startDate), new Date(endDate));
  const elapsed = daysBetween(new Date(startDate), new Date());
  const expectedCompletion = totalDuration > 0 ? Math.min(100, Math.round((elapsed / totalDuration) * 100)) : 0;

  const variance = completion - expectedCompletion;

  if (variance >= 0) return 'On Track';
  if (variance >= -15) return 'At Risk';
  return 'Behind Schedule';
};

// Helper function to get health color
const getHealthColor = (health: 'On Track' | 'At Risk' | 'Behind Schedule'): string => {
  switch (health) {
    case 'On Track': return COLORS.green;
    case 'At Risk': return COLORS.yellow;
    case 'Behind Schedule': return COLORS.red;
    default: return COLORS.gray;
  }
};

// Analytics Dashboard Component
export default function AnalyticsDashboard() {
  const { projects, tasks, users, loading } = usePlanner();
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [timeRange, setTimeRange] = useState<'7days' | '30days' | '90days' | 'all'>('30days');

  // Filter tasks based on selected project and time range
  useEffect(() => {
    if (!loading) {
      let filtered = [...tasks];

      // Filter by project
      if (selectedProject !== 'all') {
        filtered = filtered.filter(task => task.projectId === selectedProject);
      }

      // Filter by time range
      if (timeRange !== 'all') {
        const now = new Date();
        const pastDate = new Date();

        switch (timeRange) {
          case '7days':
            pastDate.setDate(now.getDate() - 7);
            break;
          case '30days':
            pastDate.setDate(now.getDate() - 30);
            break;
          case '90days':
            pastDate.setDate(now.getDate() - 90);
            break;
        }

        filtered = filtered.filter(task => {
          const taskDate = new Date(task.createdAt || task.startDate);
          return taskDate >= pastDate;
        });
      }

      setFilteredTasks(filtered);
    }
  }, [loading, tasks, selectedProject, timeRange]);

  // Calculate metrics
  const totalProjects = projects.length;
  const activeProjects = projects.filter(p => p.status === 'Active').length;
  const completedProjects = projects.filter(p => p.status === 'Completed').length;
  const totalTasks = tasks.length;
  const completedTasks = tasks.filter(t => t.status === 'Complete').length;
  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  // Task status distribution data
  const taskStatusData = [
    { name: 'Not Started', value: filteredTasks.filter(t => t.status === 'Not Started').length },
    { name: 'In Progress', value: filteredTasks.filter(t => t.status === 'In Progress').length },
    { name: 'Reviewed', value: filteredTasks.filter(t => t.status === 'Reviewed').length },
    { name: 'Complete', value: filteredTasks.filter(t => t.status === 'Complete').length },
  ];

  // Task priority distribution data
  const taskPriorityData = [
    { name: 'Low', value: filteredTasks.filter(t => t.priority === 'Low').length },
    { name: 'Medium', value: filteredTasks.filter(t => t.priority === 'Medium').length },
    { name: 'High', value: filteredTasks.filter(t => t.priority === 'High').length },
    { name: 'Critical', value: filteredTasks.filter(t => t.priority === 'Critical').length },
  ];

  // User workload data
  const userWorkloadData = users.map(user => {
    const assignedTasks = filteredTasks.filter(task =>
      task.assignedTo && task.assignedTo.includes(user.id)
    );

    const completedAssignedTasks = assignedTasks.filter(task => task.status === 'Complete');

    return {
      id: user.id,
      name: user.name,
      totalTasks: assignedTasks.length,
      completedTasks: completedAssignedTasks.length,
      pendingTasks: assignedTasks.length - completedAssignedTasks.length,
      completionRate: assignedTasks.length > 0
        ? Math.round((completedAssignedTasks.length / assignedTasks.length) * 100)
        : 0
    };
  }).sort((a, b) => b.totalTasks - a.totalTasks);

  // Category distribution data
  const categoryMap: Record<string, number> = {};
  filteredTasks.forEach(task => {
    if (task.category) {
      categoryMap[task.category] = (categoryMap[task.category] || 0) + 1;
    }
  });

  const categoryData = Object.entries(categoryMap)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);

  // Project progress data
  const projectProgressData = projects.map(project => {
    const projectTasks = tasks.filter(task => task.projectId === project.id);
    const completion = calculateProjectCompletion(projectTasks);
    const health = calculateProjectHealth(projectTasks, new Date(project.startDate), new Date(project.endDate));
    const remaining = daysRemaining(new Date(project.endDate));

    return {
      id: project.id,
      name: project.name,
      completion,
      health,
      healthColor: getHealthColor(health),
      remaining,
      totalTasks: projectTasks.length,
      completedTasks: projectTasks.filter(t => t.status === 'Complete').length,
    };
  }).sort((a, b) => a.completion - b.completion);

  // Timeline data (tasks by due date)
  const timelineData: { date: string; count: number }[] = [];
  const dateMap: Record<string, number> = {};

  filteredTasks.forEach(task => {
    const dueDate = formatDate(task.dueDate);
    dateMap[dueDate] = (dateMap[dueDate] || 0) + 1;
  });

  // Sort dates and create timeline data
  Object.entries(dateMap)
    .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
    .forEach(([date, count]) => {
      timelineData.push({ date, count });
    });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <div className="container mx-auto px-4 py-6">
          {/* Context Debugger */}
          <ContextDebugger />

          <div className="flex items-center justify-center mt-20">
            <div className="text-xl text-gray-300">Loading analytics...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <div className="container mx-auto px-4 py-6">
        {/* Context Debugger */}
        <ContextDebugger />

        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-white">Project Analytics</h1>
            <p className="text-gray-400 mt-1">Monitor project progress and performance metrics</p>
          </div>

          <div className="mt-4 md:mt-0 flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            {/* Project filter */}
            <div className="relative">
              <select
                value={selectedProject}
                onChange={(e) => setSelectedProject(e.target.value)}
                className="pl-3 pr-10 py-2 border border-gray-700 rounded-md bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="all">All Projects</option>
                {projects.map(project => (
                  <option key={project.id} value={project.id}>{project.name}</option>
                ))}
              </select>
            </div>

            {/* Time range filter */}
            <div className="relative">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as '7days' | '30days' | '90days' | 'all')}
                className="pl-3 pr-10 py-2 border border-gray-700 rounded-md bg-gray-800 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              >
                <option value="7days">Last 7 Days</option>
                <option value="30days">Last 30 Days</option>
                <option value="90days">Last 90 Days</option>
                <option value="all">All Time</option>
              </select>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-800 rounded-lg p-6 border-l-4 border-purple-500">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-400">Total Projects</p>
                <h2 className="text-3xl font-bold text-white mt-1">{totalProjects}</h2>
                <p className="text-sm text-gray-400 mt-1">
                  {activeProjects} active, {completedProjects} completed
                </p>
              </div>
              <div className="p-2 bg-purple-900/30 rounded-lg">
                <BarChart2 className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border-l-4 border-blue-500">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-400">Task Completion</p>
                <h2 className="text-3xl font-bold text-white mt-1">{completionRate}%</h2>
                <p className="text-sm text-gray-400 mt-1">
                  {completedTasks} of {totalTasks} tasks
                </p>
              </div>
              <div className="p-2 bg-blue-900/30 rounded-lg">
                <CheckSquare className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border-l-4 border-green-500">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-400">Team Members</p>
                <h2 className="text-3xl font-bold text-white mt-1">{users.length}</h2>
                <p className="text-sm text-gray-400 mt-1">
                  Across {projects.length} projects
                </p>
              </div>
              <div className="p-2 bg-green-900/30 rounded-lg">
                <Users className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-6 border-l-4 border-yellow-500">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-gray-400">Avg. Completion Time</p>
                <h2 className="text-3xl font-bold text-white mt-1">
                  {tasks.length > 0 ? Math.round(tasks.reduce((acc, task) => {
                    if (task.status === 'Complete' && task.startDate && task.updatedAt) {
                      return acc + daysBetween(new Date(task.startDate), new Date(task.updatedAt));
                    }
                    return acc;
                  }, 0) / Math.max(1, tasks.filter(t => t.status === 'Complete').length)) : 0} days
                </h2>
                <p className="text-sm text-gray-400 mt-1">
                  Per completed task
                </p>
              </div>
              <div className="p-2 bg-yellow-900/30 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Charts Row 1 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Project Progress */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4 flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-purple-400" />
              Project Progress
            </h2>

            {projectProgressData.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p>No project data available</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-80 overflow-y-auto pr-2">
                {projectProgressData.map(project => (
                  <div key={project.id} className="bg-gray-700 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium text-white">{project.name}</h3>
                      <span
                        className="px-2 py-1 rounded-full text-xs font-medium"
                        style={{ backgroundColor: `${project.healthColor}30`, color: project.healthColor }}
                      >
                        {project.health}
                      </span>
                    </div>

                    <div className="flex justify-between text-sm text-gray-400 mb-2">
                      <span>{project.completedTasks} of {project.totalTasks} tasks</span>
                      <span>{project.completion}%</span>
                    </div>

                    <div className="w-full bg-gray-600 rounded-full h-2.5">
                      <div
                        className="h-2.5 rounded-full"
                        style={{
                          width: `${project.completion}%`,
                          backgroundColor: project.healthColor
                        }}
                      ></div>
                    </div>

                    <div className="mt-2 text-xs text-gray-400">
                      {project.remaining > 0
                        ? `${project.remaining} days remaining`
                        : 'Past due date'}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Task Status Distribution */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4 flex items-center">
              <PieChartIcon className="w-5 h-5 mr-2 text-blue-400" />
              Task Status Distribution
            </h2>

            {taskStatusData.every(item => item.value === 0) ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p>No task data available</p>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={taskStatusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {taskStatusData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={STATUS_COLORS[entry.name as TaskStatus] || COLORS.gray}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => [`${value} tasks`, 'Count']}
                      contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                    />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>
        </div>

        {/* Charts Row 2 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Timeline Chart */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-green-400" />
              Task Timeline
            </h2>

            {timelineData.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p>No timeline data available</p>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={timelineData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      dataKey="date"
                      stroke="#9ca3af"
                      tick={{ fill: '#9ca3af' }}
                    />
                    <YAxis
                      stroke="#9ca3af"
                      tick={{ fill: '#9ca3af' }}
                    />
                    <Tooltip
                      formatter={(value) => [`${value} tasks`, 'Count']}
                      contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                    />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke={COLORS.green}
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>

          {/* Task Priority Distribution */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-yellow-400" />
              Task Priority Distribution
            </h2>

            {taskPriorityData.every(item => item.value === 0) ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p>No priority data available</p>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={taskPriorityData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      dataKey="name"
                      stroke="#9ca3af"
                      tick={{ fill: '#9ca3af' }}
                    />
                    <YAxis
                      stroke="#9ca3af"
                      tick={{ fill: '#9ca3af' }}
                    />
                    <Tooltip
                      formatter={(value) => [`${value} tasks`, 'Count']}
                      contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                    />
                    <Bar dataKey="value">
                      {taskPriorityData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={PRIORITY_COLORS[entry.name as TaskPriority] || COLORS.gray}
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>
        </div>

        {/* Charts Row 3 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Workload */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4 flex items-center">
              <Users className="w-5 h-5 mr-2 text-indigo-400" />
              Team Workload
            </h2>

            {userWorkloadData.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p>No user workload data available</p>
              </div>
            ) : (
              <div className="space-y-4 max-h-80 overflow-y-auto pr-2">
                {userWorkloadData.map(user => (
                  <div key={user.id} className="bg-gray-700 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="font-medium text-white">{user.name}</h3>
                      <span className="text-sm text-gray-400">
                        {user.completedTasks} / {user.totalTasks} tasks
                      </span>
                    </div>

                    <div className="w-full bg-gray-600 rounded-full h-2.5 mb-2">
                      <div
                        className="h-2.5 rounded-full bg-indigo-500"
                        style={{ width: `${user.completionRate}%` }}
                      ></div>
                    </div>

                    <div className="flex justify-between text-xs text-gray-400">
                      <span>{user.completionRate}% complete</span>
                      <span>{user.pendingTasks} pending</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Category Distribution */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-medium text-white mb-4 flex items-center">
              <Tag className="w-5 h-5 mr-2 text-pink-400" />
              Task Categories
            </h2>

            {categoryData.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                <AlertTriangle className="w-12 h-12 mb-2" />
                <p>No category data available</p>
              </div>
            ) : (
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={categoryData.slice(0, 10)} // Show top 10 categories
                    layout="vertical"
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis
                      type="number"
                      stroke="#9ca3af"
                      tick={{ fill: '#9ca3af' }}
                    />
                    <YAxis
                      dataKey="name"
                      type="category"
                      stroke="#9ca3af"
                      tick={{ fill: '#9ca3af' }}
                      width={100}
                    />
                    <Tooltip
                      formatter={(value) => [`${value} tasks`, 'Count']}
                      contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                    />
                    <Bar
                      dataKey="value"
                      fill={COLORS.pink}
                      radius={[0, 4, 4, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
