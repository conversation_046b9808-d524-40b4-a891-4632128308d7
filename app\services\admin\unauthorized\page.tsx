'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { AlertTriangle, ArrowLeft } from 'lucide-react';

export default function UnauthorizedPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 flex items-center justify-center">
      <div className="bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
        <div className="flex items-center justify-center mb-6">
          <AlertTriangle className="w-16 h-16 text-yellow-500" />
        </div>
        <h1 className="text-2xl font-bold text-center mb-4">Access Denied</h1>
        <p className="text-gray-300 text-center mb-6">
          You don't have permission to access this area. Please contact the system administrator to request access.
        </p>
        <div className="flex justify-center">
          <button
            onClick={() => router.push('/')}
            className="flex items-center px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Return to Home
          </button>
        </div>
      </div>
    </div>
  );
}
