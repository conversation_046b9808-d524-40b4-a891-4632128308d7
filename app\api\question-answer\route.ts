/**
 * API route for the QuestionAnswerAgent
 *
 * This route handles requests to the QuestionAnswerAgent, which provides enhanced
 * question answering capabilities by leveraging document search, logical reasoning,
 * and contextual analysis.
 */

import { NextRequest, NextResponse } from 'next/server';
import { questionAnswerAgent } from '../../../components/Agents/QuestionAnswerAgent';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';

export async function POST(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await req.json();
    const {
      userRequest,
      context,
      category,
      previousQuestions,
      maxQuestions,
      enabledTools
    } = body;

    // Validate required fields
    if (!userRequest) {
      return NextResponse.json(
        { error: 'Missing required field: userRequest' },
        { status: 400 }
      );
    }

    // Get user ID from session (using email as ID)
    const userId = session.user.email || 'anonymous-user';

    // Process the request with QuestionAnswerAgent
    const result = await questionAnswerAgent.process({
      userRequest,
      context,
      userId,
      category,
      previousQuestions,
      maxQuestions,
      enabledTools
    });

    // Return the result
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in question-answer API route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}

// For streaming responses (optional)
export async function GET(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const userRequest = url.searchParams.get('userRequest');
    const context = url.searchParams.get('context');
    const category = url.searchParams.get('category');
    const maxQuestions = url.searchParams.get('maxQuestions');

    // Validate required fields
    if (!userRequest) {
      return NextResponse.json(
        { error: 'Missing required parameter: userRequest' },
        { status: 400 }
      );
    }

    // Get user ID from session (using email as ID)
    const userId = session.user.email || 'anonymous-user';

    // Process the request with QuestionAnswerAgent
    const result = await questionAnswerAgent.process({
      userRequest,
      context: context || undefined,
      userId,
      category: category || undefined,
      maxQuestions: maxQuestions ? parseInt(maxQuestions) : undefined
    });

    // Return the result
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in question-answer API route:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Unknown error occurred' },
      { status: 500 }
    );
  }
}
