// research/PMOTaskConverter.ts

import { Task } from '../../../admin/planner/types';
import { ResearchTaskRequest, PMOResearchTaskRequest } from './ResearchInterfaces';
import { AgenticTeamId } from '../pmo/PMOInterfaces';

// Extended Task interface for PMO operations that includes metadata
interface PMOTask extends Task {
  metadata?: {
    source?: string;
    pmoId?: string;
    pmoAssessment?: string;
    teamSelectionRationale?: string;
    requirementsDocument?: string;
    autoTriggered?: boolean;
    triggerTimestamp?: string;
    context?: string;
    outputFormat?: string;
    deadline?: string;
    category?: string;
    assessment?: string;
    crossTeamCoordination?: boolean;
    strategic?: boolean;
    [key: string]: any;
  };
}

/**
 * PMO-Research Task Converter Utility
 *
 * This utility provides standardized conversion between PMO Task format
 * and Research team formats, ensuring seamless integration between
 * PMO workflows and Research team processes.
 */
export class PMOTaskConverter {

  /**
   * Convert PMO Task to ResearchTaskRequest format
   * This is the primary conversion method for standard research workflows
   */
  static convertPMOTaskToResearchBrief(task: PMOTask): ResearchTaskRequest {
    // Determine research depth based on task priority and complexity
    let requiredDepth: 'surface' | 'moderate' | 'deep' = 'moderate';

    if (task.priority === 'High' || task.priority === 'Critical') {
      requiredDepth = 'deep';
    } else if (task.priority === 'Low') {
      requiredDepth = 'surface';
    }

    // Determine output format based on task description and metadata
    let outputFormat: 'summary' | 'report' | 'presentation_points' | 'raw_data' = 'report';

    const description = task.description.toLowerCase();
    const metadata = task.metadata || {};

    if (description.includes('summary') || description.includes('brief') || metadata.outputFormat === 'summary') {
      outputFormat = 'summary';
    } else if (description.includes('presentation') || description.includes('slides') || metadata.outputFormat === 'presentation') {
      outputFormat = 'presentation_points';
    } else if (description.includes('raw') || description.includes('data') || metadata.outputFormat === 'raw_data') {
      outputFormat = 'raw_data';
    }

    // Extract deadline from task or metadata
    let deadline = task.dueDate;
    if (!deadline && metadata.deadline) {
      deadline = new Date(metadata.deadline);
    }

    return {
      taskId: task.id,
      topic: task.title,
      scope: task.description,
      requiredDepth,
      outputFormat,
      deadline,
      requesterInfo: `PMO Task ${task.id} - ${task.category || 'General'}`
    };
  }

  /**
   * Convert PMO Task to enhanced PMOResearchTaskRequest format
   * This method includes PMO-specific context and metadata
   */
  static convertPMOTaskToEnhancedResearchBrief(task: PMOTask): PMOResearchTaskRequest {
    // Start with standard conversion
    const baseRequest = this.convertPMOTaskToResearchBrief(task);

    // Extract PMO-specific metadata
    const metadata = task.metadata || {};

    return {
      ...baseRequest,
      pmoId: metadata.pmoId || (metadata.source === 'PMO' ? task.id : undefined),
      pmoAssessment: metadata.pmoAssessment || metadata.assessment,
      teamSelectionRationale: metadata.teamSelectionRationale || 'Research team selected for comprehensive analysis',
      priority: task.priority?.toUpperCase() || 'MEDIUM',
      category: metadata.category || task.category || 'Research',
      requirementsDocument: metadata.requirementsDocument,
      crossTeamCoordination: this.requiresCrossTeamCoordination(task)
    };
  }

  /**
   * Convert Research results back to PMO Task format for status updates
   */
  static convertResearchResultToPMOTaskUpdate(
    originalTask: PMOTask,
    researchResult: any
  ): Partial<PMOTask> {
    const now = new Date();

    return {
      id: originalTask.id,
      status: researchResult.success ? 'Complete' : 'Not Started',
      updatedAt: now,
      metadata: {
        ...originalTask.metadata,
        researchPlanId: researchResult.researchPlanId,
        strategicPlanUrl: researchResult.documentUrl,
        researchMethodology: researchResult.methodology,
        outputDocumentIds: researchResult.outputDocumentIds || [],
        processedBy: 'Research Team Agent',
        processedAt: now.toISOString(),
        qualityAssurance: researchResult.success ? 'Completed' : 'Failed',
        crossTeamTasks: researchResult.crossTeamTasks || []
      }
    };
  }

  /**
   * Extract PMO context from task metadata
   */
  static extractPMOContext(task: PMOTask): {
    pmoId?: string;
    assessment?: string;
    category?: string;
    requirementsDocument?: string;
    priority?: string;
    teamSelectionRationale?: string;
  } {
    const metadata = task.metadata || {};

    return {
      pmoId: metadata.pmoId || (metadata.source === 'PMO' ? task.id : undefined),
      assessment: metadata.pmoAssessment || metadata.assessment,
      category: metadata.category || task.category,
      requirementsDocument: metadata.requirementsDocument,
      priority: task.priority?.toUpperCase(),
      teamSelectionRationale: metadata.teamSelectionRationale
    };
  }

  /**
   * Determine if task requires cross-team coordination
   */
  static requiresCrossTeamCoordination(task: PMOTask): boolean {
    const crossTeamKeywords = [
      'cross-team', 'cross-functional', 'collaboration', 'coordination',
      'marketing', 'sales', 'business analysis', 'software design',
      'strategic', 'enterprise', 'initiative'
    ];

    const taskText = `${task.title} ${task.description}`.toLowerCase();
    const metadata = task.metadata || {};

    // Check if explicitly marked for cross-team coordination
    if (metadata.crossTeamCoordination === true) {
      return true;
    }

    // Check for cross-team keywords
    return crossTeamKeywords.some(keyword => taskText.includes(keyword));
  }

  /**
   * Determine if task is a PMO strategic task requiring enhanced processing
   */
  static isPMOStrategicTask(task: PMOTask): boolean {
    const strategicKeywords = [
      'strategic', 'pmo', 'cross-team', 'enterprise', 'initiative',
      'coordination', 'planning', 'assessment', 'analysis'
    ];

    const taskText = `${task.title} ${task.description}`.toLowerCase();
    const metadata = task.metadata || {};

    // Check if explicitly marked as strategic
    if (metadata.strategic === true || metadata.source === 'PMO') {
      return true;
    }

    // Check for strategic keywords
    return strategicKeywords.some(keyword => taskText.includes(keyword));
  }

  /**
   * Generate research task summary for PMO reporting
   */
  static generatePMOTaskSummary(task: PMOTask, researchResult?: any): string {
    const timestamp = new Date().toISOString();
    const pmoContext = this.extractPMOContext(task);

    return `# PMO Research Task Summary

## Task Information
- **Task ID**: ${task.id}
- **Title**: ${task.title}
- **PMO ID**: ${pmoContext.pmoId || 'N/A'}
- **Priority**: ${task.priority || 'Medium'}
- **Status**: ${task.status || 'Pending'}
- **Created**: ${task.createdAt?.toISOString() || 'N/A'}
- **Updated**: ${timestamp}

## Research Assignment
- **Assigned Team**: Research Team (${AgenticTeamId.Research})
- **Research Depth**: ${this.determineResearchDepth(task)}
- **Output Format**: ${this.determineOutputFormat(task)}
- **Cross-team Coordination**: ${this.requiresCrossTeamCoordination(task) ? 'Yes' : 'No'}
- **Strategic Task**: ${this.isPMOStrategicTask(task) ? 'Yes' : 'No'}

## Scope and Requirements
${task.description}

${pmoContext.assessment ? `## PMO Assessment\n${pmoContext.assessment}` : ''}

${pmoContext.teamSelectionRationale ? `## Team Selection Rationale\n${pmoContext.teamSelectionRationale}` : ''}

${researchResult ? `## Research Results
- **Research Plan ID**: ${researchResult.researchPlanId || 'N/A'}
- **Methodology**: ${researchResult.methodology || 'Standard Research Workflow'}
- **Output Documents**: ${researchResult.outputDocumentIds?.length || 0} documents
- **Quality Assurance**: ${researchResult.success ? 'Completed' : 'Failed'}
- **Strategic Plan**: ${researchResult.documentUrl ? 'Available' : 'Not applicable'}` : ''}

---
*Generated by PMO Task Converter - Research Team Integration*`;
  }

  /**
   * Helper method to determine research depth from task
   */
  private static determineResearchDepth(task: PMOTask): string {
    if (task.priority === 'High' || task.priority === 'Critical') {
      return 'Deep';
    } else if (task.priority === 'Low') {
      return 'Surface';
    }
    return 'Moderate';
  }

  /**
   * Helper method to determine output format from task
   */
  private static determineOutputFormat(task: PMOTask): string {
    const description = task.description.toLowerCase();
    const metadata = task.metadata || {};

    if (description.includes('summary') || description.includes('brief') || metadata.outputFormat === 'summary') {
      return 'Summary';
    } else if (description.includes('presentation') || description.includes('slides') || metadata.outputFormat === 'presentation') {
      return 'Presentation';
    }
    return 'Report';
  }

  /**
   * Validate PMO task for research team processing
   */
  static validatePMOTaskForResearch(task: PMOTask): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields validation
    if (!task.id) {
      errors.push('Task ID is required');
    }
    if (!task.title) {
      errors.push('Task title is required');
    }
    if (!task.description) {
      errors.push('Task description is required');
    }

    // Warnings for missing optional fields
    if (!task.priority) {
      warnings.push('Task priority not specified, defaulting to Medium');
    }
    if (!task.dueDate) {
      warnings.push('No deadline specified for research task');
    }
    if (!task.category) {
      warnings.push('Task category not specified');
    }

    // PMO-specific validation
    const metadata = task.metadata || {};
    if (metadata.source === 'PMO' && !metadata.pmoId) {
      warnings.push('PMO task missing PMO ID in metadata');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}
