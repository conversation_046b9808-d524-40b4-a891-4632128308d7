'use client';

import React, { useState, useEffect } from 'react';
import { usePlanner } from '../services/context/PlannerContext';
import { TaskComment } from '../../admin/planner/types';
import { Send } from 'lucide-react';
import { format } from 'date-fns';

interface TaskCommentsProps {
  taskId: string;
  currentUser: string;
}

const TaskComments: React.FC<TaskCommentsProps> = ({ taskId, currentUser }) => {
  const { taskComments, fetchTaskComments, addTaskComment, loading } = usePlanner();
  const [comment, setComment] = useState('');
  const [error, setError] = useState('');

  // Fetch comments when component mounts
  useEffect(() => {
    const loadComments = async () => {
      try {
        await fetchTaskComments(taskId);
      } catch (err: any) {
        setError(err.message || 'Failed to load comments');
      }
    };

    loadComments();
  }, [taskId, fetchTaskComments]);

  // Get comments for this task
  const comments = taskComments[taskId] || [];

  // Handle comment submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!comment.trim()) return;

    try {
      await addTaskComment(taskId, comment, currentUser);
      setComment('');
      setError('');
    } catch (err: any) {
      setError(err.message || 'Failed to add comment');
    }
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return format(new Date(date), 'MMM d, yyyy h:mm a');
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-lg font-medium text-white mb-4">Comments</h3>

      {/* Comments list */}
      <div className="space-y-4 mb-4 max-h-[400px] overflow-y-auto">
        {comments.length === 0 ? (
          <div className="text-center py-4 text-gray-400">
            <p>No comments yet</p>
          </div>
        ) : (
          comments.map((comment: TaskComment) => (
            <div key={comment.id} className="bg-gray-700 rounded-lg p-3">
              <div className="flex justify-between items-start">
                <div className="font-medium text-white">{comment.createdBy}</div>
                <div className="text-xs text-gray-400">{formatDate(comment.createdAt)}</div>
              </div>
              <div className="mt-2 text-gray-300">{comment.content}</div>
            </div>
          ))
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-500 text-red-200 px-4 py-2 rounded-md mb-4">
          {error}
        </div>
      )}

      {/* Comment form */}
      <form onSubmit={handleSubmit} className="mt-4">
        <div className="flex">
          <input
            type="text"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Add a comment..."
            className="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          />
          <button
            type="submit"
            disabled={loading || !comment.trim()}
            className="bg-purple-600 text-white px-4 py-2 rounded-r-md hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed flex items-center"
          >
            <Send className="h-4 w-4 mr-2" />
            Send
          </button>
        </div>
      </form>
    </div>
  );
};

export default TaskComments;
