/**
 * Demonstration Script for Adaptive Truncation Feature
 * 
 * This script demonstrates how the adaptive truncation feature works
 * with different file types and shows the cost savings achieved.
 */

const { CodebaseIndexingTool } = require('../lib/tools/codebase-indexing-tool');

// Mock storage tool for demonstration
const mockStorageTool = {
  savePdfToByteStore: () => Promise.resolve('mock-result')
};

// Create sample file contents for different types
const sampleFiles = {
  'package.json': {
    content: JSON.stringify({
      "name": "my-project",
      "version": "1.0.0",
      "description": "A sample project with many dependencies",
      "dependencies": {
        "react": "^18.0.0",
        "next": "^13.0.0",
        "typescript": "^4.9.0",
        "tailwindcss": "^3.0.0",
        "@types/react": "^18.0.0",
        "@types/node": "^18.0.0",
        "eslint": "^8.0.0",
        "prettier": "^2.8.0"
      },
      "devDependencies": {
        "jest": "^29.0.0",
        "@testing-library/react": "^13.0.0",
        "@testing-library/jest-dom": "^5.16.0"
      },
      "scripts": {
        "dev": "next dev",
        "build": "next build",
        "start": "next start",
        "test": "jest",
        "lint": "eslint . --ext .ts,.tsx,.js,.jsx"
      }
    }, null, 2) + '\n'.repeat(50), // Add extra content to exceed 1000 chars
    language: 'JSON'
  },

  'types/user.types.ts': {
    content: `
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  preferences: UserPreferences;
  roles: Role[];
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  marketing: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private' | 'friends';
  showEmail: boolean;
  showPhone: boolean;
  allowDataCollection: boolean;
}

export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete';
}

export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending';

export interface CreateUserRequest {
  email: string;
  name: string;
  password: string;
  roleIds?: string[];
}

export interface UpdateUserRequest {
  name?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}
    `.trim() + '\n'.repeat(30), // Add extra content
    language: 'TypeScript'
  },

  'api/users/route.ts': {
    content: `
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { CreateUserRequest, UpdateUserRequest } from '@/types/user.types';
import { validateEmail, validatePassword } from '@/lib/validation';
import { hashPassword } from '@/lib/crypto';
import { sendWelcomeEmail } from '@/lib/email';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    const users = await prisma.user.findMany({
      where: search ? {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      } : {},
      skip: (page - 1) * limit,
      take: limit,
      include: {
        roles: true,
        preferences: true
      },
      orderBy: { createdAt: 'desc' }
    });

    const total = await prisma.user.count({
      where: search ? {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ]
      } : {}
    });

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session || !session.user.roles.includes('admin')) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body: CreateUserRequest = await request.json();
    
    // Validation
    if (!validateEmail(body.email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    if (!validatePassword(body.password)) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: body.email }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'User already exists' },
        { status: 409 }
      );
    }

    // Create user
    const hashedPassword = await hashPassword(body.password);
    const user = await prisma.user.create({
      data: {
        email: body.email,
        name: body.name,
        password: hashedPassword,
        roles: body.roleIds ? {
          connect: body.roleIds.map(id => ({ id }))
        } : undefined
      },
      include: {
        roles: true
      }
    });

    // Send welcome email
    await sendWelcomeEmail(user.email, user.name);

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
    `.trim() + '\n'.repeat(20), // Add extra content
    language: 'TypeScript'
  },

  'utils/helpers.ts': {
    content: `
export function formatDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
    `.trim() + '\n'.repeat(40), // Add extra content
    language: 'TypeScript'
  }
};

async function demonstrateAdaptiveTruncation() {
  console.log('🚀 Adaptive Truncation Demonstration\n');
  console.log('=' .repeat(60));

  const indexingTool = new CodebaseIndexingTool(mockStorageTool);
  
  // Test each file type
  for (const [filePath, fileData] of Object.entries(sampleFiles)) {
    console.log(`\n📁 File: ${filePath}`);
    console.log(`📏 Original size: ${fileData.content.length} characters`);
    
    // Get adaptive context limit
    const contextLimit = indexingTool.getAdaptiveContextLimit(
      filePath, 
      fileData.language, 
      fileData.content
    );
    console.log(`🎯 Adaptive limit: ${contextLimit} characters`);
    
    // Apply smart truncation
    const truncatedContent = indexingTool.smartTruncateContent(
      fileData.content, 
      contextLimit
    );
    console.log(`✂️  Truncated size: ${truncatedContent.length} characters`);
    
    // Calculate savings
    const { savedTokens, savedCost } = indexingTool.calculateCostSavings(
      fileData.content.length,
      truncatedContent.length
    );
    
    const reductionPercent = ((fileData.content.length - truncatedContent.length) / fileData.content.length * 100).toFixed(1);
    
    console.log(`💰 Tokens saved: ${savedTokens}`);
    console.log(`💵 Cost saved: $${savedCost.toFixed(6)}`);
    console.log(`📊 Reduction: ${reductionPercent}%`);
    
    // Show truncated content preview
    console.log(`📄 Truncated content preview:`);
    console.log('─'.repeat(40));
    console.log(truncatedContent.substring(0, 200) + (truncatedContent.length > 200 ? '...' : ''));
    console.log('─'.repeat(40));
  }

  // Calculate total savings
  const totalOriginal = Object.values(sampleFiles).reduce((sum, file) => sum + file.content.length, 0);
  const totalTruncated = Object.values(sampleFiles).reduce((sum, file) => {
    const limit = indexingTool.getAdaptiveContextLimit(Object.keys(sampleFiles)[Object.values(sampleFiles).indexOf(file)], file.language, file.content);
    return sum + indexingTool.smartTruncateContent(file.content, limit).length;
  }, 0);

  const { savedTokens: totalSavedTokens, savedCost: totalSavedCost } = indexingTool.calculateCostSavings(totalOriginal, totalTruncated);
  const totalReductionPercent = ((totalOriginal - totalTruncated) / totalOriginal * 100).toFixed(1);

  console.log('\n' + '='.repeat(60));
  console.log('📊 TOTAL SAVINGS SUMMARY');
  console.log('='.repeat(60));
  console.log(`📏 Total original: ${totalOriginal.toLocaleString()} characters`);
  console.log(`✂️  Total truncated: ${totalTruncated.toLocaleString()} characters`);
  console.log(`💰 Total tokens saved: ${totalSavedTokens.toLocaleString()}`);
  console.log(`💵 Total cost saved: $${totalSavedCost.toFixed(6)}`);
  console.log(`📊 Total reduction: ${totalReductionPercent}%`);
  console.log('\n✨ Adaptive truncation preserves context while optimizing costs!');
}

// Run the demonstration
if (require.main === module) {
  demonstrateAdaptiveTruncation().catch(console.error);
}

module.exports = { demonstrateAdaptiveTruncation };
