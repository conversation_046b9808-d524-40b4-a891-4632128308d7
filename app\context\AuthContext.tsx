'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  User as FirebaseUser,
  GoogleAuthProvider,
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { useSession, signIn, signOut as nextAuthSignOut } from 'next-auth/react';
import { auth } from '../lib/firebase/config';
import { getUsers, initializeUsers, checkUserAuthorized, updateUser } from '../lib/firebase/planner';
import { User } from '../../admin/planner/types';

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  error: string | null;
  isAuthorized: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  checkAuthorization: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { data: session, status: sessionStatus } = useSession();
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Initialize users if they don't exist
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeUsers();
      } catch (err) {
        console.error('Error initializing users:', err);
      }
    };

    initialize();
  }, []);

  // Listen for NextAuth session changes
  useEffect(() => {
    const handleSessionChange = async () => {
      setLoading(true);

      if (sessionStatus === 'loading') {
        return;
      }

      // Special case for system admin
      if (session?.user?.email === '<EMAIL>') {
        console.log('System admin detected in session, setting isAuthorized to true immediately');
        setIsAuthorized(true);

        // Try to find the admin user in the database
        try {
          const allUsers = await getUsers();
          const adminUser = allUsers.find(u => u.email === '<EMAIL>');

          if (adminUser && session.user && session.user.image) {
            // Update the admin's avatar with their Google profile picture if available
            if (adminUser.avatar !== session.user.image) {
              console.log('Updating admin avatar with Google profile picture:', session.user.image);
              try {
                await updateUser(adminUser.id, { avatar: session.user.image });
                adminUser.avatar = session.user.image;
                console.log('Successfully updated admin avatar in Firestore');
              } catch (avatarErr) {
                console.error('Error updating admin avatar:', avatarErr);
              }
            }
            setUser(adminUser);
          } else {
            // Admin not found in database, this shouldn't happen but we'll handle it
            console.warn('Admin user not found in database, this should not happen');
            // Create a temporary user object for the admin
            if (session.user) {
              setUser({
                id: 'admin',
                name: 'Admin User',
                email: '<EMAIL>',
                role: 'admin',
                avatar: session.user.image || '/avatars/admin.png',
                availability: 'Full-time',
                isAuthorized: true,
                createdAt: new Date(),
                updatedAt: new Date(),
                photoURL: session.user.image || '/avatars/admin.png',
                displayName: 'Admin User'
              });
            }
          }
        } catch (err) {
          console.error('Error fetching admin user data:', err);
          // Create a temporary user object for the admin even if there's an error
          if (session?.user) {
            setUser({
              id: 'admin',
              name: 'Admin User',
              email: '<EMAIL>',
              role: 'admin',
              avatar: session.user.image || '/avatars/admin.png',
              availability: 'Full-time',
              isAuthorized: true,
              createdAt: new Date(),
              updatedAt: new Date(),
              photoURL: session.user.image || '/avatars/admin.png',
              displayName: 'Admin User'
            });
          } else {
            setUser({
              id: 'admin',
              name: 'Admin User',
              email: '<EMAIL>',
              role: 'admin',
              avatar: '/avatars/admin.png',
              availability: 'Full-time',
              isAuthorized: true,
              createdAt: new Date(),
              updatedAt: new Date(),
              photoURL: '/avatars/admin.png',
              displayName: 'Admin User'
            });
          }
        }
      }
      // Normal case for other users
      else if (session && session.user) {
        try {
          // Check if user exists in our database
          const allUsers = await getUsers();
          const matchingUser = allUsers.find(u => u.email === session?.user?.email);

          if (matchingUser && session.user && session.user.image) {
            // Update the user's avatar with their Google profile picture if available
            if (matchingUser.avatar !== session.user.image) {
              console.log('Updating user avatar with Google profile picture:', session.user.image);
              try {
                await updateUser(matchingUser.id, { avatar: session.user.image });
                matchingUser.avatar = session.user.image;
                console.log('Successfully updated user avatar in Firestore');
              } catch (avatarErr) {
                console.error('Error updating user avatar:', avatarErr);
                // Still use the Google profile image in the local user object even if Firestore update fails
                matchingUser.avatar = session.user.image;
              }
            }
            setUser(matchingUser);

            // Check if user is authorized
            if (session.user && session.user.email) {
              const userEmail = session.user.email;
              const authorized = await checkUserAuthorized(userEmail);
              console.log('User authorization check:', { email: userEmail, authorized });
              setIsAuthorized(authorized);

              // Update the user object with the isAuthorized property
              matchingUser.isAuthorized = authorized;
              setUser({...matchingUser});
            }
          } else if (session.user) {
            // User not found in database
            console.warn('User not found in database:', session.user.email || 'unknown email');

            // Check if the user should be authorized
            const userEmail = session.user.email || '';
            const authorized = await checkUserAuthorized(userEmail);
            setIsAuthorized(authorized);

            if (authorized) {
              // Create a temporary user object for authorized users
              const tempUser: User = {
                id: 'temp-' + Date.now(),
                name: session.user.name || 'New User',
                email: userEmail,
                role: 'user', // Using UserRole type
                avatar: session.user.image || '/avatars/default.png',
                availability: 'Full-time',
                isAuthorized: true,
                createdAt: new Date(),
                updatedAt: new Date(),
                photoURL: session.user.image || '/avatars/default.png',
                displayName: session.user.name || 'New User'
              };
              setUser(tempUser);
              console.log('Created temporary user object for authorized user:', tempUser);
            } else {
              setUser(null);
              setError('User not found in database');
            }
          }
        } catch (err) {
          console.error('Error fetching user data:', err);

          // Check if this is a system admin even if there was an error
          if (session?.user?.email === '<EMAIL>') {
            setIsAuthorized(true);
            // Create a temporary admin user object
            setUser({
              id: 'admin',
              name: 'Admin User',
              email: '<EMAIL>',
              role: 'admin',
              avatar: session?.user?.image || '/avatars/admin.png',
              availability: 'Full-time',
              isAuthorized: true,
              createdAt: new Date(),
              updatedAt: new Date(),
              photoURL: session?.user?.image || '/avatars/admin.png',
              displayName: 'Admin User'
            });
          } else {
            setError('Failed to load user data');
          }
        }
      } else {
        setUser(null);
        setIsAuthorized(false);
      }

      setLoading(false);
    };

    handleSessionChange();
  }, [session, sessionStatus]);

  // Also listen for Firebase auth state changes for backward compatibility
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      setFirebaseUser(firebaseUser);
    });

    return () => unsubscribe();
  }, []);

  const signInWithGoogle = async () => {
    setLoading(true);
    setError(null);

    try {
      // Use NextAuth for Google sign-in
      await signIn('google', { callbackUrl: '/services/admin' });

      // Also sign in with Firebase for backward compatibility
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
    } catch (err: any) {
      console.error('Google sign in error:', err);
      setError(err.message || 'Failed to sign in with Google');
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    setLoading(true);
    setError(null);

    try {
      // Sign out from NextAuth
      await nextAuthSignOut({ callbackUrl: '/services/admin/login' });

      // Also sign out from Firebase for backward compatibility
      await firebaseSignOut(auth);

      // Reset state
      setUser(null);
      setIsAuthorized(false);
    } catch (err: any) {
      console.error('Sign out error:', err);
      setError(err.message || 'Failed to sign out');
    } finally {
      setLoading(false);
    }
  };

  const checkAuthorization = async (): Promise<boolean> => {
    // First check NextAuth session
    if (session?.user?.email === '<EMAIL>') {
      console.log('System admin detected in NextAuth session in checkAuthorization');
      setIsAuthorized(true);
      return true;
    }

    // Then check Firebase user
    if (!firebaseUser?.email) {
      console.log('No email found in firebaseUser');
      return false;
    }

    try {
      console.log('Checking authorization for:', firebaseUser.email);

      // System admin is always authorized
      if (firebaseUser.email === '<EMAIL>') {
        console.log('System admin detected in checkAuthorization');
        setIsAuthorized(true);
        return true;
      }

      const authorized = await checkUserAuthorized(firebaseUser.email || '');
      console.log('Authorization result:', authorized);
      setIsAuthorized(authorized);
      return authorized;
    } catch (err) {
      console.error('Error checking authorization:', err);
      setIsAuthorized(false);
      return false;
    }
  };

  // Check authorization when user changes
  useEffect(() => {
    if (user && firebaseUser?.email) {
      checkAuthorization();
    }
  }, [user, firebaseUser]);

  const value = {
    user,
    firebaseUser,
    loading,
    error,
    isAuthorized,
    signInWithGoogle,
    signOut,
    checkAuthorization
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
