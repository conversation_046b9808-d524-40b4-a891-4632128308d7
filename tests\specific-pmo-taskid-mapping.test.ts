/**
 * Test for specific PMO TaskId mapping issue
 * PMO ID: c76670a7-bc7b-44ea-9905-189a4bcf36c8
 * Task: "Perform Continuous Multi-Stage Quality Assurance & PMO Compliance"
 */

import { PMOProjectsTaskAgent } from '../lib/agents/pmoProjectsTaskAgent';

describe('Specific PMO TaskId Mapping Issue', () => {
  let agent: PMOProjectsTaskAgent;

  beforeEach(() => {
    agent = new PMOProjectsTaskAgent();
  });

  describe('PMO ID: c76670a7-bc7b-44ea-9905-189a4bcf36c8', () => {
    const specificPmoId = 'c76670a7-bc7b-44ea-9905-189a4bcf36c8';
    const expectedProjectId = 'PMO-QA-COMPLIANCE-c76670a7';

    test('should generate correct PMO Project ID for specific PMO ID', () => {
      const content = 'Task ID: QA_001 - Quality Assurance Task';
      
      const result = (agent as any)._mapTaskIdsToProjectIds(content, specificPmoId);
      
      expect(result).toContain(expectedProjectId);
      expect(result).not.toContain('QA_001');
    });

    test('should handle Quality Assurance specific TaskIds', () => {
      const content = `
        Task ID: QA_001 - Initial Quality Review
        TaskId: COMPLIANCE_002 - Compliance Check
        ID: QA-003 - Final Quality Assurance
        Task COMPLIANCE-004 - PMO Compliance Review
      `;
      
      const result = (agent as any)._mapTaskIdsToProjectIds(content, specificPmoId);
      
      // Should map all QA and Compliance TaskIds to PMO format
      expect(result).toContain(`Task ID: ${expectedProjectId}`);
      expect(result).toContain(`TaskId: ${expectedProjectId}`);
      expect(result).toContain(`ID: ${expectedProjectId}`);
      expect(result).toContain(`Task ${expectedProjectId}`);
      
      // Should not contain original TaskIds
      expect(result).not.toContain('QA_001');
      expect(result).not.toContain('COMPLIANCE_002');
      expect(result).not.toContain('QA-003');
      expect(result).not.toContain('COMPLIANCE-004');
    });

    test('should handle task title "Perform Continuous Multi-Stage Quality Assurance & PMO Compliance"', () => {
      const taskTitle = 'Perform Continuous Multi-Stage Quality Assurance & PMO Compliance';
      const content = `
        # ${taskTitle}
        
        Task ID: QA_001 - Multi-stage quality review process
        TaskId: COMPLIANCE_002 - PMO compliance verification
        
        ## Quality Assurance Steps
        1. Initial QA review (Task QA-003)
        2. Compliance check (ID: COMPLIANCE-004)
        3. Final verification (QA_005)
      `;
      
      const result = (agent as any)._mapTaskIdsToProjectIds(content, specificPmoId);
      
      // Task title should remain unchanged
      expect(result).toContain(taskTitle);
      
      // All TaskIds should be mapped to PMO format
      expect(result).toContain(`Task ID: ${expectedProjectId}`);
      expect(result).toContain(`TaskId: ${expectedProjectId}`);
      expect(result).toContain(`Task ${expectedProjectId}`);
      expect(result).toContain(`ID: ${expectedProjectId}`);
      
      // Original TaskIds should be replaced
      expect(result).not.toContain('QA_001');
      expect(result).not.toContain('COMPLIANCE_002');
      expect(result).not.toContain('QA-003');
      expect(result).not.toContain('COMPLIANCE-004');
      expect(result).not.toContain('QA_005');
    });

    test('should preserve existing PMO format TaskIds', () => {
      const content = `
        Task ID: PMO-QA-COMPLIANCE-c76670a7 - Already correct format
        TaskId: QA_002 - Needs mapping
        ID: PMO-PROJECT-001 - Another correct format
      `;
      
      const result = (agent as any)._mapTaskIdsToProjectIds(content, specificPmoId);
      
      // Should preserve existing PMO formats
      expect(result).toContain('Task ID: PMO-QA-COMPLIANCE-c76670a7');
      expect(result).toContain('ID: PMO-PROJECT-001');
      
      // Should map the one that needs mapping
      expect(result).toContain(`TaskId: ${expectedProjectId}`);
      expect(result).not.toContain('QA_002');
    });

    test('should handle enhanced notes generation with PMO metadata', async () => {
      const mockTask = {
        title: 'Perform Continuous Multi-Stage Quality Assurance & PMO Compliance',
        description: 'Comprehensive quality assurance and compliance verification process',
        category: 'Quality Assurance',
        teamAssignment: 'Quality Assurance Team',
        estimatedDuration: '3 weeks',
        dependencies: []
      };

      const mockAgentOutput = {
        agentType: 'strategic-director',
        pmoMetadata: {
          pmoId: specificPmoId
        },
        result: {
          output: `
            Task ID: QA_001 - Initial quality review
            TaskId: COMPLIANCE_002 - Compliance verification
            
            Quality assurance process includes:
            1. Multi-stage review (Task QA-003)
            2. PMO compliance check (ID: COMPLIANCE-004)
          `
        }
      };

      // Mock the Google AI call
      jest.mock('../../lib/tools/google-ai', () => ({
        processWithGoogleAI: jest.fn().mockResolvedValue(`
          Enhanced task notes for quality assurance and compliance.
          
          Task ID: ${expectedProjectId} - Mapped correctly
          TaskId: ${expectedProjectId} - Also mapped correctly
        `)
      }));

      const result = await (agent as any)._generateEnhancedNotes(
        mockTask, 
        mockAgentOutput, 
        expectedProjectId
      );

      expect(result).toContain(`PMO Project ID: ${expectedProjectId}`);
      expect(result).toContain(`PMO Record ID: ${specificPmoId}`);
      expect(result).toContain('Quality Assurance Team');
    });
  });

  describe('General PMO TaskId Mapping', () => {
    test('should handle other PMO IDs correctly', () => {
      const otherPmoId = '9978d39b-2483-4478-8a94-ab20f61185d2';
      const content = 'Task ID: MKT_001 - Marketing Task';
      
      const result = (agent as any)._mapTaskIdsToProjectIds(content, otherPmoId);
      
      expect(result).toContain('PMO-9978d39b');
      expect(result).not.toContain('MKT_001');
    });
  });
});
