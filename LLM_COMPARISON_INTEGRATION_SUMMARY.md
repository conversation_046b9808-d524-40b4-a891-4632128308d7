# LLM Comparison Integration with Investigative Research Agent

## Overview

This document outlines the successful integration of the LLM Comparison methodology with the Investigative Research Agent, providing users with the ability to specify their own comparison models while maintaining investigative journalism capabilities.

## Problem Statement

The original implementation had journalist personas tied to specific LLM models, which didn't align with the true LLM comparison methodology where users should be able to specify their own comparison models for analysis.

## Solution: Full Integration (Option C)

### ✅ **Key Questions Answered:**

1. **Are journalist personas different LLM models in disguise?**
   - **YES** - Each journalist persona was previously tied to a specific model (e.g., Financial Reporter → GPT-4o, Investigative Journalist → Claude Sonnet)

2. **Does each journalist use a different LLM model?**
   - **YES** - The system now supports both predefined journalist-model pairings AND user-specified comparison models

3. **How does journalist selection relate to comparisonModels?**
   - **INTEGRATED** - Users can now choose between:
     - Traditional journalist personas (backward compatibility)
     - Direct LLM comparison with user-specified models
     - Automatic fallback to investigation-type defaults

4. **Should the 3 reports represent outputs from 3 different LLM models?**
   - **YES** - The system now fully supports this through the `comparisonModels` parameter

## Implementation Details

### 1. Enhanced Interface Support

**Files Modified:**
- `lib/agents/investigative/InvestigativeResearchAgent.ts`
- `lib/agents/investigative/InvestigativeResearchAgentManager.ts`
- `app/api/investigative-research/route.ts`

**New Parameters:**
```typescript
interface InvestigationRequest {
  // ... existing fields
  selectedJournalists?: string[]; // Now optional
  comparisonModels?: Array<{model: string, provider: LlmProvider}> | string[];
}
```

### 2. Dynamic Investigation Configuration

**New Method:** `createLLMComparisonConfigs()`

Converts user-specified comparison models into investigative analyst configurations:

```typescript
// Input: ['gpt-4o', 'claude-sonnet-4-0', 'gemini-2.5-pro']
// Output: 3 investigative analyst configurations with:
// - Model-specific names: "gpt-4o Investigative Analyst"
// - Investigation angles: "analytical", "comprehensive", "critical"
// - Investigation-type specific expertise
```

### 3. Flexible Investigation Modes

**Mode 1: LLM Comparison (New)**
```javascript
{
  comparisonModels: [
    { model: 'gpt-4o', provider: 'openai' },
    { model: 'claude-sonnet-4-0', provider: 'anthropic' },
    { model: 'gemini-2.5-pro', provider: 'google' }
  ]
}
```

**Mode 2: Journalist Personas (Backward Compatible)**
```javascript
{
  selectedJournalistIds: ['financial-reporter', 'investigative-journalist', 'technology-analyst']
}
```

**Mode 3: Auto-Selection (Fallback)**
```javascript
{
  // Neither comparisonModels nor selectedJournalistIds provided
  // System selects default journalists for investigation type
}
```

### 4. Priority Logic

1. **comparisonModels** takes precedence if provided
2. **selectedJournalistIds** used if comparisonModels not provided
3. **Auto-selection** if neither provided

## Expected Agent_Output Structure

### LLM Comparison Mode
For PMO request with `comparisonModels: ['gpt-4o', 'claude-sonnet-4-0', 'gemini-2.5-pro']`:

```
PMO-001_journalist_1:
  journalistName: "gpt-4o Investigative Analyst"
  model: "gpt-4o"
  provider: "openai"
  investigationAngle: "analytical and data-driven perspective"

PMO-001_journalist_2:
  journalistName: "claude-sonnet-4-0 Investigative Analyst"
  model: "claude-sonnet-4-0"
  provider: "anthropic"
  investigationAngle: "comprehensive and methodical perspective"

PMO-001_journalist_3:
  journalistName: "gemini-2.5-pro Investigative Analyst"
  model: "gemini-2.5-pro"
  provider: "google"
  investigationAngle: "critical and investigative perspective"

PMO-001_assessment:
  Contains comparative analysis with model-specific scores and reasoning
```

### Journalist Persona Mode (Unchanged)
```
PMO-001_journalist_1:
  journalistName: "Sarah Chen"
  model: "gpt-4o"
  provider: "openai"
  investigationAngle: "Financial perspective with analytical approach"
```

## Integration Benefits

### ✅ **User Control**
- Users can specify exact models for comparison
- No longer limited to predefined journalist-model pairings
- Full compatibility with LLM comparison methodology

### ✅ **Flexibility**
- Supports both string arrays and object arrays for models
- Automatic provider detection for string format
- Maintains backward compatibility with existing workflows

### ✅ **Consistency**
- Uses same criteria generation as `/api/llm-comparison`
- Uses same prompt optimization workflow
- Uses same assessment and comparison logic

### ✅ **Specialization**
- Maintains investigative research focus
- Applies investigative-specific prompting to all models
- Includes internet search and document analysis
- Preserves source verification requirements

## API Usage Examples

### Example 1: Pure LLM Comparison
```javascript
POST /api/investigative-research
{
  "action": "conduct",
  "pmoId": "PMO-001",
  "title": "Market Analysis",
  "investigationType": "FINANCIAL",
  "comparisonModels": ["gpt-4o", "claude-sonnet-4-0", "gemini-2.5-pro"],
  "userId": "<EMAIL>"
}
```

### Example 2: Object Format with Providers
```javascript
POST /api/investigative-research
{
  "action": "conduct",
  "pmoId": "PMO-002",
  "title": "Technology Trends",
  "investigationType": "TECHNOLOGY",
  "comparisonModels": [
    { "model": "gpt-4o", "provider": "openai" },
    { "model": "claude-sonnet-4-0", "provider": "anthropic" },
    { "model": "gemini-2.5-pro", "provider": "google" }
  ],
  "userId": "<EMAIL>"
}
```

### Example 3: Traditional Journalist Personas
```javascript
POST /api/investigative-research
{
  "action": "conduct",
  "pmoId": "PMO-003",
  "title": "Political Analysis",
  "investigationType": "POLITICAL",
  "selectedJournalistIds": ["political-correspondent", "investigative-journalist"],
  "userId": "<EMAIL>"
}
```

## Conclusion

The Investigative Research Agent now provides **true LLM comparison capabilities** while maintaining its investigative journalism specialization. Users can:

1. **Specify their own comparison models** for pure LLM comparison
2. **Use traditional journalist personas** for specialized investigative approaches
3. **Rely on automatic selection** for investigation-type defaults

The integration maintains **full backward compatibility** while providing the **flexibility and user control** expected from a true LLM comparison system. Each PMO request still results in **4 comprehensive Agent_Output documents** with complete investigation content, accurate timestamps, and detailed assessment results.

## Files Modified

1. `lib/agents/investigative/InvestigativeResearchAgent.ts` - Added LLM comparison support
2. `lib/agents/investigative/InvestigativeResearchAgentManager.ts` - Enhanced storage and interfaces
3. `app/api/investigative-research/route.ts` - Added comparisonModels parameter support
