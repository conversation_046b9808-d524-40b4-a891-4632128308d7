// DialogueDisplay.tsx with SimpleScriptFormatter integration
// This implementation uses the minimal, focused approach for clean results

import React, { useRef, useEffect } from "react";
import { MessageSquare, User, Bot } from "lucide-react";
import EnhancedMarkdownContent from "../../components/scriptreaderAI/EnhancedMarkdownContent";
import { processScriptContent } from "../../components/scriptreaderAI/SimpleScriptFormatter";

interface DialogueDisplayProps {
  dialogue: Array<{role: string, content: string}>;
  currentResponse: string;
  isSpeaking: boolean;
  isListening: boolean;
  onFollowupClick?: (text: string) => void;
  onCopyCode?: (code: string) => void;
  enableScriptFormatting?: boolean;
}

export default function DialogueDisplay({
  dialogue,
  currentResponse,
  isSpeaking,
  isListening,
  onFollowupClick,
  onCopyCode,
  enableScriptFormatting = true
}: DialogueDisplayProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Role identification helpers
  const isUserRole = (role: string): boolean => role.toLowerCase() === "user";
  const isAssistantRole = (role: string): boolean => !isUserRole(role);
  
  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [dialogue, currentResponse]);
  
  // Connection status indicator
  const connectionStatus = () => {
    if (!isListening) {
      return (
        <div className="text-center text-xs text-yellow-300 py-1 px-3 bg-yellow-900/20 rounded-md">
          No active connection - Voice conversation will appear here once connected
        </div>
      );
    }
    
    if (isSpeaking) {
      return (
        <div className="text-center text-xs text-green-300 py-1 px-3 bg-green-900/20 rounded-md">
          Scene Mate is speaking
        </div>
      );
    }
    
    return (
      <div className="text-center text-xs text-blue-300 py-1 px-3 bg-blue-900/20 rounded-md">
        Connection active - Transcribing conversation
      </div>
    );
  };
  
  // Empty state display
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full text-gray-400">
      <MessageSquare className="w-12 h-12 mb-4 opacity-30" />
      <p className="text-lg">Conversation Transcript</p>
      <p className="text-sm mt-2 mb-4">
        {isListening 
          ? "Begin speaking in the Rehearsing tab to see dialogue appear here" 
          : "Connect in the Rehearsing tab to start your session"}
      </p>
      
      {connectionStatus()}
    </div>
  );
  
  // Process content before rendering
  const prepareContent = (content: string, role: string): string => {
    if (enableScriptFormatting && isAssistantRole(role)) {
      return processScriptContent(content, role);
    }
    return content;
  };
  
  // Code highlighting theme
  const codeTheme = {
    "hljs-comment": { color: "#6A9955" },
    "hljs-keyword": { color: "#569CD6" },
    "hljs-string": { color: "#CE9178" },
    "hljs-number": { color: "#B5CEA8" },
    "hljs-function": { color: "#DCDCAA" },
    "hljs-title": { color: "#DCDCAA" },
    "hljs-params": { color: "#9CDCFE" },
    "hljs-built_in": { color: "#4EC9B0" },
    "hljs-literal": { color: "#569CD6" },
    "hljs-type": { color: "#4EC9B0" },
    "hljs-tag": { color: "#569CD6" },
    "hljs-name": { color: "#9CDCFE" },
    "hljs-attr": { color: "#9CDCFE" },
    "hljs-selector-id": { color: "#D7BA7D" },
    "hljs-selector-class": { color: "#D7BA7D" },
    "hljs-attribute": { color: "#9CDCFE" },
    "hljs-regexp": { color: "#D16969" },
    "hljs-meta": { color: "#DCDCAA" }
  };
  
  return (
    <div className="flex flex-col h-full">
      {/* Connection status bar */}
      {(dialogue.length > 0 || currentResponse) && (
        <div className="p-2 border-b border-white/10 bg-black/20">
          {connectionStatus()}
        </div>
      )}
      
      {/* Conversation display area */}
      <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
        {dialogue.length === 0 && !currentResponse ? (
          renderEmptyState()
        ) : (
          <div className="space-y-4">
            {/* Render historical dialogue messages */}
            {dialogue.map((message, index) => (
              <div 
                key={index} 
                className={`flex ${isUserRole(message.role) ? "justify-end" : "justify-start"}`}
              >
                <div 
                  className={`max-w-[80%] rounded-lg p-3 ${
                    isUserRole(message.role) 
                      ? "bg-purple-500/20 text-purple-100" 
                      : "bg-gray-700/50 text-gray-100"
                  }`}
                >
                  {/* Message sender identifier */}
                  <div className="flex items-center mb-1">
                    {isUserRole(message.role) ? (
                      <>
                        <span className="text-xs font-medium">You</span>
                        <User className="w-3 h-3 ml-1" />
                      </>
                    ) : (
                      <>
                        <span className="text-xs font-medium">{message.role}</span>
                        <Bot className="w-3 h-3 ml-1" />
                      </>
                    )}
                  </div>
                  
                  {/* Message content with optional formatting */}
                  <div className="text-sm script-content">
                    <EnhancedMarkdownContent 
                      content={prepareContent(message.content || "", message.role)}
                      onItemClick={onFollowupClick}
                      customTheme={codeTheme}
                      onCopyCode={onCopyCode}
                    />
                  </div>
                </div>
              </div>
            ))}
            
            {/* Current streaming response */}
            {currentResponse && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-lg p-3 bg-gray-700/50 text-gray-100">
                  <div className="flex items-center mb-1">
                    <span className="text-xs font-medium">Scene Mate</span>
                    <Bot className="w-3 h-3 ml-1" />
                  </div>
                  
                  {/* Streaming response content */}
                  <div className="text-sm script-content">
                    <EnhancedMarkdownContent 
                      content={prepareContent(currentResponse || "", "Scene Mate")}
                      onItemClick={onFollowupClick}
                      customTheme={codeTheme}
                      onCopyCode={onCopyCode}
                    />
                    
                    {/* Speaking animation cursor */}
                    {isSpeaking && (
                      <span className="inline-block ml-1 animate-pulse">▌</span>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {/* Auto-scroll reference element */}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      {/* Informational footer */}
      <div className="p-3 border-t border-white/10 bg-black/20">
        <div className="text-xs text-gray-400 flex items-center justify-between">
          <span>
            {dialogue.length > 0 ? 
              `${dialogue.length} messages (${dialogue.reduce((sum, msg) => sum + (msg.content?.length || 0), 0)} total chars)` : 
              "No messages yet"}
          </span>
          <span>
            {isListening ? "Live transcription active" : "Disconnected"}
          </span>
        </div>
      </div>
      
      {/* Script styling - scoped to this component */}
      <style jsx>{`
        .script-content :global(blockquote) {
          border-left: 3px solid #6366f1;
          padding-left: 10px;
          margin: 8px 0;
          font-style: italic;
          color: #a5b4fc;
        }
        
        .script-content :global(strong) {
          color: #f9a8d4;
          font-weight: bold;
        }
        
        .script-content :global(em) {
          color: #d1d5db;
          font-style: italic;
        }
      `}</style>
    </div>
  );
}