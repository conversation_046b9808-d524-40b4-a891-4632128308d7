// research/ResearchAgent.ts

import { <PERSON><PERSON><PERSON>ool, LlmProvider } from '../../tools/llm-tool';
import { WebContentExtractorTool, ExtractionOptions, ExtractionResult } from '../../tools/web-content-extractor'; // Assuming path
import { StorageTool } from '../../tools/storage-tool'; // If needed for saving reports/findings
import { PdfGeneratorTool, PdfContent, PdfGenerationOptions } from '../../tools/pdf-generator';
import { SavePdfToByteStoreResult } from '../../tools/storage-tool';
import { AgentMemoryManager } from '../AgentMemoryManager'; // Assuming shared manager or a research-specific one

// Re-define or import shared interfaces if they are generic enough
export interface AgentMemory {
  Agent_Response: Record<string, any>; // For agent responses and interactions
}

export interface AgentMessage {
  from: string;
  to: string;
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AgentTask {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  assignedTo: string;
  createdBy: string;
  createdAt: Date;
  dueDate?: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Base Research Agent class providing common functionality
 */
export class ResearchAgent {
  protected id: string;
  protected name: string;
  protected role: string;
  protected description: string;
  protected memory: AgentMemory;
  protected messageQueue: AgentMessage[];
  protected tasks: AgentTask[];
  protected llmTool: LlmTool;
  protected webContentExtractorTool: WebContentExtractorTool;
  protected pdfGeneratorTool: PdfGeneratorTool;
  protected storageTool: StorageTool; // Optional, depending on persistence needs
  protected defaultLlmProvider: LlmProvider;
  protected defaultLlmModel: string;
  protected memoryManager: AgentMemoryManager | null = null;
  protected userId: string;

  constructor(
    id: string,
    name: string,
    role: string,
    description: string,
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'openai',
    defaultLlmModel: string = 'gpt-4o'
  ) {
    this.id = id;
    this.name = name;
    this.role = role;
    this.description = description;
    this.userId = userId;
    this.defaultLlmProvider = defaultLlmProvider;
    this.defaultLlmModel = defaultLlmModel;

    this.memory = { Agent_Response: {} };
    this.messageQueue = [];
    this.tasks = [];

    this.llmTool = new LlmTool();
    this.webContentExtractorTool = new WebContentExtractorTool();
    this.pdfGeneratorTool = new PdfGeneratorTool();
    this.storageTool = new StorageTool(); // Initialize if used

    if (userId) {
      // Use the same manager or instantiate a research-specific one if needed
      this.memoryManager = new AgentMemoryManager(userId);
      this.loadMemoryFromStorage();
    }
  }

  /**
   * Process a request using the LLM
   */
  async processRequest(request: string, contextOverride?: string): Promise<string> {
    try {
      const prompt = this.createPrompt(request, contextOverride);
      const response = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions: { temperature: 0.5 } // Research might benefit from lower temp
      });
      this.storeInteraction(request, response);
      return response;
    } catch (error) {
      console.error(`Error processing request in ${this.name}:`, error);
      return `Error: Could not process request. ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Create a prompt for the LLM
   */
  protected createPrompt(request: string, contextOverride?: string): string {
    // Check if this is a PMO task by looking for PMO indicators in the request
    const isPMOTask = this.isPMOTask(request);

    if (isPMOTask) {
      return this.createPMOPrompt(request, contextOverride);
    }

    return `You are ${this.name}, a research agent with the role of ${this.role}.
${this.description}

${contextOverride ? `Specific Context:\n${contextOverride}` : `Recent Context:\n${this.getRecentContext()}`}

User request/task: ${request}

Please respond in a helpful, analytical, and objective manner consistent with your role.`;
  }

  /**
   * Create a PMO-specific prompt that emphasizes building upon existing PMO analysis
   */
  protected createPMOPrompt(request: string, contextOverride?: string): string {
    return `You are ${this.name}, a research agent with the role of ${this.role}.
${this.description}

IMPORTANT: This is a PMO-delegated research task. Your role is to ELABORATE and BUILD UPON the detailed PMO assessment provided, not to start research from scratch.

PMO RESEARCH INSTRUCTIONS:
- Treat the PMO assessment as your foundation and starting point
- Expand on every aspect identified in the PMO analysis with detailed research
- Enhance the PMO findings with additional data, insights, and strategic recommendations
- Maintain alignment with PMO requirements and objectives throughout your research
- Focus on adding value to the existing PMO analysis rather than duplicating it

${contextOverride ? `PMO Context and Assessment:\n${contextOverride}` : `Recent Context:\n${this.getRecentContext()}`}

PMO Research Task: ${request}

Please provide comprehensive research that builds upon and enhances the PMO assessment. Focus on elaborating the existing analysis with additional insights, data, and strategic recommendations.`;
  }

  /**
   * Check if this is a PMO task based on content indicators
   */
  protected isPMOTask(request: string): boolean {
    const pmoIndicators = [
      'pmo research task',
      'pmo assessment',
      'pmo analysis',
      'requirements specification',
      'strategic implementation',
      'cross-team coordination',
      'pmo findings',
      'pmo requirements',
      'build upon',
      'elaborate on'
    ];

    const requestLower = request.toLowerCase();
    return pmoIndicators.some(indicator => requestLower.includes(indicator));
  }

  /**
   * Get recent context from memory
   */
   protected getRecentContext(): string {
    const recentInteractions = this.memory.Agent_Response.recentInteractions || [];
    if (recentInteractions.length === 0) return "No recent interactions.";
    return recentInteractions
      .slice(-3)
      .map((interaction: any) => `User: ${interaction.request}\nAgent: ${interaction.response}`)
      .join('\n\n');
  }

  /**
   * Store an interaction in memory
   */
  protected storeInteraction(request: string, response: string): void {
    if (!this.memory.Agent_Response.recentInteractions) {
      this.memory.Agent_Response.recentInteractions = [];
    }
    this.memory.Agent_Response.recentInteractions.push({ request, response, timestamp: new Date() });
    if (this.memory.Agent_Response.recentInteractions.length > 10) {
      this.memory.Agent_Response.recentInteractions.shift();
    }
    this.saveMemoryToStorage();
  }

  /**
   * Send a message to another agent
   */
  async sendMessage(toAgentId: string, content: string, metadata?: Record<string, any>): Promise<void> {
    const message: AgentMessage = {
      from: this.id,
      to: toAgentId,
      content,
      timestamp: new Date(),
      metadata
    };
    this.messageQueue.push(message); // Local queue
    if (this.memoryManager) {
      await this.memoryManager.sendMessage(this.id, toAgentId, content, metadata);
    }
    console.log(`Message sent from ${this.name} (${this.id}) to agent ${toAgentId}: ${content.substring(0, 100)}...`);
  }

  /**
   * Receive messages for this agent
   */
  async receiveMessages(): Promise<AgentMessage[]> {
     // Basic implementation - real world might involve polling or subscriptions
    const localMessages = this.messageQueue.filter(msg => msg.to === this.id);
    this.messageQueue = this.messageQueue.filter(msg => msg.to !== this.id); // Consume local messages

    if (this.memoryManager) {
        const persistentMessages = await this.memoryManager.getUnreadMessages(this.id);
        return [...localMessages, ...persistentMessages];
    }
    return localMessages;
  }


  /**
   * Create a new task
   */
  async createTask(title: string, description: string, priority: 'low' | 'medium' | 'high', assignedTo: string, dueDate?: Date, metadata?: Record<string, any>): Promise<AgentTask> {
    const task: AgentTask = {
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title,
      description,
      status: 'pending',
      priority,
      assignedTo,
      createdBy: this.id,
      createdAt: new Date(),
      dueDate,
      metadata
    };
    this.tasks.push(task); // Store locally

    if (this.memoryManager) {
      try {
        const taskId = await this.memoryManager.createTask(
          title, description, priority, assignedTo, this.id, dueDate, metadata
        );
        task.id = taskId; // Update with persistent ID
        console.log(`Task created and saved with ID: ${taskId}`);
      } catch (error) {
        console.error(`Failed to save task persistently: ${error}`);
        // Keep local task anyway
      }
    }
    return task;
  }

   /**
    * Update task status
    */
   async updateTaskStatus(taskId: string, status: 'pending' | 'in-progress' | 'completed' | 'failed'): Promise<boolean> {
    const task = this.tasks.find(t => t.id === taskId);
    let taskUpdated = false;

    if (task) {
        task.status = status;
        if (status === 'completed') task.completedAt = new Date();
        taskUpdated = true;
    }

    if (this.memoryManager) {
        try {
            await this.memoryManager.updateTaskStatus(taskId, status);
            console.log(`Task ${taskId} status updated to ${status} persistently.`);
            taskUpdated = true; // Even if not found locally, it might exist persistently
        } catch (error) {
            console.error(`Failed to update task ${taskId} status persistently: ${error}`);
            // If local update succeeded, it's still true locally
        }
    } else if (!task) {
      console.warn(`Task ${taskId} not found locally to update status.`);
      return false; // Cannot update if not found and no persistence manager
    }
    return taskUpdated;
   }

    /**
     * Get tasks assigned to this agent
     */
    async getAssignedTasks(): Promise<AgentTask[]> {
        let allTasks = [...this.tasks.filter(task => task.assignedTo === this.id)];
        if (this.memoryManager) {
            try {
                const persistentTasks = await this.memoryManager.getAssignedTasks(this.id);
                // Simple merge: Add persistent tasks not already present locally
                const localTaskIds = new Set(allTasks.map(t => t.id));
                persistentTasks.forEach(pt => {
                    if (!localTaskIds.has(pt.id)) {
                        allTasks.push(pt);
                    }
                });
            } catch (error) {
                console.error(`Failed to get assigned tasks from persistence: ${error}`);
            }
        }
        return allTasks;
    }

    /**
     * Get tasks created by this agent
     */
    async getCreatedTasks(): Promise<AgentTask[]> {
        let allTasks = [...this.tasks.filter(task => task.createdBy === this.id)];
        if (this.memoryManager) {
             try {
                const persistentTasks = await this.memoryManager.getCreatedTasks(this.id);
                const localTaskIds = new Set(allTasks.map(t => t.id));
                persistentTasks.forEach(pt => {
                    if (!localTaskIds.has(pt.id)) {
                        allTasks.push(pt);
                    }
                });
             } catch (error) {
                 console.error(`Failed to get created tasks from persistence: ${error}`);
             }
        }
        return allTasks;
    }

// research/ResearchAgent.ts (modification within the class)

  /**
   * Extract content from a specific URL using the WebContentExtractorTool.
   * For general web searches, use the InformationRetrievalAgent with sourceType 'web'.
   * @param url - The specific URL to extract content from.
   * @param options - Extraction options.
   * @returns ExtractionResult object.
   */
  async extractFromUrl(url: string, options: ExtractionOptions = {}): Promise<ExtractionResult> {
    if (!url || (!url.startsWith('http://') && !url.startsWith('https://'))) {
        throw new Error("A valid URL starting with http:// or https:// is required for extractFromUrl.");
    }
    try {
        console.log(`[${this.name}] Extracting content directly from URL: ${url}`);
        // Use the instance member
        return await this.webContentExtractorTool.extractContent(url, options);
    } catch (error) {
        console.error(`Error during URL extraction for "${url}" in agent ${this.name}:`, error);
        throw error; // Re-throw to be handled by the calling method
    }
  }

  // Keep the old researchWeb for potential backward compatibility or rename it,
  // but emphasize its URL-specific nature in comments.
  /**
   * @deprecated Prefer extractFromUrl for specific URLs or InformationRetrievalAgent.executeSearch for queries.
   * Perform web research using the WebContentExtractorTool (primarily for URL extraction).
   */
   async researchWeb(queryOrUrl: string, options: ExtractionOptions = {}): Promise<ExtractionResult | ExtractionResult[]> {
       if (queryOrUrl.startsWith('http://') || queryOrUrl.startsWith('https://')) {
            return this.extractFromUrl(queryOrUrl, options);
       } else {
            console.warn("researchWeb called with a query, not a URL. This method is intended for direct URL extraction. Use InformationRetrievalAgent for searches.");
            // Returning an error or empty result might be better here.
            throw new Error("researchWeb should only be called with a valid URL. Use InformationRetrievalAgent for search queries.");
       }
   }

  /**
   * Generate a PDF document
   * @param title - The title of the PDF
   * @param contents - The content sections of the PDF
   * @param options - PDF generation options
   * @param saveToByteStore - Whether to save the PDF to byteStore (default: false)
   * @returns Promise with the PDF buffer or byteStore result
   */
  async generatePdf(title: string, contents: PdfContent[], options: PdfGenerationOptions = {}, saveToByteStore: boolean = false): Promise<Buffer | SavePdfToByteStoreResult> {
    try {
      // Add additional options
      const pdfOptions: PdfGenerationOptions = {
        ...options,
        title: title,
        author: this.name,
        saveToByteStore,
        agentId: this.id,
        agentName: this.name,
        category: options.category || 'Research Agent'
      };

      return await this.pdfGeneratorTool.generatePdf(contents, pdfOptions);
    } catch (error) {
      console.error(`Error generating PDF "${title}" in ${this.name}:`, error);
      throw error;
    }
  }

  /**
   * Save content to storage (example using StorageTool)
   */
  async saveContent(content: Record<string, any>, collectionName = "research_findings"): Promise<string> {
    if (!this.userId) throw new Error("User ID is required to save content.");
    try {
      // Add metadata
      const contentToSave = {
        ...content,
        agentId: this.id,
        agentName: this.name,
        userId: this.userId,
        savedAt: new Date(),
      };
      // Use user-specific path if StorageTool supports it, otherwise adjust collectionName
      const finalCollection = `users/${this.userId}/${collectionName}`;
      return await this.storageTool.saveToFirestore(contentToSave, finalCollection);
    } catch (error) {
      console.error(`Error saving content in ${this.name}:`, error);
      throw error;
    }
  }


  /**
   * Load memory from persistent storage
   */
  protected async loadMemoryFromStorage(): Promise<void> {
    if (!this.memoryManager) return;
    try {
      this.memory = await this.memoryManager.loadMemory(this.id);
      console.log(`Memory loaded for agent ${this.name} (${this.id})`);
    } catch (error) {
      console.error(`Error loading memory for agent ${this.name} (${this.id}):`, error);
       this.memory = { Agent_Response: {} }; // Reset to default if load fails
    }
  }

  /**
   * Save memory to persistent storage
   */
  protected async saveMemoryToStorage(): Promise<void> {
    if (!this.memoryManager) return;
    try {
      await this.memoryManager.saveMemory(this.id, this.memory);
    } catch (error) {
      console.error(`Error saving memory for agent ${this.name} (${this.id}):`, error);
    }
  }

  /**
   * Get agent information
   */
  getInfo(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      role: this.role,
      description: this.description,
      userId: this.userId
    };
  }
}