// types/shared.ts
import { BasePromptValueInterface } from "@langchain/core/prompt_values";

export interface Source {
  title: string;
  page: number;
  doc_id: string;
  relevance: number;
  // Added properties for vector similarity
  text_content?: string;  // The actual text content from the chunk
  chunkId?: string;       // The ID of the chunk for reference
}

export interface StreamOptions {
  streamId: string;
  metadata?: Record<string, any>;
  controller: ReadableStreamDefaultController;
  signal?: AbortSignal;
}



export interface VisualizationRequestOptions {
  type: string;
  features: any[]; // Adjust the type as needed
  formatting: any; // Adjust the type as needed
  metadata?: {
    type: "time-series" | "categorical" | "distribution";
    metrics: string[];
    timeUnit?: "day" | "month" | "quarter" | "year";
  };
}

export interface ProcessMessageInput {
  userEmail: string;
  streamOptions: StreamOptions;
  prompt: BasePromptValueInterface;
  data?: any[];
  config?: {
    temperature?: number;
    maxTokens?: number;
    visualizationOptions?: {
      preferredType?: string;
      maxCategories?: number;
      minDataPoints?: number;
      features?: string[];
      formatting?: Record<string, any>;
      type?: string;
    };
  };
}

export interface VisualizationResult {
  success: boolean;
  error?: string;
  result?: {
    type: 'chart' | 'table';
    subtype?: string;
    config: Record<string, any>;
    data: any[];
    reasoning?: string;
  };
}

export interface ProcessMessageOutput {
  success: boolean;
  error?: string;
  response?: any;
  visualization?: {
    charttype: string; // Add this line to include the 'charttype' property
    content: {
      metadata: any;
      confidence: any;
      type: any;
      config: any;
      data: any;
      reasoning?: "" | undefined;
      url?: string; // Ensure the 'url' property is included
    };
    type: 'chart' | 'table';
    subtype?: string;
    config: Record<string, any>;
    data: any[];
    reasoning?: string;
  };
}

export interface TokenUsage {
  contextTokens: number;
  systemPromptTokens: number;
  chatHistoryTokens: number;
  totalTokens: number;
}

export interface QueryMatch {
  metadata?: {
    chunkId: string;
    score?: number;
    namespace?: string;
    embedding?: number[];
  };
  score?: number;
  values?: number[];
  id?: string;
}

export interface ContentSelectionResult {
  content: string;
  metadata: {
    sources: Source[];
    totalTokens: number;
    chunkCount: number;
    averageRelevance: number;
    namespaceDistribution: Record<string, number>;
  };
}