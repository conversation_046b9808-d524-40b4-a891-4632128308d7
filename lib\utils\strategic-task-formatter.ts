/**
 * Strategic Task Formatter Utility
 *
 * Centralized formatting logic for Strategic Director Agent task management.
 * This module contains all formatting functions for task descriptions, notes, and content.
 */

// Type definitions for StrategicTask and StrategicTaskCollection (extracted from StrategicDirectorAgent)
export interface StrategicTask {
  id: string;
  title: string;
  description: string;
  category: 'Market Intelligence' | 'Product Analysis' | 'Customer Intelligence' | 'Marketing Infrastructure' | 'Performance Metrics' | 'Customer Validation' | 'Strategic Planning' | 'Implementation' | 'Research' | 'Content Creation';
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  assignedTeam: 'Research Team' | 'Business Analysis Team' | 'Marketing Team' | 'Sales Team' | 'Software Design Team' | 'Content Team' | 'Strategic Director';
  status: 'IDENTIFIED' | 'ASSIGNED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'BLOCKED' | 'CANCELLED';
  specificRequirements: string[];
  deliverable: string;
  timeline: {
    estimatedDuration: string;
    startDate?: Date;
    dueDate?: Date;
  };
  dependencies?: string[];
  blockers?: string[];
  successCriteria: string[];
  resources?: {
    budget?: number;
    tools?: string[];
    personnel?: string[];
    documents?: string[];
  };
  metadata: {
    createdAt: Date;
    createdBy: string;
    updatedAt: Date;
    source: 'Information Gap Analysis' | 'Strategic Planning' | 'PMO Requirements' | 'User Request' | 'System Generated';
    pmoId?: string;
    projectId?: string;
    requestId?: string;
  };
}

export interface StrategicTaskCollection {
  collectionId: string;
  name: string;
  description: string;
  source: string;
  totalTasks: number;
  tasks: StrategicTask[];
  overallTimeline: {
    startDate: Date;
    estimatedEndDate: Date;
    criticalPath: string[];
  };
  teamAssignments: {
    [teamName: string]: {
      taskCount: number;
      taskIds: string[];
      estimatedWorkload: string;
    };
  };
  successMetrics: string[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Strategic Task Formatter Class
 * Contains all formatting logic for strategic task management
 */
export class StrategicTaskFormatter {

  /**
   * Format comprehensive task description from StrategicTask
   */
  static formatTaskDescription(strategicTask: StrategicTask): string {
    let description = strategicTask.description;

    if (strategicTask.specificRequirements.length > 0) {
      description += '\n\n**Specific Requirements:**\n';
      description += strategicTask.specificRequirements.map(req => `- ${req}`).join('\n');
    }

    if (strategicTask.deliverable) {
      description += `\n\n**Deliverable:** ${strategicTask.deliverable}`;
    }

    if (strategicTask.successCriteria.length > 0) {
      description += '\n\n**Success Criteria:**\n';
      description += strategicTask.successCriteria.map(criteria => `- ${criteria}`).join('\n');
    }

    description += '\n\n*Generated from Strategic Director comprehensive task analysis*';

    return description;
  }

  /**
   * Format comprehensive task notes from StrategicTask and collection context
   */
  static formatTaskNotes(strategicTask: StrategicTask, collection: StrategicTaskCollection): string {
    const notes = [
      `Strategic Task Collection: ${collection.name}`,
      `Source: ${strategicTask.metadata.source}`,
      `Created by: ${strategicTask.metadata.createdBy}`,
      `Assigned Team: ${strategicTask.assignedTeam}`,
      `Priority Level: ${strategicTask.priority}`,
      `Estimated Duration: ${strategicTask.timeline.estimatedDuration}`
    ];

    if (strategicTask.dependencies && strategicTask.dependencies.length > 0) {
      notes.push(`Dependencies: ${strategicTask.dependencies.join(', ')}`);
    }

    if (strategicTask.blockers && strategicTask.blockers.length > 0) {
      notes.push(`Blockers: ${strategicTask.blockers.join(', ')}`);
    }

    if (strategicTask.metadata.pmoId) {
      notes.push(`PMO ID: ${strategicTask.metadata.pmoId}`);
    }

    if (strategicTask.resources) {
      if (strategicTask.resources.budget) {
        notes.push(`Budget: $${strategicTask.resources.budget}`);
      }
      if (strategicTask.resources.tools && strategicTask.resources.tools.length > 0) {
        notes.push(`Required Tools: ${strategicTask.resources.tools.join(', ')}`);
      }
    }

    return notes.join('\n');
  }

  /**
   * Generate success metrics based on output content and tasks
   */
  static generateSuccessMetricsFromOutput(outputText: string, tasks: StrategicTask[]): string[] {
    const metrics = [
      'All extracted tasks completed within estimated timelines',
      'All team deliverables meet quality standards',
      'Strategic objectives alignment achieved',
      'Team coordination and communication effectiveness'
    ];

    // Add metrics based on output content
    if (outputText.toLowerCase().includes('market')) {
      metrics.push('Market research objectives achieved');
    }
    if (outputText.toLowerCase().includes('customer')) {
      metrics.push('Customer intelligence goals met');
    }
    if (outputText.toLowerCase().includes('product')) {
      metrics.push('Product analysis requirements fulfilled');
    }

    // Add team-specific metrics
    const teams = [...new Set(tasks.map(task => task.assignedTeam))];
    teams.forEach(team => {
      metrics.push(`${team} deliverables completed successfully`);
    });

    return metrics;
  }

  /**
   * Format information request response from gap analysis
   */
  static formatInformationRequestResponse(gapAnalysis: any, originalMissingInfo: string, getTeamEmoji: (teamName: string) => string): string {
    const gaps = gapAnalysis.identifiedGaps || [];
    const status = gapAnalysis.status || 'Information Gaps Identified';
    const summary = gapAnalysis.summary || originalMissingInfo;
    const urgencyLevel = gapAnalysis.urgencyLevel || 'HIGH';
    const nextSteps = gapAnalysis.nextSteps || [];

    let response = `## Strategic Director Assessment: ${status}

**Status:** Analysis cannot proceed due to insufficient information.

**Situation Analysis:**
${summary}

**Urgency Level:** ${urgencyLevel}

## Specific Information Gaps and Team Delegation

Based on intelligent analysis of the information gaps, I am delegating the following specific tasks to specialized teams:

`;

    // Add each gap as a specific team assignment
    gaps.forEach((gap: any, index: number) => {
      const priorityEmoji = gap.priority === 'HIGH' ? '🔴' : gap.priority === 'MEDIUM' ? '🟡' : '🟢';
      const teamEmoji = getTeamEmoji(gap.assignedTeam);

      response += `### ${priorityEmoji} **${teamEmoji} ${gap.assignedTeam} - ${gap.gapTitle} (Priority: ${gap.priority})**

**Information Gap:** ${gap.gapDescription}

**Specific Requirements:**
${gap.specificRequirements.map((req: string) => `- ${req}`).join('\n')}

**Expected Deliverable:** ${gap.deliverable}
**Timeline:** ${gap.timeline}
**Team Assignment Rationale:** ${gap.rationale}

`;
    });

    // Add structured JSON for reference
    response += `## 📋 **Delegation Structure (JSON)**

\`\`\`json
${JSON.stringify({
      status,
      summary,
      totalGaps: gaps.length,
      urgencyLevel,
      identifiedGaps: gaps
    }, null, 2)}
\`\`\`

`;

    // Add next steps
    if (nextSteps.length > 0) {
      response += `## Next Steps

${nextSteps.map((step: string, index: number) => `${index + 1}. ${step}`).join('\n')}

`;
    } else {
      response += `## Next Steps

1. **Immediate:** Teams should begin information gathering within 24 hours
2. **Coordination:** Each team should provide findings within their specified timeline
3. **Reconvene:** Strategic analysis will resume once sufficient information is available
4. **Documentation:** All findings should be documented and shared with the Strategic Director

`;
    }

    response += `**Note:** Strategic analysis cannot proceed without this foundational information. Attempting to create strategy without proper context would result in ineffective recommendations that could harm business objectives.

---
*Strategic Director will not proceed with analysis until teams provide the required information above.*`;

    return response;
  }

  /**
   * Generate static fallback response when LLM processing fails
   */
  static generateStaticInformationRequestResponse(missingInfo: string): string {
    return `## Strategic Director Assessment: Insufficient Information

**Status:** Analysis cannot proceed due to insufficient information.

**Issue Identified:**
${missingInfo}

## Required Actions - Team Delegation

To proceed with strategic analysis, I am delegating the following information gathering tasks to specialized teams:

### 🔍 **Research Team - Immediate Actions Required**
**Task:** Conduct comprehensive information gathering
**Specific Requirements:**
- Market research and competitive analysis
- Target audience demographics and psychographics
- Industry trends and market size data
- Customer behavior patterns and preferences

### 📊 **Business Analysis Team - Data Collection**
**Task:** Gather business context and requirements
**Specific Requirements:**
- Business objectives and success metrics
- Budget constraints and resource availability
- Timeline requirements and key milestones
- Stakeholder requirements and constraints

### 💼 **Sales Team - Customer Insights**
**Task:** Provide customer and market intelligence
**Specific Requirements:**
- Customer pain points and needs assessment
- Pricing sensitivity and competitive positioning
- Sales channel effectiveness and preferences
- Customer feedback and market validation data

## Next Steps

1. **Immediate:** Teams should begin information gathering within 24 hours
2. **Coordination:** Each team should provide findings within 1 week
3. **Reconvene:** Strategic analysis will resume once sufficient information is available
4. **Documentation:** All findings should be documented and shared with the Strategic Director

**Note:** Strategic analysis cannot proceed without this foundational information. Attempting to create strategy without proper context would result in ineffective recommendations that could harm business objectives.

---
*Strategic Director will not proceed with analysis until teams provide the required information above.*`;
  }

  /**
   * Format delegation structure data into standardized markdown
   * Converts JSON delegation structure into clean, consistent markdown format
   */
  static formatDelegationStructureMarkdown(delegationData: any): string {
    const {
      status = 'Information Gaps Identified',
      summary = '',
      urgencyLevel = 'HIGH',
      totalGaps = 0,
      identifiedGaps = []
    } = delegationData;

    // Helper function to get priority emoji
    const getPriorityEmoji = (priority: string): string => {
      switch (priority?.toUpperCase()) {
        case 'HIGH': return '🔴';
        case 'MEDIUM': return '🟡';
        case 'LOW': return '🟢';
        default: return '🟡';
      }
    };

    // Helper function to get team emoji (using StrategicTaskMapper logic)
    const getTeamEmoji = (teamName: string): string => {
      const lowerTeam = teamName.toLowerCase();
      if (lowerTeam.includes('research')) return '🔍';
      if (lowerTeam.includes('business') || lowerTeam.includes('analysis')) return '📊';
      if (lowerTeam.includes('sales')) return '💼';
      if (lowerTeam.includes('marketing')) return '📈';
      if (lowerTeam.includes('software') || lowerTeam.includes('design')) return '💻';
      if (lowerTeam.includes('content')) return '📝';
      if (lowerTeam.includes('strategic')) return '🎯';
      return '👥'; // Default team emoji
    };

    let markdown = `## Strategic Director Assessment: ${status}

**Urgency Level:** ${urgencyLevel}

**Situation Summary:**
${summary || 'Strategic analysis requires additional information to proceed effectively.'}

## Team Assignments

`;

    // Add each information gap as a team assignment
    identifiedGaps.forEach((gap: any) => {
      const priorityEmoji = getPriorityEmoji(gap.priority);
      const teamEmoji = getTeamEmoji(gap.assignedTeam);
      const gapTitle = gap.gapTitle || gap.category || gap.description || 'Information Gap';

      markdown += `### ${priorityEmoji} **${teamEmoji} ${gap.assignedTeam} - ${gapTitle} (Priority: ${gap.priority})**

**Information Gap:** ${gap.gapDescription || gap.description || 'Missing critical information for strategic analysis'}

**Specific Requirements:**
${(gap.specificRequirements || []).map((req: string) => `- ${req}`).join('\n')}

**Expected Deliverable:** ${gap.deliverable || 'Information gathering report'}
**Timeline:** ${gap.timeline || '1 week'}
**Team Assignment Rationale:** ${gap.rationale || `${gap.assignedTeam} has the expertise and resources to gather this information effectively.`}

`;
    });

    // Add summary section
    const teamsInvolved = [...new Set(identifiedGaps.map((gap: any) => gap.assignedTeam))];
    const priorityCounts = identifiedGaps.reduce((acc: any, gap: any) => {
      acc[gap.priority] = (acc[gap.priority] || 0) + 1;
      return acc;
    }, {});

    markdown += `## Summary

**Total Information Gaps:** ${totalGaps}
**Teams Involved:** ${teamsInvolved.join(', ')}
**Priority Breakdown:** ${Object.entries(priorityCounts).map(([priority, count]) => `${priority}: ${count}`).join(', ')}
**Overall Timeline:** ${this.calculateOverallTimeline(identifiedGaps)}

---
*Strategic analysis will resume once teams provide the required information above.*`;

    return markdown;
  }

  /**
   * Format structured information gap analysis into markdown
   * Handles the specific case of structured gap analysis with JSON data
   */
  static formatStructuredGapAnalysisMarkdown(gapAnalysisData: any): string {
    const {
      status = 'Information Gaps Identified',
      summary = '',
      totalGaps = 0,
      identifiedGaps = []
    } = gapAnalysisData;

    let markdown = `# Structured Information Gap Analysis

**Total Gaps Identified:** ${totalGaps}

## Executive Summary

${summary || 'Strategic analysis has identified critical information gaps that must be addressed before proceeding.'}

## Information Gap Details

`;

    // Process each identified gap
    identifiedGaps.forEach((gap: any, index: number) => {
      const priorityEmoji = gap.priority === 'HIGH' ? '🔴' : gap.priority === 'MEDIUM' ? '🟡' : '🟢';
      const teamEmoji = this.getTeamEmojiForGap(gap.assignedTeam);

      markdown += `### ${index + 1}. ${priorityEmoji} **${gap.category || 'Information Gap'}** (Priority: ${gap.priority})

**Assigned Team:** ${teamEmoji} ${gap.assignedTeam}

**Description:** ${gap.description || gap.gapDescription || 'Critical information missing for strategic analysis'}

**Specific Requirements:**
${(gap.specificRequirements || []).map((req: string) => `- ${req}`).join('\n')}

**Expected Deliverable:** ${gap.deliverable || 'Information gathering report'}
**Timeline:** ${gap.timeline || '1 week'}

`;
    });

    // Add action items section
    markdown += `## Next Steps

1. **Immediate Action Required:** Teams should begin information gathering within 24 hours
2. **Coordination:** Regular check-ins to monitor progress and address blockers
3. **Documentation:** All findings must be properly documented and shared
4. **Review:** Strategic analysis will resume once sufficient information is available

## Team Coordination

`;

    // Group gaps by team for coordination
    const gapsByTeam = identifiedGaps.reduce((acc: any, gap: any) => {
      if (!acc[gap.assignedTeam]) {
        acc[gap.assignedTeam] = [];
      }
      acc[gap.assignedTeam].push(gap);
      return acc;
    }, {});

    Object.entries(gapsByTeam).forEach(([team, gaps]: [string, any]) => {
      const teamEmoji = this.getTeamEmojiForGap(team);
      markdown += `### ${teamEmoji} **${team}**
- **Total Assignments:** ${gaps.length}
- **Priority Levels:** ${gaps.map((g: any) => g.priority).join(', ')}
- **Combined Timeline:** ${this.calculateTeamTimeline(gaps)}

`;
    });

    markdown += `---
*This analysis was generated by the Strategic Director Agent. Strategic planning cannot proceed without the information identified above.*`;

    return markdown;
  }

  /**
   * Helper method to get team emoji for gap analysis
   */
  private static getTeamEmojiForGap(teamName: string): string {
    const lowerTeam = teamName.toLowerCase();
    if (lowerTeam.includes('research')) return '🔍';
    if (lowerTeam.includes('business') || lowerTeam.includes('analysis')) return '📊';
    if (lowerTeam.includes('sales')) return '💼';
    if (lowerTeam.includes('marketing')) return '📈';
    if (lowerTeam.includes('software') || lowerTeam.includes('design')) return '💻';
    if (lowerTeam.includes('content')) return '📝';
    if (lowerTeam.includes('strategic')) return '🎯';
    return '👥';
  }

  /**
   * Calculate overall timeline from multiple gaps
   */
  private static calculateOverallTimeline(gaps: any[]): string {
    if (!gaps || gaps.length === 0) return 'To be determined';

    const timelines = gaps.map(gap => gap.timeline || '1 week');
    const hasWeeks = timelines.some(t => t.includes('week'));
    const hasDays = timelines.some(t => t.includes('day'));

    if (hasWeeks && hasDays) {
      return '3 days - 3 weeks depending on priority levels';
    } else if (hasWeeks) {
      return '1-3 weeks depending on complexity';
    } else if (hasDays) {
      return '3-7 days depending on priority levels';
    }

    return '1-2 weeks estimated';
  }

  /**
   * Calculate timeline for a specific team's assignments
   */
  private static calculateTeamTimeline(teamGaps: any[]): string {
    if (!teamGaps || teamGaps.length === 0) return 'To be determined';

    const timelines = teamGaps.map(gap => gap.timeline || '1 week');
    const uniqueTimelines = [...new Set(timelines)];

    if (uniqueTimelines.length === 1) {
      return uniqueTimelines[0];
    }

    return `${Math.min(...timelines.map(t => parseInt(t) || 1))} - ${Math.max(...timelines.map(t => parseInt(t) || 1))} ${timelines[0].includes('week') ? 'weeks' : 'days'}`;
  }
}
