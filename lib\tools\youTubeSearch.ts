// components/tools/YouTubeSearchTool.ts
import { DynamicTool } from 'langchain/tools';

// Define interfaces for the YouTube API response
interface YouTubeApiResponseItem {
  id: {
    videoId: string;
  };
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      high?: { url: string };
      default?: { url: string };
    };
    channelTitle: string;
    publishedAt: string;
  };
}

interface YouTubeApiResponse {
  items: YouTubeApiResponseItem[];
  aborted?: boolean;  // Indicates if the search was aborted by the user
  error?: string;    // Error message if the search failed
}

// Define interface for search results
interface YouTubeSearchResult {
  title: string;
  description: string;
  videoId: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
}

interface SearchResponse {
  success: boolean;
  results: YouTubeSearchResult[];
  metadata: {
    source: string;
    searchTime?: number;
    resultCount?: number;
    enhancedQuery?: string;
    error?: string;
    aborted?: boolean;  // Indicates if the search was aborted by the user
  };
}

interface YouTubeSearchToolConfig {
  maxResults?: number;
  preferOfficial?: boolean;
  useMock?: boolean;
}

/**
 * YouTubeSearchTool
 *
 * A specialized tool for searching YouTube videos related to exercises.
 * Uses the YouTube Data API to find relevant videos based on exercise names and descriptions.
 */
export class YouTubeSearchTool extends DynamicTool {
  private maxResults: number;
  private preferOfficial: boolean;
  private useMock: boolean;
  private abortController: AbortController | null = null;

  static description = `Search YouTube for exercise demonstration videos.
  This tool is optimized for finding high-quality fitness content related to specific exercises.

  Input should be a specific exercise name and any relevant details (equipment, focus area, etc.)
  Output will contain relevant video results including title, description, and video ID.`;

  constructor(config: YouTubeSearchToolConfig) {
    super({
      name: 'youtube_search',
      description: YouTubeSearchTool.description,
      func: async (query: string) => await this.searchYouTube(query)
    });

    // We no longer need to store the API key in this class
    // as we're using the API route which handles the API key
    this.maxResults = config.maxResults || 3;
    this.preferOfficial = config.preferOfficial || true;
    this.useMock = config.useMock || false; // Default to using real API
  }

  /**
   * Public search method that can be called directly by external code
   * @param query Exercise name and details to search for
   * @returns Search results with video information
   */
  public async search(query: string): Promise<SearchResponse> {
    return await this.searchYouTube(query);
  }

  /**
   * Abort any ongoing search request
   * @returns True if a request was aborted, false if no request was in progress
   */
  public abortSearch(): boolean {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
      return true;
    }
    return false;
  }

  /**
   * Search YouTube for videos related to the given exercise
   * @param query Exercise name and details to search for
   * @returns Search results with video information
   */
  private async searchYouTube(query: string): Promise<SearchResponse> {
    try {
      // Create a new AbortController for this request
      // This will abort any previous request that might still be in progress
      if (this.abortController) {
        this.abortController.abort();
      }
      this.abortController = new AbortController();

      const startTime = Date.now();

      // Enhance the query for better fitness-related results
      const enhancedQuery = this.enhanceSearchQuery(query);

      // Use server-side API call to avoid referrer restrictions
      let apiUrl: string;

      // Always use the API route to handle YouTube API requests
      // The API route will use mock data in development mode or when the API fails
      apiUrl = `/api/youtube-search?q=${encodeURIComponent(enhancedQuery)}&maxResults=${this.maxResults}`;

      // Pass the mock parameter based on the configuration
      apiUrl += `&mock=${this.useMock}`;

      const response = await fetch(
        apiUrl,
        {
          headers: {
            'Accept': 'application/json'
          },
          signal: this.abortController.signal
        }
      );

      if (!response.ok) {
        const errorBody = await response.text().catch(() => 'Could not read error response body');
        throw new Error(`YouTube API error: Status ${response.status} - ${response.statusText}. Details: ${errorBody}`);
      }

      // Parse the response
      let data: YouTubeApiResponse;

      try {
        const responseData = await response.json();

        // Check if we're handling a response from our API route or direct YouTube API
        if (responseData.items) {
          // Direct YouTube API response format
          data = responseData as YouTubeApiResponse;
        } else if (responseData.error) {
          // Our API route returned an error
          throw new Error(`API route error: ${responseData.error}. Details: ${responseData.details || 'No details provided'}`);
        } else {
          // Unexpected response format
          throw new Error(`Unexpected response format: ${JSON.stringify(responseData).substring(0, 100)}...`);
        }
      } catch (parseError) {
        throw new Error(`Failed to parse API response: ${parseError instanceof Error ? parseError.message : 'Unknown error'}`);
      }
      // Extract and format the results with explicit type annotation
      const videoResults: YouTubeSearchResult[] = data.items.map((item: YouTubeApiResponseItem) => ({
        title: item.snippet.title,
        description: item.snippet.description,
        videoId: item.id.videoId,
        thumbnailUrl: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url || '',
        channelTitle: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt
      }));

      // If preferring official content, sort results to prioritize content from reputable fitness channels
      if (this.preferOfficial) {
        this.prioritizeOfficialContent(videoResults);
      }

      // Clear the abort controller since the request completed successfully
      this.abortController = null;

      return {
        success: true,
        results: videoResults,
        metadata: {
          source: 'youtube_api',
          searchTime: Date.now() - startTime,
          resultCount: videoResults.length,
          enhancedQuery
        }
      };

    } catch (error) {
      // Clear the abort controller since the request is complete (either successfully or with error)
      this.abortController = null;

      // Check if this was an abort error (user cancelled the request)
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log('YouTube search request was aborted by user');
        return {
          success: false,
          results: [],
          metadata: {
            source: 'youtube_api',
            error: 'Search request was cancelled',
            aborted: true
          }
        };
      }

      // Handle other errors
      console.error('YouTube Search API error:', error);
      return {
        success: false,
        results: [],
        metadata: {
          source: 'youtube_api',
          error: error instanceof Error ? error.message : 'Unknown error occurred'
        }
      };
    }
  }

  /**
   * Enhance the search query to improve fitness-related video results
   */
  private enhanceSearchQuery(query: string): string {
    // Add fitness-specific terms to the query to find better demonstration videos
    return `${query} exercise demonstration proper form technique`;
  }

  /**
   * Prioritize videos from official or reputable fitness channels
   * @param results The array of search results to prioritize
   */
  private prioritizeOfficialContent(results: YouTubeSearchResult[]): void {
    // List of reputable fitness channels and terms to prioritize
    const reputableChannels = [
      'V Shred','athlean-x', 'Renaissance Periodization', 'Breon Ansley', 'jeff nippard', 
      'Greg Doucette', 'jeremy ethier', 'fitness blender',
      'nasm', 'ace fitness', 'bodybuilding.com', 'strength camp',
      'official', 'certified', 'trainer', 'Larry Wheels'
    ];

    // Sort results to prioritize videos from reputable sources
    results.sort((a, b) => {
      const aIsOfficial = reputableChannels.some(
        channel => a.channelTitle.toLowerCase().includes(channel.toLowerCase()) ||  
                  a.title.toLowerCase().includes(channel.toLowerCase())
      );

      const bIsOfficial = reputableChannels.some(
        channel => b.channelTitle.toLowerCase().includes(channel.toLowerCase()) ||
                  b.title.toLowerCase().includes(channel.toLowerCase())
      );

      if (aIsOfficial && !bIsOfficial) return -1;
      if (!aIsOfficial && bIsOfficial) return 1;
      return 0;
    });
  }

  /**
   * Get a complete YouTube video URL from a video ID
   */
  public static getVideoUrl(videoId: string): string {
    return `https://www.youtube.com/watch?v=${videoId}`;
  }

  /**
   * Get an embeddable YouTube video URL from a video ID
   */
  public static getEmbedUrl(videoId: string): string {
    return `https://www.youtube.com/embed/${videoId}`;
  }
}