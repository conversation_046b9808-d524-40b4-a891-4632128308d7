
You are a prompt engineering expert. Your task is to optimize the following prompt by applying the provided criteria.

ORIGINAL PROMPT:
"""

Project Name: Agent iKe
Description: Agent <PERSON><PERSON><PERSON> is the ultimate Agent force designed to process requests for tasks and carry out actions:
producing documentation , 
completing social media tasks
conducting research
performing marketing tasks
creating social media posts
Timeline: 14/04/2025 to 14/06/2025
Categories: Design, Bugs, Testing

Additional Instructions: Agentic AI – Canonical Task List

The rough notes have been synthesised into a concrete, implementation-ready task hierarchy. Each top-level task can be owned by an individual agent or micro-service; subtasks describe the atomic steps that must be executed or delegated.

──────────────────────────────────────── T0 SYSTEM INITIALISATION (one-off or scheduled) ──────────────────────────────────────── T0.1 Build / refresh vector index of all “internal documents”.
T0.2 Register the 3 canonical iKe retrieval queries that will be reused.
T0.3 Configure OCR pipeline and image classifier.
T0.4 Expose API endpoints for:
• Free-text request (input box)
• Document upload
• Image upload

──────────────────────────────────────── T1 REQUEST INTAKE (triggered per user request) ──────────────────────────────────────── T1.1 Capture incoming payload (text, docs, images).
T1.2 Instantiate a Job object; generate globally unique JobID.
T1.3 Persist raw assets and Job metadata to storage.
T1.4 Emit JobCreated event → Project-Management Agent.

──────────────────────────────────────── T2 PROJECT-MANAGEMENT AGENT ──────────────────────────────────────── T2.1 Parse Job details; auto-classify request type / domain.
T2.2 Decide which specialist agents are required (e.g., Doc-RAG, Image-OCR, Domain-Expert, Writer).
T2.3 Attach any user-supplied reference material to the Job.
T2.4 Post delegation orders to the appropriate agent queues.
T2.5 Maintain Job status state-machine (Queued → In-Progress → Review → Complete).

──────────────────────────────────────── T3 DOCUMENT QUERY & DATA-GATHERING AGENT (if relevant) ──────────────────────────────────────── T3.1 Receive JobID and request context.
T3.2 Compose the 3 iKe queries (Query-A, Query-B, Query-C).
T3.3 Execute queries against the internal document index.
T3.4 Rank and deduplicate hits.
T3.5 Extract salient passages (chunking + relevance threshold).
T3.6 Package extracted data as “ContextBundle” and attach to Job.
T3.7 Signal completion → Project-Management Agent.

──────────────────────────────────────── T4 IMAGE PROCESSING AGENT (on-demand) ──────────────────────────────────────── T4.1 Detect images attached to Job; route each to OCR pipeline.
T4.2 Run OCR + layout analysis → produce text + structured data.
T4.3 Optionally run image captioning / classification if useful.
T4.4 Append extracted text & metadata to the Job’s ContextBundle.
T4.5 Signal completion → Project-Management Agent.

──────────────────────────────────────── T5 SYNTHESIS / RESPONSE-GENERATION AGENT ──────────────────────────────────────── T5.1 Gather: user prompt, ContextBundle, OCR output, etc.
T5.2 Run Retrieval-Augmented Generation (RAG) to draft answer / deliverables.
T5.3 Validate factual accuracy & traceability to sources.
T5.4 Produce final artefact(s): text, report, code, slide deck, etc.
T5.5 Attach artefact(s) to Job; update status → Review / Complete.

──────────────────────────────────────── T6 DELIVERY & FEEDBACK LOOP ──────────────────────────────────────── T6.1 Notify user; expose links to outputs and underlying sources.
T6.2 Collect user feedback / revision requests.
T6.3 If revisions needed, spawn new subtasks under same JobID.

──────────────────────────────────────── T7 AUDIT & METRICS (background service) ──────────────────────────────────────── T7.1 Log all retrieval queries, document IDs, and model output.
T7.2 Track per-agent latency, success rate, and error codes.
T7.3 Surface dashboards for Ops / Compliance.

──────────────────────────────────────── Notes & Implementation Tips ──────────────────────────────────────── • JobID must propagate end-to-end for lineage; include in all filenames and database keys.
• ContextBundle = JSON blob holding (doc-id, passage, source-page) triples and OCR text.
• “3 iKe queries” should cover orthogonal perspectives (keyword, semantic, metadata).
• Each agent publishes events; Project-Management Agent orchestrates via a state machine, NOT hard-coded waits.
• Image reading can be kept stateless—store raw image once, forward handle to OCR micro-service.

Based on the above project details, please create a comprehensive list of tasks that would be needed to complete this project successfully. For each task, provide:

1. A clear, concise title
2. A detailed description of what needs to be done
3. An estimated due date
4. A suggested status (Not Started, In Progress, Reviewed, or Complete)
5. Any categories/tags that would be relevant
6. Dependencies where appropriate
7. Appropriate priorities
8. Relevant notes for execution

Format your response as a numbered list with clear sections for each task.

"""

CRITERIA TO APPLY:
1. Add more specific details about the technical requirements for each task, such as programming languages, tools, or frameworks to be used.
2. Clarify the roles and responsibilities of each agent and how they interact with one another.
3. Provide a detailed project timeline with milestones and deadlines for each task.
4. Include a section on the target audience and the purpose of the project to ensure alignment with user needs.
5. Specify the format and structure requirements for deliverables, such as document templates or API specifications.
6. Define the tone and style preferences for all written content, including technical documentation and user-facing outputs.
7. Add examples or demonstrations for complex tasks, such as how to compose the 3 iKe queries or how to structure the ContextBundle.
8. Outline any constraints or limitations, such as resource constraints or technical boundaries.
9. Establish clear evaluation criteria for measuring the success of each task and the overall project.
10. Include any missing critical information, such as budget, team size, or external dependencies.
11. Add dependencies between tasks to ensure proper sequencing and execution order.
12. Include priorities for each task to guide resource allocation and focus.
13. Provide notes for execution that offer practical advice or considerations for completing each task.
14. Ensure consistent use of terminology and concepts throughout the task list.
15. Add categories or tags to each task to facilitate organization and filtering.
16. Include a section on how to handle errors, exceptions, and edge cases in each task.
17. Specify the required input and output formats for each task, such as JSON structure for the ContextBundle.
18. Add a section on how to ensure data consistency and integrity across all tasks and agents.
19. Include a section on security considerations and data protection measures.
20. Provide a section on how to handle user feedback and revisions effectively.

IMPORTANT INSTRUCTIONS:
1. Your output should be an OPTIMIZED PROMPT, not an answer to the prompt.
2. Do NOT provide the actual information or answer that would respond to the prompt.
3. Instead, rewrite the original prompt to make it clearer, more specific, and more effective.
4. The optimized prompt should incorporate all the criteria provided.
5. The optimized prompt should be something a user would send to an AI to get better results.
6. The optimized prompt should start with an action verb like "Provide", "Explain", "Describe", etc.
7. Keep the optimized prompt concise and focused - ideally under 300 characters.
8. Do not include any meta-commentary or notes about the prompt - just write the optimized prompt itself.

For example, if the original prompt is "tell me about dogs", your optimized prompt might be "Provide a comprehensive overview of domestic dogs, including their evolution, common breeds, and their role as human companions. Include specific examples and focus on scientifically accurate information."


Your response should ONLY contain the optimized prompt itself, with no additional text, headers, or formatting.

