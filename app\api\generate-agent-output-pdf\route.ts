import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { generatePDF } from '../../../lib/tools/pdf-generator';
import { adminDb } from '../../../components/firebase-admin';

interface GenerateAgentOutputPdfRequest {
  agentOutputId: string;
  title: string;
  content: string;
  category: string;
  metadata?: Record<string, string>;
}

/**
 * API endpoint for generating and saving PDF reports from Agent Output data
 * This endpoint:
 * 1. Fetches the Agent Output data from Firestore
 * 2. Generates a PDF using the pdf-generator utility
 * 3. Saves the PDF to byteStore with the specified category
 * 4. Returns success status and file URL
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const {
      agentOutputId,
      title,
      content,
      category,
      metadata = {}
    }: GenerateAgentOutputPdfRequest = await request.json();

    // Validate required parameters
    if (!agentOutputId || !title || !content) {
      return NextResponse.json({
        success: false,
        error: 'Missing required parameters: agentOutputId, title, and content are required'
      }, { status: 400 });
    }

    console.log(`[AGENT_OUTPUT_PDF] Generating PDF for Agent Output ID: ${agentOutputId}`);

    // Fetch the full Agent Output data from Firestore to get additional metadata
    let agentOutputData = null;
    try {
      const outputDoc = await adminDb.collection('Agent_Output').doc(agentOutputId).get();
      if (outputDoc.exists) {
        agentOutputData = outputDoc.data();
        console.log(`[AGENT_OUTPUT_PDF] Retrieved Agent Output data for ID: ${agentOutputId}`);
      } else {
        console.warn(`[AGENT_OUTPUT_PDF] Agent Output document not found: ${agentOutputId}`);
      }
    } catch (fetchError) {
      console.error(`[AGENT_OUTPUT_PDF] Error fetching Agent Output data:`, fetchError);
      // Continue with provided data even if fetch fails
    }

    // Extract category from Agent Output metadata if not provided
    let finalCategory = category;
    if (agentOutputData?.metadata?.category) {
      finalCategory = agentOutputData.metadata.category;
      console.log(`[AGENT_OUTPUT_PDF] Using category from Agent Output metadata: ${finalCategory}`);
    }

    // Prepare enhanced metadata
    const enhancedMetadata = {
      agentOutputId,
      sourceCollection: 'Agent_Output',
      generatedBy: 'PMO Agent Output System',
      documentType: 'Agent Output Report',
      userId: session.user.email,
      ...metadata,
      // Add additional metadata from Agent Output if available
      ...(agentOutputData?.agentType && { agentType: agentOutputData.agentType }),
      ...(agentOutputData?.userId && { originalUserId: agentOutputData.userId }),
      ...(agentOutputData?.timestamp && { originalTimestamp: agentOutputData.timestamp }),
      ...(agentOutputData?.metadata?.pmoId && { pmoId: agentOutputData.metadata.pmoId }),
      ...(agentOutputData?.metadata?.assignedTeam && { assignedTeam: agentOutputData.metadata.assignedTeam }),
      ...(agentOutputData?.metadata?.teamName && { teamName: agentOutputData.metadata.teamName })
    };

    console.log(`[AGENT_OUTPUT_PDF] Generating PDF with category: ${finalCategory}`);

    // Generate PDF using the pdf-generator utility
    const result = await generatePDF({
      title,
      content,
      category: finalCategory,
      metadata: enhancedMetadata
    });

    if (!result.success) {
      console.error(`[AGENT_OUTPUT_PDF] PDF generation failed:`, result.error);
      return NextResponse.json({
        success: false,
        error: result.error || 'Failed to generate PDF'
      }, { status: 500 });
    }

    console.log(`[AGENT_OUTPUT_PDF] PDF generated successfully for Agent Output ID: ${agentOutputId}`);
    console.log(`[AGENT_OUTPUT_PDF] PDF saved with file URL: ${result.fileUrl}`);

    return NextResponse.json({
      success: true,
      fileUrl: result.fileUrl,
      category: finalCategory,
      agentOutputId,
      message: 'PDF report generated and saved successfully'
    });

  } catch (error: any) {
    console.error('[AGENT_OUTPUT_PDF] Error generating PDF:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to generate PDF report'
    }, { status: 500 });
  }
}
