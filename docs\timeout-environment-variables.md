# Timeout Configuration Environment Variables

This document describes the environment variables available for configuring timeout settings for long-running operations in the application.

## Business Analysis Timeout Configuration

### Core Timeout Settings

```bash
# Business Analysis Request Timeout (default: 45 minutes)
BA_REQUEST_TIMEOUT=2700000

# Business Analysis Connection Timeout (default: 30 seconds)
BA_CONNECTION_TIMEOUT=30000

# Business Analysis Keep-Alive Timeout (default: 10 minutes)
BA_KEEP_ALIVE_TIMEOUT=600000

# Business Analysis Maximum Retry Attempts (default: 2)
BA_MAX_RETRIES=2

# Business Analysis Retry Delay (default: 10 seconds)
BA_RETRY_DELAY=10000

# Additional timeout per analyst (default: 5 minutes)
BA_PER_ANALYST_TIMEOUT=300000
```

### Analysis Type Specific Timeouts

```bash
# System Analysis Timeout (default: 20 minutes)
BA_SYSTEM_TIMEOUT=1200000

# Product Overview Timeout (default: 15 minutes)
BA_PRODUCT_TIMEOUT=900000

# Requirements Engineering Timeout (default: 25 minutes)
BA_REQUIREMENTS_TIMEOUT=1500000

# Specification Development Timeout (default: 30 minutes)
BA_SPECIFICATION_TIMEOUT=1800000

# Comprehensive Analysis Timeout (default: 60 minutes)
BA_COMPREHENSIVE_TIMEOUT=3600000
```

## PMO Notification Timeout Configuration

```bash
# PMO Request Timeout (default: 50 minutes)
PMO_REQUEST_TIMEOUT=3000000

# PMO Connection Timeout (default: 30 seconds)
PMO_CONNECTION_TIMEOUT=30000

# PMO Keep-Alive Timeout (default: 15 minutes)
PMO_KEEP_ALIVE_TIMEOUT=900000

# PMO Maximum Retry Attempts (default: 1)
PMO_MAX_RETRIES=1

# PMO Retry Delay (default: 30 seconds)
PMO_RETRY_DELAY=30000
```

## Recommended Production Settings

For production environments, consider these optimized settings:

```bash
# Extended timeouts for production reliability
BA_REQUEST_TIMEOUT=3600000          # 60 minutes
BA_COMPREHENSIVE_TIMEOUT=5400000    # 90 minutes
PMO_REQUEST_TIMEOUT=3900000         # 65 minutes

# Conservative retry settings
BA_MAX_RETRIES=1
PMO_MAX_RETRIES=0

# Longer keep-alive for stable connections
BA_KEEP_ALIVE_TIMEOUT=1800000       # 30 minutes
PMO_KEEP_ALIVE_TIMEOUT=1800000      # 30 minutes
```

## Development Settings

For development environments with faster processing:

```bash
# Shorter timeouts for development
BA_REQUEST_TIMEOUT=1800000          # 30 minutes
BA_COMPREHENSIVE_TIMEOUT=2700000    # 45 minutes
PMO_REQUEST_TIMEOUT=2100000         # 35 minutes

# More aggressive retries for testing
BA_MAX_RETRIES=3
PMO_MAX_RETRIES=2

# Shorter keep-alive for development
BA_KEEP_ALIVE_TIMEOUT=300000        # 5 minutes
PMO_KEEP_ALIVE_TIMEOUT=300000       # 5 minutes
```

## Environment Variable Priority

The timeout configuration follows this priority order:

1. **Environment Variables** - Highest priority
2. **Default Values** - Used when environment variables are not set
3. **Fallback Values** - Used when parsing fails

## Timeout Calculation Examples

### Standard Analysis
- Base timeout: 20 minutes (system analysis)
- 1 analyst: 20 minutes total
- 3 analysts: 20 + (2 × 5) = 30 minutes total

### Comprehensive Analysis
- Base timeout: 60 minutes
- 1 analyst: 60 minutes total
- 4 analysts: 60 + (3 × 5) = 75 minutes total

## Monitoring and Logging

The application logs timeout-related events:

```
[PMO Business Analysis] Request timeout configured: 45 minutes
[PMO Business Analysis] Analysis type: comprehensive, estimated duration: 75 minutes
[PMO Business Analysis] Using dynamic timeout: 75 minutes
```

## Troubleshooting

### Common Issues

1. **Timeout Too Short**
   - Symptom: Frequent timeout errors
   - Solution: Increase relevant timeout values

2. **Timeout Too Long**
   - Symptom: Hanging requests, resource exhaustion
   - Solution: Decrease timeout values

3. **Connection Drops**
   - Symptom: "fetch failed" errors
   - Solution: Increase keep-alive timeout

### Error Messages

- `REQUEST_TIMEOUT`: Main request timeout exceeded
- `CONNECTION_TIMEOUT`: Failed to establish connection
- `KEEP_ALIVE_TIMEOUT`: Connection dropped during processing
- `ABORT_TIMEOUT`: Request manually aborted

## Best Practices

1. **Set Conservative Timeouts**: Better to have longer timeouts than failed requests
2. **Monitor Performance**: Track actual processing times to optimize timeouts
3. **Environment-Specific**: Use different settings for dev/staging/production
4. **Gradual Adjustment**: Make incremental changes and monitor results
5. **Document Changes**: Keep track of timeout adjustments and their impact

## Integration with Next.js

These environment variables should be added to:

- `.env.local` for local development
- `.env.production` for production deployment
- Deployment platform environment configuration (Vercel, Railway, etc.)

Note: These are server-side environment variables and do not need the `NEXT_PUBLIC_` prefix.
