/**
 * Base Marketing Agent class
 * All specialized marketing agents will inherit from this class
 */

import { LlmTool, LlmProvider } from '../../tools/llm-tool';
import { webContentExtractorTool, ExtractionOptions } from '../../tools/web-content-extractor';
import { internetSearchTool } from '../../tools/internet-search';
import { SavePdfToByteStoreResult, StorageTool } from '../../tools/storage-tool';
import { PdfGeneratorTool, PdfContent, PdfGenerationOptions } from '../../tools/pdf-generator';
import { AgentMemoryManager } from '../AgentMemoryManager';

export interface AgentMemory {
  Agent_Response: Record<string, any>; // For agent responses and interactions
}

export interface AgentMessage {
  from: string;
  to: string;
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AgentTask {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  assignedTo: string;
  createdBy: string;
  createdAt: Date;
  dueDate?: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Base Marketing Agent class that provides common functionality
 * for all marketing agents
 */
export class MarketingAgent {
  protected id: string;
  protected name: string;
  protected role: string;
  protected description: string;
  protected memory: AgentMemory;
  protected messageQueue: AgentMessage[];
  protected tasks: AgentTask[];
  protected llmTool: LlmTool;
  protected defaultLlmProvider: LlmProvider;
  protected defaultLlmModel: string;
  protected memoryManager: AgentMemoryManager | null = null;
  protected storageTool: StorageTool;
  protected pdfGeneratorTool: PdfGeneratorTool;
  protected userId: string;

  constructor(
    id: string,
    name: string,
    role: string,
    description: string,
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'openai',
    defaultLlmModel: string = 'gpt-4o'
  ) {
    this.id = id;
    this.name = name;
    this.role = role;
    this.description = description;
    this.userId = userId;
    this.defaultLlmProvider = defaultLlmProvider;
    this.defaultLlmModel = defaultLlmModel;

    // Initialize memory
    this.memory = {
      Agent_Response: {}
    };

    // Initialize message queue
    this.messageQueue = [];

    // Initialize tasks
    this.tasks = [];

    // Initialize tools
    this.llmTool = new LlmTool();
    this.storageTool = new StorageTool();
    this.pdfGeneratorTool = new PdfGeneratorTool();

    // Initialize memory manager if userId is provided
    if (userId) {
      this.memoryManager = new AgentMemoryManager(userId);
      this.loadMemoryFromStorage();
    }
  }

  /**
   * Process a request and generate a response
   * @param request The request to process
   * @returns The response from the agent
   */
  async processRequest(request: string): Promise<string> {
    try {
      // Create a prompt that includes the agent's role and context
      const prompt = this.createPrompt(request);

      // Create model options based on the provider
      const modelOptions: any = { temperature: 0.7 };

      // Process with LLM
      const response = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
      });

      // Store the interaction in memory
      this.storeInteraction(request, response);

      return response;
    } catch (error) {
      console.error(`Error processing request in ${this.name}:`, error);
      return `I encountered an error while processing your request. Please try again.`;
    }
  }

  /**
   * Create a prompt for the LLM that includes the agent's role and context
   * @param request The user request
   * @returns The formatted prompt
   */
  protected createPrompt(request: string): string {
    return `You are ${this.name}, a marketing agent with the role of ${this.role}.
${this.description}

Recent context:
${this.getRecentContext()}

User request: ${request}

Please respond in a helpful, professional manner consistent with your role.`;
  }

  /**
   * Get recent context from memory to include in prompts
   */
  protected getRecentContext(): string {
    // Extract relevant context from memory
    const recentInteractions = this.memory.Agent_Response.recentInteractions || [];

    if (recentInteractions.length === 0) {
      return "No recent interactions.";
    }

    // Format recent interactions as context
    return recentInteractions
      .slice(-3) // Get last 3 interactions
      .map((interaction: any) =>
        `User: ${interaction.request}\nAgent: ${interaction.response}`
      )
      .join('\n\n');
  }

  /**
   * Store an interaction in the agent's memory
   */
  protected storeInteraction(request: string, response: string): void {
    // Initialize if doesn't exist
    if (!this.memory.Agent_Response.recentInteractions) {
      this.memory.Agent_Response.recentInteractions = [];
    }

    // Add to recent interactions
    this.memory.Agent_Response.recentInteractions.push({
      request,
      response,
      timestamp: new Date()
    });

    // Limit size of recent interactions
    if (this.memory.Agent_Response.recentInteractions.length > 10) {
      this.memory.Agent_Response.recentInteractions.shift();
    }

    // Save to persistent storage if memory manager is available
    this.saveMemoryToStorage();
  }

  /**
   * Get the agent's thinking process for a given request
   * This method is used to provide transparency into the agent's reasoning
   * @param request The user request
   * @returns A detailed explanation of the agent's thinking process
   */
  async getThinking(request: string): Promise<string> {
    try {
      // Create a prompt that asks the agent to explain its thinking process
      const prompt = `You are ${this.name}, a marketing agent with the role of ${this.role}.
${this.description}

I want you to explain your thinking process for addressing the following request:
"${request}"

Please provide a detailed explanation of:
1. How you understand this request
2. What key marketing concepts and strategies are relevant
3. What approach you would take to address it
4. What specific marketing techniques or frameworks you would apply
5. How you would structure your response

Explain your reasoning in detail, showing your expertise in marketing and your specific role.
`;

      // Create model options based on the provider
      const modelOptions: any = { temperature: 0.7 };

      // Process with LLM
      const thinking = await this.llmTool.processContent({
        prompt,
        model: this.defaultLlmModel,
        provider: this.defaultLlmProvider,
        modelOptions
      });

      return thinking;
    } catch (error) {
      console.error(`Error getting thinking process in ${this.name}:`, error);
      return `I encountered an error while explaining my thinking process. The error was: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Send a message to another agent
   */
  async sendMessage(toAgentId: string, content: string, metadata?: Record<string, any>): Promise<void> {
    const message: AgentMessage = {
      from: this.id,
      to: toAgentId,
      content,
      timestamp: new Date(),
      metadata
    };

    // Add to local message queue
    this.messageQueue.push(message);

    // Send to persistent storage if memory manager is available
    if (this.memoryManager) {
      await this.memoryManager.sendMessage(this.id, toAgentId, content, metadata);
    }

    console.log(`Message sent from ${this.name} to agent ${toAgentId}: ${content}`);
  }

  /**
   * Request additional information from another agent (typically the Strategic Director)
   * This method allows agents to request clarification or additional context when needed
   *
   * @param toAgentId - The ID of the agent to request information from
   * @param requestType - The type of information being requested
   * @param requestDetails - Specific details about what information is needed
   * @param taskId - Optional ID of the task this request is related to
   * @returns Promise<void>
   */
  async requestInformation(
    toAgentId: string,
    requestType: 'clarification' | 'additional_context' | 'feedback' | 'approval',
    requestDetails: {
      topic: string;
      questions: string[];
      currentProgress?: string;
      blockers?: string[];
    },
    taskId?: string
  ): Promise<void> {
    // Format the request message
    const formattedRequest = `
## Information Request from ${this.name}
**Type:** ${requestType}
**Topic:** ${requestDetails.topic}
**Related Task:** ${taskId || 'Not specified'}

### Questions:
${requestDetails.questions.map((q, i) => `${i+1}. ${q}`).join('\n')}

${requestDetails.currentProgress ? `### Current Progress:\n${requestDetails.currentProgress}` : ''}
${requestDetails.blockers ? `### Blockers:\n${requestDetails.blockers.map(b => `- ${b}`).join('\n')}` : ''}
`;

    // Send the request as a message with special metadata
    await this.sendMessage(toAgentId, formattedRequest, {
      messageType: 'information_request',
      requestType,
      taskId,
      requiresResponse: true,
      messageId: `req-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    });

    console.log(`Information request sent from ${this.name} to ${toAgentId} regarding ${requestDetails.topic}`);
  }

  /**
   * Receive messages sent to this agent
   */
  async receiveMessages(): Promise<AgentMessage[]> {
    // Get messages from local queue
    const localMessages = this.messageQueue.filter(msg => msg.to === this.id);

    // Remove processed messages from the local queue
    this.messageQueue = this.messageQueue.filter(msg => msg.to !== this.id);

    // Get messages from persistent storage if memory manager is available
    if (this.memoryManager) {
      const persistentMessages = await this.memoryManager.getUnreadMessages(this.id);
      return [...localMessages, ...persistentMessages];
    }

    return localMessages;
  }

  /**
   * Create a new task
   */
  async createTask(title: string, description: string, priority: 'low' | 'medium' | 'high', assignedTo: string, dueDate?: Date): Promise<AgentTask> {
    const task: AgentTask = {
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title,
      description,
      status: 'pending',
      priority,
      assignedTo,
      createdBy: this.id,
      createdAt: new Date(),
      dueDate
    };

    this.tasks.push(task);

    // Save to persistent storage if memory manager is available
    if (this.memoryManager) {
      const taskId = await this.memoryManager.createTask(
        title,
        description,
        priority,
        assignedTo,
        this.id,
        dueDate
      );
      task.id = taskId;
    }

    return task;
  }

  /**
   * Update a task's status
   */
  async updateTaskStatus(taskId: string, status: 'pending' | 'in-progress' | 'completed' | 'failed'): Promise<boolean> {
    const task = this.tasks.find(t => t.id === taskId);

    if (!task) {
      return false;
    }

    task.status = status;

    if (status === 'completed') {
      task.completedAt = new Date();
    }

    // Update in persistent storage if memory manager is available
    if (this.memoryManager) {
      await this.memoryManager.updateTaskStatus(taskId, status);
    }

    return true;
  }

  /**
   * Get all tasks assigned to this agent
   */
  async getAssignedTasks(): Promise<AgentTask[]> {
    // Get tasks from local memory
    const localTasks = this.tasks.filter(task => task.assignedTo === this.id);

    // Get tasks from persistent storage if memory manager is available
    if (this.memoryManager) {
      const persistentTasks = await this.memoryManager.getAssignedTasks(this.id);
      return [...localTasks, ...persistentTasks];
    }

    return localTasks;
  }

  /**
   * Get all tasks created by this agent
   */
  async getCreatedTasks(): Promise<AgentTask[]> {
    // Get tasks from local memory
    const localTasks = this.tasks.filter(task => task.createdBy === this.id);

    // Get tasks from persistent storage if memory manager is available
    if (this.memoryManager) {
      const persistentTasks = await this.memoryManager.getCreatedTasks(this.id);
      return [...localTasks, ...persistentTasks];
    }

    return localTasks;
  }

  /**
   * Research information from the web
   */
  async researchWeb(query: string, options: ExtractionOptions = {}): Promise<string> {
    try {
      // First, try to use internet-search to get relevant information
      const searchResults = await internetSearchTool.search(query, { numResults: 3 });

      if (searchResults && searchResults.results && searchResults.results.length > 0) {
        // If we have search results, try to extract more detailed content from the URLs
        const contentPromises = searchResults.results.slice(0, 3).map(async (result) => {
          try {
            // Try to extract more detailed content from the URL
            if (result.link && (result.link.startsWith('http://') || result.link.startsWith('https://'))) {
              const extracted = await webContentExtractorTool.extractContent(result.link, options);
              return `## ${result.title}\n${extracted.formattedContent}\n\n`;
            } else {
              // If the link is invalid, just use the snippet
              return `## ${result.title}\n${result.snippet || 'No description available.'}\n\n`;
            }
          } catch (extractError) {
            console.error(`Error extracting content from ${result.link}:`, extractError);
            // Fallback to using just the snippet if extraction fails
            return `## ${result.title}\n${result.snippet || 'No description available.'}\n\n`;
          }
        });

        try {
          const contents = await Promise.all(contentPromises);
          return contents.join('\n');
        } catch (promiseError) {
          console.error(`Error processing search results for ${query}:`, promiseError);
          // Fallback to just using the formatted search results
          return searchResults.formattedResults;
        }
      } else {
        // If no search results, check if the query is a URL and try direct extraction
        if (query.startsWith('http://') || query.startsWith('https://')) {
          try {
            const result = await webContentExtractorTool.extractContent(query, options);
            return result.formattedContent;
          } catch (directError) {
            console.error(`Error with direct extraction for ${query}:`, directError);
            return `I couldn't extract information from "${query}". The URL may be invalid or inaccessible.`;
          }
        } else {
          // Return the formatted results or error message from the search
          return searchResults.formattedResults ||
                 `No search results found for "${query}". Please try a different query.`;
        }
      }
    } catch (error) {
      console.error(`Error researching web in ${this.name}:`, error);

      // If the query looks like a URL, try direct extraction as a last resort
      if (query.startsWith('http://') || query.startsWith('https://')) {
        try {
          const result = await webContentExtractorTool.extractContent(query, options);
          return result.formattedContent;
        } catch (directError) {
          // Both search and direct extraction failed
          return `I encountered an error while researching "${query}". The URL may be invalid or inaccessible.`;
        }
      }

      return `I encountered an error while researching "${query}". Please try again with a more specific query.`;
    }
  }

  /**
   * Generate a PDF document
   * @param title - The title of the PDF
   * @param content - The content of the PDF
   * @param options - PDF generation options
   * @param saveToByteStore - Whether to save the PDF to byteStore (default: true)
   * @returns Promise with the PDF buffer or byteStore result
   */
  async generatePdf(title: string, content: string, options: PdfGenerationOptions = {}, saveToByteStore: boolean = true): Promise<Buffer | SavePdfToByteStoreResult> {
    try {
      const pdfContents: PdfContent[] = [{
        title,
        content
      }];

      // Add additional options for byteStore
      const pdfOptions: PdfGenerationOptions = {
        ...options,
        title,
        saveToByteStore,
        agentId: this.id,
        agentName: this.name,
        category: 'Marketing Agent Team'
      };

      return await this.pdfGeneratorTool.generatePdf(pdfContents, pdfOptions);
    } catch (error) {
      console.error(`Error generating PDF in ${this.name}:`, error);
      throw error;
    }
  }

  /**
   * Save content to storage
   */
  async saveContent(content: Record<string, any>, collectionName = "marketing_documents"): Promise<string> {
    try {
      return await this.storageTool.saveToFirestore(content, collectionName);
    } catch (error) {
      console.error(`Error saving content in ${this.name}:`, error);
      throw error;
    }
  }

  /**
   * Load memory from persistent storage
   */
  protected async loadMemoryFromStorage(): Promise<void> {
    if (!this.memoryManager) return;

    try {
      this.memory = await this.memoryManager.loadMemory(this.id);
      console.log(`Memory loaded for agent ${this.name}`);
    } catch (error) {
      console.error(`Error loading memory for agent ${this.name}:`, error);
    }
  }

  /**
   * Save memory to persistent storage
   */
  protected async saveMemoryToStorage(): Promise<void> {
    if (!this.memoryManager) return;

    try {
      await this.memoryManager.saveMemory(this.id, this.memory);
    } catch (error) {
      console.error(`Error saving memory for agent ${this.name}:`, error);
    }
  }

  /**
   * Get agent information
   */
  getInfo(): Record<string, any> {
    return {
      id: this.id,
      name: this.name,
      role: this.role,
      description: this.description
    };
  }
}
