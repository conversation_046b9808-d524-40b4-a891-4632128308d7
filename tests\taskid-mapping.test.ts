/**
 * Test for TaskId mapping functionality in PMOProjectsTaskAgent
 * Verifies that Agent_Output TaskIds are properly mapped to PMO ProjectIds
 */

import { PMOProjectsTaskAgent } from '../lib/agents/pmoProjectsTaskAgent';

describe('TaskId Mapping in PMOProjectsTaskAgent', () => {
  let agent: PMOProjectsTaskAgent;

  beforeEach(() => {
    agent = new PMOProjectsTaskAgent();
  });

  describe('_mapTaskIdsToProjectIds', () => {
    test('should map Agent_Output TaskIds to PMO ProjectIds', () => {
      const content = `
        Task ID: RPT_001 - Market Research Analysis
        TaskId: MKT_002 - Marketing Strategy Development
        ID: DEV_003 - Software Development
        Task RPT_004 - Additional Research
        RPT_005 analysis completed
        Task-ABC123 implementation
      `;

      const pmoId = '9978d39b-2483-4478-8a94-ab20f61185d2';
      const pmoProjectId = 'PMO-PROJECT-001';

      // Access the private method for testing
      const result = (agent as any)._mapTaskIdsToProjectIds(content, pmoId, pmoProjectId);

      // Verify that Agent_Output TaskIds are replaced with PMO ProjectId
      expect(result).toContain('Task ID: PMO-PROJECT-001');
      expect(result).toContain('TaskId: PMO-PROJECT-001');
      expect(result).toContain('ID: PMO-PROJECT-001');
      expect(result).toContain('PMO-PROJECT-001'); // For standalone TaskIds

      // Verify original Agent_Output TaskIds are removed
      expect(result).not.toContain('RPT_001');
      expect(result).not.toContain('MKT_002');
      expect(result).not.toContain('DEV_003');
      expect(result).not.toContain('RPT_004');
      expect(result).not.toContain('RPT_005');
    });

    test('should handle content without TaskIds gracefully', () => {
      const content = 'This is regular content without any TaskIds';
      const pmoId = '9978d39b-2483-4478-8a94-ab20f61185d2';
      const pmoProjectId = 'PMO-PROJECT-001';

      const result = (agent as any)._mapTaskIdsToProjectIds(content, pmoId, pmoProjectId);

      expect(result).toBe(content); // Should return unchanged
    });

    test('should generate PMO ProjectId from pmoId when projectId not provided', () => {
      const content = 'Task ID: RPT_001 - Test Task';
      const pmoId = '9978d39b-2483-4478-8a94-ab20f61185d2';

      const result = (agent as any)._mapTaskIdsToProjectIds(content, pmoId);

      expect(result).toContain('PMO-9978d39b'); // Should use first 8 chars of pmoId
    });

    test('should preserve existing PMO format TaskIds', () => {
      const content = `
        Task ID: PMO-PROJECT-001 - Already correct format
        TaskId: RPT_002 - Needs mapping
      `;
      const pmoProjectId = 'PMO-PROJECT-001';

      const result = (agent as any)._mapTaskIdsToProjectIds(content, undefined, pmoProjectId);

      // Should preserve existing PMO format
      expect(result).toContain('Task ID: PMO-PROJECT-001');
      // Should map the other one
      expect(result).toContain('TaskId: PMO-PROJECT-001');
      expect(result).not.toContain('RPT_002');
    });

    test('should handle empty or null content', () => {
      const pmoProjectId = 'PMO-PROJECT-001';

      expect((agent as any)._mapTaskIdsToProjectIds('', undefined, pmoProjectId)).toBe('');
      expect((agent as any)._mapTaskIdsToProjectIds(null, undefined, pmoProjectId)).toBe(null);
      expect((agent as any)._mapTaskIdsToProjectIds(undefined, undefined, pmoProjectId)).toBe(undefined);
    });
  });

  describe('Enhanced Notes Generation', () => {
    test('should include PMO Project ID in metadata', async () => {
      const mockTask = {
        title: 'Test Task',
        description: 'Test Description',
        category: 'Research',
        teamAssignment: 'Research Team',
        estimatedDuration: '2 weeks',
        dependencies: []
      };

      const mockAgentOutput = {
        agentType: 'strategic-director',
        pmoMetadata: {
          pmoId: '9978d39b-2483-4478-8a94-ab20f61185d2'
        },
        result: {
          output: 'Task ID: RPT_001 - Market research analysis required'
        }
      };

      const pmoProjectId = 'PMO-PROJECT-001';

      // Mock the Google AI call to return a simple response
      jest.mock('../../lib/tools/google-ai', () => ({
        processWithGoogleAI: jest.fn().mockResolvedValue('Enhanced task notes content')
      }));

      const result = await (agent as any)._generateEnhancedNotes(mockTask, mockAgentOutput, pmoProjectId);

      expect(result).toContain('PMO Project ID: PMO-PROJECT-001');
      expect(result).toContain('PMO Record ID: 9978d39b-2483-4478-8a94-ab20f61185d2');
    });
  });
});
