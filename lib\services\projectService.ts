/**
 * projectService.ts
 *
 * This service provides functionality for managing projects,
 * including creation, updating, and deletion.
 */

import { adminDb } from '../../components/firebase-admin';

export interface ProjectData {
  id?: string;
  name: string;
  description?: string;
  startDate?: Date | string;
  endDate?: Date | string;
  owner: string;
  teamMembers?: string[];
  categories?: string[];
  status?: string;
  isAdmin?: boolean;
}

export interface DeleteProjectResult {
  success: boolean;
  message: string;
  error?: string;
}

export class ProjectService {
  /**
   * Delete a project and all its associated data
   *
   * @param projectId - The ID of the project to delete
   * @param userId - The ID of the user performing the deletion
   * @returns Result of the deletion operation
   */
  static async deleteProject(projectId: string, userId: string): Promise<DeleteProjectResult> {
    try {
      // Start a batch operation
      const batch = adminDb.batch();

      // Reference to the project document
      const projectRef = adminDb.collection('projects').doc(projectId);

      // Get the project data to check permissions
      const projectDoc = await projectRef.get();

      if (!projectDoc.exists) {
        return {
          success: false,
          message: 'Project not found',
          error: 'The specified project does not exist'
        };
      }

      const projectData = projectDoc.data() as ProjectData | undefined;

      // Ensure projectData exists
      if (!projectData) {
        return {
          success: false,
          message: 'Project data not found',
          error: 'The project exists but has no data'
        };
      }

      // Check if user has permission to delete the project
      const isOwner = projectData.owner === userId;
      const isMember = Array.isArray(projectData.teamMembers) && projectData.teamMembers.includes(userId);
      const isAdmin = projectData.isAdmin === true;

      if (!isOwner && !isMember && !isAdmin) {
        return {
          success: false,
          message: 'Permission denied',
          error: 'You do not have permission to delete this project'
        };
      }

      // Get all tasks associated with this project
      const tasksSnapshot = await adminDb
        .collection('tasks')
        .where('projectId', '==', projectId)
        .get();

      // Add all tasks to the batch delete operation
      tasksSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // Get all comments associated with this project
      const commentsSnapshot = await adminDb
        .collection('comments')
        .where('projectId', '==', projectId)
        .get();

      // Add all comments to the batch delete operation
      commentsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // Get all attachments associated with this project
      const attachmentsSnapshot = await adminDb
        .collection('attachments')
        .where('projectId', '==', projectId)
        .get();

      // Add all attachments to the batch delete operation
      attachmentsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      // Finally, delete the project itself
      batch.delete(projectRef);

      // Commit the batch operation
      await batch.commit();

      return {
        success: true,
        message: 'Project and all associated data successfully deleted'
      };
    } catch (error) {
      console.error('Error deleting project:', error);
      return {
        success: false,
        message: 'Failed to delete project',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}
