"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import Rehearsals from "./Rehearsals";

// Voice settings interface
export interface VoiceSettings {
  gender: "male" | "female";
  tone: "neutral" | "professional" | "friendly" | "dramatic";
  emotionalIntensity: "low" | "medium" | "high";
  readingPace: "slow" | "normal" | "fast";
  instructions: string;
}

// Updated Props Interface with additional status reporting callbacks
interface RealtimeProps {
  // Script Info
  scriptContent: string;
  isScriptLoading: boolean;
  isScriptReady: boolean;
  scriptName: string | null;

  // Dialogue History Management
  dialogueHistory: Array<{ role: string; content: string }>;
  setDialogueHistory: React.Dispatch<React.SetStateAction<Array<{ role: string; content: string }>>>;
  realtimeDialogue: string;
  setRealtimeDialogue: React.Dispatch<React.SetStateAction<string>>;

  // Interaction Mode
  interactionMode: "direct" | "conversational";
  setInteractionMode: React.Dispatch<React.SetStateAction<"direct" | "conversational">>;

  // Voice Settings
  voiceGender: "male" | "female";
  setVoiceGender: React.Dispatch<React.SetStateAction<"male" | "female">>;
  voiceTone?: "neutral" | "professional" | "friendly" | "dramatic";
  emotionalIntensity?: "low" | "medium" | "high";
  readingPace?: "slow" | "normal" | "fast";

  // Session Status
  sessionStatus?: "loading" | "authenticated" | "unauthenticated";

  // Status Reporting Callbacks
  setExternalIsListening: React.Dispatch<React.SetStateAction<boolean>>;
  setExternalIsSpeaking: React.Dispatch<React.SetStateAction<boolean>>;
  setExternalApiConfigStatus: React.Dispatch<React.SetStateAction<"unchecked" | "valid" | "invalid" | "connecting">>;
  setExternalDetailedErrorInfo: React.Dispatch<React.SetStateAction<string | null>>;
  setExternalDiagnosisInProgress: React.Dispatch<React.SetStateAction<boolean>>;
  setExternalVoiceErrorMessage: React.Dispatch<React.SetStateAction<string>>;
  setExternalIsMuted: React.Dispatch<React.SetStateAction<boolean>>;
  setExternalHasPermission: React.Dispatch<React.SetStateAction<boolean>>;
}

// Direct WebRTC Connection Interface
interface DirectRTCConnection {
  peerConnection: RTCPeerConnection;
  dataChannel: RTCDataChannel;
  mediaStream: MediaStream;
  audioElement: HTMLAudioElement;
  active: boolean;
}

// Add CSS styles for the technical details modal
const technicalDetailsStyles = `
.technical-details-btn {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.technical-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.technical-details-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  max-width: 80%;
  max-height: 80%;
  overflow: auto;
}

.technical-details-content h3 {
  margin-top: 0;
}

.technical-details-content pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  font-size: 12px;
  white-space: pre-wrap;
}

.close-modal-btn {
  margin-top: 15px;
  padding: 5px 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
`;

function Realtime({
  // Destructure props from Reader-modal
  scriptContent,
  isScriptLoading,
  isScriptReady,
  scriptName,
  setDialogueHistory,
  realtimeDialogue,
  setRealtimeDialogue,
  // Interaction Mode
  interactionMode,
  setInteractionMode,
  // Voice Settings
  voiceGender,
  setVoiceGender, // Not used directly but required for prop interface
  voiceTone = "neutral", // Default values for optional props
  emotionalIntensity = "medium",
  readingPace = "normal",
  // Session Status
  sessionStatus = "authenticated",
  // Status reporting callbacks
  setExternalIsListening,
  setExternalIsSpeaking,
  setExternalApiConfigStatus,
  setExternalDetailedErrorInfo,
  setExternalDiagnosisInProgress,
  setExternalVoiceErrorMessage,
  setExternalIsMuted,
  setExternalHasPermission
}: RealtimeProps) {
  // --- WebRTC State ---
  const [apiConfigStatus, setApiConfigStatus] = useState<"unchecked" | "valid" | "invalid" | "connecting">("unchecked");
  const [isListening, setIsListening] = useState<boolean>(false);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [detailedErrorInfo, setDetailedErrorInfo] = useState<string | null>(null);
  const [diagnosisInProgress, setDiagnosisInProgress] = useState<boolean>(false);
  const [voiceErrorMessage, setVoiceErrorMessage] = useState<string>("");
  const [hasPermission, setHasPermission] = useState<boolean>(false);
  const [connectionQuality, setConnectionQuality] = useState<"excellent" | "good" | "poor" | "unknown">("unknown");
  const [retryCount, setRetryCount] = useState<number>(0);
  const [isReconnecting, setIsReconnecting] = useState<boolean>(false);
  const directConnectionRef = useRef<DirectRTCConnection | null>(null);
  const isMounted = useRef<boolean>(true);
  const connectionHealthInterval = useRef<NodeJS.Timeout | null>(null);
  const lastEphemeralKeyRef = useRef<string | null>(null);

  // --- Interaction Mode State ---
  // Use the prop value as the initial state
  useEffect(() => {
    // Sync with parent component when the prop changes
    if (isMounted.current) {
      setInteractionMode(interactionMode);
    }
  }, [interactionMode]);

  // Sync local state to parent component
  useEffect(() => {
    setExternalIsListening(isListening);
  }, [isListening, setExternalIsListening]);

  useEffect(() => {
    setExternalIsSpeaking(isSpeaking);
  }, [isSpeaking, setExternalIsSpeaking]);

  useEffect(() => {
    setExternalApiConfigStatus(apiConfigStatus);
  }, [apiConfigStatus, setExternalApiConfigStatus]);

  // We'll add the event listener for end-rehearsal after endDirectConnection is defined

  useEffect(() => {
    setExternalDetailedErrorInfo(detailedErrorInfo);
  }, [detailedErrorInfo, setExternalDetailedErrorInfo]);

  useEffect(() => {
    setExternalDiagnosisInProgress(diagnosisInProgress);
  }, [diagnosisInProgress, setExternalDiagnosisInProgress]);

  useEffect(() => {
    setExternalVoiceErrorMessage(voiceErrorMessage);
  }, [voiceErrorMessage, setExternalVoiceErrorMessage]);

  useEffect(() => {
    setExternalIsMuted(isMuted);
  }, [isMuted, setExternalIsMuted]);

  useEffect(() => {
    setExternalHasPermission(hasPermission);
  }, [hasPermission, setExternalHasPermission]);

  // --- WebRTC Functions ---
  // Debug API Response (Helper)
  const debugApiResponse = useCallback(async (response: Response): Promise<string | null> => {
    try {
      console.groupCollapsed("API Response Debug");
      const cloned = response.clone();
      const text = await cloned.text();
      console.log(`Status: ${cloned.status} ${cloned.statusText}, Length: ${text.length}`);
      console.log("Preview:", text.substring(0, 500) + (text.length > 500 ? "..." : ""));
      try {
        console.log("Parsed JSON:", JSON.parse(text));
      } catch {
        console.log("Not JSON");
      }
      console.groupEnd();
      return text;
    } catch (e) {
      console.error("Debug func error", e);
      console.groupEnd();
      return null;
    }
  }, []);

  // Enhanced Network Diagnostics with more detailed checks
  const runNetworkDiagnostics = useCallback(async () => {
    if (!isMounted.current) return "Component unmounted";
    console.group("=== Enhanced Network Diagnostics ===");
    setDiagnosisInProgress(true);
    setDetailedErrorInfo(null);
    let finalMessage = null;

    const diagnosticResults: Record<string, string> = {};

    try {
      // Check internet connectivity
      console.log("Checking internet connectivity...");
      try {
        await fetch("https://www.google.com", {
          method: "HEAD",
          mode: "no-cors",
          cache: "no-cache",
          signal: AbortSignal.timeout(5000)
        });
        diagnosticResults.internet = "Connected";
      } catch (error) {
        diagnosticResults.internet = "Failed - No internet connection";
        throw new Error("Internet connection test failed. Please check your network connection.");
      }

      // Check OpenAI API connectivity
      console.log("Checking OpenAI API connectivity...");
      try {
        await fetch("https://api.openai.com", {
          method: "HEAD",
          mode: "no-cors",
          cache: "no-cache",
          signal: AbortSignal.timeout(5000)
        });
        diagnosticResults.openaiApi = "Connected";
      } catch (error) {
        diagnosticResults.openaiApi = "Failed - Cannot reach OpenAI API";
        throw new Error("Cannot reach OpenAI API. This may be due to network restrictions or firewall settings.");
      }

      // Check internal API endpoint
      console.log("Checking internal API endpoint...");
      try {
        const internalApiTest = await fetch("/api/realtime-session", {
          method: "GET",
          cache: "no-cache",
          signal: AbortSignal.timeout(8000) // Longer timeout for API call
        });

        if (!internalApiTest.ok) {
          diagnosticResults.internalApi = `Failed - Status: ${internalApiTest.status}`;
          throw new Error(`Internal API error: ${internalApiTest.status}`);
        }

        const responseData = await internalApiTest.json();
        if (!responseData.ephemeral_key && !responseData.client_secret?.value && !responseData.original_response) {
          diagnosticResults.internalApiFormat = "Failed - Invalid response format";
          throw new Error("API response format issue. The server did not return the expected data format.");
        }

        diagnosticResults.internalApi = "Connected";
        diagnosticResults.internalApiFormat = "Valid";
      } catch (error) {
        if (!diagnosticResults.internalApi) {
          diagnosticResults.internalApi = "Failed - Connection error";
        }
        throw error;
      }

      // Enhanced WebRTC support and connectivity check
      console.log("Checking WebRTC support...");
      if (!window.RTCPeerConnection) {
        diagnosticResults.webrtcSupport = "Failed - Not supported";
        throw new Error("WebRTC is not supported in this browser. Please try a different browser like Chrome or Edge.");
      }

      // Test WebRTC functionality with comprehensive diagnostics
      try {
        // Use the same STUN server configuration that works in the main connection
        const testPc = new RTCPeerConnection({
          iceServers: [
            {
              urls: [
                "stun:stun1.l.google.com:19302",
                "stun:stun2.l.google.com:19302",
                "stun:stun.l.google.com:19302"
              ],
            },
          ],
          iceCandidatePoolSize: 10
        });

        // Create a data channel to trigger ICE gathering
        testPc.createDataChannel("test");

        // Track ICE gathering progress and errors
        let iceGatheringComplete = false;
        let iceCandidates = {
          host: 0,
          srflx: 0, // STUN reflexive
          relay: 0, // TURN relay
          tcp: 0,   // TCP candidates
          udp: 0    // UDP candidates
        };

        let iceErrors: Array<{url: string, errorCode: number, errorText: string}> = [];

        testPc.onicecandidate = (event) => {
          if (event.candidate) {
            // Count candidate types
            const candidate = event.candidate;
            if (candidate.candidate.includes("typ host")) iceCandidates.host++;
            if (candidate.candidate.includes("typ srflx")) iceCandidates.srflx++;
            if (candidate.candidate.includes("typ relay")) iceCandidates.relay++;
            if (candidate.candidate.includes("tcptype")) iceCandidates.tcp++;
            else iceCandidates.udp++;

            console.log("ICE candidate:", candidate.candidate);
          }
        };

        testPc.onicecandidateerror = (event) => {
          const errorEvent = event as RTCPeerConnectionIceErrorEvent;
          iceErrors.push({
            url: errorEvent.url,
            errorCode: errorEvent.errorCode,
            errorText: errorEvent.errorText
          });
          console.error("ICE candidate error during diagnostics:", errorEvent);
        };

        testPc.onicegatheringstatechange = () => {
          if (testPc.iceGatheringState === "complete") {
            iceGatheringComplete = true;
          }
        };

        // Create a simple offer to test ICE gathering
        await testPc.createOffer();
        await testPc.setLocalDescription();

        // Wait for ICE gathering to complete or timeout after 5 seconds
        await Promise.race([
          new Promise<void>(resolve => {
            const checkComplete = () => {
              if (iceGatheringComplete || testPc.iceGatheringState === "complete") {
                resolve();
              } else {
                setTimeout(checkComplete, 500);
              }
            };
            checkComplete();
          }),
          new Promise<void>(resolve => setTimeout(resolve, 5000))
        ]);

        // Record diagnostic results
        diagnosticResults.webrtcIceGathering = testPc.iceGatheringState;
        diagnosticResults.webrtcCandidates = JSON.stringify(iceCandidates);

        if (iceErrors.length > 0) {
          diagnosticResults.webrtcIceErrors = JSON.stringify(iceErrors);
        }

        // Analyze connectivity options
        if (iceCandidates.srflx > 0) {
          diagnosticResults.stunConnectivity = "Working - STUN reflexive candidates found";
        } else {
          diagnosticResults.stunConnectivity = "Failed - No STUN reflexive candidates";
        }

        if (iceCandidates.relay > 0) {
          diagnosticResults.turnConnectivity = "Working - TURN relay candidates found";
        } else {
          diagnosticResults.turnConnectivity = "Not available or failed";
        }

        if (iceCandidates.tcp > 0) {
          diagnosticResults.tcpConnectivity = "Available - TCP candidates found";
        } else {
          diagnosticResults.tcpConnectivity = "Not available - No TCP candidates";
        }

        // Clean up
        testPc.close();

        // Overall assessment
        if (iceCandidates.srflx > 0 || iceCandidates.relay > 0) {
          diagnosticResults.webrtcSupport = "Supported with external connectivity";
        } else if (iceCandidates.host > 0) {
          diagnosticResults.webrtcSupport = "Supported with local connectivity only";
        } else {
          diagnosticResults.webrtcSupport = "Limited - No viable candidates found";
        }

        // Provide recommendations based on diagnostics
        if (iceErrors.length > 0 && iceCandidates.tcp === 0) {
          diagnosticResults.recommendation = "Network appears to be blocking UDP traffic. Try using a different network or contact your network administrator.";
        } else if (iceCandidates.srflx === 0 && iceCandidates.tcp > 0) {
          diagnosticResults.recommendation = "UDP appears to be blocked, but TCP is available. The application will attempt to use TCP fallback.";
        }
      } catch (error) {
        diagnosticResults.webrtcSupport = "Failed - Error initializing";
        diagnosticResults.webrtcError = error instanceof Error ? error.message : String(error);
        throw new Error("WebRTC initialization failed. This may be due to browser restrictions or network issues.");
      }

      // Check microphone permission
      console.log("Checking microphone permission...");
      if (!hasPermission) {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          stream.getTracks().forEach(t => t.stop());

          if (isMounted.current) {
            setHasPermission(true);
            setVoiceErrorMessage("");
          }
          diagnosticResults.microphonePermission = "Granted";
        } catch (error) {
          diagnosticResults.microphonePermission = "Denied";
          throw new Error("Microphone access denied. Please enable microphone access in your browser settings.");
        }
      } else {
        diagnosticResults.microphonePermission = "Already granted";
      }

      // All tests passed
      console.log("All diagnostics passed:", diagnosticResults);
      setDetailedErrorInfo(JSON.stringify(diagnosticResults, null, 2));
    } catch (error) {
      finalMessage = error instanceof Error ? error.message : String(error);
      console.error("Diagnostics Failed:", finalMessage);

      // Provide detailed error information
      setDetailedErrorInfo(
        `Diagnostic Results:\n${JSON.stringify(diagnosticResults, null, 2)}\n\nError: ${finalMessage}`
      );
    } finally {
      if (isMounted.current) setDiagnosisInProgress(false);
      console.groupEnd();
    }

    return finalMessage;
  }, [hasPermission]);

  // End Direct Connection
  const endDirectConnection = useCallback(async () => {
    console.group("=== End Direct WebRTC Connection ===");
    if (!directConnectionRef.current || !directConnectionRef.current.active) {
      console.log("No active connection or already inactive.");
      console.groupEnd();
      directConnectionRef.current = null;
      return;
    }
    console.log("Cleaning up connection...");
    directConnectionRef.current.active = false;
    const { peerConnection, dataChannel, mediaStream, audioElement } = directConnectionRef.current;

    // Stop connection health monitoring
    if (connectionHealthInterval.current) {
      clearInterval(connectionHealthInterval.current);
      connectionHealthInterval.current = null;
      console.log("Connection health monitoring stopped");
    }

    try {
      if (dataChannel?.readyState === "open") dataChannel.close();
    } catch (e) {
      console.warn("DC close error", e);
    }
    try {
      mediaStream?.getTracks().forEach(t => t.stop());
    } catch (e) {
      console.warn("Track stop error", e);
    }
    try {
      if (audioElement) {
        audioElement.pause();
        audioElement.srcObject = null;
        audioElement.remove();
      }
    } catch (e) {
      console.warn("Audio cleanup error", e);
    }
    try {
      if (peerConnection?.connectionState !== "closed") peerConnection.close();
    } catch (e) {
      console.warn("PC close error", e);
    }
    directConnectionRef.current = null;
    if (isMounted.current) {
      setIsListening(false);
      setIsSpeaking(false);
      setApiConfigStatus("unchecked");
      setRealtimeDialogue("");
      setVoiceErrorMessage("");
      setDetailedErrorInfo(null);
      setIsReconnecting(false);
      setConnectionQuality("unknown");
      console.log("Connection state reset.");
    } else {
      console.log("Cleanup occurred after component unmounted.");
    }
    console.groupEnd();
  }, [setRealtimeDialogue]);

  // Establish Direct Connection
  const establishDirectConnection = useCallback(async (ephemeralKey: string) => {
    if (!isMounted.current) return false;
    console.group("=== Direct WebRTC Connection Establishment ===");
    if (!ephemeralKey) {
      setVoiceErrorMessage("Connection error: Missing API credentials");
      setApiConfigStatus("invalid");
      console.groupEnd();
      return false;
    }

    // Store the ephemeral key for potential reconnection
    lastEphemeralKeyRef.current = ephemeralKey;

    await endDirectConnection();
    try {
      // Use multiple STUN servers for better connectivity
      const peerConnection = new RTCPeerConnection({
        iceServers: [
          {
            urls: [
              "stun:stun1.l.google.com:19302",
              "stun:stun2.l.google.com:19302",
              "stun:stun.l.google.com:19302"
            ],
          },
        ],
        iceCandidatePoolSize: 10
      });

      // Connection state monitoring with auto-reconnect
      peerConnection.onconnectionstatechange = () => {
        if (!isMounted.current) return;
        console.log("PC State:", peerConnection.connectionState);

        // Update connection quality based on state
        if (peerConnection.connectionState === "connected") {
          setConnectionQuality("excellent");
          setRetryCount(0); // Reset retry count on successful connection
          setIsReconnecting(false);
        } else if (peerConnection.connectionState === "connecting") {
          setConnectionQuality("unknown");
          setIsReconnecting(true);
        }

        if (["failed", "disconnected"].includes(peerConnection.connectionState)) {
          if (isMounted.current) {
            setConnectionQuality("poor");
            setVoiceErrorMessage(`WebRTC connection ${peerConnection.connectionState}`);

            // Auto-reconnect logic with exponential backoff
            if (retryCount < 3 && lastEphemeralKeyRef.current) {
              const backoffTime = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff capped at 10 seconds
              console.log(`Connection failed. Attempting reconnect in ${backoffTime}ms (retry ${retryCount + 1}/3)`);

              setIsReconnecting(true);
              setVoiceErrorMessage(`Connection lost. Reconnecting in ${backoffTime/1000}s (attempt ${retryCount + 1}/3)...`);

              setTimeout(() => {
                if (isMounted.current && lastEphemeralKeyRef.current) {
                  console.log(`Attempting reconnection (retry ${retryCount + 1}/3)`);
                  setRetryCount(prev => prev + 1);
                  establishDirectConnection(lastEphemeralKeyRef.current);
                }
              }, backoffTime);
            } else if (retryCount >= 3) {
              // After max retries, give up and show error
              setApiConfigStatus("invalid");
              setVoiceErrorMessage(`Connection failed after ${retryCount} attempts. Please try again.`);
              setIsReconnecting(false);
            }
          }
        } else if (peerConnection.connectionState === "closed") {
          if (isMounted.current) {
            setVoiceErrorMessage(`WebRTC connection closed`);
            setApiConfigStatus("invalid");
            setIsReconnecting(false);
          }
        }
      };

      // ICE connection state monitoring
      peerConnection.oniceconnectionstatechange = () => {
        if (!isMounted.current) return;
        console.log("ICE State:", peerConnection.iceConnectionState);

        // Update connection quality based on ICE state
        if (peerConnection.iceConnectionState === "connected" || peerConnection.iceConnectionState === "completed") {
          setConnectionQuality("good");
        } else if (peerConnection.iceConnectionState === "checking") {
          setConnectionQuality("unknown");
        }

        if (["failed", "disconnected"].includes(peerConnection.iceConnectionState)) {
          if (isMounted.current) {
            setConnectionQuality("poor");
            setVoiceErrorMessage(`ICE connection ${peerConnection.iceConnectionState}`);
          }
        }
      };

      // Simple ICE candidate error handling
      peerConnection.onicecandidateerror = (e) => console.error("ICE Candidate Error:", e);

      const audioElement = document.createElement("audio");
      audioElement.autoplay = true;
      document.body.appendChild(audioElement);

      peerConnection.ontrack = (event) => {
        if (!isMounted.current) return;
        if (audioElement && event.streams[0]) {
          audioElement.srcObject = event.streams[0];
          if (isMounted.current) {
            setIsSpeaking(true);
            setRealtimeDialogue("");
          }
          event.streams[0].onremovetrack = () => {
            if (isMounted.current) setIsSpeaking(false);
          };
        }
      };

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 24000
        }
      });

      mediaStream.getTracks().forEach(track => peerConnection.addTrack(track, mediaStream));

      // Use the data channel name from the legacy version with maxRetransmits from current version
      const dataChannel = peerConnection.createDataChannel("oai-events", {
        ordered: true,
        maxRetransmits: 10
      });

      dataChannel.onopen = () => {
        if (!isMounted.current) return;
        console.log("Data Channel Open");
        if (isMounted.current) {
          setIsListening(true);
          setIsSpeaking(false);
        }

        try {
          // Define instructions based on interaction mode and voice settings
          let instructions = "";

          // Generate voice style instructions based on settings
          const getVoiceStyleInstructions = () => {
            // Tone instructions
            let toneInstructions = "";
            switch(voiceTone) {
              case "professional":
                toneInstructions = "Maintain a professional, authoritative tone throughout the reading.";
                break;
              case "friendly":
                toneInstructions = "Use a warm, friendly, and approachable tone throughout the reading.";
                break;
              case "dramatic":
                toneInstructions = "Read with dramatic flair and theatrical emphasis where appropriate.";
                break;
              default: // neutral
                toneInstructions = "Maintain a neutral, balanced tone throughout the reading.";
            }

            // Emotional intensity instructions
            let emotionInstructions = "";
            switch(emotionalIntensity) {
              case "low":
                emotionInstructions = "Keep emotional expression subtle and restrained.";
                break;
              case "high":
                emotionInstructions = "Express emotions strongly and vividly when called for in the script.";
                break;
              default: // medium
                emotionInstructions = "Express emotions with moderate intensity when appropriate.";
            }

            // Reading pace instructions
            let paceInstructions = "";
            switch(readingPace) {
              case "slow":
                paceInstructions = "Read at a slower, more deliberate pace to allow for clarity and emphasis.";
                break;
              case "fast":
                paceInstructions = "Read at a quicker pace while maintaining clarity.";
                break;
              default: // normal
                paceInstructions = "Read at a natural, conversational pace.";
            }

            return `${toneInstructions} ${emotionInstructions} ${paceInstructions}`;
          };

          // Get voice style instructions
          const voiceStyleInstructions = getVoiceStyleInstructions();
          console.log("Voice style instructions:", voiceStyleInstructions);

          if (interactionMode === "direct") {
            // Direct mode instructions - formal, minimal conversation
            instructions = `You are an AI Script/Rehearsal Assistant that helps actors practice their lines and prepare for performances.
The following are your strict instructions as a rehearsal partner in DIRECT MODE:
Use the following script content to assist the user during rehearsal: ${scriptContent}

1. Your purpose is to serve as a formal, direct rehearsal partner who can read opposite parts with minimal conversation.
2. Always ask which character the user wishes to play and which line they want to start from.
3. Once the user has selected their character and starting point, read ONLY the lines for other characters.
4. Do not speak unless it's your turn to read a line or the user explicitly asks you a question.
5. When reading lines, NEVER prefix with the character's name (e.g., "HAMLET: To be or not to be") and and DO NOT recite the user's lines.
6. Do not offer additional commentary, advice, or conversation unless specifically requested.

Voice style instructions: ${voiceStyleInstructions}

Your interaction style should be:
- Formal and direct
- Focused only on the script reading
- Minimal conversation outside of reading lines
- Precise and accurate with the script text
- Responsive only to direct instructions from the user`;
          } else {
            // Conversational mode instructions - informal, more interactive
            instructions = `You are an AI Script/Rehearsal Assistant that helps actors practice their lines and prepare for performances.
The following are your strict instructions as a rehearsal partner in CONVERSATIONAL MODE:
Use the following script content to assist the user during rehearsal: ${scriptContent}

1. Your purpose is to serve as a reliable rehearsal partner who can read opposite parts and help with line memorization.
2. Unless the user says otherwise omit the characters name before starting the characters lines, unless the character's name is part of the dialogue.
3. Prompt the actor when they forget their lines or make errors in the dialogue, before you state the next line.
4. Read the other character's lines clearly when you practice scenes together and DO NOT recite the user's lines.
5. Adhere strictly to any instructions by the user to assist with the rehearsal.

Voice style instructions: ${voiceStyleInstructions}

Important:
- Adjust your reading pace and emotional tone based on user requests.
- Continue the scene from wherever the user instructs if you need to restart.
- Maintain awareness of the full script structure and character relationships.

Your capabilities include:
- Working with any script that is provided, regardless of format.
- Distinguishing between different characters' dialogue.
- Detecting when the user has skipped lines or made errors and being able to correct the user.
- Being available whenever the user needs to practice.
- Adapting to specific rehearsal style and preferences.

Your interaction style should be:
- Patient and willing to repeat scenes as many times as needed.
- Corrective especially if the user is saying the wrong lines. Let them know they were incorrect and point out exactly what the correct line should be.
- Clear in your delivery but flexible enough to change based on direction.
- Professional yet supportive in your approach and always offering advice.
- Capable of simulating different emotional qualities when reading other parts.`;
          }

          // Get the selected voice based on voiceGender prop
          let selectedVoice = "verse"; // Default voice

          if (voiceGender === 'male') {
            selectedVoice = "echo"; // Male voice
          } else if (voiceGender === 'female') {
            selectedVoice = "alloy"; // Female voice
          }

          console.log(`Using voice: ${selectedVoice} for ${voiceGender} gender`);

          const config = {
            type: "session.update",
            session: {
              instructions: instructions,
              turn_detection: { type: "server_vad" },
              voice: selectedVoice,
              modalities: ["text", "audio"],
            }
          };
          dataChannel.send(JSON.stringify(config));
        } catch (e) {
          console.error("Config send failed:", e);
          if (isMounted.current) setVoiceErrorMessage("Failed config send");
          endDirectConnection();
        }
      };

      dataChannel.onclose = () => {
        if (!isMounted.current) return;
        console.log("Data Channel Closed");
        if (isMounted.current) {
          setIsListening(false);
          setIsSpeaking(false);
        }
      };

      dataChannel.onerror = (e) => {
        if (!isMounted.current) return;
        const err = e as RTCErrorEvent;
        console.error("Data Channel Error:", err);
        if (isMounted.current) {
          setVoiceErrorMessage("Data channel error: " + (err.error?.message || "Unknown"));
          setApiConfigStatus("invalid");
          endDirectConnection();
        }
      };

      // FIXED DATA CHANNEL MESSAGE HANDLER with restored audio transcript delta handling
      dataChannel.onmessage = (event) => {
        if (!isMounted.current) return;
        try {
          const msg = JSON.parse(event.data);
          console.log(`WebRTC Message: ${msg.type}`, msg);

          // Handle text deltas (from text modality)
          if (msg.type === "response.text.delta" && msg.delta) {
            if (isMounted.current) setRealtimeDialogue(prev => prev + msg.delta);
          }
          // Handle audio transcript deltas (from voice transcript)
          else if (msg.type === "response.audio_transcript.delta" && msg.delta) {
            if (isMounted.current) setRealtimeDialogue(prev => prev + msg.delta);
          }
          // Handle user audio transcription completed
          else if (msg.type === "input_audio_transcription.completed" && msg.transcript) {
            console.log("User said:", msg.transcript);
            if (isMounted.current) {
              setDialogueHistory(prev => [...prev, { role: "user", content: msg.transcript }]);
              setRealtimeDialogue("");
              setIsSpeaking(false);
            }
          }
          // Handle assistant response completed
          else if (msg.type === "response.completed") {
            console.log("Response completed.");
            if (isMounted.current) {
              setIsSpeaking(false);
              if (realtimeDialogue.trim()) {
                setDialogueHistory(prev => [...prev, { role: "assistant", content: realtimeDialogue.trim() }]);
                setRealtimeDialogue("");
              }
            }
          }
          // Handle errors
          else if (msg.type === 'error' && msg.message) {
            console.error("Assistant Error:", msg.message);
            if (isMounted.current) {
              setVoiceErrorMessage(`Assistant error: ${msg.message}`);
              setApiConfigStatus("invalid");
              endDirectConnection();
            }
          }
        } catch (e) {
          console.error("Parse error", e);
        }
      };

      const offer = await peerConnection.createOffer({ offerToReceiveAudio: true });
      await peerConnection.setLocalDescription(offer);

      if (peerConnection.iceGatheringState !== "complete") {
        await new Promise<void>(resolve => {
          const tout = setTimeout(resolve, 2000);
          peerConnection.onicegatheringstatechange = () => {
            if (peerConnection.iceGatheringState === "complete") {
              clearTimeout(tout);
              resolve();
            }
          };
          if (peerConnection.iceGatheringState === "complete") {
            clearTimeout(tout);
            resolve();
          }
        });
      }

      // Use the specific model version that works in the legacy implementation
      const model = "gpt-4o-realtime";

      // SDP exchange based on working legacy implementation
      console.log("Sending SDP to OpenAI");
      const sdpResponse = await fetch(`https://api.openai.com/v1/realtime?model=${model}`, {
        method: "POST",
        body: peerConnection.localDescription?.sdp,
        headers: {
          Authorization: `Bearer ${ephemeralKey}`,
          "Content-Type": "application/sdp"
        }
      });

      if (!sdpResponse.ok) {
        const errTxt = await sdpResponse.text();
        let errorMessage = `SDP exchange failed with status: ${sdpResponse.status}`;
        try {
          const errorJson = JSON.parse(errTxt);
          errorMessage = `SDP exchange failed: ${errorJson.error?.message || errorJson.error || errorJson.message || sdpResponse.status}`;
        } catch {
          if (errTxt) errorMessage = `SDP exchange failed: ${errTxt}`;
        }
        throw new Error(errorMessage);
      }

      console.log("Received SDP answer from OpenAI");

      const answerSdp = await sdpResponse.text();
      if (!answerSdp) throw new Error("Empty SDP answer");
      await peerConnection.setRemoteDescription({ type: "answer", sdp: answerSdp });

      directConnectionRef.current = { peerConnection, dataChannel, mediaStream, audioElement, active: true };

      if (isMounted.current) {
        setApiConfigStatus("valid");
        setVoiceErrorMessage("");
        setDetailedErrorInfo(null);
        setIsReconnecting(false);
        setConnectionQuality("good"); // Initial quality assessment

        // Start monitoring connection health
        monitorConnectionHealth();
      }

      console.log("WebRTC Connection Established");
      console.groupEnd();
      return true;
    } catch (error) {
      console.error("Error establishing WebRTC connection:", error);
      const msg = error instanceof Error ? error.message : String(error);

      if (isMounted.current) {
        // Set a user-friendly error message
        setVoiceErrorMessage("Connection failed: " + msg);
        setApiConfigStatus("invalid");

        // Create a detailed technical error report for developers
        const technicalDetails = generateTechnicalErrorReport(error);
        setDetailedErrorInfo(technicalDetails);

        // Add a "Show technical details" button to the UI for developers
        const errorDiv = document.querySelector('.error-message');
        if (errorDiv) {
          const detailsButton = document.createElement('button');
          detailsButton.textContent = 'Show technical details (for developers)';
          detailsButton.className = 'technical-details-btn';
          detailsButton.onclick = () => {
            // Create a modal with the technical details
            const modal = document.createElement('div');
            modal.className = 'technical-details-modal';
            modal.innerHTML = `
              <div class="technical-details-content">
                <h3>WebRTC Technical Details</h3>
                <pre>${technicalDetails}</pre>
                <button class="close-modal-btn">Close</button>
              </div>
            `;
            document.body.appendChild(modal);

            // Add close button functionality
            modal.querySelector('.close-modal-btn')?.addEventListener('click', () => {
              modal.remove();
            });
          };

          // Only add the button if it doesn't already exist
          if (!errorDiv.querySelector('.technical-details-btn')) {
            errorDiv.appendChild(detailsButton);
          }
        }
      }

      await endDirectConnection();
      console.groupEnd();
      await endDirectConnection();
      console.groupEnd();
      return false;
    }
  }, [
    scriptContent,
    endDirectConnection,
    realtimeDialogue,
    setDialogueHistory,
    setRealtimeDialogue,
    voiceGender,
    voiceTone,
    emotionalIntensity,
    readingPace,
    interactionMode
  ]);

  // Toggle Mute - mutes the microphone input, not the audio output
  const toggleMute = useCallback(() => {
    if (!directConnectionRef.current?.mediaStream) return;

    // Get all audio tracks from the media stream
    const audioTracks = directConnectionRef.current.mediaStream.getAudioTracks();

    // Toggle the enabled state of all audio tracks (mute/unmute the microphone)
    const newMuteState = !isMuted;
    audioTracks.forEach(track => {
      track.enabled = !newMuteState;
    });

    // Update the mute state
    setIsMuted(newMuteState);
    console.log("Microphone muted:", newMuteState);
  }, [isMuted]);

  // Listen for end-rehearsal and toggle-mute events from Script tab
  useEffect(() => {
    const handleEndRehearsal = () => {
      console.log("[RealtimeConnection] Received end-rehearsal event from Script tab");
      endDirectConnection();
    };

    const handleToggleMute = () => {
      console.log("[RealtimeConnection] Received toggle-mute event from Script tab");
      toggleMute();
    };

    document.addEventListener('end-rehearsal-from-script', handleEndRehearsal);
    document.addEventListener('toggle-mute', handleToggleMute);

    return () => {
      document.removeEventListener('end-rehearsal-from-script', handleEndRehearsal);
      document.removeEventListener('toggle-mute', handleToggleMute);
    };
  }, [endDirectConnection, toggleMute]);

  // Start Conversation
  const handleStartConversation = useCallback(async () => {
    if (!isMounted.current) return;
    console.group("=== Starting Conversation Session ===");
    setVoiceErrorMessage("");
    setDetailedErrorInfo(null);
    setApiConfigStatus("connecting");
    setDialogueHistory([]);
    setRealtimeDialogue("");

    try {
      const diagError = await runNetworkDiagnostics();
      if (diagError) throw new Error(diagError);

      if (!hasPermission) throw new Error("Microphone permission is required.");
      if (isScriptLoading) throw new Error("Script is still loading.");
      if (!isScriptReady || !scriptContent?.trim()) throw new Error("Script not ready or empty.");

      const response = await fetch("/api/realtime-session", {
        cache: "no-cache",
        headers: {
          "Pragma": "no-cache",
          "Cache-Control": "no-cache"
        }
      });

      const rawText = await debugApiResponse(response.clone());
      if (!response.ok) throw new Error(`API Error ${response.status}: ${rawText || response.statusText}`);

      const data = JSON.parse(rawText!);
      let key = data.ephemeral_key || data.client_secret?.value;

      if (!key && data.original_response) {
        try {
          const orig = typeof data.original_response === 'string' ?
            JSON.parse(data.original_response) : data.original_response;
          key = orig?.client_secret?.value;
        } catch {}
      }

      if (!key) throw new Error("Ephemeral key not found in response.");

      const success = await establishDirectConnection(key);
      if (!success) throw new Error("Failed WebRTC setup.");

      console.log("Conversation Started Successfully.");
    } catch (error) {
      const msg = error instanceof Error ? error.message : String(error);
      console.error("Error Starting Conversation:", msg);

      if (isMounted.current) {
        setVoiceErrorMessage("Failed to start: " + msg);
        setDetailedErrorInfo(msg);
        setApiConfigStatus("invalid");
      }

      await endDirectConnection();
    } finally {
      if (isMounted.current) setDiagnosisInProgress(false);
      console.groupEnd();
    }
  }, [
    runNetworkDiagnostics,
    hasPermission,
    isScriptLoading,
    isScriptReady,
    scriptContent,
    debugApiResponse,
    establishDirectConnection,
    endDirectConnection,
    setDialogueHistory,
    setRealtimeDialogue
  ]);

  // Effect: Request microphone permission
  useEffect(() => {
    const requestMicPermission = async () => {
      if (hasPermission) return;
      console.log("[Effect] Requesting microphone permission...");
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        if (isMounted.current) {
          setHasPermission(true);
          setVoiceErrorMessage("");
        }
      } catch (error) {
        console.error("Microphone permission error:", error);
        if (isMounted.current) {
          setVoiceErrorMessage("Microphone access denied. Please enable in browser settings.");
          setHasPermission(false);
        }
      }
    };
    requestMicPermission();
  }, [hasPermission]);

  // Generate detailed technical error report for developers
  const generateTechnicalErrorReport = useCallback((error: any): string => {
    try {
      const report = [
        "=== WebRTC Technical Error Report ===",
        `Timestamp: ${new Date().toISOString()}`,
        `Error: ${error instanceof Error ? error.message : String(error)}`,
        `Stack: ${error instanceof Error ? error.stack : 'No stack trace available'}`,
        "",
        "=== Browser Information ===",
        `User Agent: ${navigator.userAgent}`,
        `Browser: ${navigator.userAgent.split(' ').slice(-1)[0]}`,
        `WebRTC Support: ${window.RTCPeerConnection ? 'Yes' : 'No'}`,
        `Online: ${navigator.onLine ? 'Yes' : 'No'}`,
        "",
        "=== Connection State ===",
      ];

      // Add connection state if available
      if (directConnectionRef.current?.peerConnection) {
        const pc = directConnectionRef.current.peerConnection;
        report.push(
          `Connection State: ${pc.connectionState}`,
          `ICE Connection State: ${pc.iceConnectionState}`,
          `ICE Gathering State: ${pc.iceGatheringState}`,
          `Signaling State: ${pc.signalingState}`
        );
      } else {
        report.push("No active WebRTC connection");
      }

      // Add network information
      report.push(
        "",
        "=== Network Information ===",
        `Connection Type: ${(navigator as any).connection?.type || 'Unknown'}`,
        `Effective Type: ${(navigator as any).connection?.effectiveType || 'Unknown'}`,
        `Downlink: ${(navigator as any).connection?.downlink || 'Unknown'} Mbps`,
        `RTT: ${(navigator as any).connection?.rtt || 'Unknown'} ms`
      );

      // Add diagnostic results if available
      if (detailedErrorInfo) {
        report.push(
          "",
          "=== Previous Diagnostic Results ===",
          detailedErrorInfo
        );
      }

      return report.join("\n");
    } catch (e) {
      console.error("Error generating technical report:", e);
      return `Failed to generate technical report: ${e instanceof Error ? e.message : String(e)}`;
    }
  }, [detailedErrorInfo]);

  // Simple connection health monitoring function
  const monitorConnectionHealth = useCallback(() => {
    if (!directConnectionRef.current || !directConnectionRef.current.active) return;

    // Basic connection state check
    const checkConnectionState = () => {
      try {
        if (!directConnectionRef.current || !directConnectionRef.current.peerConnection) return;

        const { peerConnection } = directConnectionRef.current;

        // Log connection states
        console.log(`Connection health check - Connection state: ${peerConnection.connectionState}, ICE state: ${peerConnection.iceConnectionState}`);

        // Update connection quality based on connection state
        if (peerConnection.connectionState === "connected" &&
            (peerConnection.iceConnectionState === "connected" || peerConnection.iceConnectionState === "completed")) {
          setConnectionQuality("excellent");
        } else if (peerConnection.connectionState === "connecting" || peerConnection.iceConnectionState === "checking") {
          setConnectionQuality("unknown");
        } else if (["disconnected", "failed"].includes(peerConnection.connectionState) ||
                  ["disconnected", "failed"].includes(peerConnection.iceConnectionState)) {
          setConnectionQuality("poor");
        }
      } catch (error) {
        console.error("Error checking connection state:", error);
      }
    };

    // Run the check every 5 seconds
    if (connectionHealthInterval.current) {
      clearInterval(connectionHealthInterval.current);
    }

    connectionHealthInterval.current = setInterval(() => {
      if (isMounted.current && directConnectionRef.current?.active) {
        checkConnectionState();
      } else if (connectionHealthInterval.current) {
        clearInterval(connectionHealthInterval.current);
        connectionHealthInterval.current = null;
      }
    }, 5000);
  }, []);

  // Effect: Component mount/unmount cleanup
  useEffect(() => {
    console.log("[Effect] Mounting Realtime component");
    isMounted.current = true;

    // Add technical details styles to document
    const styleElement = document.createElement('style');
    styleElement.setAttribute('data-technical-details', 'true');
    styleElement.textContent = technicalDetailsStyles;
    document.head.appendChild(styleElement);

    // Critical: Add a visibility change listener to maintain connection when tab is hidden
    const handleVisibilityChange = () => {
      console.log("Document visibility changed:", document.visibilityState);

      // If tab becomes visible again and we have an active connection, check its health
      if (document.visibilityState === 'visible' && directConnectionRef.current?.active) {
        // Check if connection is still alive after tab becomes visible again
        const { peerConnection } = directConnectionRef.current;
        if (peerConnection && ["disconnected", "failed"].includes(peerConnection.connectionState)) {
          console.log("Connection appears to be broken after tab visibility change, attempting recovery");
          if (lastEphemeralKeyRef.current) {
            setVoiceErrorMessage("Connection lost while tab was inactive. Reconnecting...");
            setIsReconnecting(true);
            // Use the stored key to reconnect - can't use the function directly due to dependency cycle
            const key = lastEphemeralKeyRef.current;
            if (key) {
              console.log("Attempting reconnection after visibility change");
              // We need to call this manually to avoid the circular dependency
              endDirectConnection().then(() => {
                if (isMounted.current && key) {
                  // This is a simplified version of establishDirectConnection
                  // that avoids the circular dependency
                  setApiConfigStatus("connecting");
                  fetch("/api/realtime-session")
                    .then(response => response.json())
                    .then(data => {
                      const ephemeralKey = data.ephemeral_key || data.client_secret?.value;
                      if (ephemeralKey) {
                        lastEphemeralKeyRef.current = ephemeralKey;
                        establishDirectConnection(ephemeralKey);
                      }
                    })
                    .catch(err => {
                      console.error("Reconnection error:", err);
                      setVoiceErrorMessage("Failed to reconnect: " + err.message);
                      setApiConfigStatus("invalid");
                      setIsReconnecting(false);
                    });
                }
              });
            }
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Handle network connectivity changes
    const handleNetworkChange = () => {
      console.log("Network connection changed. Online:", navigator.onLine);

      if (navigator.onLine && directConnectionRef.current?.active) {
        // If we're back online and have an active connection, check its health
        const { peerConnection } = directConnectionRef.current;
        if (peerConnection && ["disconnected", "failed"].includes(peerConnection.connectionState)) {
          console.log("Network reconnected but WebRTC connection is broken, attempting recovery");
          if (lastEphemeralKeyRef.current) {
            setVoiceErrorMessage("Network reconnected. Reestablishing connection...");
            setIsReconnecting(true);
            // Similar approach as above to avoid circular dependency
            endDirectConnection().then(() => {
              if (isMounted.current) {
                setApiConfigStatus("connecting");
                fetch("/api/realtime-session")
                  .then(response => response.json())
                  .then(data => {
                    const ephemeralKey = data.ephemeral_key || data.client_secret?.value;
                    if (ephemeralKey) {
                      lastEphemeralKeyRef.current = ephemeralKey;
                      establishDirectConnection(ephemeralKey);
                    }
                  })
                  .catch(err => {
                    console.error("Network reconnection error:", err);
                    setVoiceErrorMessage("Failed to reconnect: " + err.message);
                    setApiConfigStatus("invalid");
                    setIsReconnecting(false);
                  });
              }
            });
          }
        }
      } else if (!navigator.onLine) {
        setVoiceErrorMessage("Network connection lost. Waiting for reconnection...");
        setConnectionQuality("poor");
      }
    };

    window.addEventListener('online', handleNetworkChange);
    window.addEventListener('offline', handleNetworkChange);

    return () => {
      console.log("[Effect] Unmounting Realtime component, ensuring cleanup.");
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('online', handleNetworkChange);
      window.removeEventListener('offline', handleNetworkChange);

      // Remove technical details styles
      const styleElement = document.querySelector('style[data-technical-details]');
      if (styleElement) {
        styleElement.remove();
      }

      if (connectionHealthInterval.current) {
        clearInterval(connectionHealthInterval.current);
        connectionHealthInterval.current = null;
      }

      isMounted.current = false;
      if (directConnectionRef.current) {
        endDirectConnection();
      }
    };
  }, [endDirectConnection]);

  // Calculate voiceStatus for Rehearsals component
  const voiceStatus = apiConfigStatus === 'valid' && isListening ? "connected" : "disconnected";

  // Render the Rehearsals UI component with all necessary props
  return (
    <Rehearsals
      // Pass WebRTC state props
      apiConfigStatus={apiConfigStatus}
      detailedErrorInfo={detailedErrorInfo}
      isListening={isListening}
      voiceStatus={voiceStatus}
      isMuted={isMuted}
      isSpeaking={isSpeaking}
      hasPermission={hasPermission}
      voiceErrorMessage={voiceErrorMessage}
      diagnosisInProgress={diagnosisInProgress}
      sessionStatus={sessionStatus} // Pass the session status from props
      // Pass connection quality props
      connectionQuality={connectionQuality}
      isReconnecting={isReconnecting}
      // Pass interaction mode props
      interactionMode={interactionMode}
      setInteractionMode={setInteractionMode}
      // Pass handler function props
      toggleMute={toggleMute}
      handleEndConversation={endDirectConnection}
      handleStartConversation={handleStartConversation}
      setVoiceErrorMessage={setVoiceErrorMessage}
      // Pass script-related props
      isScriptLoading={isScriptLoading}
      isScriptReady={isScriptReady}
      scriptName={scriptName}
    />
  );
}

export default Realtime;