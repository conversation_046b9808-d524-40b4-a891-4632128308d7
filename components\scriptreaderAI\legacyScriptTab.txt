"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, FileText, Mic, StopCircle, Eye, EyeOff, Volume2, VolumeX } from "lucide-react";
import { ScriptFormatterTool } from "../../lib/tools/scriptFormatter";
import { markdownFormatterTool } from "../../lib/tools/markdownFormatter";
import EnhancedMarkdownContent from "./EnhancedMarkdownContent";
import ScriptMarkdownContent from "./ScriptMarkdownContent";

interface ScriptTabProps {
  scriptContent: string;
  isScriptLoading: boolean;
  isScriptReady: boolean;
  scriptName: string | null;
  isListening?: boolean;
  isMuted?: boolean;
  handleEndConversation?: () => Promise<void>;
  toggleMute?: () => void;
}

function ScriptTab({
  scriptContent,
  isScriptLoading,
  isScriptReady,
  scriptName,
  isListening = false,
  isMuted = false,
  handleEndConversation = async () => {},
  toggleMute = () => {},
}: ScriptTabProps) {
  const [formattedMarkdown, setFormattedMarkdown] = useState<string>("");
  const [isFormatting, setIsFormatting] = useState<boolean>(false);
  const [formatError, setFormatError] = useState<string | null>(null);
  const [showRawScript, setShowRawScript] = useState<boolean>(false);

  // Use the singleton instance of the ScriptFormatterTool
  const scriptFormatter = new ScriptFormatterTool();

  // Format script content using the scriptFormatter tool
  useEffect(() => {
    if (isScriptReady && scriptContent && !isFormatting) {
      const formatScriptContent = async () => {
        setIsFormatting(true);
        setFormatError(null);

        try {
          //console.log("Starting script formatting with AI...");

          // Format the script using the scriptFormatter tool with more robust error handling
          const formattedScript = await scriptFormatter.formatScript(scriptContent, {
            model: "llama-3.3-70b-versatile",
            provider: "groq",
            includeLineNumbers: false,
            includeNotes: true
          });

          //console.log("Script formatting successful, converting to markdown...");

          // Convert the formatted script to markdown
          const markdown = await markdownFormatterTool.formatScriptToMarkdown(formattedScript);

          // Check if the markdown appears to be JSON-like content
          if (markdown.includes('Content:') && markdown.includes('{') && markdown.includes('}')) {
            //console.log("Detected JSON-like content in markdown, creating custom format...");

            // Create a more readable format for the script
            let customMarkdown = "# " + (formattedScript.metadata?.title || "Script") + "\n\n";

            if (formattedScript.metadata?.author) {
              customMarkdown += "By " + formattedScript.metadata.author + "\n\n";
            }

            if (formattedScript.metadata?.summary) {
              customMarkdown += "## Summary\n\n" + formattedScript.metadata.summary + "\n\n";
            }

            if (formattedScript.metadata?.characters && formattedScript.metadata.characters.length > 0) {
              customMarkdown += "## Characters\n\n";
              formattedScript.metadata.characters.forEach(character => {
                customMarkdown += "- " + character + "\n";
              });
              customMarkdown += "\n";
            }

            customMarkdown += "## Script\n\n";

            // Add script lines
            let currentCharacter = "";
            formattedScript.lines.forEach(line => {
              if (line.character && line.character !== currentCharacter) {
                currentCharacter = line.character;
                customMarkdown += "## " + currentCharacter + "\n\n";
              }

              if (line.text) {
                customMarkdown += line.text + "\n\n";
              }

              if (line.notes) {
                customMarkdown += "*" + line.notes + "*\n\n";
              }
            });

            setFormattedMarkdown(customMarkdown);
          } else {
            setFormattedMarkdown(markdown);
          }

          //console.log("Script formatting complete.");
        } catch (error) {
          console.error("Error formatting script:", error);
          setFormatError(error instanceof Error ? error.message : String(error));

          // Create a simple markdown fallback without showing the error message
          const fallbackMarkdown = `## Original Content

` +
            `${scriptContent.split('\n').slice(0, 50).join('\n')}`;

          // Log the error but don't show it to the user
          console.warn(`Script formatting error: ${error instanceof Error ? error.message : String(error)}`);

          setFormattedMarkdown(fallbackMarkdown);
        } finally {
          setIsFormatting(false);
        }
      };

      formatScriptContent();
    }
  }, [scriptContent, isScriptReady]);

  // Simple format for raw script view
  const rawFormattedContent = scriptContent
    .split("\n")
    .map((line, index) => (
      <p key={index} className={`mb-2 ${line.trim() === "" ? "h-4" : ""}`}>
        {line}
      </p>
    ));

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header with mute and stop buttons */}
      <div className="flex-shrink-0 mb-4 pb-4 border-b border-white/10 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-white flex items-center">
          <FileText className="w-5 h-5 mr-2 text-purple-400" />
          {/* Don't show the filename here as it's already shown in the tab header */}
          Script Content
        </h2>

        {/* Mute and Stop buttons at the far right */}
        {isListening && (
          <div className="flex items-center space-x-2">
            {/* Mute button */}
            <button
              onClick={toggleMute}
              className={`p-2 rounded-full ${
                isMuted ? "bg-red-500/20 text-red-400" : "bg-green-500/20 text-green-400"
              } hover:bg-opacity-30 transition-colors`}
              title={isMuted ? "Unmute Microphone" : "Mute Microphone"}
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </button>

            {/* Stop button */}
            <button
              onClick={() => {
                // Hide settings panel when ending rehearsal
                const hideSettingsEvent = new CustomEvent("hide-settings-panel");
                document.dispatchEvent(hideSettingsEvent);

                // End the conversation
                handleEndConversation();
              }}
              className="p-2 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded-full transition-colors"
              title="End Rehearsal"
            >
              <StopCircle className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>

      {/* Scrollable Script Content */}
      <div className="flex-grow overflow-y-auto custom-script-scrollbar pr-4 -mr-4 relative">
        <div className="prose prose-invert max-w-none">
        {isScriptLoading ? (
          <div className="flex items-center justify-center h-40">
            <Loader className="w-6 h-6 text-purple-400 animate-spin mr-3" />
            <span className="text-gray-300">Loading script content...</span>
          </div>
        ) : !isScriptReady ? (
          <div className="text-center py-10">
            <p className="text-gray-400">
              {scriptName
                ? "Script content is being prepared..."
                : "Please select a script to view its content."}
            </p>
          </div>
        ) : (
          <div>
            {isFormatting ? (
              <div className="flex items-center justify-center h-40">
                <Loader className="w-6 h-6 text-purple-400 animate-spin mr-3" />
                <span className="text-gray-300">Formatting script with CastMate...</span>
              </div>
            ) : formatError ? (
              <div className="text-gray-200 whitespace-pre-wrap font-light leading-relaxed">
                <EnhancedMarkdownContent content={formattedMarkdown} />
              </div>
            ) : showRawScript ? (
              <div className="text-gray-200 whitespace-pre-wrap font-light leading-relaxed">
                {rawFormattedContent}
              </div>
            ) : (
              <ScriptMarkdownContent content={formattedMarkdown} />
            )}

            {/* Toggle raw/formatted view button - positioned relative to the scrollable container */}
            {isScriptReady && !isFormatting && (
              <button
                onClick={() => setShowRawScript(!showRawScript)}
                className="absolute bottom-6 right-10 p-2 bg-gray-800/80 hover:bg-gray-700 rounded-full shadow-lg transition-colors z-10"
                title={showRawScript ? "Show formatted script" : "Show raw script"}
              >
                {showRawScript ? <Eye className="w-5 h-5 text-gray-300" /> : <EyeOff className="w-5 h-5 text-gray-300" />}
              </button>
            )}
          </div>
        )}
        </div>
      </div>

      {/* Global styles for custom scrollbar and script formatting */}
      <style jsx global>{`
        .custom-script-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 10px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(138, 75, 175, 0.5);
          border-radius: 10px;
        }
        .custom-script-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(138, 75, 175, 0.7);
        }
        .custom-script-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: rgba(138, 75, 175, 0.5) rgba(0, 0, 0, 0.1);
        }

        /* Script dialogue styling */
        .script-dialogue {
          margin-bottom: 1.25rem;
          padding-left: 0.5rem;
          border-left: 2px solid rgba(138, 75, 175, 0.3);
        }
        .script-dialogue strong {
          color: #f59e0b; /* amber-500 */
          display: inline-block;
          min-width: 120px;
        }

        /* Line number styling */
        .line-number {
          color: #6b7280; /* gray-500 */
          font-weight: normal;
          margin-right: 0.5rem;
          user-select: none;
          display: inline-block;
          min-width: 2rem;
          text-align: right;
        }

        /* Highlighted text styling */
        .highlighted-text {
          background-color: #fef3c7; /* amber-100 */
          color: #000000;
          padding: 0 4px;
        }
      `}</style>
    </div>
  );
}

export default ScriptTab;
