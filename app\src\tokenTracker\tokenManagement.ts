/**
 * tokenManagement.ts
 *
 * This module manages token usage tracking and analytics for AI interactions.
 * It handles token budgeting across system prompts, chat history, and context,
 * while providing analytics insights about token usage patterns and content processing.
 *
 * Key features:
 * - Token usage tracking and limits
 * - Chat history token analysis
 * - Content processing metrics
 * - Analytics integration
 * - Token budget management
 */

import { AnalyticsMiddleware } from "lib/analytics/AnalyticsMiddleware";
import {
  TOKEN_LIMITS,
  TokenUsage,
  TokenConfig,
  ProcessedMetadata,
  TokenLimitStatus
} from '../types/token-config';

// Move the interface into the types import above instead of declaring it here


/**
 * Manages token usage across different components of the AI interaction system
 */
export class TokenManagement {
  calculateTokens(combinedContent: string): number | PromiseLike<number> {
      throw new Error("Method not implemented.");
  }
  private analytics: AnalyticsMiddleware;
  private tokenUsage: TokenUsage;

  constructor(analytics: AnalyticsMiddleware) {
    this.analytics = analytics;
    this.tokenUsage = {
      contextTokens: 0,
      systemPromptTokens: TOKEN_LIMITS.SYSTEM_PROMPT_TOKENS,
      chatHistoryTokens: 0,
      totalTokens: TOKEN_LIMITS.SYSTEM_PROMPT_TOKENS
    };
  }

  /**
   * Checks the current token limit status
   */
  public checkTokenLimitStatus(): TokenLimitStatus {
    const totalTokens = this.tokenUsage.totalTokens;
    const maxTokens = TOKEN_LIMITS.MAX_INPUT_TOKENS;
    const currentUsagePercent = (totalTokens / maxTokens);

    return {
      isApproachingLimit: currentUsagePercent >= TOKEN_LIMITS.DEFAULT_WARNING_THRESHOLD,
      hasExceededLimit: currentUsagePercent >= 1,
      currentUsagePercent: currentUsagePercent * 100,
      availableTokens: Math.max(0, maxTokens - totalTokens)
    };
  }

  /**
   * Initializes token tracking with chat history information
   */
  public async initializeWithChatHistory(
    chatHistoryTokens: number,
    historyAnalytics: any // Temporarily using 'any' until 'HistoryAnalytics' is defined
  ): Promise<TokenUsage> {
    this.tokenUsage = {
      contextTokens: 0,
      systemPromptTokens: TOKEN_LIMITS.SYSTEM_PROMPT_TOKENS,
      chatHistoryTokens,
      totalTokens: TOKEN_LIMITS.SYSTEM_PROMPT_TOKENS + chatHistoryTokens
    };

    await this.reportChatHistoryMetrics(historyAnalytics);
    await this.reportTokenAnalysis(historyAnalytics, chatHistoryTokens);

    return this.tokenUsage;
  }

  /**
   * Reports chat history metrics to analytics system
   */
  private async reportChatHistoryMetrics(historyAnalytics: any): Promise<void> {
    await this.analytics.interceptChatHistoryMetrics({
      totalMessages: historyAnalytics.totalMessages,
      willBeTruncated: historyAnalytics.willBeTruncated,
      averageMessageLength: historyAnalytics.averageMessageLength,
      originalCount: historyAnalytics.originalCount,
      convertedCount: historyAnalytics.convertedCount,
      truncatedMessages: historyAnalytics.truncatedMessages,
      messagesOverTokenLimit: historyAnalytics.messagesOverTokenLimit
    });
  }

  /**
   * Reports token analysis metrics to analytics system
   * Note: Token metrics collection has been disabled for performance optimization
   */
  private async reportTokenAnalysis(
    historyAnalytics: any, // Changed from HistoryAnalytics to any due to the lint error
    chatHistoryTokens: number
  ): Promise<void> {
    // Token metrics collection disabled to improve performance
    // We still call the interceptTokenAnalysis method, which now just logs a message
    await this.analytics.interceptTokenAnalysis({
      messageCount: historyAnalytics.totalMessages,
      tokenCountPerMessage: historyAnalytics.tokensPerMessage,
      totalTokens: chatHistoryTokens,
      averageTokensPerMessage: historyAnalytics.totalMessages > 0
        ? chatHistoryTokens / historyAnalytics.totalMessages
        : 0
    });
  }

  /**
   * Provides current token configuration limits
   */
  public getTokenConfig(): TokenConfig {
    return {
      maxInputTokens: TOKEN_LIMITS.MAX_INPUT_TOKENS,
      systemPromptTokens: this.tokenUsage.systemPromptTokens,
      chatHistoryTokens: this.tokenUsage.chatHistoryTokens
    };
  }

  /**
   * Updates token usage based on processed content metrics
   */
  public async updateContentProcessing(
    processedMetadata: ProcessedMetadata
  ): Promise<TokenUsage> {
    this.tokenUsage = {
      ...this.tokenUsage,
      contextTokens: processedMetadata.totalTokens,
      totalTokens: processedMetadata.totalTokens +
                   this.tokenUsage.systemPromptTokens +
                   this.tokenUsage.chatHistoryTokens
    };

    await this.analytics.interceptContentProcessing(
      processedMetadata.totalTokens,
      processedMetadata.chunkCount,
      processedMetadata.averageRelevance,
      processedMetadata.namespaceDistribution,
      this.tokenUsage.systemPromptTokens,
      this.tokenUsage.chatHistoryTokens
    );

    return this.tokenUsage;
  }

  /**
   * Returns current token usage state
   */
  public getCurrentUsage(): TokenUsage {
    return { ...this.tokenUsage };
  }

  /**
   * Checks if total token usage exceeds warning threshold
   */
  public isApproachingTokenLimit(): boolean {
    return (this.tokenUsage.totalTokens / TOKEN_LIMITS.MAX_INPUT_TOKENS) >=
           TOKEN_LIMITS.DEFAULT_WARNING_THRESHOLD;
  }

  /**
   * Formats token usage metadata for client consumption
   */
  public getMetadataPayload(
    processedMetadata: Pick<ProcessedMetadata, 'chunkCount' | 'averageRelevance'>
  ): {
    contextTokens: number;
    chatHistoryTokens: number;
    totalChunks: number;
    averageRelevance: string;
    tokenUsagePercentage: number;
  } {
    return {
      contextTokens: this.tokenUsage.contextTokens,
      chatHistoryTokens: this.tokenUsage.chatHistoryTokens,
      totalChunks: processedMetadata.chunkCount,
      averageRelevance: processedMetadata.averageRelevance.toFixed(2),
      tokenUsagePercentage: (this.tokenUsage.totalTokens / TOKEN_LIMITS.MAX_INPUT_TOKENS) * 100
    };
  }
}

// Only export TokenUsage since HistoryAnalytics is now imported
export type { TokenUsage };