<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElevenLabs Configuration Debug</title>
</head>
<body>
    <h1>ElevenLabs Configuration Debug</h1>
    <button onclick="debugElevenLabsConfig()">Debug ElevenLabs Config</button>
    <div id="result"></div>

    <script>
        async function debugElevenLabsConfig() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Debugging ElevenLabs configuration...';
            
            try {
                // Check if ElevenLabs SDK is available
                console.log('Window object keys:', Object.keys(window));
                console.log('ElevenLabs available:', typeof window.ElevenLabs);
                
                // Check audio context
                const audioContextAvailable = typeof AudioContext !== 'undefined' || typeof window.webkitAudioContext !== 'undefined';
                console.log('AudioContext available:', audioContextAvailable);
                
                if (audioContextAvailable) {
                    const AudioCtx = AudioContext || window.webkitAudioContext;
                    const audioContext = new AudioCtx();
                    console.log('AudioContext state:', audioContext.state);
                    console.log('AudioContext sample rate:', audioContext.sampleRate);
                    console.log('AudioContext destination:', audioContext.destination);
                }
                
                // Check media devices
                if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const audioOutputs = devices.filter(device => device.kind === 'audiooutput');
                    const audioInputs = devices.filter(device => device.kind === 'audioinput');
                    
                    console.log('Audio output devices:', audioOutputs);
                    console.log('Audio input devices:', audioInputs);
                    
                    resultDiv.innerHTML = `
                        <h3>Audio Setup Debug Results:</h3>
                        <p><strong>AudioContext:</strong> ${audioContextAvailable ? 'Available' : 'Not Available'}</p>
                        <p><strong>Audio Output Devices:</strong> ${audioOutputs.length}</p>
                        <p><strong>Audio Input Devices:</strong> ${audioInputs.length}</p>
                        <h4>Output Devices:</h4>
                        <ul>
                            ${audioOutputs.map(device => `<li>${device.label || 'Unknown Device'} (${device.deviceId})</li>`).join('')}
                        </ul>
                        <h4>Input Devices:</h4>
                        <ul>
                            ${audioInputs.map(device => `<li>${device.label || 'Unknown Device'} (${device.deviceId})</li>`).join('')}
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">Media devices API not available</p>';
                }
                
                // Test audio playback
                try {
                    const audioElement = new Audio();
                    audioElement.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                    audioElement.volume = 0.1;
                    await audioElement.play();
                    console.log('Test audio playback successful');
                    resultDiv.innerHTML += '<p style="color: green;">✅ Test audio playback successful</p>';
                } catch (audioError) {
                    console.error('Test audio playback failed:', audioError);
                    resultDiv.innerHTML += `<p style="color: red;">❌ Test audio playback failed: ${audioError.message}</p>`;
                }
                
            } catch (error) {
                console.error('Debug failed:', error);
                resultDiv.innerHTML = `<p style="color: red;">Debug failed: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
