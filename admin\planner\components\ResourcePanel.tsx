'use client';

import React, { useState } from 'react';
import { Resource } from '../types';
import { User, X, AlertCircle, Plus } from 'lucide-react';

interface ResourcePanelProps {
  resources: Resource[];
  onAddResource: (resource: Omit<Resource, 'id'>) => void;
  onClose: () => void;
}

const ResourcePanel: React.FC<ResourcePanelProps> = ({
  resources,
  onAddResource,
  onClose
}) => {
  const [isAddingResource, setIsAddingResource] = useState(false);
  const [newResource, setNewResource] = useState<Omit<Resource, 'id'>>({
    name: '',
    role: 'user', // Default to 'user' role
    jobTitle: '', // Added jobTitle field
    avatar: '/avatars/default.png',
    availability: 'Full-time',
    email: '',
    phone: '',
    department: '',
    skills: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [skillInput, setSkillInput] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewResource(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const addSkill = () => {
    if (skillInput.trim()) {
      setNewResource(prev => ({
        ...prev,
        skills: [...(prev.skills || []), skillInput.trim()]
      }));
      setSkillInput('');
    }
  };

  const removeSkill = (skill: string) => {
    setNewResource(prev => ({
      ...prev,
      skills: (prev.skills || []).filter(s => s !== skill)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!newResource.name?.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!newResource.role?.trim()) {
      newErrors.role = 'Role is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onAddResource(newResource);
      setIsAddingResource(false);
      setNewResource({
        name: '',
        role: 'user',
        avatar: '/avatars/default.png',
        availability: 'Full-time',
        email: '',
        phone: '',
        department: '',
        skills: []
      });
    }
  };

  return (
    <div>
      {isAddingResource ? (
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name*
              </label>
              <input
                type="text"
                name="name"
                value={newResource.name}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="Enter resource name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-500 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Role */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Role*
              </label>
              <select
                name="role"
                value={newResource.role}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md bg-white ${errors.role ? 'border-red-500' : 'border-gray-300'}`}
              >
                <option value="user">User</option>
                <option value="admin">Admin</option>
              </select>
              {errors.role && (
                <p className="mt-1 text-sm text-red-500 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.role}
                </p>
              )}
            </div>

            {/* Job Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Job Title
              </label>
              <input
                type="text"
                name="jobTitle"
                value={newResource.jobTitle || ''}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.jobTitle ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="e.g. UX Designer"
              />
              {errors.jobTitle && (
                <p className="mt-1 text-sm text-red-500 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.jobTitle}
                </p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={newResource.email || ''}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter email address"
              />
            </div>

            {/* Phone */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <input
                type="text"
                name="phone"
                value={newResource.phone || ''}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter phone number"
              />
            </div>

            {/* Department */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Department
              </label>
              <input
                type="text"
                name="department"
                value={newResource.department || ''}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="e.g., Marketing, Design"
              />
            </div>

            {/* Availability */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Availability
              </label>
              <select
                name="availability"
                value={newResource.availability}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md bg-white"
              >
                <option value="Full-time">Full-time</option>
                <option value="Part-time">Part-time</option>
                <option value="Contract">Contract</option>
              </select>
            </div>

            {/* Skills */}
            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Skills
              </label>
              <div className="flex">
                <input
                  type="text"
                  value={skillInput}
                  onChange={(e) => setSkillInput(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-l-md"
                  placeholder="Add skills (e.g., Photoshop, React)"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addSkill();
                    }
                  }}
                />
                <button
                  type="button"
                  onClick={addSkill}
                  className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
                >
                  Add
                </button>
              </div>

              {newResource.skills && newResource.skills.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {newResource.skills.map(skill => (
                    <span
                      key={skill}
                      className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center"
                    >
                      {skill}
                      <button
                        type="button"
                        onClick={() => removeSkill(skill)}
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              onClick={() => setIsAddingResource(false)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Add Resource
            </button>
          </div>
        </form>
      ) : (
        <>
          <div className="mb-4 flex justify-between items-center">
            <p className="text-gray-600">
              Manage team members and resources for your project.
            </p>
            <button
              onClick={() => setIsAddingResource(true)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Resource
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {resources.map(resource => (
              <div
                key={resource.id}
                className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center mb-3">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 mr-3">
                    <User className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">{resource.name}</h3>
                    <p className="text-sm text-gray-500">{resource.jobTitle || resource.role}</p>
                  </div>
                </div>

                <div className="text-sm">
                  {resource.email && (
                    <p className="text-gray-600 mb-1">
                      <span className="font-medium">Email:</span> {resource.email}
                    </p>
                  )}

                  {resource.department && (
                    <p className="text-gray-600 mb-1">
                      <span className="font-medium">Department:</span> {resource.department}
                    </p>
                  )}

                  <p className="text-gray-600 mb-1">
                    <span className="font-medium">Availability:</span> {resource.availability}
                  </p>

                  {resource.skills && resource.skills.length > 0 && (
                    <div className="mt-2">
                      <span className="font-medium text-gray-600">Skills:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {resource.skills.map(skill => (
                          <span
                            key={skill}
                            className="bg-gray-100 text-gray-800 px-2 py-0.5 rounded-full text-xs"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      <div className="mt-6 pt-4 border-t border-gray-200 flex justify-end">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ResourcePanel;
