# PMO Strategic Documents Location

## Document Storage Location
**Strategic documents are saved to: `services/pmo/`**

This document serves as a reference for the correct location of PMO strategic documents within the system.

## Updated Components

### 1. createProjectTool.ts
- **Project Metadata**: Updated to reference `services/pmo/` in project descriptions
- **Document Paths**: Requirements and Strategic Plan documents now show full path `services/pmo/{documentId}`
- **Helper Function**: `extractProjectFromStrategicAnalysis` updated with PMO context

### 2. createPMOTasksAgent.ts
- **Document Reading**: Updated to read from `services/pmo/` directory
- **Query Context**: Strategic plan and requirements queries specify `services/pmo/` location
- **Analysis Prompts**: OpenAI o3 prompts include PMO context and document location
- **Logging**: Console logs show full document paths `services/pmo/{documentId}`

## PMO Workflow Integration

### Strategic Document Flow
1. **Strategic Director Agents** generate strategic plans using Requirements Specification as input
2. **Strategic documents** are saved to `services/pmo/` directory
3. **PMO categories** are adopted by agentic teams
4. **Continuous flow** from PMO to domain-specific pipelines with proper source document integration

### Project Creation Flow
1. **Strategic Analysis** outputs trigger automatic project and task creation
2. **createPMOTasksAgent** reads documents from `services/pmo/` using queryDocumentAgent
3. **OpenAI o3** analyzes strategic documents to derive specific tasks
4. **Projects** are created with PMO-aligned naming: `PMO - {category} - {id}`
5. **Tasks** are automatically generated with proper PMO compliance

## Document Types in services/pmo/

### Strategic Planning Documents
- Strategic objectives and goals
- Implementation timelines
- Resource requirements
- Key deliverables and milestones
- Success metrics and KPIs
- Risk assessment and mitigation strategies
- Team responsibilities and assignments
- PMO alignment and compliance requirements

### Requirements Analysis Documents
- Functional requirements
- Non-functional requirements
- Business requirements
- Technical specifications
- Constraints and assumptions
- Acceptance criteria
- PMO compliance requirements

## System Integration

### QueryDocumentsAgent Integration
- **File IDs**: Reference documents in `services/pmo/` directory
- **Query Context**: Includes PMO-specific context and compliance requirements
- **Document Retrieval**: Optimized for PMO document structure and templates

### Project Metadata
- **Requirements Document**: `services/pmo/{requirementsDocumentId}`
- **Strategic Plan Document**: `services/pmo/{strategicPlanDocumentId}`
- **Document Location**: `services/pmo/`
- **Team**: Team name that created the strategic plan
- **PMO ID**: PMO record ID for alignment

### Task Creation
- **PMO Agent**: `createPMOTasksAgent` specifically designed for PMO documents
- **OpenAI o3**: Advanced analysis of strategic documents from `services/pmo/`
- **Task Derivation**: Intelligent extraction of actionable tasks from strategic plans
- **Compliance**: Ensures PMO standards and methodology compliance

## Default Users
- **Project Owner**: Admin User (<EMAIL>)
- **Team Members**: <EMAIL>, Admin User
- **Task Assignment**: Admin User for all derived tasks

## Output Visibility
- **PMO Output Tab**: All agent outputs viewable (similar to Marketing-agent-tests)
- **Strategic Analysis**: Continuous flow from PMO to domain-specific pipelines
- **Project Dashboard**: PMO-aligned project names and metadata
- **Task Management**: Full integration with existing PMO infrastructure

---

**Last Updated**: January 2025
**Document Version**: 1.0
**Location**: services/pmo/PMO_STRATEGIC_DOCUMENTS_LOCATION.md
