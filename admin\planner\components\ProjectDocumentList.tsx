'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { collection, query, orderBy, onSnapshot, addDoc, getDocs, where, limit, serverTimestamp } from 'firebase/firestore';
import { db } from '../../../components/firebase';
import { FileIcon, ImageIcon, Download, Trash2, ExternalLink, MessageSquare } from 'lucide-react';
import { format } from 'date-fns';

interface ProjectDocument {
  id: string;
  name?: string;
  type?: string;
  size?: number;
  downloadUrl?: string;
  createdAt?: {
    toDate: () => Date;
  };
  createdBy?: string;
  isImage?: boolean;
  path?: string;
}

interface ProjectDocumentListProps {
  projectId: string;
  onDeleteDocument?: () => void;
}

export default function ProjectDocumentList({ projectId, onDeleteDocument }: ProjectDocumentListProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [documents, setDocuments] = useState<ProjectDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!projectId || !session?.user?.email) return;

    setLoading(true);

    const documentsRef = collection(db, 'projects', projectId, 'documents');
    const documentsQuery = query(documentsRef, orderBy('createdAt', 'desc'));

    const unsubscribe = onSnapshot(
      documentsQuery,
      (snapshot) => {
        const docs = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        } as ProjectDocument));

        setDocuments(docs);
        setLoading(false);
      },
      (err) => {
        console.error('Error fetching documents:', err);
        setError('Failed to load project documents');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [projectId, session]);

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const getFileIcon = (document: ProjectDocument) => {
    // Check if document is an image (with null safety)
    if (document.isImage === true) {
      return <ImageIcon className="w-5 h-5 text-blue-400" />;
    }

    // Check for document type with null safety
    const fileType = document.type || '';

    if (fileType === 'application/pdf') {
      return <FileIcon className="w-5 h-5 text-red-400" />;
    }

    if (fileType.includes('word')) {
      return <FileIcon className="w-5 h-5 text-blue-600" />;
    }

    // Default icon
    return <FileIcon className="w-5 h-5 text-gray-400" />;
  };

  const handleChatWithDocument = async (document: ProjectDocument) => {
    if (!session?.user?.email) {
      console.log("Can't see the user");
      alert('You must be logged in to perform this action.');
      return;
    }

    try {
      const userEmail = session.user.email;
      const chatsRef = collection(db, 'users', userEmail, 'chats');

      // For project documents, we'll use a specific category
      console.log(`Creating chat for document: ${document.name || 'Unnamed document'} in Project Planner category`);

      // Find or create a chat for this document
      const chatQuery = query(
        chatsRef,
        where('fileDocumentId', '==', document.id),
        orderBy('createdAt', 'desc'),
        limit(1)
      );

      const chatSnapshot = await getDocs(chatQuery);
      let chatId: string;

      if (!chatSnapshot.empty) {
        // Use the last chat for this file
        chatId = chatSnapshot.docs[0].id;
      } else {
        // Create new chat for this file using the enhanced approach
        const chatData = {
          userId: userEmail,
          createdAt: serverTimestamp(),
          lastUpdated: serverTimestamp(),
          firstMessage: "New Chat",
          fileDocumentId: document.id,
          fileNamespace: document.id, // Use document ID as namespace
          projectId: projectId,  // Store the project ID in the chat document
          category: 'Project Planner'
        };

        const newChatRef = await addDoc(chatsRef, chatData);
        chatId = newChatRef.id;
        console.log(`New project document chat created: ${chatId}`);
      }

      router.push(`/chat/${chatId}`);
    } catch (error) {
      console.error('Error handling document chat:', error);
      alert('An error occurred. Please try again.');
    }
  };

  const handleDelete = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/projects/${projectId}/documents/${documentId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete document');
      }

      // Document deleted successfully
      if (onDeleteDocument) {
        onDeleteDocument();
      }

      // Show temporary success message
      setError(null);
    } catch (error: any) {
      console.error('Error deleting document:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-4">Project Documents</h3>
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-4">Project Documents</h3>
        <div className="bg-red-900/50 border border-red-700 text-red-100 px-4 py-3 rounded">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-white mb-4">Project Documents</h3>

      {documents.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <p>No documents uploaded yet</p>
        </div>
      ) : (
        <div className="space-y-2">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                {getFileIcon(doc)}
                <div>
                  <p className="text-white font-medium">{doc.name || 'Unnamed document'}</p>
                  <div className="flex space-x-4 text-xs text-gray-400">
                    <span>{formatFileSize(doc.size || 0)}</span>
                    <span>
                      {doc.createdAt?.toDate ?
                        format(new Date(doc.createdAt.toDate()), 'MMM d, yyyy') :
                        'Unknown date'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                {doc.downloadUrl ? (
                  <>
                    <a
                      href={doc.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1.5 bg-white text-gray-900 hover:text-white hover:bg-gray-600 rounded-full"
                      title="View"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                    <a
                      href={doc.downloadUrl}
                      download={doc.name || 'document'}
                      className="p-1.5 bg-green-500 text-gray-900 hover:text-white hover:bg-gray-600 rounded-full"
                      title="Download"
                    >
                      <Download className="w-4 h-4" />
                    </a>
                  </>
                ) : (
                  <span className="p-1.5 text-gray-500" title="Download URL not available">
                    <ExternalLink className="w-4 h-4" />
                  </span>
                )}
                <button
                  onClick={() => handleChatWithDocument(doc)}
                  className="p-1.5 bg-amber-500 text-gray-900 hover:text-white hover:bg-gray-600 rounded-full"
                  title="Chat with document"
                >
                  <MessageSquare className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(doc.id)}
                  className="p-1.5 hover:bg-gray-600 hover:text-red-400 text-white bg-red-500 rounded-full"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
