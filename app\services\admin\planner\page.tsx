'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  PlusCircle,
  Calendar,
  Users,
  Clock,
  Edit,
  Trash2,
  Search,
  FolderPlus,
  AlertCircle,
  RefreshCw,
  LayoutGrid,
  BarChart,
  ListChecks
} from 'lucide-react';
import { usePlanner } from '../../../context/PlannerContext';
import { Project, Task } from '../../../../admin/planner/types';
import ProjectForm from '../../../../admin/planner/components/ProjectForm';
import { getCategoryColorClass } from '../../../utils/categoryColors';
import FirebaseDiagnostic from '../../../components/FirebaseDiagnostic';
import TabsContextDebugger from '../../../components/TabsContextDebugger';
import TaskChartTabs from './components/TaskChartTabs';

export default function ProjectList() {
  const { projects, users, tasks, loading, error, refreshData, createProject, removeProject } = usePlanner();
  const [isProjectFormOpen, setIsProjectFormOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'projects' | 'taskSummary'>('projects');
  const router = useRouter();

  // Debug logging
  console.log('ProjectList: Loading state:', loading);
  console.log('ProjectList: Projects data:', projects);
  console.log('ProjectList: Users data:', users);

  // Filter projects based on search query
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get user name by ID
  const getUserName = (userId: string): string => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : 'Unknown User';
  };

  // Format date for display
  const formatDate = (date: Date | string): string => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Handle creating a new project
  const handleCreateProject = async (projectData: Omit<Project, 'id'>) => {
    try {
      await createProject(projectData);
      setIsProjectFormOpen(false);
    } catch (error) {
      console.error('Error creating project:', error);
      alert('Failed to create project. Please try again.');
    }
  };

  // Handle deleting a project
  const handleDeleteProject = async (projectId: string) => {
    if (confirm('Are you sure you want to delete this project? This will also delete all tasks associated with this project.')) {
      try {
        await removeProject(projectId);
      } catch (error) {
        console.error('Error deleting project:', error);
        alert('Failed to delete project. Please try again.');
      }
    }
  };

  // Navigate to project details
  const navigateToProject = (projectId: string) => {
    router.push(`/services/admin/planner/${projectId}`);
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Active':
        return 'bg-green-900/50 text-green-300';
      case 'On Hold':
        return 'bg-yellow-900/50 text-yellow-300';
      case 'Completed':
        return 'bg-blue-900/50 text-blue-300';
      case 'Cancelled':
        return 'bg-red-900/50 text-red-300';
      default:
        return 'bg-gray-700 text-gray-300';
    }
  };

  // Helper function to normalize task status
  const normalizeTaskStatus = (status: string): string => {
    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus === 'not started') return 'Not Started';
    if (normalizedStatus === 'in progress') return 'In Progress';
    if (normalizedStatus === 'reviewed') return 'Reviewed';
    if (normalizedStatus === 'complete' || normalizedStatus === 'completed') return 'Complete';
    return status; // Default fallback
  };

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
    } catch (err) {
      console.error('Error refreshing data:', err);
    } finally {
      setIsRefreshing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <div className="container mx-auto px-4 py-6">
          {/* Tabs Context Debugger */}
          <TabsContextDebugger />

          <div className="flex items-center justify-center mt-20">
            <div className="text-xl text-gray-300">Loading projects...</div>
          </div>
        </div>
      </div>
    );
  }

  return (

    <div className="min-h-screen bg-gray-900 text-gray-100 overflow-x-hidden">
      <div className="container mx-auto px-4 py-6 overflow-x-hidden max-w-full">
        {/* Header */}
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row justify-between items-start md:items-center mb-6">
          <div className="w-full flex justify-end md:justify-start">
            <div className="text-right md:text-left pl-8 md:pl-0 pr-2">
              <h1 className="text-2xl font-bold text-white">Projects</h1>
              <p className="text-gray-400 mt-1">Manage your projects and tasks</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 w-full sm:w-auto">
            {/* Tabs */}
            <div className="flex border border-gray-700 rounded-md overflow-hidden w-full sm:w-auto shadow-md">
              <button
                className={`px-3 py-1.5 text-xs md:text-sm font-medium flex-1 sm:flex-auto ${activeTab === 'projects' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'} flex items-center justify-center transition-colors min-w-[90px] md:min-w-[110px] whitespace-nowrap`}
                onClick={() => setActiveTab('projects')}
              >
                <LayoutGrid className="w-3.5 h-3.5 md:w-4 md:h-4 mr-1.5 hidden md:inline" />
                Projects
              </button>
              <button
                className={`px-3 py-1.5 text-xs md:text-sm font-medium flex-1 sm:flex-auto ${activeTab === 'taskSummary' ? 'bg-purple-600 text-white' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'} flex items-center justify-center transition-colors min-w-[140px] md:min-w-[160px] whitespace-nowrap`}
                onClick={() => setActiveTab('taskSummary')}
              >
                <BarChart className="w-3.5 h-3.5 md:w-4 md:h-4 mr-1.5 hidden md:inline" />
                Task Summary
              </button>
            </div>

            {activeTab === 'projects' && (
              <button
                onClick={() => setIsProjectFormOpen(true)}
                className="flex items-center justify-center px-3 py-1.5 md:px-4 md:py-2 bg-purple-600 text-white text-xs md:text-sm rounded-md hover:bg-purple-700 transition-colors w-full sm:w-auto shadow-md min-w-[140px] md:min-w-[160px] whitespace-nowrap"
              >
                <PlusCircle className="w-4 h-4 md:w-5 md:h-5 mr-1.5" />
                Create Project
              </button>
            )}
          </div>
        </div>

        {activeTab === 'projects' && (
          <>
            {/* Search bar */}
            <div className="bg-gray-800 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search projects..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-700 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 text-sm sm:text-base"
                />
              </div>
            </div>
            {/* Projects list */}
            {!error && filteredProjects.length === 0 ? (
              <div className="bg-gray-800 rounded-lg shadow-md p-8 text-center">
                <FolderPlus className="w-16 h-16 mx-auto text-gray-600 mb-4" />
                <h2 className="text-xl font-medium text-gray-300 mb-2">No projects found</h2>
                <p className="text-gray-400 mb-6">
                  {searchQuery
                    ? 'No projects match your search criteria. Try a different search term.'
                    : 'Get started by creating your first project.'}
                </p>
                {!searchQuery && (
                  <button
                    onClick={() => setIsProjectFormOpen(true)}
                    className="inline-flex items-center px-3 py-1.5 md:px-4 md:py-2 bg-purple-600 text-white text-xs md:text-sm rounded-md hover:bg-purple-700 transition-colors shadow-md min-w-[140px] md:min-w-[160px] whitespace-nowrap"
                  >
                    <PlusCircle className="w-4 h-4 md:w-5 md:h-5 mr-1.5" />
                    Create Project
                  </button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 overflow-x-hidden">
                {filteredProjects.map(project => (
                  <div
                    key={project.id}
                    className="bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow w-full max-w-full"
                  >
                    <div className="p-3 sm:p-6">
                      <div className="flex flex-col sm:flex-row justify-between items-start mb-3 gap-2">
                        <h2
                          className="text-lg font-medium text-white cursor-pointer hover:text-purple-300 break-words w-full sm:w-auto"
                          onClick={() => navigateToProject(project.id)}
                        >
                          {project.name}
                        </h2>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </div>

                      <p className="text-gray-400 text-sm mb-4 line-clamp-2 break-words">
                        {project.description || 'No description provided.'}
                      </p>

                      <div className="space-y-2 text-sm overflow-hidden">
                        <div className="flex items-center text-gray-400">
                          <Calendar className="w-4 h-4 mr-2 flex-shrink-0" />
                          <span className="truncate">{formatDate(project.startDate)} - {formatDate(project.endDate)}</span>
                        </div>

                        <div className="flex items-center text-gray-400">
                          <Users className="w-4 h-4 mr-2 flex-shrink-0" />
                          <span className="truncate">Owner: {getUserName(project.owner)}</span>
                        </div>

                        <div className="flex items-center text-gray-400">
                          <Clock className="w-4 h-4 mr-2 flex-shrink-0" />
                          <span className="truncate">Created: {formatDate(project.createdAt || new Date())}</span>
                        </div>
                      </div>

                      {project.categories && project.categories.length > 0 && (
                        <div className="mt-4 flex flex-wrap gap-2">
                          {project.categories.map((category) => {
                            // Get consistent color for this category
                            const colorClass = getCategoryColorClass(category);

                            return (
                              <span
                                key={category}
                                className={`${colorClass} px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs font-medium shadow-sm truncate max-w-[120px] sm:max-w-none`}
                              >
                                {category}
                              </span>
                            );
                          })}
                        </div>
                      )}
                    </div>

                    <div className="bg-gray-700 px-3 sm:px-6 py-3 flex flex-wrap justify-between gap-2">
                      <button
                        onClick={() => navigateToProject(project.id)}
                        className="text-gray-300 hover:text-white transition-colors flex items-center"
                      >
                        <ListChecks className="w-4 h-4 mr-1" />
                        View Tasks
                      </button>

                      <div className="flex space-x-3">
                        <button
                          onClick={() => {
                            // Edit project functionality
                            // For now, just navigate to the project
                            navigateToProject(project.id);
                          }}
                          className="text-gray-300 hover:text-blue-400 transition-colors"
                          title="Edit project"
                        >
                          <Edit className="w-5 h-5" />
                        </button>

                        <button
                          onClick={() => handleDeleteProject(project.id)}
                          className="text-gray-300 hover:text-red-400 transition-colors"
                          title="Delete project"
                        >
                          <Trash2 className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {activeTab === 'taskSummary' && (
          <div className="bg-gray-800 rounded-lg p-6">
            <div className="mb-6">
              <h2 className="text-xl font-bold text-white mb-2 text-right md:text-left pl-8 md:pl-0 pr-2">Task Statistics</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-700 p-4 rounded-lg">
                  <p className="text-gray-400 text-sm">Total Tasks</p>
                  <p className="text-white text-2xl font-bold">{tasks.length}</p>
                </div>
                <div className="bg-gray-700 p-4 rounded-lg">
                  <p className="text-gray-400 text-sm">Not Started</p>
                  <p className="text-white text-2xl font-bold">
                    {tasks.filter(task => normalizeTaskStatus(task.status) === 'Not Started').length}
                  </p>
                </div>
                <div className="bg-gray-700 p-4 rounded-lg">
                  <p className="text-gray-400 text-sm">In Progress</p>
                  <p className="text-white text-2xl font-bold">
                    {tasks.filter(task => normalizeTaskStatus(task.status) === 'In Progress').length}
                  </p>
                </div>
                <div className="bg-gray-700 p-4 rounded-lg">
                  <p className="text-gray-400 text-sm">Completed</p>
                  <p className="text-white text-2xl font-bold">
                    {tasks.filter(task => normalizeTaskStatus(task.status) === 'Complete').length}
                  </p>
                </div>
              </div>
            </div>

            <TaskChartTabs />
          </div>
        )}
        {/* Tabs Context Debugger */}
        <TabsContextDebugger />


        {/* Firebase Diagnostic - Only shown in development or when there's an error */}
        {(process.env.NODE_ENV === 'development' || error) && (
          <FirebaseDiagnostic />
        )}

        {/* Error message */}
        {error && (
          <div className="bg-red-900/30 border border-red-700 rounded-lg p-4 mb-6 flex items-start">
            <AlertCircle className="h-5 w-5 text-red-400 mr-3 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h3 className="text-red-400 font-medium">Error loading projects</h3>
              <p className="text-red-300 text-sm mt-1">{error}</p>
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="mt-2 inline-flex items-center px-3 py-1.5 bg-red-800 hover:bg-red-700 text-white text-sm rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                {isRefreshing ? 'Refreshing...' : 'Retry'}
              </button>
            </div>
          </div>
        )}


      </div>

      {/* Project form modal */}
      {isProjectFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-1 sm:p-4 overflow-y-auto overflow-x-hidden">
          <div className="bg-gray-800 rounded-lg shadow-xl p-2 sm:p-4 w-full max-w-2xl max-h-[90vh] overflow-y-auto overflow-x-hidden my-2 mx-1 sm:mx-4">
            <h2 className="text-xl font-bold mb-3 text-white sticky top-0 bg-gray-800 py-1 z-10">Create New Project</h2>
            <ProjectForm
              users={users}
              onSubmit={handleCreateProject}
              onCancel={() => setIsProjectFormOpen(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}