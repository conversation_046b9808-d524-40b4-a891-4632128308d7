import { NextRequest, NextResponse } from "next/server";
import { PdfGeneratorTool, PdfContent } from "../../../lib/tools/pdf-generator";
import { InvestigationResult, JournalistResponse } from "../../../lib/agents/investigative/InvestigativeResearchAgent";

// Initialize the PDF generator tool
const pdfGeneratorTool = new PdfGeneratorTool();

interface InvestigativeReportPdfRequest {
  investigationResult: InvestigationResult;
  includeIndividualReports?: boolean;
  includeAssessment?: boolean;
  includeConsolidated?: boolean;
}

/**
 * API route for generating PDF reports from investigative research results
 */
export async function POST(request: NextRequest) {
  try {
    const {
      investigationResult,
      includeIndividualReports = true,
      includeAssessment = true,
      includeConsolidated = true
    }: InvestigativeReportPdfRequest = await request.json();

    // Validate required parameters
    if (!investigationResult) {
      return NextResponse.json({
        success: false,
        error: "Investigation result is required"
      }, { status: 400 });
    }

    const result = investigationResult;

    // Create content sections for the PDF
    const pdfContents: PdfContent[] = [];

    // Add executive summary section
    pdfContents.push({
      title: "Executive Summary",
      content: `
# Investigative Research Report

**Investigation ID:** ${result.investigationId}
**Investigation Type:** ${result.investigationType.toUpperCase()}
**Conducted:** ${result.createdAt.toLocaleDateString()}
**Investigator:** Investigative Research Agent System

## Original Research Question
${result.originalPrompt}

## Key Findings Summary
${result.keyFindings.map((finding, index) => `${index + 1}. ${finding}`).join('\n')}

## Investigation Overview
- **Number of Journalists:** ${result.journalistResponses.length}
- **Investigation Methodology:** Multi-perspective investigative analysis
- **Models Used:** ${result.criteriaModel}, ${result.optimizationModel}, ${result.assessmentModel}${result.consolidationModel ? `, ${result.consolidationModel}` : ''}
- **Consolidation:** ${result.consolidatedReport ? 'Yes' : 'No'}

## Recommendations
${result.recommendations.map((rec, index) => `${index + 1}. ${rec}`).join('\n')}
`
    });

    // Add investigation criteria and methodology
    pdfContents.push({
      title: "Investigation Methodology",
      content: `
## Investigation Criteria
*Generated by ${result.criteriaModel} (${result.criteriaProvider})*

${result.criteria}

## Optimized Research Question
*Optimized by ${result.optimizationModel} (${result.optimizationProvider})*

${result.optimizedPrompt}

## Investigation Approach
This investigation employed a multi-perspective approach using specialized journalist personas, each bringing unique expertise and investigative methodologies to ensure comprehensive coverage of the research question.
`
    });

    // Add individual journalist reports if requested
    if (includeIndividualReports && result.journalistResponses.length > 0) {
      const validResponses = result.journalistResponses.filter(r => r.response !== null);
      
      validResponses.forEach((response, index) => {
        pdfContents.push({
          title: `Journalist Report ${index + 1}: ${response.journalistName}`,
          content: `
## ${response.journalistName}
**Investigation Angle:** ${response.investigationAngle}
**Model Used:** ${response.model} via ${response.provider}

### Key Findings
${response.keyFindings.map(finding => `• ${finding}`).join('\n')}

### Full Investigation Report
${response.response}

---
`
        });
      });

      // Add error reports if any
      const errorResponses = result.journalistResponses.filter(r => r.error !== null);
      if (errorResponses.length > 0) {
        pdfContents.push({
          title: "Investigation Errors",
          content: `
## Failed Investigations

The following journalist investigations encountered errors:

${errorResponses.map(response => `
**${response.journalistName}**
- Model: ${response.model} via ${response.provider}
- Error: ${response.error}
`).join('\n')}
`
        });
      }
    }

    // Add comparative assessment if requested
    if (includeAssessment) {
      pdfContents.push({
        title: "Comparative Assessment",
        content: `
## Editorial Assessment
*Assessed by ${result.assessmentModel} (${result.assessmentProvider})*

${result.assessment}
`
      });
    }

    // Add consolidated report if available and requested
    if (includeConsolidated && result.consolidatedReport) {
      pdfContents.push({
        title: "Consolidated Investigation Report",
        content: `
## Consolidated Report
*Consolidated by ${result.consolidationModel} (${result.consolidationProvider})*

${result.consolidatedReport}
`
      });
    }

    // Add sources and references
    if (result.sources && result.sources.length > 0) {
      pdfContents.push({
        title: "Sources and References",
        content: `
## Sources Referenced

${result.sources.map((source, index) => `${index + 1}. ${source}`).join('\n')}

---

*Note: Sources listed above were identified and referenced by the investigative journalists during their research. Verification of sources is recommended for any actionable decisions based on this investigation.*
`
      });
    }

    // Add appendix with technical details
    pdfContents.push({
      title: "Technical Appendix",
      content: `
## Investigation Configuration

**Investigation ID:** ${result.investigationId}
**User ID:** ${result.userId}
${result.pmoId ? `**PMO ID:** ${result.pmoId}` : ''}

### Model Configuration
- **Criteria Generation:** ${result.criteriaModel} (${result.criteriaProvider})
- **Prompt Optimization:** ${result.optimizationModel} (${result.optimizationProvider})
- **Assessment:** ${result.assessmentModel} (${result.assessmentProvider})
${result.consolidationModel ? `- **Consolidation:** ${result.consolidationModel} (${result.consolidationProvider})` : ''}

### Journalist Configuration
${result.journalistResponses.map(response => `
- **${response.journalistName}:** ${response.model} (${response.provider})
  - Investigation Angle: ${response.investigationAngle}
  - Status: ${response.error ? 'Failed' : 'Completed'}
`).join('')}

### Investigation Statistics
- **Total Journalists:** ${result.journalistResponses.length}
- **Successful Investigations:** ${result.journalistResponses.filter(r => r.response !== null).length}
- **Failed Investigations:** ${result.journalistResponses.filter(r => r.error !== null).length}
- **Key Findings Identified:** ${result.keyFindings.length}
- **Recommendations Generated:** ${result.recommendations.length}
- **Sources Referenced:** ${result.sources.length}

---

*This report was generated by the Investigative Research Agent System, part of the PMO (Project Management Office) ecosystem. The system employs multiple specialized AI models to simulate diverse journalistic perspectives and provide comprehensive investigative analysis.*
`
    });

    // Generate the PDF
    const pdfResult = await pdfGeneratorTool.generatePdf(pdfContents, {
      agentName: 'Investigative Research Agent',
      agentId: 'investigative-research-agent',
      documentType: 'Investigation Report',
      author: 'Investigative Research Agent System',
      department: 'PMO Research Division',
      status: 'Completed',
      priority: 'High',
      version: '1.0',
      reportId: result.investigationId,
      reportType: 'Investigative Research',
      tags: [result.investigationType, 'investigation', 'multi-perspective', 'research'],
      relatedDocuments: result.pmoId ? [result.pmoId] : undefined,
      saveToByteStore: false // Ensure we get a Buffer, not SavePdfToByteStoreResult
    });

    // Ensure we have a Buffer for the response
    if (!Buffer.isBuffer(pdfResult)) {
      throw new Error('PDF generation did not return a Buffer');
    }

    // Return the PDF as a blob
    return new NextResponse(pdfResult, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="investigative-report-${result.investigationId}-${Date.now()}.pdf"`
      }
    });

  } catch (error: any) {
    console.error("Error generating investigative research PDF:", error);
    return NextResponse.json({
      success: false,
      error: error.message || "Failed to generate PDF report"
    }, { status: 500 });
  }
}
