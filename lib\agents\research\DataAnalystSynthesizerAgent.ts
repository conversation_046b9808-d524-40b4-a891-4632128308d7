// research/DataAnalystSynthesizerAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { RetrievalResult, AnalyzedSource, SynthesizedFindings } from './ResearchInterfaces';

export class DataAnalystSynthesizerAgent extends ResearchAgent {
    constructor(
        id: string = 'data-analyst',
        name: string = 'Data Analyst & Synthesizer',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'gpt-4o'
    ) {
        const role = 'Data Analysis and Synthesis Specialist';
        const description = `I analyze raw information provided by retrievers, evaluate source credibility, extract key findings, identify themes and patterns, synthesize this into a coherent understanding, generate research visualizations, and create strategic task breakdowns.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Analyze retrieved information and synthesize findings
     * @param results - Array of retrieval results
     * @param subTaskId - The specific sub-task this analysis pertains to
     * @param parentTaskId - The overall research task ID
     * @param researchQuestion - The guiding question for this analysis
     * @returns Synthesized findings
     */
    async analyzeAndSynthesize(
        results: RetrievalResult[],
        subTaskId: string,
        parentTaskId: string,
        researchQuestion: string
    ): Promise<SynthesizedFindings> {
        console.log(`[${this.name}] Analyzing ${results.length} results for sub-task ${subTaskId}: ${researchQuestion}`);

        if (results.length === 0) {
            console.warn(`[${this.name}] No results provided for analysis.`);
            return {
                synthesisId: `syn-${subTaskId}-${Date.now()}`,
                subTaskId,
                parentTaskId,
                summary: "No information retrieved for this topic.",
                keyThemes: [],
                supportingEvidence: {},
                analyzedSources: [],
                confidenceLevel: 'low',
            };
        }

        // Prepare context for LLM analysis
        const sourcesText = results.map((r, index) =>
            `Source ${index + 1} (URL: ${r.sourceUrl}, Reliability: ${r.initialReliabilityScore?.toFixed(2) ?? 'N/A'}):\n` +
            `Title: ${r.title}\n` +
            `Content Snippet:\n${r.formattedContent || r.rawContent.substring(0, 1500)}...\n---`
        ).join('\n\n');

        const prompt = `
          Research Question: ${researchQuestion}

          Analyze the following ${results.length} sources to answer the research question.
          For each source, assess its relevance and credibility more deeply based on the content provided.
          Extract key findings relevant to the question.
          Identify the main themes emerging across the sources.
          Note any significant agreements, contradictions, or gaps.
          Provide a concise synthesized summary answering the research question.
          Finally, assess the overall confidence level (low, medium, high) in the synthesized findings based on the source quality and consistency.

          Sources:
          ${sourcesText}

          Format your response as a JSON object with keys:
          - "analyzedSources": An array of objects, each with "sourceUrl", "keyFindings" (array of strings), "relevanceScore" (0-1), "credibilityScore" (0-1), "biasAssessment" (string).
          - "keyThemes": An array of strings.
          - "summary": A synthesized paragraph answering the research question.
          - "contradictions": (Optional) An array of strings describing contradictions.
          - "gaps": (Optional) An array of strings describing information gaps.
          - "confidenceLevel": 'low', 'medium', or 'high'.
        `;

        const responseJson = await this.processRequest(prompt);
        let synthesis: SynthesizedFindings;

        try {
            const parsed = JSON.parse(responseJson);

            // Map parsed sources to AnalyzedSource structure
             const analyzedSources: AnalyzedSource[] = (parsed.analyzedSources || []).map((ps: any, index: number) => ({
                analysisId: `ana-${results[index]?.resultId || index}-${Date.now()}`,
                retrievalResultId: results[index]?.resultId || `unknown-${index}`,
                subTaskId: subTaskId,
                sourceUrl: ps.sourceUrl || results[index]?.sourceUrl || 'Unknown',
                keyFindings: ps.keyFindings || [],
                relevanceScore: ps.relevanceScore || 0.5,
                credibilityScore: ps.credibilityScore || 0.5,
                biasAssessment: ps.biasAssessment || 'Not assessed',
            }));


            synthesis = {
                synthesisId: `syn-${subTaskId}-${Date.now()}`,
                subTaskId,
                parentTaskId,
                summary: parsed.summary || "Analysis failed to produce a summary.",
                keyThemes: parsed.keyThemes || [],
                supportingEvidence: {}, // Needs population based on themes and sources - complex mapping
                contradictions: parsed.contradictions || [],
                gaps: parsed.gaps || [],
                confidenceLevel: parsed.confidenceLevel || 'medium',
                analyzedSources: analyzedSources,
            };
             console.log(`[${this.name}] Analysis complete for ${subTaskId}. Confidence: ${synthesis.confidenceLevel}`);

        } catch (error) {
            console.error(`[${this.name}] Failed to parse analysis response for ${subTaskId}:`, error);
            synthesis = { // Provide fallback structure
                synthesisId: `syn-${subTaskId}-${Date.now()}-error`,
                subTaskId, parentTaskId,
                summary: "Error during analysis.", keyThemes: [], supportingEvidence: {}, analyzedSources: [], confidenceLevel: 'low',
            };
        }

        return synthesis;
    }

    /**
     * Generate research visualization charts (DELEGATED from ResearchLeadAgent)
     */
    async generateCharts(taskData: {
        instruction: string;
        analysisContent: string;
        title: string;
        chartTypes: string[];
        requirements: string[];
    }): Promise<any> {
        console.log(`[${this.name}] Generating charts for: ${taskData.title}`);

        try {
            const chartPrompt = `
            You are a Data Analyst creating research visualization charts.

            CHART GENERATION REQUEST:
            Title: ${taskData.title}
            Analysis Content: ${taskData.analysisContent}
            Preferred Chart Types: ${taskData.chartTypes.join(', ')}
            Requirements: ${taskData.requirements.join(', ')}

            Based on the analysis content, create 2-3 chart specifications that would effectively visualize the key findings and insights.

            For each chart, provide:
            1. Chart type (bar, line, pie, scatter, etc.)
            2. Title and subtitle
            3. Data structure and format
            4. Key insights it would reveal
            5. Recommended colors and styling

            Return JSON array of chart specifications:
            [
              {
                "chartType": "bar|line|pie|scatter|area",
                "title": "Chart title",
                "subtitle": "Chart subtitle",
                "dataStructure": "Description of data format needed",
                "insights": "Key insights this chart reveals",
                "styling": {
                  "colors": ["#color1", "#color2"],
                  "theme": "professional|modern|minimal"
                }
              }
            ]
            `;

            const response = await this.processRequest(chartPrompt);
            const chartSpecs = JSON.parse(response);

            console.log(`[${this.name}] Generated ${chartSpecs.length} chart specifications`);
            return {
                success: true,
                charts: chartSpecs,
                generatedBy: this.name,
                generatedAt: new Date()
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error generating charts:`, error);
            return {
                success: false,
                error: error.message,
                fallbackCharts: [{
                    chartType: 'bar',
                    title: taskData.title,
                    subtitle: 'Chart generation failed',
                    dataStructure: 'Basic data structure needed',
                    insights: 'Chart generation requires implementation'
                }]
            };
        }
    }

    /**
     * Create strategic task breakdown (DELEGATED from ResearchLeadAgent)
     */
    async createStrategicTasks(taskData: {
        instruction: string;
        context: any;
        taskCategories: string[];
        teamAssignments: string[];
        requirements: string[];
        taskCount: string;
        researchFocus: boolean;
    }): Promise<any> {
        console.log(`[${this.name}] Creating strategic task breakdown`);

        try {
            const strategicPrompt = `
            You are a Data Analyst creating a strategic task breakdown with research focus.

            CONTEXT:
            User Request: ${taskData.context.userRequest || 'Strategic research planning required'}
            Analysis Content: ${taskData.context.analysisContent || 'Requires comprehensive research analysis'}
            Strategic Objectives: ${taskData.context.strategicObjectives?.join(', ') || 'Evidence-based strategic objectives'}
            Research Scope: ${taskData.context.researchScope || 'Comprehensive multi-dimensional analysis'}

            TASK CATEGORIES: ${taskData.taskCategories.join(', ')}
            AVAILABLE TEAMS: ${taskData.teamAssignments.join(', ')}
            REQUIREMENTS: ${taskData.requirements.join(', ')}

            Create ${taskData.taskCount} strategic tasks with strong research focus.

            Return JSON array of tasks:
            [
              {
                "title": "Clear, research-focused actionable task title",
                "description": "Detailed task description emphasizing research methodology",
                "category": "One of the provided categories",
                "priority": "CRITICAL|HIGH|MEDIUM|LOW",
                "assignedTeam": "Most appropriate team from the list",
                "timeline": { "estimatedDuration": "X days" },
                "specificRequirements": ["Array of specific requirements"],
                "deliverable": "Expected research output",
                "successCriteria": ["Array of success measures"],
                "dependencies": ["Array of task dependencies"]
              }
            ]

            Ensure Research Team is involved in majority of tasks, either leading or collaborating.
            `;

            const response = await this.processRequest(strategicPrompt);
            const strategicTasks = JSON.parse(response);

            console.log(`[${this.name}] Generated ${strategicTasks.length} strategic tasks`);
            return {
                success: true,
                tasks: strategicTasks,
                generatedBy: this.name,
                generatedAt: new Date()
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error creating strategic tasks:`, error);
            return {
                success: false,
                error: error.message,
                fallbackTasks: [{
                    title: 'Comprehensive Research Analysis',
                    description: 'Conduct comprehensive research and analysis based on project requirements',
                    category: 'Research',
                    priority: 'HIGH',
                    assignedTeam: 'Research Team',
                    timeline: { estimatedDuration: '5 days' },
                    specificRequirements: ['Define research scope', 'Identify data sources', 'Establish methodology'],
                    deliverable: 'Research analysis report',
                    successCriteria: ['Research completed', 'Analysis documented', 'Recommendations provided'],
                    dependencies: []
                }]
            };
        }
    }

    /**
     * Enhanced strategic analysis and pattern identification delegated from ResearchAgentManager
     */
    async performStrategicAnalysis(taskData: {
        instruction: string;
        dataSet: any;
        analysisObjectives: string[];
        strategicQuestions: string[];
        context: any;
    }): Promise<any> {
        console.log(`[${this.name}] Performing strategic analysis and pattern identification`);

        try {
            const strategicAnalysisPrompt = `
            You are a Data Analyst & Synthesizer conducting strategic analysis for a research project.

            DELEGATION CONTEXT:
            You have been delegated this task by the ResearchAgentManager as part of a coordinated research team effort.

            STRATEGIC ANALYSIS REQUIREMENTS:
            Task: ${taskData.instruction}
            Analysis Objectives: ${taskData.analysisObjectives.join(', ')}
            Strategic Questions: ${taskData.strategicQuestions.join(', ')}

            DATA SET:
            ${JSON.stringify(taskData.dataSet, null, 2)}

            PROJECT CONTEXT:
            ${JSON.stringify(taskData.context, null, 2)}

            YOUR SPECIALIZED EXPERTISE:
            As the Data Analyst & Synthesizer, you excel at:
            - Identifying patterns and trends in complex data sets
            - Synthesizing information from multiple sources
            - Creating strategic insights from raw data
            - Developing evidence-based recommendations
            - Generating strategic task breakdowns and implementation plans

            Return a comprehensive JSON response with:
            {
              "strategicAnalysis": "detailed analysis summary",
              "keyPatterns": ["list of identified patterns and trends"],
              "strategicInsights": ["list of strategic insights and implications"],
              "competitiveLandscape": ["list of competitive analysis findings"],
              "recommendations": ["list of strategic recommendations"],
              "taskBreakdown": ["list of strategic tasks and priorities"],
              "teamCoordination": ["list of coordination requirements"],
              "successMetrics": ["list of KPIs and success measures"],
              "riskFactors": ["list of identified risks and mitigations"],
              "implementationPlan": "strategic implementation approach"
            }

            Focus on leveraging your analytical expertise to provide deep strategic insights and actionable recommendations.
            `;

            const response = await this.processRequest(strategicAnalysisPrompt);
            const strategicAnalysis = JSON.parse(response);

            console.log(`[${this.name}] Strategic analysis completed successfully`);
            return {
                success: true,
                strategicAnalysis,
                specialistAgent: this.name,
                delegatedBy: 'ResearchAgentManager',
                completedAt: new Date()
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error in strategic analysis:`, error);
            return {
                success: false,
                error: error.message,
                fallbackAnalysis: {
                    strategicAnalysis: 'Basic data analysis and pattern identification',
                    keyPatterns: ['Data trends identified', 'Basic correlations found'],
                    strategicInsights: ['Strategic opportunities exist', 'Further analysis recommended'],
                    competitiveLandscape: ['Competitive analysis required', 'Market positioning unclear'],
                    recommendations: ['Conduct deeper analysis', 'Gather additional data'],
                    taskBreakdown: ['Analysis tasks', 'Research tasks', 'Implementation tasks'],
                    teamCoordination: ['Coordinate with all team members'],
                    successMetrics: ['Analysis completion', 'Insight generation'],
                    riskFactors: ['Data quality risks', 'Analysis complexity'],
                    implementationPlan: 'Phased approach with iterative analysis'
                }
            };
        }
    }

     /**
      * Enhanced task handling with comprehensive LLM processing
      * Handles incoming tasks/messages for analysis and synthesis with role-specific expertise
      */
     async handleTask(_messageContent: string, metadata: Record<string, any>): Promise<void> {
         // Handle different types of delegated tasks
         if (metadata.taskType === 'generateCharts') {
             console.log(`[${this.name}] Handling chart generation task`);
             const chartTaskData = {
                 instruction: metadata.instruction || 'Generate research visualization charts',
                 analysisContent: metadata.analysisContent || '',
                 title: metadata.title || 'Research Analysis Charts',
                 chartTypes: metadata.chartTypes || ['bar', 'line', 'pie'],
                 requirements: metadata.requirements || ['Clear visualization', 'Data insights']
             };
             const result = await this.generateCharts(chartTaskData);
             // Send result back to ResearchLeadAgent
             await this.sendMessage('research-lead', 'Chart generation complete', {
                 type: 'chart_generation_complete',
                 result
             });
             return;
         }

         if (metadata.taskType === 'createStrategicTasks') {
             console.log(`[${this.name}] Handling strategic task creation`);
             const strategicTaskData = {
                 instruction: metadata.instruction || 'Create strategic task breakdown',
                 context: metadata.context || {},
                 taskCategories: metadata.taskCategories || ['Research', 'Analysis'],
                 teamAssignments: metadata.teamAssignments || ['Research Team'],
                 requirements: metadata.requirements || ['Strategic focus', 'Research emphasis'],
                 taskCount: metadata.taskCount || '5',
                 researchFocus: metadata.researchFocus !== undefined ? metadata.researchFocus : true
             };
             const result = await this.createStrategicTasks(strategicTaskData);
             // Send result back to ResearchLeadAgent
             await this.sendMessage('research-lead', 'Strategic task creation complete', {
                 type: 'strategic_tasks_complete',
                 result
             });
             return;
         }

         // Original analysis and synthesis handling
         if (metadata.subTaskId && metadata.instruction && metadata.retrievalResults) {
             console.log(`[${this.name}] Received task: ${metadata.subTaskId} - ${metadata.instruction}`);
             const results: RetrievalResult[] = metadata.retrievalResults;
             const parentTaskId = metadata.parentTaskId || 'unknown_parent';
             const researchQuestion = metadata.instruction.replace(/^Analyze retrieved information for /i, '').trim(); // Basic parsing

             const synthesis = await this.analyzeAndSynthesize(results, metadata.subTaskId, parentTaskId, researchQuestion);

             // Send results back to the Lead or directly to Writer?
             const leadAgentId = 'research-lead'; // Placeholder
             await this.sendMessage(
                 leadAgentId,
                 `Analysis complete for sub-task ${metadata.subTaskId}`,
                 {
                     type: 'analysis_complete',
                     subTaskId: metadata.subTaskId,
                     synthesis: synthesis // Attach results
                 }
             );
             await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

         } else {
             console.warn(`[${this.name}] Received message missing required data for analysis:`, metadata);
         }
     }
}