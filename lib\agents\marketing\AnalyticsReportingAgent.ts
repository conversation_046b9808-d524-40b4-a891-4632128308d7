/**
 * Analytics & Reporting Agent
 *
 * This agent provides data-driven insights and performance tracking:
 * - Tracks performance of all social media content
 * - Identifies which messaging resonates best with different audiences
 * - Provides feedback loops to improve future content
 * - Generates performance reports to guide strategy refinement
 * - Establishes baseline metrics for measuring growth
 */

import { MarketingAgent } from './MarketingAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { chartTool, ChartGenerationResult, ChartType } from '../../tools/chart-tool';
import { PdfContent, PdfGenerationOptions } from '../../tools/pdf-generator';
import { SavePdfToByteStoreResult } from '../../tools/storage-tool';

export interface PerformanceMetric {
  id: string;
  name: string;
  description: string;
  value: number;
  unit: string;
  target?: number;
  previousValue?: number;
  changePercentage?: number;
  category: 'engagement' | 'conversion' | 'reach' | 'awareness' | 'sentiment';
  platform?: string;
  contentId?: string;
  timestamp: Date;
}

export interface PerformanceReport {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  metrics: PerformanceMetric[];
  insights: string[];
  recommendations: string[];
  createdAt: Date;
}

export interface KpiDashboard {
  id: string;
  name: string;
  description: string;
  kpis: Array<{
    name: string;
    value: number;
    target: number;
    unit: string;
    trend: 'up' | 'down' | 'stable';
    changePercentage: number;
  }>;
  charts: Array<{
    title: string;
    type: 'bar' | 'line' | 'pie' | 'radar';
    data: any;
  }>;
  lastUpdated: Date;
}

export class AnalyticsReportingAgent extends MarketingAgent {
  private metrics: PerformanceMetric[] = [];
  private reports: PerformanceReport[] = [];
  private dashboards: KpiDashboard[] = [];

  constructor(
    id: string = 'analytics-reporting',
    name: string = 'Analytics & Reporting',
    userId: string = '',
    defaultLlmProvider: LlmProvider = 'openai',
    defaultLlmModel: string = 'gpt-4o'
  ) {
    const role = 'Analytics & Reporting Specialist';
    const description = `As the Analytics & Reporting Specialist, I am responsible for tracking and analyzing
marketing performance metrics, identifying trends and insights, generating comprehensive reports, and
providing data-driven recommendations to optimize marketing strategies. I establish KPIs, monitor progress,
and create feedback loops for continuous improvement.`;

    super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
  }

  /**
   * Track a performance metric
   */
  async trackMetric(
    name: string,
    description: string,
    value: number,
    unit: string,
    category: 'engagement' | 'conversion' | 'reach' | 'awareness' | 'sentiment',
    platform?: string,
    contentId?: string,
    target?: number,
    previousValue?: number
  ): Promise<PerformanceMetric> {
    // Calculate change percentage if previous value is provided
    let changePercentage: number | undefined;

    if (previousValue !== undefined && previousValue !== 0) {
      changePercentage = ((value - previousValue) / previousValue) * 100;
    }

    // Create the metric object
    const metric: PerformanceMetric = {
      id: `metric-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      value,
      unit,
      target,
      previousValue,
      changePercentage,
      category,
      platform,
      contentId,
      timestamp: new Date()
    };

    this.metrics.push(metric);

    // Store in memory for context
    if (!this.memory.Agent_Response.metrics) {
      this.memory.Agent_Response.metrics = [];
    }
    this.memory.Agent_Response.metrics.push(metric);

    // Save to storage
    await this.saveContent({
      type: 'performance_metric',
      ...metric
    }, 'marketing_analytics');

    return metric;
  }

  /**
   * Generate a performance report
   */
  async generatePerformanceReport(
    name: string,
    description: string,
    startDate: Date,
    endDate: Date,
    metricIds: string[]
  ): Promise<PerformanceReport> {
    // Get the selected metrics
    const selectedMetrics = this.metrics.filter(metric =>
      metricIds.includes(metric.id) &&
      metric.timestamp >= startDate &&
      metric.timestamp <= endDate
    );

    if (selectedMetrics.length === 0) {
      throw new Error('No valid metrics found for the specified date range');
    }

    // Create a prompt for insights and recommendations
    const prompt = `
    Generate insights and recommendations based on the following performance metrics:

    Report Name: ${name}
    Description: ${description}
    Date Range: ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}

    Metrics:
    ${selectedMetrics.map(metric =>
      `${metric.name}: ${metric.value}${metric.unit} (${metric.category})` +
      (metric.changePercentage ? ` | Change: ${metric.changePercentage.toFixed(2)}%` : '') +
      (metric.target ? ` | Target: ${metric.target}${metric.unit}` : '')
    ).join('\n')}

    Please provide:
    1. 5-7 key insights derived from these metrics
    2. 5-7 actionable recommendations based on the insights

    Format your response as a JSON object with "insights" and "recommendations" arrays.
    `;

    // Process with LLM
    const analysisJson = await this.processRequest(prompt);

    try {
      // Parse the JSON response
      const analysisData = JSON.parse(analysisJson);

      // Create the report object
      const report: PerformanceReport = {
        id: `report-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name,
        description,
        startDate,
        endDate,
        metrics: selectedMetrics,
        insights: analysisData.insights || [],
        recommendations: analysisData.recommendations || [],
        createdAt: new Date()
      };

      this.reports.push(report);

      // Store in memory for context
      if (!this.memory.Agent_Response.reports) {
        this.memory.Agent_Response.reports = [];
      }
      this.memory.Agent_Response.reports.push(report);

      // Save to storage
      await this.saveContent({
        type: 'performance_report',
        name,
        description,
        startDate,
        endDate,
        metrics: selectedMetrics.map(m => m.id),
        insights: analysisData.insights || [],
        recommendations: analysisData.recommendations || [],
        createdAt: new Date()
      }, 'marketing_analytics');

      // Generate PDF report
      await this.generateReportPdf(report);

      return report;
    } catch (error) {
      console.error('Error generating performance report:', error);
      throw new Error('Failed to generate performance report');
    }
  }

  /**
   * Create a KPI dashboard
   */
  async createKpiDashboard(
    name: string,
    description: string,
    kpis: Array<{
      name: string;
      value: number;
      target: number;
      unit: string;
    }>
  ): Promise<KpiDashboard> {
    // Get historical data for trends
    const historicalData: Record<string, number[]> = {};

    for (const kpi of kpis) {
      const kpiMetrics = this.metrics
        .filter(m => m.name === kpi.name)
        .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      historicalData[kpi.name] = kpiMetrics.map(m => m.value);
    }

    // Calculate trends and change percentages
    const kpisWithTrends = kpis.map(kpi => {
      const values = historicalData[kpi.name] || [];
      let trend: 'up' | 'down' | 'stable' = 'stable';
      let changePercentage = 0;

      if (values.length >= 2) {
        const previousValue = values[values.length - 2];
        const currentValue = values[values.length - 1];

        if (previousValue !== 0) {
          changePercentage = ((currentValue - previousValue) / previousValue) * 100;

          if (changePercentage > 1) {
            trend = 'up';
          } else if (changePercentage < -1) {
            trend = 'down';
          }
        }
      }

      return {
        ...kpi,
        trend,
        changePercentage
      };
    });

    // Generate charts for the dashboard
    const charts = [
      {
        title: 'KPI Performance vs Target',
        type: 'bar' as const,
        data: kpisWithTrends.map(kpi => ({
          name: kpi.name,
          value: kpi.value,
          target: kpi.target
        }))
      },
      {
        title: 'KPI Trends Over Time',
        type: 'line' as const,
        data: Object.entries(historicalData).map(([name, values]) => ({
          name,
          values
        }))
      }
    ];

    // Create the dashboard object
    const dashboard: KpiDashboard = {
      id: `dashboard-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name,
      description,
      kpis: kpisWithTrends,
      charts,
      lastUpdated: new Date()
    };

    this.dashboards.push(dashboard);

    // Store in memory for context
    if (!this.memory.Agent_Response.dashboards) {
      this.memory.Agent_Response.dashboards = [];
    }
    this.memory.Agent_Response.dashboards.push(dashboard);

    // Save to storage
    await this.saveContent({
      type: 'kpi_dashboard',
      ...dashboard
    }, 'marketing_analytics');

    return dashboard;
  }

  /**
   * Generate a performance chart
   */
  async generatePerformanceChart(
    title: string,
    metricIds: string[],
    chartType: ChartType = 'bar'
  ): Promise<ChartGenerationResult> {
    // Get the selected metrics
    const selectedMetrics = this.metrics.filter(metric => metricIds.includes(metric.id));

    if (selectedMetrics.length === 0) {
      throw new Error('No valid metrics found');
    }

    // Prepare data for chart
    const chartData = selectedMetrics.map(metric => ({
      name: metric.name,
      value: metric.value,
      target: metric.target,
      category: metric.category,
      platform: metric.platform
    }));

    // Create a prompt for the chart generation
    const chartPrompt = `Generate a ${chartType} chart titled "${title}" with the following data: ${JSON.stringify(chartData)}.
    The x-axis should represent the 'name' field, the y-axis should represent the 'value' field, and the series should be grouped by 'category'.
    Use the following colors: #4CAF50, #2196F3, #FF9800, #F44336, #9C27B0.`;

    // Generate the chart using the prompt
    return await chartTool.generateChart({
      prompt: chartPrompt,
      chartType: chartType
    });
  }

  /**
   * Generate a PDF report
   */
  private async generateReportPdf(report: PerformanceReport): Promise<Buffer | SavePdfToByteStoreResult> {
    // Create content sections for the PDF
    const pdfContents: PdfContent[] = [
      {
        title: report.name,
        content: report.description
      },
      {
        title: 'Executive Summary',
        content: `
          This report analyzes marketing performance from ${report.startDate.toLocaleDateString()} to ${report.endDate.toLocaleDateString()}.

          Key metrics tracked include ${report.metrics.map(m => m.name).join(', ')}.

          The analysis reveals important insights and provides actionable recommendations for improving marketing performance.
        `
      },
      {
        title: 'Performance Metrics',
        content: report.metrics.map(metric =>
          `### ${metric.name}\n` +
          `**Value:** ${metric.value}${metric.unit}\n` +
          `**Category:** ${metric.category}\n` +
          (metric.target ? `**Target:** ${metric.target}${metric.unit}\n` : '') +
          (metric.changePercentage ? `**Change:** ${metric.changePercentage.toFixed(2)}%\n` : '') +
          `**Description:** ${metric.description}\n`
        ).join('\n\n')
      },
      {
        title: 'Key Insights',
        content: report.insights.map(insight => `• ${insight}`).join('\n\n')
      },
      {
        title: 'Recommendations',
        content: report.recommendations.map(recommendation => `• ${recommendation}`).join('\n\n')
      }
    ];

    // Generate the PDF with all content sections
    // Note: We're passing an empty string as content since we're providing the content in pdfContents
    return await this.pdfGeneratorTool.generatePdf(
      pdfContents,
      {
        title: `${report.name} - Performance Report`,
        subtitle: 'Marketing Performance Report',
        date: report.createdAt.toLocaleDateString(),
        saveToByteStore: true,
        agentId: this.id,
        agentName: this.name,
        category: 'Marketing Agent Team',
        reportId: report.id,
        reportType: 'performance_report'
      }
    );
  }

  /**
   * Get all performance metrics
   */
  getMetrics(): PerformanceMetric[] {
    return this.metrics;
  }

  /**
   * Get metrics by category
   */
  getMetricsByCategory(category: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.category === category);
  }

  /**
   * Get metrics by platform
   */
  getMetricsByPlatform(platform: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.platform === platform);
  }

  /**
   * Get all performance reports
   */
  getReports(): PerformanceReport[] {
    return this.reports;
  }

  /**
   * Get a specific performance report
   */
  getReport(reportId: string): PerformanceReport | undefined {
    return this.reports.find(report => report.id === reportId);
  }

  /**
   * Get all KPI dashboards
   */
  getDashboards(): KpiDashboard[] {
    return this.dashboards;
  }

  /**
   * Get a specific KPI dashboard
   */
  getDashboard(dashboardId: string): KpiDashboard | undefined {
    return this.dashboards.find(dashboard => dashboard.id === dashboardId);
  }
}
