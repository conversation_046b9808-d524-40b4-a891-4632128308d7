/**
 * Prompt Optimizer Agent
 *
 * This agent takes an original prompt and passes it to the simplePromptOptimizer
 * which uses a two-step process:
 * 1. Infer optimization goals for the prompt
 * 2. Apply those goals to create an enhanced version of the prompt
 */

import { simplePromptOptimizer } from '../../tools/simplePromptOptimizer';

export interface SimplePromptAgentOptions {
  temperature?: number;
  maxTokens?: number;
  includeExplanation?: boolean;
}

export interface SimplePromptAgentResult {
  success: boolean;
  originalPrompt: string;
  optimizedPrompt: string;
  explanation?: string;
  error?: string;
}

export class SimplePromptAgent {
  private options: SimplePromptAgentOptions;

  constructor(options: SimplePromptAgentOptions = {}) {
    this.options = {
      temperature: options.temperature ?? 0.4,
      maxTokens: options.maxTokens ?? 2000,
      includeExplanation: options.includeExplanation ?? false
    };
  }

  /**
   * Optimize a prompt in a single pass
   * @param prompt - The original prompt to optimize
   * @returns - The optimization result including optimized prompt
   */
  async optimizePrompt(prompt: string): Promise<SimplePromptAgentResult> {
    try {
      if (!prompt) {
        throw new Error("Prompt is required");
      }

      console.log(`SimplePromptAgent: Processing prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`);

      // Step 1 & 2: Infer goals and optimize the prompt
      console.log("SimplePromptAgent: Starting two-step optimization process...");
      const optimizeResult = await simplePromptOptimizer.optimizePrompt({
        originalPrompt: prompt,
        includeExplanation: this.options.includeExplanation,
        modelOptions: {
          temperature: this.options.temperature,
          maxTokens: this.options.maxTokens
        }
      });

      console.log("SimplePromptAgent: Optimization complete:", optimizeResult);

      if (!optimizeResult.success) {
        throw new Error(`Failed to optimize prompt: ${optimizeResult.error}`);
      }

      // Verify we have an optimized prompt
      if (!optimizeResult.optimizedPrompt) {
        throw new Error("No optimized prompt was generated");
      }

      console.log("SimplePromptAgent: Successfully completed two-step prompt optimization");

      return {
        success: true,
        originalPrompt: prompt,
        optimizedPrompt: optimizeResult.optimizedPrompt,
        explanation: optimizeResult.explanation
      };
    } catch (error: any) {
      console.error("SimplePromptAgent error:", error);
      return {
        success: false,
        originalPrompt: prompt,
        optimizedPrompt: "",
        error: error.message || "Unknown error occurred in SimplePromptAgent"
      };
    }
  }
}

// Export a singleton instance
export const simplePromptAgent = new SimplePromptAgent();
