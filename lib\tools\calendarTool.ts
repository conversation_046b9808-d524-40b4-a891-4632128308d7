/**
 * calendarTool.ts
 *
 * This tool provides date and time operations for agents.
 * It supports various calendar operations like getting current date/time,
 * calculating dates, finding day of week, and calculating time differences.
 *
 * Note: This is a simplified version without external dependencies.
 * For production, consider using libraries like date-fns and date-fns-tz.
 */

export type CalendarOperation =
  | 'getCurrentDateTime'
  | 'calculateDate'
  | 'getDayOfWeek'
  | 'getTimeDifference';

export interface CalendarToolOptions {
  operation: CalendarOperation;
  date?: string;
  dateFormat?: string;
  timezone?: string;
  daysToAdd?: number;
  secondDate?: string;
}

export interface CalendarToolResult {
  success: boolean;
  result?: string | number;
  error?: string;
}

class CalendarTool {
  /**
   * Process a calendar operation request
   *
   * @param options - Calendar operation options
   * @returns Operation result
   */
  async process(options: CalendarToolOptions): Promise<CalendarToolResult> {
    try {
      const {
        operation,
        date,
        dateFormat = 'yyyy-MM-dd',
        timezone = 'UTC',
        daysToAdd = 0,
        secondDate
      } = options;

      switch (operation) {
        case 'getCurrentDateTime':
          return this.getCurrentDateTime(dateFormat, timezone);

        case 'calculateDate':
          return this.calculateDate(date, daysToAdd, dateFormat, timezone);

        case 'getDayOfWeek':
          return this.getDayOfWeek(date, dateFormat, timezone);

        case 'getTimeDifference':
          return this.getTimeDifference(date, secondDate, dateFormat, timezone);

        default:
          return {
            success: false,
            error: `Unknown operation: ${operation}`
          };
      }
    } catch (error) {
      console.error("Error in calendar tool:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error in calendar operation"
      };
    }
  }

  /**
   * Get current date and time
   */
  private getCurrentDateTime(dateFormat: string, timezone: string): CalendarToolResult {
    try {
      const now = new Date();

      // Apply timezone offset if not UTC
      if (timezone !== 'UTC') {
        try {
          // Try to get timezone offset
          const offset = this.getTimezoneOffset(timezone);
          now.setMinutes(now.getMinutes() + offset);
        } catch (error) {
          console.warn(`Timezone ${timezone} not supported, using UTC`);
        }
      }

      const formatted = this.formatDate(now, dateFormat);

      return {
        success: true,
        result: formatted
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error getting current date/time"
      };
    }
  }

  /**
   * Calculate a new date by adding/subtracting days
   */
  private calculateDate(
    date?: string,
    daysToAdd: number = 0,
    dateFormat: string = 'yyyy-MM-dd',
    timezone: string = 'UTC'
  ): CalendarToolResult {
    try {
      // If no date provided, use current date
      let baseDate: Date;

      if (!date) {
        baseDate = new Date();
      } else {
        // Try to parse the provided date
        baseDate = this.parseDate(date);

        if (isNaN(baseDate.getTime())) {
          return {
            success: false,
            error: `Invalid date format: ${date}`
          };
        }
      }

      // Add/subtract days
      const newDate = new Date(baseDate);
      newDate.setDate(newDate.getDate() + daysToAdd);

      // Format the result
      const formatted = this.formatDate(newDate, dateFormat);

      return {
        success: true,
        result: formatted
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error calculating date"
      };
    }
  }

  /**
   * Get the day of week for a date
   */
  private getDayOfWeek(
    date?: string,
    dateFormat: string = 'yyyy-MM-dd',
    timezone: string = 'UTC'
  ): CalendarToolResult {
    try {
      // If no date provided, use current date
      let targetDate: Date;

      if (!date) {
        targetDate = new Date();
      } else {
        // Try to parse the provided date
        targetDate = this.parseDate(date);

        if (isNaN(targetDate.getTime())) {
          return {
            success: false,
            error: `Invalid date format: ${date}`
          };
        }
      }

      // Get day of week
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const dayOfWeek = days[targetDate.getDay()];

      return {
        success: true,
        result: dayOfWeek
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error getting day of week"
      };
    }
  }

  /**
   * Calculate the difference between two dates in days
   */
  private getTimeDifference(
    firstDate?: string,
    secondDate?: string,
    dateFormat: string = 'yyyy-MM-dd',
    timezone: string = 'UTC'
  ): CalendarToolResult {
    try {
      // If no dates provided, return error
      if (!firstDate || !secondDate) {
        return {
          success: false,
          error: "Both dates are required for time difference calculation"
        };
      }

      // Parse the dates
      const date1 = this.parseDate(firstDate);
      const date2 = this.parseDate(secondDate);

      if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
        return {
          success: false,
          error: "Invalid date format"
        };
      }

      // Calculate difference in days
      const diffTime = Math.abs(date2.getTime() - date1.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      return {
        success: true,
        result: diffDays
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Error calculating time difference"
      };
    }
  }

  /**
   * Parse a date string
   */
  private parseDate(dateStr: string): Date {
    // Try to parse as ISO date first
    const isoDate = new Date(dateStr);
    if (!isNaN(isoDate.getTime())) {
      return isoDate;
    }

    // Try to parse common formats
    const formats = [
      // MM/DD/YYYY
      {
        regex: /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
        parse: (match: RegExpMatchArray) => new Date(
          parseInt(match[3]),
          parseInt(match[1]) - 1,
          parseInt(match[2])
        )
      },
      // DD/MM/YYYY
      {
        regex: /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
        parse: (match: RegExpMatchArray) => new Date(
          parseInt(match[3]),
          parseInt(match[2]) - 1,
          parseInt(match[1])
        )
      },
      // Month DD, YYYY
      {
        regex: /^([A-Za-z]+) (\d{1,2}),? (\d{4})$/,
        parse: (match: RegExpMatchArray) => {
          const months = {
            january: 0, february: 1, march: 2, april: 3, may: 4, june: 5,
            july: 6, august: 7, september: 8, october: 9, november: 10, december: 11
          };
          const month = months[match[1].toLowerCase() as keyof typeof months];
          return new Date(parseInt(match[3]), month, parseInt(match[2]));
        }
      }
    ];

    for (const format of formats) {
      const match = dateStr.match(format.regex);
      if (match) {
        return format.parse(match);
      }
    }

    // Return invalid date if no format matches
    return new Date(NaN);
  }

  /**
   * Format a date according to the specified format
   */
  private formatDate(date: Date, format: string): string {
    // Simple format implementation
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    // Pad with leading zeros
    const pad = (num: number) => num.toString().padStart(2, '0');

    // Replace format tokens
    return format
      .replace(/yyyy/g, year.toString())
      .replace(/MM/g, pad(month))
      .replace(/dd/g, pad(day))
      .replace(/HH/g, pad(hours))
      .replace(/mm/g, pad(minutes))
      .replace(/ss/g, pad(seconds));
  }

  /**
   * Get timezone offset in minutes
   */
  private getTimezoneOffset(timezone: string): number {
    // Simple implementation for common timezones
    const timezones: Record<string, number> = {
      'UTC': 0,
      'GMT': 0,
      'EST': -300, // UTC-5
      'EDT': -240, // UTC-4
      'CST': -360, // UTC-6
      'CDT': -300, // UTC-5
      'MST': -420, // UTC-7
      'MDT': -360, // UTC-6
      'PST': -480, // UTC-8
      'PDT': -420  // UTC-7
    };

    if (timezone in timezones) {
      return timezones[timezone];
    }

    throw new Error(`Timezone ${timezone} not supported`);
  }

  /**
   * Tool definition for function calling
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "calendar",
        description: "Retrieve date and time information or perform date calculations",
        parameters: {
          type: "object",
          properties: {
            operation: {
              type: "string",
              enum: ["getCurrentDateTime", "calculateDate", "getDayOfWeek", "getTimeDifference"],
              description: "The calendar operation to perform"
            },
            date: {
              type: "string",
              description: "Date string in ISO format (YYYY-MM-DD) or natural language"
            },
            dateFormat: {
              type: "string",
              description: "Output format for dates (default: 'yyyy-MM-dd')"
            },
            timezone: {
              type: "string",
              description: "Timezone for date operations (default: 'UTC')"
            },
            daysToAdd: {
              type: "integer",
              description: "Number of days to add/subtract for calculateDate operation"
            },
            secondDate: {
              type: "string",
              description: "Second date for getTimeDifference operation"
            }
          },
          required: ["operation"]
        }
      }
    };
  }
}

// Export the class and a singleton instance for easy import
export { CalendarTool };
export const calendarTool = new CalendarTool();
