/**
 * Direct YouTube Search
 *
 * This module provides a client-side implementation for searching YouTube
 * that bypasses the server-side API call and directly calls the YouTube API
 * from the browser. This ensures the HTTP referrer header is properly included.
 */

// Define interfaces for YouTube API responses
export interface YouTubeSearchResult {
  title: string;
  description: string;
  videoId: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  statistics?: {
    viewCount?: string;
    likeCount?: string;
    commentCount?: string;
  };
  contentDetails?: {
    duration?: string;
  };
}

export interface SearchResponse {
  success: boolean;
  results: YouTubeSearchResult[];
  metadata: {
    source: string;
    searchTime?: number;
    resultCount?: number;
    enhancedQuery?: string;
    error?: string;
    channelId?: string;
    channelTitle?: string;
  };
}

export interface YouTubeChannel {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  publishedAt: string;
}

/**
 * Search for YouTube channels by name
 */
export async function searchYouTubeChannels(query: string, maxResults: number = 5): Promise<YouTubeChannel[]> {
  try {
    const response = await fetch(`/api/youtube-channel-search?q=${encodeURIComponent(query)}&maxResults=${maxResults}`);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || errorData.details || `Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.channels || [];
  } catch (error) {
    console.error('Error searching for YouTube channels:', error);
    return [];
  }
}

/**
 * YouTube search filter options
 */
export interface YouTubeSearchFilters {
  maxResults?: number;
  publishedAfter?: string; // ISO 8601 format (e.g., "2022-01-01T00:00:00Z")
  publishedBefore?: string; // ISO 8601 format (e.g., "2023-01-01T00:00:00Z")
  videoDuration?: 'any' | 'short' | 'medium' | 'long'; // YouTube API duration filters
  order?: 'date' | 'rating' | 'relevance' | 'title' | 'videoCount' | 'viewCount';
  channelId?: string; // YouTube channel ID
  channelTitle?: string; // For display purposes only
}

/**
 * Helper function to format a date as ISO 8601 for the YouTube API
 * @param date Date object or string
 * @returns ISO 8601 formatted date string
 */
export function formatDateForYouTubeAPI(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Helper function to get a date from X days/months/years ago
 * @param amount Number of time units ago
 * @param unit Time unit (days, months, years)
 * @returns Date object
 */
export function getDateFromTimeAgo(amount: number, unit: 'days' | 'months' | 'years'): Date {
  const date = new Date();

  if (unit === 'days') {
    date.setDate(date.getDate() - amount);
  } else if (unit === 'months') {
    date.setMonth(date.getMonth() - amount);
  } else if (unit === 'years') {
    date.setFullYear(date.getFullYear() - amount);
  }

  return date;
}

/**
 * Directly search YouTube from the client side
 * This ensures the HTTP referrer header is included in the request
 */
export async function directYouTubeSearch(
  query: string,
  filters: YouTubeSearchFilters = {}
): Promise<SearchResponse> {
  try {
    const startTime = Date.now();

    // Get the API key from the server
    const apiKeyResponse = await fetch('/api/youtube-api-key');
    if (!apiKeyResponse.ok) {
      throw new Error('Failed to get YouTube API key');
    }

    const { apiKey } = await apiKeyResponse.json();
    if (!apiKey) {
      throw new Error('YouTube API key is not configured');
    }

    // Extract filters with defaults
    const {
      maxResults = 3,
      publishedAfter,
      publishedBefore,
      videoDuration,
      order = 'relevance'
    } = filters;

    // Build the search URL with query parameters
    let searchApiUrl = new URL('https://www.googleapis.com/youtube/v3/search');

    // Add required parameters
    searchApiUrl.searchParams.append('part', 'snippet');
    searchApiUrl.searchParams.append('q', query);
    searchApiUrl.searchParams.append('maxResults', maxResults.toString());
    searchApiUrl.searchParams.append('key', apiKey);
    searchApiUrl.searchParams.append('type', 'video');
    searchApiUrl.searchParams.append('order', order);

    // Add optional filters if provided
    if (publishedAfter) {
      searchApiUrl.searchParams.append('publishedAfter', publishedAfter);
    }

    if (publishedBefore) {
      searchApiUrl.searchParams.append('publishedBefore', publishedBefore);
    }

    if (videoDuration) {
      searchApiUrl.searchParams.append('videoDuration', videoDuration);
    }

    // Add channel ID if provided
    if (filters.channelId) {
      searchApiUrl.searchParams.append('channelId', filters.channelId);
    }

    // Make the request - the browser will automatically include the referrer header
    const searchResponse = await fetch(searchApiUrl.toString());

    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error('YouTube Search API error:', searchResponse.status, errorText);
      throw new Error(`YouTube API error: ${searchResponse.status}`);
    }

    const searchData = await searchResponse.json();

    // Extract video IDs for the second API call
    const videoIds = searchData.items.map((item: any) => item.id.videoId).join(',');

    // If we have video IDs, get additional details
    let videoDetails: Record<string, any> = {};
    if (videoIds) {
      try {
        // Get video details including statistics and content details
        const detailsApiUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=${videoIds}&key=${apiKey}`;
        const detailsResponse = await fetch(detailsApiUrl);

        if (detailsResponse.ok) {
          const detailsData = await detailsResponse.json();

          // Create a map of video details by ID for easy lookup
          videoDetails = detailsData.items.reduce((acc: Record<string, any>, item: any) => {
            acc[item.id] = {
              statistics: item.statistics || {},
              contentDetails: item.contentDetails || {}
            };
            return acc;
          }, {});
        }
      } catch (detailsError) {
        console.warn('Error fetching video details:', detailsError);
      }
    }

    // Transform the response to match our expected format
    const results: YouTubeSearchResult[] = searchData.items.map((item: any) => {
      const videoId = item.id.videoId;
      const details = videoDetails[videoId] || {};

      return {
        title: item.snippet.title,
        description: item.snippet.description,
        videoId: videoId,
        thumbnailUrl: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url || '',
        channelTitle: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt,
        statistics: details.statistics || {},
        contentDetails: details.contentDetails || {}
      };
    });

    return {
      success: true,
      results: results,
      metadata: {
        source: 'youtube_api_direct',
        searchTime: Date.now() - startTime,
        resultCount: results.length,
        enhancedQuery: query,
        // Include channel info if it was part of the search
        channelId: filters.channelId,
        channelTitle: filters.channelTitle
      }
    };
  } catch (error) {
    console.error('YouTube search error:', error);
    return {
      success: false,
      results: [],
      metadata: {
        source: 'youtube_api_direct',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    };
  }
}
