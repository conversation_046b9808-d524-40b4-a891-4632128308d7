# 🔍 Investigative Research Refinement - Testing Workflow

## ✅ **Testing Steps**

### **1. Basic Detection Test**
1. Navigate to: http://localhost:3000/services/pmo
2. Click "Create New Request"
3. Enter title: "Corporate Investigation"
4. Enter description: "Investigate potential financial irregularities in vendor contracts"
5. **Expected**: "Refine for Investigation" button should appear (blue styling)

### **2. Refinement Process Test**
1. Click the "Refine for Investigation" button
2. **Expected**: 
   - <PERSON><PERSON> shows "Refining..." with spinning icon
   - Loading message: "Refining for investigative research using LLM comparison..."
   - Description gets enhanced with investigative criteria
   - Label changes to "Investigative Research Request (Refined)" in blue

### **3. Enhanced Description Validation**
The refined description should include:
- ✅ Clear investigation objectives and scope
- ✅ Specific types of evidence and sources needed
- ✅ Multi-source verification methodology
- ✅ Professional fact-checking protocols
- ✅ Investigative journalism reporting standards
- ✅ Ethical guidelines for investigative research

### **4. PMO Assessment Integration**
1. Click "Generate PMO Assessment"
2. **Expected**:
   - Assessment recognizes investigative research team assignment
   - Shows "Investigative Research Team" in proposed delegation
   - Includes investigative research configuration section

### **5. End-to-End Workflow**
1. Submit the refined PMO request
2. **Expected**:
   - PMO record created with investigative research metadata
   - "Send to Investigative Research Team" button appears
   - Clicking initiates investigative research process

## 🧪 **Test Cases**

### **Investigative Research Triggers**
These descriptions should show the "Refine for Investigation" button:

1. **Financial Investigation**: "Investigate potential financial irregularities in vendor contracts"
2. **Corruption Analysis**: "Expose corruption in government procurement processes"
3. **Whistleblower Case**: "Deep dive investigation into allegations of corporate misconduct"
4. **Fact-checking Initiative**: "Comprehensive fact-checking of public statements and claims"
5. **Fraud Detection**: "Uncover financial fraud in accounting practices"

### **Non-Investigative Cases**
These should NOT show the refinement button:

1. **Marketing Campaign**: "Develop a marketing campaign for our new product"
2. **Software Development**: "Create a mobile application for customer engagement"
3. **Market Research**: "Conduct market research on industry trends"
4. **Sales Strategy**: "Improve sales processes and client relationships"

## 🔧 **Technical Validation**

### **API Endpoint Test**
```bash
curl -X POST http://localhost:3000/api/llm-comparison \
  -H "Content-Type: application/json" \
  -d '{
    "action": "optimize",
    "prompt": "Investigate financial irregularities",
    "optimizationType": "investigative-research",
    "criteria": {
      "investigativeClarity": "Clear investigation objectives",
      "evidenceSpecification": "Specific evidence types needed"
    },
    "models": {
      "optimizationModel": "gpt-4o"
    }
  }'
```

**Expected Response**:
```json
{
  "success": true,
  "optimizedPrompt": "Enhanced investigative research request...",
  "metadata": {
    "optimizationType": "investigative-research",
    "timestamp": "2025-01-XX..."
  }
}
```

## 🎯 **Success Criteria**

### **UI/UX Requirements**
- ✅ Blue "Refine for Investigation" button appears for investigative requests
- ✅ Button has search icon and proper styling
- ✅ Loading states work correctly
- ✅ Label updates to show refinement type
- ✅ Blue color scheme for investigative refinement

### **Functionality Requirements**
- ✅ Detection algorithm works accurately
- ✅ LLM comparison API integration functions
- ✅ Refinement produces enhanced descriptions
- ✅ Metadata is preserved through workflow
- ✅ PMO assessment recognizes investigative research

### **Integration Requirements**
- ✅ Works with existing PMO workflow
- ✅ Preserves all existing functionality
- ✅ Investigative research team assignment works
- ✅ End-to-end workflow completes successfully

## 🚨 **Known Issues & Fixes**

### **Fixed Issues**
1. ✅ **ReferenceError**: `detectInvestigativeResearch is not defined`
   - **Fix**: Moved function to shared scope at top of file
   - **Status**: Resolved

### **Potential Issues to Watch**
1. **API Timeout**: LLM comparison might take time
   - **Mitigation**: Added loading states and user feedback
2. **Model Availability**: Some models might be unavailable
   - **Mitigation**: Fallback to default models in API
3. **Rate Limiting**: Multiple refinement attempts
   - **Mitigation**: Disable button during processing

## 📊 **Performance Metrics**

### **Expected Response Times**
- **Detection**: < 100ms (instant)
- **Refinement**: 5-15 seconds (LLM processing)
- **Assessment**: 10-30 seconds (full PMO analysis)

### **Quality Metrics**
- **Detection Accuracy**: > 95% for clear investigative requests
- **Refinement Quality**: Should include all 6 criteria areas
- **User Satisfaction**: Intuitive workflow with clear feedback

## 🎉 **Success Indicators**

When testing is complete, you should see:
1. **Seamless Detection**: Investigative requests automatically detected
2. **Professional Refinement**: Enhanced descriptions with investigative criteria
3. **Integrated Workflow**: Works perfectly with existing PMO process
4. **Quality Results**: Better investigative research specifications
5. **User-Friendly**: Clear visual indicators and feedback

The investigative research refinement feature is now **ready for production use**! 🔍✨
