// tools/academic-search.ts

import fetch from 'node-fetch'; // Use node-fetch or a browser-compatible alternative

// Define interfaces for academic search results
export interface AcademicSearchResultItem {
    doi: string | null;
    title: string | null;
    authors: string[] | null;
    journal: string | null;
    year: number | null;
    isOa: boolean;
    bestOaUrl: string | null; // Link to the best available Open Access version
    snippet: string | null; // Snippet showing query match from Unpaywall
    score: number | null; // Relevance score from Unpaywall
}

export interface AcademicSearchOptions {
    isOa?: boolean | null; // Filter by Open Access status
    numResults?: number; // Unpaywall returns 50 per page, this controls how many we process/return
    page?: number;
}

export interface AcademicSearchResultMetadata {
    source: string;
    searchTime?: number;
    resultCount?: number; // Number of results processed/returned
    totalResultsFound?: number; // Approximate total found by API (if available)
    page?: number;
    error?: string;
}

export interface AcademicSearchResult {
    success?: boolean;
    results: AcademicSearchResultItem[];
    formattedResults: string;
    metadata: AcademicSearchResultMetadata;
}

/**
 * Academic Search Tool using Unpaywall API
 * Searches for academic articles based on titles/keywords.
 */
export class AcademicSearchTool {
    private email: string; // Required by Unpaywall API

    /**
     * Static description for AI agent usage
     */
    static description = {
        name: "academicSearch",
        description: "Search academic literature (titles, abstracts) for peer-reviewed articles using the Unpaywall API. Returns results including title, authors, journal, year, DOI, Open Access status, and links to OA versions if available.",
        parameters: {
            type: "object",
            properties: {
                query: {
                    type: "string",
                    description: "The search query (keywords, title fragments). Supports AND (default), OR, \"phrases\", -negation."
                },
                isOa: {
                    type: "boolean",
                    description: "Optional: Set to true to find only Open Access articles, false for only non-OA.",
                    nullable: true
                },
                numResults: {
                    type: "integer",
                    description: "Approximate number of results desired (default: 10, max processed per call: 50).",
                    default: 10,
                    minimum: 1,
                    maximum: 50 // Limited by Unpaywall page size
                }
            },
            required: ["query"]
        },
        returns: {
            type: "object",
            properties: {
                results: {
                    type: "array",
                    description: "List of academic article results",
                    items: { /* Omitted for brevity, defined in AcademicSearchResultItem */ }
                },
                formattedResults: {
                    type: "string",
                    description: "Search results formatted as markdown"
                }
            }
        },
         examples: [
            {
                input: { query: "quantum entanglement applications" },
                output: "Returns academic results about quantum entanglement applications"
            },
            {
                input: { query: "\"large language model\" ethics", isOa: true, numResults: 5 },
                output: "Returns 5 Open Access results about large language model ethics"
            }
        ]
    };

    constructor(email?: string) {
        // Get email from environment or argument - IMPORTANT for Unpaywall politeness policy
        this.email = email || process.env.UNPAYWALL_EMAIL || '<EMAIL>'; // Replace default
        if (this.email === '<EMAIL>') {
            console.warn('Warning: UNPAYWALL_EMAIL environment variable or constructor argument is not set. Using placeholder. Please provide a valid email for Unpaywall API usage.');
        }
    }

    /**
     * Perform an academic search using Unpaywall API
     * @param query - The search query
     * @param options - Search options
     * @returns - Search results
     */
    async search(query: string, options: AcademicSearchOptions = {}): Promise<AcademicSearchResult> {
        if (!query) {
            throw new Error('Search query is required');
        }
        if (!this.email || this.email === '<EMAIL>') {
             return {
                success: false,
                results: [],
                formattedResults: 'Error: Valid email address required for Unpaywall API.',
                metadata: { source: 'unpaywall', error: 'Email not configured' }
             };
        }

        try {
            const startTime = Date.now();
            const numResults = Math.min(options.numResults || 10, 50); // Process up to 50
            const page = options.page || 1;

            let apiUrl = `https://api.unpaywall.org/v2/search?query=${encodeURIComponent(query)}&email=${encodeURIComponent(this.email)}&page=${page}`;
            if (options.isOa !== undefined && options.isOa !== null) {
                apiUrl += `&is_oa=${options.isOa}`;
            }

            const response = await fetch(apiUrl, {
                headers: { 'Accept': 'application/json' }
            });

            if (!response.ok) {
                 const errorText = await response.text();
                 console.error(`Unpaywall API error ${response.status}: ${errorText}`);
                 throw new Error(`Unpaywall API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json() as Record<string, any>;

            // Extract and process results
            const apiResults = Array.isArray(data.results) ? data.results.slice(0, numResults) : [];
            const results: AcademicSearchResultItem[] = apiResults.map((item: any) => {
                const res = item.response || {}; // The main DOI object
                return {
                    doi: res.doi || null,
                    title: res.title || item.snippet || 'No Title Found', // Use snippet as fallback title
                    authors: (res.z_authors || []).map((a: any) => a.given || a.family ? `${a.given || ''} ${a.family || ''}`.trim() : a.name).filter(Boolean) || null,
                    journal: res.journal_name || null,
                    year: res.published_year || res.year || null,
                    isOa: res.is_oa || false,
                    bestOaUrl: res.best_oa_location?.url || null,
                    snippet: item.snippet || null,
                    score: item.score || null
                };
            });

            return {
                success: true,
                results,
                formattedResults: this._formatResultsAsMarkdown(results),
                metadata: {
                    source: 'unpaywall',
                    searchTime: Date.now() - startTime,
                    resultCount: results.length,
                    page: page,
                    // totalResultsFound: data.total_results // Unpaywall doesn't seem to return total easily
                }
            };
        } catch (error: any) {
            console.error('Academic search error:', error);
            return {
                success: false,
                results: [],
                formattedResults: `Error: ${error.message || 'Unknown error occurred'}`,
                metadata: {
                    source: 'unpaywall',
                    error: error.message || 'Unknown error occurred'
                }
            };
        }
    }

    /**
     * Format academic search results as markdown
     * @private
     */
    private _formatResultsAsMarkdown(items: AcademicSearchResultItem[]): string {
        if (!items || !Array.isArray(items) || items.length === 0) {
            return 'No academic results found.';
        }

        return items.map((item, index) => {
            const authors = item.authors?.join(', ') || 'N/A';
            const journalInfo = item.journal ? `${item.journal}, ${item.year || 'N/A'}` : (item.year || 'N/A');
            const oaStatus = item.isOa ? `[Open Access${item.bestOaUrl ? ` Link](${item.bestOaUrl})` : ']'}` : 'Closed Access';
            const doiLink = item.doi ? `[DOI: ${item.doi}](https://doi.org/${item.doi})` : 'No DOI';

            return `### ${index + 1}. ${item.title || 'No Title'}
**Authors: <AUTHORS>
**Source:** ${journalInfo} | ${doiLink} | ${oaStatus}
${item.snippet ? `\n*Match:* ${item.snippet}\n` : ''}`; // Include snippet if available
        }).join('\n');
    }

    getDescription(): typeof AcademicSearchTool.description {
        return AcademicSearchTool.description;
    }
}

// Export a singleton instance (requires email during construction or ENV var)
export const academicSearchTool = new AcademicSearchTool();