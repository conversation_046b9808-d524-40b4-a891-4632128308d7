import { NextRequest, NextResponse } from "next/server";
import { setDoc, doc as firestoreDoc } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from "@pinecone-database/pinecone";
import { FirestoreStore } from "lib/FirestoreStore";
import { processDocument } from "components/DocViewer/documentProcessors";
import { createGroqClient } from "lib/llms/groq";
//import { uploadToKnowledgeBase, updateAgentKnowledgeBase } from "components/scriptreaderAI/elevenlabs";

// Model constant for image processing
const GROQ_VISION_MODEL = process.env.GROQ_VISION_MODEL || "meta-llama/llama-4-maverick-17b-128e-instruct";
//const ELEVENLABS_AGENT_ID = process.env.ELEVENLABS_AGENT_ID || "1WU4LPk9482VXQFb80aq";

interface Document {
  pageContent: string;
  metadata: Record<string, any>;
}

// Utility function for consistent logging
function logProcess(stage: string, message: string, data?: any) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [DOCUMENT_PROCESSING] [${stage}] ${message}`);
  if (data) {
    console.log(`[${timestamp}] [DOCUMENT_PROCESSING] [${stage}] Data:`, JSON.stringify(data, null, 2));
  }
}

function cleanMetadata(metadata: any): Record<string, any> {
  const cleaned: Record<string, any> = {};
  for (const [key, value] of Object.entries(metadata)) {
    cleaned[key] = value ?? (key === 'sectionTitle' ? 'No Title' : key === 'questions' ? [] : key === 'is_summary' ? false : '');
  }
  return cleaned;
}

function createErrorMetadata(error: any, docInfo: { docId: string; fileName: string; fileType: string; category?: string; chunkId?: string }): Record<string, any> {
  return cleanMetadata({
    documentId: docInfo.docId,
    document_title: docInfo.fileName,
    file_type: docInfo.fileType,
    category: docInfo.category || 'Uncategorized',
    chunk_id: docInfo.chunkId || docInfo.docId,
    error_message: error instanceof Error ? error.message : 'Unknown error',
    error_details: error.toString(),
    created_at: new Date().toISOString(),
    sectionTitle: 'Processing Error',
    status: 'failed',
    is_error: true,
    processing_stage: 'document_processing'
  });
}

// Process image with Groq Vision API
async function processImageWithGroq(groq: any, imageUrl: string): Promise<{ analysis: string; sectionTitle: string }> {
  try {
    logProcess("IMAGE_PROCESSING", "Processing image with Groq Vision API", { imageUrl });

    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `SCRIPT ANALYSIS:
Analyze this image as if it contains a script or screenplay. Extract and format script elements only.

SCRIPT ELEMENTS TO IDENTIFY:
- Scene headings/sluglines (e.g., "INT. HOUSE - DAY")
- Character names
- Dialogue
- Action/description paragraphs
- Transitions (e.g., "CUT TO:", "FADE OUT")
- Parentheticals

FORMAT REQUIREMENTS:
- Present extracted content in proper screenplay format
- Maintain original scene numbering if present
- Preserve character name capitalization
- Include only text that appears to be part of a script
- Ignore non-script elements and decorative features`
            },
            {
              type: "image_url",
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ],
      model: GROQ_VISION_MODEL,
      temperature: 0.7,
      max_tokens: 1024,
    });

    if (!chatCompletion?.choices?.[0]?.message?.content) {
      throw new Error("Invalid response format from Groq API");
    }

    const response = chatCompletion.choices[0].message.content;

    // Look for script title or scene title first
    const titleMatch = response.match(/TITLE:\s*(.*?)\s*\n/i) ||
                      response.match(/SCRIPT TITLE:\s*(.*?)\s*\n/i) ||
                      response.match(/SCENE:\s*(.*?)\s*\n/i);

    // Look for formatted script content or analysis section
    const scriptMatch = response.match(/FORMATTED SCRIPT:\s*([\s\S]*)/i) ||
                       response.match(/SCRIPT CONTENT:\s*([\s\S]*)/i) ||
                       response.match(/ANALYSIS:\s*([\s\S]*)/i);

    const sectionTitle = titleMatch?.[1] || "Script Analysis";
    const analysis = scriptMatch?.[1]?.trim() || response.trim();

    logProcess("IMAGE_PROCESSING_COMPLETE", "Image processing completed", {
      sectionTitle,
      analysisLength: analysis.length
    });

    return {
      analysis,
      sectionTitle
    };
  } catch (error) {
    logProcess("IMAGE_PROCESSING_ERROR", "Error processing image with Groq", {
      error: error instanceof Error ? error.message : String(error)
    });
    if (error instanceof Error) {
      throw new Error(`Image processing failed: ${error.message}`);
    }
    throw new Error("Image processing failed with unknown error");
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  logProcess("REQUEST_RECEIVED", "Document processing request received");

  const requestData = await req.json();
  const { docId, userId, fileName, fileType, fileUrl, isImage = false } = requestData;

  logProcess("REQUEST_PARAMS", "Request parameters extracted", {
    docId,
    userId,
    fileName,
    fileType,
    hasFileUrl: !!fileUrl,
    isImage
  });

  try {
    // Validate required parameters
    logProcess("VALIDATION", "Validating required parameters");
    if (!docId || !userId || !fileName || !fileType || !fileUrl) {
      logProcess("VALIDATION_ERROR", "Missing required parameters", {
        hasDocId: !!docId,
        hasUserId: !!userId,
        hasFileName: !!fileName,
        hasFileType: !!fileType,
        hasFileUrl: !!fileUrl
      });
      throw new Error("Missing required parameters");
    }
    logProcess("VALIDATION_SUCCESS", "All required parameters present");

    // Initialize Groq client with userId
    logProcess("INIT", "Initializing Groq client");
    const groq = createGroqClient({ userEmail: userId });
    logProcess("INIT_SUCCESS", "Groq client initialized");

    // Initialize embeddings and storage
    logProcess("INIT", "Initializing embeddings and storage");
    const embeddings = new OpenAIEmbeddings({ openAIApiKey: process.env.OPENAI_API_KEY! });
    const pinecone = new Pinecone();
    const pineconeIndex = pinecone.Index(process.env.PINECONE_scenemate_INDEX!);
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteCollection });
    const category = "SceneMate";
    logProcess("INIT_SUCCESS", "Embeddings and storage initialized");

    // Process the document
    logProcess("DOCUMENT_PROCESSING", "Starting document processing");
    const fileResponse = await fetch(fileUrl);
    if (!fileResponse.ok) {
      logProcess("DOCUMENT_FETCH_ERROR", `Failed to fetch file: ${fileResponse.statusText}`);
      throw new Error(`Failed to fetch file: ${fileResponse.statusText}`);
    }

    const fileBlob = await fileResponse.blob();
    const file = new File([fileBlob], fileName, { type: fileType });
    logProcess("DOCUMENT_PROCESSING", "File fetched, processing content");

    let processedContent;
    if (isImage) {
      logProcess("IMAGE_PROCESSING", "Detected image file, processing with Groq Vision API");
      const { analysis, sectionTitle } = await processImageWithGroq(groq, fileUrl);
      processedContent = [{
        pageContent: analysis,
        metadata: {
          doc_id: docId,
          chunk_id: `${docId}_1`,
          sectionTitle: sectionTitle,
          content_type: "script_from_image",
          script_format: true,
          questions: [],
          is_summary: true,
          is_image: true
        }
      }];
      logProcess("IMAGE_PROCESSING_COMPLETE", "Image processing completed", {
        chunks: processedContent.length
      });
    } else {
      processedContent = await processDocument(file, docId, fileType, fileName, userId, category, '', 1500, 200);
      logProcess("DOCUMENT_PROCESSING_COMPLETE", "Document processing completed", {
        chunks: processedContent.length
      });
    }

    if (!processedContent.length) {
      logProcess("CONTENT_ERROR", "No content extracted from file");
      throw new Error('No content extracted');
    }

    // Prepare data for Firestore
    logProcess("FIRESTORE_PREPARATION", "Preparing data for Firestore");
    const byteStoreData: [string, Document][] = processedContent.map(doc => [
      doc.metadata.chunk_id,
      {
        pageContent: doc.pageContent,
        metadata: cleanMetadata({
          ...doc.metadata,
          document_title: fileName,
          category,
          file_type: fileType,
          fileUrl,
          processed_at: new Date().toISOString()
        })
      }
    ]);

    logProcess("FIRESTORE_STORAGE", "Storing documents in Firestore");
    await firestoreStore.mset(byteStoreData);
    logProcess("FIRESTORE_STORAGE_COMPLETE", "Documents stored in Firestore");

    // Upsert embeddings to Pinecone
    logProcess("PINECONE_EMBEDDINGS", "Generating and storing embeddings in Pinecone");
    for (const doc of processedContent) {
      logProcess("EMBEDDING_GENERATION", `Generating embedding for chunk ${doc.metadata.chunk_id}`);
      const embedding = await embeddings.embedQuery(doc.pageContent);
      const metadataForPinecone = cleanMetadata({
        content: doc.pageContent,
        doc_id: doc.metadata.doc_id,
        chunk_id: doc.metadata.chunk_id,
        document_title: fileName,
        category,
        file_type: fileType,
        sectionTitle: doc.metadata.sectionTitle,
        questions: doc.metadata.questions,
        is_summary: doc.metadata.is_summary,
        is_image: doc.metadata.is_image || isImage
      });

      logProcess("PINECONE_UPSERT", `Upserting embedding for chunk ${doc.metadata.chunk_id}`);
      await pineconeIndex.namespace(docId).upsert([{ id: doc.metadata.chunk_id, values: embedding, metadata: metadataForPinecone }]);
    }
    logProcess("PINECONE_EMBEDDINGS_COMPLETE", "All embeddings stored in Pinecone");

    // Always upload to ElevenLabs Knowledge Base
    // logProcess("ELEVENLABS_CHECK", "Checking if ElevenLabs upload can proceed", {
    //   hasAgentId: !!ELEVENLABS_AGENT_ID
    // });

    // let elevenLabsData = null;
    // if (ELEVENLABS_AGENT_ID) {
    //   logProcess("ELEVENLABS_UPLOAD_START", "Starting ElevenLabs Knowledge Base upload process", {
    //     fileName,
    //     fileType,
    //     agentId: ELEVENLABS_AGENT_ID
    //   });

    //   try {
    //     logProcess("ELEVENLABS_UPLOAD", `Uploading ${fileName} to ElevenLabs Knowledge Base...`);
    //     //const knowledgeBaseResponse = await uploadToKnowledgeBase(fileUrl, fileName, fileType);
    //     logProcess("ELEVENLABS_UPLOAD_SUCCESS", "File uploaded to ElevenLabs Knowledge Base", {
    //      // knowledgeBaseDocId: knowledgeBaseResponse.id
    //     });

    //     logProcess("ELEVENLABS_AGENT_UPDATE", `Updating agent ${ELEVENLABS_AGENT_ID} with new knowledge base document...`);
    //    // await updateAgentKnowledgeBase(ELEVENLABS_AGENT_ID, knowledgeBaseResponse.id);
    //     logProcess("ELEVENLABS_AGENT_UPDATE_SUCCESS", "Agent updated with new knowledge base document");

    //     elevenLabsData = {
    //       //knowledgeBaseDocId: knowledgeBaseResponse.id,
    //       //prompt_injectable: knowledgeBaseResponse.prompt_injectable,
    //       uploaded_at: new Date().toISOString()
    //     };

    //     // Store ElevenLabs metadata in Firestore
    //     logProcess("ELEVENLABS_METADATA_STORAGE", "Storing ElevenLabs metadata in Firestore");
    //     await setDoc(
    //       firestoreDoc(db, "users", userId, "ElevenLabsData", docId),
    //       elevenLabsData
    //     );

    //     logProcess("ELEVENLABS_PROCESS_COMPLETE", `Successfully uploaded to ElevenLabs Knowledge Base with ID: ${knowledgeBaseResponse.id}`);
    //   } catch (elevenLabsError) {
    //     logProcess("ELEVENLABS_ERROR", "Error uploading to ElevenLabs", {
    //       error: elevenLabsError instanceof Error ? elevenLabsError.message : String(elevenLabsError)
    //     });
    //     // Don't throw here, as we still want to return success for the rest of the processing
    //     elevenLabsData = {
    //       error: elevenLabsError instanceof Error ? elevenLabsError.message : String(elevenLabsError),
    //       error_at: new Date().toISOString()
    //     };
    //   }
    // } else {
    //   logProcess("ELEVENLABS_SKIPPED", "Skipping ElevenLabs upload", {
    //     reason: "No agent ID configured"
    //   });
    // }

    logProcess("PROCESS_COMPLETE", "Document processing completed successfully");
    return NextResponse.json({
      success: true,
      //elevenLabs: elevenLabsData
    });
  } catch (error: any) {
    logProcess("ERROR", "Error during document processing", {
      error: error instanceof Error ? error.message : String(error)
    });
    const errorMetadata = createErrorMetadata(error, { docId, fileName, fileType, category: "SceneMate" });

    logProcess("ERROR_METADATA", "Storing error metadata in Firestore");
    await setDoc(firestoreDoc(db, "users", userId, "MetadataFallback", docId), errorMetadata);

    logProcess("ERROR_RESPONSE", "Returning error response");
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}

export async function OPTIONS(_req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}