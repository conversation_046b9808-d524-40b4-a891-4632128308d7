// import { adminDb } from '@/components/firebase/admin';
import { db } from 'components/firebase/config';
import { collection, addDoc, getDocs, getDoc, updateDoc, deleteDoc, doc, query, where, orderBy, limit as firestoreLimit, serverTimestamp } from 'firebase/firestore';
import { v4 as uuidv4 } from 'uuid';
import { socialMediaService } from './social-media';
import { seoAnalyzer } from './seo-analyzer';
import { analyticsService } from './analytics';

interface CampaignData {
  name: string;
  description?: string;
  status?: string;
  platforms?: string[];
  tone?: string;
  targetAudience?: string;
  callToAction?: string;
  theme?: string;
  calendarDays?: number;
  id?: string;
  [key: string]: any;
}

interface CampaignResult {
  success: boolean;
  campaignId?: string;
  campaign?: any;
  error?: string;
}

interface ContentResult {
  success: boolean;
  campaignId?: string;
  socialMediaContent?: any[];
  contentCalendar?: any;
  error?: string;
}

interface AnalyticsResult {
  success: boolean;
  metrics?: any;
  error?: string;
}

/**
 * Campaign Manager service for creating and managing marketing campaigns
 */
class CampaignManager {
  /**
   * Create a new campaign
   * @param campaignData - Campaign data
   * @param userId - User ID
   * @returns Result of the operation
   */
  async createCampaign(campaignData: CampaignData, userId: string): Promise<CampaignResult> {
    try {
      // Validate campaign data
      if (!campaignData.name) {
        throw new Error('Campaign name is required');
      }

      // Prepare campaign data
      const campaign = {
        ...campaignData,
        userId,
        status: campaignData.status || 'draft',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        id: campaignData.id || uuidv4(),
      };

      // Add campaign to database
      const docRef = await addDoc(collection(db, 'campaigns'), campaign);

      return {
        success: true,
        campaignId: docRef.id,
        campaign,
      };
    } catch (error: any) {
      console.error('Error creating campaign:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get a campaign by ID
   * @param campaignId - Campaign ID
   * @returns Campaign data
   */
  async getCampaign(campaignId: string): Promise<CampaignResult> {
    try {
      const docRef = doc(db, 'campaigns', campaignId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        return {
          success: false,
          error: `Campaign ${campaignId} not found`,
        };
      }

      const campaign = {
        id: docSnapshot.id,
        ...docSnapshot.data(),
      };

      return {
        success: true,
        campaign,
      };
    } catch (error: any) {
      console.error('Error getting campaign:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Update a campaign
   * @param campaignId - Campaign ID
   * @param campaignData - Updated campaign data
   * @returns Result of the operation
   */
  async updateCampaign(campaignId: string, campaignData: Partial<CampaignData>): Promise<CampaignResult> {
    try {
      const docRef = doc(db, 'campaigns', campaignId);

      // Prepare update data
      const updateData = {
        ...campaignData,
        updatedAt: serverTimestamp(),
      };

      // Update campaign
      await updateDoc(docRef, updateData);

      return {
        success: true,
        campaignId,
      };
    } catch (error: any) {
      console.error('Error updating campaign:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Delete a campaign
   * @param campaignId - Campaign ID
   * @returns Result of the operation
   */
  async deleteCampaign(campaignId: string): Promise<CampaignResult> {
    try {
      // Delete campaign content first
      const contentQuery = query(
        collection(db, 'campaign_content'),
        where('campaignId', '==', campaignId)
      );

      const contentSnapshot = await getDocs(contentQuery);

      const deletePromises: Promise<void>[] = [];

      contentSnapshot.forEach((doc) => {
        deletePromises.push(deleteDoc(doc.ref));
      });

      await Promise.all(deletePromises);

      // Delete campaign
      const docRef = doc(db, 'campaigns', campaignId);
      await deleteDoc(docRef);

      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Error deleting campaign:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get all campaigns for a user
   * @param userId - User ID
   * @param limit - Maximum number of campaigns to return
   * @returns List of campaigns
   */
  async getUserCampaigns(userId: string, limitCount: number = 10): Promise<CampaignResult> {
    try {
      const campaignsQuery = query(
        collection(db, 'campaigns'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        firestoreLimit(limitCount)
      );

      const campaignsSnapshot = await getDocs(campaignsQuery);

      const campaigns: any[] = [];

      campaignsSnapshot.forEach((doc) => {
        campaigns.push({
          id: doc.id,
          ...doc.data(),
        });
      });

      return {
        success: true,
        campaign: campaigns,
      };
    } catch (error: any) {
      console.error('Error getting user campaigns:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Add content to a campaign
   * @param campaignId - Campaign ID
   * @param documentId - Document ID
   * @returns Result of the operation
   */
  async addContentToCampaign(campaignId: string, documentId: string): Promise<CampaignResult> {
    try {
      // Check if document exists
      const docRef = doc(db, 'documents', documentId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        return {
          success: false,
          error: `Document ${documentId} not found`,
        };
      }

      // Check if campaign exists
      const campaignRef = doc(db, 'campaigns', campaignId);
      const campaignSnapshot = await getDoc(campaignRef);

      if (!campaignSnapshot.exists()) {
        return {
          success: false,
          error: `Campaign ${campaignId} not found`,
        };
      }

      // Check if content is already in campaign
      const contentQuery = query(
        collection(db, 'campaign_content'),
        where('campaignId', '==', campaignId),
        where('documentId', '==', documentId)
      );

      const contentSnapshot = await getDocs(contentQuery);

      if (!contentSnapshot.empty) {
        return {
          success: true,
          campaignId,
        };
      }

      // Add content to campaign
      const contentData = {
        campaignId,
        documentId,
        createdAt: serverTimestamp(),
      };

      await addDoc(collection(db, 'campaign_content'), contentData);

      return {
        success: true,
        campaignId,
      };
    } catch (error: any) {
      console.error('Error adding content to campaign:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Remove content from a campaign
   * @param campaignId - Campaign ID
   * @param documentId - Document ID
   * @returns Result of the operation
   */
  async removeContentFromCampaign(campaignId: string, documentId: string): Promise<CampaignResult> {
    try {
      // Find content in campaign
      const contentQuery = query(
        collection(db, 'campaign_content'),
        where('campaignId', '==', campaignId),
        where('documentId', '==', documentId)
      );

      const contentSnapshot = await getDocs(contentQuery);

      if (contentSnapshot.empty) {
        return {
          success: false,
          error: `Document ${documentId} not found in campaign ${campaignId}`,
        };
      }

      // Remove content from campaign
      const deletePromises: Promise<void>[] = [];

      contentSnapshot.forEach((doc) => {
        deletePromises.push(deleteDoc(doc.ref));
      });

      await Promise.all(deletePromises);

      return {
        success: true,
        campaignId,
      };
    } catch (error: any) {
      console.error('Error removing content from campaign:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Generate campaign content
   * @param campaignId - Campaign ID
   * @param options - Generation options
   * @returns Generated content
   */
  async generateCampaignContent(campaignId: string, options: Record<string, any> = {}): Promise<ContentResult> {
    try {
      // Get the campaign
      const campaignResult = await this.getCampaign(campaignId);

      if (!campaignResult.success) {
        throw new Error(`Campaign ${campaignId} not found`);
      }

      const campaign = campaignResult.campaign;

      // Get campaign content documents
      const contentQuery = query(
        collection(db, 'campaign_content'),
        where('campaignId', '==', campaignId)
      );

      const contentSnapshot = await getDocs(contentQuery);

      if (contentSnapshot.empty) {
        throw new Error('No content found for this campaign');
      }

      // Get document IDs
      const documentIds: string[] = [];
      contentSnapshot.forEach((doc) => {
        const data = doc.data();
        documentIds.push(data.documentId);
      });

      // Generate social media content for each document
      const socialMediaResults: any[] = [];

      for (const documentId of documentIds) {
        // Get document content
        const docRef = doc(db, 'documents', documentId);
        const docSnapshot = await getDoc(docRef);
        let documentContent = '';

        if (docSnapshot.exists()) {
          const docData = docSnapshot.data();
          documentContent = docData.content || '';
        } else {
          console.warn(`Document ${documentId} not found`);
          continue;
        }

        const result = await socialMediaService.generatePosts(
          documentContent,
          options.platforms || ['twitter', 'linkedin', 'facebook'],
          {
            tone: campaign.tone || options.tone,
            targetAudience: campaign.targetAudience || options.targetAudience,
            callToAction: campaign.callToAction || options.callToAction,
          }
        );

        if (result.success) {
          socialMediaResults.push({
            documentId,
            posts: result.posts,
          });
        }
      }

      // Generate content calendar if requested
      let contentCalendar = null;

      if (options.generateCalendar) {
        // Combine content from all documents
        let combinedContent = '';

        for (const documentId of documentIds) {
          const docRef = doc(db, 'documents', documentId);
          const docSnapshot = await getDoc(docRef);

          if (docSnapshot.exists()) {
            const docData = docSnapshot.data();
            combinedContent += docData.content || '';
          }
        }

        if (combinedContent) {
          const calendarResult = await socialMediaService.generateContentCalendar(
            combinedContent,
            options.platforms || ['twitter', 'linkedin', 'facebook'],
            options.calendarDays || 7,
            {
              tone: campaign.tone || options.tone,
              targetAudience: campaign.targetAudience || options.targetAudience,
              theme: campaign.theme || options.theme,
            }
          );

          if (calendarResult.success) {
            contentCalendar = calendarResult.calendar;
          }
        }
      }

      return {
        success: true,
        campaignId,
        socialMediaContent: socialMediaResults,
        contentCalendar,
      };
    } catch (error: any) {
      console.error('Error generating campaign content:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get campaign analytics
   * @param campaignId - Campaign ID
   * @param days - Number of days to get analytics for
   * @returns Campaign analytics
   */
  async getCampaignAnalytics(campaignId: string, days: number = 30): Promise<AnalyticsResult> {
    try {
      // Get the campaign
      const campaignResult = await this.getCampaign(campaignId);

      if (!campaignResult.success) {
        throw new Error(`Campaign ${campaignId} not found`);
      }

      // Get campaign content documents
      const contentQuery = query(
        collection(db, 'campaign_content'),
        where('campaignId', '==', campaignId)
      );

      const contentSnapshot = await getDocs(contentQuery);

      if (contentSnapshot.empty) {
        throw new Error('No content found for this campaign');
      }

      // Get document IDs
      const documentIds: string[] = [];
      contentSnapshot.forEach((doc) => {
        const data = doc.data();
        documentIds.push(data.documentId);
      });

      // Get analytics for each document
      const documentAnalytics: any[] = [];

      for (const documentId of documentIds) {
        const analytics = await analyticsService.getContentPerformanceMetrics(documentId, undefined, days) as any;

        if (analytics.success) {
          documentAnalytics.push({
            documentId,
            metrics: analytics.metrics,
          });
        }
      }

      // Aggregate analytics
      const aggregatedMetrics: {
        totalViews: number,
        totalGenerations: number,
        documentMetrics: any[]
      } = {
        totalViews: 0,
        totalGenerations: 0,
        documentMetrics: [],
      };

      documentAnalytics.forEach((docAnalytics) => {
        aggregatedMetrics.totalViews += docAnalytics.metrics.totalViews || 0;
        aggregatedMetrics.totalGenerations += docAnalytics.metrics.totalGenerations || 0;
        aggregatedMetrics.documentMetrics.push(docAnalytics);
      });

      return {
        success: true,
        metrics: aggregatedMetrics,
      };
    } catch (error: any) {
      console.error('Error getting campaign analytics:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Run SEO analysis for campaign content
   * @param campaignId - Campaign ID
   * @returns SEO analysis results
   */
  async runCampaignSEOAnalysis(campaignId: string): Promise<any> {
    try {
      // Get the campaign
      const campaignResult = await this.getCampaign(campaignId);

      if (!campaignResult.success) {
        throw new Error(`Campaign ${campaignId} not found`);
      }

      // Get campaign content documents
      const contentQuery = query(
        collection(db, 'campaign_content'),
        where('campaignId', '==', campaignId)
      );

      const contentSnapshot = await getDocs(contentQuery);

      if (contentSnapshot.empty) {
        throw new Error('No content found for this campaign');
      }

      // Get document IDs
      const documentIds: string[] = [];
      contentSnapshot.forEach((doc) => {
        const data = doc.data();
        documentIds.push(data.documentId);
      });

      // Run SEO analysis for each document
      const seoResults: any[] = [];

      for (const documentId of documentIds) {
        // Get document URL
        const docRef = doc(db, 'documents', documentId);
        const docSnapshot = await getDoc(docRef);

        if (docSnapshot.exists()) {
          const docData = docSnapshot.data();

          if (docData.url) {
            const seoAnalysis = await seoAnalyzer.analyzeUrl(docData.url);

            if (seoAnalysis.success) {
              seoResults.push({
                documentId,
                url: docData.url,
                analysis: seoAnalysis,
              });
            }
          }
        }
      }

      return {
        success: true,
        campaignId,
        seoResults,
      };
    } catch (error: any) {
      console.error('Error running campaign SEO analysis:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// Export a singleton instance
export const campaignManager = new CampaignManager();

/**
 * Create a comprehensive marketing campaign
 * @param campaignData - Campaign data
 * @param documentIds - Document IDs to include
 * @param userId - User ID
 * @returns Created campaign
 */
export async function createMarketingCampaign(
  campaignData: CampaignData,
  documentIds: string[],
  userId: string
): Promise<any> {
  try {
    // Create the campaign
    const campaignResult = await campaignManager.createCampaign(campaignData, userId);

    if (!campaignResult.success) {
      throw new Error(`Failed to create campaign: ${campaignResult.error}`);
    }

    const campaignId = campaignResult.campaignId as string;

    // Add documents to the campaign
    for (const documentId of documentIds) {
      await campaignManager.addContentToCampaign(campaignId, documentId);
    }

    // Generate initial content
    const contentResult = await campaignManager.generateCampaignContent(campaignId, {
      platforms: campaignData.platforms || ['twitter', 'linkedin', 'facebook'],
      generateCalendar: true,
      calendarDays: campaignData.calendarDays || 7,
    });

    return {
      success: true,
      campaignId,
      campaign: campaignResult.campaign,
      content: contentResult.success ? {
        socialMediaContent: contentResult.socialMediaContent,
        contentCalendar: contentResult.contentCalendar,
      } : null,
    };
  } catch (error: any) {
    console.error('Error creating marketing campaign:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}