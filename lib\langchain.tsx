"use server";

import { Pinecone } from "@pinecone-database/pinecone";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { OpenAIEmbeddings } from "@langchain/openai";
import { adminDb } from "../components/firebase-admin";
import { Index, RecordMetadata } from "@pinecone-database/pinecone";
import { getServerSession } from "next-auth";
import { authOptions } from "@/api/auth/[...nextauth]/authOptions";
import { PineconeStore } from "@langchain/pinecone";
import { Document } from "langchain/document"; // Ensure you're importing this

const pinecone = new Pinecone();

// Step 1: Load the document and prepare it for processing
export async function generateDocs(docId: string) {
    const session = await getServerSession(authOptions);
    const userId = session?.user?.email;

    if (!userId) {
        throw new Error("User not signed in");
    }

    // Fetch the document metadata from the "files" collection
    const firebaseRef = await adminDb
        .collection("users")
        .doc(userId)
        .collection("files")
        .doc(docId)
        .get();

    const documentData = firebaseRef.data();
    const downloadUrl = documentData?.downloadUrl;
    const documentName = documentData?.name; // Fetch the document name from Firestore
    const category = documentData?.category;

    if (!downloadUrl) {
        throw new Error(`Cannot locate the file from the URL: ${downloadUrl}`);
    }

    console.log(`Download URL fetched successfully: ${downloadUrl}`);

    const response = await fetch(downloadUrl);
    const data = await response.blob();

    const pdfLoader = new PDFLoader(data);
    const pdfdocument = await pdfLoader.load();

    // Step 2: Split the document into smaller chunks using RecursiveCharacterTextSplitter
    const splitter = new RecursiveCharacterTextSplitter({
        chunkSize: 1000,
        chunkOverlap: 100,
    });

    const splitpdfdocs = await splitter.splitDocuments(pdfdocument);
    console.log(`Splitting document into ${splitpdfdocs.length} parts...`);

    // Attach metadata and log page content with metadata
    const docsWithMetadata = splitpdfdocs.map((doc, index) => {
        const metadata = {
            page_number: doc.metadata?.pageNumber || index + 1,
            category: category || 'Uncategorized',
            document_title: documentName || 'Untitled',
        };

        console.log(`Document chunk ${index + 1}:`);
        console.log(`Page Content:`, doc.pageContent);
        console.log(`Metadata:`, metadata);

        return new Document({
            pageContent: doc.pageContent,
            metadata: metadata
        });
    });

    return docsWithMetadata;
}

// Helper function to check if a namespace exists in Pinecone
async function namespaceExists(index: Index<RecordMetadata>, namespace: string) {
    if (namespace === null) throw new Error("Namespace was not provided");
    const { namespaces } = await index.describeIndexStats();

    console.log("Namespace already exists");
    return namespaces?.[namespace] !== undefined;
}

// Step 3: Generate embeddings and upsert them into Pinecone
export async function generateEmbeddingsInPineconeVectorStore(docId: string): Promise<{ namespace: string, status: string }> {
    const session = await getServerSession(authOptions);
    const userId = session?.user?.email;

    if (!userId) {
        throw new Error("User not signed in");
    }

    console.log("Generating embeddings");

    const embeddings = new OpenAIEmbeddings();
    const index = await pinecone.index(process.env.PINECONE_INDEX!);
    const namespaceAlreadyExists = await namespaceExists(index, docId);
    let pineconeNamespace = docId

    // Generate document chunks and metadata
    const docsWithMetadata = await generateDocs(docId);

    if (namespaceAlreadyExists) {
        console.log(`${docId} ... has already been uploaded...`);
    
        // Upsert the new document into the existing namespace with metadata
        console.log(`Upserting chunks into the ${index} index under the ${docId} namespace`);
        docsWithMetadata.forEach((doc, index) => {
            console.log(`Upserting document chunk ${index + 1} with metadata:`, doc.metadata);
            console.log(`Page Content:`, doc.pageContent);
        });
        
        await PineconeStore.fromDocuments(docsWithMetadata, embeddings, {
            pineconeIndex: index,
            namespace: pineconeNamespace,
        });

        return { namespace: pineconeNamespace, status: "Embeddings upserted into existing namespace." };
    } else {
        console.log(`Converting chunks into embeddings and storing in the ${index} index under the ${pineconeNamespace} namespace`);

        // Insert new document into the new namespace with metadata
        docsWithMetadata.forEach((doc, index) => {
            console.log(`Upserting document chunk ${index + 1} with metadata:`, doc.metadata);
            console.log(`Page Content:`, doc.pageContent);
        });

        await PineconeStore.fromDocuments(docsWithMetadata, embeddings, {
            pineconeIndex: index,
            namespace: pineconeNamespace,
        });

        return { namespace: pineconeNamespace, status: "Embeddings successfully generated and stored." };
    }
}
