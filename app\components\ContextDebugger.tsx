'use client';

import React from 'react';
import { usePlanner } from '../context/PlannerContext';

export default function ContextDebugger() {
  const plannerContext = usePlanner();

  return (
    <div className="bg-gray-800 rounded-lg p-4 mb-4 text-sm">
      <h3 className="text-white font-medium mb-2">Context Debugger</h3>
      <div className="grid grid-cols-2 gap-2">
        <div className="bg-gray-700 p-2 rounded">
          <p className="text-gray-300 font-medium">Users</p>
          <p className="text-white">{plannerContext.users.length}</p>
          <p className="text-xs text-gray-400 mt-1">
            Authorized: {plannerContext.users.filter(u => u.isAuthorized !== false).length}
          </p>
        </div>
        <div className="bg-gray-700 p-2 rounded">
          <p className="text-gray-300 font-medium">Projects</p>
          <p className="text-white">{plannerContext.projects.length}</p>
        </div>
        <div className="bg-gray-700 p-2 rounded">
          <p className="text-gray-300 font-medium">Tasks</p>
          <p className="text-white">{plannerContext.tasks.length}</p>
        </div>
        <div className="bg-gray-700 p-2 rounded">
          <p className="text-gray-300 font-medium">Loading</p>
          <p className="text-white">{plannerContext.loading ? 'Yes' : 'No'}</p>
        </div>
      </div>
      {plannerContext.error && (
        <div className="mt-2 bg-red-900/50 p-2 rounded text-red-300">
          <p className="font-medium">Error</p>
          <p>{plannerContext.error}</p>
        </div>
      )}
      <button
        onClick={() => plannerContext.refreshData()}
        className="mt-2 bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700 transition-colors"
      >
        Refresh Data
      </button>
    </div>
  );
}
