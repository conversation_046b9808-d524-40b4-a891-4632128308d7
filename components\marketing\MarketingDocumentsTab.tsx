'use client';

import React, { useState, useEffect } from 'react';
import { collection, query, orderBy, onSnapshot, where } from 'firebase/firestore';
import { db } from '../../components/firebase';
import { FileIcon, Download, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';

interface MarketingDocument {
  id: string;
  name?: string;
  title?: string;
  type?: string;
  size?: number;
  downloadUrl?: string;
  createdAt?: any; // Firebase timestamp
  generatedBy?: string;
  agentName?: string;
  category?: string;
}

export default function MarketingDocumentsTab() {
  const [documents, setDocuments] = useState<MarketingDocument[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    setLoading(true);

    // Get the system admin email from environment or use default
    const sysAdmin = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';
    
    // Reference to the files collection
    const filesCollection = collection(db, `users/${sysAdmin}/files`);
    
    // Query for marketing agent documents
    const marketingDocsQuery = query(
      filesCollection,
      where('category', '==', 'Marketing Agent Team'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(
      marketingDocsQuery,
      (snapshot) => {
        const docs = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        } as MarketingDocument));

        setDocuments(docs);
        setLoading(false);
      },
      (err) => {
        console.error('Error fetching marketing documents:', err);
        setError('Failed to load marketing documents');
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, []);

  const formatFileSize = (bytes: number): string => {
    if (!bytes || isNaN(bytes)) return '0 bytes';
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return 'Unknown date';
    
    try {
      // Handle different timestamp formats
      const date = timestamp.toDate ? timestamp.toDate() : 
                  timestamp.seconds ? new Date(timestamp.seconds * 1000) :
                  new Date(timestamp);
      
      return format(date, 'MMM d, yyyy');
    } catch (e) {
      console.error('Error formatting date:', e);
      return 'Invalid date';
    }
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
          <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
        </div>
        <p className="mt-2 text-gray-400">Loading documents...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      {documents.length === 0 ? (
        <div className="text-center py-8 text-gray-400">
          <p>No documents generated yet</p>
        </div>
      ) : (
        <div className="space-y-2">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileIcon className="w-5 h-5 text-red-400" />
                <div>
                  <p className="text-white font-medium">{doc.title || doc.name || 'Unnamed document'}</p>
                  <div className="flex space-x-4 text-xs text-gray-400">
                    <span>{formatFileSize(doc.size || 0)}</span>
                    <span>{formatDate(doc.createdAt)}</span>
                    <span>By: {doc.agentName || doc.generatedBy || 'Unknown agent'}</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-2">
                {doc.downloadUrl ? (
                  <>
                    <a
                      href={doc.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1.5 bg-white text-gray-900 hover:text-white hover:bg-gray-600 rounded-full"
                      title="View"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </a>
                    <a
                      href={doc.downloadUrl}
                      download={doc.name || 'document'}
                      className="p-1.5 bg-green-500 text-gray-900 hover:text-white hover:bg-gray-600 rounded-full"
                      title="Download"
                    >
                      <Download className="w-4 h-4" />
                    </a>
                  </>
                ) : (
                  <span className="p-1.5 text-gray-500" title="Download URL not available">
                    <ExternalLink className="w-4 h-4" />
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
