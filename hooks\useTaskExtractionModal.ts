"use client"

import { useState, useCallback } from 'react'

export interface TaskExtractionResult {
  taskCount: number
  extractionMode: 'PMO' | 'Standard'
  modelUsed: 'Gemini' | 'Groq'
  tasks: Array<{
    title: string
    category: string
    assignedTo?: string
    dueDate?: string
  }>
  confidence: number
  processingTime: number
  projectName?: string
  agentType?: string
  requestId?: string
}

export interface TaskExtractionModalState {
  isOpen: boolean
  result: TaskExtractionResult | null
}

export const useTaskExtractionModal = () => {
  const [modalState, setModalState] = useState<TaskExtractionModalState>({
    isOpen: false,
    result: null
  })

  const showModal = useCallback((result: TaskExtractionResult) => {
    setModalState({
      isOpen: true,
      result
    })
  }, [])

  const hideModal = useCallback(() => {
    setModalState({
      isOpen: false,
      result: null
    })
  }, [])

  const updateResult = useCallback((updates: Partial<TaskExtractionResult>) => {
    setModalState(prev => ({
      ...prev,
      result: prev.result ? { ...prev.result, ...updates } : null
    }))
  }, [])

  return {
    isOpen: modalState.isOpen,
    result: modalState.result,
    showModal,
    hideModal,
    updateResult
  }
}

// Global event system for task extraction notifications
export class TaskExtractionNotificationSystem {
  private static listeners: Array<(result: TaskExtractionResult) => void> = []

  static subscribe(listener: (result: TaskExtractionResult) => void) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  static notify(result: TaskExtractionResult) {
    console.log('TaskExtractionNotificationSystem: Notifying listeners of task extraction success', result)
    this.listeners.forEach(listener => {
      try {
        listener(result)
      } catch (error) {
        console.error('TaskExtractionNotificationSystem: Error in listener:', error)
      }
    })
  }

  static clear() {
    this.listeners = []
  }
}

export default useTaskExtractionModal
