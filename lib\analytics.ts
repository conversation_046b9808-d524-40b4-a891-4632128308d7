// import { adminDb } from 'components/firebase/admin';
import { db } from 'components/firebase/config';
import { collection, query, where, orderBy, getDocs, addDoc, serverTimestamp, Query, Timestamp } from 'firebase/firestore';

// Define interfaces for event and metrics data
interface AnalyticsEvent {
  documentId: string;
  userId: string;
  eventType: string;
  timestamp: Timestamp;
  metadata: Record<string, any>;
}

interface AnalyticsEventDoc extends AnalyticsEvent {
  id: string;
}

interface DocumentMetrics {
  documentId: string;
  views: number;
  generations: number;
  lastViewed: Date | null;
}

interface ContentPerformanceMetrics {
  totalViews: number;
  totalGenerations: number;
  uniqueDocumentsViewed: number;
  documentMetrics: DocumentMetrics[];
  timeRange: {
    start: Date;
    end: Date;
    days: number;
  };
}

interface UserMetrics {
  userId: string;
  totalEvents: number;
  views: number;
  generations: number;
  uniqueDocumentsCount: number;
  lastActive: Date | null;
}

interface UserActivityMetrics {
  totalUsers: number;
  mostActiveUsers: UserMetrics[];
  timeRange: {
    start: Date;
    end: Date;
    days: number;
  };
}

/**
 * Analytics service for tracking and analyzing content performance
 */
class AnalyticsService {
  /**
   * Track a content view event
   * @param documentId - The ID of the viewed document
   * @param userId - The ID of the user viewing the document
   * @param metadata - Additional metadata about the view
   * @returns Result of the operation
   */
  async trackContentView(
    documentId: string,
    userId: string = 'anonymous',
    metadata: Record<string, any> = {}
  ): Promise<{ success: true; eventId: string } | { success: false; error: string }> {
    try {
      const eventData = {
        documentId,
        userId,
        eventType: 'content_view',
        timestamp: serverTimestamp(),
        metadata: {
          ...metadata,
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
        },
      };

      const docRef = await addDoc(collection(db, 'analytics_events'), eventData);

      return {
        success: true,
        eventId: docRef.id,
      };
    } catch (error: any) {
      console.error('Error tracking content view:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Track a content generation event
   * @param documentId - The ID of the generated document
   * @param userId - The ID of the user generating the document
   * @param metadata - Additional metadata about the generation
   * @returns Result of the operation
   */
  async trackContentGeneration(
    documentId: string,
    userId: string = 'anonymous',
    metadata: Record<string, any> = {}
  ): Promise<{ success: true; eventId: string } | { success: false; error: string }> {
    try {
      const eventData = {
        documentId,
        userId,
        eventType: 'content_generation',
        timestamp: serverTimestamp(),
        metadata,
      };

      const docRef = await addDoc(collection(db, 'analytics_events'), eventData);

      return {
        success: true,
        eventId: docRef.id,
      };
    } catch (error: any) {
      console.error('Error tracking content generation:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get content performance metrics
   * @param documentId - Optional document ID to filter by
   * @param userId - Optional user ID to filter by
   * @param days - Number of days to look back
   * @returns Performance metrics
   */
  async getContentPerformanceMetrics(
    documentId: string | null = null,
    userId: string | null = null,
    days: number = 30
  ): Promise<{ success: true; metrics: ContentPerformanceMetrics } | { success: false; error: string }> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      let q: Query = query(
        collection(db, 'analytics_events'),
        where('timestamp', '>=', startDate),
        where('timestamp', '<=', endDate),
        orderBy('timestamp', 'desc')
      );

      if (documentId) {
        q = query(q, where('documentId', '==', documentId));
      }

      if (userId) {
        q = query(q, where('userId', '==', userId));
      }

      const querySnapshot = await getDocs(q);

      const events: AnalyticsEventDoc[] = [];
      querySnapshot.forEach((doc) => {
        events.push({
          id: doc.id,
          ...(doc.data() as AnalyticsEvent),
        });
      });

      const viewEvents = events.filter((event) => event.eventType === 'content_view');
      const generationEvents = events.filter((event) => event.eventType === 'content_generation');

      const documentMetrics: { [key: string]: DocumentMetrics } = {};
      events.forEach((event) => {
        if (!documentMetrics[event.documentId]) {
          documentMetrics[event.documentId] = {
            documentId: event.documentId,
            views: 0,
            generations: 0,
            lastViewed: null,
          };
        }

        if (event.eventType === 'content_view') {
          documentMetrics[event.documentId].views += 1;
          const eventTimestamp = event.timestamp?.toDate?.() || new Date();
          if (
            !documentMetrics[event.documentId].lastViewed ||
            eventTimestamp > documentMetrics[event.documentId].lastViewed!
          ) {
            documentMetrics[event.documentId].lastViewed = eventTimestamp;
          }
        } else if (event.eventType === 'content_generation') {
          documentMetrics[event.documentId].generations += 1;
        }
      });

      return {
        success: true,
        metrics: {
          totalViews: viewEvents.length,
          totalGenerations: generationEvents.length,
          uniqueDocumentsViewed: Object.keys(documentMetrics).length,
          documentMetrics: Object.values(documentMetrics),
          timeRange: {
            start: startDate,
            end: endDate,
            days,
          },
        },
      };
    } catch (error: any) {
      console.error('Error getting content performance metrics:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Get user activity metrics
   * @param days - Number of days to look back
   * @param userLimit - Maximum number of users to return
   * @returns User activity metrics
   */
  async getUserActivityMetrics(
    days: number = 30,
    userLimit: number = 100
  ): Promise<{ success: true; metrics: UserActivityMetrics } | { success: false; error: string }> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const q: Query = query(
        collection(db, 'analytics_events'),
        where('timestamp', '>=', startDate),
        where('timestamp', '<=', endDate),
        orderBy('timestamp', 'desc')
      );

      const querySnapshot = await getDocs(q);

      const events: AnalyticsEventDoc[] = [];
      querySnapshot.forEach((doc) => {
        events.push({
          id: doc.id,
          ...(doc.data() as AnalyticsEvent),
        });
      });

      const userMetrics: {
        [key: string]: {
          userId: string;
          totalEvents: number;
          views: number;
          generations: number;
          uniqueDocuments: Set<string>;
          lastActive: Date | null;
        };
      } = {};
      events.forEach((event) => {
        if (!userMetrics[event.userId]) {
          userMetrics[event.userId] = {
            userId: event.userId,
            totalEvents: 0,
            views: 0,
            generations: 0,
            uniqueDocuments: new Set<string>(),
            lastActive: null,
          };
        }

        userMetrics[event.userId].totalEvents += 1;
        userMetrics[event.userId].uniqueDocuments.add(event.documentId);

        if (event.eventType === 'content_view') {
          userMetrics[event.userId].views += 1;
        } else if (event.eventType === 'content_generation') {
          userMetrics[event.userId].generations += 1;
        }

        const eventTimestamp = event.timestamp?.toDate?.() || new Date();
        if (
          !userMetrics[event.userId].lastActive ||
          eventTimestamp > userMetrics[event.userId].lastActive!
        ) {
          userMetrics[event.userId].lastActive = eventTimestamp;
        }
      });

      const sortedUsers: UserMetrics[] = Object.values(userMetrics)
        .map((user) => ({
          userId: user.userId,
          totalEvents: user.totalEvents,
          views: user.views,
          generations: user.generations,
          uniqueDocumentsCount: user.uniqueDocuments.size,
          lastActive: user.lastActive,
        }))
        .sort((a, b) => b.totalEvents - a.totalEvents)
        .slice(0, userLimit);

      return {
        success: true,
        metrics: {
          totalUsers: Object.keys(userMetrics).length,
          mostActiveUsers: sortedUsers,
          timeRange: {
            start: startDate,
            end: endDate,
            days,
          },
        },
      };
    } catch (error: any) {
      console.error('Error getting user activity metrics:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
}

// Export a singleton instance
export const analyticsService = new AnalyticsService();

/**
 * Generate a comprehensive analytics report
 * @param days - Number of days to include in the report
 * @returns Analytics report data
 */
export async function generateAnalyticsReport(
  days: number = 30
): Promise<{
  success: true;
  reportDate: Date;
  timeRange: { start: Date; end: Date; days: number };
  contentMetrics: ContentPerformanceMetrics | null;
  userMetrics: UserActivityMetrics | null;
} | { success: false; error: string }> {
  try {
    const contentPerformance = await analyticsService.getContentPerformanceMetrics(null, null, days);
    const userActivity = await analyticsService.getUserActivityMetrics(days);

    return {
      success: true,
      reportDate: new Date(),
      timeRange: {
        start: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
        end: new Date(),
        days,
      },
      contentMetrics: contentPerformance.success ? contentPerformance.metrics : null,
      userMetrics: userActivity.success ? userActivity.metrics : null,
    };
  } catch (error: any) {
    console.error('Error generating analytics report:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}