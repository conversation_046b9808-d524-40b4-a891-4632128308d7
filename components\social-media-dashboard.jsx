'use client';

import React, { useState } from 'react';
import {
  Twitter,
  Linkedin,
  Facebook,
  Instagram,
  Calendar,
  Send,
  Copy,
  Check,
  RefreshCw,
  MessageSquare,
  Hash,
  AtSign,
  Link as LinkIcon,
  AlertTriangle,
  Save,
  FileText
} from 'lucide-react';
// Use API routes instead of direct library calls

/**
 * Social Media Dashboard component for generating and managing social media content
 */
export default function SocialMediaDashboard({ documentId, content }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [generatedPosts, setGeneratedPosts] = useState(null);
  const [contentCalendar, setContentCalendar] = useState(null);
  const [selectedPlatforms, setSelectedPlatforms] = useState(['twitter', 'linkedin', 'facebook']);
  const [options, setOptions] = useState({
    tone: 'professional',
    targetAudience: 'marketing professionals',
    callToAction: 'Learn more',
    generateCalendar: false,
    calendarDays: 7,
  });
  const [copiedPost, setCopiedPost] = useState(null);
  const [savedPosts, setSavedPosts] = useState([]);

  // Platform icons and colors
  const platformConfig = {
    twitter: {
      icon: <Twitter size={20} />,
      color: 'text-blue-400',
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-800',
      name: 'Twitter'
    },
    linkedin: {
      icon: <Linkedin size={20} />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-800',
      name: 'LinkedIn'
    },
    facebook: {
      icon: <Facebook size={20} />,
      color: 'text-blue-500',
      bgColor: 'bg-blue-900/20',
      borderColor: 'border-blue-800',
      name: 'Facebook'
    },
    instagram: {
      icon: <Instagram size={20} />,
      color: 'text-pink-500',
      bgColor: 'bg-pink-900/20',
      borderColor: 'border-pink-800',
      name: 'Instagram'
    },
  };

  // Toggle platform selection
  const togglePlatform = (platform) => {
    if (selectedPlatforms.includes(platform)) {
      setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform));
    } else {
      setSelectedPlatforms([...selectedPlatforms, platform]);
    }
  };

  // Handle option change
  const handleOptionChange = (e) => {
    const { name, value, type, checked } = e.target;
    setOptions({
      ...options,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  // Generate social media posts
  const handleGeneratePosts = async () => {
    if (selectedPlatforms.length === 0) {
      setError('Please select at least one platform');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Use the dedicated social media API route
      const response = await fetch('/api/social-media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generatePosts',
          content,
          platforms: selectedPlatforms,
          options: {
            tone: options.tone,
            targetAudience: options.targetAudience,
            callToAction: options.callToAction,
          }
        }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success) {
        setGeneratedPosts(result.posts);

        // Generate content calendar if requested
        if (options.generateCalendar) {
          const calendarResponse = await fetch('/api/social-media', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              action: 'generateContentCalendar',
              content,
              platforms: selectedPlatforms,
              days: options.calendarDays,
              options: {
                tone: options.tone,
                targetAudience: options.targetAudience,
                callToAction: options.callToAction,
              }
            }),
          });

          if (!calendarResponse.ok) {
            throw new Error(`Server responded with ${calendarResponse.status}: ${calendarResponse.statusText}`);
          }

          const calendarResult = await calendarResponse.json();

          if (calendarResult.success) {
            setContentCalendar(calendarResult.calendar);
          }
        }
      } else {
        setError(result.error || 'Failed to generate social media posts');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while generating posts');
    } finally {
      setLoading(false);
    }
  };

  // Copy post to clipboard
  const copyPostToClipboard = (platform) => {
    if (generatedPosts && generatedPosts[platform]) {
      navigator.clipboard.writeText(generatedPosts[platform].text);
      setCopiedPost(platform);
      setTimeout(() => setCopiedPost(null), 2000);
    }
  };

  // Save post
  const savePost = async (platform) => {
    if (generatedPosts && generatedPosts[platform]) {
      try {
        // For now, we'll just simulate saving the post
        // In a real implementation, this would call a dedicated API endpoint
        console.log('Saving post:', platform, generatedPosts[platform]);

        // Simulate successful save
        const result = { success: true };

        if (result.success) {
          setSavedPosts([...savedPosts, platform]);
        }
      } catch (err) {
        console.error('Error saving post:', err);
      }
    }
  };

  // Render platform selection buttons
  const renderPlatformButtons = () => (
    <div className="flex flex-wrap gap-2 mb-4">
      {Object.entries(platformConfig).map(([platform, config]) => (
        <button
          key={platform}
          onClick={() => togglePlatform(platform)}
          className={`flex items-center px-3 py-2 rounded-md text-sm ${
            selectedPlatforms.includes(platform)
              ? `${config.bgColor} ${config.color} border ${config.borderColor}`
              : 'bg-zinc-800 text-zinc-400 border border-zinc-700 hover:bg-zinc-700'
          }`}
        >
          {config.icon}
          <span className="ml-2">{config.name}</span>
        </button>
      ))}
    </div>
  );

  // Render options form
  const renderOptionsForm = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <div>
        <label className="block text-sm font-medium text-zinc-400 mb-1">
          Tone
        </label>
        <select
          name="tone"
          value={options.tone}
          onChange={handleOptionChange}
          className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="professional">Professional</option>
          <option value="casual">Casual</option>
          <option value="friendly">Friendly</option>
          <option value="authoritative">Authoritative</option>
          <option value="humorous">Humorous</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-zinc-400 mb-1">
          Target Audience
        </label>
        <input
          type="text"
          name="targetAudience"
          value={options.targetAudience}
          onChange={handleOptionChange}
          placeholder="e.g., marketing professionals"
          className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-zinc-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-zinc-400 mb-1">
          Call to Action
        </label>
        <input
          type="text"
          name="callToAction"
          value={options.callToAction}
          onChange={handleOptionChange}
          placeholder="e.g., Learn more"
          className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-zinc-500"
        />
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="generateCalendar"
          name="generateCalendar"
          checked={options.generateCalendar}
          onChange={handleOptionChange}
          className="h-4 w-4 rounded border-zinc-600 text-blue-600 focus:ring-blue-500 focus:ring-offset-zinc-900"
        />
        <label htmlFor="generateCalendar" className="ml-2 block text-sm text-zinc-400">
          Generate Content Calendar
        </label>

        {options.generateCalendar && (
          <div className="ml-4">
            <select
              name="calendarDays"
              value={options.calendarDays}
              onChange={handleOptionChange}
              className="px-2 py-1 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-200 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7">7 days</option>
              <option value="14">14 days</option>
              <option value="30">30 days</option>
            </select>
          </div>
        )}
      </div>
    </div>
  );

  // Render generated posts
  const renderGeneratedPosts = () => (
    <div className="space-y-4">
      {selectedPlatforms.map(platform => {
        if (!generatedPosts || !generatedPosts[platform]) return null;

        const post = generatedPosts[platform];
        const config = platformConfig[platform];

        return (
          <div
            key={platform}
            className={`p-4 rounded-lg border ${config.borderColor} ${config.bgColor}`}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                {config.icon}
                <span className={`ml-2 font-medium ${config.color}`}>{config.name}</span>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => copyPostToClipboard(platform)}
                  className="p-1.5 rounded-md bg-zinc-800 text-zinc-300 hover:bg-zinc-700"
                  title="Copy to clipboard"
                >
                  {copiedPost === platform ? (
                    <Check size={16} className="text-green-500" />
                  ) : (
                    <Copy size={16} />
                  )}
                </button>

                <button
                  onClick={() => savePost(platform)}
                  className={`p-1.5 rounded-md ${
                    savedPosts.includes(platform)
                      ? 'bg-green-900/30 text-green-300'
                      : 'bg-zinc-800 text-zinc-300 hover:bg-zinc-700'
                  }`}
                  title="Save post"
                  disabled={savedPosts.includes(platform)}
                >
                  {savedPosts.includes(platform) ? (
                    <Check size={16} />
                  ) : (
                    <Save size={16} />
                  )}
                </button>
              </div>
            </div>

            <div className="bg-zinc-900 p-3 rounded-md text-zinc-200 whitespace-pre-wrap">
              {post.text}
            </div>

            <div className="mt-3 flex flex-wrap gap-2">
              {post.hashtags.length > 0 && (
                <div className="flex items-center text-xs bg-zinc-800 px-2 py-1 rounded-md text-zinc-300">
                  <Hash size={12} className="mr-1" />
                  {post.hashtags.length} hashtags
                </div>
              )}

              {post.mentions.length > 0 && (
                <div className="flex items-center text-xs bg-zinc-800 px-2 py-1 rounded-md text-zinc-300">
                  <AtSign size={12} className="mr-1" />
                  {post.mentions.length} mentions
                </div>
              )}

              {post.urls.length > 0 && (
                <div className="flex items-center text-xs bg-zinc-800 px-2 py-1 rounded-md text-zinc-300">
                  <LinkIcon size={12} className="mr-1" />
                  {post.urls.length} links
                </div>
              )}

              <div className="flex items-center text-xs bg-zinc-800 px-2 py-1 rounded-md text-zinc-300">
                <MessageSquare size={12} className="mr-1" />
                {post.characterCount} characters
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  // Render content calendar
  const renderContentCalendar = () => (
    <div className="mt-6">
      <h3 className="text-lg font-semibold mb-4 flex items-center text-white">
        <Calendar className="mr-2 text-purple-400" size={20} />
        Content Calendar ({options.calendarDays} days)
      </h3>

      <div className="space-y-6">
        {contentCalendar.map((day, dayIndex) => (
          <div key={dayIndex} className="bg-zinc-900 p-4 rounded-lg border border-zinc-700">
            <h4 className="text-md font-medium mb-3 text-zinc-200">
              Day {day.day}
            </h4>

            <div className="space-y-4">
              {day.posts.map((post, postIndex) => {
                const platform = post.platform;
                const config = platformConfig[platform];

                return (
                  <div
                    key={`${dayIndex}-${postIndex}`}
                    className={`p-3 rounded-lg border ${config.borderColor} ${config.bgColor}`}
                  >
                    <div className="flex items-center mb-2">
                      {config.icon}
                      <span className={`ml-2 font-medium ${config.color}`}>{config.name}</span>
                    </div>

                    <div className="bg-zinc-900 p-3 rounded-md text-zinc-200 whitespace-pre-wrap">
                      {post.text}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="social-media-dashboard space-y-6">
      <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
          <Send className="mr-2 text-blue-400" size={20} />
          Social Media Content Generator
        </h2>

        <div className="mb-4">
          <h3 className="text-md font-medium mb-2 text-zinc-300">Select Platforms</h3>
          {renderPlatformButtons()}
        </div>

        <div className="mb-4">
          <h3 className="text-md font-medium mb-2 text-zinc-300">Content Options</h3>
          {renderOptionsForm()}
        </div>

        <button
          onClick={handleGeneratePosts}
          disabled={loading || selectedPlatforms.length === 0}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-zinc-900 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <RefreshCw className="inline-block mr-2 animate-spin" size={16} />
              Generating...
            </>
          ) : (
            'Generate Social Media Content'
          )}
        </button>

        {error && (
          <div className="mt-4 p-3 bg-red-900/20 border border-red-800 rounded-md text-red-200">
            <div className="flex items-start">
              <AlertTriangle className="mr-2 flex-shrink-0 mt-0.5" size={16} />
              <p>{error}</p>
            </div>
          </div>
        )}
      </div>

      {/* Generated Posts */}
      {generatedPosts && (
        <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
            <MessageSquare className="mr-2 text-emerald-400" size={20} />
            Generated Posts
          </h2>

          {renderGeneratedPosts()}
        </div>
      )}

      {/* Content Calendar */}
      {contentCalendar && (
        <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
          {renderContentCalendar()}
        </div>
      )}

      {/* Content Preview */}
      {content && (
        <div className="bg-zinc-900 p-6 rounded-lg border border-zinc-700">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-white">
            <FileText className="mr-2 text-zinc-400" size={20} />
            Content Preview
          </h2>

          <div className="bg-zinc-800 p-4 rounded-md max-h-64 overflow-y-auto">
            <p className="text-zinc-300 whitespace-pre-wrap">
              {content.length > 500
                ? content.substring(0, 500) + '...'
                : content}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
