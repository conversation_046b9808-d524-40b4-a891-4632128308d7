'use client';

import React, { useState, useEffect } from 'react';
import { usePlanner } from '../../context/PlannerContext';
import { User } from '../../../../admin/planner/types';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

export default function TeamPage() {
  const { users, refreshData } = usePlanner();
  const { data: session } = useSession();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [directUsers, setDirectUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Check if the current user is the SYS_ADMIN (<EMAIL>)
  useEffect(() => {
    console.log('Checking authorization for Team Management page...');
    console.log('Current user email:', session?.user?.email);

    if (session?.user?.email === '<EMAIL>') {
      console.log('User is authorized as SYS_ADMIN');
      setIsAuthorized(true);
    } else {
      console.log('User is NOT authorized as SYS_ADMIN');
      setIsAuthorized(false);
    }
  }, [session]);

  // Add debug logging
  useEffect(() => {
    console.log('Team page - All users:', users);
    console.log('Team page - Filtered users:', users.filter(u => u.isAuthorized === true));
    console.log('Team page users with isAuthorized explicitly set to true:', users.filter(u => u.isAuthorized === true).length);
    console.log('Team page users with isAuthorized undefined or null:', users.filter(u => u.isAuthorized === undefined || u.isAuthorized === null).length);

    // If users array is empty, try to fetch directly from Firestore
    if (users.length === 0) {
      const fetchUsersDirectly = async () => {
        try {
          setLoading(true);
          console.log('No users found in context, fetching directly from Firestore...');
          const { getUsers } = await import('../../../lib/firebase/planner');
          const fetchedUsers = await getUsers();
          console.log(`Fetched ${fetchedUsers.length} users directly from Firestore:`, fetchedUsers);
          setDirectUsers(fetchedUsers);

          // If we got users directly but the context is empty, refresh the context
          if (fetchedUsers.length > 0) {
            console.log('Found users directly from Firestore, refreshing context...');
            refreshData();
          }
        } catch (error) {
          console.error('Error fetching users directly:', error);
        } finally {
          setLoading(false);
        }
      };

      fetchUsersDirectly();
    }
  }, [users, refreshData]);

  // Add a direct Firestore check function
  const checkFirestore = async () => {
    try {
      setLoading(true);
      // Import the getUsers function
      const { getUsers } = await import('../../../lib/firebase/planner');

      // Get all users directly from Firestore
      const fetchedUsers = await getUsers();

      // Log the results
      console.log(`Direct Firestore check: Retrieved ${fetchedUsers.length} users`);
      console.log('Direct Firestore users:', fetchedUsers);

      // Update the directUsers state
      setDirectUsers(fetchedUsers);

      // Alert the results
      alert(`Direct Firestore check: Retrieved ${fetchedUsers.length} users. Check console for details.`);
    } catch (error) {
      console.error('Error checking Firestore:', error);
      alert(`Error checking Firestore: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  // Filter users based on search query and authorization status
  // In production, we'll consider all users as authorized if isAuthorized is not explicitly false
  const filteredUsers = users.filter(user => {
    // Make sure we have a valid user object with required fields
    if (!user || !user.name || !user.email) {
      console.log('Invalid user object:', user);
      return false;
    }

    // In production, consider users authorized unless explicitly set to false
    const isAuthorized = user.isAuthorized !== false;

    const matchesSearch =
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.email.toLowerCase().includes(searchQuery.toLowerCase());

    return isAuthorized && matchesSearch;
  });

  // Use direct users if context users are empty
  const displayUsers = filteredUsers.length > 0 ? filteredUsers : directUsers;

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-900 text-white p-6 rounded-lg shadow-md">
            <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
            <p className="mb-4">You do not have permission to access the Team Management page.</p>
            <p className="mb-6">Only the system administrator (<EMAIL>) can access this page.</p>
            <button
              onClick={() => router.push('/services/admin')}
              className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-md text-white transition-colors"
            >
              Return to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-white">Team Management</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={checkFirestore}
              className="px-3 py-2 bg-purple-800 hover:bg-purple-500 rounded-md flex items-center gap-2 transition-colors"
              disabled={loading}
            >
              <span className="text-sm">Check DB</span>
            </button>
            <button
              onClick={refreshData}
              className="px-3 py-2 bg-green-800 hover:bg-green-500 rounded-md flex items-center gap-2 transition-colors"
              disabled={loading}
            >
              <span className="text-sm">{loading ? 'Refreshing...' : 'Refresh'}</span>
              {loading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              )}
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative mb-4">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>

        {/* Users list */}
        <div className="bg-gray-800 rounded-lg shadow-md p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-white flex items-center">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
              Team Members
            </h2>
            <div className="text-sm text-gray-400">
              {displayUsers.length} {displayUsers.length === 1 ? 'user' : 'users'}
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
              <span className="ml-2 text-gray-400">Loading users...</span>
            </div>
          ) : displayUsers.length === 0 ? (
            <div className="text-center py-8">
              <svg className="w-16 h-16 mx-auto text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
              <p className="mt-4 text-gray-400">No users found. Try refreshing the page.</p>
              <button
                onClick={refreshData}
                className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                Refresh
              </button>
            </div>
          ) : (
            <div className="space-y-2 max-h-[500px] overflow-y-auto">
              {displayUsers.map((user) => (
                <div
                  key={user.id}
                  className="p-3 rounded-md border border-gray-700 hover:bg-gray-700 transition-colors"
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 rounded-full bg-purple-900 flex items-center justify-center overflow-hidden">
                      {user.avatar && (user.avatar.startsWith('http') || user.avatar.startsWith('/')) ? (
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="h-10 w-10 rounded-full object-cover"
                          referrerPolicy="no-referrer"
                        />
                      ) : (
                        <span className="text-lg font-medium text-gray-300">
                          {user.name.charAt(0)}
                        </span>
                      )}
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-200">{user.name}</p>
                      <p className="text-xs text-gray-400">{user.email}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {user.role === 'admin' ? (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-900 text-red-200">
                            Admin
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-900 text-blue-200">
                            User
                          </span>
                        )}
                        {user.availability && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-700 text-gray-300 ml-2">
                            {user.availability}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Debug info */}
        <div className="mt-8 bg-gray-800 rounded-lg shadow-md p-4">
          <h2 className="text-lg font-semibold text-white mb-2">Debug Information</h2>
          <div className="text-sm text-gray-400">
            <p>Context users: {users.length}</p>
            <p>Direct users: {directUsers.length}</p>
            <p>Filtered users: {filteredUsers.length}</p>
            <p>Display users: {displayUsers.length}</p>
            <p>Session user: {session?.user?.email || 'Not signed in'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}