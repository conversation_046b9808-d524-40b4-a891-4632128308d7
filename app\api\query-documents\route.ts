import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { queryDocumentsTool } from '../../../lib/tools/queryDocumentsTool';
import { markdownRendererTool } from '../../../lib/tools/markdown-renderer-tool';

interface RequestBody {
  query: string;
  filename?: string;
  category?: string;
  useInternetSearch?: boolean;
}

export async function POST(req: NextRequest) {
  try {
    // Auth validation
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Request body validation
    const body: RequestBody = await req.json();
    const { query, filename, category, useInternetSearch = false } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    if (!filename && !category) {
      return NextResponse.json(
        { success: false, error: 'Either filename or category is required' },
        { status: 400 }
      );
    }

    // Process the query with the queryDocumentsTool
    const userId = session.user.email;

    console.log('Processing query:', {
      query,
      userId,
      filename,
      category,
      useInternetSearch
    });

    try {
      const result = await queryDocumentsTool.process({
        query,
        userId,
        filename,
        category,
        useInternetSearch
      });

      console.log('Query result:', {
        success: result.success,
        contentLength: result.content?.length || 0,
        error: result.error,
        metadata: result.metadata
      });

      // Process the content with markdown-renderer-tool
      if (result.success && result.content) {
        try {
          const markdownResult = await markdownRendererTool.process({
            markdown: result.content,
            operation: 'preprocess'
          });

          if (markdownResult.success) {
            result.content = markdownResult.content;
          } else {
            console.warn("Failed to process content with markdown-renderer-tool:", markdownResult.error);
          }
        } catch (error) {
          console.error("Error processing content with markdown-renderer-tool:", error);
        }
      }

      // Return the result
      return NextResponse.json(result);
    } catch (processingError) {
      console.error('Error in queryDocumentsTool.process:', processingError);
      return NextResponse.json({
        success: false,
        error: processingError instanceof Error ? processingError.message : 'Error processing query'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in query-documents API:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}
