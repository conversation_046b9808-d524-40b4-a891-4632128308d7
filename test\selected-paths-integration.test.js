/**
 * Integration Test for Selected Paths Feature
 * 
 * This test verifies that the selectedPaths feature works end-to-end:
 * 1. Frontend sends rootPath + selectedPaths
 * 2. Backend passes selectedPaths to CodebaseIndexingTool
 * 3. Tool only scans the selected paths instead of entire rootPath
 */

const { CodebaseIndexingTool } = require('../lib/tools/codebase-indexing-tool');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

describe('Selected Paths Integration Test', () => {
  let tempDir;
  let indexingTool;

  beforeAll(async () => {
    // Create a temporary directory structure for testing
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'selected-paths-test-'));
    
    // Create test directory structure
    await fs.mkdir(path.join(tempDir, 'src'), { recursive: true });
    await fs.mkdir(path.join(tempDir, 'components'), { recursive: true });
    await fs.mkdir(path.join(tempDir, 'lib'), { recursive: true });
    await fs.mkdir(path.join(tempDir, 'node_modules'), { recursive: true });
    await fs.mkdir(path.join(tempDir, 'dist'), { recursive: true });

    // Create test files
    await fs.writeFile(path.join(tempDir, 'src', 'index.ts'), 'export const main = () => console.log("main");');
    await fs.writeFile(path.join(tempDir, 'src', 'utils.ts'), 'export const helper = () => "helper";');
    await fs.writeFile(path.join(tempDir, 'components', 'Button.tsx'), 'export const Button = () => <button>Click</button>;');
    await fs.writeFile(path.join(tempDir, 'components', 'Modal.tsx'), 'export const Modal = () => <div>Modal</div>;');
    await fs.writeFile(path.join(tempDir, 'lib', 'api.ts'), 'export const fetchData = async () => {};');
    await fs.writeFile(path.join(tempDir, 'node_modules', 'package.js'), 'module.exports = {};');
    await fs.writeFile(path.join(tempDir, 'dist', 'bundle.js'), 'console.log("bundled");');

    // Mock storage tool
    indexingTool = new CodebaseIndexingTool({
      savePdfToByteStore: jest.fn().mockResolvedValue('mock-document-id')
    });
  });

  afterAll(async () => {
    // Clean up temp directory
    await fs.rmdir(tempDir, { recursive: true });
  });

  describe('findCodeFiles with selectedPaths', () => {
    test('should scan entire rootPath when no selectedPaths provided', async () => {
      const files = await indexingTool.findCodeFiles(
        tempDir,
        ['node_modules', 'dist'], // exclude patterns
        ['.ts', '.tsx'], // include extensions
        false // verbose
        // no selectedPaths - should scan entire rootPath
      );

      // Should find files from src, components, and lib (but not node_modules or dist)
      expect(files).toHaveLength(4);
      expect(files.some(f => f.includes('src/index.ts'))).toBe(true);
      expect(files.some(f => f.includes('src/utils.ts'))).toBe(true);
      expect(files.some(f => f.includes('components/Button.tsx'))).toBe(true);
      expect(files.some(f => f.includes('components/Modal.tsx'))).toBe(true);
      expect(files.some(f => f.includes('lib/api.ts'))).toBe(true);
      
      // Should not find excluded files
      expect(files.some(f => f.includes('node_modules'))).toBe(false);
      expect(files.some(f => f.includes('dist'))).toBe(false);
    });

    test('should only scan selectedPaths when provided', async () => {
      const selectedPaths = [
        path.join(tempDir, 'src'),
        path.join(tempDir, 'components')
      ];

      const files = await indexingTool.findCodeFiles(
        tempDir,
        ['node_modules', 'dist'], // exclude patterns
        ['.ts', '.tsx'], // include extensions
        false, // verbose
        selectedPaths // only scan these paths
      );

      // Should only find files from src and components directories
      expect(files).toHaveLength(4);
      expect(files.some(f => f.includes('src/index.ts'))).toBe(true);
      expect(files.some(f => f.includes('src/utils.ts'))).toBe(true);
      expect(files.some(f => f.includes('components/Button.tsx'))).toBe(true);
      expect(files.some(f => f.includes('components/Modal.tsx'))).toBe(true);
      
      // Should NOT find files from lib directory (not in selectedPaths)
      expect(files.some(f => f.includes('lib/api.ts'))).toBe(false);
    });

    test('should handle single file selection', async () => {
      const selectedPaths = [
        path.join(tempDir, 'src', 'index.ts')
      ];

      const files = await indexingTool.findCodeFiles(
        tempDir,
        ['node_modules', 'dist'],
        ['.ts', '.tsx'],
        false,
        selectedPaths
      );

      // Should only find the single selected file
      expect(files).toHaveLength(1);
      expect(files[0]).toContain('src/index.ts');
    });

    test('should handle mixed file and directory selection', async () => {
      const selectedPaths = [
        path.join(tempDir, 'src', 'index.ts'), // single file
        path.join(tempDir, 'components') // entire directory
      ];

      const files = await indexingTool.findCodeFiles(
        tempDir,
        ['node_modules', 'dist'],
        ['.ts', '.tsx'],
        false,
        selectedPaths
      );

      // Should find the single file + all files in components directory
      expect(files).toHaveLength(3);
      expect(files.some(f => f.includes('src/index.ts'))).toBe(true);
      expect(files.some(f => f.includes('components/Button.tsx'))).toBe(true);
      expect(files.some(f => f.includes('components/Modal.tsx'))).toBe(true);
      
      // Should NOT find other files
      expect(files.some(f => f.includes('src/utils.ts'))).toBe(false);
      expect(files.some(f => f.includes('lib/api.ts'))).toBe(false);
    });

    test('should handle non-existent selectedPaths gracefully', async () => {
      const selectedPaths = [
        path.join(tempDir, 'non-existent-dir'),
        path.join(tempDir, 'src') // this one exists
      ];

      const files = await indexingTool.findCodeFiles(
        tempDir,
        ['node_modules', 'dist'],
        ['.ts', '.tsx'],
        false,
        selectedPaths
      );

      // Should only find files from the existing path
      expect(files).toHaveLength(2);
      expect(files.some(f => f.includes('src/index.ts'))).toBe(true);
      expect(files.some(f => f.includes('src/utils.ts'))).toBe(true);
    });
  });

  describe('indexCodebaseDirect with selectedPaths', () => {
    test('should pass selectedPaths to findCodeFiles', async () => {
      // Mock the findCodeFiles method to verify it receives selectedPaths
      const originalFindCodeFiles = indexingTool.findCodeFiles;
      const mockFindCodeFiles = jest.fn().mockResolvedValue([
        path.join(tempDir, 'src', 'index.ts')
      ]);
      indexingTool.findCodeFiles = mockFindCodeFiles;

      const selectedPaths = [path.join(tempDir, 'src')];

      try {
        await indexingTool.indexCodebaseDirect({
          rootPath: tempDir,
          userId: 'test-user',
          projectName: 'test-project',
          selectedPaths: selectedPaths,
          verbose: false
        });

        // Verify that findCodeFiles was called with selectedPaths
        expect(mockFindCodeFiles).toHaveBeenCalledWith(
          tempDir,
          expect.any(Array), // excludePatterns
          expect.any(Array), // includeExtensions
          false, // verbose
          selectedPaths // selectedPaths should be passed
        );
      } finally {
        // Restore original method
        indexingTool.findCodeFiles = originalFindCodeFiles;
      }
    });
  });
});

// Mock external dependencies
jest.mock('../lib/tools/google-ai', () => ({
  processWithGoogleAI: jest.fn().mockResolvedValue('Mock application context analysis')
}));

jest.mock('../lib/tools/vector-embeddings', () => ({
  vectorEmbeddingTool: {
    embedAndStore: jest.fn().mockResolvedValue({
      success: true,
      documentId: 'mock-doc-id',
      chunkIds: ['chunk1', 'chunk2']
    })
  }
}));

jest.mock('../lib/tools/storage-tool', () => ({
  StorageTool: jest.fn().mockImplementation(() => ({
    savePdfToByteStore: jest.fn().mockResolvedValue('mock-document-id')
  }))
}));
