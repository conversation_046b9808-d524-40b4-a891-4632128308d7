// Utility function to get a consistent color for a category
// This ensures the same category always gets the same color

// Define color classes for categories
export const categoryColors = [
  "bg-purple-500 text-white",
  "bg-blue-500 text-white",
  "bg-green-500 text-white",
  "bg-yellow-500 text-black",
  "bg-red-500 text-white",
  "bg-indigo-500 text-white",
  "bg-pink-500 text-white",
  "bg-teal-500 text-white",
  "bg-orange-500 text-white",
  "bg-cyan-500 text-white",
  "bg-lime-500 text-black",
  "bg-emerald-500 text-white",
  "bg-violet-500 text-white",
  "bg-fuchsia-500 text-white",
  "bg-rose-500 text-white",
  "bg-amber-500 text-black",
];

// Simple hash function to convert a string to a number
const hashString = (str: string): number => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
};

// Get a consistent color class for a category
export const getCategoryColorClass = (category: string): string => {
  const index = hashString(category) % categoryColors.length;
  return categoryColors[index];
};
