// Config.tsx

interface ApiConfigHeaders {
    "Content-Type": string;
    [key: string]: string;
}

interface ApiConfig {
    endpoint: string;
    endpoint2: string;
    endpoint3: string;
    headers: ApiConfigHeaders;
}

export const API_CONFIG: ApiConfig = {
    endpoint: process.env.FOLLOWUPFLOWISE || "https://flowiseai-railway-production-28a57.up.railway.app/api/v1/prediction/************************************",
    endpoint2: process.env.RECIPES_FLOWISE || "https://flowiseai-railway-production-28a57.up.railway.app/api/v1/prediction/************************************",
    endpoint3: process.env.CLIENT_FLOWISE || "https://flowiseai-railway-production-28a57.up.railway.app/api/v1/prediction/************************************",
    headers: {
        "Content-Type": "application/json"
    }
};
