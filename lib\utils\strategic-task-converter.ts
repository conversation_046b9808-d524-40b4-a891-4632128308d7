/**
 * Strategic Task Converter Utility
 *
 * Centralized conversion logic for Strategic Director Agent task management.
 * This module contains all conversion functions between different task formats.
 */

import { Task } from '../../admin/planner/types';
import { StrategicTaskMapper } from './strategic-task-mapper';
import { StrategicTaskFormatter } from './strategic-task-formatter';

// Type definitions (extracted from StrategicDirectorAgent)
export interface StrategicTask {
  id: string;
  title: string;
  description: string;
  category: 'Market Intelligence' | 'Product Analysis' | 'Customer Intelligence' | 'Marketing Infrastructure' | 'Performance Metrics' | 'Customer Validation' | 'Strategic Planning' | 'Implementation' | 'Research' | 'Content Creation';
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  assignedTeam: 'Research Team' | 'Business Analysis Team' | 'Marketing Team' | 'Sales Team' | 'Software Design Team' | 'Content Team' | 'Strategic Director';
  status: 'IDENTIFIED' | 'ASSIGNED' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'BLOCKED' | 'CANCELLED';
  specificRequirements: string[];
  deliverable: string;
  timeline: {
    estimatedDuration: string;
    startDate?: Date;
    dueDate?: Date;
  };
  dependencies?: string[];
  blockers?: string[];
  successCriteria: string[];
  resources?: {
    budget?: number;
    tools?: string[];
    personnel?: string[];
    documents?: string[];
  };
  metadata: {
    createdAt: Date;
    createdBy: string;
    updatedAt: Date;
    source: 'Information Gap Analysis' | 'Strategic Planning' | 'PMO Requirements' | 'User Request' | 'System Generated';
    pmoId?: string;
    projectId?: string;
    requestId?: string;
  };
}

export interface StrategicTaskCollection {
  collectionId: string;
  name: string;
  description: string;
  source: string;
  totalTasks: number;
  tasks: StrategicTask[];
  overallTimeline: {
    startDate: Date;
    estimatedEndDate: Date;
    criticalPath: string[];
  };
  teamAssignments: {
    [teamName: string]: {
      taskCount: number;
      taskIds: string[];
      estimatedWorkload: string;
    };
  };
  successMetrics: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskGenerationContext {
  userRequest: string;
  analysisContent: string;
  pmoContext?: {
    pmoId: string;
    projectTitle: string;
    priority: string;
    category: string;
  };
  identifiedGaps?: any[];
  strategicObjectives?: string[];
  constraints?: string[];
}

/**
 * Strategic Task Converter Class
 * Contains all conversion logic for strategic task management
 */
export class StrategicTaskConverter {

  /**
   * Convert LLM-generated task data to StrategicTask objects
   */
  static convertLLMTasksToStrategicTasks(llmTasks: any[], context: TaskGenerationContext): StrategicTask[] {
    const tasks: StrategicTask[] = [];
    const currentDate = new Date();

    llmTasks.forEach((llmTask, index) => {
      try {
        const task: StrategicTask = {
          id: `llm-strategic-task-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
          title: llmTask.title || `Strategic Task ${index + 1}`,
          description: llmTask.description || llmTask.title || '',
          category: StrategicTaskMapper.mapTaskCategory(llmTask.category || 'Strategic Planning'),
          priority: StrategicTaskMapper.mapPriorityText(llmTask.priority || 'MEDIUM'),
          assignedTeam: StrategicTaskMapper.normalizeTeamName(llmTask.assignedTeam || 'Strategic Director') as any,
          status: 'IDENTIFIED',
          specificRequirements: Array.isArray(llmTask.specificRequirements)
            ? llmTask.specificRequirements
            : [llmTask.description || llmTask.title],
          deliverable: llmTask.deliverable || `Completed: ${llmTask.title}`,
          timeline: {
            estimatedDuration: llmTask.timeline || '1 week',
            startDate: currentDate,
            dueDate: new Date(currentDate.getTime() + (this.parseTimelineToWeeks(llmTask.timeline || '1 week') * 7 * 24 * 60 * 60 * 1000))
          },
          dependencies: Array.isArray(llmTask.dependencies) ? llmTask.dependencies : undefined,
          successCriteria: Array.isArray(llmTask.successCriteria)
            ? llmTask.successCriteria
            : [
                `Complete all requirements: ${Array.isArray(llmTask.specificRequirements) ? llmTask.specificRequirements.join(', ') : llmTask.description}`,
                `Deliver ${llmTask.deliverable || llmTask.title}`,
                'Meet strategic quality standards'
              ],
          metadata: {
            createdAt: currentDate,
            createdBy: 'Strategic Director LLM',
            updatedAt: currentDate,
            source: 'Strategic Planning',
            pmoId: context.pmoContext?.pmoId,
            projectId: context.pmoContext?.projectTitle
          }
        };

        tasks.push(task);
        console.log(`StrategicTaskConverter: Created LLM strategic task "${task.title}" assigned to ${task.assignedTeam}`);
      } catch (taskError) {
        console.error(`StrategicTaskConverter: Error creating strategic task from LLM data:`, taskError, llmTask);
      }
    });

    return tasks;
  }

  /**
   * Convert StrategicTaskCollection to standard Task format for compatibility
   * This ensures compatibility with existing createTasksAgent, pmoProjectsTaskAgent, and API endpoints
   */
  static convertStrategicTasksToStandardTasks(collection: StrategicTaskCollection, projectId: string): Array<Omit<Task, 'id'>> {
    console.log(`StrategicTaskConverter: Converting ${collection.totalTasks} strategic tasks to standard Task format for project ${projectId}`);

    const standardTasks: Array<Omit<Task, 'id'>> = [];

    collection.tasks.forEach((strategicTask) => {
      // Map StrategicTask to standard Task interface
      const standardTask: Omit<Task, 'id'> = {
        projectId,
        title: strategicTask.title,
        description: StrategicTaskFormatter.formatTaskDescription(strategicTask),
        category: strategicTask.category,
        status: StrategicTaskMapper.mapStrategicStatusToTaskStatus(strategicTask.status as any),
        startDate: strategicTask.timeline.startDate || new Date(),
        dueDate: strategicTask.timeline.dueDate || new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // Default 2 weeks
        assignedTo: StrategicTaskMapper.mapTeamToUserIds(strategicTask.assignedTeam),
        priority: StrategicTaskMapper.mapStrategicPriorityToTaskPriority(strategicTask.priority as any),
        dependencies: strategicTask.dependencies || [],
        notes: StrategicTaskFormatter.formatTaskNotes(strategicTask, collection),
        createdBy: 'strategic-director-agent',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      standardTasks.push(standardTask);
    });

    console.log(`StrategicTaskConverter: Successfully converted ${standardTasks.length} tasks to standard format`);
    return standardTasks;
  }

  /**
   * Parse timeline string to weeks (e.g., "2 weeks" -> 2, "1 week" -> 1)
   */
  private static parseTimelineToWeeks(timeline: string): number {
    const match = timeline.match(/(\d+)\s*weeks?/i);
    if (match) {
      return parseInt(match[1], 10);
    }

    // Default to 2 weeks if parsing fails
    return 2;
  }

  /**
   * Create static strategic planning tasks when LLM fails
   */
  static createStaticStrategicPlanningTasks(context: TaskGenerationContext): StrategicTask[] {
    console.log(`StrategicTaskConverter: Creating static fallback strategic planning tasks`);

    const tasks: StrategicTask[] = [];
    const currentDate = new Date();

    // Core strategic planning tasks (original implementation)
    const coreStrategicTasks: Array<{
      category: 'Strategic Planning' | 'Implementation';
      title: string;
      description: string;
      assignedTeam: 'Strategic Director';
      priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
      timeline: string;
      requirements: string[];
      deliverable: string;
    }> = [
      {
        category: 'Strategic Planning' as const,
        title: 'Strategic Analysis Synthesis',
        description: 'Synthesize all gathered information into comprehensive strategic recommendations',
        assignedTeam: 'Strategic Director' as const,
        priority: 'CRITICAL' as const,
        timeline: '1 week',
        requirements: [
          'Review all team deliverables',
          'Identify strategic opportunities and threats',
          'Develop comprehensive strategic recommendations',
          'Create implementation roadmap'
        ],
        deliverable: 'Comprehensive Strategic Analysis Report'
      },
      {
        category: 'Implementation' as const,
        title: 'Implementation Planning',
        description: 'Create detailed implementation plan with timelines and resource allocation',
        assignedTeam: 'Strategic Director' as const,
        priority: 'HIGH' as const,
        timeline: '1 week',
        requirements: [
          'Define implementation phases and milestones',
          'Allocate resources and responsibilities',
          'Establish success metrics and KPIs',
          'Create risk mitigation strategies'
        ],
        deliverable: 'Strategic Implementation Plan'
      }
    ];

    // Add PMO-specific task if context is from PMO
    if (context.pmoContext) {
      coreStrategicTasks.push({
        category: 'Strategic Planning' as const,
        title: 'PMO Strategic Alignment',
        description: 'Ensure strategic recommendations align with PMO objectives and constraints',
        assignedTeam: 'Strategic Director' as const,
        priority: 'HIGH' as const,
        timeline: '3 days',
        requirements: [
          'Review PMO requirements and constraints',
          'Validate strategic alignment with organizational goals',
          'Ensure compliance with PMO standards',
          'Document strategic rationale and justification'
        ],
        deliverable: 'PMO Strategic Alignment Document'
      });
    }

    // Convert to StrategicTask format
    coreStrategicTasks.forEach((taskTemplate, index) => {
      const timelineWeeks = this.parseTimelineToWeeks(taskTemplate.timeline);
      const dueDate = new Date(currentDate.getTime() + (timelineWeeks * 7 * 24 * 60 * 60 * 1000));

      const task: StrategicTask = {
        id: `static-strategic-task-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`,
        title: taskTemplate.title,
        description: taskTemplate.description,
        category: taskTemplate.category,
        priority: taskTemplate.priority,
        assignedTeam: taskTemplate.assignedTeam,
        status: 'IDENTIFIED',
        specificRequirements: taskTemplate.requirements,
        deliverable: taskTemplate.deliverable,
        timeline: {
          estimatedDuration: taskTemplate.timeline,
          startDate: currentDate,
          dueDate: dueDate
        },
        successCriteria: [
          `Complete all requirements: ${taskTemplate.requirements.join(', ')}`,
          `Deliver ${taskTemplate.deliverable} within ${taskTemplate.timeline}`,
          'Meet strategic quality standards'
        ],
        metadata: {
          createdAt: currentDate,
          createdBy: 'Strategic Director Static Fallback',
          updatedAt: currentDate,
          source: 'Strategic Planning',
          pmoId: context.pmoContext?.pmoId,
          projectId: context.pmoContext?.projectTitle
        }
      };

      tasks.push(task);
    });

    return tasks;
  }
}
