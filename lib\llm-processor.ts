import { llmTool, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./tools/llm-tool";

/**
 * Options for processing content with an LLM
 */
export interface ProcessContentOptions {
  model?: string;
  provider?: LlmProvider;
}

/**
 * Processes raw content with an LLM to extract meaningful text
 * @param content - The raw content to process
 * @param url - The URL of the content source
 * @param options - Optional processing options
 * @param options.model - The model to use (default: "gpt-4o")
 * @param options.provider - The LLM provider (default: "openai")
 * @returns The processed content in Markdown
 */
export async function processContentWithLLM(
  content: string,
  url: string,
  options: ProcessContentOptions = {}
): Promise<string> {
  try {
    const { model = "gpt-4o", provider = "openai" } = options;

    // Create the prompt template
    const promptTemplate = `
You are a content extraction expert specializing in customer support documentation.
Your task is to extract meaningful content from the following webpage text
and convert it into a well-structured Markdown document for a Customer Support Knowledge Base.

The content is from: ${url}

Focus on:
1. Main article content relevant to product usage
2. Important headings and subheadings
3. Key information useful for customer support staff, such as:
   - Product features and specifications
   - Pricing and availability
   - Usage instructions
   - Troubleshooting steps
   - FAQs
   - Contact information
4. Removing navigation elements, advertisements, and other non-essential content

Format the output as a clean Markdown document with proper headings (e.g., #, ##), lists, and paragraphs. Ensure the content is concise and actionable for support purposes.

Here is the content:
{chunk}
`;

    // Process the content using the LLM tool
    const processedContent = await llmTool.processLargeContent({
      content,
      prompt: promptTemplate,
      model,
      provider,
      maxChunkSize: 4000,
      maxChunks: 10
    });

    // Return processed content or a fallback if empty
    return processedContent || `No meaningful content extracted from ${url}.`;
  } catch (error) {
    console.error("Error processing content with LLM:", error);
    // Return fallback content instead of throwing error
    return `Error processing content from ${url}: Unable to extract meaningful information.`;
  }
}