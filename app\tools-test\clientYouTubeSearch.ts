/**
 * Client-side YouTube search implementation
 * This approach ensures the HTTP referrer header is properly included in the request
 */

// Define interfaces for the YouTube API response
interface YouTubeVideoItem {
  id: {
    videoId: string;
  };
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      high?: { url: string };
      default?: { url: string };
    };
    channelTitle: string;
    publishedAt: string;
  };
  statistics?: {
    viewCount?: string;
    likeCount?: string;
    commentCount?: string;
  };
  contentDetails?: {
    duration?: string;
  };
}

export interface YouTubeSearchResult {
  title: string;
  description: string;
  videoId: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  statistics?: {
    viewCount?: string;
    likeCount?: string;
    commentCount?: string;
  };
  contentDetails?: {
    duration?: string;
  };
}

export interface SearchResponse {
  success: boolean;
  results: YouTubeSearchResult[];
  metadata: {
    source: string;
    searchTime?: number;
    resultCount?: number;
    enhancedQuery?: string;
    error?: string;
    aborted?: boolean;
  };
}

/**
 * Perform a YouTube search directly from the client
 * This ensures the HTTP referrer header is included in the request
 */
export async function clientYouTubeSearch(query: string, maxResults: number = 3): Promise<SearchResponse> {
  try {
    const startTime = Date.now();
    
    // First, search for videos using our API endpoint
    // The browser will automatically include the referrer header
    const searchUrl = `/api/youtube-search?q=${encodeURIComponent(query)}&maxResults=${maxResults}`;
    const response = await fetch(searchUrl);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || errorData.details || `Error: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Check if we have items in the response
    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      throw new Error('No videos found matching your search criteria');
    }
    
    // Transform the API response to match our SearchResponse interface
    const videoResults: YouTubeSearchResult[] = data.items.map((item: YouTubeVideoItem) => ({
      title: item.snippet.title,
      description: item.snippet.description,
      videoId: item.id.videoId,
      thumbnailUrl: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url || '',
      channelTitle: item.snippet.channelTitle,
      publishedAt: item.snippet.publishedAt,
      statistics: item.statistics || {
        viewCount: '0',
        likeCount: '0',
        commentCount: '0'
      },
      contentDetails: item.contentDetails || {
        duration: 'PT0M0S'
      }
    }));
    
    return {
      success: true,
      results: videoResults,
      metadata: {
        source: 'youtube_api',
        searchTime: Date.now() - startTime,
        resultCount: videoResults.length,
        enhancedQuery: query
      }
    };
  } catch (error) {
    console.error('YouTube search error:', error);
    return {
      success: false,
      results: [],
      metadata: {
        source: 'youtube_api',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    };
  }
}
