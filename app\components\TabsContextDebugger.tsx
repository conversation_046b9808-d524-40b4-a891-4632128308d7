'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { usePlanner } from '../context/PlannerContext';
import { useAuth } from '../context/AuthContext';
import { Task } from '../../admin/planner/types';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import TaskComments from './TaskComments';
import { Calendar, Search, ArrowUpDown } from 'lucide-react';

// Define a type for assignee objects that might be in the assignedTo array
type AssigneeObject = {
  email?: string;
  id?: string;
  [key: string]: any;
};

export default function TabsContextDebugger() {
  const plannerContext = usePlanner();
  const { user } = useAuth();
  // Always show task summary tab
  const activeTab = 'taskSummary';
  const [commentsTab, setCommentsTab] = useState<'comments' | 'documents'>('documents');
  const [chartTab, setChartTab] = useState<'status' | 'priority'>('status');
  const [userTasks, setUserTasks] = useState<Task[]>([]);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [showTaskPopup, setShowTaskPopup] = useState(false);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchQuery, setSearchQuery] = useState('');

  // Close the task popup
  const closeTaskPopup = () => {
    setShowTaskPopup(false);
    setSelectedTask(null);
  };

  // Handle task click to show popup
  const handleTaskClick = (task: Task) => {
    setSelectedTask(task);
    setShowTaskPopup(true);
  };

  // Navigate to the task's project page
  const goToTask = () => {
    if (selectedTask) {
      window.location.href = `/services/admin/planner/${selectedTask.projectId}`;
    }
  };

  // Navigate to edit task page
  const editTask = () => {
    if (selectedTask) {
      // Navigate to the project page with a query parameter to open the edit task modal
      closeTaskPopup(); // Close the popup first for better UX
      window.location.href = `/services/admin/planner/${selectedTask.projectId}?editTask=${selectedTask.id}`;
    }
  };

  // Add keyboard event listener for Escape key to close popup
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showTaskPopup) {
        closeTaskPopup();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [showTaskPopup, closeTaskPopup]);

  // Helper function to normalize task status
  const normalizeTaskStatus = (status: string): string => {
    const normalizedStatus = status.toLowerCase();
    if (normalizedStatus === 'not started') return 'Not Started';
    if (normalizedStatus === 'in progress') return 'In Progress';
    if (normalizedStatus === 'reviewed') return 'Reviewed';
    if (normalizedStatus === 'complete' || normalizedStatus === 'completed') return 'Complete';
    return status; // Default fallback
  };

  // Filter tasks assigned to the current user and sort by due date
  useEffect(() => {
    if (user && plannerContext.tasks) {
      const currentUserEmail = user.email;

      console.log('Current user email for filtering tasks:', currentUserEmail);

      if (!currentUserEmail) {
        console.warn('No email found for current user');
        setUserTasks([]);
        return;
      }

      // Check if assignedTo contains either the user's email or ID
      const filteredTasks = plannerContext.tasks
        .filter((task: Task) => {
          // Check if the task's assignedTo array includes the user's email
          const isAssigned = task.assignedTo.some(assignee => {
            // It could be an email directly (case insensitive)
            if (typeof assignee === 'string' && assignee.toLowerCase() === currentUserEmail.toLowerCase())
              return true;

            // It could be an object with an email or id property
            if (typeof assignee === 'object' && assignee !== null) {
              // Type guard to treat assignee as an AssigneeObject
              const assigneeObj = assignee as AssigneeObject;

              if (assigneeObj.email && assigneeObj.email.toLowerCase() === currentUserEmail.toLowerCase())
                return true;
              if (assigneeObj.id) {
                const currentUser = plannerContext.users.find(u => u.email === currentUserEmail);
                if (currentUser && assigneeObj.id === currentUser.id) return true;
              }
            }

            // Or it could be a user ID
            if (plannerContext.users) {
              const currentUser = plannerContext.users.find(u => u.email === currentUserEmail);
              if (currentUser && assignee === currentUser.id) return true;
            }

            return false;
          });

          return isAssigned;
        })
        .sort((a: Task, b: Task) => {
          const dateA = new Date(a.dueDate);
          const dateB = new Date(b.dueDate);
          return dateA.getTime() - dateB.getTime();
        });

      // Debug: Log all tasks assigned to the user
      console.log('All user tasks:', filteredTasks);
      console.log('Completed tasks:', filteredTasks.filter(t =>
        normalizeTaskStatus(t.status) === 'Complete'
      ));

      setUserTasks(filteredTasks);
    }
  }, [user, plannerContext.tasks, plannerContext.users]);

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get task status color
  const getStatusColor = (status: string) => {
    // Normalize status to handle variations
    const normalizedStatus = status.toLowerCase();

    if (normalizedStatus === 'not started') {
      return 'bg-gray-500';
    } else if (normalizedStatus === 'in progress') {
      return 'bg-blue-500';
    } else if (normalizedStatus === 'reviewed') {
      return 'bg-yellow-500';
    } else if (normalizedStatus === 'complete' || normalizedStatus === 'completed') {
      return 'bg-green-500';
    } else {
      return 'bg-gray-500';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Low':
        return 'text-gray-400';
      case 'Medium':
        return 'text-blue-400';
      case 'High':
        return 'text-yellow-400';
      case 'Critical':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // Toggle sort direction
  const toggleSort = () => {
    setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
  };

  // Get sorted and filtered tasks
  const getSortedAndFilteredTasks = useMemo(() => {
    // First filter by search query
    const filteredTasks = userTasks.filter(task =>
      searchQuery === '' ||
      task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      task.description.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Then sort by title
    return [...filteredTasks].sort((a, b) => {
      const comparison = a.title.localeCompare(b.title);
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [userTasks, searchQuery, sortDirection]);

  // Prepare chart data for tasks by project and status
  const statusChartData = useMemo(() => {
    if (!plannerContext.tasks || plannerContext.tasks.length === 0) return [];

    // Group tasks by project
    const projectMap = new Map();

    // Get unique project names
    const uniqueProjects = Array.from(new Set(plannerContext.tasks.map(task => {
      // Get project name from projectId
      const project = plannerContext.projects.find(p => p.id === task.projectId);
      return project ? project.name : 'Unassigned';
    })));

    // Initialize project data
    uniqueProjects.forEach(project => {
      projectMap.set(project, {
        name: project,
        'Not Started': 0,
        'In Progress': 0,
        'Reviewed': 0,
        'Complete': 0
      });
    });

    // Count tasks by status for each project
    plannerContext.tasks.forEach(task => {
      // Get project name from projectId
      const project = plannerContext.projects.find(p => p.id === task.projectId);
      const projectName = project ? project.name : 'Unassigned';
      const status = normalizeTaskStatus(task.status);

      const projectData = projectMap.get(projectName);
      if (projectData && projectData[status] !== undefined) {
        projectData[status] += 1;
      } else if (projectData) {
        // Default to Not Started if status is not recognized
        projectData['Not Started'] += 1;
      }
    });

    return Array.from(projectMap.values());
  }, [plannerContext.tasks, plannerContext.projects]);

  // Prepare chart data for tasks by project and priority
  const priorityChartData = useMemo(() => {
    if (!plannerContext.tasks || plannerContext.tasks.length === 0) return [];

    // Group tasks by project
    const projectMap = new Map();

    // Get unique project names
    const uniqueProjects = Array.from(new Set(plannerContext.tasks.map(task => {
      // Get project name from projectId
      const project = plannerContext.projects.find(p => p.id === task.projectId);
      return project ? project.name : 'Unassigned';
    })));

    // Initialize project data
    uniqueProjects.forEach(project => {
      projectMap.set(project, {
        name: project,
        'Low': 0,
        'Medium': 0,
        'High': 0,
        'Critical': 0
      });
    });

    // Count tasks by priority for each project
    plannerContext.tasks.forEach(task => {
      // Get project name from projectId
      const project = plannerContext.projects.find(p => p.id === task.projectId);
      const projectName = project ? project.name : 'Unassigned';
      const priority = task.priority || 'Medium';

      const projectData = projectMap.get(projectName);
      if (projectData && projectData[priority] !== undefined) {
        projectData[priority] += 1;
      } else if (projectData) {
        // Default to Medium if priority is not recognized
        projectData['Medium'] += 1;
      }
    });

    return Array.from(projectMap.values());
  }, [plannerContext.tasks, plannerContext.projects]);


  return (
    <div className="bg-gray-800 rounded-lg p-4 mb-4  mt-6 text-sm">
      {/* Task Summary Header */}
      <div className="flex border-b border-gray-700 mb-4">
        <h3 className="px-4 py-2 font-medium text-white border-b-2 border-purple-500">
          Task Summary
        </h3>
      </div>

      {/* Task Summary Content */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Left side - Tasks assigned to user */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <h3 className="text-white font-medium ml-2">Your Tasks</h3>
                <div className="ml-2 bg-purple-600 text-white text-xs px-2 py-0.5 rounded-full">
                  {userTasks.length}
                </div>
              </div>
              <div className="flex items-center space-x-2 ml-auto mr-2">
                <button
                  onClick={toggleSort}
                  className="bg-gray-700 hover:bg-gray-600 text-white text-xs px-2 py-1 rounded flex items-center"
                  title={sortDirection === 'asc' ? 'Sort A to Z' : 'Sort Z to A'}
                >
                  <span>Sort</span>
                  <ArrowUpDown className={`h-3 w-3 ml-1 ${sortDirection === 'desc' ? 'text-purple-400' : 'text-gray-400'}`} />
                </button>
                <div className="relative">
                  <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400">
                    <Search className="h-3 w-3" />
                  </div>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search tasks..."
                    className="bg-gray-700 hover:bg-gray-600 focus:bg-gray-600 text-white text-xs pl-7 pr-6 py-1 rounded w-32 focus:outline-none focus:ring-1 focus:ring-purple-500"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            </div>
            <div className="bg-gray-700 p-3 rounded">
              {userTasks.length === 0 ? (
                <p className="text-gray-400">No tasks assigned to you</p>
              ) : searchQuery && getSortedAndFilteredTasks.length === 0 ? (
                <p className="text-gray-400">No tasks match your search</p>
              ) : (
                <div className="space-y-2 h-[calc(100%-24px)] overflow-y-auto pr-2" style={{ height: '80vh' }}>
                  {getSortedAndFilteredTasks.map((task) => (
                    <div
                      key={task.id}
                      className={`${normalizeTaskStatus(task.status) === 'Complete' ? 'bg-green-900/30' : 'bg-gray-700/50'} p-3 rounded cursor-pointer hover:bg-gray-600/50 transition-colors`}
                      onClick={() => handleTaskClick(task)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className={`${normalizeTaskStatus(task.status) === 'Complete' ? 'text-green-300 line-through' : 'text-white'} font-medium`}>{task.title}</h4>
                          <p className="text-xs text-gray-400 mt-1">
                            {task.category} • Due: {formatDate(task.dueDate)}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <span className={`${getPriorityColor(task.priority)} text-xs mr-2`}>
                            {task.priority}
                          </span>
                          <span className={`${getStatusColor(task.status)} px-2 py-1 rounded-full text-xs text-white`}>
                            {task.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Right side - Additional task info */}
          <div>
            <div className="flex items-center mb-2">
              <h3 className="text-amber- font-medium ml-2">All Tasks Statistics</h3>
              <div className="ml-2 bg-green-800 text-green-200  text-xs px-2 py-0.5 rounded-full">
                {plannerContext.tasks.length}
              </div>
            </div>
            <div className="bg-gray-700 p-3 rounded">
              <div className="grid grid-cols-2 gap-2 mb-4">
                <div>
                  <p className="text-gray-400 text-xs">Total Assigned</p>
                  <p className="text-white font-medium">{userTasks.length}</p>
                </div>
                <div>
                  <p className="text-gray-400 text-xs">Not Started</p>
                  <p className="text-white font-medium">
                    {userTasks.filter(t => normalizeTaskStatus(t.status) === 'Not Started').length}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400 text-xs">In Progress</p>
                  <p className="text-white font-medium">
                    {userTasks.filter(t => normalizeTaskStatus(t.status) === 'In Progress').length}
                  </p>
                </div>
                <div>
                  <p className="text-gray-400 text-xs">Completed</p>
                  <p className="text-white font-medium">
                    {userTasks.filter(t => normalizeTaskStatus(t.status) === 'Complete').length}
                  </p>
                </div>
              </div>

              {userTasks.length > 0 && (
                <div className="mb-4">
                  <p className="text-gray-400 text-xs mb-1">Next Deadline</p>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 text-purple-400 mr-2" />
                    <p className="text-white">
                      {formatDate(userTasks[0].dueDate)}
                    </p>
                  </div>
                </div>
              )}

              {/* Chart tabs */}
              <div className="mt-10">
                <div className="flex border-b border-gray-600 mb-3">
                  <button
                    className={`px-3 py-1 text-xs font-medium ${
                      chartTab === 'status'
                        ? 'text-white border-b-2 border-purple-500'
                        : 'text-gray-400 hover:text-gray-300'
                    }`}
                    onClick={() => setChartTab('status')}
                  >
                    By Status
                  </button>
                  <button
                    className={`px-3 py-1 text-xs font-medium ${
                      chartTab === 'priority'
                        ? 'text-white border-b-2 border-purple-500'
                        : 'text-gray-400 hover:text-gray-300'
                    }`}
                    onClick={() => setChartTab('priority')}
                  >
                    By Priority
                  </button>
                </div>

                {/* Chart content */}
                <div className="h-64">
                  {chartTab === 'status' ? (
                    // Status Chart
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={statusChartData}
                        margin={{ top: 5, right: 5, left: 0, bottom: 20 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis
                          dataKey="name"
                          tick={{ fill: '#9ca3af', fontSize: 10 }}
                          angle={-45}
                          textAnchor="end"
                          height={60}
                        />
                        <YAxis tick={{ fill: '#9ca3af' }} />
                        <Tooltip
                          contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                          itemStyle={{ color: '#f9fafb' }}
                          cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
                        />
                        <Legend
                          wrapperStyle={{ fontSize: 10, paddingTop: 5 }}
                          iconSize={8}
                        />
                        <Bar dataKey="Not Started" stackId="a" fill="#6B7280" /> {/* Gray */}
                        <Bar dataKey="In Progress" stackId="a" fill="#3B82F6" /> {/* Blue */}
                        <Bar dataKey="Reviewed" stackId="a" fill="#F59E0B" /> {/* Yellow */}
                        <Bar dataKey="Complete" stackId="a" fill="#10B981" /> {/* Green */}
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    // Priority Chart
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={priorityChartData}
                        margin={{ top: 5, right: 5, left: 0, bottom: 20 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                        <XAxis
                          dataKey="name"
                          tick={{ fill: '#9ca3af', fontSize: 10 }}
                          angle={-45}
                          textAnchor="end"
                          height={60}
                        />
                        <YAxis tick={{ fill: '#9ca3af' }} />
                        <Tooltip
                          contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                          itemStyle={{ color: '#f9fafb' }}
                          cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
                        />
                        <Legend
                          wrapperStyle={{ fontSize: 10, paddingTop: 5 }}
                          iconSize={8}
                        />
                        <Bar dataKey="Low" stackId="a" fill="#9CA3AF" /> {/* Gray */}
                        <Bar dataKey="Medium" stackId="a" fill="#3B82F6" /> {/* Blue */}
                        <Bar dataKey="High" stackId="a" fill="#F59E0B" /> {/* Yellow */}
                        <Bar dataKey="Critical" stackId="a" fill="#EF4444" /> {/* Red */}
                      </BarChart>
                    </ResponsiveContainer>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Refresh button */}
      <button
        onClick={() => plannerContext.refreshData()}
        className="mt-4 bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700 transition-colors"
      >
        Refresh Data
      </button>

      {/* Task Popup */}
      {showTaskPopup && selectedTask && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-bold text-white">{selectedTask.title}</h3>
              <button
                onClick={closeTaskPopup}
                className="text-gray-400 hover:text-white"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-gray-400 text-sm">Status</p>
                <p className="text-white">
                  <span className={`${getStatusColor(selectedTask.status)} px-2 py-1 rounded-full text-xs text-white inline-block mt-1`}>
                    {selectedTask.status}
                  </span>
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Priority</p>
                <p className={`${getPriorityColor(selectedTask.priority)} font-medium`}>
                  {selectedTask.priority}
                </p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Category</p>
                <p className="text-white">{selectedTask.category}</p>
              </div>
              <div>
                <p className="text-gray-400 text-sm">Due Date</p>
                <p className="text-white">{formatDate(selectedTask.dueDate)}</p>
              </div>
            </div>

            {/* Tabs for task details */}
            <div className="mb-4">
              <div className="flex border-b border-gray-700 mb-2">
                <button
                  onClick={() => setCommentsTab('documents')}
                  className={`px-3 py-1 text-sm ${commentsTab === 'documents' ? 'text-white border-b-2 border-purple-500' : 'text-gray-400 hover:text-white'}`}
                >
                  Description
                </button>
                <button
                  onClick={() => setCommentsTab('comments')}
                  className={`px-3 py-1 text-sm ${commentsTab === 'comments' ? 'text-white border-b-2 border-purple-500' : 'text-gray-400 hover:text-white'}`}
                >
                  Comments
                </button>
              </div>

              {commentsTab === 'documents' && (
                <div>
                  <p className="text-gray-400 text-sm mb-1">Description</p>
                  <div className="bg-gray-700/50 p-3 rounded text-white">
                    {selectedTask.description || 'No description provided.'}
                  </div>
                </div>
              )}

              {commentsTab === 'comments' && (
                <TaskComments
                  taskId={selectedTask.id}
                  currentUser={user?.email || ''}
                />
              )}
            </div>

            <div className="mb-4">
              <p className="text-gray-400 text-sm mb-1">Assigned To</p>
              <div className="flex flex-wrap gap-2">
                {selectedTask.assignedTo.map((assignee, index) => {
                  // Check if the assignee is an email address (contains @)
                  if (assignee.includes('@')) {
                    // Try to find the user by email to get their name
                    const user = plannerContext.users.find(u => u.email === assignee);
                    return (
                      <span key={index} className="bg-purple-500/30 text-purple-200 px-2 py-1 rounded text-xs">
                        {user ? `${user.name} (${assignee})` : assignee}
                      </span>
                    );
                  } else {
                    // Assume it's a user ID
                    const user = plannerContext.users.find(u => u.id === assignee);
                    return (
                      <span key={index} className="bg-purple-500/30 text-purple-200 px-2 py-1 rounded text-xs">
                        {user ? `${user.name} (${user.email})` : assignee}
                      </span>
                    );
                  }
                })}
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={editTask}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors"
              >
                Edit Task
              </button>
              <button
                onClick={goToTask}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Go to Project
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}