# Investigative Research Agent System

A comprehensive investigative research system that leverages multi-LLM comparison capabilities for conducting thorough investigative analysis through specialized journalist AI agents.

## Overview

The Investigative Research Agent System is designed to provide deep, multi-perspective investigative analysis by employing specialized journalist AI personas, each with unique expertise and investigation methodologies. The system integrates seamlessly with the PMO (Project Management Office) ecosystem and provides professional-grade investigative reports.

## Key Features

### 🔍 Multi-Perspective Investigation
- **6 Specialized Journalist Personas**: Each with domain-specific expertise
- **Parallel Investigation Processing**: Multiple journalists investigate simultaneously
- **Diverse LLM Models**: Each journalist uses optimized models for their specialty

### 📊 Advanced Analysis Pipeline
- **5-Step Processing**: Criteria → Optimization → Investigation → Assessment → Consolidation
- **Automated Criteria Generation**: AI-generated evaluation standards
- **Prompt Optimization**: Research questions refined for maximum effectiveness
- **Comparative Assessment**: Editorial AI evaluates and compares findings
- **Optional Consolidation**: Unified reports combining best elements

### 🏢 PMO Integration
- **Seamless Workflow Integration**: Built into existing PMO processes
- **Agent Output Storage**: Results stored in PMO system
- **Progress Tracking**: Real-time investigation progress monitoring
- **PDF Export**: Professional report generation

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    PMO Interface Layer                      │
├─────────────────────────────────────────────────────────────┤
│              Investigative Research Agent Manager           │
├─────────────────────────────────────────────────────────────┤
│                Investigative Research Agent                 │
├─────────────────────────────────────────────────────────────┤
│  Journalist Personas  │  LLM Integration  │  Assessment AI  │
├─────────────────────────────────────────────────────────────┤
│              Multi-LLM Comparison Engine                    │
├─────────────────────────────────────────────────────────────┤
│    OpenAI    │  Anthropic  │   Google   │     Groq        │
└─────────────────────────────────────────────────────────────┘
```

## Journalist Personas

### 1. Investigative Journalist
- **Specialty**: General investigative reporting
- **Model**: Claude Sonnet 4.0
- **Style**: Methodical, thorough, skeptical, evidence-based
- **Expertise**: Investigative techniques, source verification, document analysis

### 2. Financial Reporter
- **Specialty**: Financial markets and corporate finance
- **Model**: GPT-4o
- **Style**: Data-driven, analytical, focused on financial implications
- **Expertise**: Financial analysis, market trends, regulatory compliance

### 3. Political Correspondent
- **Specialty**: Political systems and policy analysis
- **Model**: Gemini 2.5 Pro
- **Style**: Balanced, contextual, focused on policy implications
- **Expertise**: Political analysis, government processes, electoral systems

### 4. Technology Analyst
- **Specialty**: Technology trends and cybersecurity
- **Model**: o3-2025-04-16
- **Style**: Technical, forward-looking, innovation-focused
- **Expertise**: Technology trends, cybersecurity, digital privacy

### 5. Social Affairs Reporter
- **Specialty**: Social issues and community impact
- **Model**: Claude 4 Sonnet
- **Style**: Empathetic, community-focused, human-centered
- **Expertise**: Social issues, human rights, demographic analysis

### 6. Feature Reporter
- **Specialty**: Long-form narrative storytelling
- **Model**: Claude Opus 4.0
- **Style**: Narrative-driven, contextual, comprehensive
- **Expertise**: Narrative storytelling, cultural analysis, historical context

## Investigation Types

1. **Financial Investigation**: Corporate finance, market analysis, regulatory compliance
2. **Political Investigation**: Policy analysis, government operations, electoral systems
3. **Technology Investigation**: Tech trends, cybersecurity, digital transformation
4. **Social Affairs Investigation**: Community impact, human rights, demographic analysis
5. **Corporate Investigation**: Business practices, governance, industry analysis
6. **Environmental Investigation**: Climate change, sustainability, environmental impact
7. **General Investigative**: Deep-dive investigative reporting and analysis
8. **Feature Investigation**: Long-form narrative storytelling with comprehensive context

## API Endpoints

### `/api/investigative-research`
Main API endpoint for investigative research operations.

**Actions:**
- `types`: Get available investigation types
- `journalists`: Get available journalist personas
- `preview`: Preview investigation configuration
- `conduct`: Conduct investigation
- `history`: Get investigation history

### `/api/investigative-research-pdf`
PDF generation endpoint for investigation reports.

## Usage Examples

### Basic Investigation
```typescript
import { createInvestigativeResearchAgent } from '@/lib/agents/investigative';

const agent = createInvestigativeResearchAgent('user123');

const result = await agent.conductPMOInvestigation({
  pmoId: 'pmo_001',
  title: 'Corporate governance practices in tech industry',
  description: 'Investigate current corporate governance trends...',
  investigationType: 'corporate',
  selectedJournalistIds: ['corporate-journalist', 'financial-reporter'],
  consolidate: true,
  userId: 'user123',
  priority: 'High'
});
```

### PMO Integration
```typescript
import { createPMOInvestigativeIntegration } from '@/lib/agents/investigative';

const integration = createPMOInvestigativeIntegration('user123');

const output = await integration.processInvestigativeRequest({
  pmoId: 'pmo_001',
  title: 'Market analysis for new product launch',
  description: 'Comprehensive market research...',
  investigationType: 'financial',
  priority: 'High',
  userId: 'user123',
  autoSelectJournalists: true,
  consolidateReport: true
});
```

## UI Components

### InvestigativeResearchInterface
Main interface component for conducting investigations.

```tsx
import InvestigativeResearchInterface from '@/components/pmo/InvestigativeResearchInterface';

<InvestigativeResearchInterface
  pmoId="pmo_001"
  onInvestigationComplete={(result) => console.log(result)}
  onClose={() => setShowInterface(false)}
/>
```

### PMOInvestigativeResearchCard
Card component for PMO dashboard integration.

```tsx
import PMOInvestigativeResearchCard from '@/components/pmo/PMOInvestigativeResearchCard';

<PMOInvestigativeResearchCard
  pmoId="pmo_001"
  onInvestigationComplete={(result) => handleResult(result)}
/>
```

## Configuration

### Default Models
```typescript
const DEFAULT_SYSTEM_MODELS = {
  CRITERIA: 'gemini-2.5-pro',
  OPTIMIZATION: 'gpt-4o',
  ASSESSMENT: 'claude-sonnet-4-0',
  CONSOLIDATION: 'o3-2025-04-16'
};
```

### System Capabilities
- **Maximum Journalists**: 6 per investigation
- **Minimum Journalists**: 1 per investigation
- **Supported Providers**: OpenAI, Anthropic, Google, Groq
- **Average Duration**: 5-15 minutes per investigation
- **Success Rate**: 95%+

## Output Format

Investigation results include:
- **Original and optimized research questions**
- **Investigation criteria and methodology**
- **Individual journalist reports**
- **Comparative assessment**
- **Consolidated report** (optional)
- **Key findings and recommendations**
- **Source references**
- **Technical metadata**

## Error Handling

The system includes comprehensive error handling:
- **Individual journalist failures** don't stop the investigation
- **Fallback models** for critical components
- **Graceful degradation** with partial results
- **Detailed error reporting** for debugging

## Performance Considerations

- **Parallel processing** for journalist investigations
- **Optimized model selection** per journalist specialty
- **Progress tracking** for user feedback
- **Efficient memory management** for large investigations

## Security and Privacy

- **User authentication** required for all operations
- **PMO-level access control** for investigations
- **Secure API endpoints** with proper validation
- **Data encryption** for sensitive investigation content

## Integration Points

### PMO System
- **Agent Output Storage**: Results stored in PMO database
- **Workflow Integration**: Part of PMO task creation process
- **Progress Monitoring**: Real-time status updates
- **Document Management**: PDF reports integrated with PMO docs

### LLM Comparison Tool
- **Shared Infrastructure**: Uses same multi-LLM comparison engine
- **Model Configuration**: Consistent model selection patterns
- **Progress Tracking**: Similar UI patterns and feedback systems
- **Export Functionality**: Shared PDF generation capabilities

## Future Enhancements

1. **Real-time Collaboration**: Multiple users working on same investigation
2. **Source Integration**: Direct integration with news APIs and databases
3. **Advanced Analytics**: Investigation quality metrics and insights
4. **Custom Journalist Personas**: User-defined specialist journalists
5. **Investigation Templates**: Pre-configured investigation types
6. **Automated Follow-ups**: AI-suggested next investigation steps

## Support and Maintenance

For technical support or feature requests, please refer to the main PMO system documentation or contact the development team.

---

*This system is part of the PMO (Project Management Office) ecosystem and integrates with the broader Agentic Teams framework.*
