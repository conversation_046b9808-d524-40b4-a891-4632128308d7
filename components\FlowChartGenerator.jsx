'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { Info } from 'lucide-react';

// Default color palette for nodes and edges
const DEFAULT_COLORS = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#f59e0b', // amber-500
  '#ef4444', // red-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#06b6d4', // cyan-500
  '#f97316', // orange-500
  '#84cc16', // lime-500
  '#6366f1', // indigo-500
];

// Node type color mapping
const NODE_TYPE_COLORS = {
  start: '#10b981', // emerald-500
  end: '#ef4444',   // red-500
  process: '#3b82f6', // blue-500
  decision: '#f59e0b', // amber-500
  input: '#8b5cf6',  // violet-500
  output: '#06b6d4', // cyan-500
  default: '#6366f1', // indigo-500
};

/**
 * FlowChartGenerator component for rendering flow charts
 * This is a simplified version that renders a flow chart without using ReactFlow
 * @param {Object} props - Component props
 * @param {Object} props.flowConfig - Flow chart configuration
 */
export default function FlowChartGenerator({ flowConfig }) {
  if (!flowConfig || !flowConfig.nodes || !flowConfig.edges) {
    return (
      <div className="p-4 bg-zinc-800 rounded-md">
        <p className="text-zinc-400">No flow chart data available</p>
      </div>
    );
  }

  const { nodes, edges, direction = 'LR', title, subtitle, explanation, colors = DEFAULT_COLORS } = flowConfig;

  // Determine canvas size based on node positions
  const nodePositions = nodes.map(node => node.position);
  const minX = Math.min(...nodePositions.map(pos => pos.x));
  const maxX = Math.max(...nodePositions.map(pos => pos.x));
  const minY = Math.min(...nodePositions.map(pos => pos.y));
  const maxY = Math.max(...nodePositions.map(pos => pos.y));

  const width = Math.max(600, maxX - minX + 300);
  const height = Math.max(400, maxY - minY + 200);

  // Determine node types and assign colors
  const getNodeColor = (node, index) => {
    // If node has explicit style with background, use that
    if (node.style?.background) return node.style.background;

    // If node has a type, use type-based color
    const nodeType = node.type?.toLowerCase() || '';
    if (nodeType && NODE_TYPE_COLORS[nodeType]) {
      return NODE_TYPE_COLORS[nodeType];
    }

    // Try to infer type from label
    const label = (node.data?.label || '').toLowerCase();
    if (label.includes('start')) return NODE_TYPE_COLORS.start;
    if (label.includes('end')) return NODE_TYPE_COLORS.end;
    if (label.includes('decision') || label.includes('?')) return NODE_TYPE_COLORS.decision;
    if (label.includes('input')) return NODE_TYPE_COLORS.input;
    if (label.includes('output')) return NODE_TYPE_COLORS.output;

    // Default to color from palette based on index
    return colors[index % colors.length];
  };

  // Adjust node positions to ensure they're all visible and assign colors
  const adjustedNodes = nodes.map((node, index) => ({
    ...node,
    adjustedPosition: {
      x: node.position.x - minX + 50,
      y: node.position.y - minY + 50
    },
    color: getNodeColor(node, index)
  }));

  return (
    <div className="flow-chart-container bg-zinc-900 rounded-lg border border-zinc-700 p-6">
      {/* Chart header */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white">{title}</h3>
        {subtitle && <p className="text-zinc-400 mt-1">{subtitle}</p>}
      </div>

      {/* Flow chart visualization */}
      <div className="flow-chart-visualization mb-6 overflow-auto">
        <svg width={width} height={height} className="bg-zinc-800 rounded-md">
          {/* Draw edges */}
          {edges.map((edge, edgeIndex) => {
            const sourceNode = adjustedNodes.find(n => n.id === edge.source);
            const targetNode = adjustedNodes.find(n => n.id === edge.target);

            if (!sourceNode || !targetNode) return null;

            const sourceX = sourceNode.adjustedPosition.x + 75; // Assuming node width of 150
            const sourceY = sourceNode.adjustedPosition.y + 25; // Assuming node height of 50
            const targetX = targetNode.adjustedPosition.x;
            const targetY = targetNode.adjustedPosition.y + 25;

            // Create a path for the edge
            const path = `M${sourceX},${sourceY} C${sourceX + 50},${sourceY} ${targetX - 50},${targetY} ${targetX},${targetY}`;

            // Determine edge color - use source node color with opacity for cohesive look
            const edgeColor = edge.style?.stroke || sourceNode.color || colors[edgeIndex % colors.length];
            const edgeOpacity = edge.style?.opacity || 0.8;

            return (
              <g key={edge.id}>
                <path
                  d={path}
                  stroke={edgeColor}
                  strokeWidth={edge.style?.strokeWidth || 2}
                  strokeOpacity={edgeOpacity}
                  fill="none"
                  markerEnd={`url(#arrowhead-${edgeIndex})`}
                  className={edge.animated ? "animated-path" : ""}
                />
                {edge.label && (
                  <text
                    x={(sourceX + targetX) / 2}
                    y={((sourceY + targetY) / 2) - 10}
                    textAnchor="middle"
                    fill="#ddd"
                    fontSize="12"
                    fontWeight="500"
                  >
                    {edge.label}
                  </text>
                )}

                {/* Custom arrowhead for this edge */}
                <defs>
                  <marker
                    id={`arrowhead-${edgeIndex}`}
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon points="0 0, 10 3.5, 0 7" fill={edgeColor} />
                  </marker>
                </defs>
              </g>
            );
          })}

          {/* Draw nodes */}
          {adjustedNodes.map(node => {
            // Determine text color based on background brightness
            const hexToRgb = (hex) => {
              const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
              return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
              } : null;
            };

            const rgb = hexToRgb(node.color);
            // Calculate brightness using the formula (0.299*R + 0.587*G + 0.114*B)
            const brightness = rgb ? (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255 : 0.5;
            const textColor = brightness > 0.6 ? '#000' : '#fff';

            // Determine if this is a decision node (diamond shape)
            const isDecision = node.type === 'decision' ||
                             (node.data?.label || '').toLowerCase().includes('decision') ||
                             (node.data?.label || '').includes('?');

            return (
              <g key={node.id}>
                {isDecision ? (
                  // Diamond shape for decision nodes
                  <polygon
                    points={`
                      ${node.adjustedPosition.x + 75},${node.adjustedPosition.y}
                      ${node.adjustedPosition.x + 150},${node.adjustedPosition.y + 25}
                      ${node.adjustedPosition.x + 75},${node.adjustedPosition.y + 50}
                      ${node.adjustedPosition.x},${node.adjustedPosition.y + 25}
                    `}
                    fill={node.color}
                    stroke={node.style?.borderColor || '#fff'}
                    strokeWidth={node.style?.borderWidth || 1.5}
                    strokeOpacity={0.8}
                  />
                ) : (
                  // Rectangle for other nodes
                  <rect
                    x={node.adjustedPosition.x}
                    y={node.adjustedPosition.y}
                    width={150}
                    height={50}
                    rx={5}
                    ry={5}
                    fill={node.color}
                    stroke={node.style?.borderColor || '#fff'}
                    strokeWidth={node.style?.borderWidth || 1.5}
                    strokeOpacity={0.8}
                  />
                )}

                {/* Node label */}
                <text
                  x={node.adjustedPosition.x + 75}
                  y={node.adjustedPosition.y + 30}
                  textAnchor="middle"
                  fill={textColor}
                  fontSize="14"
                  fontWeight="500"
                >
                  {node.data?.label || `Node ${node.id}`}
                </text>
              </g>
            );
          })}
        </svg>
      </div>

      {/* Node details */}
      <div className="node-details mb-6">
        <h4 className="text-md font-semibold text-zinc-300 mb-2">Node Details</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {adjustedNodes.map(node => {
            // Calculate text color based on background brightness
            const hexToRgb = (hex) => {
              const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
              return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
              } : null;
            };

            const rgb = hexToRgb(node.color);
            const brightness = rgb ? (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255 : 0.5;
            const textColor = brightness > 0.6 ? '#000' : '#fff';

            return (
              <div
                key={node.id}
                className="p-3 rounded-md border border-zinc-700"
                style={{
                  backgroundColor: node.color,
                  borderColor: node.style?.borderColor || (brightness > 0.6 ? '#475569' : '#fff'),
                  color: textColor
                }}
              >
                <h5 className="font-medium" style={{ color: textColor }}>
                  {node.data?.label || `Node ${node.id}`}
                </h5>
                {node.data?.description && (
                  <p className="text-sm mt-1" style={{ color: textColor, opacity: 0.8 }}>
                    {node.data.description}
                  </p>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Chart explanation */}
      {explanation && (
        <div className="mt-4 p-4 bg-zinc-800 rounded-md border border-zinc-700">
          <div className="flex items-start">
            <Info className="text-blue-400 mr-2 mt-1 flex-shrink-0" size={16} />
            <p className="text-zinc-300 text-sm">{explanation}</p>
          </div>
        </div>
      )}

      {/* CSS for animated paths */}
      <style jsx>{`
        .animated-path {
          stroke-dasharray: 5;
          animation: dash 1.5s linear infinite;
        }

        @keyframes dash {
          to {
            stroke-dashoffset: 20;
          }
        }
      `}</style>
    </div>
  );
}
