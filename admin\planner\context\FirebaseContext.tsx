'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  auth,
  getCurrentUser,
  loginUser,
  signOut,
  getUserById,
  getUsers,
  getProjects,
  getTasks
} from '../firebase/firestore';
import { User, Project, Task } from '../types';
import { onAuthStateChanged } from 'firebase/auth';
import initializeFirebase from '../firebase/init';

interface FirebaseContextType {
  currentUser: User | null;
  users: User[];
  projects: Project[];
  tasks: Task[];
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshData: () => Promise<void>;
}

const FirebaseContext = createContext<FirebaseContextType | undefined>(undefined);

export const useFirebase = () => {
  const context = useContext(FirebaseContext);
  if (context === undefined) {
    throw new Error('useFirebase must be used within a FirebaseProvider');
  }
  return context;
};

interface FirebaseProviderProps {
  children: ReactNode;
}

export const FirebaseProvider: React.FC<FirebaseProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);

  // Initialize Firebase
  useEffect(() => {
    const initialize = async () => {
      await initializeFirebase();
      setLoading(false);
    };

    initialize();
  }, []);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        try {
          const userData = await getUserById(user.uid);

          // Check if userData has all required User properties
          if (userData &&
              'name' in userData &&
              'email' in userData &&
              'role' in userData &&
              'avatar' in userData &&
              'availability' in userData) {
            // Cast to User type since we've verified all required properties exist
            setCurrentUser(userData as User);
          } else {
            console.error('User data missing required fields:', userData);
            setCurrentUser(null);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }

      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const usersData = await getUsers();
        // Validate that each user has the required fields
        const validUsers = usersData.filter(user =>
          user &&
          'name' in user &&
          'email' in user &&
          'role' in user &&
          'avatar' in user &&
          'availability' in user
        ) as User[];

        setUsers(validUsers);

        // Get projects for the current user
        // The getProjects function now expects a userEmail parameter
        const projectsData = currentUser ? await getProjects(currentUser.email as any) : [];
        // Validate that each project has the required fields
        const validProjects = projectsData.filter(project =>
          project &&
          'name' in project &&
          'description' in project &&
          'startDate' in project &&
          'endDate' in project &&
          'owner' in project &&
          'members' in project &&
          'status' in project
        ) as Project[];

        setProjects(validProjects);

        const tasksData = await getTasks();
        // Validate that each task has the required fields
        const validTasks = tasksData.filter(task =>
          task &&
          'projectId' in task &&
          'title' in task &&
          'description' in task &&
          'category' in task &&
          'status' in task &&
          'startDate' in task &&
          'dueDate' in task &&
          'assignedTo' in task &&
          'priority' in task &&
          'dependencies' in task
        ) as Task[];

        setTasks(validTasks);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    if (!loading) {
      fetchData();
    }
  }, [loading]);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      await loginUser(email, password);
      // Auth state change listener will update the currentUser
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      await signOut();
      // Auth state change listener will update the currentUser
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    try {
      setLoading(true);

      const usersData = await getUsers();
      // Validate that each user has the required fields
      const validUsers = usersData.filter(user =>
        user &&
        'name' in user &&
        'email' in user &&
        'role' in user &&
        'avatar' in user &&
        'availability' in user
      ) as User[];

      setUsers(validUsers);

      // Get projects for the current user
      const projectsData = currentUser ? await getProjects(currentUser.email as any) : [];
      // Validate that each project has the required fields
      const validProjects = projectsData.filter(project =>
        project &&
        'name' in project &&
        'description' in project &&
        'startDate' in project &&
        'endDate' in project &&
        'owner' in project &&
        'members' in project &&
        'status' in project
      ) as Project[];

      setProjects(validProjects);

      const tasksData = await getTasks();
      // Validate that each task has the required fields
      const validTasks = tasksData.filter(task =>
        task &&
        'projectId' in task &&
        'title' in task &&
        'description' in task &&
        'category' in task &&
        'status' in task &&
        'startDate' in task &&
        'dueDate' in task &&
        'assignedTo' in task &&
        'priority' in task &&
        'dependencies' in task
      ) as Task[];

      setTasks(validTasks);
    } catch (error) {
      console.error('Error refreshing data:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    currentUser,
    users,
    projects,
    tasks,
    loading,
    login,
    logout,
    refreshData
  };

  return (
    <FirebaseContext.Provider value={value}>
      {children}
    </FirebaseContext.Provider>
  );
};
