// app/ClientLayout.tsx
'use client'

import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { AnalyticsProvider } from "lib/analytics/analyticsProvider";

interface ClientLayoutProps {
  children: ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  return (
    <SessionProvider>
      <SelectedDocProvider>
        <div className="flex flex-row w-full min-h-screen">
          <div className="h-screen">
            {/* <SideBar /> */}
          </div>
          <div className="flex-grow">
          <AnalyticsProvider>
            <div className="max-w-full">{children}</div>
            </AnalyticsProvider>
          </div>
        </div>
      </SelectedDocProvider>
    </SessionProvider>
  );
}