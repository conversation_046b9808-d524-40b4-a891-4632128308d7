/**
 * Test script to verify PMO record creation and retrieval for codebase documentation
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, orderBy } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

async function testPMOIntegration() {
  console.log('🧪 Testing PMO Integration for Codebase Documentation');
  console.log('====================================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Test user email (replace with actual test user)
    const testUserEmail = '<EMAIL>';

    console.log(`\n📋 Step 1: Checking PMO records for user: ${testUserEmail}`);

    // Get PMO records from user-specific collection
    const pmoCollectionRef = collection(db, 'users', testUserEmail, 'PMO');
    const recordsQuery = query(pmoCollectionRef, orderBy('createdAt', 'desc'));
    
    const snapshot = await getDocs(recordsQuery);
    console.log(`   Found ${snapshot.docs.length} PMO records in user collection`);

    if (snapshot.docs.length > 0) {
      console.log('\n📄 Recent PMO Records:');
      snapshot.docs.slice(0, 5).forEach((doc, index) => {
        const data = doc.data();
        console.log(`   ${index + 1}. ID: ${doc.id}`);
        console.log(`      Title: ${data.title || 'No title'}`);
        console.log(`      Category: ${data.category || 'No category'}`);
        console.log(`      Status: ${data.status || 'No status'}`);
        console.log(`      Created: ${data.createdAt ? new Date(data.createdAt.seconds * 1000).toISOString() : 'No date'}`);
        console.log(`      Agent IDs: ${data.agentIds ? data.agentIds.join(', ') : 'None'}`);
        console.log('');
      });

      // Check for codebase documentation records
      const codebaseDocRecords = snapshot.docs.filter(doc => {
        const data = doc.data();
        return data.title && data.title.toLowerCase().includes('codebase documentation');
      });

      console.log(`\n🔍 Step 2: Found ${codebaseDocRecords.length} codebase documentation records`);
      
      if (codebaseDocRecords.length > 0) {
        console.log('\n📊 Codebase Documentation Records:');
        codebaseDocRecords.forEach((doc, index) => {
          const data = doc.data();
          console.log(`   ${index + 1}. ${data.title}`);
          console.log(`      Status: ${data.status}`);
          console.log(`      Team Assignment: ${data.agentIds ? data.agentIds.join(', ') : 'None'}`);
          console.log(`      Team Rationale: ${data.teamSelectionRationale || 'None'}`);
          console.log('');
        });
      }
    } else {
      console.log('\n⚠️  No PMO records found. This could indicate:');
      console.log('   - No codebase documentation requests have been made');
      console.log('   - PMO records are being stored in a different location');
      console.log('   - There\'s an issue with the PMO record creation process');
    }

    console.log('\n✅ PMO Integration Test Complete');

  } catch (error) {
    console.error('\n❌ Error testing PMO integration:', error);
    console.error('   This could indicate:');
    console.error('   - Firebase connection issues');
    console.error('   - Permission problems');
    console.error('   - Configuration errors');
  }
}

// Run the test
testPMOIntegration().then(() => {
  console.log('\n🎉 Test execution finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test execution failed:', error);
  process.exit(1);
});
