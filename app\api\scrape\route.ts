import { NextRequest, NextResponse } from "next/server";
import { webScraperTool } from "lib/tools/web-scraper";

interface ScrapeRequest {
  url: string;
}

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json() as ScrapeRequest;
    console.log("Received request to scrape URL:", url);

    if (!url) {
      console.log("Error: No URL provided in request");
      return NextResponse.json({ error: "URL is required" }, { status: 400 });
    }

    const scrapedUrls = await webScraperTool.extractLinks(url, true);
    console.log("Scraped URLs:", scrapedUrls);

    return NextResponse.json({ urls: scrapedUrls });
  } catch (error: any) {
    console.log("Error scraping URLs:", error.message);
    return NextResponse.json({ error: "Failed to scrape URLs" }, { status: 500 });
  }
}
