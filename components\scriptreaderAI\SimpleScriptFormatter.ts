// SimpleScriptFormatter.ts
// A minimalistic, focused text formatter for script rehearsal content with smart rules

/**
 * Smart script formatter that applies minimal, focused formatting to enhance readability
 * without overloading the visual presentation
 * 
 * @param text The original script text
 * @returns Formatted script text with targeted enhancements
 */
export function formatScript(text: string): string {
    if (!text) return '';
    
    // Split text into paragraphs for individual processing
    const paragraphs = text.split(/\n\n+/);
    const formattedParagraphs = paragraphs.map(paragraph => {
      // Skip processing for empty paragraphs
      if (!paragraph.trim()) return '';
      
      // Apply specific formatting based on paragraph content type
      if (isInstructionParagraph(paragraph)) {
        return formatInstructionParagraph(paragraph);
      } else if (isDialogueParagraph(paragraph)) {
        return formatDialogueParagraph(paragraph);
      } else {
        return formatStandardParagraph(paragraph);
      }
    });
    
    // Join with double newlines for paragraph separation
    return formattedParagraphs.join('\n\n');
  }
  
  /**
   * Detects if a paragraph is primarily an instruction to the user
   */
  function isInstructionParagraph(text: string): boolean {
    // Specific phrases that strongly indicate rehearsal instructions
    const instructionalPhrases = [
      'ready to rehearse',
      'ready to begin',
      'ready to start',
      'whenever you\'re ready',
      'would you like to start',
      'shall we begin',
      'let\'s start',
      'begin when you\'re ready',
      'you can begin',
      'we can start',
      'would you like to focus on',
      'would you like to read',
      'I\'ll read for',
      'you can read'
    ];
    
    // Check if the paragraph contains any of these phrases
    return instructionalPhrases.some(phrase => 
      text.toLowerCase().includes(phrase.toLowerCase()));
  }
  
  /**
   * Detects if a paragraph contains dialogue or dialogue reference
   */
  function isDialogueParagraph(text: string): boolean {
    // Check for quoted text, character names with colons, or explicit line references
    return /[""].+[""]/.test(text) || 
           /\b[A-Z][a-z]+:/.test(text) || 
           /\b[A-Z][a-z]+ says\b/.test(text) ||
           /\bline is\b/i.test(text) && /[""].+[""]/.test(text);
  }
  
  /**
   * Format a paragraph identified as an instruction
   */
  function formatInstructionParagraph(text: string): string {
    // Use blockquote for instruction paragraphs - clean and distinct
    return `> ${text}`;
  }
  
  /**
   * Format a paragraph containing dialogue
   */
  function formatDialogueParagraph(text: string): string {
    let result = text;
    
    // Format specific character names - more precision to avoid over-formatting
    result = result.replace(/\b([A-Z][a-z]{2,})(['']s|\s*:)(\s|$)/g, '**$1**$2$3');
    
    // Format quoted dialogue - only if it doesn't already have formatting
    result = result.replace(/([^*_])[""]([^*_""][^""]+)[""]([^*_])/g, '$1*"$2"*$3');
    
    return result;
  }
  
  /**
   * Format standard text paragraph with minimal enhancements
   */
  function formatStandardParagraph(text: string): string {
    let result = text;
    
    // Bold character names when they appear as proper nouns
    // More selective regex to avoid matching common words
    result = result.replace(/\b([A-Z][a-z]{3,})\b(?!\s*\.\s*[A-Z])/g, (match, name) => {
      // Skip common words that shouldn't be highlighted
      if (isCommonWord(name)) return match;
      return `**${name}**`;
    });
    
    // Italicize play/book titles
    result = result.replace(/\b(A Tale of Two Cities)\b/g, '_$1_');
    
    // Format parenthetical stage directions
    result = result.replace(/\(([^)]+)\)/g, '_($1)_');
    
    return result;
  }
  
  /**
   * Check if a word is common and shouldn't be treated as a character name
   */
  function isCommonWord(word: string): boolean {
    const commonWords = [
      "The", "This", "That", "These", "Those", "There", "Here", "Where",
      "When", "What", "Who", "Why", "How", "Which", "Would", "Could",
      "Should", "Might", "Must", "Will", "Shall", "Can", "May", "Let",
      "Scene", "Act", "Line", "Lines", "Dialogue", "Text", "Script",
      "From", "With", "About", "Into", "Onto", "Under", "Over", "Through",
      "After", "Before", "During", "While", "Since", "Until", "Because",
      "Although", "Though", "Unless", "Whether", "However", "Therefore"
    ];
    
    return commonWords.includes(word);
  }
  
  // Simple integration function for DialogueDisplay
  export function processScriptContent(content: string, role: string): string {
    // Only apply script formatting to non-user messages (e.g., Scene Mate)
    if (role !== "user") {
      return formatScript(content);
    }
    return content;
  }