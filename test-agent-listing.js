// Test script to verify agent listing functionality
async function testAgentListing() {
  try {
    console.log('Testing agent listing...');
    
    // Test listing all agents
    const listResponse = await fetch('/api/elevenlabs/create-agent?list=true', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!listResponse.ok) {
      throw new Error(`Failed to list agents: ${listResponse.status}`);
    }
    
    const listData = await listResponse.json();
    console.log('All agents:', listData);
    
    // Test searching for PMO agents
    const searchResponse = await fetch('/api/elevenlabs/create-agent?list=true&search=pmo', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!searchResponse.ok) {
      throw new Error(`Failed to search agents: ${searchResponse.status}`);
    }
    
    const searchData = await searchResponse.json();
    console.log('PMO agents found:', searchData);
    
    // Look for duplicates
    const agents = searchData.agents || [];
    const agentNames = agents.map(agent => agent.name);
    const duplicates = agentNames.filter((name, index) => agentNames.indexOf(name) !== index);
    
    if (duplicates.length > 0) {
      console.log('⚠️ Duplicate agent names found:', duplicates);
    } else {
      console.log('✅ No duplicate agent names found');
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test if this script is executed directly
if (typeof window !== 'undefined') {
  testAgentListing();
}
