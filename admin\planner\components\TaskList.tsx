'use client';

import React, { useState } from 'react';
import { Task, Resource, TaskStatus } from '../types';
import { Calendar, Clock, Edit, Trash2, CheckCircle, AlertCircle, User } from 'lucide-react';
import TaskDetailsModal from './TaskDetailsModal';

interface TaskListProps {
  tasks: Task[];
  resources: Resource[];
  onEdit: (task: Task) => void;
  onDelete: (id: string) => void;
  onStatusChange: (id: string, status: TaskStatus) => void;
}

const TaskList: React.FC<TaskListProps> = ({
  tasks,
  resources,
  onEdit,
  onDelete,
  onStatusChange
}) => {
  // State for task details modal
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // Group tasks by status
  const tasksByStatus: Record<TaskStatus, Task[]> = {
    'Not Started': [],
    'In Progress': [],
    'Reviewed': [],
    'Complete': []
  };

  tasks.forEach(task => {
    tasksByStatus[task.status].push(task);
  });

  // Get resource names for display
  const getResourceNames = (resourceIds: string[]): string => {
    return resourceIds
      .map(id => {
        const resource = resources.find(r => r.id === id);
        return resource ? `${resource.name}${resource.jobTitle ? ` (${resource.jobTitle})` : ''}` : 'Unknown';
      })
      .join(', ');
  };

  // Get status color
  const getStatusColor = (status: TaskStatus): string => {
    switch (status) {
      case 'Not Started':
        return 'bg-gray-100 text-gray-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Reviewed':
        return 'bg-yellow-100 text-yellow-800';
      case 'Complete':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'Low':
        return 'bg-gray-100 text-gray-800';
      case 'Medium':
        return 'bg-blue-100 text-blue-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format date for display
  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Calculate days remaining
  const getDaysRemaining = (dueDate: Date): number => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const due = new Date(dueDate);
    due.setHours(0, 0, 0, 0);

    const diffTime = due.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Render a task card
  const renderTaskCard = (task: Task) => {
    const daysRemaining = getDaysRemaining(task.dueDate);
    const isOverdue = daysRemaining < 0 && task.status !== 'Complete';

    // Handle clicking on the task card to open task details modal
    const handleTaskClick = () => {
      setSelectedTask(task);
    };

    return (
      <div
        key={task.id}
        className="bg-white rounded-lg shadow-md p-4 mb-3 border-l-4 hover:shadow-lg transition-shadow cursor-pointer"
        style={{
          borderLeftColor:
            task.priority === 'Critical' ? '#ef4444' :
            task.priority === 'High' ? '#f97316' :
            task.priority === 'Medium' ? '#3b82f6' :
            '#9ca3af'
        }}
        onClick={handleTaskClick}
      >
        <div className="flex justify-between items-start mb-2">
          <h3 className="font-medium text-gray-900 pr-4">{task.title}</h3>
          <div className="flex space-x-1">
            <button
              onClick={(e) => {
                e.stopPropagation(); // Prevent task card click
                onEdit(task);
              }}
              className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
              title="Edit task"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation(); // Prevent task card click
                onDelete(task.id);
              }}
              className="p-1 text-gray-500 hover:text-red-600 transition-colors"
              title="Delete task"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="text-sm text-gray-500 mb-3">
          {task.description.length > 100
            ? `${task.description.substring(0, 100)}...`
            : task.description}
        </div>

        <div className="flex flex-wrap gap-2 mb-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
            {task.status}
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
            {task.priority}
          </span>
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
            {task.category}
          </span>
        </div>

        <div className="flex items-center text-xs text-gray-500 mb-2">
          <Calendar className="w-3 h-3 mr-1" />
          <span className={isOverdue ? 'text-red-600 font-medium' : ''}>
            {formatDate(task.dueDate)}
            {isOverdue
              ? ` (${Math.abs(daysRemaining)} days overdue)`
              : daysRemaining === 0
                ? ' (Today)'
                : ` (${daysRemaining} days left)`}
          </span>
        </div>

        <div className="flex items-center text-xs text-gray-500 mb-3">
          <User className="w-3 h-3 mr-1" />
          <span>{getResourceNames(task.assignedTo)}</span>
        </div>

        {task.status !== 'Complete' && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Update Status:
            </label>
            <div className="flex flex-wrap gap-1">
              {Object.keys(tasksByStatus).map(status => (
                <button
                  key={status}
                  onClick={() => onStatusChange(task.id, status as TaskStatus)}
                  disabled={status === task.status}
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    status === task.status
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : `${getStatusColor(status as TaskStatus)} hover:opacity-80`
                  }`}
                >
                  {status}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {Object.entries(tasksByStatus).map(([status, statusTasks]) => (
          <div key={status} className="bg-gray-50 rounded-lg p-3">
            <h2 className="font-bold text-gray-700 mb-3 flex items-center">
              <span className={`w-3 h-3 rounded-full mr-2 ${
                status === 'Not Started' ? 'bg-gray-500' :
                status === 'In Progress' ? 'bg-blue-500' :
                status === 'Reviewed' ? 'bg-yellow-500' :
                'bg-green-500'
              }`}></span>
              {status} ({statusTasks.length})
            </h2>

            <div className="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto pr-2 custom-scrollbar">
              {statusTasks.length === 0 ? (
                <div className="bg-white rounded-lg shadow-sm p-4 text-center text-gray-500 text-sm">
                  No tasks in this status
                </div>
              ) : (
                statusTasks.map(renderTaskCard)
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Task details modal */}
      {selectedTask && (
        <TaskDetailsModal
          task={selectedTask}
          onClose={() => setSelectedTask(null)}
          onEdit={() => {
            // Close the details modal and open the edit modal
            const taskToEdit = selectedTask;
            setSelectedTask(null);
            onEdit(taskToEdit);
          }}
        />
      )}
    </>
  );
};

export default TaskList;
