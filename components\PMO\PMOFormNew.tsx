'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Project } from 'admin/planner/types'; // Assuming this path is correct
import { PMORecordPriority, PMOFormInput, PMOContextOptions } from 'lib/agents/pmo/PMOInterfaces';
import { Button } from 'components/ui/button'; // Assuming you have a Button component
import { Textarea } from 'components/ui/textarea'; // Assuming Textarea
import { Input } from 'components/ui/input'; // Assuming Input
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from 'components/ui/select'; // Assuming Select
import { Sparkles, Search, ChevronDown, Loader2, AlertTriangle } from 'lucide-react';

// Types for files and categories
interface UserFile {
  id: string;
  name: string;
  category: string;
  namespace?: string;
}
interface UserCategory {
  id: string;
  name: string;
  documentCount?: number;
}

interface PMOFormProps {
  projects: Project[];
  userFiles: UserFile[];
  userCategories: UserCategory[];
  isFetchingFilesCategories?: boolean;
  onSubmit: (formData: PMOFormInput) => Promise<void>;
  onCancel: () => void;
  initialData?: Partial<PMOFormInput>; // For editing later
  inlineMode?: boolean; // For displaying in an inline mode vs modal
}

const quickStartPrompts = [
  "Launch a new internal tool for tracking employee feedback.",
  "Organize a company-wide hackathon for Q3.",
  "Develop a marketing campaign for our new SaaS product feature.",
  "Research and implement a new CRM system for the sales team.",
  "Improve the onboarding process for new software engineers."
];

const PMOForm: React.FC<PMOFormProps> = ({
  projects,
  userFiles,
  userCategories,
  isFetchingFilesCategories = false,
  onSubmit,
  onCancel,
  initialData = {},
  inlineMode = false
}) => {
  const [title, setTitle] = useState(initialData.title || '');
  const [initialDescription, setInitialDescription] = useState(initialData.description || '');
  const [refinedDescription, setRefinedDescription] = useState('');
  const [useRefined, setUseRefined] = useState(false);
  const [isRefining, setIsRefining] = useState(false);
  const [refinementError, setRefinementError] = useState<string | null>(null);

  const [priority, setPriority] = useState<PMORecordPriority>(initialData.priority || 'Medium');
  const [selectedProjectId, setSelectedProjectId] = useState(initialData.projectId || '');

  const [customContext, setCustomContext] = useState(initialData.contextOptions?.customContext || '');
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>(initialData.contextOptions?.fileIds || []);
  const [selectedCategoryIds, setSelectedCategoryIds] = useState<string[]>(initialData.contextOptions?.categoryIds || []);

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Dropdown states
  const [fileSearchQuery, setFileSearchQuery] = useState('');
  const [categorySearchQuery, setCategorySearchQuery] = useState('');
  const [isFileDropdownOpen, setIsFileDropdownOpen] = useState(false);
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const fileDropdownRef = useRef<HTMLDivElement>(null);
  const categoryDropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (fileDropdownRef.current && !fileDropdownRef.current.contains(event.target as Node)) {
        setIsFileDropdownOpen(false);
      }
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setIsCategoryDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleRefineDescription = async () => {
    if (!initialDescription.trim()) {
      setRefinementError("Please enter an initial description to refine.");
      return;
    }
    setIsRefining(true);
    setRefinementError(null);
    setRefinedDescription('');
    try {
      const response = await fetch('/api/optimize-pmo-prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ originalPrompt: initialDescription }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to refine description');
      }
      const data = await response.json();
      if (data.success) {
        setRefinedDescription(data.optimizedPrompt);
        setUseRefined(true); // Automatically switch to using refined description
      } else {
        throw new Error(data.error || 'Refinement API call failed');
      }
    } catch (error: any) {
      setRefinementError(error.message);
      setRefinedDescription(''); // Clear any partial refined description
      setUseRefined(false); // Fallback to not using refined
    } finally {
      setIsRefining(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const finalDescription = useRefined && refinedDescription ? refinedDescription : initialDescription;

    const contextOptions: PMOContextOptions = {
      customContext: customContext.trim() || null,
      fileIds: selectedFileIds.length > 0 ? selectedFileIds : null,
      categoryIds: selectedCategoryIds.length > 0 ? selectedCategoryIds : null,
    };

    const formData: PMOFormInput = {
      title: title.trim(),
      description: finalDescription.trim(),
      priority,
      projectId: selectedProjectId || null,
      contextOptions,
    };

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Submission error in PMOForm:", error);
      // Error handling might be done by parent, or show a message here
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter files to show only those with category 'Unknown'
  const filteredFiles = userFiles.filter(file =>
    file.category === 'Unknown' &&
    file.name.toLowerCase().includes(fileSearchQuery.toLowerCase())
  );

  const filteredCategories = userCategories.filter(cat =>
    cat.name.toLowerCase().includes(categorySearchQuery.toLowerCase())
  );

  const toggleFileSelection = (fileId: string) => {
    setSelectedFileIds(prev =>
      prev.includes(fileId) ? prev.filter(id => id !== fileId) : [...prev, fileId]
    );
  };

  const toggleCategorySelection = (categoryId: string) => {
    setSelectedCategoryIds(prev =>
      prev.includes(categoryId) ? prev.filter(id => id !== categoryId) : [...prev, categoryId]
    );
  };

  return (
    <form onSubmit={handleSubmit} className={`space-y-6 ${inlineMode ? 'inline-form' : ''}`}>
      {/* Title field */}
      <div>
        {inlineMode && <h2 className="text-xl font-bold mb-4">Define Your Request</h2>}
        <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-1">Title <span className="text-red-500">*</span></label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter a title for your PMO request"
          required
          className="bg-gray-700 border-gray-600 text-white"
        />
      </div>

      {/* Main content area */}
      <div className={inlineMode ? "grid grid-cols-1 lg:grid-cols-2 gap-6" : ""}>
        {/* Left column in inline mode - Request Definition */}
        <div className={inlineMode ? "" : "p-4 bg-gray-700/50 rounded-lg border border-gray-600/80"}>
          {!inlineMode && <h3 className="text-lg font-semibold text-purple-300 mb-3">Define Your Request</h3>}
          <div>
            <label htmlFor="initialDescription" className="block text-sm font-medium text-gray-300 mb-1">
              Request Description <span className="text-red-500">*</span>
            </label>
            <Textarea
              id="initialDescription"
              value={initialDescription}
              onChange={(e) => {
                setInitialDescription(e.target.value);
                setUseRefined(false); // If user edits initial, assume they want to use it or re-refine
                setRefinedDescription(''); // Clear refined if initial changes
              }}
              rows={inlineMode ? 8 : 5}
              placeholder="Describe the task or project you need help with..."
              required
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>
          <div className="mt-3">
            <Button
              type="button"
              onClick={handleRefineDescription}
              disabled={isRefining || !initialDescription.trim()}
              variant="outline"
              className="bg-indigo-600 hover:bg-indigo-700 text-white border-indigo-500"
            >
              {isRefining ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Sparkles className="w-4 h-4 mr-2" />
              )}
              Refine with AI
            </Button>
          </div>
          {refinementError && (
            <p className="mt-2 text-sm text-red-400 flex items-center">
              <AlertTriangle size={16} className="mr-1" /> {refinementError}
            </p>
          )}
          {refinedDescription && (
            <div className="mt-4 p-3 bg-gray-800 rounded-md border border-purple-500">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-purple-300">AI Refined Description:</h4>
                <label className="flex items-center text-sm text-gray-300 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={useRefined}
                    onChange={(e) => setUseRefined(e.target.checked)}
                    className="mr-2 h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                  />
                  Use this refined version
                </label>
              </div>
              <p className="text-sm text-gray-200 whitespace-pre-wrap">{refinedDescription}</p>
            </div>
          )}

          {!inlineMode && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-400 mb-2">Quick Start Prompts:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {quickStartPrompts.map((prompt, idx) => (
                  <Button
                    key={idx}
                    type="button"
                    variant="outline"
                    size="sm"
                    className="text-left justify-start h-auto whitespace-normal bg-gray-600 hover:bg-gray-500 text-gray-200 border-gray-500"
                    onClick={() => {
                      setInitialDescription(prompt);
                      setRefinedDescription('');
                      setUseRefined(false);
                    }}
                  >
                    {prompt}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right column in inline mode - Context Options */}
        <div className={inlineMode ? "" : "p-4 bg-gray-700/50 rounded-lg border border-gray-600/80"}>
          {inlineMode ? (
            <h3 className="text-lg font-semibold text-purple-300 mb-3">Context Options</h3>
          ) : (
            <h3 className="text-lg font-semibold text-purple-300 mb-3">Additional Context (Optional)</h3>
          )}
          {!inlineMode && (
            <p className="text-xs text-gray-400 mb-3">
              Provide any supplementary information. File and category selections can help the PMO Agent understand existing resources.
            </p>
          )}
          {inlineMode && (
            <p className="text-xs text-gray-400 mb-3">
              All context options are optional. You can provide custom context in the text box and combine it with either a document or category selection. Document and category selections are mutually exclusive (document takes precedence if both are selected).
            </p>
          )}

          {/* Context Options */}
          <div className="mb-4">
            <label htmlFor="customContext" className="block text-sm font-medium text-gray-300 mb-1">Custom Context (Optional)</label>
            <Textarea
              id="customContext"
              value={customContext}
              onChange={(e) => setCustomContext(e.target.value)}
              rows={inlineMode ? 4 : 3}
              placeholder="Add any other relevant details, links, or notes..."
              className="bg-gray-700 border-gray-600 text-white"
            />
          </div>

          {/* Files and Categories */}
          <div className="grid grid-cols-1 gap-4 mt-4">
            {/* Files Dropdown */}
            <div className="relative" ref={fileDropdownRef}>
              <label className="block text-sm font-medium text-gray-300 mb-1">Files (Optional)</label>
              <div className="flex items-center justify-between p-3 bg-gray-700 border border-gray-600 rounded-md cursor-pointer"
                onClick={() => setIsFileDropdownOpen(!isFileDropdownOpen)}>
                <span className={selectedFileIds.length > 0 ? "text-white" : "text-gray-400"}>
                  {selectedFileIds.length > 0 ? `${selectedFileIds.length} file(s) selected` : "Select a file"}
                </span>
                <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isFileDropdownOpen ? 'rotate-180' : ''}`} />
              </div>
              {isFileDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
                  <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-600">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input type="text" placeholder="Search files..." value={fileSearchQuery} onChange={(e) => setFileSearchQuery(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm" />
                    </div>
                  </div>
                  <div className="py-1">
                    <div className="px-4 py-2 text-xs text-amber-400 border-b border-gray-600">
                      Showing only files with category 'Unknown'
                    </div>
                    <ul className="py-1">
                      {isFetchingFilesCategories ? <li className="px-4 py-3 text-gray-400 text-center text-sm">Loading files...</li>
                        : filteredFiles.length > 0 ? filteredFiles.map((file) => (
                          <li key={file.id} className="px-4 py-2 hover:bg-purple-900/50 cursor-pointer text-sm" onClick={() => toggleFileSelection(file.id)}>
                            <div className="font-medium">{file.name}</div>
                            <div className="text-xs text-gray-400">{file.category}</div>
                          </li>))
                        : <li className="px-4 py-3 text-gray-400 text-center text-sm">No files found</li>}
                    </ul>
                  </div>
                </div>
              )}
            </div>

            {/* Categories Dropdown */}
            <div className="relative" ref={categoryDropdownRef}>
              <label className="block text-sm font-medium text-gray-300 mb-1">Categories (Optional)</label>
              <div className="flex items-center justify-between p-3 bg-gray-700 border border-gray-600 rounded-md cursor-pointer"
                onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}>
                <span className={selectedCategoryIds.length > 0 ? "text-white" : "text-gray-400"}>
                  {selectedCategoryIds.length > 0 ? `${selectedCategoryIds.length} category(s) selected` : "Select a category"}
                </span>
                <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform ${isCategoryDropdownOpen ? 'rotate-180' : ''}`} />
              </div>
              {isCategoryDropdownOpen && (
                <div className="absolute z-10 w-full mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto">
                  <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-600">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input type="text" placeholder="Search categories..." value={categorySearchQuery} onChange={(e) => setCategorySearchQuery(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm" />
                    </div>
                  </div>
                  <div className="py-1">
                    <div className="px-4 py-2 text-xs text-amber-400 border-b border-gray-600">
                      Showing all categories except 'Unknown'
                    </div>
                    <ul className="py-1">
                      {isFetchingFilesCategories ? <li className="px-4 py-3 text-gray-400 text-center text-sm">Loading categories...</li>
                        : filteredCategories.length > 0 ? filteredCategories.map((category) => (
                          <li key={category.id} className="px-4 py-2 hover:bg-purple-900/50 cursor-pointer text-sm" onClick={() => toggleCategorySelection(category.id)}>
                            <div className="font-medium">{category.name}</div>
                            <div className="text-xs text-gray-400">{category.documentCount || userFiles.filter(doc => doc.category === category.name).length} documents</div>
                          </li>))
                        : <li className="px-4 py-3 text-gray-400 text-center text-sm">No categories available</li>}
                    </ul>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Priority and Project */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-gray-300 mb-1">Priority</label>
              <Select value={priority} onValueChange={(value) => setPriority(value as PMORecordPriority)}>
                <SelectTrigger className="w-full bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 text-white border-gray-600">
                  <SelectItem value="Low" className="hover:bg-purple-600">Low</SelectItem>
                  <SelectItem value="Medium" className="hover:bg-purple-600">Medium</SelectItem>
                  <SelectItem value="High" className="hover:bg-purple-600">High</SelectItem>
                  <SelectItem value="Critical" className="hover:bg-purple-600">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label htmlFor="project" className="block text-sm font-medium text-gray-300 mb-1">Project (Optional)</label>
              <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                <SelectTrigger className="w-full bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select a project" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 text-white border-gray-600">
                  <SelectItem value="new_project" className="hover:bg-purple-600">-- Create New Project --</SelectItem>
                  {projects.map(project => (
                    <SelectItem key={project.id} value={project.id} className="hover:bg-purple-600">
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Start Prompts in inline mode */}
      {inlineMode && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-400 mb-2">Quick Start Prompts:</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {quickStartPrompts.map((prompt, idx) => (
              <Button
                key={idx}
                type="button"
                variant="outline"
                size="sm"
                className="text-left justify-start h-auto whitespace-normal bg-gray-600 hover:bg-gray-500 text-gray-200 border-gray-500"
                onClick={() => {
                  setInitialDescription(prompt);
                  setRefinedDescription('');
                  setUseRefined(false);
                }}
              >
                {prompt}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Strategic Prompt Section */}
      {inlineMode && (
        <div className="mt-6 p-4 bg-gray-700/50 rounded-lg border border-gray-600/80">
          <h3 className="text-lg font-semibold text-purple-300 mb-3">Your Strategic Prompt</h3>
          <Textarea
            value={useRefined ? refinedDescription : initialDescription}
            readOnly
            rows={4}
            className="bg-gray-800 border-gray-600 text-white font-mono text-sm"
          />
          <div className="mt-2 text-xs text-gray-400">
            This is the prompt that will be sent to the PMO Agent.
          </div>
        </div>
      )}

      {/* Actions */}
      <div className={`flex ${inlineMode ? 'justify-center' : 'justify-end'} space-x-3 pt-4 border-t border-gray-700`}>
        {!inlineMode && (
          <Button type="button" variant="outline" onClick={onCancel} className="text-gray-300 border-gray-600 hover:bg-gray-600">
            Cancel
          </Button>
        )}
        <Button
          type="submit"
          disabled={isSubmitting || !title.trim() || !(useRefined ? refinedDescription : initialDescription).trim()}
          className={`${inlineMode ? 'w-full md:w-auto px-8 py-2' : ''} bg-purple-600 hover:bg-purple-700 text-white`}
        >
          {isSubmitting ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
          {inlineMode ? 'Send Strategic Request' : (Object.keys(initialData).length > 0 ? 'Update Request' : 'Submit Request')}
        </Button>
        {inlineMode && (
          <Button type="button" variant="outline" onClick={onCancel} className="text-gray-300 border-gray-600 hover:bg-gray-600">
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
};

export default PMOForm;
