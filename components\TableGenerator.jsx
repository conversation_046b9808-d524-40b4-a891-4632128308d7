'use client';

import React, { useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
} from '@tanstack/react-table';
import { ChevronDown, ChevronUp, ChevronsUpDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

/**
 * TableGenerator component for rendering data tables
 * @param {Object} props - Component props
 * @param {Object} props.tableConfig - Table configuration object
 */
export default function TableGenerator({ tableConfig }) {
  // If no table config is provided, show a placeholder
  if (!tableConfig) {
    return (
      <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
        <div className="mx-auto mb-4 text-zinc-600 grid place-items-center">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="3" y1="9" x2="21" y2="9"></line>
            <line x1="3" y1="15" x2="21" y2="15"></line>
            <line x1="9" y1="3" x2="9" y2="21"></line>
            <line x1="15" y1="3" x2="15" y2="21"></line>
          </svg>
        </div>
        <h3 className="text-lg font-semibold mb-2 text-zinc-300">No Table Data Available</h3>
        <p>Enter a prompt to generate a table visualization.</p>
      </div>
    );
  }

  // Extract table configuration
  const {
    title,
    subtitle,
    columns = [],
    data = [],
    pagination = true,
    rowsPerPage = 10,
    explanation
  } = tableConfig;

  // Debug output
  console.log('TableGenerator received config:', { title, subtitle, columns, dataLength: data.length, pagination, rowsPerPage });
  console.log('First data item:', data[0]);

  // Define columns for the table
  const tableColumns = useMemo(() => {
    // If columns are explicitly defined, use them
    if (columns && columns.length > 0) {
      return columns.map(column => {
        // Handle different column formats
        const accessorKey = column.accessorKey || column.key || column.field || column.accessor;
        const headerText = column.header || column.label || column.name || accessorKey;
        const columnType = column.type || 'string';

        console.log('Processing column:', { original: column, accessorKey, headerText, columnType });

        return {
          accessorKey,
          header: ({ column }) => (
            <div className="flex items-center space-x-1">
              <span>{headerText}</span>
              {column.isSortable !== false && (
                <button
                  onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                  className="ml-1 rounded-md p-1 hover:bg-zinc-700"
                >
                  {column.getIsSorted() === 'asc' ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : column.getIsSorted() === 'desc' ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronsUpDown className="h-4 w-4 opacity-50" />
                  )}
                </button>
              )}
            </div>
          ),
          cell: ({ row, column }) => {
            const value = row.getValue(column.id);
            // Format based on column type
            if (columnType === 'number') {
              return typeof value === 'number' ? value.toLocaleString() : value;
            } else if (columnType === 'currency') {
              return typeof value === 'number' ? `$${value.toLocaleString()}` : value;
            } else if (columnType === 'percent') {
              return typeof value === 'number' ? `${value}%` : value;
            } else if (columnType === 'date') {
              return value ? new Date(value).toLocaleDateString() : '';
            }
            return value;
          }
        };
      });
    }

    // If no columns are defined, infer them from the first data item
    if (data && data.length > 0) {
      return Object.keys(data[0]).map(key => {
        // Try to infer column type from data
        const sampleValue = data[0][key];
        let columnType = 'string';

        if (typeof sampleValue === 'number') {
          columnType = 'number';
          // Check if it might be a percentage
          if (key.toLowerCase().includes('percent') || key.toLowerCase().includes('rate')) {
            columnType = 'percent';
          }
          // Check if it might be currency
          else if (key.toLowerCase().includes('price') || key.toLowerCase().includes('cost') ||
                   key.toLowerCase().includes('revenue') || key.toLowerCase().includes('sales') ||
                   key.toLowerCase().includes('income') || key.toLowerCase().includes('expense')) {
            columnType = 'currency';
          }
        } else if (sampleValue instanceof Date ||
                  (typeof sampleValue === 'string' && !isNaN(Date.parse(sampleValue)))) {
          columnType = 'date';
        }

        // Format header text - convert camelCase to Title Case with Spaces
        const headerText = key
          .replace(/([A-Z])/g, ' $1') // Insert space before capital letters
          .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
          .trim(); // Remove any leading/trailing spaces

        return {
          accessorKey: key,
          header: headerText,
          type: columnType,
          cell: ({ row, column }) => {
            const value = row.getValue(column.id);

            // Format based on inferred type
            if (columnType === 'number') {
              return typeof value === 'number' ? value.toLocaleString() : value;
            } else if (columnType === 'currency') {
              return typeof value === 'number' ? `$${value.toLocaleString()}` : value;
            } else if (columnType === 'percent') {
              return typeof value === 'number' ? `${value}%` : value;
            } else if (columnType === 'date') {
              return value ? new Date(value).toLocaleDateString() : '';
            }
            return value;
          }
        };
      });
    }

    return [];
  }, [columns, data]);

  // Initialize the table
  const table = useReactTable({
    data,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      pagination: {
        pageSize: rowsPerPage,
      },
    },
  });

  return (
    <div className="space-y-4">
      {/* Table title and subtitle */}
      {title && (
        <div className="text-center">
          <h2 className="text-xl font-semibold text-white">{title}</h2>
          {subtitle && <p className="text-sm text-zinc-400">{subtitle}</p>}
        </div>
      )}

      {/* Table */}
      <div className="rounded-md border border-zinc-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead className="bg-zinc-800 text-zinc-300 sticky top-0">
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      className="px-4 py-3 text-left font-semibold border-b border-zinc-700 uppercase text-xs tracking-wider"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="divide-y divide-zinc-700 bg-zinc-900">
              {table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map(row => (
                  <tr
                    key={row.id}
                    className="hover:bg-zinc-800 transition-colors"
                  >
                    {row.getVisibleCells().map(cell => (
                      <td key={cell.id} className="px-4 py-3 text-zinc-300">
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={tableColumns.length}
                    className="px-4 py-6 text-center text-zinc-400"
                  >
                    No results found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination controls */}
      {pagination && (
        <div className="flex items-center justify-between px-2">
          <div className="text-sm text-zinc-400">
            Showing{' '}
            <span className="font-medium text-zinc-300">
              {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}
            </span>{' '}
            to{' '}
            <span className="font-medium text-zinc-300">
              {Math.min(
                (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
                table.getRowCount()
              )}
            </span>{' '}
            of{' '}
            <span className="font-medium text-zinc-300">{table.getRowCount()}</span>{' '}
            results
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronsLeft className="h-5 w-5" />
            </button>
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <span className="text-sm text-zinc-300">
              Page{' '}
              <span className="font-medium">
                {table.getState().pagination.pageIndex + 1}
              </span>{' '}
              of{' '}
              <span className="font-medium">
                {table.getPageCount()}
              </span>
            </span>
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
            <button
              className="p-1 rounded-md hover:bg-zinc-800 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
            >
              <ChevronsRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Explanation */}
      {explanation && (
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-800 rounded-md">
          <p className="text-sm text-blue-200">{explanation}</p>
        </div>
      )}
    </div>
  );
}
