/**
 * enhancedDocumentContentExtractorTool.ts
 *
 * This tool extends the genericDocumentContentExtractorTool to handle both individual files
 * and categories of files. It provides comprehensive document content extraction and analysis
 * capabilities that can be used directly by the StrategicDirectorAgent.
 */

import { genericDocumentContentExtractorTool, DocumentContentExtractorResult } from './genericDocumentContentExtractorTool';
import { adminDb } from '../../components/firebase-admin';
import { LlmProvider } from './llm-tool';
import {
  triggerGarbageCollection,
  logMemoryUsage,
  checkAndCleanMemory
} from '../utils/memoryManager';

export interface ExtendedDocumentContentExtractorOptions {
  // Existing options
  documentContent?: string;
  documentTitle?: string;
  documentId?: string;
  userQuery?: string;
  performVectorSimilarity?: boolean;

  // New options for category-based extraction
  userId: string;
  category?: string;
  filename?: string;
  namespace?: string;
  consolidateResults?: boolean; // Whether to consolidate results from multiple files
  summarizeMultipleFiles?: boolean; // Whether to summarize results from multiple files

  // Model options
  modelProvider?: LlmProvider; // The LLM provider to use (e.g., 'anthropic', 'google', 'groq')
  modelName?: string; // The specific model to use
}

export interface EnhancedDocumentContentResult {
  success: boolean;
  extractedContent?: string;
  consolidatedContent?: string;
  results?: DocumentContentExtractorResult[];
  files?: Array<{
    title: string;
    namespace: string;
  }>;
  error?: string;
}

/**
 * Enhanced document content extractor that handles both individual files and categories
 */
export const enhancedDocumentContentExtractorTool = {
  /**
   * Get the tool definition for function calling
   *
   * @returns Tool definition object
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "enhancedDocumentContentExtractorTool",
        description: "Retrieves and analyzes content from specific documents (identified by filename, ID/namespace, or category) or directly provided text. Can consolidate findings from multiple files and optionally summarize them, potentially focusing on a user's query.",
        parameters: {
          type: "object",
          properties: {
            documentContent: {
              type: "string",
              description: "Direct content of the document to analyze. If provided, other lookup methods (filename, category, namespace) are ignored for this call."
            },
            documentTitle: {
              type: "string",
              description: "Optional title of the document, especially if documentContent is provided."
            },
            documentId: {
              type: "string",
              description: "Optional document identifier (can be a namespace). Used if documentContent is provided, or for context."
            },
            userQuery: {
              type: "string",
              description: "Optional user query to focus the extraction and analysis."
            },
            performVectorSimilarity: {
              type: "boolean",
              description: "Whether to perform vector similarity assessment if userQuery is provided (applies when processing single documents or direct content)."
            },
            userId: {
              type: "string",
              description: "The user ID for accessing user-specific document collections. This is required for any operations involving database lookups (by filename, category, or namespace)."
            },
            category: {
              type: "string",
              description: "Category of documents to process. Used if filename, namespace, or documentContent are not provided."
            },
            filename: {
              type: "string",
              description: "Filename of the document to process. Used if namespace or documentContent are not provided."
            },
            namespace: {
              type: "string",
              description: "Namespace (unique ID) of the document to process. Takes precedence over filename and category if documentContent is not provided."
            },
            consolidateResults: {
              type: "boolean",
              description: "Whether to consolidate results from multiple files into a single report (e.g., when processing a category)."
            },
            summarizeMultipleFiles: {
              type: "boolean",
              description: "Whether to generate a summary if multiple files are processed (e.g., from a category or consolidated results)."
            },
            modelProvider: {
              type: "string",
              description: "Optional LLM provider to use (e.g., 'anthropic', 'google', 'groq', 'openai')."
            },
            modelName: {
              type: "string",
              description: "Optional specific model name to use for content extraction or summarization."
            }
          },
          required: ["userId"] // userId is essential for most operations.
        }
      }
    };
  },
  /**
   * Process documents based on category, filename, or namespace
   */
  async processDocuments(options: ExtendedDocumentContentExtractorOptions): Promise<EnhancedDocumentContentResult> {
    try {
      console.log(`EnhancedDocumentContentExtractor: Processing request with options:`, {
        userId: options.userId,
        category: options.category,
        filename: options.filename,
        namespace: options.namespace,
        hasDirectContent: !!options.documentContent,
        userQuery: options.userQuery ? options.userQuery.substring(0, 50) + '...' : undefined
      });

      // Case 1: Direct document content provided
      if (options.documentContent) {
        const result = await this.processSingleDocument(options);
        return {
          success: result.success,
          extractedContent: result.extractedContent,
          results: [result],
          error: result.error
        };
      }

      // Case 2: Lookup by namespace (highest priority)
      if (options.namespace) {
        console.log(`EnhancedDocumentContentExtractor: Looking up content by namespace: ${options.namespace}`);
        const content = await this.getDocumentContentByNamespace(options.userId, options.namespace);
        if (content) {
          const result = await this.processSingleDocument({
            ...options,
            documentContent: content.content,
            documentTitle: content.title,
            documentId: options.namespace
          });

          return {
            success: result.success,
            extractedContent: result.extractedContent,
            results: [result],
            files: [{
              title: content.title,
              namespace: options.namespace
            }],
            error: result.error
          };
        } else {
          console.warn(`EnhancedDocumentContentExtractor: No content found for namespace: ${options.namespace}`);

          // Provide a specific error message for namespace lookup failure
          return {
            success: false,
            error: `Document with ID ${options.namespace} could not be found. The document may not exist or may be stored in a different format. Please check the document ID and try again.`
          };
        }
      }

      // Case 3: Lookup by filename
      if (options.filename) {
        console.log(`EnhancedDocumentContentExtractor: Looking up content by filename: ${options.filename}`);
        const fileInfo = await this.getDocumentByFilename(options.userId, options.filename);
        if (fileInfo) {
          const result = await this.processSingleDocument({
            ...options,
            documentContent: fileInfo.content,
            documentTitle: fileInfo.title,
            documentId: fileInfo.namespace
          });

          return {
            success: result.success,
            extractedContent: result.extractedContent,
            results: [result],
            files: [{
              title: fileInfo.title,
              namespace: fileInfo.namespace
            }],
            error: result.error
          };
        } else {
          console.warn(`EnhancedDocumentContentExtractor: No content found for filename: ${options.filename}`);

          // Provide a specific error message for filename lookup failure
          return {
            success: false,
            error: `Document with filename "${options.filename}" could not be found. Please check if the filename is correct and try again.`
          };
        }
      }

      // Case 4: Lookup by category
      if (options.category && options.category !== 'Unknown') {
        console.log(`EnhancedDocumentContentExtractor: Processing documents by category: ${options.category}`);
        return await this.processDocumentsByCategory(options);
      }

      // No valid input provided - construct a helpful error message
      let errorMessage = "No valid document content, filename, namespace, or category provided.";

      // Add more context to the error message based on what was attempted
      if (options.namespace) {
        errorMessage += ` Document with ID "${options.namespace}" could not be found.`;
      }
      if (options.filename) {
        errorMessage += ` Document with filename "${options.filename}" could not be found.`;
      }
      if (options.category) {
        errorMessage += ` No documents found in category "${options.category}".`;
      }

      errorMessage += " Please check your document reference and try again.";

      console.error(`EnhancedDocumentContentExtractor: ${errorMessage}`);
      return {
        success: false,
        error: errorMessage
      };
    } catch (error) {
      console.error("Error in enhanced document content extractor:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  },

  /**
   * Process a single document
   */
  async processSingleDocument(options: ExtendedDocumentContentExtractorOptions): Promise<DocumentContentExtractorResult> {
    console.log(`EnhancedDocumentContentExtractor: Processing single document: ${options.documentTitle || 'Untitled'}`);

    // Determine the appropriate model provider
    let modelProvider = options.modelProvider;
    const modelName = options.modelName;

    // If modelProvider is not set but modelName is, try to infer the provider from the model name
    if (!modelProvider && modelName) {
      if (modelName.startsWith('gemini-')) {
        modelProvider = 'google';
        console.log(`EnhancedDocumentContentExtractor: Inferred provider 'google' from model name '${modelName}'`);
      } else if (modelName.startsWith('gpt-') || modelName.startsWith('o3-')) {
        modelProvider = 'openai';
        console.log(`EnhancedDocumentContentExtractor: Inferred provider 'openai' from model name '${modelName}'`);
      } else if (modelName.startsWith('claude-')) {
        modelProvider = 'anthropic';
        console.log(`EnhancedDocumentContentExtractor: Inferred provider 'anthropic' from model name '${modelName}'`);
      } else if (modelName.includes('llama') ) {
        modelProvider = 'groq';
        console.log(`EnhancedDocumentContentExtractor: Inferred provider 'groq' from model name '${modelName}'`);
      }
    }

    // Handle deepseek provider by mapping it to groq
    if (modelProvider === 'deepseek') {
      modelProvider = 'groq';
      console.log(`EnhancedDocumentContentExtractor: Mapped 'deepseek' provider to 'groq'`);
    }

    // Log model information
    console.log(`EnhancedDocumentContentExtractor: Using model provider: ${modelProvider || 'default'}, model: ${modelName || 'default'}`);

    // Use the existing genericDocumentContentExtractorTool
    console.log(`EnhancedDocumentContentExtractor: Calling genericDocumentContentExtractorTool with performVectorSimilarity=${options.performVectorSimilarity}, userId=${options.userId}, documentId=${options.documentId || 'none'}`);

    // Make sure performVectorSimilarity is explicitly set to true if userQuery is provided
    const performVectorSimilarity = options.userQuery ? true : options.performVectorSimilarity;

    if (performVectorSimilarity) {
      console.log(`EnhancedDocumentContentExtractor: Vector similarity is ENABLED for this request`);
    }

    return await genericDocumentContentExtractorTool.process({
      documentContent: options.documentContent!,
      documentTitle: options.documentTitle,
      documentId: options.documentId,
      userQuery: options.userQuery,
      performVectorSimilarity: performVectorSimilarity,
      userId: options.userId,
      modelProvider: modelProvider as LlmProvider | undefined,
      modelName: modelName,
      category: options.category // Pass the category for category-based vector similarity
    });
  },

  /**
   * Process documents by category
   */
  async processDocumentsByCategory(options: ExtendedDocumentContentExtractorOptions): Promise<EnhancedDocumentContentResult> {
    // Get all files in the category
    const files = await this.getDocumentsByCategory(options.userId, options.category!);

    console.log(`EnhancedDocumentContentExtractor: Found ${files.length} documents in category "${options.category}"`);
    logMemoryUsage('Before processing category documents');

    if (files.length === 0) {
      return {
        success: false,
        error: `No documents found in category "${options.category}"`
      };
    }

    // Process each file SEQUENTIALLY instead of in parallel to avoid memory issues
    const results: DocumentContentExtractorResult[] = [];
    let successCount = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`EnhancedDocumentContentExtractor: Processing document ${i+1}/${files.length}: ${file.title}`);

      try {
        const result = await this.processSingleDocument({
          ...options,
          documentContent: file.content,
          documentTitle: file.title,
          documentId: file.namespace
        });

        if (result.success) {
          results.push(result);
          successCount++;
        }

        // Explicitly clean up memory after each document
        if (i < files.length - 1) {
          // Only log and clean if not the last document
          logMemoryUsage(`After processing document ${i+1}/${files.length}`);
          checkAndCleanMemory(70); // Trigger GC if heap usage is above 70%

          // Small delay to allow GC to complete
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      } catch (error) {
        console.error(`Error processing document ${file.title}:`, error);
        // Continue with next document
      }
    }

    // Log final memory usage
    logMemoryUsage('After processing all category documents');
    triggerGarbageCollection(); // Force GC after all documents are processed

    console.log(`EnhancedDocumentContentExtractor: Successfully processed ${successCount} out of ${files.length} documents`);

    if (results.length === 0) {
      return {
        success: false,
        error: "Failed to process any documents in the category"
      };
    }

    // Consolidate results
    if (options.consolidateResults) {
      return this.consolidateResults(results, files, options);
    }

    // Return all results
    return {
      success: true,
      results: results,
      files: files.map(file => ({
        title: file.title,
        namespace: file.namespace
      }))
    };
  },

  /**
   * Consolidate results from multiple documents
   */
  async consolidateResults(
    results: DocumentContentExtractorResult[],
    files: Array<{title: string, namespace: string, content: string}>,
    options: ExtendedDocumentContentExtractorOptions
  ): Promise<EnhancedDocumentContentResult> {
    console.log(`EnhancedDocumentContentExtractor: Consolidating results from ${results.length} documents`);
    logMemoryUsage('Before consolidating results');

    // Combine extracted content more efficiently
    let combinedContent = '';

    // Process in smaller batches to avoid large string concatenations
    const BATCH_SIZE = 10;

    for (let i = 0; i < results.length; i += BATCH_SIZE) {
      const batchEnd = Math.min(i + BATCH_SIZE, results.length);
      console.log(`EnhancedDocumentContentExtractor: Combining batch ${i+1}-${batchEnd} of ${results.length} documents`);

      // Create a batch of content
      const batchContent = results
        .slice(i, batchEnd)
        .map((result, batchIndex) => {
          const fileIndex = i + batchIndex;
          return `## Document: ${files[fileIndex].title}\n\n${result.extractedContent || 'No content extracted'}`;
        })
        .join('\n\n---\n\n');

      // Add to combined content
      combinedContent += (i > 0 ? '\n\n---\n\n' : '') + batchContent;

      // Check memory after each batch
      if (i + BATCH_SIZE < results.length) {
        logMemoryUsage(`After combining batch ${i+1}-${batchEnd}`);
        checkAndCleanMemory(70);
      }
    }

    // If summarization is requested, summarize the combined content
    let finalContent = combinedContent;
    if (options.summarizeMultipleFiles && results.length > 1) {
      console.log(`EnhancedDocumentContentExtractor: Summarizing content from ${results.length} documents`);
      finalContent = await this.summarizeMultipleDocuments(combinedContent, files, options);
    }

    // Clean up memory before returning
    logMemoryUsage('After consolidation and summarization');
    triggerGarbageCollection();

    // Create a clean file list without the content to save memory
    const fileList = files.map(file => ({
      title: file.title,
      namespace: file.namespace
    }));

    return {
      success: true,
      extractedContent: combinedContent,
      consolidatedContent: finalContent,
      results: results,
      files: fileList
    };
  },

  /**
   * Utility function to estimate token count based on text length
   * This is a rough estimate - 1 token is approximately 4 characters for English text
   */
  estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  },

  /**
   * Split text into chunks of approximately equal size
   */
  splitTextIntoChunks(text: string, maxChunkSize: number = 10000): string[] {
    console.log(`EnhancedDocumentContentExtractor: Splitting text of length ${text.length} into chunks of ~${maxChunkSize} tokens`);

    // Safety check for extremely large texts
    if (text.length > 10000000) { // 10MB text
      console.warn(`EnhancedDocumentContentExtractor: Extremely large text detected (${text.length} chars), using aggressive chunking`);
      return this.splitExtremelyLargeText(text, maxChunkSize);
    }

    // If text is already small enough, return it as a single chunk
    if (this.estimateTokenCount(text) <= maxChunkSize) {
      return [text];
    }

    const chunks: string[] = [];

    // Try to split on document boundaries first (marked by ## Document: in our case)
    const documentSections = text.split(/(?=## Document:)/);

    if (documentSections.length > 1) {
      console.log(`EnhancedDocumentContentExtractor: Found ${documentSections.length} document sections to process separately`);

      let currentChunk = "";

      for (const section of documentSections) {
        // If adding this section would exceed the chunk size, save current chunk and start a new one
        if (currentChunk && this.estimateTokenCount(currentChunk + section) > maxChunkSize) {
          chunks.push(currentChunk);
          currentChunk = section;
        } else {
          currentChunk += section;
        }

        // If current section alone is too large, split it further
        if (this.estimateTokenCount(currentChunk) > maxChunkSize) {
          // Split large sections by paragraphs
          const furtherChunks = this.splitLargeTextByParagraphs(currentChunk, maxChunkSize);
          // Add all but the last chunk
          chunks.push(...furtherChunks.slice(0, -1));
          // Keep the last chunk as the current one
          currentChunk = furtherChunks[furtherChunks.length - 1] || "";
        }
      }

      // Add the last chunk if it's not empty
      if (currentChunk) {
        chunks.push(currentChunk);
      }
    } else {
      // If no document sections, split by paragraphs
      chunks.push(...this.splitLargeTextByParagraphs(text, maxChunkSize));
    }

    console.log(`EnhancedDocumentContentExtractor: Split text into ${chunks.length} chunks`);
    return chunks;
  },

  /**
   * Handle extremely large texts with a more aggressive chunking strategy
   * This is a fallback for very large documents that might cause memory issues
   */
  splitExtremelyLargeText(text: string, maxChunkSize: number): string[] {
    // For extremely large texts, we'll use a fixed-size chunking approach
    // to avoid memory issues during processing
    const approximateCharsPerToken = 4;
    const maxChunkChars = maxChunkSize * approximateCharsPerToken;
    const chunks: string[] = [];

    // Process the text in fixed-size segments
    for (let i = 0; i < text.length; i += maxChunkChars) {
      const chunk = text.substring(i, i + maxChunkChars);

      // Try to find a natural break point (paragraph or sentence end)
      let breakPoint = chunk.lastIndexOf('\n\n');
      if (breakPoint === -1 || breakPoint < maxChunkChars * 0.5) {
        // If no paragraph break or it's too early, try sentence break
        breakPoint = chunk.lastIndexOf('. ');
        if (breakPoint === -1 || breakPoint < maxChunkChars * 0.7) {
          // If no good break point, just use the full chunk
          breakPoint = chunk.length;
        } else {
          breakPoint += 2; // Include the period and space
        }
      }

      // Add the chunk up to the break point
      chunks.push(chunk.substring(0, breakPoint));

      // Adjust the index to account for the break point
      i -= (chunk.length - breakPoint);
    }

    console.log(`EnhancedDocumentContentExtractor: Split extremely large text into ${chunks.length} fixed-size chunks`);
    return chunks;
  },

  /**
   * Split large text by paragraphs to create chunks
   */
  splitLargeTextByParagraphs(text: string, maxChunkSize: number): string[] {
    const chunks: string[] = [];
    const paragraphs = text.split(/\n\s*\n/);
    let currentChunk = "";

    for (const paragraph of paragraphs) {
      // If adding this paragraph would exceed the chunk size, save current chunk and start a new one
      if (currentChunk && this.estimateTokenCount(currentChunk + paragraph) > maxChunkSize) {
        chunks.push(currentChunk);
        currentChunk = paragraph;
      } else {
        // Add a paragraph separator if this isn't the first paragraph in the chunk
        if (currentChunk) {
          currentChunk += "\n\n";
        }
        currentChunk += paragraph;
      }

      // If a single paragraph is too large, split it by sentences
      if (this.estimateTokenCount(currentChunk) > maxChunkSize) {
        const sentenceChunks = this.splitLargeTextBySentences(currentChunk, maxChunkSize);
        chunks.push(...sentenceChunks.slice(0, -1));
        currentChunk = sentenceChunks[sentenceChunks.length - 1] || "";
      }
    }

    // Add the last chunk if it's not empty
    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks;
  },

  /**
   * Split large text by sentences as a last resort
   */
  splitLargeTextBySentences(text: string, maxChunkSize: number): string[] {
    const chunks: string[] = [];
    // Simple sentence splitting - not perfect but good enough for this purpose
    const sentences = text.split(/(?<=[.!?])\s+/);
    let currentChunk = "";

    for (const sentence of sentences) {
      // If adding this sentence would exceed the chunk size, save current chunk and start a new one
      if (currentChunk && this.estimateTokenCount(currentChunk + sentence) > maxChunkSize) {
        chunks.push(currentChunk);
        currentChunk = sentence;
      } else {
        // Add a space if this isn't the first sentence in the chunk
        if (currentChunk) {
          currentChunk += " ";
        }
        currentChunk += sentence;
      }

      // If a single sentence is too large (rare but possible), split it arbitrarily
      if (this.estimateTokenCount(currentChunk) > maxChunkSize) {
        // Split by a fixed number of characters as a last resort
        const approximateCharsPerToken = 4;
        const maxChunkChars = maxChunkSize * approximateCharsPerToken;

        while (currentChunk.length > maxChunkChars) {
          chunks.push(currentChunk.substring(0, maxChunkChars));
          currentChunk = currentChunk.substring(maxChunkChars);
        }
      }
    }

    // Add the last chunk if it's not empty
    if (currentChunk) {
      chunks.push(currentChunk);
    }

    return chunks;
  },

  /**
   * Process a single chunk with the specified LLM
   */
  async processChunkWithLLM(
    chunk: string,
    chunkIndex: number,
    totalChunks: number,
    modelProvider: LlmProvider,
    modelName: string,
    options?: ExtendedDocumentContentExtractorOptions
  ): Promise<string> {
    console.log(`EnhancedDocumentContentExtractor: Processing chunk ${chunkIndex + 1}/${totalChunks} with ${modelProvider}/${modelName}`);

    // Create a summarization prompt that focuses on the user query if available
    const prompt = options && options.userQuery && options.userQuery.trim().length > 0
      ? `
      You are an expert document analyzer focused on extracting ONLY information relevant to a specific query.
      This is chunk ${chunkIndex + 1} of ${totalChunks} from a larger document collection.

      USER QUERY: "${options.userQuery}"

      Text to analyze:
      ${chunk}

      INSTRUCTIONS:
      1. Focus EXCLUSIVELY on finding and extracting information from this text that directly helps answer the user's query.
      2. Ignore any content that is not relevant to the query.
      3. Your response should be concise and focused ONLY on information relevant to the query.
      `
      : `
      You are an expert document summarizer. Your task is to create a concise summary of the following text.
      This is chunk ${chunkIndex + 1} of ${totalChunks} from a larger document collection.

      Text to summarize:
      ${chunk}

      Please create a summary that:
      1. Captures the main points and key information
      2. Preserves important details and context
      3. Is well-structured with clear organization
      4. Can be combined with summaries of other chunks to form a coherent whole

      Your summary should be comprehensive yet concise.
      `;

    try {
      // Use the appropriate provider based on the options
      switch (modelProvider) {
        case 'google':
          const { processWithGoogleAI } = await import('./google-ai');
          return await processWithGoogleAI({
            prompt,
            model: modelName
          });

        case 'openai':
          const { processWithOpenAI } = await import('./openai');
          return await processWithOpenAI({
            prompt,
            model: modelName,
            modelOptions: {
              temperature: 0.2,
              maxTokens: 4000
            }
          });

        case 'anthropic':
          const { processWithAnthropic } = await import('./anthropic-ai');
          return await processWithAnthropic({
            prompt,
            model: modelName,
            modelOptions: {
              temperature: 0.2,
              maxTokens: 4000
            }
          });

        // deepseek should be handled by mapping to groq in the summarizeMultipleDocuments method
        // This case should not be reached, but just in case:
        case 'deepseek':
          console.log(`EnhancedDocumentContentExtractor: Mapping 'deepseek' provider to 'groq' in processChunkWithLLM`);
          const { processWithGroq } = await import('./groq-ai');
          return await processWithGroq({
            prompt,
            model: modelName || "llama-3.3-70b-versatile",
            modelOptions: {
              temperature: 0.2,
              maxTokens: 4000
            }
          });

        default:
          // Default to Google
          const { processWithGoogleAI: defaultGoogleAI } = await import('./google-ai');
          return await defaultGoogleAI({
            prompt,
            model: modelName
          });
      }
    } catch (error) {
      console.error(`Error processing chunk ${chunkIndex + 1}/${totalChunks}:`, error);
      // Return a placeholder for failed chunks
      return `[Error summarizing chunk ${chunkIndex + 1}: ${error instanceof Error ? error.message : String(error)}]`;
    }
  },

  /**
   * Process large documents in chunks to avoid memory issues
   */
  async summarizeHugeDocuments(
    combinedContent: string,
    files: Array<{title: string, namespace: string, content: string}>,
    modelProvider: LlmProvider,
    modelName: string,
    maxChunkSize: number = 500,
    options?: ExtendedDocumentContentExtractorOptions
  ): Promise<string> {
    console.log(`EnhancedDocumentContentExtractor: Using 32-chunk processing for large document collection (${files.length} files, ~${this.estimateTokenCount(combinedContent)} tokens)`);
    logMemoryUsage('Before chunking document');

    // Step 1: Split the combined content into manageable chunks
    const chunks = this.splitTextIntoChunks(combinedContent, maxChunkSize);
    console.log(`EnhancedDocumentContentExtractor: Split content into ${chunks.length} chunks for processing`);

    // Free up memory after chunking
    combinedContent = ''; // Allow GC to reclaim this large string
    triggerGarbageCollection();
    logMemoryUsage('After chunking document');

    // Step 2: Process chunks SEQUENTIALLY to avoid memory issues
    const chunkSummaries: string[] = [];

    for (let i = 0; i < chunks.length; i++) {
      console.log(`EnhancedDocumentContentExtractor: Processing chunk ${i + 1}/${chunks.length}`);

      try {
        // Process each chunk individually
        const summary = await this.processChunkWithLLM(
          chunks[i],
          i,
          chunks.length,
          modelProvider,
          modelName,
          options
        );

        chunkSummaries.push(summary);

        // Free the chunk from memory
        chunks[i] = '';

        // Check memory usage and clean if necessary
        logMemoryUsage(`After processing chunk ${i + 1}/${chunks.length}`);
        checkAndCleanMemory(50); // More aggressive memory cleanup

        // Longer delay to allow GC to complete
        await new Promise(resolve => setTimeout(resolve, 250));
      } catch (error) {
        console.error(`Error processing chunk ${i + 1}/${chunks.length}:`, error);
        // Add a placeholder for the failed chunk
        chunkSummaries.push(`[Error processing chunk ${i + 1}/${chunks.length}]`);
      }
    }

    // Step 3: Combine the chunk summaries
    console.log(`EnhancedDocumentContentExtractor: Successfully processed ${chunks.length} chunks, now creating final summary`);
    logMemoryUsage('Before creating final summary');

    // Step 4: Create a final summary if needed
    if (chunks.length === 1) {
      // If there was only one chunk, return its summary directly
      return chunkSummaries[0];
    }

    // For many chunks, we may need to summarize in a hierarchical fashion
    let finalSummary: string;

    if (chunks.length > 10) {
      // For very large documents, use a hierarchical approach
      console.log(`EnhancedDocumentContentExtractor: Large number of chunks (${chunks.length}), using hierarchical summarization`);
      finalSummary = await this.createHierarchicalSummary(chunkSummaries, files, modelProvider, modelName, options);
    } else {
      // For a moderate number of chunks, combine them directly
      const combinedSummaries = chunkSummaries.join("\n\n---\n\n");
      finalSummary = await this.createFinalSummary(combinedSummaries, chunks.length, files.length, modelProvider, modelName, options);
    }

    // Final memory cleanup
    logMemoryUsage('After creating final summary');
    triggerGarbageCollection();

    return finalSummary;
  },

  /**
   * Create a hierarchical summary for very large documents
   * This breaks the summarization into multiple levels to handle many chunks
   */
  async createHierarchicalSummary(
    chunkSummaries: string[],
    files: Array<{title: string, namespace: string, content: string}>,
    modelProvider: LlmProvider,
    modelName: string,
    options?: ExtendedDocumentContentExtractorOptions
  ): Promise<string> {
    console.log(`EnhancedDocumentContentExtractor: Creating hierarchical summary from ${chunkSummaries.length} chunk summaries`);
    logMemoryUsage('Before hierarchical summarization');

    // Step 1: Group summaries into manageable batches
    const SUMMARY_BATCH_SIZE = 32; // Adjusted to use 32 chunks
    const summaryBatches: string[][] = [];

    for (let i = 0; i < chunkSummaries.length; i += SUMMARY_BATCH_SIZE) {
      summaryBatches.push(chunkSummaries.slice(i, i + SUMMARY_BATCH_SIZE));
    }

    // Step 2: Create intermediate summaries for each batch SEQUENTIALLY
    const intermediateSummaries: string[] = [];

    for (let batchIndex = 0; batchIndex < summaryBatches.length; batchIndex++) {
      console.log(`EnhancedDocumentContentExtractor: Processing summary batch ${batchIndex + 1}/${summaryBatches.length}`);

      const batch = summaryBatches[batchIndex];
      const batchText = batch.join("\n\n---\n\n");

      // Create a batch prompt that focuses on the user query if available
      const batchPrompt = options && options.userQuery && options.userQuery.trim().length > 0
        ? `
        You are an expert document analyzer focused on extracting ONLY information relevant to a specific query.
        This is batch ${batchIndex + 1} of ${summaryBatches.length} from a larger document collection.

        USER QUERY: "${options.userQuery}"

        Text to analyze:
        ${batchText}

        INSTRUCTIONS:
        1. Focus EXCLUSIVELY on finding and extracting information from this text that directly helps answer the user's query.
        2. Ignore any content that is not relevant to the query.
        3. Your response should be concise and focused ONLY on information relevant to the query.
        `
        : `
        You are an expert document summarizer. Your task is to create a concise summary of the following text.
        This is batch ${batchIndex + 1} of ${summaryBatches.length} from a larger document collection.

        Text to summarize:
        ${batchText}

        Please create a summary that captures the main points and key information.
        Your summary should be well-structured and preserve important details.
        `;

      try {
        // Use the appropriate provider for the intermediate summary
        let batchSummary: string;

        switch (modelProvider) {
          case 'google':
            const { processWithGoogleAI } = await import('./google-ai');
            batchSummary = await processWithGoogleAI({
              prompt: batchPrompt,
              model: modelName
            });
            break;
          case 'openai':
            const { processWithOpenAI } = await import('./openai');
            batchSummary = await processWithOpenAI({
              prompt: batchPrompt,
              model: modelName,
              modelOptions: {
                temperature: 0.2,
                maxTokens: 4000
              }
            });
            break;
          case 'anthropic':
            const { processWithAnthropic } = await import('./anthropic-ai');
            batchSummary = await processWithAnthropic({
              prompt: batchPrompt,
              model: modelName,
              modelOptions: {
                temperature: 0.2,
                maxTokens: 4000
              }
            });
            break;
          default:
            const { processWithGoogleAI: defaultGoogleAI } = await import('./google-ai');
            batchSummary = await defaultGoogleAI({
              prompt: batchPrompt,
              model: modelName
            });
        }

        intermediateSummaries.push(batchSummary);

        // Clean up memory after each batch
        logMemoryUsage(`After processing summary batch ${batchIndex + 1}/${summaryBatches.length}`);
        checkAndCleanMemory(50); // More aggressive memory cleanup

        // Longer delay to allow GC to complete
        await new Promise(resolve => setTimeout(resolve, 250));
      } catch (error) {
        console.error(`Error creating intermediate summary for batch ${batchIndex + 1}:`, error);
        intermediateSummaries.push(`[Error summarizing batch ${batchIndex + 1}]`);
      }
    }

    // Step 3: Create the final summary from the intermediate summaries
    logMemoryUsage('Before creating final hierarchical summary');
    const combinedIntermediateSummaries = intermediateSummaries.join("\n\n---\n\n");
    const finalSummary = await this.createFinalSummary(
      combinedIntermediateSummaries,
      summaryBatches.length,
      files.length,
      modelProvider,
      modelName,
      options
    );

    // Final cleanup
    logMemoryUsage('After creating final hierarchical summary');
    triggerGarbageCollection();

    return finalSummary;
  },

  /**
   * Create a final summary from combined summaries
   */
  async createFinalSummary(
    combinedSummaries: string,
    numChunks: number,
    numFiles: number,
    modelProvider: LlmProvider,
    modelName: string,
    options?: ExtendedDocumentContentExtractorOptions
  ): Promise<string> {
    // Create a final summarization prompt that focuses on the user query if available
    const finalPrompt = options && options.userQuery && options.userQuery.trim().length > 0
      ? `
      You are an expert document analyzer focused on extracting ONLY information relevant to a specific query.

      USER QUERY: "${options.userQuery}"

      The following text contains summaries from ${numChunks} different chunks of a larger collection of ${numFiles} documents:

      ${combinedSummaries}

      INSTRUCTIONS:
      1. Focus EXCLUSIVELY on finding and extracting information from these summaries that directly helps answer the user's query.
      2. Ignore any content that is not relevant to the query.
      3. Do NOT create a comprehensive summary - focus only on query-relevant information.
      4. Structure your response as follows:
         a. Brief context about the documents (1-2 sentences)
         b. Directly relevant information from the documents that addresses the query
         c. Brief summary of how this information answers the query

      Your response should be concise and focused ONLY on information relevant to the query.
      `
      : `
      You are an expert document summarizer. Your task is to create a unified summary from the following summaries of different parts of a document collection.

      These summaries come from ${numChunks} different chunks of a larger collection of ${numFiles} documents:

      ${combinedSummaries}

      Please create a cohesive final summary that:
      1. Identifies the main themes across all documents
      2. Highlights key information from each document
      3. Notes any contradictions or complementary information between documents
      4. Provides a coherent overview that would help someone understand the collective content

      Your summary should be well-structured with clear headings and should maintain the most important details from the original documents.
      `;

    try {
      // Use the appropriate provider for the final summary
      switch (modelProvider) {
        case 'google':
          const { processWithGoogleAI } = await import('./google-ai');
          return await processWithGoogleAI({
            prompt: finalPrompt,
            model: modelName
          });

        case 'openai':
          const { processWithOpenAI } = await import('./openai');
          return await processWithOpenAI({
            prompt: finalPrompt,
            model: modelName,
            modelOptions: {
              temperature: 0.2,
              maxTokens: 4000
            }
          });

        case 'anthropic':
          const { processWithAnthropic } = await import('./anthropic-ai');
          return await processWithAnthropic({
            prompt: finalPrompt,
            model: modelName,
            modelOptions: {
              temperature: 0.2,
              maxTokens: 4000
            }
          });

        // deepseek is handled by mapping to groq in the summarizeMultipleDocuments method

        default:
          // Default to Google
          const { processWithGoogleAI: defaultGoogleAI } = await import('./google-ai');
          return await defaultGoogleAI({
            prompt: finalPrompt,
            model: modelName
          });
      }
    } catch (finalError) {
      console.error("Error creating final summary, returning combined summaries:", finalError);
      // If final summarization fails, return the combined summaries
      return `# Combined Document Summaries\n\n${combinedSummaries}`;
    }
  },

  /**
   * Summarize content from multiple documents
   */
  async summarizeMultipleDocuments(
    combinedContent: string,
    files: Array<{title: string, namespace: string, content: string}>,
    options: ExtendedDocumentContentExtractorOptions
  ): Promise<string> {
    // Parse the model name if it contains provider information
    let modelProvider: LlmProvider = options.modelProvider || 'google';
    // Handle deepseek provider by mapping it to groq
    if (modelProvider === 'deepseek') {
      modelProvider = 'groq';
      console.log(`EnhancedDocumentContentExtractor: Mapped 'deepseek' provider to 'groq'`);
    }

    let modelName = options.modelName;

    // If modelName contains provider information (e.g., "gemini-2.5-pro")
    if (modelName && modelName.includes('/')) {
      const parts = modelName.split('/');
      let parsedProvider = parts[0];
      // Handle deepseek provider in combined format
      if (parsedProvider === 'deepseek') {
        parsedProvider = 'groq';
        console.log(`EnhancedDocumentContentExtractor: Mapped 'deepseek' provider to 'groq' from combined format`);
      }
      modelProvider = parsedProvider as LlmProvider;
      modelName = parts[1];
      console.log(`EnhancedDocumentContentExtractor: Parsed provider "${modelProvider}" and model "${modelName}" from combined format`);
    }

    // If no model name is provided, use defaults based on provider
    if (!modelName) {
      if (modelProvider === 'google') {
        modelName = "gemini-1.5-pro"; // Updated to use the latest Gemini model
      } else if (modelProvider === 'openai') {
        modelName = "o3-2025-04-16";
      } else if (modelProvider === 'anthropic') {
        modelName = "claude-sonnet-4-20250514";
      } else if (modelProvider === 'deepseek') {
        modelName = "llama-3.3-70b-versatile";
      } else {
        modelName = "gemini-2.5-pro"; // Default fallback updated
      }
    }

    console.log(`EnhancedDocumentContentExtractor: Summarizing with provider ${modelProvider} and model ${modelName}`);

    // Check if the content is too large for a single processing
    const estimatedTokens = this.estimateTokenCount(combinedContent);
    const TOKEN_THRESHOLD = 500; // Lower threshold for using chunked processing to prevent memory issues

    if (estimatedTokens > TOKEN_THRESHOLD) {
      console.log(`EnhancedDocumentContentExtractor: Content is large (${estimatedTokens} estimated tokens), using 32-chunk processing`);

      try {
        return await this.summarizeHugeDocuments(
          combinedContent,
          files,
          modelProvider,
          modelName,
          500, // Default chunk size
          options
        );
      } catch (chunkingError) {
        console.error("Error in chunked processing, trying with smaller chunks:", chunkingError);

        // If the first attempt fails, try with even smaller chunks
        return await this.summarizeHugeDocuments(
          combinedContent,
          files,
          modelProvider,
          modelName,
          250, // Smaller chunk size for fallback
          options
        );
      }
    }

    // For smaller content, use the original approach
    console.log(`EnhancedDocumentContentExtractor: Content is manageable (${estimatedTokens} estimated tokens), using standard processing`);

    // Create a summarization prompt that focuses on the user query if available
    const prompt = options.userQuery && options.userQuery.trim().length > 0
      ? `
      You are an expert document analyzer focused on extracting ONLY information relevant to a specific query.

      USER QUERY: "${options.userQuery}"

      The following text contains content extracted from ${files.length} different documents:

      ${combinedContent}

      INSTRUCTIONS:
      1. Focus EXCLUSIVELY on finding and extracting information from these documents that directly helps answer the user's query.
      2. Ignore any content that is not relevant to the query.
      3. Do NOT create a comprehensive summary - focus only on query-relevant information.
      4. Structure your response as follows:
         a. Brief context about the documents (1-2 sentences)
         b. Directly relevant information from the documents that addresses the query
         c. Brief summary of how this information answers the query

      Your response should be concise and focused ONLY on information relevant to the query.
      `
      : `
      You are an expert document summarizer. Your task is to create a comprehensive summary of multiple documents.

      The following text contains content extracted from ${files.length} different documents:

      ${combinedContent}

      Please create a unified summary that:
      1. Identifies the main themes across all documents
      2. Highlights key information from each document
      3. Notes any contradictions or complementary information between documents
      4. Provides a coherent overview that would help someone understand the collective content

      Your summary should be well-structured with clear headings and should maintain the most important details from each document.
      `;

    try {
      // Use the appropriate provider based on the options
      switch (modelProvider) {
        case 'google':
          const { processWithGoogleAI } = await import('./google-ai');
          return await processWithGoogleAI({
            prompt,
            model: modelName
          });

        case 'openai':
          const { processWithOpenAI } = await import('./openai');
          return await processWithOpenAI({
            prompt,
            model: modelName,
            modelOptions: {
              temperature: 0.2,
              maxTokens: 10000
            }
          });

        case 'anthropic':
          const { processWithAnthropic } = await import('./anthropic-ai');
          return await processWithAnthropic({
            prompt,
            model: modelName,
            modelOptions: {
              temperature: 0.2,
              maxTokens: 10000
            }
          });

        // deepseek should be handled by mapping to groq in the summarizeMultipleDocuments method
        // This case should not be reached, but just in case:
        case 'deepseek':
          console.log(`EnhancedDocumentContentExtractor: Mapping 'deepseek' provider to 'groq' in switch statement`);
          const { processWithGroq } = await import('./groq-ai');
          return await processWithGroq({
            prompt,
            model: modelName || "llama-3.3-70b-versatile",
            modelOptions: {
              temperature: 0.2,
              maxTokens: 8000
            }
          });

        default:
          // Default to Google
          const { processWithGoogleAI: defaultGoogleAI } = await import('./google-ai');
          try {
            return await defaultGoogleAI({
              prompt,
              model: "gemini-2.5-pro"
            });
          } catch (googleError) {
            console.error("Error with Google AI in document summarization, starting fallback chain:", googleError);

            // Start the fallback chain
            try {
              // First fallback: OpenAI o3
              console.log(`Attempting first fallback with OpenAI o3...`);
              const { processWithOpenAI } = await import('./openai');
              return await processWithOpenAI({
                prompt,
                model: "o3-2025-04-16",
                modelOptions: {
                  temperature: 0.2,
                  maxTokens: 10000
                }
              });
            } catch (openaiError) {
              console.error("OpenAI fallback failed:", openaiError);

              try {
                // Second fallback: Claude Sonnet 4.0
                console.log(`Attempting second fallback with Claude Sonnet 4.0...`);
                const { processWithAnthropic } = await import('./anthropic-ai');
                return await processWithAnthropic({
                  prompt,
                  model: "claude-sonnet-4-0",
                  modelOptions: {
                    temperature: 0.2,
                    maxTokens: 10000
                  }
                });
              } catch (claudeError) {
                console.error("Claude fallback failed:", claudeError);

                // Final fallback: Groq with Llama model
                console.log(`Attempting final fallback with Groq Llama model...`);
                const { processWithGroq } = await import('./groq-ai');
                return await processWithGroq({
                  prompt,
                  model: "llama-3.3-70b-versatile",
                  modelOptions: {
                    temperature: 0.2,
                    maxTokens: 10000
                  }
                });
              }
            }
          }
      }
    } catch (processingError) {
      console.error("Error processing with all providers, using fallback chain:", processingError);

      // Implement the fallback chain
      try {
        // First fallback: OpenAI o3
        console.log(`Attempting first fallback with OpenAI o3...`);
        const { processWithOpenAI } = await import('./openai');
        return await processWithOpenAI({
          prompt,
          model: "o3-2025-04-16",
          modelOptions: {
            temperature: 0.2,
            maxTokens: 10000
          }
        });
      } catch (openaiError) {
        console.error("OpenAI fallback failed:", openaiError);

        try {
          // Second fallback: Claude 4.0
          console.log(`Attempting second fallback with Claude 4.0...`);
          const { processWithAnthropic } = await import('./anthropic-ai');
          return await processWithAnthropic({
            prompt,
            model: "claude-sonnet-4-0",
            modelOptions: {
              temperature: 0.2,
              maxTokens: 10000
            }
          });
        } catch (claudeError) {
          console.error("Claude fallback failed:", claudeError);

          // Final fallback: Groq with Llama model
          console.log(`Attempting final fallback with Groq Llama model...`);
          const { processWithGroq } = await import('./groq-ai');
          return await processWithGroq({
            prompt,
            model: "llama-3.3-70b-versatile",
            modelOptions: {
              temperature: 0.2,
              maxTokens: 10000
            }
          });
        }
      }
    }
  },

  /**
   * Get document content by namespace
   */
  async getDocumentContentByNamespace(userId: string, namespace: string): Promise<{content: string, title: string, namespace: string} | null> {
    try {
      console.log(`EnhancedDocumentContentExtractor: Attempting to get content for namespace ${namespace} for user ${userId}`);

      // First, try to get file metadata
      const fileSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('namespace', '==', namespace)
        .limit(1)
        .get();

      let title = `Document ${namespace}`;
      let fileId = namespace;

      if (!fileSnapshot.empty) {
        const fileData = fileSnapshot.docs[0].data();
        title = fileData.name || title;
        console.log(`EnhancedDocumentContentExtractor: Found file metadata with name: ${title}`);

        // If the file has an ID field different from namespace, store it
        if (fileData.id && fileData.id !== namespace) {
          fileId = fileData.id;
          console.log(`EnhancedDocumentContentExtractor: File has ID different from namespace: ${fileId}`);
        }
      } else {
        console.log(`EnhancedDocumentContentExtractor: No file metadata found for namespace: ${namespace}`);
      }

      // PRIORITY 1: Try to get content from byteStoreCollection first (based on screenshots)
      console.log(`EnhancedDocumentContentExtractor: Trying to get content from byteStoreCollection for namespace ${namespace}`);

      // First try to get all chunks for this document from byteStoreCollection
      const bytestoreChunksSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('byteStoreCollection')
        .where('metadata.doc_id', '==', namespace)
        .get();

      if (!bytestoreChunksSnapshot.empty) {
        console.log(`EnhancedDocumentContentExtractor: Found ${bytestoreChunksSnapshot.docs.length} chunks in byteStoreCollection for doc_id ${namespace}`);

        // Combine all chunks into a single content string
        let combinedContent = '';
        let documentTitle = title;

        // Sort chunks by chunk_id if available
        const sortedChunks = bytestoreChunksSnapshot.docs
          .map(doc => ({ id: doc.id, data: doc.data() }))
          .sort((a, b) => {
            const aChunkId = a.data.metadata?.chunk_id || '';
            const bChunkId = b.data.metadata?.chunk_id || '';
            return aChunkId.localeCompare(bChunkId);
          });

        for (const chunk of sortedChunks) {
          // Extract content from each chunk
          const chunkContent = chunk.data.content || '';
          combinedContent += chunkContent + '\n\n';

          // Try to get a better title from metadata if available
          if (chunk.data.metadata?.document_title) {
            documentTitle = chunk.data.metadata.document_title;
          }
        }

        return {
          content: combinedContent.trim(),
          title: documentTitle,
          namespace
        };
      }

      // Try alternative field names in byteStoreCollection
      const bytestoreAltSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('byteStoreCollection')
        .where('metadata.namespace', '==', namespace)
        .get();

      if (!bytestoreAltSnapshot.empty) {
        console.log(`EnhancedDocumentContentExtractor: Found ${bytestoreAltSnapshot.docs.length} chunks in byteStoreCollection for namespace ${namespace}`);

        // Combine all chunks into a single content string
        let combinedContent = '';
        let documentTitle = title;

        for (const doc of bytestoreAltSnapshot.docs) {
          const chunkData = doc.data();
          combinedContent += (chunkData.content || '') + '\n\n';

          if (chunkData.metadata?.document_title) {
            documentTitle = chunkData.metadata.document_title;
          }
        }

        return {
          content: combinedContent.trim(),
          title: documentTitle,
          namespace
        };
      }

      // PRIORITY 2: Try the original bytestore collection (as in your code)
      console.log(`EnhancedDocumentContentExtractor: Trying to get content from bytestore collection`);
      const bytestoreSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('bytestore')
        .where('namespace', '==', namespace)
        .limit(1)
        .get();

      if (!bytestoreSnapshot.empty) {
        console.log(`EnhancedDocumentContentExtractor: Found content in bytestore collection`);
        const bytestoreData = bytestoreSnapshot.docs[0].data();
        return {
          content: bytestoreData?.content || bytestoreData?.text || '',
          title,
          namespace
        };
      }

      // PRIORITY 3: Try to get content from raw_content collection using namespace
      console.log(`EnhancedDocumentContentExtractor: Trying to get content from raw_content/${namespace}`);
      const contentSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('raw_content')
        .doc(namespace)
        .get();

      if (contentSnapshot.exists) {
        console.log(`EnhancedDocumentContentExtractor: Found content in raw_content/${namespace}`);
        const contentData = contentSnapshot.data();
        return {
          content: contentData?.content || contentData?.text || '',
          title,
          namespace
        };
      }

      // If namespace didn't work, try using the fileId if it's different
      if (fileId !== namespace) {
        console.log(`EnhancedDocumentContentExtractor: Trying to get content from raw_content/${fileId}`);
        const contentByIdSnapshot = await adminDb.collection('users')
          .doc(userId)
          .collection('raw_content')
          .doc(fileId)
          .get();

        if (contentByIdSnapshot.exists) {
          console.log(`EnhancedDocumentContentExtractor: Found content in raw_content/${fileId}`);
          const contentData = contentByIdSnapshot.data();
          return {
            content: contentData?.content || contentData?.text || '',
            title,
            namespace: fileId
          };
        }
      }

      // Try to get content from the files collection directly
      if (!fileSnapshot.empty) {
        const fileData = fileSnapshot.docs[0].data();
        if (fileData.content) {
          console.log(`EnhancedDocumentContentExtractor: Found content directly in files collection`);
          return {
            content: fileData.content,
            title,
            namespace
          };
        }
      }

      // Try to get content from the documents collection
      console.log(`EnhancedDocumentContentExtractor: Trying to get content from documents collection`);
      const documentSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('documents')
        .doc(namespace)
        .get();

      if (documentSnapshot.exists) {
        console.log(`EnhancedDocumentContentExtractor: Found content in documents/${namespace}`);
        const documentData = documentSnapshot.data();
        return {
          content: documentData?.content || documentData?.text || '',
          title: documentData?.name || title,
          namespace
        };
      }

      // Try to find the document by name in the documents collection
      if (title && title !== `Document ${namespace}`) {
        console.log(`EnhancedDocumentContentExtractor: Trying to find document by name: ${title}`);
        const documentsByNameSnapshot = await adminDb.collection('users')
          .doc(userId)
          .collection('documents')
          .where('name', '==', title)
          .limit(1)
          .get();

        if (!documentsByNameSnapshot.empty) {
          console.log(`EnhancedDocumentContentExtractor: Found document by name in documents collection`);
          const documentData = documentsByNameSnapshot.docs[0].data();
          const docId = documentsByNameSnapshot.docs[0].id;
          return {
            content: documentData?.content || documentData?.text || '',
            title,
            namespace: docId
          };
        }
      }

      console.log(`EnhancedDocumentContentExtractor: No content found for namespace ${namespace} after trying all methods`);
      return null;
    } catch (error) {
      console.error(`Error getting document content for namespace ${namespace}:`, error);
      return null;
    }
  },

  /**
   * Get document by filename
   */
  async getDocumentByFilename(userId: string, filename: string): Promise<{content: string, title: string, namespace: string} | null> {
    try {
      console.log(`EnhancedDocumentContentExtractor: Looking up document by filename: ${filename} for user ${userId}`);

      // First try to get file metadata from files collection
      const fileSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('name', '==', filename)
        .limit(1)
        .get();

      let namespace = '';

      if (!fileSnapshot.empty) {
        console.log(`EnhancedDocumentContentExtractor: Found file metadata for filename: ${filename}`);
        const fileDoc = fileSnapshot.docs[0];
        const fileData = fileDoc.data();
        namespace = fileData.namespace;

        if (namespace) {
          console.log(`EnhancedDocumentContentExtractor: File has namespace: ${namespace}`);

          // Get content using the namespace
          const contentResult = await this.getDocumentContentByNamespace(userId, namespace);

          if (contentResult) {
            return {
              content: contentResult.content,
              title: filename,
              namespace
            };
          }
        } else {
          console.log(`EnhancedDocumentContentExtractor: File has no namespace, using document ID: ${fileDoc.id}`);
          namespace = fileDoc.id;

          // If no namespace, try using the document ID
          const contentResult = await this.getDocumentContentByNamespace(userId, fileDoc.id);

          if (contentResult) {
            return {
              content: contentResult.content,
              title: filename,
              namespace: fileDoc.id
            };
          }
        }
      } else {
        console.log(`EnhancedDocumentContentExtractor: No file metadata found for filename: ${filename}`);
      }

      // Try to find document directly in byteStoreCollection by document_title
      console.log(`EnhancedDocumentContentExtractor: Trying to find document in byteStoreCollection by document_title`);
      const bytestoreByTitleSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('byteStoreCollection')
        .where('metadata.document_title', '==', filename)
        .get();

      if (!bytestoreByTitleSnapshot.empty) {
        console.log(`EnhancedDocumentContentExtractor: Found ${bytestoreByTitleSnapshot.docs.length} chunks in byteStoreCollection for document_title ${filename}`);

        // Get the namespace/doc_id from the first chunk
        const firstChunk = bytestoreByTitleSnapshot.docs[0].data();
        const docId = firstChunk.metadata?.doc_id || bytestoreByTitleSnapshot.docs[0].id;

        // Now that we have the doc_id, get all chunks for this document
        const contentResult = await this.getDocumentContentByNamespace(userId, docId);

        if (contentResult) {
          return {
            content: contentResult.content,
            title: filename,
            namespace: docId
          };
        }
        return null;
      }

      // Try to find document in byteStoreCollection by matching filename in any field
      console.log(`EnhancedDocumentContentExtractor: Trying to find document in byteStoreCollection by searching all chunks`);
      const allBytestoreSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('byteStoreCollection')
        .get();

      // Look for any chunk that might contain the filename in its metadata
      for (const doc of allBytestoreSnapshot.docs) {
        const data = doc.data();
        const metadata = data.metadata || {};

        // Check various fields that might contain the filename
        if (
          metadata.document_title === filename ||
          metadata.title === filename ||
          metadata.name === filename ||
          metadata.file_name === filename
        ) {
          console.log(`EnhancedDocumentContentExtractor: Found matching chunk in byteStoreCollection`);
          const docId = metadata.doc_id || doc.id;

          // Now that we have the doc_id, get all chunks for this document
          const contentResult = await this.getDocumentContentByNamespace(userId, docId);

          if (contentResult) {
            return {
              content: contentResult.content,
              title: filename,
              namespace: docId
            };
          }
        }
      }

      // If not found in files collection, try documents collection
      console.log(`EnhancedDocumentContentExtractor: Trying documents collection for filename: ${filename}`);
      const documentsSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('documents')
        .where('name', '==', filename)
        .limit(1)
        .get();

      if (!documentsSnapshot.empty) {
        console.log(`EnhancedDocumentContentExtractor: Found document in documents collection`);
        const docData = documentsSnapshot.docs[0].data();
        const docId = documentsSnapshot.docs[0].id;

        return {
          content: docData.content || docData.text || '',
          title: filename,
          namespace: docId
        };
      }

      // Try to find the document in raw_content by looking for a matching title field
      console.log(`EnhancedDocumentContentExtractor: Trying to find document in raw_content by title`);
      const rawContentSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('raw_content')
        .get();

      for (const doc of rawContentSnapshot.docs) {
        const data = doc.data();
        if (data.title === filename || data.name === filename) {
          console.log(`EnhancedDocumentContentExtractor: Found document in raw_content by title match`);
          return {
            content: data.content || data.text || '',
            title: filename,
            namespace: doc.id
          };
        }
      }

      console.log(`EnhancedDocumentContentExtractor: No document found for filename: ${filename} after trying all methods`);
      return null;
    } catch (error) {
      console.error(`Error getting document by filename ${filename}:`, error);
      return null;
    }
  },

  /**
   * Get documents by category
   */
  async getDocumentsByCategory(userId: string, category: string): Promise<Array<{content: string, title: string, namespace: string}>> {
    try {
      // Get all files in the category
      const filesSnapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('category', '==', category)
        .get();

      if (filesSnapshot.empty) {
        return [];
      }

      // Get content for each file
      const filesWithContent = await Promise.all(
        filesSnapshot.docs.map(async (doc) => {
          const fileData = doc.data();
          const namespace = fileData.namespace;

          if (!namespace) {
            return null;
          }

          const contentResult = await this.getDocumentContentByNamespace(userId, namespace);

          if (!contentResult) {
            return null;
          }

          return {
            content: contentResult.content,
            title: fileData.name || `Document ${namespace}`,
            namespace
          };
        })
      );

      // Filter out null results
      return filesWithContent.filter((file): file is {content: string, title: string, namespace: string} => file !== null);
    } catch (error) {
      console.error(`Error getting documents by category ${category}:`, error);
      return [];
    }
  }
};


