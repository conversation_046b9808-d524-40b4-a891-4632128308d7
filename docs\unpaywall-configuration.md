# Unpaywall API Configuration

## Overview
The Research Agent system uses the Unpaywall API to access open access academic papers for comprehensive research capabilities. This configuration is required for academic research functionality.

## What is Unpaywall?
Unpaywall is a database of free scholarly articles. It provides access to over 30 million open access research papers from publishers, repositories, and other sources.

## Configuration Required

### Environment Variable
Add this to your `.env.local` file:
```bash
# Unpaywall API Configuration (Required for academic research)
# This email is required by Unpaywall's politeness policy for API access
UNPAYWALL_EMAIL="<EMAIL>"
```

### Why Email is Required
Unpaywall requires an email address as part of their "politeness policy" to:
1. **Contact users** if there are issues with their API usage
2. **Track usage patterns** and prevent abuse
3. **Comply with academic standards** for API access
4. **Provide support** when needed

## How It's Used in the System

### Academic Search Tool
The `academic-search.ts` tool uses Unpaywall API for:
- **Open Access Paper Discovery**: Finding freely available research papers
- **Academic Literature Search**: Searching scholarly articles by topic
- **Citation Information**: Retrieving paper metadata (authors, journals, DOI)
- **Research Quality**: Accessing peer-reviewed academic sources

### Research Agent Integration
The Research Agents use academic search for:
- **Literature Reviews**: Comprehensive academic research
- **Evidence-Based Analysis**: Supporting findings with scholarly sources
- **Competitive Intelligence**: Academic research on industry topics
- **Technical Research**: Finding papers on specific technologies or methods

## API Usage Examples

### Basic Academic Search
```typescript
// Search for open access papers on AI ethics
const results = await academicSearchTool.search("artificial intelligence ethics", {
  isOa: true,
  numResults: 10
});
```

### Research Agent Usage
```typescript
// Research agents automatically use academic search for scholarly sources
const researchRequest = {
  taskId: "research-001",
  topic: "Machine Learning in Healthcare",
  requiredDepth: "deep",
  outputFormat: "report"
};
```

## Configuration Status

### ✅ **Configured**
- **Email Set**: `<EMAIL>` (using your existing admin email)
- **Environment Variable**: `UNPAYWALL_EMAIL` added to `.env.local`
- **API Integration**: Academic search tool will use the configured email

### 🔄 **What Happens Now**
1. **Warning Eliminated**: The "UNPAYWALL_EMAIL not set" warning will disappear
2. **Academic Search Enabled**: Research agents can access academic papers
3. **Enhanced Research**: Better quality sources for research tasks
4. **PMO Research**: Academic backing for strategic research initiatives

## Alternative Configuration Options

### Option 1: Use Different Email
If you prefer to use a different email:
```bash
UNPAYWALL_EMAIL="<EMAIL>"
```

### Option 2: Personal Email
You can use a personal email if preferred:
```bash
UNPAYWALL_EMAIL="<EMAIL>"
```

### Option 3: Disable Academic Search
If you don't need academic research, you can disable it by setting:
```bash
UNPAYWALL_EMAIL=""
```
*Note: This will disable academic paper access but won't break the system*

## Benefits of Academic Integration

### 📚 **Enhanced Research Quality**
- Access to peer-reviewed sources
- Scholarly citations and references
- Academic credibility for research reports

### 🔬 **Research Capabilities**
- Literature reviews and meta-analyses
- Technical research with academic backing
- Evidence-based strategic recommendations

### 🏢 **PMO Integration**
- Academic research for strategic initiatives
- Scholarly support for business decisions
- Research-backed PMO assessments

### 🤝 **Cross-Team Benefits**
- Marketing: Academic research on consumer behavior
- Sales: Research on sales methodologies
- Software Design: Technical papers on best practices
- Business Analysis: Academic frameworks and models

## Troubleshooting

### If You Still See Warnings
1. **Restart the development server** after adding the environment variable
2. **Check the email format** - ensure it's a valid email address
3. **Verify the variable name** - must be exactly `UNPAYWALL_EMAIL`

### If Academic Search Fails
1. **Check internet connectivity** - Unpaywall requires external API access
2. **Verify email configuration** - invalid emails may cause API rejection
3. **Check API limits** - Unpaywall has rate limiting (usually generous)

## Privacy and Compliance

### Data Usage
- **Email Purpose**: Only used for API identification, not data collection
- **No Personal Data**: Research queries don't include personal information
- **Academic Use**: Complies with academic research standards

### Best Practices
- **Use Professional Email**: Preferably a business or academic email
- **Respect Rate Limits**: Don't make excessive API calls
- **Academic Ethics**: Use for legitimate research purposes

The configuration is now complete and your Research Agent system will have full academic research capabilities!
