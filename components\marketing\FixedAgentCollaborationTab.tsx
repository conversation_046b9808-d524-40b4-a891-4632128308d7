'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { collection, query, getDocs } from 'firebase/firestore';
import { db } from '../firebase';
import MarkdownRenderer from '../MarkdownRenderer';
import { <PERSON>rkles, ArrowRight, FileText, Search, HelpCircle, Brain, CheckCircle, ChevronDown, FolderOpen, Settings, Edit3, Play } from 'lucide-react';

type ModelProvider = 'openai' | 'anthropic' | 'groq' | 'google';
type AgentType = 'strategic-director' | 'query-documents' | 'question-answer' | 'user';

interface AgentMessage {
  from: AgentType;
  to: AgentType | 'user';
  message: string;
  timestamp: Date;
  thinking?: string;
  isLoading?: boolean;
}

interface Document {
  id: string;
  name: string;
  namespace: string;
  category: string;
}

interface CollaborationProps {
  selectedProvider: ModelProvider;
  selectedModel: string;
}

interface LogEntry {
  id: number;
  text: string;
  timestamp: Date;
}

const FixedAgentCollaborationTab: React.FC<CollaborationProps> = ({
  selectedProvider: initialProvider,
  selectedModel: initialModel,
}) => {
  const { data: session } = useSession();
  const [prompt, setPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversation, setConversation] = useState<AgentMessage[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>(initialProvider);
  const [selectedModel, setSelectedModel] = useState(initialModel);
  const [showToast, setShowToast] = useState(false);
  const [showOutputStoredToast, setShowOutputStoredToast] = useState(false);
  const [showInternalMessages, setShowInternalMessages] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const [showDebugInfo, setShowDebugInfo] = useState(false);

  // Context options state
  const [customContext, setCustomContext] = useState<string>('');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [documentSearchQuery, setDocumentSearchQuery] = useState<string>('');
  const [isDocDropdownOpen, setIsDocDropdownOpen] = useState<boolean>(false);
  const [isCatDropdownOpen, setIsCatDropdownOpen] = useState<boolean>(false);
  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(true);

  const docDropdownRef = useRef<HTMLDivElement>(null);
  const catDropdownRef = useRef<HTMLDivElement>(null);

  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [nextLogId, setNextLogId] = useState(0);
  const [showLogs, setShowLogs] = useState(false);
  const [predefinedPrompts, setPredefinedPrompts] = useState<string[]>([
    "Develop a comprehensive content strategy based on our existing marketing documents",
    "Research our competitors' social media strategies and recommend improvements to our approach",
    "Analyze our Q1 marketing performance and suggest strategic adjustments for Q2",
    "Create a marketing plan for our new AI-powered product that targets enterprise customers",
    "Generate a PDF report analyzing the effectiveness of our email marketing campaigns"
  ]);

  const handlePredefinedPrompt = (selectedPrompt: string) => {
    setPrompt(selectedPrompt);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    // Check if user is authenticated
    if (!session?.user?.email) {
      console.error("User is not authenticated");
      // Redirect to login page
      window.location.href = '/marketing-agent-tests/login?redirect=/marketing-agent-tests';
      return;
    }

    setIsLoading(true);
    setShowLogs(true);
    setConversation([
      { from: 'user', to: 'strategic-director', message: prompt, timestamp: new Date() },
      { from: 'strategic-director', to: 'user', message: '', timestamp: new Date(), isLoading: true },
    ]);
    setDebugInfo('');

    try {
      const requestBody: any = {
        mode: 'collaboration',
        prompt,
        modelProvider: selectedProvider,
        modelName: selectedModel,
        useModelForAllAgents: true,
        userEmail: session.user.email,
        userId: session.user.email,
      };

      // Add context options if provided
      if (customContext.trim()) {
        requestBody.context = customContext.trim();
      }

      if (selectedDocument) {
        requestBody.documentReferences = [selectedDocument];
      } else if (selectedCategory) {
        requestBody.category = selectedCategory;
      }

      // Log the request body for debugging
      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch('/api/marketing-agent-tests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      });

      // Get response text for debugging
      const responseText = await response.text();

      try {
        // Try to parse the response as JSON
        const data = JSON.parse(responseText);

        // Log the response data for debugging
        console.log('Response data:', JSON.stringify(data, null, 2));

        if (data.conversation && Array.isArray(data.conversation)) {
          const processedConversation = data.conversation.map((msg: any) => ({
            ...msg,
            timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(),
          }));

          setConversation([
            { from: 'user', to: 'strategic-director', message: prompt, timestamp: new Date() },
            ...processedConversation
          ]);
          setShowOutputStoredToast(true);
        } else {
          // Handle case where conversation is not in expected format
          setDebugInfo(`Response missing expected conversation array: ${JSON.stringify(data, null, 2)}`);
          throw new Error('Response missing expected conversation array');
        }
      } catch (parseError) {
        // Handle JSON parse error
        console.error('Error parsing response:', parseError);
        setDebugInfo(`Error parsing response: ${parseError}\n\nResponse text: ${responseText}`);
        throw new Error(`Failed to parse response: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
      }
    } catch (error) {
      console.error('Error in agent collaboration:', error);
      setConversation([
        { from: 'user', to: 'strategic-director', message: prompt, timestamp: new Date() },
        {
          from: 'strategic-director',
          to: 'user',
          message: 'An error occurred. Please try again. Check the debug info for more details.',
          timestamp: new Date()
        },
      ]);

      // Set detailed debug info
      setDebugInfo(prevDebug =>
        `${prevDebug}\n\nError: ${error instanceof Error ? error.message : String(error)}`
      );
      setShowDebugInfo(true);
    } finally {
      setIsLoading(false);
    }
  };

  const getAgentIcon = (agent: AgentType | 'user') => {
    switch (agent) {
      case 'strategic-director': return <Brain className="w-5 h-5 text-purple-400" />;
      case 'query-documents': return <Search className="w-5 h-5 text-blue-400" />;
      case 'question-answer': return <HelpCircle className="w-5 h-5 text-green-400" />;
      case 'user': return <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"></path></svg>;
      default: return <Sparkles className="w-5 h-5 text-amber-400" />;
    }
  };

  const getAgentName = (agent: AgentType | 'user') => {
    if (agent === 'user') return 'You';
    return agent.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
  };

  const getMessageStyle = (agent: AgentType | 'user') => {
    switch (agent) {
      case 'strategic-director': return 'bg-purple-900/30 border-purple-700/50';
      case 'query-documents': return 'bg-blue-900/30 border-blue-700/50';
      case 'question-answer': return 'bg-green-900/30 border-green-700/50';
      case 'user': return 'bg-gray-800 border-gray-700';
      default: return 'bg-amber-900/30 border-amber-700/50';
    }
  };

  return (
    <div className="space-y-8">
      {/* Prompt Input Form */}
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="collaborationPrompt" className="block text-lg font-medium mb-2 text-purple-300">
            Your Strategic Prompt
          </label>
          <textarea id="collaborationPrompt" value={prompt} onChange={(e) => setPrompt(e.target.value)}
            className="w-full p-4 bg-gray-900/50 text-gray-200 border border-gray-700/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            rows={4} placeholder="Enter a marketing request or problem for the Strategic Director Agent..." />
        </div>
        <button type="submit" disabled={isLoading || !prompt.trim()}
          className="w-full md:w-auto px-8 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium disabled:opacity-60 disabled:cursor-not-allowed transition-colors flex items-center justify-center shadow-md hover:shadow-lg">
          {isLoading ? (
            <><svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Processing...</>
          ) : (<><Play className="w-5 h-5 mr-2" />Start Strategic Analysis</>)}
        </button>
      </form>

      {/* Conversation Display */}
      {conversation.length > 0 && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-purple-300">Strategic Analysis Breakdown</h3>
            <div className="flex items-center">
              <label htmlFor="showDebugInfo" className="mr-2 text-sm text-gray-300">Show debug info</label>
              <div className="relative inline-block w-10 mr-2 align-middle select-none">
                <input type="checkbox" id="showDebugInfo" checked={showDebugInfo} onChange={() => setShowDebugInfo(!showDebugInfo)} className="sr-only" />
                <div className={`block h-6 rounded-full w-10 ${showDebugInfo ? 'bg-purple-600' : 'bg-gray-600'}`}></div>
                <div className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition ${showDebugInfo ? 'transform translate-x-full' : ''}`}></div>
              </div>
            </div>
          </div>

          {/* Debug Info */}
          {showDebugInfo && debugInfo && (
            <div className="mb-4 p-4 bg-gray-900 border border-amber-700 rounded-lg">
              <h4 className="text-amber-400 font-medium mb-2">Debug Information</h4>
              <pre className="text-gray-300 text-xs overflow-x-auto whitespace-pre-wrap">{debugInfo}</pre>
            </div>
          )}

          {/* Messages */}
          <div className="space-y-4">
            {conversation
              .filter(message => showInternalMessages || message.from === 'user' || message.to === 'user')
              .map((message, index) => (
                <div key={index} className={`p-4 rounded-lg border ${getMessageStyle(message.from)}`}>
                  <div className="flex items-center mb-3">
                    <div className="mr-2">{getAgentIcon(message.from)}</div>
                    <div className="font-medium text-gray-200">{getAgentName(message.from)}</div>
                    {message.to !== 'user' && (
                      <>
                        <ArrowRight className="mx-2 text-gray-500 w-4 h-4" />
                        <div className="mr-2">{getAgentIcon(message.to)}</div>
                        <div className="font-medium text-gray-200">{getAgentName(message.to)}</div>
                      </>
                    )}
                    <div className="ml-auto text-xs text-gray-500">{message.timestamp.toLocaleTimeString()}</div>
                  </div>

                  {message.isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse delay-150"></div>
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse delay-300"></div>
                      <span className="text-gray-400 ml-2">Thinking...</span>
                    </div>
                  ) : (
                    <>
                      {message.thinking && (
                        <div className="mb-3">
                          <div className="flex items-center mb-1 text-sm font-medium text-amber-300">
                            <Brain className="w-4 h-4 mr-2 text-amber-400" />Agent Reasoning
                          </div>
                          <div className="bg-black/30 border border-amber-800/40 rounded p-3 text-sm text-gray-300 max-h-60 overflow-y-auto custom-scrollbar">
                            <MarkdownRenderer content={message.thinking} />
                          </div>
                        </div>
                      )}
                      <div className="text-gray-200">
                        <MarkdownRenderer content={message.message} />
                      </div>
                    </>
                  )}
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FixedAgentCollaborationTab;
