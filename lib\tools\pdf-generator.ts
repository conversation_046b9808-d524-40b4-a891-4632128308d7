import { jsPDF } from 'jspdf';
import { Buffer } from 'node:buffer';
import { storageTool, SavePdfToByteStoreResult } from './storage-tool';
import { markdownRendererTool } from './markdown-renderer-tool';
import { ChartConfig, CHART_TYPES } from './chart-tool';

/**
 * Interface for PDF content
 */
export interface PdfContent {
  title: string;
  content: string;
}

/**
 * Interface for PDF generation options
 */
export interface PdfGenerationOptions {
  title?: string;
  subtitle?: string;
  date?: string;
  logoPath?: string;
  margin?: number;
  size?: string;
  includeCover?: boolean;
  includeToc?: boolean;
  saveToByteStore?: boolean;
  category?: string;
  agentId?: string;
  agentName?: string;
  contents?: PdfContent[];
  // Additional properties that might be used as metadata
  generatedAt?: string;
  generatedBy?: string;
  documentType?: string;
  tags?: string[];
  version?: string;
  author?: string;
  department?: string;
  status?: string;
  priority?: string;
  dueDate?: string;
  relatedDocuments?: string[];
  // Analytics report specific properties
  reportId?: string;
  reportType?: string;
  // Strategy specific properties
  strategyId?: string;
  // Research specific properties
  queryId?: string;
  subTaskId?: string;
}

/**
 * PDF Generator Tool for creating PDFs from formatted content
 */
export class PdfGeneratorTool {
  /**
   * Generate a PDF from formatted content
   * @param contents - Array of content objects with title and content properties
   * @param options - PDF generation options
   * @returns PDF buffer
   */
  async generatePdf(contents: PdfContent[], options: PdfGenerationOptions = {}): Promise<Buffer | SavePdfToByteStoreResult> {
    try {
      // Initialize jsPDF instance for PDF generation
      const doc = new jsPDF();

      // Set initial position
      let yPosition = 20;

      // Add cover page if requested
      if (options.includeCover !== false) {
        yPosition = this.addCoverPage(doc, contents, options, yPosition);
      }

      // Add table of contents if requested
      if (options.includeToc !== false) {
        doc.addPage();
        yPosition = 20;
        yPosition = this.addTableOfContents(doc, contents, options, yPosition);
      }

      // Add content pages
      for (const content of contents) {
        // Process markdown content using the MarkdownRendererTool
        const processedContent = {
          title: content.title,
          content: markdownRendererTool.markdownToPdfFormat(content.content)
        };

        doc.addPage();
        yPosition = 20;
        yPosition = this.addContentPage(doc, processedContent, options, yPosition);
      }

      // Convert PDF to ArrayBuffer and then to Node Buffer
      const pdfArrayBuffer = doc.output("arraybuffer");
      const pdfBuffer = Buffer.from(pdfArrayBuffer);

      // If saveToByteStore is true, save to byteStore
      if (options.saveToByteStore) {
        // Combine all content into a single string for vector search
        // Use plain text conversion for better vector search results
        const combinedContent = contents.map(content =>
          `${content.title}\n\n${markdownRendererTool.markdownToPlainText(content.content)}`
        ).join('\n\n');

        // Use the title from options or from the first content item
        const title = options.title || (contents.length > 0 ? contents[0].title : 'Untitled Document');

        // Save to byteStore with Marketing Agent Team category
        const category = options.category || 'Marketing Agent Team';

        // Add metadata
        const metadata: Record<string, string | string[] | undefined> = {
          generatedAt: new Date().toISOString(),
          generatedBy: options.agentName || 'Marketing Agent',
          agentId: options.agentId || 'unknown',
          documentType: options.documentType,
          author: options.author,
          department: options.department,
          status: options.status,
          priority: options.priority,
          dueDate: options.dueDate,
          version: options.version,
          reportId: options.reportId,
          reportType: options.reportType,
          strategyId: options.strategyId,
          queryId: options.queryId,
          subTaskId: options.subTaskId,
          tags: Array.isArray(options.tags) ? options.tags.join(',') : undefined,
          relatedDocuments: Array.isArray(options.relatedDocuments) ? options.relatedDocuments.join(',') : undefined
        };

        // Filter out undefined values
        const cleanedMetadata: Record<string, string> = {};
        Object.entries(metadata).forEach(([key, value]) => {
          if (value !== undefined) {
            cleanedMetadata[key] = String(value);
          }
        });

        // Save to byteStore
        return await storageTool.savePdfToByteStore(
          pdfBuffer,
          title,
          combinedContent,
          category,
          cleanedMetadata
        );
      }

      // Return the PDF buffer if not saving to byteStore
      return pdfBuffer;
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }

  /**
   * Add a cover page to the PDF
   * @private
   */
  private addCoverPage(doc: jsPDF, _contents: PdfContent[], options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;

    // Set title
    const title = options.title || "Generated Document";

    // Add title
    doc.setFontSize(18);
    doc.setFont("helvetica", "bold");
    const titleWidth = doc.getTextWidth(title);
    const pageWidth = doc.internal.pageSize.getWidth();
    doc.text(title, (pageWidth - titleWidth) / 2, yPosition);

    yPosition += 20;

    // Add subtitle if provided
    if (options.subtitle) {
      doc.setFontSize(14);
      doc.setFont("helvetica", "normal");
      const subtitleWidth = doc.getTextWidth(options.subtitle);
      doc.text(options.subtitle, (pageWidth - subtitleWidth) / 2, yPosition);
      yPosition += 10;
    }

    // Add date
    const date = options.date || new Date().toLocaleDateString();
    doc.setFontSize(12);
    const dateText = `Generated on: ${date}`;
    const dateWidth = doc.getTextWidth(dateText);
    doc.text(dateText, (pageWidth - dateWidth) / 2, yPosition);

    return yPosition;
  }

  /**
   * Add table of contents to the PDF
   * @private
   */
  private addTableOfContents(doc: jsPDF, contents: PdfContent[], _options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;

    // Add TOC title
    doc.setFontSize(11);
    doc.setFont("helvetica", "bold");
    const tocTitle = 'Table of Contents';
    const tocTitleWidth = doc.getTextWidth(tocTitle);
    const pageWidth = doc.internal.pageSize.getWidth();
    doc.text(tocTitle, (pageWidth - tocTitleWidth) / 2, yPosition);

    yPosition += 15;

    // Add each content item to TOC
    doc.setFontSize(11);
    doc.setFont("helvetica", "normal");

    contents.forEach((content, index) => {
      const tocEntry = `${index + 1}. ${content.title || 'Untitled Section'}`;
      doc.text(tocEntry, 20, yPosition);
      yPosition += 10;

      // Check if we need a new page
      if (yPosition > 280) {
        doc.addPage();
        yPosition = 20;
      }
    });

    return yPosition;
  }

  /**
   * Add a content page to the PDF
   * @private
   */
  private addContentPage(doc: jsPDF, content: PdfContent, _options: PdfGenerationOptions, startY: number): number {
    let yPosition = startY;

    // Add title
    doc.setFontSize(14);
    doc.setFont("helvetica", "bold");
    doc.text(content.title || 'Untitled Section', 20, yPosition);

    yPosition += 10;

    // Add content with improved markdown handling
    doc.setFontSize(10);
    doc.setFont("helvetica", "normal");

    // Check if content contains chart data
    if (content.content.includes('<chart-data>')) {
      // Extract chart data
      const chartDataMatch = content.content.match(/<chart-data>([\s\S]*?)<\/chart-data>/);
      if (chartDataMatch && chartDataMatch[1]) {
        try {
          // Parse the chart data
          const chartConfig = JSON.parse(chartDataMatch[1]) as ChartConfig;

          // Render the chart based on its type
          return this.renderChartInPdf(doc, chartConfig, content.title || 'Chart', yPosition);
        } catch (error) {
          console.error('Error processing chart data:', error);
          // Add a note about chart data error
          doc.text('Chart visualization could not be rendered due to an error.', 20, yPosition);
          yPosition += 10;
          doc.text('Falling back to text representation.', 20, yPosition);
          yPosition += 20;

          // Fall back to regular text processing
        }
      }
    }

    // Check if content contains HTML tags from our enhanced markdown renderer
    let newYPosition: number;
    if (content.content.includes('<h') || content.content.includes('<strong>') || content.content.includes('<em>')) {
      // Process HTML-formatted content
      newYPosition = this.addHtmlFormattedContent(doc, content.content, yPosition);
    } else {
      // Process regular markdown content line by line
      newYPosition = this.addPlainMarkdownContent(doc, content.content, yPosition);
    }
    return newYPosition;
  }

  /**
   * Process HTML-formatted content from the enhanced markdown renderer
   * @private
   */
  private addHtmlFormattedContent(doc: jsPDF, content: string, startY: number): number {
    let yPosition = startY;
    const contentLines = content.split('\n');

    for (let i = 0; i < contentLines.length; i++) {
      const line = contentLines[i];

      // Check for page overflow
      if (yPosition > 280) {
        doc.addPage();
        yPosition = 20;
      }

      // Handle heading tags
      if (line.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/)) {
        const headingMatch = line.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/);
        if (headingMatch) {
          const level = parseInt(headingMatch[1]);
          const text = headingMatch[2];

          // Set font size based on heading level
          const fontSize = 20 - ((level - 1) * 2); // h1=20, h2=18, h3=16, etc.
          doc.setFontSize(fontSize);
          doc.setFont("helvetica", "bold");
          doc.text(text, 20, yPosition);
          doc.setFontSize(12); // Reset to default
          doc.setFont("helvetica", "normal");

          yPosition += 10;
          continue;
        }
      }

      // Handle strong/bold tags
      if (line.includes('<strong>')) {
        // Extract text between strong tags
        const boldMatch = line.match(/<strong>(.*?)<\/strong>/);
        if (boldMatch) {
          doc.setFont("helvetica", "bold");
          const text = boldMatch[1];
          doc.text(text, 20, yPosition);
          doc.setFont("helvetica", "normal");
          yPosition += 7;
          continue;
        }
      }

      // Handle bullet points
      if (line.includes('•')) {
        const indentMatch = line.match(/^(\s*)•/);
        if (indentMatch) {
          const indentSize = indentMatch[1].length;
          const indent = 20 + (indentSize * 2);
          const text = line.replace(/^\s*•\s*/, '');

          // Split text to fit page width with indentation
          const textLines = doc.splitTextToSize(text, 170 - (indent - 20));

          textLines.forEach((textLine: string) => {
            doc.text(textLine, indent, yPosition);
            yPosition += 7;

            if (yPosition > 280) {
              doc.addPage();
              yPosition = 20;
            }
          });

          continue;
        }
      }

      // Handle blockquotes
      if (line.includes('<blockquote')) {
        const quoteMatch = line.match(/<blockquote[^>]*>(.*?)<\/blockquote>/);
        if (quoteMatch) {
          const text = quoteMatch[1];

          // Draw a line for the blockquote
          doc.setDrawColor(200, 200, 200);
          doc.line(15, yPosition - 5, 15, yPosition + 10);

          // Split text to fit page width
          const textLines = doc.splitTextToSize(text, 160);

          textLines.forEach((textLine: string) => {
            doc.text(textLine, 25, yPosition);
            yPosition += 7;

            if (yPosition > 280) {
              doc.addPage();
              yPosition = 20;
            }
          });

          continue;
        }
      }

      // Handle regular text (strip any remaining HTML tags)
      if (line.trim() !== '') {
        const plainText = line.replace(/<[^>]*>/g, '');

        // Split text to fit page width
        const textLines = doc.splitTextToSize(plainText, 170);

        textLines.forEach((textLine: string) => {
          doc.text(textLine, 20, yPosition);
          yPosition += 7;

          if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
          }
        });
      } else {
        // Empty line - add some space
        yPosition += 5;
      }
    }

    return yPosition;
  }

  /**
   * Process plain markdown content line by line
   * @private
   */
  private addPlainMarkdownContent(doc: jsPDF, content: string, startY: number): number {
    let yPosition = startY;
    const contentLines = (content || '').split('\n');

    for (let i = 0; i < contentLines.length; i++) {
      const line = contentLines[i];

      // Check for page overflow
      if (yPosition > 280) {
        doc.addPage();
        yPosition = 20;
      }

      // Handle headers (# Header)
      if (line.match(/^#{1,6}\s/)) {
        const headerLevel = line.match(/^(#{1,6})\s/)?.[1].length || 1;
        const headerText = line.replace(/^#{1,6}\s/, '');

        // Set font size based on header level
        const fontSize = 16 - (headerLevel - 1) * 2; // h1=16, h2=14, h3=12, etc.
        doc.setFontSize(fontSize);
        doc.setFont("helvetica", "bold");
        doc.text(headerText, 20, yPosition);
        doc.setFontSize(12); // Reset to default
        doc.setFont("helvetica", "normal");

        yPosition += 8;
        continue;
      }

      // Handle bold text (**text**)
      if (line.includes('**')) {
        // This is a simplified approach - a more robust solution would parse the line
        // and alternate between bold and normal text
        doc.setFont("helvetica", "bold");
      }

      // Handle list items
      if (line.match(/^\s*[-*+]\s/)) {
        const listText = line.replace(/^\s*[-*+]\s/, '• ');
        const indentMatch = line.match(/^(\s*)/)?.[1].length || 0;
        const indent = 20 + (indentMatch * 5); // Indent based on leading spaces

        // Split text to fit page width with indentation
        const textLines = doc.splitTextToSize(listText, 170 - (indent - 20));

        textLines.forEach((textLine: string) => {
          doc.text(textLine, indent, yPosition);
          yPosition += 7;

          if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
          }
        });

        continue;
      }

      // Handle regular text
      if (line.trim() !== '') {
        // Split text to fit page width
        const textLines = doc.splitTextToSize(line, 170);

        textLines.forEach((textLine: string) => {
          doc.text(textLine, 20, yPosition);
          yPosition += 7;

          if (yPosition > 280) {
            doc.addPage();
            yPosition = 20;
          }
        });
      } else {
        // Empty line - add some space
        yPosition += 5;
      }

      // Reset font to normal after each line
      doc.setFont("helvetica", "normal");
    }

    return yPosition;
  }

  /**
   * Renders a chart in the PDF document using the ChartConfig from chart-tool
   * @param doc - The PDF document
   * @param chartConfig - The chart configuration from ChartTool
   * @param title - The title of the chart section
   * @param yPosition - The y position to start rendering
   * @returns The new y position after rendering the chart
   */
  private renderChartInPdf(doc: jsPDF, chartConfig: ChartConfig, title: string, yPosition: number): number {
    try {
      // Add title for the chart
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text(title || chartConfig.title || 'Chart Visualization', 20, yPosition);
      yPosition += 10;

      // Add subtitle if available
      if (chartConfig.subtitle) {
        doc.setFontSize(12);
        doc.setFont('helvetica', 'italic');
        doc.text(chartConfig.subtitle, 20, yPosition);
        yPosition += 8;
      }

      // Set back to normal text
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');

      // Add chart type info
      const chartTypeName = chartConfig.chartType.charAt(0).toUpperCase() + chartConfig.chartType.slice(1);
      doc.text(`Chart Type: ${chartTypeName}`, 20, yPosition);
      yPosition += 10;

      // Create a placeholder for the chart
      doc.setDrawColor(200, 200, 200);
      doc.setFillColor(240, 240, 240);
      doc.roundedRect(20, yPosition, 170, 100, 3, 3, 'FD');

      // Add chart data as text
      yPosition += 10;
      doc.text('Chart Data:', 30, yPosition);
      yPosition += 10;

      // Handle different chart types appropriately
      if (chartConfig.chartType === CHART_TYPES.TABLE) {
        // For table charts
        // Type assertion for table chart
        const tableConfig = chartConfig as unknown as {
          chartType: 'table';
          columns?: Array<{ title?: string; field?: string; header?: string; accessorKey?: string; }>;
          data?: Array<Record<string, any>>;
        };

        if (tableConfig.columns && tableConfig.data) {
          // Display column headers
          let xPos = 40;
          tableConfig.columns.slice(0, 3).forEach((column: any) => {
            doc.setFont('helvetica', 'bold');
            doc.text(column.title || column.field || column.header || column.accessorKey || 'Column', xPos, yPosition);
            xPos += 40;
          });
          doc.setFont('helvetica', 'normal');
          yPosition += 7;

          // Display rows (limit to 5)
          tableConfig.data.slice(0, 5).forEach((row: any) => {
            xPos = 40;
            tableConfig.columns!.slice(0, 3).forEach((column: any) => {
              const field = column.field || column.title || column.accessorKey || '';
              const value = field && row[field] !== undefined ? String(row[field]) : '';
              doc.text(value.substring(0, 15), xPos, yPosition);
              xPos += 40;
            });
            yPosition += 7;
          });

          // If we have more rows, add ellipsis
          if (tableConfig.data.length > 5) {
            doc.text(`... and ${tableConfig.data.length - 5} more rows`, 40, yPosition);
            yPosition += 7;
          }
        }
      } else if (chartConfig.chartType === CHART_TYPES.FLOW) {
        // For flow charts
        // Type assertion for flow chart
        const flowConfig = chartConfig as unknown as {
          chartType: 'flow';
          nodes?: Array<{ id: string; data?: { label?: string }; }>;
          edges?: Array<{ source: string; target: string; label?: string; }>;
        };

        if (flowConfig.nodes) {
          doc.text('Nodes:', 40, yPosition);
          yPosition += 7;

          flowConfig.nodes.slice(0, 5).forEach((node: any) => {
            doc.text(`- ${node.data?.label || node.id || 'Node'}`, 50, yPosition);
            yPosition += 7;
          });

          if (flowConfig.nodes.length > 5) {
            doc.text(`... and ${flowConfig.nodes.length - 5} more nodes`, 50, yPosition);
            yPosition += 7;
          }

          if (flowConfig.edges && flowConfig.edges.length > 0) {
            yPosition += 3;
            doc.text('Connections:', 40, yPosition);
            yPosition += 7;

            flowConfig.edges.slice(0, 3).forEach((edge: any) => {
              doc.text(`- ${edge.source} → ${edge.target}${edge.label ? `: ${edge.label}` : ''}`, 50, yPosition);
              yPosition += 7;
            });

            if (flowConfig.edges.length > 3) {
              doc.text(`... and ${flowConfig.edges.length - 3} more connections`, 50, yPosition);
              yPosition += 7;
            }
          }
        }
      } else {
        // For standard charts (bar, line, pie, etc.)
        // Type assertion for standard charts
        const standardConfig = chartConfig as unknown as {
          chartType: string;
          data?: Array<Record<string, any>>;
        };

        if (standardConfig.data && Array.isArray(standardConfig.data)) {
          // Display data points
          standardConfig.data.slice(0, 5).forEach((item: any, index: number) => {
            let label = '';
            let value = '';

            if (standardConfig.chartType === CHART_TYPES.SCATTER || standardConfig.chartType === CHART_TYPES.BUBBLE) {
              label = item.name || `Point ${index + 1}`;
              value = `(x: ${item.x}, y: ${item.y}${item.z ? `, z: ${item.z}` : ''})`;
            } else {
              // For bar, line, pie, etc.
              label = item.name || Object.keys(item).find(k => k !== 'value') || `Item ${index + 1}`;
              value = item.value !== undefined ? String(item.value) :
                     String(Object.values(item).find(v => typeof v === 'number') || '');
            }

            doc.text(`${label}: ${value}`, 40, yPosition);
            yPosition += 7;
          });

          // If we have more data points, add ellipsis
          if (standardConfig.data.length > 5) {
            doc.text(`... and ${standardConfig.data.length - 5} more data points`, 40, yPosition);
            yPosition += 7;
          }
        }
      }

      // Add explanation if available
      if (chartConfig.explanation) {
        yPosition += 5;
        doc.setFont('helvetica', 'italic');
        doc.text('Explanation:', 30, yPosition);
        yPosition += 7;

        // Split explanation into multiple lines if needed
        const maxWidth = 150;
        const lines = doc.splitTextToSize(chartConfig.explanation, maxWidth);
        doc.text(lines, 40, yPosition);
        yPosition += lines.length * 7;
      }

      // Add some space after the chart
      yPosition += 15;

      return yPosition;
    } catch (error) {
      console.error('Error rendering chart in PDF:', error);

      // Add error message
      doc.text('Chart visualization could not be rendered due to an error.', 20, yPosition);
      yPosition += 10;

      return yPosition;
    }
  }
}

// Export a singleton instance
export const pdfGeneratorTool = new PdfGeneratorTool();

/**
 * Generate a PDF from markdown content
 * @param options - PDF generation options
 * @returns - The PDF generation result
 */
export async function generatePDF(options: {
  title: string;
  content: string;
  fileName?: string;
  category?: string;
  metadata?: Record<string, string>;
}): Promise<{
  success: boolean;
  fileUrl?: string;
  error?: string;
}> {
  try {
    // Create PDF content
    const pdfContent: PdfContent[] = [
      {
        title: options.title,
        content: options.content
      }
    ];

    // Generate PDF
    const result = await pdfGeneratorTool.generatePdf(pdfContent, {
      title: options.title,
      saveToByteStore: true,
      category: options.category || 'PMO',
      documentType: 'Requirements Specification',
      includeCover: true,
      includeToc: true,
      ...options.metadata
    });

    // Check if result is a SavePdfToByteStoreResult
    if (typeof result === 'object' && 'downloadUrl' in result && typeof result.downloadUrl === 'string') {
      return {
        success: true,
        fileUrl: result.downloadUrl
      };
    }

    return {
      success: false,
      error: 'Failed to save PDF to byte store'
    };
  } catch (error: any) {
    console.error('Error generating PDF:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate PDF'
    };
  }
}

