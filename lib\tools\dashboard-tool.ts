/**
 * Dashboard Generation Tool for creating multi-chart dashboards from text prompts
 * Uses LLM to convert natural language to dashboard configuration with multiple visualizations
 */

import { LlmTool, LlmProvider } from './llm-tool';
import { CHART_TYPES, ChartType } from './chart-tool';
import { z } from 'zod';

// Initialize the LLM tool for processing prompts
const llmTool = new LlmTool();

/**
 * Dashboard layouts supported by the tool
 */
export const DASHBOARD_LAYOUTS = {
  GRID: 'grid',
  ROWS: 'rows',
  COLUMNS: 'columns',
  FEATURED: 'featured' // One main visualization with supporting smaller ones
} as const;

export type DashboardLayout = typeof DASHBOARD_LAYOUTS[keyof typeof DASHBOARD_LAYOUTS];

/**
 * Zod schemas for dashboard validation
 */

// Schema for axis configuration
const AxisSchema = z.object({
  label: z.string(),
  dataKey: z.string().optional(),
});

type AxisConfig = z.infer<typeof AxisSchema>;

// Schema for table column
const TableColumnSchema = z.object({
  header: z.string(),
  accessorKey: z.string(),
  type: z.enum(['string', 'number', 'date', 'boolean']),
});

type TableColumn = z.infer<typeof TableColumnSchema>;

// Base visualization schema with common properties
const BaseVisualizationSchema = z.object({
  id: z.string(),
  title: z.string(),
  chartType: z.enum(['bar', 'line', 'pie', 'area', 'scatter', 'radar', 'composed', 'table', 'flow', 'heatmap', 'bubble'] as [string, ...string[]]),
  width: z.number().optional().default(1),
  height: z.number().optional(),
  colors: z.array(z.string()).optional(),
  legend: z.boolean().optional(),
  tooltip: z.boolean().optional(),
  grid: z.boolean().optional(),
  explanation: z.string().optional(),
  featured: z.boolean().optional(),
});

type BaseVisualization = z.infer<typeof BaseVisualizationSchema>;

// Schema for standard chart visualizations (bar, line, pie, etc.)
const StandardChartSchema = BaseVisualizationSchema.extend({
  data: z.array(z.record(z.string(), z.union([z.string(), z.number()]))),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
});

type StandardChartVisualization = z.infer<typeof StandardChartSchema>;

// Schema specifically for table visualizations
const TableChartSchema = BaseVisualizationSchema.extend({
  columns: z.array(TableColumnSchema),
  data: z.array(z.record(z.string(), z.union([z.string(), z.number(), z.boolean(), z.date()]))),
});

type TableChartVisualization = z.infer<typeof TableChartSchema>;

// Combined visualization schema that can be either a standard chart or a table
const VisualizationSchema = z.union([
  StandardChartSchema,
  TableChartSchema
]);

export type Visualization = z.infer<typeof VisualizationSchema>;

// Schema for the entire dashboard configuration
const DashboardConfigSchema = z.object({
  title: z.string(),
  subtitle: z.string().optional(),
  layout: z.enum(['grid', 'rows', 'columns', 'featured'] as [string, ...string[]]),
  insights: z.string().optional(),
  recommendations: z.string().optional(),
  visualizations: z.array(VisualizationSchema).min(1),
});

export type DashboardConfig = z.infer<typeof DashboardConfigSchema>;

// Types for dashboard generation parameters
export interface DashboardGenerationParams {
  prompt: string;
  layout?: DashboardLayout;
  model?: string;
  provider?: LlmProvider;
}

// Types for dashboard generation result
export interface DashboardGenerationResult {
  success: boolean;
  dashboardConfig?: DashboardConfig;
  error?: string;
}

/**
 * Dashboard Generation Tool class
 */
export class DashboardTool {
  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "generateDashboard",
    description: "Generate interactive dashboards with multiple data visualizations from natural language descriptions.",
    parameters: {
      type: "object",
      properties: {
        prompt: {
          type: "string",
          description: "Natural language description of the dashboard to generate, including the types of insights needed."
        },
        layout: {
          type: "string",
          enum: Object.values(DASHBOARD_LAYOUTS),
          description: "Optional specific dashboard layout. If not provided, the LLM will determine the best layout."
        },
        model: {
          type: "string",
          description: "The LLM model to use for processing the prompt.",
          default: "gpt-4o"
        },
        provider: {
          type: "string",
          description: "The provider to use for the LLM.",
          default: "openai"
        }
      },
      required: ["prompt"]
    }
  };

  /**
   * Generate a dashboard configuration based on a natural language prompt
   * @param params - Parameters for dashboard generation
   * @returns - Dashboard configuration
   */
  async generateDashboard(params: DashboardGenerationParams): Promise<DashboardGenerationResult> {
    const { prompt, layout, model = 'gpt-4o', provider = 'openai' } = params;

    if (!prompt) {
      throw new Error("Prompt is required");
    }

    try {
      // Create system prompt for the LLM
      const systemPrompt = this._createSystemPrompt(layout);

      // Process the prompt with the LLM
      const llmResponse = await llmTool.processContent({
        prompt,
        context: systemPrompt,
        model,
        provider,
        modelOptions: {
          temperature: 0.7,
          maxTokens: 4000
        }
      });

      // Parse the LLM response to extract the dashboard configuration
      const parsedConfig = this._parseLlmResponse(llmResponse);

      // Validate the dashboard configuration using Zod schema
      const validatedConfig = this._validateDashboardConfig(parsedConfig);

      return {
        success: true,
        dashboardConfig: validatedConfig
      };
    } catch (error: any) {
      console.error("Error generating dashboard:", error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Create a system prompt for the LLM based on the requested dashboard layout
   * @param layout - Optional specific dashboard layout
   * @returns - System prompt for the LLM
   */
  _createSystemPrompt(layout?: DashboardLayout): string {
    const availableLayouts = Object.values(DASHBOARD_LAYOUTS).join(', ');
    const availableChartTypes = Object.values(CHART_TYPES).join(', ');

    return `You are a data visualization and dashboard expert. Your task is to convert a natural language description into a complete dashboard configuration with multiple visualizations that can be rendered with React.

${layout ? `The user has requested a ${layout} dashboard layout specifically.` : `Choose the most appropriate dashboard layout from: ${availableLayouts}.`}

Analyze the user's request and generate a complete JSON response with the following structure:

{
  "title": "Dashboard title",
  "subtitle": "Optional dashboard subtitle or description",
  "layout": "${layout || 'grid'}",
  "insights": "A brief summary of key insights from the dashboard",
  "recommendations": "Optional actionable recommendations based on the data",
  "visualizations": [
    {
      "id": "viz1",
      "title": "First visualization title",
      "chartType": "One of: ${availableChartTypes}",
      "width": 1, // Width units (1 for full width, 0.5 for half width, etc.)
      "height": 300, // Height in pixels
      "data": [
        // Array of data points appropriate for the chart type
      ],
      "xAxis": {
        "label": "x-axis label",
        "dataKey": "the key in data objects to use for x-axis"
      },
      "yAxis": {
        "label": "y-axis label"
      },
      "colors": ["#hexcolor1", "#hexcolor2"], // Optional array of colors
      "legend": true, // Whether to show a legend
      "tooltip": true, // Whether to show tooltips
      "grid": true, // Whether to show grid lines
      "explanation": "A brief explanation of what this visualization shows"
    },
    // Additional visualizations...
  ]
}

IMPORTANT: YOUR RESPONSE WILL BE VALIDATED AGAINST A STRICT SCHEMA

JSON FORMATTING AND VALIDATION RULES:
1. Use double quotes for all strings and property names
2. Do not use trailing commas in arrays or objects
3. Ensure all brackets and braces are properly closed and matched
4. Do not include comments in the final JSON
5. Do not include any text outside the JSON object
6. Ensure all array elements are separated by commas
7. Ensure all object properties are separated by commas
8. DO NOT wrap the JSON in markdown code blocks or backtick tags
9. Return ONLY the raw JSON object with no additional formatting
10. All required fields must be present and have the correct types
11. For scatter plots, each data point MUST have numeric x and y properties
12. For tables, all column accessorKeys must exist in every data row

For different chart types, adjust the data structure appropriately:
- Bar charts: data with name/category and value(s)
  Example: [{ "name": "Category 1", "value": 100 }, { "name": "Category 2", "value": 200 }]

- Line charts: data with x values (often time-based) and y values
  Example: [{ "name": "Jan", "value": 100 }, { "name": "Feb", "value": 200 }]

- Pie charts: data with name and value (representing portions of a whole)
  Example: [{ "name": "Segment 1", "value": 30 }, { "name": "Segment 2", "value": 70 }]

- Area charts: similar to line charts but with filled areas
  Example: [{ "name": "Jan", "value": 100 }, { "name": "Feb", "value": 200 }]

- Scatter plots: data with x and y coordinates
  Example: [{ "name": "Point 1", "x": 10, "y": 20 }, { "name": "Point 2", "x": 30, "y": 40 }]
  IMPORTANT: For scatter plots, each data point MUST have 'x' and 'y' properties as numeric values

- Radar charts: data with multiple dimensions/metrics
  Example: [{ "name": "Metric 1", "value": 80 }, { "name": "Metric 2", "value": 60 }]

- Composed charts: combination of multiple chart types (specify in configuration)
  Example: [{ "name": "Jan", "bar": 100, "line": 80, "area": 60 }, { "name": "Feb", "bar": 200, "line": 150, "area": 120 }]

- Tables: tabular data with columns and rows
  Example structure:
  {
    "chartType": "table",
    "columns": [
      { "header": "Region", "accessorKey": "region", "type": "string" },
      { "header": "Q1 Sales", "accessorKey": "q1", "type": "number" },
      { "header": "Q2 Sales", "accessorKey": "q2", "type": "number" }
    ],
    "data": [
      { "region": "North", "q1": 12500, "q2": 14200 },
      { "region": "South", "q1": 9800, "q2": 10600 },
      { "region": "East", "q1": 15200, "q2": 16100 },
      { "region": "West", "q1": 8900, "q2": 9300 }
    ]
  }

  IMPORTANT FOR TABLES:
  - ALWAYS include the "columns" property with proper headers for each column
  - Make column headers clear, concise, and descriptive
  - Use proper capitalization for column headers
  - For number columns, specify "type": "number" to ensure proper formatting
  - The "accessorKey" in each column must match the property name in the data objects

DASHBOARD LAYOUT GUIDANCE:
- For 'grid' layout: Arrange visualizations in a responsive grid. Specify width for each visualization (1 for full width, 0.5 for half, etc.)
- For 'rows' layout: Stack visualizations vertically, each taking full width
- For 'columns' layout: Arrange visualizations side by side in columns
- For 'featured' layout: One main visualization (mark with "featured": true) with supporting smaller visualizations

Create a dashboard with 3-6 complementary visualizations that together tell a cohesive story about the data.
Choose visualization types that best represent the data and insights needed.
Use realistic, plausible data that matches the user's request.
Ensure your response is valid JSON that can be parsed directly.`;
  }

  /**
   * Parse the LLM response to extract the dashboard configuration
   * @param llmResponse - Response from the LLM
   * @returns - Parsed dashboard configuration
   */
  _parseLlmResponse(llmResponse: string): any {
    try {
      // Try to parse the response as JSON
      return JSON.parse(llmResponse);
    } catch (error) {
      console.error("Error parsing LLM response:", error);

      // Try to extract JSON from the response if it contains other text
      // First, check for markdown code blocks with JSON
      const markdownJsonMatch = llmResponse.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (markdownJsonMatch && markdownJsonMatch[1]) {
        try {
          return JSON.parse(markdownJsonMatch[1]);
        } catch (markdownError) {
          console.error("Error parsing markdown JSON:", markdownError);
        }
      }

      // Then try to find any JSON-like structure
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[0]);
        } catch (innerError) {
          console.error("Error parsing extracted JSON:", innerError);
          throw new Error("Could not parse dashboard configuration from LLM response");
        }
      }

      throw new Error("Invalid dashboard configuration format");
    }
  }

  /**
   * Validate the dashboard configuration using Zod schemas
   * @param config - Dashboard configuration to validate
   * @throws {Error} If the configuration is invalid
   */
  _validateDashboardConfig(config: any): DashboardConfig {
    try {
      // Parse and validate the configuration using Zod schema
      const validatedConfig = DashboardConfigSchema.parse(config);

      // Additional validation for specific chart types
      validatedConfig.visualizations.forEach(viz => {
        // For scatter plots, ensure each data point has x and y properties
        if (viz.chartType === CHART_TYPES.SCATTER) {
          (viz.data as any[]).forEach((point, idx) => {
            if (typeof point.x !== 'number' || typeof point.y !== 'number') {
              throw new Error(`Scatter plot ${viz.id} data point at index ${idx} must have numeric x and y properties`);
            }
          });
        }

        // For table charts, ensure columns match data properties
        if (viz.chartType === CHART_TYPES.TABLE && 'columns' in viz && Array.isArray(viz.columns)) {
          const columnKeys = viz.columns.map(col => col.accessorKey);
          viz.data.forEach((row: any, idx: number) => {
            const rowKeys = Object.keys(row);
            const missingKeys = columnKeys.filter(key => !rowKeys.includes(key));
            if (missingKeys.length > 0) {
              throw new Error(`Table ${viz.id} row at index ${idx} is missing properties: ${missingKeys.join(', ')}`);
            }
          });
        }
      });

      return validatedConfig;
    } catch (error: any) {
      // Handle Zod validation errors
      if (error.errors) {
        const formattedErrors = error.errors.map((err: any) => {
          return `${err.path.join('.')}: ${err.message}`;
        }).join('\n');
        throw new Error(`Dashboard configuration validation failed:\n${formattedErrors}`);
      }
      throw error;
    }
  }
}

// Export a singleton instance of the tool
export const dashboardTool = new DashboardTool();
