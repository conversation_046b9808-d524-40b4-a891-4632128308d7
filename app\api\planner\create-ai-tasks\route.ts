import { NextRequest, NextResponse } from 'next/server';
import { createTasksAgent } from 'lib/agents/createTasksAgent';


export async function POST(request: NextRequest) {
  try {
    const { project, instructions } = await request.json();

    if (!project || !project.id) {
      return NextResponse.json({ error: 'Project data is required' }, { status: 400 });
    }

    // Use the createTasks method of the createTasksAgent instance
    const result = await createTasksAgent.createTasks(project.id, project, instructions);

    // Return tasks, reasoning, and visualization data
    return NextResponse.json({
      tasks: result.tasks,
      reasoning: result.reasoning,
      taskListVisualization: result.taskListVisualization
    });
  } catch (error) {
    console.error('Error generating tasks with AI:', error);
    return NextResponse.json(
      { error: 'Failed to generate tasks. Please try again.' },
      { status: 500 }
    );
  }
}