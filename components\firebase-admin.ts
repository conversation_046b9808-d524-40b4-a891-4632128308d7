import { getApp, getApps, initializeApp, App, cert } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import * as admin from "firebase-admin";

const serviceKey = require('../service_key.json');

let app: App;

if (getApps().length === 0) {
  app = initializeApp({
    credential: cert(serviceKey),
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,  // Ensure the storage bucket is set here
  });
} else {
  app = getApp();
}

const adminDb = getFirestore(app);
const adminStorage = admin.storage();

export { app as adminApp, adminDb, adminStorage };
