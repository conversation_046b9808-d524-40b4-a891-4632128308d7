'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import UserProfileCard from '../../../components/UserProfileCard';
import useUserProfile from '../../../hooks/useUserProfile';
import useFetchCategories from '../../../hooks/useFetchCategories';
import { syncUserProfileImage, updateUserProfile } from '../../../lib/firebase/userUtils';
import FirebaseDiagnostic from '../../../components/FirebaseDiagnostic';

export default function ProfilePage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { profile, loading: profileLoading, error: profileError, refresh: refreshProfile } = useUserProfile();
  const { categories, loading: categoriesLoading, error: categoriesError } = useFetchCategories();
  const [showDebug, setShowDebug] = useState(false);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'success' | 'error'>('idle');
  const [syncError, setSyncError] = useState<string | null>(null);

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/services/admin/login');
    return null;
  }

  // Handle manual sync of profile image
  const handleSyncProfileImage = async () => {
    if (!session?.user?.email || !session?.user?.image) {
      setSyncError('No session or profile image available');
      setSyncStatus('error');
      return;
    }

    setSyncStatus('syncing');
    setSyncError(null);

    try {
      const result = await syncUserProfileImage(
        session.user.email,
        profile?.avatar,
        session.user.image
      );

      if (result) {
        setSyncStatus('success');
        // Refresh the profile data
        refreshProfile();
      } else {
        setSyncStatus('error');
        setSyncError('Failed to sync profile image');
      }
    } catch (error) {
      setSyncStatus('error');
      setSyncError(error instanceof Error ? error.message : 'Unknown error');
    }
  };

  return (
    <div className="min-h-screen bg-ike-purple text-gray-100">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-row justify-between items-center mb-6">
          <div className="flex flex-row items-center gap-4">
            <Image
              src="/favicon.png"
              alt="Company Logo"
              width={60}
              height={60}
              className="hidden sm:block"
              style={{ width: 'auto', height: '60px' }}
            />
            <div>
              <h1 className="text-2xl font-bold text-white">User Profile</h1>
              <p className="text-gray-400 mt-1">Manage your profile and settings</p>
            </div>
          </div>
          <button
            onClick={() => setShowDebug(!showDebug)}
            className="px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-md text-sm transition-colors"
          >
            {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
          </button>
        </div>

        {/* Debug panel */}
        {showDebug && (
          <div className="mb-6">
            <FirebaseDiagnostic />
          </div>
        )}

        {/* Main content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Profile card */}
          <div>
            <UserProfileCard showDebug={showDebug} />

            {/* Manual sync button */}
            <div className="mt-4">
              <button
                onClick={handleSyncProfileImage}
                disabled={syncStatus === 'syncing' || !session?.user?.image}
                className={`w-full py-2 rounded-md text-white font-medium transition-colors ${
                  syncStatus === 'syncing'
                    ? 'bg-blue-700 cursor-wait'
                    : syncStatus === 'success'
                    ? 'bg-green-700 hover:bg-green-600'
                    : syncStatus === 'error'
                    ? 'bg-red-700 hover:bg-red-600'
                    : 'bg-blue-600 hover:bg-blue-500'
                }`}
              >
                {syncStatus === 'syncing'
                  ? 'Syncing...'
                  : syncStatus === 'success'
                  ? 'Sync Successful'
                  : syncStatus === 'error'
                  ? 'Sync Failed'
                  : 'Sync Profile Image'}
              </button>

              {syncError && (
                <p className="mt-2 text-sm text-red-400">{syncError}</p>
              )}
            </div>
          </div>

          {/* User data */}
          <div className="md:col-span-2">
            <div className="bg-gray-800 rounded-lg shadow-md p-4">
              <h2 className="text-lg font-semibold text-white mb-4">Your Data</h2>

              {/* Categories */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-300 mb-2">Your Categories</h3>
                {categoriesLoading ? (
                  <p className="text-gray-500 text-sm">Loading categories...</p>
                ) : categoriesError ? (
                  <p className="text-red-400 text-sm">Error loading categories: {categoriesError.message}</p>
                ) : categories.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {categories.map((category) => (
                      <span
                        key={category}
                        className="px-3 py-1 bg-purple-900/50 text-purple-300 rounded-full text-sm"
                      >
                        {category}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No categories found</p>
                )}
              </div>

              {/* Session info (debug only) */}
              {showDebug && session && (
                <div className="mt-6 pt-4 border-t border-gray-700">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">Session Data</h3>
                  <pre className="bg-gray-900 p-3 rounded-md text-xs font-mono text-gray-400 overflow-x-auto">
                    {JSON.stringify(session, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
