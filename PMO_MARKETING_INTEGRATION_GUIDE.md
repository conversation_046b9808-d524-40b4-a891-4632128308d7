# PMO to Marketing Integration Guide

## Overview

This document outlines the comprehensive integration between the PMO "Send to Marketing" process and the marketing-agent-collaboration API, bridging the gap between PMO requirements and marketing team execution.

## Problem Statement

### Original Disconnects
1. **Data Format Mismatch**: PMO sends `projectTitle`, `projectDescription`, `pmoAssessment` while Marketing API expects `prompt`, `context`, `documentReferences`
2. **Missing Integration Layer**: No mechanism to convert PMO notifications into marketing collaboration requests
3. **Context Loss**: PMO assessment and rationale not properly formatted for marketing agents
4. **Manual Process**: No automatic triggering of marketing workflow from PMO notifications

## Solution Architecture

### 1. Enhanced PMO Notify Team API (`/api/pmo-notify-team`)

**Key Changes:**
- Automatically detects when Marketing team is selected
- Triggers marketing collaboration workflow seamlessly
- Maintains backward compatibility for other teams

**New Features:**
- Auto-triggers `triggerMarketingCollaboration()` for Marketing team
- Constructs comprehensive marketing prompt from PMO data
- Includes PMO context and metadata in collaboration request

### 2. New PMO-Marketing Integration API (`/api/pmo-marketing-integration`)

**Endpoints:**
- `POST` - Trigger marketing collaboration from notification
- `GET` - Fetch marketing team notifications
- `POST` with action - Update notification status

**Key Functions:**
- `trigger-marketing-collaboration`: Converts PMO notification to marketing request
- `get-marketing-notifications`: Retrieves all PMO notifications for marketing team
- `update-notification-status`: Updates notification processing status

### 3. Enhanced Marketing Agent Collaboration API

**New Features:**
- Accepts PMO metadata in request body
- Stores PMO context in agent output data
- Includes traceability back to original PMO request

**Enhanced Data Structure:**
```typescript
type RequestBody = {
  prompt: string;
  modelProvider?: string;
  modelName?: string;
  context?: string;
  documentReferences?: string[];
  category?: string;
  userId?: string;
  metadata?: {
    source?: string;
    pmoId?: string;
    notificationId?: string;
    autoTriggered?: boolean;
    triggerTimestamp?: string;
  };
}
```

### 4. Marketing Team Dashboard Component

**Features:**
- Displays all PMO notifications for marketing team
- Shows notification status and priority
- Provides "Start Analysis" button to trigger collaboration
- Detailed notification view with full PMO context

## Data Flow

### Automatic Flow (Recommended)
```
PMO "Send to Marketing" → pmo-notify-team API → Auto-trigger marketing collaboration → Marketing analysis complete
```

### Manual Flow (Fallback)
```
PMO "Send to Marketing" → pmo-notify-team API → Marketing Dashboard → Manual trigger → Marketing analysis complete
```

## Implementation Details

### PMO Data Transformation

**Input (PMO Format):**
```json
{
  "pmoId": "pmo-123",
  "projectTitle": "Scene Mate viral campaign",
  "projectDescription": "Develop a viral video ad campaign...",
  "pmoAssessment": "The Marketing Team is best suited...",
  "teamSelectionRationale": "Marketing strategy, content creation...",
  "priority": "High"
}
```

**Output (Marketing Format):**
```json
{
  "prompt": "# PMO Marketing Requirements Analysis\n\n## Project Overview...",
  "context": "PMO Context:\n- Project: Scene Mate viral campaign...",
  "category": "PMO - Scene Mate viral campaign - pmo-123",
  "metadata": {
    "source": "PMO",
    "pmoId": "pmo-123",
    "autoTriggered": true
  }
}
```

### Marketing Prompt Structure

The system automatically constructs a comprehensive marketing prompt:

1. **Project Overview** - Title, Priority, PMO ID
2. **Project Description** - Full description from PMO
3. **PMO Assessment** - PMO's analysis and reasoning
4. **Team Selection Rationale** - Why marketing was chosen
5. **Marketing Objectives** - 8-point analysis framework:
   - Strategic Marketing Assessment
   - Target Audience Analysis
   - Competitive Landscape
   - Marketing Channel Strategy
   - Content Strategy
   - Campaign Planning
   - Success Metrics
   - Resource Requirements

## Usage Instructions

### For PMO Users
1. Create PMO request as usual
2. Select "Marketing" as assigned team
3. Click "Send to Marketing" button
4. System automatically triggers marketing analysis
5. View results in PMO Output tab

### For Marketing Team
1. Access Marketing Team Dashboard
2. View incoming PMO notifications
3. Click "Start Analysis" if auto-trigger failed
4. Monitor analysis progress in Agent Collaboration tab
5. Review completed analysis in Agent Outputs

## API Reference

### Trigger Marketing Collaboration
```bash
POST /api/pmo-marketing-integration
{
  "notificationId": "notification-123",
  "action": "trigger-marketing-collaboration"
}
```

### Get Marketing Notifications
```bash
GET /api/pmo-marketing-integration
```

### Update Notification Status
```bash
POST /api/pmo-marketing-integration
{
  "notificationId": "notification-123",
  "action": "update-notification-status",
  "status": "In Progress"
}
```

## Benefits

### For PMO
- Seamless handoff to marketing team
- Automatic initiation of marketing analysis
- Full traceability and audit trail
- Reduced manual coordination

### For Marketing Team
- Structured requirements from PMO
- Comprehensive context and background
- Automated workflow initiation
- Clear objectives and deliverables

### For Organization
- Faster project initiation
- Consistent process execution
- Better cross-team collaboration
- Improved project outcomes

## Error Handling

### Automatic Fallback
- If auto-trigger fails, notification still created
- Marketing team can manually trigger via dashboard
- Error logged but doesn't block PMO workflow

### Retry Mechanisms
- Failed collaborations can be retried
- Notification status tracks attempts
- Manual override always available

## Future Enhancements

1. **Real-time Notifications**: Push notifications to marketing team
2. **Progress Tracking**: Live updates on analysis progress
3. **Approval Workflow**: PMO review and approval of marketing plans
4. **Resource Integration**: Automatic resource allocation and scheduling
5. **Cross-team Collaboration**: Integration with other team workflows

## Files Modified/Created

### Modified:
- `app/api/pmo-notify-team/route.ts` - Added auto-trigger functionality
- `app/api/marketing-agent-collaboration/route.ts` - Enhanced metadata support

### Created:
- `app/api/pmo-marketing-integration/route.ts` - New integration API
- `components/marketing/MarketingTeamDashboard.tsx` - Marketing team interface

This integration successfully bridges the gap between PMO requirements and marketing execution, providing a seamless, automated workflow while maintaining flexibility for manual intervention when needed.
