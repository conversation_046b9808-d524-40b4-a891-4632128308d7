'use client';

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Users,
  Settings,
  ArrowLeft,
  Sparkles
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { TaskExtractionModalProvider } from '../../../components/PMO/TaskExtractionModalProvider';

interface PMOLayoutProps {
  children: ReactNode;
}

export default function PMOLayout({ children }: PMOLayoutProps) {
  const pathname = usePathname();
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="bg-gray-800 rounded-lg shadow-md p-8 max-w-md w-full">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-gray-300 mb-6">
            You need to be logged in to access the PMO system.
          </p>
          <Link
            href="/services/admin/login"
            className="block w-full py-2 px-4 bg-purple-600 text-white rounded-md text-center hover:bg-purple-700 transition-colors"
          >
            Go to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <TaskExtractionModalProvider>
      <div className="min-h-screen flex flex-col bg-gray-900">
        {/* Top Navigation Bar */}
        <header className="bg-gray-800 border-b border-gray-700">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between h-16">
              <div className="flex items-center">
                <Link href="/services/admin" className="flex items-center text-gray-300 hover:text-white mr-4">
                  <ArrowLeft className="w-5 h-5 mr-1" />
                  <span>Back to Admin</span>
                </Link>

                <div className="hidden md:flex items-center space-x-4 ml-4">
                  <Link
                    href="/services/pmo"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      pathname === '/services/pmo'
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    }`}
                  >
                    Dashboard
                  </Link>
                  <Link
                    href="/services/pmo/teams"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      pathname === '/services/pmo/teams'
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    }`}
                  >
                    Teams
                  </Link>
                  <Link
                    href="/services/pmo/settings"
                    className={`px-3 py-2 rounded-md text-sm font-medium ${
                      pathname === '/services/pmo/settings'
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    }`}
                  >
                    Settings
                  </Link>
                </div>
              </div>

              <div className="flex items-center">
                <div className="flex items-center">
                  <div className="flex items-center bg-purple-600/20 px-3 py-1 rounded-full">
                    <Sparkles className="w-4 h-4 text-purple-400 mr-2" />
                    <span className="text-sm font-medium text-purple-300">PMO Agent</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Mobile Navigation (shown on small screens) */}
        <div className="md:hidden bg-gray-800 border-b border-gray-700">
          <div className="px-2 py-3 space-y-1 sm:px-3">
            <Link
              href="/services/pmo"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                pathname === '/services/pmo'
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              Dashboard
            </Link>
            <Link
              href="/services/pmo/teams"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                pathname === '/services/pmo/teams'
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              Teams
            </Link>
            <Link
              href="/services/pmo/settings"
              className={`block px-3 py-2 rounded-md text-base font-medium ${
                pathname === '/services/pmo/settings'
                  ? 'bg-gray-900 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              Settings
            </Link>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </TaskExtractionModalProvider>
  );
}
