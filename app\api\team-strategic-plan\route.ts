import { NextRequest, NextResponse } from 'next/server';
import { addDoc, collection, updateDoc, doc } from 'firebase/firestore';
import { db } from '../../../components/firebase';
import { addAgentOutput } from '../../../lib/firebase/agentOutputs';
import { processPMODocument } from '../../../lib/pmo/processPMODocument';

/**
 * API endpoint for teams to create strategic implementation plans
 * This replaces the PMO-generated strategic plans with team-specific plans
 */
export async function POST(request: NextRequest) {
  try {
    const {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      strategicPlanContent,
      userId,
      category,
      agentType,
      metadata
    } = await request.json();

    // Validate required fields
    if (!pmoId || !teamId || !teamName || !projectTitle || !strategicPlanContent || !userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const currentDate = new Date().toISOString();
    const documentTitle = `Strategic Implementation Plan - ${projectTitle} (${teamName}) - (${pmoId})`;

    // Save to Agent_Output collection with proper PMO integration
    const agentOutputData = {
      userId,
      agentType: agentType || `${teamName}_Strategic_Director`,
      title: documentTitle,
      content: strategicPlanContent,
      createdAt: new Date(),
      metadata: {
        ...metadata,
        pmoId,
        teamId,
        teamName,
        projectTitle,
        category: category || `PMO -  ${projectTitle} - ${pmoId}`,
        documentType: 'Strategic Implementation Plan',
        generatedAt: currentDate,
        source: 'Team',
        createdBy: teamName,
        strategicPlanVersion: '1.0'
      }
    };

    // Add to Agent_Output collection
    const agentOutputResult = await addAgentOutput(agentOutputData);

    // Automatically trigger project creation from the agent output
    try {
      console.log(`[PROJECT_CREATION] Triggering automatic project creation for requestId: ${agentOutputResult.id}`);

      // Import the createProjectAgent
      const { createProjectAgent } = await import('../../../lib/agents/createProjectAgent');

      // Create projects from the agent output
      const projectResult = await createProjectAgent.createProjectsFromAgentOutput(
        agentOutputResult.id,
        userId,
        pmoId
      );

      if (projectResult.success) {
        console.log(`[PROJECT_CREATION] Successfully created ${projectResult.projectsCreated.length} projects with ${projectResult.totalTasksCreated} tasks`);
        console.log(`[PROJECT_CREATION] Projects: ${projectResult.projectsCreated.map(p => p.projectName).join(', ')}`);
      } else {
        console.warn(`[PROJECT_CREATION] Failed to create projects: ${projectResult.error}`);
      }
    } catch (projectError) {
      console.error(`[PROJECT_CREATION] Error during automatic project creation:`, projectError);
      // Don't fail the entire request if project creation fails
    }

    // Process as PMO document for PDF generation and bytestore storage
    const pmoDocumentResult = await processPMODocument({
      title: documentTitle,
      content: strategicPlanContent,
      pmoId,
      userId,
      category: category || `PMO -  ${projectTitle} - ${pmoId}`,
      metadata: {
        ...agentOutputData.metadata,
        agentOutputId: agentOutputResult.id,
        pdfGenerated: true
      }
    });

    if (!pmoDocumentResult.success) {
      console.warn('Failed to process PMO document, but agent output was saved:', pmoDocumentResult.error);
    }

    // Update team notification status if it exists
    try {
      // This would require finding and updating the team notification
      // For now, we'll create a completion record
      await addDoc(
        collection(db, 'users', userId, 'teamCompletions'),
        {
          pmoId,
          teamId,
          teamName,
          projectTitle,
          strategicPlanCreated: true,
          strategicPlanId: agentOutputResult.id,
          completedAt: new Date(),
          documentTitle,
          category: category || `PMO -  ${projectTitle} - ${pmoId}`,
          metadata: {
            agentOutputId: agentOutputResult.id,
            pdfUrl: pmoDocumentResult.downloadUrl
          }
        }
      );
    } catch (updateError) {
      console.warn('Failed to update team notification status:', updateError);
    }

    return NextResponse.json({
      success: true,
      agentOutputId: agentOutputResult.id,
      documentTitle,
      pdfUrl: pmoDocumentResult.downloadUrl,
      message: `Strategic implementation plan created successfully by ${teamName} team`
    });

  } catch (error) {
    console.error('Error creating team strategic plan:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create strategic plan'
      },
      { status: 500 }
    );
  }
}
