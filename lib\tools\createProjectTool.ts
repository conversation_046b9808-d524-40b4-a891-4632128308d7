import { z } from 'zod';
import { addProject } from '@/lib/firebase/planner';
import { processWithGroq } from './groq-ai';
import { Project } from 'admin/planner/types';
import { CreateTasksAgent } from '../agents/createTasksAgent';
import { adminDb } from '../../components/firebase-admin';

// Define the schema for project creation input
export const CreateProjectInputSchema = z.object({
  projectName: z.string().describe('Name of the project to create'),
  projectDescription: z.string().describe('Description of the project'),
  startDate: z.string().describe('Project start date in YYYY-MM-DD format'),
  endDate: z.string().describe('Project end date in YYYY-MM-DD format'),
  owner: z.string().describe('Project owner user ID (email)'),
  members: z.array(z.string()).describe('Array of team member user IDs'),
  categories: z.array(z.string()).describe('Project categories'),
  status: z.enum(['Active', 'Completed', 'On Hold', 'Cancelled']).default('Active'),
  taskInstructions: z.string().optional().describe('Instructions for creating tasks based on strategic analysis'),
  createTasks: z.boolean().default(true).describe('Whether to automatically create tasks for the project'),
  // PMO-specific fields
  pmoId: z.string().optional().describe('PMO record ID for alignment'),
  pmoCategory: z.string().optional().describe('PMO category (generated from PMO Title) for contextual information'),
  teamName: z.string().optional().describe('Team name that created the strategic plan'),
  usePMOTaskAgent: z.boolean().default(false).describe('Whether to use PMO-specific task creation agent with category context'),
});

export type CreateProjectInput = z.infer<typeof CreateProjectInputSchema>;

// Define the schema for project creation result
export const CreateProjectResultSchema = z.object({
  success: z.boolean().describe('Whether the project was created successfully'),
  projectId: z.string().optional().describe('ID of the created project'),
  projectName: z.string().optional().describe('Name of the created project'),
  tasksCreated: z.number().default(0).describe('Number of tasks created'),
  taskCreationSummary: z.string().optional().describe('Summary of task creation process'),
  error: z.string().optional().describe('Error message if project creation failed'),
});

export type CreateProjectResult = z.infer<typeof CreateProjectResultSchema>;

// Define the createProjectTool
export const createProjectTool = {
  name: 'createProject',
  description: 'Creates a new project in the system and optionally generates tasks based on strategic analysis',
  inputSchema: CreateProjectInputSchema,
  responseSchema: CreateProjectResultSchema,
  model: 'gemini-2.5-pro',
  streaming: false,
  execute: async (input: CreateProjectInput): Promise<CreateProjectResult> => {
    try {
      // Validate input
      const validatedInput = CreateProjectInputSchema.parse(input);

      console.log(`CreateProjectTool: Creating project "${validatedInput.projectName}"`);

      // Format project name for PMO alignment if PMO fields are provided
      let projectName = validatedInput.projectName;
      if (validatedInput.pmoCategory && validatedInput.pmoId) {
        projectName = `PMO - ${validatedInput.pmoCategory} - ${validatedInput.pmoId}`;
      }

      // Create enhanced project description with PMO metadata
      let enhancedDescription = validatedInput.projectDescription;
      if (validatedInput.pmoCategory || validatedInput.pmoId || validatedInput.teamName) {
        enhancedDescription += '\n\n--- PMO Metadata ---\n';
        if (validatedInput.pmoCategory) {
          enhancedDescription += `PMO Category: ${validatedInput.pmoCategory}\n`;
        }
        if (validatedInput.teamName) {
          enhancedDescription += `Team: ${validatedInput.teamName}\n`;
        }
        if (validatedInput.pmoId) {
          enhancedDescription += `PMO ID: ${validatedInput.pmoId}\n`;
        }
        enhancedDescription += `PMO Context: Category-based strategic planning\n`;
      }

      // Create the project data
      const projectData: Omit<Project, 'id'> = {
        name: projectName,
        description: enhancedDescription,
        startDate: new Date(validatedInput.startDate),
        endDate: new Date(validatedInput.endDate),
        owner: validatedInput.owner,
        members: validatedInput.members,
        categories: validatedInput.categories,
        status: validatedInput.status,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Create the project in the system
      const projectId = await addProject(projectData);
      console.log(`CreateProjectTool: Project created with ID: ${projectId}`);

      let tasksCreated = 0;
      let taskCreationSummary = '';

      // Create tasks if requested
      if (validatedInput.createTasks && validatedInput.taskInstructions) {
        console.log(`CreateProjectTool: Creating tasks for project ${projectId}`);

        try {
          // Create the full project object for the createTasksAgent
          const fullProject: Project = {
            id: projectId,
            ...projectData
          };

          // Use PMO task agent if specified, otherwise use regular task agent
          if (validatedInput.usePMOTaskAgent && validatedInput.pmoCategory) {
            // Import and use PMO task agent
            const { CreatePMOTasksAgent } = await import('../agents/createPMOTasksAgent');
            const pmoTaskAgent = new CreatePMOTasksAgent({
              includeExplanation: true,
              streamResponse: false
            });

            // Prepare additional context from various PMO fields
            let additionalContext = '';
            if (validatedInput.taskInstructions) {
              additionalContext += `Task Instructions: ${validatedInput.taskInstructions}\n`;
            }
            if (validatedInput.teamName) {
              additionalContext += `Team Name: ${validatedInput.teamName}\n`;
            }
            if (validatedInput.pmoId) {
              additionalContext += `PMO ID: ${validatedInput.pmoId}\n`;
            }

            const taskResult = await pmoTaskAgent.createTasksFromStrategicPlan(
              projectId,
              fullProject,
              validatedInput.pmoCategory,
              additionalContext || undefined
            );

            if (taskResult.success) {
              tasksCreated = taskResult.tasks.length;
              taskCreationSummary = taskResult.creationResults.summary;
              console.log(`CreateProjectTool: Successfully created ${tasksCreated} tasks using PMO agent`);

              // Update project categories to match task categories
              await updateProjectCategoriesFromTasks(projectId, taskResult.tasks);
            } else {
              console.warn(`CreateProjectTool: PMO task creation failed: ${taskResult.error}`);
              taskCreationSummary = `PMO task creation failed: ${taskResult.error}`;
            }
          } else {
            // Use the regular createTasksAgent
            const taskAgent = new CreateTasksAgent({
              includeExplanation: true,
              streamResponse: false
            });

            const taskResult = await taskAgent.createTasks(
              projectId,
              fullProject,
              validatedInput.taskInstructions
            );

            if (taskResult.success) {
              tasksCreated = taskResult.tasks.length;
              taskCreationSummary = taskResult.creationResults.summary;
              console.log(`CreateProjectTool: Successfully created ${tasksCreated} tasks`);

              // Update project categories to match task categories
              await updateProjectCategoriesFromTasks(projectId, taskResult.tasks);
            } else {
              console.warn(`CreateProjectTool: Task creation failed: ${taskResult.error}`);
              taskCreationSummary = `Task creation failed: ${taskResult.error}`;
            }
          }
        } catch (taskError) {
          console.error(`CreateProjectTool: Error creating tasks:`, taskError);
          taskCreationSummary = `Task creation error: ${taskError instanceof Error ? taskError.message : String(taskError)}`;
        }
      }

      return {
        success: true,
        projectId,
        projectName: validatedInput.projectName,
        tasksCreated,
        taskCreationSummary
      };

    } catch (error) {
      console.error('CreateProjectTool: Error creating project:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        tasksCreated: 0
      };
    }
  }
};

/**
 * Helper function to extract project and task information from strategic analysis
 * This can be used by agents to parse their strategic plans and create projects
 * Uses PMO category context rather than reading actual documents
 */
export const extractProjectFromStrategicAnalysis = async (
  strategicAnalysis: string,
  userId: string,
  defaultMembers: string[] = [],
  documentPath?: string
): Promise<CreateProjectInput | null> => {
  try {
    const prompt = `
You are a PMO project management expert. Analyze the following strategic analysis and extract project information.

STRATEGIC ANALYSIS (PMO Category Context):
${strategicAnalysis}

${documentPath ? `CONTEXT PATH: ${documentPath}` : ''}

Extract the following information and format as JSON:
1. Project name (concise, professional, PMO-aligned)
2. Project description (comprehensive summary with PMO context)
3. Suggested start date (YYYY-MM-DD format, default to today if not specified)
4. Suggested end date (YYYY-MM-DD format, estimate based on scope)
5. Appropriate project categories (e.g., Marketing, Development, Research, etc.)
6. Task creation instructions (detailed instructions for PMO task creation based on category context)

Return ONLY a JSON object with this structure:
{
  "projectName": "string",
  "projectDescription": "string with PMO context",
  "startDate": "YYYY-MM-DD",
  "endDate": "YYYY-MM-DD",
  "categories": ["category1", "category2"],
  "taskInstructions": "detailed instructions for PMO task creation based on category context"
}
`;

    const response = await processWithGroq({
      prompt,
      model: 'gemini-2.5-pro',
      modelOptions: {
        temperature: 0.3,
        maxTokens: 2000,
      },
    });

    // Parse the JSON response
    const jsonMatch = response.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in response');
    }

    const extractedData = JSON.parse(jsonMatch[0]);

    return {
      projectName: extractedData.projectName,
      projectDescription: extractedData.projectDescription,
      startDate: extractedData.startDate,
      endDate: extractedData.endDate,
      owner: '<EMAIL>', // Default to Admin User
      members: ['<EMAIL>', '<EMAIL>', ...defaultMembers],
      categories: extractedData.categories || ['General'],
      status: 'Active' as const,
      taskInstructions: extractedData.taskInstructions,
      createTasks: true,
      usePMOTaskAgent: false // Default to false for extracted projects
    };

  } catch (error) {
    console.error('Error extracting project from strategic analysis:', error);
    return null;
  }
};

/**
 * Update project categories to match the categories from created tasks
 */
async function updateProjectCategoriesFromTasks(projectId: string, tasks: any[]): Promise<void> {
  try {
    // Extract unique categories from tasks
    const taskCategories = [...new Set(tasks.map(task => task.category).filter(Boolean))];

    if (taskCategories.length === 0) {
      console.log(`CreateProjectTool: No task categories found for project ${projectId}`);
      return;
    }

    console.log(`CreateProjectTool: Updating project ${projectId} categories to match tasks: [${taskCategories.join(', ')}]`);

    // Update the project document with the task categories
    const projectRef = adminDb.collection('projects').doc(projectId);
    await projectRef.update({
      categories: taskCategories,
      updatedAt: new Date()
    });

    console.log(`CreateProjectTool: Successfully updated project ${projectId} categories`);
  } catch (error) {
    console.error(`CreateProjectTool: Error updating project categories for ${projectId}:`, error);
  }
}
