// GenericScriptFormatter.ts
// A pattern-based script formatter that dynamically identifies character names without hardcoding

/**
 * Formats theatrical script content following standard script conventions
 * using pattern recognition to dynamically identify character names.
 * 
 * @param text The original script content
 * @returns Properly formatted theatrical script text
 */
export function formatScript(text: string): string {
    if (!text || typeof text !== 'string') return '';
    
    // Create an initial working copy
    let result = text;
    
    // STEP 1: PREPROCESS TEXT INTO PARAGRAPHS
    // Split into paragraphs for structural processing
    const paragraphs = result.split(/\n{2,}|\r\n{2,}/);
    
    // STEP 2: PARAGRAPH-LEVEL PROCESSING
    const processedParagraphs = paragraphs.map(paragraph => {
      // Skip empty paragraphs
      if (!paragraph.trim()) return '';
      
      // Format different types of content
      if (isInstructionText(paragraph)) {
        return formatInstruction(paragraph);
      } else if (isDialogueText(paragraph)) {
        return formatDialogue(paragraph);
      } else {
        return formatGeneralText(paragraph);
      }
    });
    
    // Join processed paragraphs
    result = processedParagraphs.join('\n\n');
    
    // STEP 3: GLOBAL POST-PROCESSING
    // Ensure proper line breaks around character names
    result = ensureProperLineBreaks(result);
    
    return result;
  }
  
  /**
   * Determines if a paragraph contains instructional content
   */
  function isInstructionText(text: string): boolean {
    const instructionalPatterns = [
      /\b(ready|begin|start|rehearse|practice)/i,
      /\b(would you like)/i,
      /\b(your line|your part)/i,
      /\b(let's|let us)/i,
      /\b(I'll read|I will read)/i,
      /\b(whenever you're ready)/i,
      /\b(shall we)/i,
      /\b(feel free)/i,
      /\b(focus on)/i,
      /\b(pick up from)/i
    ];
    
    return instructionalPatterns.some(pattern => pattern.test(text));
  }
  
  /**
   * Determines if a paragraph contains dialogue
   */
  function isDialogueText(text: string): boolean {
    // Check for dialogue patterns
    const dialoguePatterns = [
      /^[A-Z][a-z]+(\s[A-Z][a-z]+)?:/m,  // NAME: at start of line
      /[""][^""]+[""]/,                  // Quoted text
      /\([^)]+\)/,                       // Parenthetical notes
      /\b[A-Z][a-z]+\s+says\b/,          // NAME says
      /\b[A-Z][a-z]+'s line is\b/        // NAME's line is
    ];
    
    return dialoguePatterns.some(pattern => pattern.test(text));
  }
  
  /**
   * Format text identified as an instruction to the user
   */
  function formatInstruction(text: string): string {
    // Subtle formatting for instructions
    return `> ${text}`;
  }
  
  /**
   * Format text identified as dialogue
   */
  function formatDialogue(text: string): string {
    let result = text;
    
    // Format character names with explicit dialogue attribution patterns
    // Pattern 1: NAME: dialogue
    result = result.replace(/\b([A-Z][a-z]+(?:\s[A-Z][a-z]+)?)(\s*:)(?!\s*\n)/g, '\n\n**$1**$2');
    
    // Pattern 2: NAME (emotion): dialogue
    result = result.replace(/\b([A-Z][a-z]+(?:\s[A-Z][a-z]+)?)\s*\(([^)]+)\)(\s*:)/g, '\n\n**$1** _($2)_$3');
    
    // Pattern 3: NAME'S LINE IS: "dialogue"
    result = result.replace(/\b([A-Z][a-z]+(?:\s[A-Z][a-z]+)?)(['']s line is:?)/gi, '\n\n**$1**$2');
    
    // Format quoted text
    result = result.replace(/([^*])[""]([^*""][^""]+)[""]([^*])/g, '$1*"$2"*$3');
    
    // Format stage directions
    result = result.replace(/\(([^)]+)\)/g, '_($1)_');
    
    // Clean up leading newlines
    result = result.replace(/^\n+/, '');
    
    return result;
  }
  
  /**
   * Format general text
   */
  function formatGeneralText(text: string): string {
    let result = text;
    
    // Identify and format likely character names using contextual patterns
    // Look for capitalized names that appear followed by a verb of speech
    result = result.replace(/\b([A-Z][a-z]+(?:\s[A-Z][a-z]+)?)\b\s+(says|tells|asks|exclaims|shouts|whispers|responds|replies|interjects)/g, 
      '\n\n**$1** $2');
    
    // Format stage directions
    result = result.replace(/\(([^)]+)\)/g, '_($1)_');
    
    // Format play titles and other special terms
    result = result.replace(/["']([A-Za-z\s]+)["']\s+by\s+([A-Za-z\s]+)/g, 
      '"_$1_" by **$2**');
    
    return result;
  }
  
  /**
   * Post-process the entire text to ensure proper formatting
   */
  function ensureProperLineBreaks(text: string): string {
    let result = text;
    
    // Ensure character names have proper spacing before them
    // This helps with names that weren't caught in the first pass
    result = result.replace(/([^\n])(\n\*\*[A-Z][a-z]+(?:\s[A-Z][a-z]+)?\*\*)/g, '$1\n$2');
    
    // Ensure no excessive whitespace
    result = result.replace(/\n{3,}/g, '\n\n');
    
    // Clean up the beginning of the text
    result = result.replace(/^\n+/, '');
    
    return result;
  }
  
  /**
   * Integration function for DialogueDisplay
   */
  export function processScriptText(content: string, role: string): string {
    // Only apply script formatting to non-user messages
    if (role.toLowerCase() !== "user") {
      return formatScript(content);
    }
    return content;
  }