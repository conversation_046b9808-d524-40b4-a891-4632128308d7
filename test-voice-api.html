<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Preview API Test</title>
</head>
<body>
    <h1>Voice Preview API Test</h1>
    <button onclick="testVoiceAPI()">Test Voice Preview API</button>
    <div id="result"></div>
    <audio id="audioPlayer" controls style="display: none;"></audio>

    <script>
        async function testVoiceAPI() {
            const resultDiv = document.getElementById('result');
            const audioPlayer = document.getElementById('audioPlayer');
            
            resultDiv.innerHTML = 'Testing Voice Preview API...';
            
            try {
                const voiceId = '2mltbVQP21Fq8XgIfRQJ'; // Marketing Director voice
                const text = 'Hello, this is a test of the voice preview API.';
                
                console.log('Making API request to /api/elevenlabs/voice-preview...');
                
                const response = await fetch('/api/elevenlabs/voice-preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        voiceId: voiceId,
                        text: text
                    }),
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));
                
                if (!response.ok) {
                    let errorMessage = 'Unknown error';
                    try {
                        const errorData = await response.json();
                        errorMessage = errorData.error || errorMessage;
                    } catch {
                        errorMessage = await response.text();
                    }
                    throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorMessage}`);
                }
                
                const audioBlob = await response.blob();
                console.log('Audio blob size:', audioBlob.size, 'bytes');
                console.log('Audio blob type:', audioBlob.type);
                
                if (audioBlob.size === 0) {
                    throw new Error('Received empty audio blob');
                }
                
                const audioUrl = URL.createObjectURL(audioBlob);
                audioPlayer.src = audioUrl;
                audioPlayer.style.display = 'block';
                
                resultDiv.innerHTML = `
                    <p style="color: green;">✅ Voice Preview API Test Successful!</p>
                    <p>Audio blob size: ${audioBlob.size} bytes</p>
                    <p>Audio type: ${audioBlob.type}</p>
                    <p>Audio URL created: ${audioUrl}</p>
                `;
                
                // Try to play the audio
                try {
                    await audioPlayer.play();
                    resultDiv.innerHTML += '<p style="color: green;">🔊 Audio playback started successfully!</p>';
                } catch (playError) {
                    resultDiv.innerHTML += `<p style="color: orange;">⚠️ Audio created but autoplay blocked: ${playError.message}</p>`;
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                resultDiv.innerHTML = `
                    <p style="color: red;">❌ Voice Preview API Test Failed!</p>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
