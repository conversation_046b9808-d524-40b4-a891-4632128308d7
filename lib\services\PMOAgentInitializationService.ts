/**
 * PMO Agent Initialization Service
 * Handles the creation and configuration of ElevenLabs agents for PMO voice meetings
 */

import { PMOAgentVoiceConfig, generatePMOAgentPrompt } from '../agents/voice/pmoAgentVoiceConfig';

export interface AgentInitializationOptions {
  userId: string;
  agentConfig: PMOAgentVoiceConfig;
  agentId: string;
  documentContext: any[];
  forceRecreate?: boolean;
}

export interface AgentInitializationResult {
  success: boolean;
  agentId: string;
  conversationId?: string;
  knowledgeBaseId?: string;
  documentsUploaded: number;
  error?: string;
}

/**
 * PMO Agent Initialization Service
 */
export class PMOAgentInitializationService {
  private static instance: PMOAgentInitializationService;
  private initializedAgents: Map<string, AgentInitializationResult> = new Map();
  private agentDocumentHashes: Map<string, Set<string>> = new Map(); // Track uploaded document hashes per agent

  private constructor() {}

  static getInstance(): PMOAgentInitializationService {
    if (!PMOAgentInitializationService.instance) {
      PMOAgentInitializationService.instance = new PMOAgentInitializationService();
    }
    return PMOAgentInitializationService.instance;
  }

  /**
   * Initialize or get existing PMO agent
   */
  async initializeAgent(options: AgentInitializationOptions): Promise<AgentInitializationResult> {
    const { userId, agentConfig, agentId, documentContext, forceRecreate = false } = options;

    console.log(`[PMO_AGENT_INIT] Initializing agent: ${agentId} for user: ${userId}`);

    // Check if agent is already initialized and not forcing recreation
    if (!forceRecreate && this.initializedAgents.has(agentId)) {
      const existing = this.initializedAgents.get(agentId)!;
      console.log(`[PMO_AGENT_INIT] Using existing agent: ${agentId}`);
      return existing;
    }

    try {
      // Step 1: Check if this specific PMO agent already exists
      const existingAgent = await this.checkPMOAgentExists(userId, agentId);

      if (existingAgent && !forceRecreate) {
        console.log(`[PMO_AGENT_INIT] PMO agent already exists: ${agentId} for user: ${userId}`);

        // Create conversation session for existing agent
        const conversationId = await this.createConversationSession(agentId);

        const result: AgentInitializationResult = {
          success: true,
          agentId,
          conversationId,
          documentsUploaded: 0 // Existing agent, documents already uploaded
        };

        this.initializedAgents.set(agentId, result);
        return result;
      }

      // Step 2: Prepare knowledge base documents with deduplication
      const knowledgeBaseDocuments = this.prepareKnowledgeBaseDocuments(
        documentContext,
        agentId,
        forceRecreate // If forcing recreation, include all documents
      );
      
      // Step 3: Generate agent prompt with context
      const prompt = generatePMOAgentPrompt(agentConfig, {
        documentCount: documentContext.length,
        projectCount: documentContext.filter(doc => doc.projectId).length,
        recentProjects: documentContext
          .filter(doc => doc.projectId)
          .slice(0, 3)
          .map(doc => doc.title)
      });

      // Step 4: Create agent via API
      const response = await fetch('/api/elevenlabs/create-agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId,
          name: agentConfig.agentType,
          voiceId: agentConfig.voiceId,
          prompt,
          knowledgeBase: knowledgeBaseDocuments
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('[PMO_AGENT_INIT] Agent creation failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData,
          requestPayload: {
            agentId,
            name: agentConfig.agentName,
            voiceId: agentConfig.voiceId,
            promptLength: prompt.length,
            documentsCount: knowledgeBaseDocuments.length
          }
        });
        throw new Error(errorData.error || `Failed to create agent (${response.status}: ${response.statusText})`);
      }

      const result = await response.json();
      
      const initResult: AgentInitializationResult = {
        success: true,
        agentId: result.agentId,
        conversationId: result.conversationId,
        knowledgeBaseId: result.knowledgeBaseId,
        documentsUploaded: result.documentsUploaded
      };

      // Cache the result
      this.initializedAgents.set(agentId, initResult);
      
      console.log(`[PMO_AGENT_INIT] Agent initialized successfully: ${agentId}`);
      return initResult;

    } catch (error) {
      console.error(`[PMO_AGENT_INIT] Error initializing agent ${agentId}:`, error);
      
      const errorResult: AgentInitializationResult = {
        success: false,
        agentId,
        documentsUploaded: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };

      return errorResult;
    }
  }

  /**
   * Check if agent exists in ElevenLabs
   */
  private async checkAgentExists(agentId: string): Promise<boolean> {
    try {
      const response = await fetch(`/api/elevenlabs/create-agent?agentId=${encodeURIComponent(agentId)}`);

      if (response.ok) {
        const data = await response.json();
        return data.exists;
      }

      return false;
    } catch (error) {
      console.error(`[PMO_AGENT_INIT] Error checking agent existence:`, error);
      return false;
    }
  }

  /**
   * Check if PMO agent exists using the new validation logic
   */
  private async checkPMOAgentExists(userId: string, agentId: string): Promise<boolean> {
    try {
      // Import the validation function dynamically to avoid circular dependencies
      const { checkPMOAgentExists } = await import('../elevenlabs/agentValidation');

      const result = await checkPMOAgentExists(userId, agentId);
      return result.exists;
    } catch (error) {
      console.error(`[PMO_AGENT_INIT] Error checking PMO agent existence:`, error);
      return false;
    }
  }

  /**
   * Create conversation session for existing agent
   */
  private async createConversationSession(agentId: string): Promise<string | undefined> {
    try {
      const response = await fetch('/api/elevenlabs/create-conversation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ agentId })
      });

      if (response.ok) {
        const data = await response.json();
        return data.conversationId;
      }
      
      return undefined;
    } catch (error) {
      console.error(`[PMO_AGENT_INIT] Error creating conversation session:`, error);
      return undefined;
    }
  }

  /**
   * Prepare documents for knowledge base upload with deduplication
   */
  private prepareKnowledgeBaseDocuments(
    documentContext: any[],
    agentId: string,
    forceIncludeAll: boolean = false
  ): Array<{
    id: string;
    title: string;
    content: string;
    metadata?: Record<string, any>;
    contentHash: string;
  }> {
    const existingHashes = this.agentDocumentHashes.get(agentId) || new Set();
    const newDocuments: Array<{
      id: string;
      title: string;
      content: string;
      metadata?: Record<string, any>;
      contentHash: string;
    }> = [];

    for (const doc of documentContext) {
      const sanitizedContent = this.sanitizeContent(doc.content || '');
      const contentHash = this.generateContentHash(doc.id, sanitizedContent, doc.title);

      // Skip if document already exists (unless forcing inclusion)
      if (!forceIncludeAll && existingHashes.has(contentHash)) {
        console.log(`[PMO_AGENT_INIT] Skipping duplicate document: ${doc.title} (${doc.id})`);
        continue;
      }

      newDocuments.push({
        id: doc.id,
        title: doc.title || 'Untitled Document',
        content: sanitizedContent,
        contentHash,
        metadata: {
          agentType: doc.agentType,
          category: doc.category,
          createdAt: doc.createdAt,
          projectId: doc.projectId,
          teamName: doc.teamName,
          documentId: doc.id, // Include original document ID for tracking
          ...doc.metadata
        }
      });

      // Track this document hash
      existingHashes.add(contentHash);
    }

    // Update the agent's document hash set
    this.agentDocumentHashes.set(agentId, existingHashes);

    console.log(`[PMO_AGENT_INIT] Prepared ${newDocuments.length} new documents for upload (${documentContext.length - newDocuments.length} duplicates skipped)`);
    return newDocuments;
  }

  /**
   * Sanitize content for knowledge base upload
   */
  private sanitizeContent(content: string): string {
    // Remove excessive whitespace and normalize line breaks
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n')
      .trim();
  }

  /**
   * Generate a content hash for duplicate detection
   */
  private generateContentHash(id: string, content: string, title: string): string {
    // Simple hash based on document ID, title, and content length/checksum
    const contentChecksum = this.simpleChecksum(content);
    const titleChecksum = this.simpleChecksum(title);
    return `${id}-${titleChecksum}-${contentChecksum}-${content.length}`;
  }

  /**
   * Simple checksum function for content hashing
   */
  private simpleChecksum(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    return Math.abs(hash).toString(36);
  }

  /**
   * Update agent knowledge base with new documents
   */
  async updateAgentKnowledgeBase(
    agentId: string, 
    newDocuments: any[]
  ): Promise<{ success: boolean; documentsAdded: number; error?: string }> {
    try {
      console.log(`[PMO_AGENT_INIT] Updating knowledge base for agent: ${agentId}`);
      
      const knowledgeBaseDocuments = this.prepareKnowledgeBaseDocuments(newDocuments, agentId, false);
      
      const response = await fetch('/api/elevenlabs/update-knowledge-base', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agentId,
          documents: knowledgeBaseDocuments
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update knowledge base');
      }

      const result = await response.json();
      
      console.log(`[PMO_AGENT_INIT] Knowledge base updated: ${result.documentsAdded} documents added`);
      
      return {
        success: true,
        documentsAdded: result.documentsAdded
      };

    } catch (error) {
      console.error(`[PMO_AGENT_INIT] Error updating knowledge base:`, error);
      return {
        success: false,
        documentsAdded: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get initialization status for an agent
   */
  getAgentStatus(agentId: string): AgentInitializationResult | null {
    return this.initializedAgents.get(agentId) || null;
  }

  /**
   * Clear cached agent data (useful for forcing recreation)
   */
  clearAgentCache(agentId?: string): void {
    if (agentId) {
      this.initializedAgents.delete(agentId);
      this.agentDocumentHashes.delete(agentId); // Also clear document hashes
      console.log(`[PMO_AGENT_INIT] Cleared cache for agent: ${agentId}`);
    } else {
      this.initializedAgents.clear();
      this.agentDocumentHashes.clear(); // Clear all document hashes
      console.log(`[PMO_AGENT_INIT] Cleared all agent cache`);
    }
  }

  /**
   * Get all initialized agents
   */
  getAllInitializedAgents(): Map<string, AgentInitializationResult> {
    return new Map(this.initializedAgents);
  }
}
