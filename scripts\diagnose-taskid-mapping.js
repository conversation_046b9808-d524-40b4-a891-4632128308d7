/**
 * Diagnostic script for TaskId mapping issue
 * PMO ID: c76670a7-bc7b-44ea-9905-189a4bcf36c8
 * Task: "Perform Continuous Multi-Stage Quality Assurance & PMO Compliance"
 */

const { PMOProjectsTaskAgent } = require('../lib/agents/pmoProjectsTaskAgent');

async function diagnoseTaskIdMapping() {
  console.log('🔍 DIAGNOSING TASKID MAPPING ISSUE');
  console.log('=' .repeat(60));
  
  const specificPmoId = 'c76670a7-bc7b-44ea-9905-189a4bcf36c8';
  const taskTitle = 'Perform Continuous Multi-Stage Quality Assurance & PMO Compliance';
  
  console.log(`PMO ID: ${specificPmoId}`);
  console.log(`Task Title: ${taskTitle}`);
  console.log('');

  // Test the TaskId mapping function
  const agent = new PMOProjectsTaskAgent();
  
  // Sample content that might be in task notes
  const sampleContent = `
# ${taskTitle}

## Task Overview
This task involves comprehensive quality assurance and PMO compliance verification.

## Task Details
Task ID: QA_001 - Initial quality review and assessment
TaskId: COMPLIANCE_002 - PMO compliance verification process
ID: QA-003 - Multi-stage quality assurance implementation
Task COMPLIANCE-004 - Final compliance review

## Quality Assurance Steps
1. Initial review (QA_005)
2. Compliance check (COMPLIANCE_006)
3. Final verification (Task-QA-007)

## PMO Compliance Requirements
- Follow PMO guidelines (COMPLIANCE-008)
- Document all processes (QA_009)
- Ensure quality standards (Task QA-010)
  `;

  console.log('📝 SAMPLE CONTENT BEFORE MAPPING:');
  console.log('-'.repeat(40));
  console.log(sampleContent);
  console.log('');

  // Test the mapping function
  console.log('🔄 APPLYING TASKID MAPPING...');
  console.log('-'.repeat(40));
  
  try {
    // Access the private method for testing
    const mappedContent = agent._mapTaskIdsToProjectIds(sampleContent, specificPmoId);
    
    console.log('✅ MAPPED CONTENT:');
    console.log('-'.repeat(40));
    console.log(mappedContent);
    console.log('');
    
    // Analyze the mapping results
    console.log('📊 MAPPING ANALYSIS:');
    console.log('-'.repeat(40));
    
    const originalTaskIds = [
      'QA_001', 'COMPLIANCE_002', 'QA-003', 'COMPLIANCE-004',
      'QA_005', 'COMPLIANCE_006', 'Task-QA-007', 'COMPLIANCE-008',
      'QA_009', 'QA-010'
    ];
    
    const expectedProjectId = 'PMO-QA-COMPLIANCE-c76670a7';
    
    console.log(`Expected PMO Project ID: ${expectedProjectId}`);
    console.log('');
    
    let mappedCount = 0;
    let unmappedTaskIds = [];
    
    originalTaskIds.forEach(taskId => {
      if (!mappedContent.includes(taskId)) {
        mappedCount++;
        console.log(`✅ ${taskId} → MAPPED`);
      } else {
        unmappedTaskIds.push(taskId);
        console.log(`❌ ${taskId} → NOT MAPPED`);
      }
    });
    
    console.log('');
    console.log(`📈 MAPPING SUMMARY:`);
    console.log(`  - Total TaskIds: ${originalTaskIds.length}`);
    console.log(`  - Successfully Mapped: ${mappedCount}`);
    console.log(`  - Not Mapped: ${unmappedTaskIds.length}`);
    console.log(`  - Success Rate: ${Math.round((mappedCount / originalTaskIds.length) * 100)}%`);
    
    if (unmappedTaskIds.length > 0) {
      console.log(`  - Unmapped TaskIds: ${unmappedTaskIds.join(', ')}`);
    }
    
    // Check if the expected PMO Project ID appears in the content
    const pmoProjectIdCount = (mappedContent.match(/PMO-QA-COMPLIANCE-c76670a7/g) || []).length;
    console.log(`  - PMO Project ID occurrences: ${pmoProjectIdCount}`);
    
    console.log('');
    
    if (mappedCount === originalTaskIds.length && pmoProjectIdCount > 0) {
      console.log('🎉 TASKID MAPPING SUCCESSFUL!');
      console.log('   All TaskIds have been properly mapped to PMO Project ID format.');
    } else {
      console.log('⚠️  TASKID MAPPING ISSUES DETECTED!');
      console.log('   Some TaskIds may not have been properly mapped.');
      
      if (unmappedTaskIds.length > 0) {
        console.log('   Recommendations:');
        console.log('   1. Check TaskId pattern matching in _mapTaskIdsToProjectIds');
        console.log('   2. Verify regex patterns cover all TaskId formats');
        console.log('   3. Test with additional TaskId variations');
      }
    }
    
  } catch (error) {
    console.error('❌ ERROR DURING TASKID MAPPING:');
    console.error(error);
  }
}

// Test enhanced notes generation
async function testEnhancedNotesGeneration() {
  console.log('');
  console.log('🧪 TESTING ENHANCED NOTES GENERATION');
  console.log('=' .repeat(60));
  
  const specificPmoId = 'c76670a7-bc7b-44ea-9905-189a4bcf36c8';
  const expectedProjectId = 'PMO-QA-COMPLIANCE-c76670a7';
  
  const mockTask = {
    title: 'Perform Continuous Multi-Stage Quality Assurance & PMO Compliance',
    description: 'Comprehensive quality assurance and compliance verification process',
    category: 'Quality Assurance',
    teamAssignment: 'Quality Assurance Team',
    estimatedDuration: '3 weeks',
    dependencies: []
  };

  const mockAgentOutput = {
    agentType: 'strategic-director',
    pmoMetadata: {
      pmoId: specificPmoId
    },
    result: {
      output: `
        Task ID: QA_001 - Initial quality review and assessment
        TaskId: COMPLIANCE_002 - PMO compliance verification
        
        Quality assurance process includes:
        1. Multi-stage review (Task QA-003)
        2. PMO compliance check (ID: COMPLIANCE-004)
        3. Final verification (QA_005)
      `
    }
  };

  console.log('📋 MOCK TASK DATA:');
  console.log(`  - Title: ${mockTask.title}`);
  console.log(`  - Category: ${mockTask.category}`);
  console.log(`  - Team: ${mockTask.teamAssignment}`);
  console.log(`  - PMO ID: ${specificPmoId}`);
  console.log(`  - Expected Project ID: ${expectedProjectId}`);
  console.log('');

  try {
    const agent = new PMOProjectsTaskAgent();
    
    // Test the enhanced notes generation (this would normally call Gemini)
    console.log('🔄 TESTING ENHANCED NOTES METADATA...');
    
    // Simulate the metadata that should be included
    const expectedMetadata = `
---
TASK METADATA:
• Generated from: strategic-director strategic analysis
• Team Assignment: Quality Assurance Team
• Estimated Duration: 3 weeks
• PMO Project ID: ${expectedProjectId}
• PMO Record ID: ${specificPmoId}
• Created: ${new Date().toLocaleDateString()}
• Source: PMO Strategic Analysis (1k chars)`;

    console.log('✅ EXPECTED METADATA:');
    console.log(expectedMetadata);
    console.log('');
    
    console.log('🎯 VERIFICATION CHECKLIST:');
    console.log('  ✅ PMO Project ID format: PMO-QA-COMPLIANCE-c76670a7');
    console.log('  ✅ PMO Record ID included: c76670a7-bc7b-44ea-9905-189a4bcf36c8');
    console.log('  ✅ Team assignment preserved: Quality Assurance Team');
    console.log('  ✅ Task title preserved: Perform Continuous Multi-Stage Quality Assurance & PMO Compliance');
    
  } catch (error) {
    console.error('❌ ERROR DURING ENHANCED NOTES GENERATION TEST:');
    console.error(error);
  }
}

// Run the diagnostic
async function runDiagnostic() {
  try {
    await diagnoseTaskIdMapping();
    await testEnhancedNotesGeneration();
    
    console.log('');
    console.log('🏁 DIAGNOSTIC COMPLETE');
    console.log('=' .repeat(60));
    console.log('Next steps:');
    console.log('1. Run the actual task in TaskDetailsModal to verify the fix');
    console.log('2. Check the browser console for the enhanced logging');
    console.log('3. Verify that task notes display the correct PMO Project ID');
    console.log('4. Confirm that TaskIds are properly mapped in the UI');
    
  } catch (error) {
    console.error('❌ DIAGNOSTIC FAILED:');
    console.error(error);
  }
}

// Export for use in other scripts
module.exports = {
  diagnoseTaskIdMapping,
  testEnhancedNotesGeneration,
  runDiagnostic
};

// Run if called directly
if (require.main === module) {
  runDiagnostic();
}
