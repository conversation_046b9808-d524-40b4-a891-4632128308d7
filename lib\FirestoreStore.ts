import { CustomStoreInterface } from "./CustomStoreInterface";
import { Document } from "langchain/document";
import { adminDb } from "components/firebase-admin";
import { Query, CollectionReference, FieldPath } from "firebase-admin/firestore";

interface FirestoreStoreInput {
  collectionPath: string;
}

export class FirestoreStore implements CustomStoreInterface<string, Document> {
  [x: string]: any;
  public readonly collectionPath: string;
  private collectionRef: CollectionReference;

  constructor(fields: FirestoreStoreInput) {
    this.collectionPath = fields.collectionPath;
    this.collectionRef = adminDb.collection(this.collectionPath);
  }

  /**
   * Sets multiple key-value pairs in the Firestore collection.
   * @param keyValuePairs - An array of [key, Document] tuples.
   */
  async mset(keyValuePairs: [string, Document][]): Promise<void> {
    const batch = adminDb.batch();
    keyValuePairs.forEach(([key, doc]) => {
      const docRef = this.collectionRef.doc(key);
      batch.set(docRef, {
        content: doc.pageContent,
        metadata: doc.metadata,
      });
    });
    await batch.commit();
  }

  /**
   * Retrieves multiple documents based on provided keys.
   * @param keys - An array of document IDs.
   * @returns An array of Document instances or undefined if not found.
   */
  async mget(keys: string[]): Promise<(Document | undefined)[]> {
    const promises = keys.map((key) => this.collectionRef.doc(key).get());
    const snapshots = await Promise.all(promises);
    return snapshots.map((snap) => {
      if (!snap.exists) return undefined;
      const data = snap.data();
      return new Document({
        pageContent: data?.content || "",
        metadata: data?.metadata || {},
      });
    });
  }

  /**
   * Retrieves multiple documents based on `doc_id` field.
   * @param docIds - An array of `doc_id` values to search.
   * @returns An array of Document instances or undefined if not found.
   */
  async mgetByDocId(docIds: string[]): Promise<(Document | undefined)[]> {
    const retrievedDocs = await Promise.all(
      docIds.map(async (docId) => {
        const querySnapshot = await this.collectionRef.where('doc_id', '==', docId).get();
        if (!querySnapshot.empty) {
          return querySnapshot.docs.map((docSnapshot) => {
            const docData = docSnapshot.data();
            return new Document({
              pageContent: docData?.content || "",
              metadata: docData?.metadata || {},
            });
          });
        } else {
          console.warn(`Document not found in Firestore for doc_id: ${docId}`);
          return undefined;
        }
      })
    );

    // Flatten the array and filter out undefined
    return retrievedDocs.flat().filter((doc): doc is Document => doc !== undefined);
  }


/**
 * Retrieves all document chunks for a given base docId by appending incremental indices.
 * @param docId - The base document ID (without chunk index).
 * @returns A Promise that resolves to an array of Document instances representing the chunks.
 */
async fetchDocumentChunksByDocId(docId: string): Promise<import("langchain/document").Document[]> {
  const chunks: Document[] = [];
  let index = 1; // Start with the first chunk

  try {
    while (true) {
      // Construct the document ID by appending the index
      const chunkId = `${docId}_${index}`;

      // Log the chunkId before executing the search
      console.log(`Searching for document with ID: ${chunkId}`);

      // Attempt to get the document using the constructed chunkId
      const docSnapshot = await this.collectionRef.doc(chunkId).get();

      // If no document is found, break the loop and stop fetching more chunks
      if (!docSnapshot.exists) {
        console.log(`No more chunks found for document ID: ${chunkId}`);
        break;
      }

      console.log(`Found chunk for document ID: ${chunkId}`);

      // Process the retrieved document
      const data = docSnapshot.data();
      if (data) {
        const content = data.content || "";
        const metadata = {
          category: data.category || "",
          chunk_id: chunkId, // The chunkId serves as the unique identifier here
          document_title: data.document_title || "",
          page_number: data.page_number || 1,
          ...data.metadata,
        };

        chunks.push(
          new Document({
            pageContent: content,
            metadata: metadata,
          })
        );
      } else {
        console.warn(`No data found in document snapshot for ID: ${chunkId}`);
      }

      // Increment the index to search for the next chunk
      index++;
    }
  } catch (error) {
    console.error(`Error fetching document chunks for doc_id ${docId}:`, error);
  }

  return chunks;
}




  /**
   * Deletes multiple documents based on provided keys.
   * @param keys - An array of document IDs to delete.
   */
  async mdelete(keys: string[]): Promise<void> {
    const batch = adminDb.batch();
    keys.forEach((key) => {
      const docRef = this.collectionRef.doc(key);
      batch.delete(docRef);
    });
    await batch.commit();
  }

  /**
   * Asynchronously yields keys from the Firestore collection, optionally filtered by a prefix.
   * @param prefix - Optional prefix to filter keys.
   * @returns An asynchronous generator that yields document IDs.
   */
  async *yieldKeys(prefix?: string): AsyncGenerator<string> {
    let query: Query;

    if (prefix) {
      query = this.collectionRef
        .where(FieldPath.documentId(), ">=", prefix)
        .where(FieldPath.documentId(), "<", prefix + "\uf8ff");
    } else {
      query = this.collectionRef;
    }

    const snapshot = await query.get();
    for (const doc of snapshot.docs) {
      yield doc.id;
    }
  }
}
