"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import type { CalendarEvent, CalendarViewType } from "../types"
import { ChevronLeft, ChevronRight, CalendarIcon, Clock, MapPin, User, AlertCircle } from "lucide-react"
import {
  format,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  addDays,
  addMonths,
  addWeeks,
  subMonths,
  subWeeks,
  differenceInDays,
  parseISO,
  isWithinInterval,
  getDay,
  isWeekend,
} from "date-fns"

interface CalendarViewProps {
  events: CalendarEvent[]
  view: CalendarViewType
  onEventClick?: (event: CalendarEvent) => void
}

// Helper function to calculate color brightness (0-255)
const getBrightness = (hexColor: string): number => {
  // Default to white if color is not provided or invalid
  if (!hexColor || hexColor === "") return 255

  // Remove # if present
  const color = hexColor.startsWith("#") ? hexColor.slice(1) : hexColor

  // Handle shorthand hex (e.g., #FFF)
  const hex = color.length === 3 ? `${color[0]}${color[0]}${color[1]}${color[1]}${color[2]}${color[2]}` : color

  // Parse the hex values
  const r = Number.parseInt(hex.substring(0, 2), 16) || 0
  const g = Number.parseInt(hex.substring(2, 4), 16) || 0
  const b = Number.parseInt(hex.substring(4, 6), 16) || 0

  // Calculate brightness using the formula: (0.299*R + 0.587*G + 0.114*B)
  return 0.299 * r + 0.587 * g + 0.114 * b
}

// Helper to generate a lighter version of a color for hover effects
const getLighterColor = (hexColor: string): string => {
  // Default to a light gray if color is not provided or invalid
  if (!hexColor || hexColor === "") return "#f0f0f0"

  // Remove # if present
  const color = hexColor.startsWith("#") ? hexColor.slice(1) : hexColor

  // Handle shorthand hex (e.g., #FFF)
  const hex = color.length === 3 ? `${color[0]}${color[0]}${color[1]}${color[1]}${color[2]}${color[2]}` : color

  // Parse the hex values
  const r = Number.parseInt(hex.substring(0, 2), 16) || 0
  const g = Number.parseInt(hex.substring(2, 4), 16) || 0
  const b = Number.parseInt(hex.substring(4, 6), 16) || 0

  // Make it lighter by adding 40 to each component (capped at 255)
  const lighterR = Math.min(r + 40, 255)
    .toString(16)
    .padStart(2, "0")
  const lighterG = Math.min(g + 40, 255)
    .toString(16)
    .padStart(2, "0")
  const lighterB = Math.min(b + 40, 255)
    .toString(16)
    .padStart(2, "0")

  return `#${lighterR}${lighterG}${lighterB}`
}

// Helper to format date range
const formatDateRange = (start: string | Date, end: string | Date): string => {
  try {
    const startDate = start instanceof Date ? start : parseISO(start)
    const endDate = end instanceof Date ? end : parseISO(end)

    if (isSameDay(startDate, endDate)) {
      return format(startDate, "MMM d, yyyy")
    }

    if (startDate.getFullYear() === endDate.getFullYear()) {
      if (startDate.getMonth() === endDate.getMonth()) {
        return `${format(startDate, "MMM d")} - ${format(endDate, "d, yyyy")}`
      }
      return `${format(startDate, "MMM d")} - ${format(endDate, "MMM d, yyyy")}`
    }

    return `${format(startDate, "MMM d, yyyy")} - ${format(endDate, "MMM d, yyyy")}`
  } catch (error) {
    console.error("Error formatting date range:", error)
    return "Invalid date range"
  }
}

// Safe date parsing function to handle potential invalid dates
const safeParseISO = (date: string | Date): Date | null => {
  try {
    if (!date) return null

    // If it's already a Date object, just return it
    if (date instanceof Date) {
      // Check if the date is valid
      if (isNaN(date.getTime())) return null
      return date
    }

    // Otherwise parse it as a string
    const parsedDate = parseISO(date)
    // Check if the date is valid
    if (isNaN(parsedDate.getTime())) return null
    return parsedDate
  } catch (error) {
    console.error("Error parsing date:", error)
    return null
  }
}

// Get color for day of week
const getDayColor = (day: Date, isToday: boolean): string => {
  if (isToday) return "bg-gradient-to-br from-blue-600/40 to-blue-800/40 border-blue-500"

  // Weekend days
  if (isWeekend(day)) return "bg-gradient-to-br from-purple-600/30 to-purple-800/30 border-purple-500/50"

  // Weekdays get different colors based on the day
  const dayOfWeek = getDay(day)
  switch (dayOfWeek) {
    case 1: // Monday
      return "bg-gradient-to-br from-emerald-600/30 to-emerald-800/30 border-emerald-500/50"
    case 2: // Tuesday
      return "bg-gradient-to-br from-cyan-600/30 to-cyan-800/30 border-cyan-500/50"
    case 3: // Wednesday
      return "bg-gradient-to-br from-amber-600/30 to-amber-800/30 border-amber-500/50"
    case 4: // Thursday
      return "bg-gradient-to-br from-indigo-600/30 to-indigo-800/30 border-indigo-500/50"
    case 5: // Friday
      return "bg-gradient-to-br from-rose-600/30 to-rose-800/30 border-rose-500/50"
    default:
      return "bg-gradient-to-br from-gray-600/30 to-gray-800/30 border-gray-500/50"
  }
}

// Get text color for day of week
const getDayTextColor = (day: Date, isToday: boolean): string => {
  if (isToday) return "text-blue-300"

  // Weekend days
  if (isWeekend(day)) return "text-purple-300"

  // Weekdays get different colors based on the day
  const dayOfWeek = getDay(day)
  switch (dayOfWeek) {
    case 1: // Monday
      return "text-emerald-300"
    case 2: // Tuesday
      return "text-cyan-300"
    case 3: // Wednesday
      return "text-amber-300"
    case 4: // Thursday
      return "text-indigo-300"
    case 5: // Friday
      return "text-rose-300"
    default:
      return "text-gray-300"
  }
}

// Get border color for day of week
const getDayBorderColor = (day: Date, isToday: boolean): string => {
  if (isToday) return "border-blue-500"

  // Weekend days
  if (isWeekend(day)) return "border-purple-500/50"

  // Weekdays get different colors based on the day
  const dayOfWeek = getDay(day)
  switch (dayOfWeek) {
    case 1: // Monday
      return "border-emerald-500/50"
    case 2: // Tuesday
      return "border-cyan-500/50"
    case 3: // Wednesday
      return "border-amber-500/50"
    case 4: // Thursday
      return "border-indigo-500/50"
    case 5: // Friday
      return "border-rose-500/50"
    default:
      return "border-gray-500/50"
  }
}

const CalendarView: React.FC<CalendarViewProps> = ({ events, view, onEventClick }) => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [calendarDays, setCalendarDays] = useState<Date[]>([])
  const [visibleEvents, setVisibleEvents] = useState<CalendarEvent[]>([])
  const [hoveredEvent, setHoveredEvent] = useState<string | null>(null)
  const [sortedEvents, setSortedEvents] = useState<CalendarEvent[]>([])
  const [error, setError] = useState<string | null>(null)
  const [tooltipPosition, setTooltipPosition] = useState<{ x: number; y: number } | null>(null)
  const [tooltipText, setTooltipText] = useState<string>("")
  const tooltipRef = useRef<HTMLDivElement>(null)

  // Update calendar days when view or current date changes
  useEffect(() => {
    let days: Date[] = []
    try {
      if (view === "day") {
        days = [currentDate]
      } else if (view === "week") {
        const start = startOfWeek(currentDate, { weekStartsOn: 0 }) // Start on Sunday
        const end = endOfWeek(currentDate, { weekStartsOn: 0 })
        days = eachDayOfInterval({ start, end })
      } else if (view === "month") {
        const start = startOfMonth(currentDate)
        const end = endOfMonth(currentDate)
        days = eachDayOfInterval({ start, end })
      }
      setCalendarDays(days)
      setError(null)
    } catch (err) {
      console.error("Error updating calendar days:", err)
      setError("Failed to update calendar view")
    }
  }, [currentDate, view])

  // Filter events based on the current view
  useEffect(() => {
    if (!events || events.length === 0) {
      setVisibleEvents([])
      setSortedEvents([])
      return
    }

    try {
      // Validate events data
      const validEvents = events.filter((event) => {
        if (!event.start || !event.end) {
          console.warn("Event missing start or end date:", event)
          return false
        }

        // Try to parse dates to check validity
        const startDate = safeParseISO(event.start)
        const endDate = safeParseISO(event.end)

        if (!startDate || !endDate) {
          console.warn("Event has invalid date format:", event)
          return false
        }

        return true
      })

      let filteredEvents: CalendarEvent[] = []

      if (view === "day") {
        const day = currentDate
        filteredEvents = validEvents.filter((event) => {
          const eventStart = safeParseISO(event.start)
          const eventEnd = safeParseISO(event.end)

          if (!eventStart || !eventEnd) return false

          return (
            isWithinInterval(day, { start: eventStart, end: eventEnd }) ||
            isSameDay(eventStart, day) ||
            isSameDay(eventEnd, day)
          )
        })
      } else if (view === "week") {
        const weekStart = startOfWeek(currentDate, { weekStartsOn: 0 })
        const weekEnd = endOfWeek(currentDate, { weekStartsOn: 0 })
        filteredEvents = validEvents.filter((event) => {
          const eventStart = safeParseISO(event.start)
          const eventEnd = safeParseISO(event.end)

          if (!eventStart || !eventEnd) return false

          // Check if any part of the event falls within the week
          return eventStart <= weekEnd && eventEnd >= weekStart
        })
      } else if (view === "month") {
        const monthStart = startOfMonth(currentDate)
        const monthEnd = endOfMonth(currentDate)
        filteredEvents = validEvents.filter((event) => {
          const eventStart = safeParseISO(event.start)
          const eventEnd = safeParseISO(event.end)

          if (!eventStart || !eventEnd) return false

          // Check if any part of the event falls within the month
          return eventStart <= monthEnd && eventEnd >= monthStart
        })
      }

      setVisibleEvents(filteredEvents)

      // Sort events by start date and then by title
      const sorted = [...filteredEvents].sort((a, b) => {
        const dateA = safeParseISO(a.start) || new Date()
        const dateB = safeParseISO(b.start) || new Date()

        if (dateA.getTime() !== dateB.getTime()) {
          return dateA.getTime() - dateB.getTime()
        }

        return a.title.localeCompare(b.title)
      })

      setSortedEvents(sorted)
      setError(null)
    } catch (err) {
      console.error("Error filtering events:", err)
      setError("Failed to process calendar events")
    }
  }, [events, currentDate, view])

  const goToPrevious = () => {
    if (view === "day") setCurrentDate((prev) => addDays(prev, -1))
    else if (view === "week") setCurrentDate((prev) => subWeeks(prev, 1))
    else if (view === "month") setCurrentDate((prev) => subMonths(prev, 1))
  }

  const goToNext = () => {
    if (view === "day") setCurrentDate((prev) => addDays(prev, 1))
    else if (view === "week") setCurrentDate((prev) => addWeeks(prev, 1))
    else if (view === "month") setCurrentDate((prev) => addMonths(prev, 1))
  }

  const goToToday = () => setCurrentDate(new Date())

  const getHeaderText = () => {
    if (view === "day") return format(currentDate, "MMMM d, yyyy")
    else if (view === "week") {
      const start = startOfWeek(currentDate, { weekStartsOn: 0 })
      const end = endOfWeek(currentDate, { weekStartsOn: 0 })
      return `${format(start, "MMM d")} - ${format(end, "MMM d, yyyy")}`
    } else return format(currentDate, "MMMM yyyy")
  }

  // Calculate the position and width of an event bar in the timeline
  const calculateEventPosition = (event: CalendarEvent, days: Date[]) => {
    try {
      const eventStart = safeParseISO(event.start)
      const eventEnd = safeParseISO(event.end)

      if (!eventStart || !eventEnd) {
        return { left: "0%", width: "0%", isPartial: false }
      }

      // Find the earliest and latest dates in our calendar view
      const viewStart = days[0]
      const viewEnd = days[days.length - 1]

      // Adjust event dates to be within view bounds
      const effectiveStart = eventStart < viewStart ? viewStart : eventStart
      const effectiveEnd = eventEnd > viewEnd ? viewEnd : eventEnd

      // Calculate the start position (as percentage of total width)
      const totalDays = differenceInDays(viewEnd, viewStart) + 1
      const startOffset = differenceInDays(effectiveStart, viewStart)
      const duration = differenceInDays(effectiveEnd, effectiveStart) + 1

      // Calculate percentage values
      const startPercentage = (startOffset / totalDays) * 100
      const widthPercentage = (duration / totalDays) * 100

      return {
        left: `${startPercentage}%`,
        width: `${widthPercentage}%`,
        isPartial: eventStart < viewStart || eventEnd > viewEnd,
      }
    } catch (error) {
      console.error("Error calculating event position:", error)
      return { left: "0%", width: "0%", isPartial: false }
    }
  }

  // Handle mouse events to show/hide tooltip
  const handleMouseEnter = (event: React.MouseEvent<HTMLDivElement>, title: string) => {
    const target = event.currentTarget
    const rect = target.getBoundingClientRect()
    const scrollX = window.scrollX || window.pageXOffset
    const scrollY = window.scrollY || window.pageYOffset

    // Calculate tooltip position (above the element)
    const x = rect.left + rect.width / 2 + scrollX
    const y = rect.top - 15 + scrollY // 15px above the element for better spacing

    setTooltipPosition({ x, y })
    setTooltipText(title)

    // Only set hovered event if the target has an event ID
    if (target.dataset.eventId) {
      setHoveredEvent(target.dataset.eventId)
    }
  }

  const handleMouseLeave = () => {
    setTooltipPosition(null)
    setTooltipText("")
    setHoveredEvent(null)
  }

  const renderDayView = () => {
    // For day view, we'll show a vertical timeline
    const isToday = isSameDay(currentDate, new Date())
    const dayColor = getDayColor(currentDate, isToday)
    const dayTextColor = getDayTextColor(currentDate, isToday)

    return (
      <div className="bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 overflow-hidden">
        <div className={`p-4 border-b border-white/20 ${dayColor}`}>
          <h3 className={`text-lg font-medium ${dayTextColor}`}>{format(currentDate, "EEEE, MMMM d")}</h3>
        </div>

        <div className="p-4">
          {sortedEvents.length > 0 ? (
            <div className="space-y-4">
              {sortedEvents.map((event) => {
                // Determine text color based on background color brightness
                const colorBrightness = getBrightness(event.color)
                const textColor = colorBrightness > 128 ? "text-gray-900" : "text-white"

                // Get lighter color for hover effect
                const hoverColor = getLighterColor(event.color)

                // Safely format dates
                let timeDisplay = "Time not specified"
                try {
                  const startDate = safeParseISO(event.start)
                  const endDate = safeParseISO(event.end)
                  if (startDate && endDate) {
                    timeDisplay = `${format(startDate, "h:mm a")} - ${format(endDate, "h:mm a")}`
                  }
                } catch (error) {
                  console.error("Error formatting event time:", error)
                }

                return (
                  <div
                    key={event.id}
                    data-event-id={event.id}
                    className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${textColor} ${
                      hoveredEvent === event.id ? "shadow-lg" : ""
                    }`}
                    style={{
                      backgroundColor: hoveredEvent === event.id ? hoverColor : event.color,
                    }}
                    onClick={() => onEventClick && onEventClick(event)}
                    onMouseEnter={(e) => handleMouseEnter(e, event.title)}
                    onMouseLeave={handleMouseLeave}
                  >
                    <div className={`${isMobile ? "flex-col" : "flex justify-between"} items-start`}>
                      <div>
                        <h4 className="font-medium truncate">{event.title}</h4>
                        <div className="text-sm mt-1 opacity-90">{timeDisplay}</div>
                      </div>
                      <div
                        className={`text-xs px-2 py-1 rounded-full bg-black/20 ${isMobile ? "mt-2 inline-block" : ""}`}
                      >
                        {event.type}
                      </div>
                    </div>

                    {event.location && (
                      <div className="mt-2 text-sm flex items-center">
                        <MapPin className="w-4 h-4 mr-1 opacity-70" />
                        <span className="truncate">{event.location}</span>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-white/70">
              <CalendarIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No events scheduled for this day</p>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Detect if we're on a small screen (mobile view)
  const isMobileView = () => {
    if (typeof window !== "undefined") {
      return window.innerWidth < 768 // 768px is a common breakpoint for tablets
    }
    return false
  }

  const [isMobile, setIsMobile] = useState(false)

  // Update mobile state when window resizes
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(isMobileView())
    }

    // Set initial value
    handleResize()

    // Add event listener
    window.addEventListener("resize", handleResize)

    // Clean up
    return () => window.removeEventListener("resize", handleResize)
  }, [])

  const renderMobileListView = () => {
    // For mobile, we'll show a simple list of events grouped by day
    const days = calendarDays
    const today = new Date()

    return (
      <div className="bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 overflow-hidden">
        <div className="p-3 border-b border-white/20 bg-gradient-to-r from-purple-600/30 to-blue-600/30">
          <div className="text-sm font-medium text-white/90">
            {view === "week" ? "Week View" : "Month View"} - {getHeaderText()}
          </div>
        </div>

        <div className="divide-y divide-white/10">
          {days.map((day, dayIndex) => {
            const isToday = isSameDay(day, today)
            const dayEvents = sortedEvents.filter((event) => {
              const eventStart = safeParseISO(event.start)
              const eventEnd = safeParseISO(event.end)
              if (!eventStart || !eventEnd) return false

              return isSameDay(day, eventStart) || isSameDay(day, eventEnd) || (day >= eventStart && day <= eventEnd)
            })

            if (dayEvents.length === 0 && view === "month") {
              return null // In month view, don't show days without events to save space
            }

            const dayColor = getDayColor(day, isToday)
            const dayTextColor = getDayTextColor(day, isToday)
            const dayBorderColor = getDayBorderColor(day, isToday)

            return (
              <div key={dayIndex} className={`${isToday ? "bg-blue-900/10" : ""}`}>
                <div className={`p-2 sticky top-0 ${dayColor} backdrop-blur-sm border-b ${dayBorderColor}`}>
                  <div className="flex justify-between items-center">
                    <div className={`font-medium ${dayTextColor}`}>{format(day, "EEE, MMM d")}</div>
                    {dayEvents.length > 0 && (
                      <div className="text-xs bg-white/20 px-2 py-0.5 rounded-full text-white">
                        {dayEvents.length} event{dayEvents.length !== 1 ? "s" : ""}
                      </div>
                    )}
                  </div>
                </div>

                {dayEvents.length > 0 ? (
                  <div className="divide-y divide-white/10">
                    {dayEvents.map((event) => {
                      // Add a subtle background color based on the day
                      const bgColorClass = isToday
                        ? "hover:bg-blue-900/20 active:bg-blue-900/30"
                        : isWeekend(day)
                          ? "hover:bg-purple-900/20 active:bg-purple-900/30"
                          : "hover:bg-white/5 active:bg-white/10"

                      return (
                        <div
                          key={`${dayIndex}-${event.id}`}
                          data-event-id={event.id}
                          className={`p-3 cursor-pointer ${bgColorClass}`}
                          onClick={() => onEventClick && onEventClick(event)}
                          onMouseEnter={(e) => handleMouseEnter(e, event.title)}
                          onMouseLeave={handleMouseLeave}
                        >
                          <div className="flex items-start">
                            <div
                              className="w-3 h-3 rounded-full mt-1.5 mr-2 flex-shrink-0"
                              style={{ backgroundColor: event.color }}
                            ></div>
                            <div className="flex-1 min-w-0">
                              <div
                                className="font-medium text-white truncate"
                                onMouseEnter={(e) => {
                                  // Only show tooltip if text is truncated
                                  const element = e.currentTarget
                                  if (element.scrollWidth > element.clientWidth) {
                                    handleMouseEnter(e, event.title)
                                  }
                                }}
                                onMouseLeave={handleMouseLeave}
                              >
                                {event.title}
                              </div>
                              <div className="text-xs text-white/70 mt-1 flex items-center">
                                <Clock className="w-3 h-3 mr-1" />
                                {formatDateRange(event.start, event.end)}
                              </div>
                              {event.type && (
                                <div className="mt-1">
                                  <span className="text-xs px-1.5 py-0.5 rounded bg-white/10 text-white/80">
                                    {event.type}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="p-3 text-center text-white/50 text-sm">No events</div>
                )}
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  const renderProjectStyleView = () => {
    // This is the MS Project style view with days across the top and tasks as rows
    const days = calendarDays
    const today = new Date()

    // For mobile devices, use the list view instead
    if (isMobile) {
      return renderMobileListView()
    }

    return (
      <div className="bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 overflow-hidden">
        {/* Header with days */}
        <div className="flex border-b border-white/20">
          {/* Task info column */}
          <div className="w-64 flex-shrink-0 border-r border-white/20 bg-gradient-to-br from-gray-700/50 to-gray-900/50 p-3">
            <div className="text-sm font-medium text-white/90">Task</div>
          </div>

          {/* Day columns */}
          <div className="flex-1 overflow-x-auto">
            <div className="flex min-w-max">
              {days.map((day, index) => {
                const isToday = isSameDay(day, today)
                const isCurrentMonth = view === "month" ? isSameMonth(day, currentDate) : true
                const dayColor = getDayColor(day, isToday)
                const dayTextColor = getDayTextColor(day, isToday)

                return (
                  <div
                    key={index}
                    className={`w-16 flex-shrink-0 p-2 text-center border-r border-white/10 ${dayColor} ${
                      !isCurrentMonth ? "opacity-60" : ""
                    }`}
                  >
                    <div className="text-xs text-white/70 font-medium">{format(day, "EEE")}</div>
                    <div className={`text-sm font-medium ${dayTextColor}`}>{format(day, "d")}</div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Task rows with timeline bars */}
        <div className="flex">
          {/* Task info column */}
          <div className="w-64 flex-shrink-0 border-r border-white/20 bg-white/5">
            {sortedEvents.length > 0 ? (
              sortedEvents.map((event, index) => (
                <div
                  key={event.id}
                  data-event-id={event.id}
                  className={`p-3 border-b border-white/10 hover:bg-white/5 ${index % 2 === 0 ? "bg-white/5" : ""}`}
                  onMouseEnter={(e) => handleMouseEnter(e, event.title)}
                  onMouseLeave={handleMouseLeave}
                >
                  <div className="font-medium text-white truncate">{event.title}</div>
                  <div className="text-xs text-white/70 mt-1 flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {formatDateRange(event.start, event.end)}
                  </div>
                </div>
              ))
            ) : (
              <div className="p-6 text-center text-white/70">
                <p>No events to display</p>
              </div>
            )}
          </div>

          {/* Timeline grid */}
          <div className="flex-1 overflow-x-auto">
            <div className="relative min-w-max">
              {/* Background grid */}
              <div className="flex">
                {days.map((day, index) => {
                  const isToday = isSameDay(day, today)
                  const isWeekend = getDay(day) === 0 || getDay(day) === 6
                  const isCurrentMonth = view === "month" ? isSameMonth(day, currentDate) : true

                  // Subtle background color based on day of week
                  let bgClass = ""
                  if (isToday) {
                    bgClass = "bg-blue-900/10"
                  } else if (isWeekend) {
                    bgClass = "bg-purple-900/5"
                  } else {
                    const dayOfWeek = getDay(day)
                    switch (dayOfWeek) {
                      case 1: // Monday
                        bgClass = "bg-emerald-900/5"
                        break
                      case 2: // Tuesday
                        bgClass = "bg-cyan-900/5"
                        break
                      case 3: // Wednesday
                        bgClass = "bg-amber-900/5"
                        break
                      case 4: // Thursday
                        bgClass = "bg-indigo-900/5"
                        break
                      case 5: // Friday
                        bgClass = "bg-rose-900/5"
                        break
                    }
                  }

                  return (
                    <div
                      key={index}
                      className={`w-16 flex-shrink-0 border-r border-white/10 ${bgClass} ${
                        !isCurrentMonth ? "opacity-60" : ""
                      }`}
                      style={{ height: `${Math.max(sortedEvents.length * 57, 100)}px` }}
                    ></div>
                  )
                })}
              </div>

              {/* Today indicator */}
              {days.some((day) => isSameDay(day, today)) && (
                <div
                  className="absolute top-0 bottom-0 border-l-2 border-red-500 z-10"
                  style={{
                    left: `${(days.findIndex((day) => isSameDay(day, today)) + 0.5) * 64}px`,
                  }}
                ></div>
              )}

              {/* Event bars */}
              {sortedEvents.map((event, eventIndex) => {
                const position = calculateEventPosition(event, days)
                const colorBrightness = getBrightness(event.color)
                const textColor = colorBrightness > 128 ? "text-gray-900" : "text-white"
                const hoverColor = getLighterColor(event.color)

                // Add a subtle glow effect
                const glowStyle = {
                  boxShadow: `0 0 8px ${event.color}80`,
                }

                return (
                  <div
                    key={event.id}
                    data-event-id={event.id}
                    className={`absolute h-10 rounded-md cursor-pointer transition-all duration-200 flex items-center px-2 ${textColor} ${
                      hoveredEvent === event.id ? "shadow-lg z-30" : "z-20"
                    }`}
                    style={{
                      left: position.left,
                      width: position.width,
                      top: `${eventIndex * 57 + 14}px`,
                      backgroundColor: hoveredEvent === event.id ? hoverColor : event.color,
                      ...(hoveredEvent === event.id ? glowStyle : {}),
                    }}
                    onClick={() => onEventClick && onEventClick(event)}
                    onMouseEnter={(e) => handleMouseEnter(e, event.title)}
                    onMouseLeave={handleMouseLeave}
                  >
                    <div
                      className="truncate text-sm font-medium w-full"
                      onMouseEnter={(e) => {
                        // Stop propagation to prevent parent's event handler from firing
                        e.stopPropagation()
                        // Only show tooltip if text is truncated
                        const element = e.currentTarget
                        if (element.scrollWidth > element.clientWidth) {
                          handleMouseEnter(e, event.title)
                        }
                      }}
                      onMouseLeave={handleMouseLeave}
                    >
                      {position.isPartial && <span>• </span>}
                      {event.title}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    )
  }

  const hasNoEvents = !events || events.length === 0

  return (
    <div className="calendar-container relative">
      <div
        className={`${isMobile ? "flex-col space-y-3" : "flex items-center justify-between"} p-4 border-b border-white/20 bg-gradient-to-r from-gray-800/80 to-gray-900/80`}
      >
        <div className={`${isMobile ? "flex justify-between" : "flex items-center gap-4"}`}>
          <div className="flex items-center gap-2">
            <button
              onClick={goToToday}
              className="px-3 py-1.5 text-white bg-gradient-to-r from-blue-500 to-blue-600 rounded-md hover:from-blue-600 hover:to-blue-700 transition-colors text-sm shadow-md"
            >
              Today
            </button>
            <div className="flex">
              <button onClick={goToPrevious} className="p-1.5 text-white hover:bg-white/10 rounded-l-md">
                <ChevronLeft className="h-5 w-5" />
              </button>
              <button onClick={goToNext} className="p-1.5 text-white hover:bg-white/10 rounded-r-md">
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Event count badge - show on mobile top right */}
          {isMobile && visibleEvents.length > 0 && (
            <div className="bg-gradient-to-r from-purple-500/70 to-blue-500/70 px-2 py-1 rounded-full text-xs text-white shadow-sm">
              {visibleEvents.length} event{visibleEvents.length !== 1 ? "s" : ""}
            </div>
          )}
        </div>

        <div className={`flex ${isMobile ? "justify-between items-center" : ""}`}>
          <h2 className={`${isMobile ? "text-lg" : "text-xl"} font-semibold text-white`}>{getHeaderText()}</h2>

          {/* Event count badge - show on desktop right */}
          {!isMobile && visibleEvents.length > 0 && (
            <div className="bg-gradient-to-r from-purple-500/70 to-blue-500/70 px-3 py-1 rounded-full text-sm text-white shadow-sm">
              {visibleEvents.length} event{visibleEvents.length !== 1 ? "s" : ""}
            </div>
          )}
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-900/30 border border-red-500/50 text-red-200 p-3 mb-4 rounded-lg flex items-center">
          <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}

      {hasNoEvents ? (
        <div className="bg-white/10 backdrop-blur-lg rounded-xl border border-white/20 p-10 text-center">
          <div className="text-white/70">
            <CalendarIcon size={48} className="mx-auto mb-4 opacity-50" />
            <h3 className="text-xl font-medium text-white mb-2">No events to display</h3>
            <p className="text-white/70">Add a personal item or task to see it in your calendar.</p>
          </div>
        </div>
      ) : (
        <>
          {view === "day" && renderDayView()}
          {(view === "week" || view === "month") && renderProjectStyleView()}
        </>
      )}

      {/* Tooltip for full task name */}
      {tooltipPosition && !isMobile && (
        <div
          ref={tooltipRef}
          className="fixed bg-gray-800/95 backdrop-blur-md text-white text-sm rounded-lg py-2 px-4 shadow-xl border border-white/20 z-50 whitespace-normal"
          style={{
            left: `${tooltipPosition.x}px`,
            top: `${tooltipPosition.y}px`,
            transform: "translateX(-50%) translateY(-100%)",
            maxWidth: "300px",
          }}
        >
          <div>{tooltipText}</div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-800/95"></div>
        </div>
      )}

      {/* Event details popup when hovering - only show on desktop */}
      {!isMobile && hoveredEvent && (
        <div className="fixed bottom-4 right-4 bg-gray-800/90 backdrop-blur-md p-4 rounded-lg shadow-xl border border-white/20 max-w-xs z-50 text-white">
          {(() => {
            const event = visibleEvents.find((e) => e.id === hoveredEvent)
            if (!event) return null

            return (
              <>
                <div className="flex items-start gap-2 mb-2">
                  <div
                    className="w-3 h-3 rounded-full mt-1.5 flex-shrink-0"
                    style={{ backgroundColor: event.color }}
                  ></div>
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{event.title}</h4>
                    <p className="text-xs text-white/70">{event.type}</p>
                  </div>
                </div>

                <div className="space-y-1.5 text-xs">
                  <div className="flex items-center gap-2">
                    <Clock className="w-3.5 h-3.5 text-white/70" />
                    <span>{formatDateRange(event.start, event.end)}</span>
                  </div>

                  {event.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="w-3.5 h-3.5 text-white/70" />
                      <span className="truncate">{event.location}</span>
                    </div>
                  )}

                  {event.attendees &&
                    typeof event.attendees === "object" &&
                    "length" in event.attendees &&
                    event.attendees.length > 0 && (
                      <div className="flex items-center gap-2">
                        <User className="w-3.5 h-3.5 text-white/70" />
                        <span>
                          {event.attendees.length} attendee{event.attendees.length !== 1 ? "s" : ""}
                        </span>
                      </div>
                    )}
                </div>
              </>
            )
          })()}
        </div>
      )}
    </div>
  )
}

export default CalendarView
