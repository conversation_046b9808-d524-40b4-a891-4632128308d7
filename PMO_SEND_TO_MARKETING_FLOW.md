# PMO "Send to Marketing" Flow Documentation

## Overview

This document details exactly what happens when a user clicks the "Send to Marketing" button in the PMO interface, specifically focusing on how the PMO record data (including contextCategories) flows to the marketing-agent-collaboration API.

## Complete Data Flow

### 1. User Clicks "Send to Marketing" Button

**Location:** `components/PMO/PMORecordList.tsx` - `sendToDelegatedTeam()` function

**Trigger:** User clicks the "Send to Marketing" button for a PMO record

**PMO Record Data Available:**
```typescript
interface PMORecord {
  id: string; // e.g., "9978d39b-2483-4478-8a94-ab20f61185d2"
  title: string;
  description: string;
  status: PMORecordStatus;
  priority: PMORecordPriority;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  category: string;
  sourceFile?: string | null;
  fileName?: string | null;
  customContext?: string | null;
  contextFiles?: string[] | null;
  contextCategories?: string[] | null; // ⭐ KEY FIELD - Contains document categories
  pmoAssessment?: string | null;
  agentIds: AgenticTeamId[];
  summary?: string | null;
  teamSelectionRationale?: string | null;
  resourceRecommendations?: string[] | null;
}
```

### 2. PMO Record Data Sent to pmo-notify-team API

**API Call:** `POST /api/pmo-notify-team`

**Payload Includes:**
- Basic notification data (pmoId, teamName, projectTitle, etc.)
- **Full PMO record object** containing all contextCategories and other metadata

```json
{
  "pmoId": "9978d39b-2483-4478-8a94-ab20f61185d2",
  "teamName": "Marketing",
  "projectTitle": "Scene Mate viral campaign",
  "projectDescription": "Develop a viral video ad campaign concept...",
  "pmoAssessment": "The Marketing Team is best suited...",
  "category": "PMO - Scene Mate viral campaign - 9978d39b-2483-4478-8a94-ab20f61185d2",
  "pmoRecord": {
    "id": "9978d39b-2483-4478-8a94-ab20f61185d2",
    "contextCategories": ["PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8"],
    "customContext": "Develop a viral video ad campaign concept...",
    "contextFiles": ["file-id-1", "file-id-2"],
    // ... all other PMO record fields
  }
}
```

### 3. Auto-Trigger Marketing Collaboration

**Location:** `app/api/pmo-notify-team/route.ts` - `triggerMarketingCollaboration()` function

**Process:**
1. **Team Detection:** Checks if `teamName.toLowerCase() === 'marketing'`
2. **Context Preparation:** Extracts contextCategories from PMO record
3. **Category Selection:** Uses first contextCategory as primary document search category
4. **Marketing Prompt Construction:** Creates comprehensive marketing analysis prompt

**Key Data Transformation:**
```javascript
// Use PMO's contextCategories for document search
const documentCategory = pmoData.pmoRecord?.contextCategories?.length > 0 
  ? pmoData.pmoRecord.contextCategories[0]  // ⭐ Uses PMO contextCategories
  : pmoData.category;

// Enhanced context with PMO details
const contextInformation = `
PMO Context:
- Project: ${pmoData.projectTitle}
- Assessment: ${pmoData.pmoAssessment}
- Priority: ${pmoData.priority}
- Rationale: ${pmoData.teamSelectionRationale}
- Custom Context: ${pmoData.pmoRecord?.customContext}
- Source Document Categories: ${pmoData.pmoRecord?.contextCategories?.join(', ')}
`;
```

### 4. Marketing Agent Collaboration API Call

**API Call:** `POST /api/marketing-agent-collaboration`

**Enhanced Payload:**
```json
{
  "prompt": "# PMO Marketing Requirements Analysis\n\n## Project Overview...",
  "modelProvider": "openai",
  "modelName": "o3-2025-04-16",
  "userId": "<EMAIL>",
  "context": "PMO Context:\n- Project: Scene Mate viral campaign\n- Assessment: The Marketing Team is best suited...\n- Source Document Categories: PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8",
  "category": "PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8", // ⭐ Uses PMO contextCategories
  "metadata": {
    "source": "PMO",
    "pmoId": "9978d39b-2483-4478-8a94-ab20f61185d2",
    "autoTriggered": true,
    "pmoContextCategories": ["PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8"],
    "pmoCustomContext": "Develop a viral video ad campaign concept..."
  }
}
```

### 5. Document Context Integration

**Location:** `app/api/marketing-agent-collaboration/route.ts`

**Process:**
1. **Document Search:** Uses the `category` field (from PMO contextCategories) to search for relevant documents
2. **Context Enhancement:** Retrieves documents from the specified category
3. **Strategic Analysis:** Marketing agents analyze with full document context

**Document Query:**
```javascript
// The category from PMO contextCategories is used here
const queryOptions = {
  category: body.category, // "PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8"
  useInternetSearch: false
};

// This searches for documents in the PMO's specified category
const queryResult = await queryDocumentsEnhanced(documentQuery, queryOptions, userId);
```

### 6. Marketing Analysis Generation

**Process:**
1. **Strategic Director Agent** receives the prompt with PMO context
2. **Document Context** from PMO contextCategories is integrated
3. **Comprehensive Analysis** generated with 8-point framework:
   - Strategic Marketing Assessment
   - Target Audience Analysis
   - Competitive Landscape
   - Marketing Channel Strategy
   - Content Strategy
   - Campaign Planning
   - Success Metrics
   - Resource Requirements

### 7. Result Storage and Traceability

**Storage Location:** `Agent_Output` collection

**Stored Data Includes:**
```json
{
  "requestId": "uuid-v4",
  "agentType": "strategic-director",
  "category": "PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8",
  "pmoMetadata": {
    "source": "PMO",
    "pmoId": "9978d39b-2483-4478-8a94-ab20f61185d2",
    "autoTriggered": true,
    "pmoContextCategories": ["PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8"]
  },
  "contextOptions": {
    "category": "PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8",
    "customContext": "PMO Context: Project: Scene Mate viral campaign..."
  },
  "result": {
    "thinking": "Strategic analysis thinking process...",
    "output": "Comprehensive marketing strategy analysis..."
  }
}
```

## Key Benefits of This Integration

### 1. **Seamless Context Transfer**
- PMO's contextCategories directly become the document search category
- All PMO assessment and rationale preserved
- Custom context and file references maintained

### 2. **Document Context Integration**
- Marketing agents automatically access documents from PMO's specified categories
- No manual category selection required
- Consistent document context across PMO and Marketing workflows

### 3. **Full Traceability**
- Complete audit trail from PMO record to marketing analysis
- PMO metadata preserved in marketing output
- Bidirectional linking between PMO and marketing deliverables

### 4. **Automated Workflow**
- Zero manual intervention required
- Immediate marketing analysis initiation
- Consistent process execution

## Example: Scene Mate Campaign Flow

**PMO Record ID:** `9978d39b-2483-4478-8a94-ab20f61185d2`
**Context Categories:** `["PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8"]`

1. **User clicks "Send to Marketing"**
2. **PMO record with contextCategories sent to pmo-notify-team API**
3. **Marketing collaboration auto-triggered with category: "PMO - Scene Mate campaign - 765d1041-422b-4a71-9e5d-a7df1da2a8f8"**
4. **Marketing agents search documents in that specific category**
5. **Comprehensive marketing analysis generated with full document context**
6. **Results stored with complete PMO traceability**

This ensures that all the context and source documents that informed the PMO's decision are automatically available to the marketing team for their strategic analysis.
