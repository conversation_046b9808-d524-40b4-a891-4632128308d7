import { NextRequest, NextResponse } from "next/server";
// Import server-side only
import { seoAnalyzer } from "lib/seo-analyzer";
import { socialMediaService } from "lib/social-media";
import { analyticsService } from "lib/analytics";
import { campaignManager } from "lib/campaign-manager";
import { vectorSearchClient } from "lib/vector-search";

// Import our tools
import { LlmTool, LlmProvider } from 'lib/tools/llm-tool';
import { internetSearchTool } from 'lib/tools/internet-search';
import { generateImageTool, ImageModel, ImageSize } from 'lib/tools/generate-image';
import { chartTool, ChartType } from 'lib/tools/chart-tool';
import { dashboardTool, DashboardLayout } from 'lib/tools/dashboard-tool';
import { visionTool, VisionModel, VisionTask } from 'lib/tools/vision-tool';

// Define interfaces for request parameters
interface RequestData {
  tool?: string;
  action?: string;
  params?: any;
}

interface LlmToolParams {
  prompt: string;
  context?: string;
  model?: string;
  provider?: LlmProvider;
}

interface SearchToolParams {
  query: string;
  numResults?: number;
}

interface ImageToolParams {
  prompt: string;
  refinePrompt?: boolean;
  model?: ImageModel;
  size?: ImageSize;
  style?: 'vivid' | 'natural';
  quality?: 'low' | 'medium' | 'high' | 'auto' | 'standard' | 'hd';
  format?: 'png' | 'jpeg' | 'webp';
  background?: 'transparent' | 'opaque' | 'auto';
  compression?: number; // 0-100%
  userId?: string; // Added userId for Firebase integration
}

interface ChartToolParams {
  prompt: string;
  chartType?: ChartType;
  model?: string;
  provider?: LlmProvider;
}

interface DashboardToolParams {
  prompt: string;
  layout?: DashboardLayout;
  model?: string;
  provider?: LlmProvider;
}

interface VisionToolParams {
  imageUrl: string;
  prompt?: string;
  model?: VisionModel;
  task?: VisionTask;
  userId?: string;
}

// Initialize tools
const llmTool = new LlmTool();

// This file runs on the server side only

/**
 * API route for accessing the AI marketing tools
 */
export async function POST(request: NextRequest) {
  try {
    const requestData: RequestData = await request.json();
    const { tool, action, params } = requestData;

    // Handle direct tool calls from the tools test page
    if (tool && !action && requestData.params) {
      console.log('Direct tool call detected:', tool);
      // Direct tool calls from the tools test page
      if (tool === 'llm') {
        return await processLlmTool(requestData.params);
      } else if (tool === 'search') {
        return await processSearchTool(requestData.params);
      } else if (tool === 'image') {
        return await processImageTool(requestData.params);
      } else if (tool === 'chart') {
        return await processChartTool(requestData.params);
      } else if (tool === 'dashboard') {
        return await processDashboardTool(requestData.params);
      } else if (tool === 'vision') {
        return await processVisionTool(requestData.params);
      }
    }

    // Legacy format requires both tool and action
    if (!tool || !action) {
      return NextResponse.json({
        error: "Tool and action are required"
      }, { status: 400 });
    }

    let result;

    // Handle our new tools
    if (tool === 'llm') {
      return await processLlmTool(params);
    } else if (tool === 'search') {
      return await processSearchTool(params);
    } else if (tool === 'image') {
      return await processImageTool(params);
    } else if (tool === 'chart') {
      return await processChartTool(params);
    } else if (tool === 'dashboard') {
      return await processDashboardTool(params);
    } else if (tool === 'vision') {
      return await processVisionTool(params);
    }

    // SEO Analyzer
    else if (tool === "seo") {
      switch (action) {
        case "analyzeUrl":
          if (!params?.url) {
            return NextResponse.json({ error: "URL is required" }, { status: 400 });
          }
          result = await seoAnalyzer.analyzeUrl(params.url);
          break;

        case "analyzeContent":
          if (!params?.content) {
            return NextResponse.json({ error: "Content is required" }, { status: 400 });
          }
          result = await seoAnalyzer.analyzeContent(params.content, params.targetKeywords);
          break;

        default:
          return NextResponse.json({ error: "Invalid SEO action" }, { status: 400 });
      }
    }

    // Social Media
    else if (tool === "socialMedia") {
      switch (action) {
        case "generatePosts":
          if (!params?.content) {
            return NextResponse.json({ error: "Content is required" }, { status: 400 });
          }
          result = await socialMediaService.generatePosts(
            params.content,
            params.platforms || ["twitter", "linkedin", "facebook"],
            params.options || {}
          );
          break;

        case "generateContentCalendar":
          if (!params?.content) {
            return NextResponse.json({ error: "Content is required" }, { status: 400 });
          }
          result = await socialMediaService.generateContentCalendar(
            params.content,
            params.platforms || ["twitter", "linkedin", "facebook"],
            params.days || 7,
            params.options || {}
          );
          break;

        case "savePost":
          if (!params?.post || !params?.userId) {
            return NextResponse.json({ error: "Post and userId are required" }, { status: 400 });
          }
          result = await socialMediaService.savePost(
            params.post,
            params.userId,
            params.documentId
          );
          break;

        case "getUserPosts":
          if (!params?.userId) {
            return NextResponse.json({ error: "UserId is required" }, { status: 400 });
          }
          result = await socialMediaService.getUserPosts(
            params.userId,
            params.status,
            params.limit
          );
          break;

        default:
          return NextResponse.json({ error: "Invalid social media action" }, { status: 400 });
      }
    }

    // Analytics
    else if (tool === "analytics") {
      switch (action) {
        case "trackContentView":
          if (!params?.documentId) {
            return NextResponse.json({ error: "DocumentId is required" }, { status: 400 });
          }
          result = await analyticsService.trackContentView(
            params.documentId,
            params.userId || "anonymous",
            params.metadata || {}
          );
          break;

        case "trackContentGeneration":
          if (!params?.documentId) {
            return NextResponse.json({ error: "DocumentId is required" }, { status: 400 });
          }
          result = await analyticsService.trackContentGeneration(
            params.documentId,
            params.userId || "anonymous",
            params.metadata || {}
          );
          break;

        case "getContentPerformanceMetrics":
          result = await analyticsService.getContentPerformanceMetrics(
            params?.documentId,
            params?.userId,
            params?.days || 30
          );
          break;

        case "getUserActivityMetrics":
          result = await analyticsService.getUserActivityMetrics(
            params?.days || 30,
            params?.userLimit || 100
          );
          break;

        default:
          return NextResponse.json({ error: "Invalid analytics action" }, { status: 400 });
      }
    }

    // Campaign Manager
    else if (tool === "campaign") {
      switch (action) {
        case "createCampaign":
          if (!params?.campaignData || !params?.userId) {
            return NextResponse.json({ error: "Campaign data and userId are required" }, { status: 400 });
          }
          result = await campaignManager.createCampaign(
            params.campaignData,
            params.userId
          );
          break;

        case "getCampaign":
          if (!params?.campaignId) {
            return NextResponse.json({ error: "CampaignId is required" }, { status: 400 });
          }
          result = await campaignManager.getCampaign(params.campaignId);
          break;

        case "updateCampaign":
          if (!params?.campaignId || !params?.campaignData) {
            return NextResponse.json({ error: "CampaignId and campaign data are required" }, { status: 400 });
          }
          result = await campaignManager.updateCampaign(
            params.campaignId,
            params.campaignData
          );
          break;

        case "deleteCampaign":
          if (!params?.campaignId) {
            return NextResponse.json({ error: "CampaignId is required" }, { status: 400 });
          }
          result = await campaignManager.deleteCampaign(params.campaignId);
          break;

        case "getUserCampaigns":
          if (!params?.userId) {
            return NextResponse.json({ error: "UserId is required" }, { status: 400 });
          }
          result = await campaignManager.getUserCampaigns(
            params.userId,
            params.maxLimit || 10
          );
          break;

        case "addContentToCampaign":
          if (!params?.campaignId || !params?.documentId) {
            return NextResponse.json({ error: "CampaignId and documentId are required" }, { status: 400 });
          }
          result = await campaignManager.addContentToCampaign(
            params.campaignId,
            params.documentId
          );
          break;

        case "generateCampaignContent":
          if (!params?.campaignId) {
            return NextResponse.json({ error: "CampaignId is required" }, { status: 400 });
          }
          result = await campaignManager.generateCampaignContent(
            params.campaignId,
            params.options || {}
          );
          break;

        case "getCampaignAnalytics":
          if (!params?.campaignId) {
            return NextResponse.json({ error: "CampaignId is required" }, { status: 400 });
          }
          result = await campaignManager.getCampaignAnalytics(
            params.campaignId,
            params.days || 30
          );
          break;

        default:
          return NextResponse.json({ error: "Invalid campaign action" }, { status: 400 });
      }
    }

    // Vector Search
    else if (tool === "vectorSearch") {
      switch (action) {
        case "search":
          if (!params?.query) {
            return NextResponse.json({ error: "Query is required" }, { status: 400 });
          }
          result = await vectorSearchClient.search(
            params.query,
            params.namespace,
            params.topK || 5
          );
          break;

        case "storeEmbedding":
          if (!params?.content || !params?.id) {
            return NextResponse.json({ error: "Content and ID are required" }, { status: 400 });
          }
          result = await vectorSearchClient.storeEmbedding(
            params.content,
            params.id,
            params.metadata || {},
            params.namespace
          );
          break;

        case "deleteEmbeddings":
          if (!params?.ids || !Array.isArray(params.ids)) {
            return NextResponse.json({ error: "Array of IDs is required" }, { status: 400 });
          }
          result = await vectorSearchClient.deleteEmbeddings(
            params.ids,
            params.namespace
          );
          break;

        default:
          return NextResponse.json({ error: "Invalid vector search action" }, { status: 400 });
      }
    }

    // Invalid tool
    else {
      return NextResponse.json({ error: "Invalid tool" }, { status: 400 });
    }

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Tool processing error:', error);
    return NextResponse.json({ error: error.message || 'Failed to process tool request' }, { status: 500 });
  }
}

/**
 * Process a request for the LLM tool
 * @param {LlmToolParams} params - Parameters for the LLM tool
 * @returns {Promise<NextResponse>} - The result of the LLM processing
 */
async function processLlmTool(params: LlmToolParams): Promise<NextResponse> {
  const { prompt, context, model = 'gpt-4o', provider = 'openai' } = params;

  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required for LLM tool' }, { status: 400 });
  }

  try {
    // Process with the generic processContent method
    const content = await llmTool.processContent({
      prompt,
      context,
      model,
      provider
    });

    return NextResponse.json({ content });
  } catch (error: any) {
    console.error('LLM processing error:', error);
    return NextResponse.json({ error: error.message || 'Failed to process with LLM' }, { status: 500 });
  }
}

/**
 * Process a request for the Internet Search tool
 * @param {SearchToolParams} params - Parameters for the search tool
 * @returns {Promise<NextResponse>} - The search results
 */
async function processSearchTool(params: SearchToolParams): Promise<NextResponse> {
  const { query, numResults = 5 } = params;

  if (!query) {
    return NextResponse.json({ error: 'Query is required for Internet Search tool' }, { status: 400 });
  }

  try {
    // Perform the search
    const searchOptions = {
      numResults: Math.min(numResults, 10)
    };

    const result = await internetSearchTool.search(query, searchOptions);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Internet search error:', error);
    return NextResponse.json({ error: error.message || 'Failed to perform internet search' }, { status: 500 });
  }
}

/**
 * Process a request for the Image Generation tool
 * @param {ImageToolParams} params - Parameters for the image generation tool
 * @returns {Promise<NextResponse>} - The generated image result
 */
async function processImageTool(params: ImageToolParams): Promise<NextResponse> {
  const {
    prompt,
    refinePrompt = true,
    model = 'dall-e-3',
    size = '1024x1024',
    style = 'vivid',
    quality = 'auto',
    format = 'jpeg',
    background = 'auto',
    compression,
    userId
  } = params;

  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required for Image Generation tool' }, { status: 400 });
  }

  try {
    console.log(`[processImageTool] Received model parameter: ${model}`);
    console.log(`[processImageTool] Using model: ${model}`);
    console.log(`[processImageTool] Using size: ${size}`);
    console.log(`[processImageTool] Using style: ${style}`);
    console.log(`[processImageTool] Using quality: ${quality}`);
    console.log(`[processImageTool] Using format: ${format}`);

    // If userId is provided, use Firebase integration
    if (userId) {
      console.log(`Using Firebase integration for user: ${userId}`);

      try {
        // Generate the image with Firebase integration
        const result = await generateImageTool.generateImage({
          prompt,
          refinePrompt,
          model,
          size,
          style,
          quality,
          format,
          background,
          compression,
          userId
        });

        // Return the result immediately to the client
        return NextResponse.json(result);
      } catch (firebaseError: any) {
        console.error('Firebase image generation error:', firebaseError);
        // Fall back to direct generation if Firebase integration fails
        console.log('Falling back to direct image generation');
      }
    }

    // Generate the image directly without Firebase
    const result = await generateImageTool.generateImage({
      prompt,
      refinePrompt,
      model,
      size,
      style,
      quality,
      format,
      background,
      compression
    });

    // Return the result immediately to the client
    // The image will be saved to the gallery when the user clicks the "Save to Gallery" button
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Image generation error:', error);
    return NextResponse.json({ error: error.message || 'Failed to generate image' }, { status: 500 });
  }
}

/**
 * Process a request for the Chart Generation tool
 * @param {ChartToolParams} params - Parameters for the chart generation tool
 * @returns {Promise<NextResponse>} - The generated chart configuration
 */
async function processChartTool(params: ChartToolParams): Promise<NextResponse> {
  const { prompt, chartType, model = 'gpt-4o', provider = 'openai' } = params;

  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required for Chart Generation tool' }, { status: 400 });
  }

  try {
    // Generate the chart configuration
    const result = await chartTool.generateChart({
      prompt,
      chartType,
      model,
      provider
    });
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Chart generation error:', error);
    return NextResponse.json({ error: error.message || 'Failed to generate chart' }, { status: 500 });
  }
}

/**
 * Process a request for the Dashboard Generation tool
 * @param {DashboardToolParams} params - Parameters for the dashboard generation tool
 * @returns {Promise<NextResponse>} - The generated dashboard configuration
 */
async function processDashboardTool(params: DashboardToolParams): Promise<NextResponse> {
  const { prompt, layout, model = 'gpt-4o', provider = 'openai' } = params;

  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required for Dashboard Generation tool' }, { status: 400 });
  }

  try {
    // Generate the dashboard configuration
    const result = await dashboardTool.generateDashboard({
      prompt,
      layout,
      model,
      provider
    });
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Dashboard generation error:', error);
    return NextResponse.json({ error: error.message || 'Failed to generate dashboard' }, { status: 500 });
  }
}

/**
 * Process a request for the Vision tool
 * @param {VisionToolParams} params - Parameters for the vision tool
 * @returns {Promise<NextResponse>} - The vision processing result
 */
async function processVisionTool(params: VisionToolParams): Promise<NextResponse> {
  const { imageUrl, prompt, model = 'gpt-4o', task = 'describe', userId } = params;

  if (!imageUrl) {
    return NextResponse.json({ error: 'Image URL is required for Vision tool' }, { status: 400 });
  }

  try {
    console.log(`[processVisionTool] Processing image: ${imageUrl.substring(0, 50)}...`);
    console.log(`[processVisionTool] Using model: ${model}`);
    console.log(`[processVisionTool] Task: ${task}`);

    // Get the user's email from the session if available
    let userEmail: string | undefined;
    try {
      const { getServerSession } = await import('next-auth');
      const { authOptions } = await import('@/api/auth/[...nextauth]/authOptions');
      const session = await getServerSession(authOptions);
      // Handle potential null value from session?.user?.email
      userEmail = session?.user?.email || undefined;
    } catch (error) {
      console.warn('Failed to get user email from session:', error);
    }

    // Process the image
    const result = await visionTool.processImage({
      imageUrl,
      prompt,
      model,
      task,
      userEmail,
      userId
    });

    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Vision processing error:', error);
    return NextResponse.json({ error: error.message || 'Failed to process image' }, { status: 500 });
  }
}
