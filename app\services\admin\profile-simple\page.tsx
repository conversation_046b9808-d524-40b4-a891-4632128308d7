'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import UserProfileStats from '../../../components/UserProfileStats';
import FirebaseDiagnostic from '../../../components/FirebaseDiagnostic';
import { collection, getDocs, query, where, doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../../lib/firebase/config';

export default function SimpleProfilePage() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [showDebug, setShowDebug] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/services/admin/login');
    return null;
  }

  // Fetch categories directly in the component
  useEffect(() => {
    const fetchCategories = async () => {
      if (!session?.user?.email) return;

      setLoadingCategories(true);

      try {
        const userEmail = session.user.email;
        const filesCollection = collection(db, 'users', userEmail, 'files');
        const filesSnapshot = await getDocs(filesCollection);

        // Extract unique categories
        const categoriesSet = new Set<string>();
        filesSnapshot.forEach(doc => {
          const fileData = doc.data();
          const category = fileData.category;
          if (category && category !== 'Unknown') {
            categoriesSet.add(category);
          }
        });

        setCategories(Array.from(categoriesSet));
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, [session]);

  // Sync profile image directly
  useEffect(() => {
    const syncProfileImage = async () => {
      if (!session?.user?.email || !session?.user?.image) return;

      try {
        const userEmail = session.user.email;
        const userImage = session.user.image;
        const userName = session.user.name;
        const userDocRef = doc(db, 'users', userEmail);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          const userData = userDoc.data();

          // Check if profile image needs updating
          if (userImage && userData.avatar !== userImage) {
            console.log('Updating user avatar with Google profile image');

            await updateDoc(userDocRef, {
              avatar: userImage,
              updatedAt: serverTimestamp()
            });

            console.log('Profile image updated successfully');
          }
        } else {
          // Create new user document
          console.log('Creating new user document');
          // Use setDoc instead of updateDoc for new documents
          const { setDoc } = await import('firebase/firestore');
          await setDoc(userDocRef, {
            name: userName || userEmail.split('@')[0],
            email: userEmail,
            avatar: userImage,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          });
        }
      } catch (error) {
        console.error('Error syncing profile image:', error);
      }
    };

    syncProfileImage();
  }, [session]);

  return (
    <div className="min-h-screen bg-ike-purple text-gray-100">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-row justify-between items-center mb-6">
          <div className="flex flex-row items-center gap-4">
            <Image
              src="/favicon.png"
              alt="Company Logo"
              width={60}
              height={60}
              className="hidden sm:block"
              style={{ width: 'auto', height: '60px' }}
            />
            <div>
              <h1 className="text-2xl font-bold text-white">User Profile (Simple)</h1>
              <p className="text-gray-400 mt-1">Direct Firestore access example</p>
            </div>
          </div>
          <button
            onClick={() => setShowDebug(!showDebug)}
            className="px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded-md text-sm transition-colors"
          >
            {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
          </button>
        </div>

        {/* Debug panel */}
        {showDebug && (
          <div className="mb-6">
            <FirebaseDiagnostic />
          </div>
        )}

        {/* Main content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Profile stats */}
          <div>
            <UserProfileStats />
          </div>

          {/* User data */}
          <div className="md:col-span-2">
            <div className="bg-gray-800 rounded-lg shadow-md p-4">
              <h2 className="text-lg font-semibold text-white mb-4">Your Data</h2>

              {/* Categories */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-300 mb-2">Your Categories</h3>
                {loadingCategories ? (
                  <p className="text-gray-500 text-sm">Loading categories...</p>
                ) : categories.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {categories.map((category) => (
                      <span
                        key={category}
                        className="px-3 py-1 bg-purple-900/50 text-purple-300 rounded-full text-sm"
                      >
                        {category}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No categories found</p>
                )}
              </div>

              {/* Session info (debug only) */}
              {showDebug && session && (
                <div className="mt-6 pt-4 border-t border-gray-700">
                  <h3 className="text-sm font-medium text-gray-400 mb-2">Session Data</h3>
                  <pre className="bg-gray-900 p-3 rounded-md text-xs font-mono text-gray-400 overflow-x-auto">
                    {JSON.stringify(session, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
