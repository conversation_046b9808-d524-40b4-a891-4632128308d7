'use client';

import React, { useState, useEffect, useRef } from 'react';
import { X, FileText, CheckCircle, AlertCircle, Loader2, Download } from 'lucide-react';

interface CodebaseDocumentationModalProps {
  isOpen: boolean;
  onClose: () => void;
  pmoRecord: any;
  onComplete?: (result: any) => void;
}

interface StreamUpdate {
  type: string;
  message: string;
  data?: any;
  timestamp?: string;
}

export default function CodebaseDocumentationModal({
  isOpen,
  onClose,
  pmoRecord,
  onComplete
}: CodebaseDocumentationModalProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [streamUpdates, setStreamUpdates] = useState<StreamUpdate[]>([]);
  const [currentStatus, setCurrentStatus] = useState('Initializing...');
  const [isComplete, setIsComplete] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<any>(null);
  const streamRef = useRef<EventSource | null>(null);
  const updatesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    updatesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [streamUpdates]);

  const startDocumentationGeneration = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    setStreamUpdates([]);
    setCurrentStatus('Starting codebase documentation generation...');
    setError(null);
    setIsComplete(false);
    setResult(null);

    try {
      // Extract PMO Assessment content
      const pmoAssessment = pmoRecord.assessment || pmoRecord.projectDescription || '';

      // Ensure description meets minimum length requirement (10 characters)
      const description = pmoAssessment.length >= 10
        ? pmoAssessment
        : `Please analyze the entire codebase of this application and create a comprehensive product overview. ${pmoAssessment}`.trim();

      // Prepare the request (only include fields expected by the schema)
      const documentationRequest = {
        userId: pmoRecord.createdBy, // Use createdBy field which contains the user's email
        selectedPaths: extractSelectedPathsFromAssessment(pmoAssessment, pmoRecord.projectDescription || ''),
        description: description,
        customContext: `PMO Assessment Context:\n${pmoAssessment}\n\nTeam Selection Rationale:\n${pmoRecord.teamSelectionRationale || ''}\n\nPriority: ${pmoRecord.priority || 'medium'}\nCategory: ${pmoRecord.category || 'general'}\n\nPMO Record ID: ${pmoRecord.id}`,
        category: pmoRecord.title || pmoRecord.category || 'PMO Documentation', // Add the missing category field
        documentationScope: 'full' as const,
        outputFormat: 'markdown' as const,
        includeArchitecture: true,
        includeApiDocs: true,
        includeDataFlow: true
      };

      addStreamUpdate('info', `Extracted paths: ${documentationRequest.selectedPaths.join(', ')}`);
      addStreamUpdate('info', 'Calling codebase documentation API...');

      // Debug: Log the request being sent
      console.log('[PMO Modal] PMO Record title:', pmoRecord.title);
      console.log('[PMO Modal] PMO Record category:', pmoRecord.category);
      console.log('[PMO Modal] Final category being sent:', documentationRequest.category);
      console.log('[PMO Modal] Sending documentation request:', JSON.stringify(documentationRequest, null, 2));

      // Call the streaming API
      const response = await fetch('/api/codebase-documentation/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(documentationRequest)
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('[PMO Modal] API error response:', errorData);
        throw new Error(`API error: ${response.status} ${response.statusText} - ${errorData}`);
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No response body reader available');
      }

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          setCurrentStatus('Documentation generation completed!');
          setIsComplete(true);
          break;
        }

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              handleStreamUpdate(data);
            } catch (parseError) {
              console.warn('Failed to parse stream data:', line);
            }
          }
        }
      }

    } catch (error: any) {
      console.error('Documentation generation error:', error);
      setError(error.message || 'Failed to generate documentation');
      addStreamUpdate('error', `Error: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStreamUpdate = (data: any) => {
    if (data.type === 'status') {
      setCurrentStatus(data.message);
      addStreamUpdate('status', data.message);
    } else if (data.type === 'progress') {
      addStreamUpdate('progress', data.message, data);
    } else if (data.type === 'result') {
      setResult(data.data);
      addStreamUpdate('success', 'Documentation generation completed successfully!');
      if (onComplete) {
        onComplete(data.data);
      }
    } else if (data.type === 'error') {
      setError(data.message);
      addStreamUpdate('error', data.message);
    } else {
      addStreamUpdate('info', data.message || JSON.stringify(data));
    }
  };

  const addStreamUpdate = (type: string, message: string, data?: any) => {
    const update: StreamUpdate = {
      type,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setStreamUpdates(prev => [...prev, update]);
  };

  const extractSelectedPathsFromAssessment = (pmoAssessment: string, projectDescription: string): string[] => {
    const content = `${pmoAssessment || ''} ${projectDescription || ''}`;

    console.log(`[PMO Modal Path Extraction] Processing content length: ${content.length}`);
    console.log(`[PMO Modal Path Extraction] Content preview: ${content.substring(0, 500)}...`);

    // First, look for explicit Windows/Unix paths mentioned in the content
    const explicitPathPatterns = [
      // Windows paths with spaces - more comprehensive pattern
      /([A-Za-z]:\\(?:[^"'\n\r<>|*?]+(?:\s+[^"'\n\r<>|*?]+)*)+)/g,
      // Unix/Linux paths (/path/to/folder)
      /(\/[^"'\n\r<>|*?\s]+)/g,
      // Relative paths (./path or ../path)
      /(\.[\/\\][^"'\n\r<>|*?\s]+)/g
    ];

    const extractedPaths = new Set<string>();

    // Extract explicit paths first
    for (const pattern of explicitPathPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        if (match[1]) {
          let path = match[1].trim();
          // Clean up the path
          path = path.replace(/['"`,;:()]/g, '');
          if (path.length > 3 && path.length < 200) {
            extractedPaths.add(path);
            console.log(`[PMO Modal Path Extraction] Found explicit path: ${path}`);
          }
        }
      }
    }

    // If explicit paths found, use them
    if (extractedPaths.size > 0) {
      const paths = Array.from(extractedPaths);
      console.log(`[PMO Modal Path Extraction] Using explicit paths: ${paths.join(', ')}`);
      return paths;
    }

    // Fallback: Look for common project directory patterns in lowercase content
    const contentLower = content.toLowerCase();
    const commonPatterns = [
      // Common project directories
      /(src|lib|components|pages|app|api|utils|helpers|services|models|controllers|views|public|assets|styles|tests|test|spec|docs|documentation)/gi
    ];

    for (const pattern of commonPatterns) {
      let match;
      while ((match = pattern.exec(contentLower)) !== null) {
        if (match[1]) {
          extractedPaths.add(match[1]);
        }
      }
    }

    // Default fallback paths if no specific paths found
    const defaultPaths = ['src', 'lib', 'components', 'app', 'api'];

    // Convert to array and filter
    let paths = Array.from(extractedPaths).filter(path => {
      // Filter out obviously invalid paths
      return path.length > 1;
    });

    // If no valid paths found, use intelligent defaults based on content
    if (paths.length === 0) {
      if (contentLower.includes('react') || contentLower.includes('component') || contentLower.includes('jsx')) {
        paths = ['src', 'components', 'pages'];
      } else if (contentLower.includes('api') || contentLower.includes('backend') || contentLower.includes('server')) {
        paths = ['src', 'api', 'lib'];
      } else if (contentLower.includes('frontend') || contentLower.includes('ui') || contentLower.includes('interface')) {
        paths = ['src', 'components', 'styles'];
      } else {
        paths = defaultPaths;
      }
    }

    // Limit to reasonable number of paths
    if (paths.length > 10) {
      paths = paths.slice(0, 10);
    }

    return paths;
  };

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      case 'progress':
      case 'status':
        return <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />;
      default:
        return <FileText className="w-4 h-4 text-gray-400" />;
    }
  };

  const getUpdateColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'text-green-300';
      case 'error':
        return 'text-red-300';
      case 'progress':
      case 'status':
        return 'text-blue-300';
      default:
        return 'text-gray-300';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg border border-gray-700 w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-white">Codebase Documentation Generation</h2>
            <p className="text-sm text-gray-400 mt-1">
              PMO Record: {pmoRecord.projectTitle || pmoRecord.id}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isProcessing}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Current Status */}
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center space-x-3">
              {isProcessing ? (
                <Loader2 className="w-5 h-5 text-blue-400 animate-spin" />
              ) : isComplete ? (
                <CheckCircle className="w-5 h-5 text-green-400" />
              ) : error ? (
                <AlertCircle className="w-5 h-5 text-red-400" />
              ) : (
                <FileText className="w-5 h-5 text-gray-400" />
              )}
              <span className="text-white font-medium">{currentStatus}</span>
            </div>
          </div>

          {/* Stream Updates */}
          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            {streamUpdates.map((update, index) => (
              <div key={index} className="flex items-start space-x-3 text-sm">
                <span className="text-xs text-gray-500 w-16 flex-shrink-0">
                  {update.timestamp}
                </span>
                {getUpdateIcon(update.type)}
                <span className={getUpdateColor(update.type)}>
                  {update.message}
                </span>
              </div>
            ))}
            <div ref={updatesEndRef} />
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700 flex justify-between items-center">
          <div className="text-sm text-gray-400">
            {isComplete && result && (
              <span>Documentation saved to PMO Output</span>
            )}
          </div>
          <div className="flex space-x-3">
            {!isProcessing && !isComplete && (
              <button
                onClick={startDocumentationGeneration}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <FileText className="w-4 h-4" />
                <span>Start Documentation</span>
              </button>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              disabled={isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Close'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
