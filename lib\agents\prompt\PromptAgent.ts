/**
 * Prompt Agent
 *
 * This agent takes an original prompt, passes it to the promptInterpreterTool to get criteria,
 * then passes the criteria and original prompt to the promptOptimizerTool to get an optimized prompt.
 */

import { promptInterpreterTool, PromptInterpreterResult } from '../../tools/promptInterpreterTool';
import { promptOptimizerTool, PromptOptimizerResult } from '../../tools/promptOptimizerTool';

export interface PromptAgentOptions {
  temperature?: number;
  maxTokens?: number;
  includeExplanation?: boolean;
}

export interface PromptAgentResult {
  success: boolean;
  originalPrompt: string;
  criteria: string[];
  optimizedPrompt: string;
  explanation?: string;
  error?: string;
}

export class PromptAgent {
  private options: PromptAgentOptions;

  constructor(options: PromptAgentOptions = {}) {
    this.options = {
      temperature: options.temperature ?? 0.4,
      maxTokens: options.maxTokens ?? 3000,
      includeExplanation: options.includeExplanation ?? false
    };
  }

  /**
   * Optimize a prompt by first interpreting it to get criteria, then applying those criteria
   * @param prompt - The original prompt to optimize
   * @returns - The optimization result including criteria and optimized prompt
   */
  async optimizePrompt(prompt: string): Promise<PromptAgentResult> {
    try {
      if (!prompt) {
        throw new Error("Prompt is required");
      }

      console.log(`PromptAgent: Processing prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`);

      // Step 1: Interpret the prompt to get criteria
      console.log("PromptAgent: Interpreting prompt to generate criteria...");
      const interpretResult = await this._interpretPrompt(prompt);

      console.log("PromptAgent: Interpret result:", interpretResult);

      if (!interpretResult.success) {
        throw new Error(`Failed to interpret prompt: ${interpretResult.error}`);
      }

      // Verify we have criteria
      if (!interpretResult.criteria || !Array.isArray(interpretResult.criteria) || interpretResult.criteria.length === 0) {
        throw new Error("At least one criterion is required");
      }

      console.log(`PromptAgent: Generated ${interpretResult.criteria.length} criteria:`, interpretResult.criteria);

      // Step 2: Optimize the prompt using the criteria
      console.log("PromptAgent: Optimizing prompt using the criteria...");
      const optimizeResult = await this._optimizePrompt(prompt, interpretResult.criteria);

      console.log("PromptAgent: Optimize result:", optimizeResult);

      if (!optimizeResult.success) {
        throw new Error(`Failed to optimize prompt: ${optimizeResult.error}`);
      }

      // Verify we have an optimized prompt
      if (!optimizeResult.optimizedPrompt) {
        throw new Error("No optimized prompt was generated");
      }

      // Additional validation to ensure the optimized prompt doesn't contain meta-instructions
      let cleanedPrompt = optimizeResult.optimizedPrompt;

      // Check for and remove common meta-instruction patterns
      const metaInstructionPatterns = [
        /\*\*.*\*\*/g, // **note**
        /NOTE:.*(\n|$)/gi, // NOTE: something
        /IMPORTANT:.*(\n|$)/gi, // IMPORTANT: something
        /ASSUMPTION:.*(\n|$)/gi, // ASSUMPTION: something
        /CONTEXT:.*(\n|$)/gi, // CONTEXT: something
        /BACKGROUND:.*(\n|$)/gi, // BACKGROUND: something
        /\[NOTE\].*(\n|$)/gi, // [NOTE] something
        /\[IMPORTANT\].*(\n|$)/gi, // [IMPORTANT] something
        /\[CONTEXT\].*(\n|$)/gi, // [CONTEXT] something
        /\[BACKGROUND\].*(\n|$)/gi // [BACKGROUND] something
      ];

      // Apply each pattern to remove meta-instructions
      metaInstructionPatterns.forEach(pattern => {
        cleanedPrompt = cleanedPrompt.replace(pattern, '');
      });

      // Remove any lines that start with numbered team lists
      const lines = cleanedPrompt.split('\n');
      const filteredLines = lines.filter(line => {
        const trimmedLine = line.trim();
        return !(/^\d+\.\s+(Marketing|Research|Software|Sales|Business)/.test(trimmedLine));
      });

      // Rejoin the filtered lines and trim any extra whitespace
      cleanedPrompt = filteredLines.join('\n').trim();

      // If the cleaning process removed too much content, log a warning but keep the original
      if (cleanedPrompt.length < 20 && optimizeResult.optimizedPrompt.length > 20) {
        console.warn("PromptAgent: Cleaning process removed too much content. Using original optimized prompt.");
        cleanedPrompt = optimizeResult.optimizedPrompt;
      }

      console.log("PromptAgent: Successfully optimized prompt");
      console.log("PromptAgent: Original optimized prompt length:", optimizeResult.optimizedPrompt.length);
      console.log("PromptAgent: Cleaned optimized prompt length:", cleanedPrompt.length);

      return {
        success: true,
        originalPrompt: prompt,
        criteria: interpretResult.criteria,
        optimizedPrompt: cleanedPrompt,
        explanation: optimizeResult.explanation
      };
    } catch (error: any) {
      console.error("PromptAgent error:", error);
      return {
        success: false,
        originalPrompt: prompt,
        criteria: [],
        optimizedPrompt: "",
        error: error.message || "Unknown error occurred in PromptAgent"
      };
    }
  }

  /**
   * Interpret a prompt to get optimization criteria
   * @private
   * @param prompt - The prompt to interpret
   * @returns - The interpretation result with criteria
   */
  private async _interpretPrompt(prompt: string): Promise<PromptInterpreterResult> {
    return promptInterpreterTool.interpretPrompt({
      prompt,
      modelOptions: {
        temperature: this.options.temperature,
        maxTokens: this.options.maxTokens
      }
    });
  }

  /**
   * Optimize a prompt using criteria
   * @private
   * @param originalPrompt - The original prompt
   * @param criteria - The criteria to apply
   * @returns - The optimization result with optimized prompt
   */
  private async _optimizePrompt(originalPrompt: string, criteria: string[]): Promise<PromptOptimizerResult> {
    return promptOptimizerTool.optimizePrompt({
      originalPrompt,
      criteria,
      includeExplanation: this.options.includeExplanation,
      modelOptions: {
        temperature: this.options.temperature,
        maxTokens: this.options.maxTokens
      }
    });
  }
}

// Export a singleton instance
export const promptAgent = new PromptAgent();
