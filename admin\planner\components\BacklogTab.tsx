'use client';

import React, { useState, useEffect, useRef } from 'react';
import { usePlanner } from '../../../app/services/context/PlannerContext';
import { BacklogItem, User, TaskPriority, BacklogStatus } from '../types';
import { PlusCircle, ThumbsUp, Check, X, ArrowRight, MessageSquare, AlertTriangle, RefreshCw } from 'lucide-react';
import { useSession } from 'next-auth/react';
import BacklogItemForm from './BacklogItemForm';
import BacklogItemDetails from './BacklogItemDetails';

interface BacklogTabProps {
  projectId: string;
  users: User[];
}

const BacklogTab: React.FC<BacklogTabProps> = ({ projectId, users }) => {
  const { data: session } = useSession();
  const {
    backlogItems,
    fetchBacklogItems,
    voteForBacklogItem,
    approveBacklogItem,
    rejectBacklogItem,
    convertToTask,
    loading,
    error
  } = usePlanner();

  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedItem, setSelectedItem] = useState<BacklogItem | null>(null);
  const [filter, setFilter] = useState<'all' | 'proposed' | 'approved' | 'rejected'>('all');
  const [sortBy, setSortBy] = useState<'votes' | 'priority' | 'date'>('votes');
  const [localLoading, setLocalLoading] = useState(true);
  const [localError, setLocalError] = useState<string | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if current user is admin
  const isAdmin = session?.user?.email === '<EMAIL>';

  // Function to load backlog items with error handling and timeout
  const loadBacklogItems = async () => {
    setLocalLoading(true);
    setLocalError(null);

    try {
      // Set a timeout to prevent infinite loading state
      loadingTimeoutRef.current = setTimeout(() => {
        setLocalLoading(false);
        setLocalError('Loading backlog items timed out. Please try refreshing.');
      }, 10000); // 10 second timeout

      await fetchBacklogItems(projectId);
    } catch (err) {
      console.error('Error in BacklogTab loading backlog items:', err);
      setLocalError('Failed to load backlog items. Please try again.');
    } finally {
      // Clear the timeout if the operation completes
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      setLocalLoading(false);
    }
  };

  useEffect(() => {
    loadBacklogItems();

    // Cleanup function to clear timeout if component unmounts
    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [projectId]);

  const projectBacklogItems = backlogItems[projectId] || [];

  // Filter and sort backlog items
  const filteredItems = projectBacklogItems.filter(item => {
    if (filter === 'all') return true;
    return item.status.toLowerCase() === filter;
  });

  const sortedItems = [...filteredItems].sort((a, b) => {
    if (sortBy === 'votes') return b.votes - a.votes;
    if (sortBy === 'priority') {
      const priorityOrder = { 'Critical': 0, 'High': 1, 'Medium': 2, 'Low': 3 };
      return priorityOrder[a.priority as TaskPriority] - priorityOrder[b.priority as TaskPriority];
    }
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  const handleVote = async (itemId: string) => {
    await voteForBacklogItem(projectId, itemId);
  };

  const handleApprove = async (itemId: string) => {
    await approveBacklogItem(projectId, itemId);
  };

  const handleReject = async (itemId: string) => {
    await rejectBacklogItem(projectId, itemId);
  };

  const handleConvertToTask = async (itemId: string) => {
    await convertToTask(projectId, itemId);
  };

  const getPriorityColor = (priority: TaskPriority): string => {
    switch (priority) {
      case 'Critical': return 'bg-red-900/50 text-red-300';
      case 'High': return 'bg-orange-900/50 text-orange-300';
      case 'Medium': return 'bg-yellow-900/50 text-yellow-300';
      case 'Low': return 'bg-green-900/50 text-green-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  const getStatusColor = (status: BacklogStatus): string => {
    switch (status) {
      case 'Proposed': return 'bg-blue-900/50 text-blue-300';
      case 'Approved': return 'bg-green-900/50 text-green-300';
      case 'Rejected': return 'bg-red-900/50 text-red-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  const handleAddFormSubmit = () => {
    setShowAddForm(false);
    fetchBacklogItems(projectId);
  };

  const handleCloseDetails = () => {
    setSelectedItem(null);
    fetchBacklogItems(projectId);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Product Backlog</h2>
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center gap-2 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
        >
          <PlusCircle size={18} />
          Add Backlog Item
        </button>
      </div>

      <div className="flex gap-4 mb-4">
        <div>
          <label htmlFor="filter" className="block text-sm font-medium text-white">Filter</label>
          <select
            id="filter"
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-700 bg-gray-700 text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
          >
            <option value="all">All Items</option>
            <option value="proposed">Proposed</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
        <div>
          <label htmlFor="sortBy" className="block text-sm font-medium text-white">Sort By</label>
          <select
            id="sortBy"
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border border-gray-700 bg-gray-700 text-white focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
          >
            <option value="votes">Votes</option>
            <option value="priority">Priority</option>
            <option value="date">Date Added</option>
          </select>
        </div>
      </div>

      {localError ? (
        <div className="text-center py-10 bg-red-900/20 rounded-lg">
          <AlertTriangle className="h-12 w-12 text-red-400 mx-auto" />
          <p className="mt-4 text-red-300">{localError}</p>
          <button
            onClick={() => loadBacklogItems()}
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center justify-center mx-auto"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        </div>
      ) : localLoading || loading ? (
        <div className="text-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto"></div>
          <p className="mt-4 text-gray-300">Loading backlog items...</p>
        </div>
      ) : sortedItems.length === 0 ? (
        <div className="text-center py-10 bg-gray-800 rounded-lg">
          <p className="text-gray-300">No backlog items found. Add your first item!</p>
        </div>
      ) : (
        <div className="overflow-hidden shadow ring-1 ring-gray-700 md:rounded-lg">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-800">
              <tr>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Title</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Priority</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Status</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Votes</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Created By</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-white">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700 bg-gray-800">
              {sortedItems.map((item) => (
                <tr key={item.id} className="hover:bg-gray-700 cursor-pointer" onClick={() => setSelectedItem(item)}>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-white">{item.title}</td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getPriorityColor(item.priority)}`}>
                      {item.priority}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm">
                    <span className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusColor(item.status)}`}>
                      {item.status}
                    </span>
                  </td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-white">{item.votes}</td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-white">{item.createdBy}</td>
                  <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-300">
                    <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
                      <button
                        onClick={() => handleVote(item.id)}
                        className="text-blue-400 hover:text-blue-300"
                        title="Vote"
                      >
                        <ThumbsUp size={18} />
                      </button>

                      {isAdmin && item.status === 'Proposed' && (
                        <>
                          <button
                            onClick={() => handleApprove(item.id)}
                            className="text-green-400 hover:text-green-300"
                            title="Approve"
                          >
                            <Check size={18} />
                          </button>
                          <button
                            onClick={() => handleReject(item.id)}
                            className="text-red-400 hover:text-red-300"
                            title="Reject"
                          >
                            <X size={18} />
                          </button>
                        </>
                      )}

                      {isAdmin && item.status === 'Approved' && (
                        <button
                          onClick={() => handleConvertToTask(item.id)}
                          className="text-purple-400 hover:text-purple-300"
                          title="Convert to Task"
                        >
                          <ArrowRight size={18} />
                        </button>
                      )}

                      <button
                        onClick={() => setSelectedItem(item)}
                        className="text-gray-400 hover:text-gray-300"
                        title="View Details"
                      >
                        <MessageSquare size={18} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {showAddForm && (
        <BacklogItemForm
          projectId={projectId}
          onClose={() => setShowAddForm(false)}
          onSubmit={handleAddFormSubmit}
          currentUser={session?.user?.email || ''}
        />
      )}

      {selectedItem && (
        <BacklogItemDetails
          projectId={projectId}
          backlogItem={selectedItem}
          onClose={handleCloseDetails}
          currentUser={session?.user?.email || ''}
          isAdmin={isAdmin}
        />
      )}
    </div>
  );
};

export default BacklogTab;
