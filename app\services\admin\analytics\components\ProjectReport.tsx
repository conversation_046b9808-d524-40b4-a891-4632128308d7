'use client';

import React from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>hart, 
  Line, 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  Calendar, 
  Clock, 
  CheckSquare, 
  AlertTriangle, 
  Users, 
  Tag 
} from 'lucide-react';
import { Project, Task, User, TaskStatus } from '../../../../../admin/planner/types';

// Define color constants
const COLORS = {
  purple: '#9333ea',
  blue: '#3b82f6',
  green: '#22c55e',
  yellow: '#eab308',
  red: '#ef4444',
  indigo: '#6366f1',
  pink: '#ec4899',
  teal: '#14b8a6',
  orange: '#f97316',
  gray: '#6b7280',
};

const STATUS_COLORS = {
  'Not Started': COLORS.gray,
  'In Progress': COLORS.blue,
  'Reviewed': COLORS.yellow,
  'Complete': COLORS.green,
};

// Helper function to format dates
const formatDate = (date: Date | string): string => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
};

// Helper function to calculate days between dates
const daysBetween = (date1: Date, date2: Date): number => {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  const diffDays = Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
  return diffDays;
};

// Helper function to calculate days remaining
const daysRemaining = (endDate: Date): number => {
  const today = new Date();
  return daysBetween(today, endDate);
};

// Helper function to calculate project completion percentage
const calculateProjectCompletion = (tasks: Task[]): number => {
  if (tasks.length === 0) return 0;
  const completedTasks = tasks.filter(task => task.status === 'Complete').length;
  return Math.round((completedTasks / tasks.length) * 100);
};

// Helper function to calculate project health
const calculateProjectHealth = (
  tasks: Task[], 
  startDate: Date, 
  endDate: Date
): 'On Track' | 'At Risk' | 'Behind Schedule' => {
  const completion = calculateProjectCompletion(tasks);
  const totalDuration = daysBetween(new Date(startDate), new Date(endDate));
  const elapsed = daysBetween(new Date(startDate), new Date());
  const expectedCompletion = totalDuration > 0 ? Math.min(100, Math.round((elapsed / totalDuration) * 100)) : 0;
  
  const variance = completion - expectedCompletion;
  
  if (variance >= 0) return 'On Track';
  if (variance >= -15) return 'At Risk';
  return 'Behind Schedule';
};

// Helper function to get health color
const getHealthColor = (health: 'On Track' | 'At Risk' | 'Behind Schedule'): string => {
  switch (health) {
    case 'On Track': return COLORS.green;
    case 'At Risk': return COLORS.yellow;
    case 'Behind Schedule': return COLORS.red;
    default: return COLORS.gray;
  }
};

interface ProjectReportProps {
  project: Project;
  tasks: Task[];
  users: User[];
}

export default function ProjectReport({ project, tasks, users }: ProjectReportProps) {
  // Calculate project metrics
  const projectTasks = tasks.filter(task => task.projectId === project.id);
  const completedTasks = projectTasks.filter(task => task.status === 'Complete');
  const completion = calculateProjectCompletion(projectTasks);
  const health = calculateProjectHealth(projectTasks, new Date(project.startDate), new Date(project.endDate));
  const healthColor = getHealthColor(health);
  const remaining = daysRemaining(new Date(project.endDate));
  const totalDuration = daysBetween(new Date(project.startDate), new Date(project.endDate));
  const elapsed = daysBetween(new Date(project.startDate), new Date());
  const progress = Math.min(100, Math.round((elapsed / totalDuration) * 100));
  
  // Task status distribution data
  const taskStatusData = [
    { name: 'Not Started', value: projectTasks.filter(t => t.status === 'Not Started').length },
    { name: 'In Progress', value: projectTasks.filter(t => t.status === 'In Progress').length },
    { name: 'Reviewed', value: projectTasks.filter(t => t.status === 'Reviewed').length },
    { name: 'Complete', value: projectTasks.filter(t => t.status === 'Complete').length },
  ];
  
  // Category distribution data
  const categoryMap: Record<string, number> = {};
  projectTasks.forEach(task => {
    if (task.category) {
      categoryMap[task.category] = (categoryMap[task.category] || 0) + 1;
    }
  });
  
  const categoryData = Object.entries(categoryMap)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);
  
  // Team member contribution data
  const teamContributionData = users
    .filter(user => project.members.includes(user.id) || user.id === project.owner)
    .map(user => {
      const assignedTasks = projectTasks.filter(task => 
        task.assignedTo && task.assignedTo.includes(user.id)
      );
      
      const completedAssignedTasks = assignedTasks.filter(task => task.status === 'Complete');
      
      return {
        id: user.id,
        name: user.name,
        totalTasks: assignedTasks.length,
        completedTasks: completedAssignedTasks.length,
        pendingTasks: assignedTasks.length - completedAssignedTasks.length,
        completionRate: assignedTasks.length > 0 
          ? Math.round((completedAssignedTasks.length / assignedTasks.length) * 100) 
          : 0
      };
    })
    .sort((a, b) => b.totalTasks - a.totalTasks);
  
  // Timeline data (tasks by due date)
  const timelineData: { date: string; count: number }[] = [];
  const dateMap: Record<string, number> = {};
  
  projectTasks.forEach(task => {
    const dueDate = formatDate(task.dueDate);
    dateMap[dueDate] = (dateMap[dueDate] || 0) + 1;
  });
  
  // Sort dates and create timeline data
  Object.entries(dateMap)
    .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())
    .forEach(([date, count]) => {
      timelineData.push({ date, count });
    });
  
  return (
    <div className="space-y-6">
      {/* Project Overview */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Project Overview</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Timeline</h3>
            <p className="text-lg font-medium text-white">
              {formatDate(project.startDate)} - {formatDate(project.endDate)}
            </p>
            <div className="mt-2 flex items-center text-sm">
              <Calendar className="w-4 h-4 mr-1 text-purple-400" />
              <span className="text-gray-400">
                {remaining > 0 
                  ? `${remaining} days remaining` 
                  : 'Past due date'}
              </span>
            </div>
          </div>
          
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Completion</h3>
            <p className="text-lg font-medium text-white">
              {completion}% Complete
            </p>
            <div className="mt-2 flex items-center text-sm">
              <CheckSquare className="w-4 h-4 mr-1 text-blue-400" />
              <span className="text-gray-400">
                {completedTasks.length} of {projectTasks.length} tasks
              </span>
            </div>
          </div>
          
          <div className="bg-gray-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-400 mb-1">Status</h3>
            <p className="text-lg font-medium text-white">
              {health}
            </p>
            <div className="mt-2 flex items-center text-sm">
              <Clock className="w-4 h-4 mr-1" style={{ color: healthColor }} />
              <span className="text-gray-400">
                {progress}% of timeline elapsed
              </span>
            </div>
          </div>
        </div>
        
        <div className="mt-4">
          <h3 className="text-sm font-medium text-gray-400 mb-2">Progress</h3>
          <div className="flex items-center space-x-2">
            <div className="flex-grow">
              <div className="w-full bg-gray-700 rounded-full h-2.5">
                <div 
                  className="h-2.5 rounded-full" 
                  style={{ 
                    width: `${completion}%`,
                    backgroundColor: healthColor
                  }}
                ></div>
              </div>
            </div>
            <span className="text-sm font-medium text-white">{completion}%</span>
          </div>
        </div>
      </div>
      
      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Task Status Distribution */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Task Status Distribution</h2>
          
          {taskStatusData.every(item => item.value === 0) ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <AlertTriangle className="w-12 h-12 mb-2" />
              <p>No task data available</p>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={taskStatusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {taskStatusData.map((entry, index) => (
                      <Cell 
                        key={`cell-${index}`} 
                        fill={STATUS_COLORS[entry.name as TaskStatus] || COLORS.gray} 
                      />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value) => [`${value} tasks`, 'Count']}
                    contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
        
        {/* Timeline Chart */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Task Timeline</h2>
          
          {timelineData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <AlertTriangle className="w-12 h-12 mb-2" />
              <p>No timeline data available</p>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={timelineData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis 
                    dataKey="date" 
                    stroke="#9ca3af"
                    tick={{ fill: '#9ca3af' }}
                  />
                  <YAxis 
                    stroke="#9ca3af"
                    tick={{ fill: '#9ca3af' }}
                  />
                  <Tooltip 
                    formatter={(value) => [`${value} tasks`, 'Count']}
                    contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="count" 
                    stroke={COLORS.green} 
                    activeDot={{ r: 8 }} 
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </div>
      
      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Team Member Contribution */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Team Contribution</h2>
          
          {teamContributionData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <AlertTriangle className="w-12 h-12 mb-2" />
              <p>No team data available</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-64 overflow-y-auto pr-2">
              {teamContributionData.map(member => (
                <div key={member.id} className="bg-gray-700 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-white">{member.name}</h3>
                    <span className="text-sm text-gray-400">
                      {member.completedTasks} / {member.totalTasks} tasks
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-600 rounded-full h-2.5 mb-2">
                    <div 
                      className="h-2.5 rounded-full bg-indigo-500" 
                      style={{ width: `${member.completionRate}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-400">
                    <span>{member.completionRate}% complete</span>
                    <span>{member.pendingTasks} pending</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Category Distribution */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-medium text-white mb-4">Task Categories</h2>
          
          {categoryData.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500">
              <AlertTriangle className="w-12 h-12 mb-2" />
              <p>No category data available</p>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={categoryData}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis 
                    type="number"
                    stroke="#9ca3af"
                    tick={{ fill: '#9ca3af' }}
                  />
                  <YAxis 
                    dataKey="name" 
                    type="category"
                    stroke="#9ca3af"
                    tick={{ fill: '#9ca3af' }}
                    width={100}
                  />
                  <Tooltip 
                    formatter={(value) => [`${value} tasks`, 'Count']}
                    contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151' }}
                  />
                  <Bar 
                    dataKey="value" 
                    fill={COLORS.pink}
                    radius={[0, 4, 4, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </div>
      
      {/* Task List */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-lg font-medium text-white mb-4">Task List</h2>
        
        {projectTasks.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-gray-500">
            <AlertTriangle className="w-12 h-12 mb-2" />
            <p>No tasks available for this project</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-left text-gray-400">
              <thead className="text-xs text-gray-400 uppercase bg-gray-700">
                <tr>
                  <th scope="col" className="px-4 py-3 rounded-tl-lg">Task</th>
                  <th scope="col" className="px-4 py-3">Status</th>
                  <th scope="col" className="px-4 py-3">Due Date</th>
                  <th scope="col" className="px-4 py-3">Priority</th>
                  <th scope="col" className="px-4 py-3 rounded-tr-lg">Assigned To</th>
                </tr>
              </thead>
              <tbody>
                {projectTasks.map((task, index) => (
                  <tr 
                    key={task.id} 
                    className={`border-b border-gray-700 ${index % 2 === 0 ? 'bg-gray-800' : 'bg-gray-700'}`}
                  >
                    <td className="px-4 py-3 font-medium text-white">{task.title}</td>
                    <td className="px-4 py-3">
                      <span 
                        className="px-2 py-1 rounded-full text-xs font-medium"
                        style={{ 
                          backgroundColor: `${STATUS_COLORS[task.status]}30`, 
                          color: STATUS_COLORS[task.status] 
                        }}
                      >
                        {task.status}
                      </span>
                    </td>
                    <td className="px-4 py-3">{formatDate(task.dueDate)}</td>
                    <td className="px-4 py-3">
                      <span 
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          task.priority === 'Low' ? 'bg-green-900/30 text-green-300' :
                          task.priority === 'Medium' ? 'bg-blue-900/30 text-blue-300' :
                          task.priority === 'High' ? 'bg-orange-900/30 text-orange-300' :
                          'bg-red-900/30 text-red-300'
                        }`}
                      >
                        {task.priority}
                      </span>
                    </td>
                    <td className="px-4 py-3">
                      {task.assignedTo && task.assignedTo.length > 0 ? (
                        <div className="flex -space-x-2">
                          {task.assignedTo.slice(0, 3).map((userId, i) => {
                            const user = users.find(u => u.id === userId);
                            return (
                              <div 
                                key={i}
                                className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-xs border border-gray-700"
                                title={user?.name || 'Unknown User'}
                              >
                                {user?.name.charAt(0) || '?'}
                              </div>
                            );
                          })}
                          {task.assignedTo.length > 3 && (
                            <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-xs border border-gray-700">
                              +{task.assignedTo.length - 3}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-500">Unassigned</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
