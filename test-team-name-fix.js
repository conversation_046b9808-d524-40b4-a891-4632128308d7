/**
 * Test script to verify the team name resolution fix
 * This test checks that the "Send to Team" button should now show the correct team name
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, collection, getDocs, query, orderBy, where } = require('firebase/firestore');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

async function testTeamNameFix() {
  console.log('🔧 Testing Team Name Resolution Fix');
  console.log('===================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const db = getFirestore(app);

    // Test user email
    const testUserEmail = '<EMAIL>';

    console.log(`\n📋 Step 1: Checking recent codebase documentation records for user: ${testUserEmail}`);

    // Get PMO records from user-specific collection
    const pmoCollectionRef = collection(db, 'users', testUserEmail, 'PMO');
    const recordsQuery = query(
      pmoCollectionRef, 
      orderBy('createdAt', 'desc')
    );
    
    const snapshot = await getDocs(recordsQuery);
    console.log(`   Found ${snapshot.docs.length} total PMO records`);

    // Filter for codebase documentation records
    const codebaseDocRecords = snapshot.docs.filter(doc => {
      const data = doc.data();
      return data.title && data.title.toLowerCase().includes('codebase documentation');
    });

    console.log(`\n🔍 Step 2: Found ${codebaseDocRecords.length} codebase documentation records`);

    if (codebaseDocRecords.length > 0) {
      console.log('\n📊 Recent Codebase Documentation Records:');
      
      codebaseDocRecords.slice(0, 5).forEach((doc, index) => {
        const data = doc.data();
        const createdAt = data.createdAt ? new Date(data.createdAt.seconds * 1000) : null;
        
        console.log(`\n   ${index + 1}. Record ID: ${doc.id}`);
        console.log(`      Title: ${data.title}`);
        console.log(`      Status: ${data.status || 'No status'}`);
        console.log(`      Team Assignment (agentIds): ${data.agentIds ? data.agentIds.join(', ') : 'None'}`);
        console.log(`      Team Rationale: ${data.teamSelectionRationale ? 'Present' : 'None'}`);
        console.log(`      Created: ${createdAt ? createdAt.toISOString() : 'No date'}`);
        
        // Check if PMO assessment contains team references
        if (data.pmoAssessment) {
          const assessmentSnippet = data.pmoAssessment.substring(0, 200) + '...';
          console.log(`      PMO Assessment (snippet): ${assessmentSnippet}`);
          
          // Look for team references in assessment
          const teamMatches = data.pmoAssessment.match(/\*\*Teams:\*\*\s*([A-Za-z\s]+)/i);
          if (teamMatches) {
            console.log(`      ✅ Team found in assessment: "${teamMatches[1].trim()}"`);
          } else {
            console.log(`      ⚠️  No team reference found in assessment`);
          }
        } else {
          console.log(`      ⚠️  No PMO assessment found`);
        }
      });

      console.log('\n🎯 Step 3: Team Name Resolution Test Results:');
      console.log('   With the fixes applied:');
      console.log('   ✅ PMORecordList.tsx now includes CodebaseDocumentation case');
      console.log('   ✅ Uses centralized getDisplayTeamName function');
      console.log('   ✅ PMO assessment API includes CodebaseDocumentation case');
      console.log('   ✅ PMOAgent.ts includes CodebaseDocumentation case');
      
      console.log('\n📈 Expected Behavior:');
      console.log('   - "Send to Team" button should show "Send to Codebase Documentation"');
      console.log('   - Team assignment should display "Codebase Documentation"');
      console.log('   - PMO assessment should reference "Codebase Documentation" team');
      console.log('   - No more generic "Send to Team" labels for Ag007 records');

    } else {
      console.log('\n⚠️  No codebase documentation records found.');
      console.log('   To test the fix:');
      console.log('   1. Create a new codebase documentation request');
      console.log('   2. Check that the "Send to Team" button shows the team name');
      console.log('   3. Verify team assignment displays correctly');
    }

    console.log('\n✅ Team Name Resolution Fix Test Complete');

  } catch (error) {
    console.error('\n❌ Error testing team name fix:', error);
  }
}

// Run the test
testTeamNameFix().then(() => {
  console.log('\n🎉 Test execution finished');
  process.exit(0);
}).catch((error) => {
  console.error('\n💥 Test execution failed:', error);
  process.exit(1);
});
