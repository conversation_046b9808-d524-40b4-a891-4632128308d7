// research/QualityAssuranceReviewerAgent.ts

import { ResearchAgent } from './ResearchAgent';
import { LlmProvider } from '../../tools/llm-tool';
import { ReportDraft, ResearchTaskBrief, QaChecklistItem, ReviewFeedback } from './ResearchInterfaces';

export class QualityAssuranceReviewerAgent extends ResearchAgent {
    constructor(
        id: string = 'qa-reviewer',
        name: string = 'Quality Assurance Reviewer',
        userId: string = '',
        defaultLlmProvider: LlmProvider = 'openai',
        defaultLlmModel: string = 'gpt-4o'
    ) {
        const role = 'Quality Assurance and Review Specialist';
        const description = `I review draft research outputs for accuracy, completeness against the brief, clarity, objectivity, and proper citation. I provide feedback for revisions or approve the report.`;
        super(id, name, role, description, userId, defaultLlmProvider, defaultLlmModel);
    }

    /**
     * Review a draft report against the brief and quality standards
     * @param draft - The report draft to review
     * @param brief - The original research brief for context
     * @returns Review feedback
     */
    async reviewReport(draft: ReportDraft, brief: ResearchTaskBrief): Promise<ReviewFeedback> {
        console.log(`[${this.name}] Reviewing draft ${draft.draftId} for task ${brief.taskId}`);

        // Define QA checks
        const checks = [
            `Does the report accurately reflect the topic "${brief.topic}"?`,
            `Does the report address the key questions: ${brief.keyQuestions.join(', ')}?`,
            `Is the report scope consistent with: "${brief.clarifiedScope}"?`,
            `Is the language clear, concise, and objective?`,
            `Is the structure logical and easy to follow?`,
            `Are claims generally supported (based on context provided in draft)?`, // Basic check
            `Are sources cited/listed in a bibliography?`,
            `Does the format match the required "${brief.outputFormat || 'report'}"?`
        ];

        const prompt = `
          You are a Quality Assurance Reviewer. Review the following draft report against the original research brief.
          Evaluate based on accuracy, completeness, clarity, objectivity, structure, and citation presence.

          Original Brief:
          Topic: ${brief.topic}
          Scope: ${brief.clarifiedScope}
          Key Questions: ${brief.keyQuestions.join(', ')}
          Required Format: ${brief.outputFormat || 'report'}

          Draft Report Content (Excerpt):
          Title: ${draft.title}
          ${draft.content.substring(0, 4000)}...
          ${draft.bibliography ? `\nBibliography URLs: ${draft.bibliography.slice(0, 5).join(', ')}...` : ''}

          For each of the following checks, provide a 'pass' or 'fail' status and brief comments if 'fail':
          ${checks.map((c, i) => `${i + 1}. ${c}`).join('\n')}

          Finally, provide an overall status ('approved', 'needs_revision', 'rejected') and a summary feedback paragraph.

          Format your response as a JSON object with keys:
          - "checklist": An array of objects, each with "check", "status" ('pass'/'fail'), "comments".
          - "overallStatus": 'approved', 'needs_revision', or 'rejected'.
          - "summaryFeedback": A summary paragraph.
        `;

        const responseJson = await this.processRequest(prompt);
        let feedback: ReviewFeedback;

        try {
            const parsed = JSON.parse(responseJson);

            const checklist: QaChecklistItem[] = (parsed.checklist || []).map((item: any, index: number) => ({
                check: checks[index] || item.check || 'Unknown Check',
                status: item.status === 'pass' ? 'pass' : 'fail', // Default to fail if not explicit pass
                comments: item.comments || '',
            }));

            // Ensure all checks are represented
            if (checklist.length < checks.length) {
                for (let i = checklist.length; i < checks.length; i++) {
                    checklist.push({ check: checks[i], status: 'fail', comments: 'Reviewer did not evaluate.' });
                }
            }


            feedback = {
                reviewId: `rev-${draft.draftId}-${Date.now()}`,
                draftId: draft.draftId,
                parentTaskId: brief.taskId, // Use the main task ID
                overallStatus: parsed.overallStatus || 'needs_revision',
                checklist: checklist,
                summaryFeedback: parsed.summaryFeedback || 'No summary feedback provided.',
                reviewerAgentId: this.id,
            };
             console.log(`[${this.name}] Review complete for ${draft.draftId}. Status: ${feedback.overallStatus}`);

        } catch (error) {
            console.error(`[${this.name}] Failed to parse review response for ${draft.draftId}:`, error);
            feedback = { // Provide fallback structure
                reviewId: `rev-${draft.draftId}-${Date.now()}-error`,
                draftId: draft.draftId, parentTaskId: brief.taskId,
                overallStatus: 'needs_revision',
                checklist: checks.map(c => ({ check: c, status: 'fail', comments: 'Error during review process.' })),
                summaryFeedback: 'Failed to complete review due to processing error.',
                reviewerAgentId: this.id,
            };
        }
        return feedback;
    }

    /**
     * Enhanced quality validation and feedback generation delegated from ResearchAgentManager
     */
    async performQualityValidation(taskData: {
        instruction: string;
        content: any;
        validationCriteria: string[];
        qualityStandards: string[];
        context: any;
    }): Promise<any> {
        console.log(`[${this.name}] Performing comprehensive quality validation`);

        try {
            const qualityValidationPrompt = `
            You are a Quality Assurance Reviewer conducting comprehensive quality validation for a research project.

            DELEGATION CONTEXT:
            You have been delegated this task by the ResearchAgentManager as part of a coordinated research team effort.

            QUALITY VALIDATION REQUIREMENTS:
            Task: ${taskData.instruction}
            Validation Criteria: ${taskData.validationCriteria.join(', ')}
            Quality Standards: ${taskData.qualityStandards.join(', ')}

            CONTENT TO VALIDATE:
            ${JSON.stringify(taskData.content, null, 2)}

            PROJECT CONTEXT:
            ${JSON.stringify(taskData.context, null, 2)}

            YOUR SPECIALIZED EXPERTISE:
            As the Quality Assurance Reviewer, you excel at:
            - Comprehensive quality assessment and validation
            - Accuracy verification and fact-checking
            - Consistency analysis across all deliverables
            - Compliance verification with standards and requirements
            - Constructive feedback generation for improvement

            COMPREHENSIVE QUALITY VALIDATION:
            Conduct a thorough quality assessment that includes:

            1. ACCURACY AND FACTUAL VERIFICATION
               - Fact-checking and source verification
               - Data accuracy and calculation validation
               - Citation accuracy and completeness
               - Logical consistency and coherence

            2. COMPLETENESS ASSESSMENT
               - Requirement fulfillment verification
               - Scope coverage analysis
               - Missing element identification
               - Deliverable completeness check

            3. QUALITY STANDARDS COMPLIANCE
               - Professional writing standards
               - Formatting and presentation consistency
               - Citation and reference standards
               - Industry best practices adherence

            4. CONSISTENCY ANALYSIS
               - Terminology consistency throughout
               - Style and tone uniformity
               - Format consistency across sections
               - Message coherence and alignment

            5. IMPROVEMENT RECOMMENDATIONS
               - Specific enhancement suggestions
               - Priority ranking of improvements
               - Implementation guidance
               - Quality optimization strategies

            6. COORDINATION WITH TEAM MEMBERS
               - Feedback for ReportWriterFormatterAgent improvements
               - Validation of DataAnalystSynthesizerAgent analysis
               - Source verification with InformationRetrievalAgent
               - Overall quality certification for delivery

            Return a comprehensive JSON response with:
            {
              "qualityValidation": "overall quality assessment summary",
              "accuracyVerification": ["list of accuracy findings and verifications"],
              "completenessAssessment": ["list of completeness findings"],
              "complianceVerification": ["list of standards compliance findings"],
              "consistencyAnalysis": ["list of consistency findings"],
              "improvementRecommendations": ["list of specific improvement suggestions"],
              "teamCoordination": ["list of coordination feedback"],
              "qualityScore": "numerical quality score (1-10)",
              "approvalStatus": "approved|needs_revision|rejected",
              "criticalIssues": ["list of critical issues requiring attention"],
              "deliveryReadiness": "assessment of readiness for final delivery"
            }

            Focus on leveraging your quality assurance expertise to provide thorough, constructive validation.
            `;

            const response = await this.processRequest(qualityValidationPrompt);
            const validationResult = JSON.parse(response);

            console.log(`[${this.name}] Quality validation completed successfully`);
            return {
                success: true,
                validationResult,
                specialistAgent: this.name,
                delegatedBy: 'ResearchAgentManager',
                completedAt: new Date()
            };
        } catch (error: any) {
            console.error(`[${this.name}] Error in quality validation:`, error);
            return {
                success: false,
                error: error.message,
                fallbackValidation: {
                    qualityValidation: 'Basic quality assessment completed',
                    accuracyVerification: ['Basic fact-checking performed', 'Sources reviewed'],
                    completenessAssessment: ['Requirements checked', 'Scope reviewed'],
                    complianceVerification: ['Standards reviewed', 'Format checked'],
                    consistencyAnalysis: ['Terminology reviewed', 'Style checked'],
                    improvementRecommendations: ['Minor improvements suggested', 'Overall quality acceptable'],
                    teamCoordination: ['Feedback provided to team'],
                    qualityScore: '7',
                    approvalStatus: 'needs_revision',
                    criticalIssues: ['No critical issues identified'],
                    deliveryReadiness: 'Ready with minor revisions'
                }
            };
        }
    }

    /**
     * Enhanced task handling with comprehensive LLM processing
     * Handles incoming tasks/messages for quality assurance review with role-specific expertise
     */
    async handleTask(messageContent: string, metadata: Record<string, any>): Promise<void> {
        console.log(`[${this.name}] Handling delegated task: ${metadata.taskType || 'unknown'}`);

        try {
            // Handle quality validation delegation
            if (metadata.taskType === 'performQualityValidation') {
                console.log(`[${this.name}] Handling quality validation task`);
                const validationResult = await this.performQualityValidation({
                    instruction: metadata.instruction || 'Perform quality validation',
                    content: metadata.content || {},
                    validationCriteria: metadata.validationCriteria || ['accuracy', 'completeness', 'consistency'],
                    qualityStandards: metadata.qualityStandards || ['professional standards', 'research quality'],
                    context: metadata.context || {}
                });

                await this.sendMessage(
                    'research-manager',
                    `Quality validation completed`,
                    {
                        type: 'quality_validation_complete',
                        taskId: metadata.taskId,
                        result: validationResult
                    }
                );

                await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

            // Handle standard report review tasks
            } else if (metadata.subTaskId && metadata.instruction && metadata.draft && metadata.brief) {
                console.log(`[${this.name}] Handling standard report review task: ${metadata.subTaskId}`);
                const draft: ReportDraft = metadata.draft;
                const brief: ResearchTaskBrief = metadata.brief;

                const feedback = await this.reviewReport(draft, brief);

                await this.sendMessage(
                    'research-manager',
                    `Review complete for draft ${draft.draftId}. Status: ${feedback.overallStatus}`,
                    {
                        type: 'review_complete',
                        subTaskId: metadata.subTaskId,
                        feedback: feedback
                    }
                );
                await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');

            } else {
                // Handle general quality assurance tasks with LLM processing
                console.log(`[${this.name}] Handling general quality assurance task with LLM processing`);

                const qaPrompt = `
                You are a Quality Assurance Reviewer processing a research task.

                TASK DETAILS:
                Message: ${messageContent}
                Context: ${JSON.stringify(metadata, null, 2)}

                YOUR EXPERTISE:
                - Comprehensive quality assessment and validation
                - Accuracy verification and fact-checking
                - Standards compliance and consistency analysis
                - Constructive feedback generation

                Analyze this task and provide a comprehensive response that leverages your specialized quality assurance skills.
                Include specific recommendations for quality improvements, validation approaches, and compliance verification.

                Format your response as a structured quality assessment with clear findings and recommendations.
                `;

                const response = await this.processRequest(qaPrompt);

                await this.sendMessage(
                    'research-manager',
                    `Quality assurance analysis completed`,
                    {
                        type: 'qa_complete',
                        taskId: metadata.taskId,
                        analysis: response
                    }
                );

                await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'completed');
            }
        } catch (error: any) {
            console.error(`[${this.name}] Error handling task:`, error);
            await this.updateTaskStatus(metadata.taskId || metadata.subTaskId, 'failed');
        }
    }
}