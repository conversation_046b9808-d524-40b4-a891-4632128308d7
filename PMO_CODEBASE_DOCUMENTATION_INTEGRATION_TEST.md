# PMO → Codebase Documentation Integration Test

## Overview
This document outlines the complete testing procedure for the new integration between the PMO Record List "Send to CodebaseDocumentation Team" button and the CodebaseDocumentationOrchestratorAgent.

## Integration Features Implemented ✅

### 1. **Automatic Agent Triggering**
- ✅ "Send to CodebaseDocumentation Team" button automatically initiates CodebaseDocumentationOrchestratorAgent
- ✅ PMO Assessment content used as primary input specification
- ✅ Intelligent path extraction from assessment content
- ✅ Reasonable defaults for documentationScope and outputFormat

### 2. **PMO Assessment as Driving Requirements**
- ✅ Complete PMO Assessment content passed as the user's original request
- ✅ Team selection rationale included in custom context
- ✅ Priority and category information preserved
- ✅ Agent treats PMO Assessment as full specification for documentation needs

### 3. **Agent Output Integration**
- ✅ Output automatically saved to Agent_Output collection
- ✅ Appears in PMO OUTPUT tab with proper labeling
- ✅ PMO record status updated to "Completed"
- ✅ Full metadata preserved for traceability

## Test Procedure

### Step 1: Create PMO Record with Codebase Documentation Need
1. **Navigate to PMO interface**
2. **Create a new PMO record** with description like:
   ```
   "Generate comprehensive documentation for the React application including 
   component architecture, API endpoints, and user interface documentation. 
   Focus on src/, components/, and api/ directories."
   ```
3. **Submit and wait for PMO Assessment generation**
4. **Verify PMO Assessment includes CodebaseDocumentation team assignment**

### Step 2: Trigger Team Delegation
1. **Navigate to PMO Record List**
2. **Find the created PMO record**
3. **Click "Save PMO Requirement" button** (if not already done)
4. **Click "Send to Codebase Documentation" button**
5. **Verify button shows loading state: "Sending..."**

### Step 3: Verify Automatic Agent Execution
1. **Check browser console for logs:**
   ```
   🔧 Triggering codebase documentation generation for PMO [PMO_ID]
   [PMO Codebase Documentation] Extracted paths: src, components, api
   [PMO Codebase Documentation] Using PMO Assessment as primary specification
   ✅ Codebase documentation generation initiated successfully
   ```

2. **Verify PMO record status updates to "In Progress"**

### Step 4: Monitor Documentation Generation
1. **Watch for streaming progress updates** (if monitoring tools available)
2. **Wait for completion** (typically 2-5 minutes depending on codebase size)
3. **Check server logs for completion messages**

### Step 5: Verify PMO OUTPUT Tab Integration
1. **Navigate to PMO OUTPUT tab**
2. **Look for new codebase documentation output:**
   - Should appear with **indigo "Codebase Documentation" badge**
   - Title should reference the extracted paths
   - Team should show as **"Codebase Documentation Team"**
   - Recent timestamp

### Step 6: Verify Content Quality
1. **Click on the codebase documentation output**
2. **Verify content includes:**
   - PMO Assessment requirements addressed
   - Comprehensive documentation based on assessment
   - Analysis of specified directories/paths
   - Technical details matching the original request
3. **Check metadata shows:**
   - PMO Record ID (linking back to original request)
   - Auto-triggered source
   - Selected paths extracted from assessment

### Step 7: Verify PMO Record Completion
1. **Navigate back to PMO Record List**
2. **Verify PMO record status shows "Completed"**
3. **Verify no further action buttons are shown**

## Expected Workflow Flow

```mermaid
graph TD
    A[PMO Record Created] --> B[PMO Assessment Generated]
    B --> C[CodebaseDocumentation Team Assigned]
    C --> D[User Clicks 'Send to Codebase Documentation']
    D --> E[triggerCodebaseDocumentationGeneration Called]
    E --> F[Extract Paths from PMO Assessment]
    F --> G[Call /api/codebase-documentation/stream]
    G --> H[CodebaseDocumentationOrchestratorAgent Executes]
    H --> I[Sub-agents Process Requirements]
    I --> J[Output Saved to Agent_Output Collection]
    J --> K[PMO Record Status → Completed]
    K --> L[Output Appears in PMO OUTPUT Tab]
```

## Key Integration Points

### 1. **Path Extraction Logic**
The system intelligently extracts codebase paths from PMO Assessment using:
- **Explicit path mentions**: "analyze the src/components directory"
- **Directory structure references**: "within the api folder"
- **File extension patterns**: "review .tsx files"
- **Common project directories**: src, lib, components, api, etc.
- **Smart defaults** based on technology mentions (React → components, API → api)

### 2. **PMO Assessment as Specification**
```javascript
const documentationRequest = {
  description: pmoData.pmoAssessment, // Primary specification
  customContext: `PMO Assessment Context:\n${pmoData.pmoAssessment}\n\nTeam Selection Rationale:\n${pmoData.teamSelectionRationale}`,
  selectedPaths: extractedPaths,
  documentationScope: 'full',
  outputFormat: 'markdown'
};
```

### 3. **Agent Output Storage**
```javascript
await addAgentOutput({
  agentType: 'CodebaseDocumentationOrchestrator',
  content: result.output,
  metadata: {
    pmoRecordId: pmoData.pmoId,
    autoTriggered: true,
    triggerSource: 'pmo-delegation'
  }
});
```

## Troubleshooting

### If Button Doesn't Trigger Agent
1. **Check team name matching** in `pmo-notify-team/route.ts`
2. **Verify PMO Assessment contains CodebaseDocumentation team**
3. **Check browser console for JavaScript errors**

### If Paths Not Extracted Correctly
1. **Review PMO Assessment content** for path mentions
2. **Check extraction patterns** in `extractSelectedPathsFromAssessment`
3. **Verify fallback defaults** are appropriate

### If Output Doesn't Appear in PMO OUTPUT Tab
1. **Verify Agent_Output collection** has the entry
2. **Check agent type** is `'CodebaseDocumentationOrchestrator'`
3. **Refresh PMO OUTPUT tab**

## Success Criteria ✅

- [ ] **PMO delegation triggers agent automatically**
- [ ] **PMO Assessment used as primary specification**
- [ ] **Paths extracted intelligently from assessment**
- [ ] **Documentation generation completes successfully**
- [ ] **Output saved to Agent_Output collection**
- [ ] **Output appears in PMO OUTPUT tab with proper labeling**
- [ ] **PMO record status updated to "Completed"**
- [ ] **Content addresses original PMO Assessment requirements**
- [ ] **Full traceability maintained through metadata**

## Files Modified

| File | Purpose | Changes |
|------|---------|---------|
| `app/api/pmo-notify-team/route.ts` | PMO team delegation | Added CodebaseDocumentation team handler |

## Conclusion

This integration bridges the gap between PMO workflow delegation and actual codebase documentation execution. When teams are assigned codebase documentation work through the PMO system, the work automatically begins with proper context and requirements, ensuring seamless workflow continuity and comprehensive documentation delivery.
