/**
 * queryDocumentsTool.ts
 *
 * This tool provides vector-based document search capabilities with LLM processing
 * and internet search fallback. It integrates Pinecone for vector similarity search,
 * Firestore for document retrieval, and Groq for processing user queries.
 */

import { processWithGroq } from './groq-ai';
import { vectorEmbeddingTool } from './vector-embeddings';
import { adminDb } from 'components/firebase-admin';
import { ContentSelectionResult, Source } from '../../app/src/types/shared';
import { markdownRendererTool } from './markdown-renderer-tool';

// Simplified input options for the query documents tool
export interface QueryDocumentsOptions {
  query: string;                   // User query text
  userId: string;                  // User ID for document storage paths
  category?: string;               // Category/JobId to fetch namespaces
  filename?: string;               // Filename to get category from
  namespace?: string;              // Direct namespace to use (bypasses filename lookup)
  useInternetSearch?: boolean;     // Whether to use internet search as fallback
  maxResults?: number;             // Maximum number of results to return
  model?: string;                  // Optional model name to use for processing
  modelOptions?: Record<string, any>; // Optional model configuration options
}

// Result structure for the query documents tool
export interface QueryDocumentsResult {
  success: boolean;
  content: string;                 // The generated response
  sources?: Source[];              // Sources of information used
  metadata?: {                     // Additional metadata about the query
    chunkCount?: number;
    averageRelevance?: number;
    internetSearchUsed?: boolean;
    functionCallingUsed?: boolean; // Whether function calling was used
    namespaces?: string[];         // Namespaces that were searched
    category?: string;             // Category that was used
    requestForInformation?: {      // Information about the request for more information
      message: string;             // The message requesting more information
      questions: string[];         // Specific questions to ask the user
      actions: string[];           // Recommended actions for the user
    };
  };
  error?: string;                  // Error message if any
}

/**
 * Tool for querying documents using vector search and processing results with LLMs
 */
export class QueryDocumentsTool {
  constructor() {}

  /**
   * Main entry point for processing document queries
   *
   * @param options - Query options including user query and category
   * @returns Processed content with metadata
   */
  async process(options: QueryDocumentsOptions): Promise<QueryDocumentsResult> {
    try {
      // Validate input
      this.validateInput(options);

      console.log(`QueryDocumentsTool: Processing query "${options.query}" for user ${options.userId}`);

      // Convert query to vector
      const queryVector = await this.convertPromptToVector(options.query);
      console.log(`QueryDocumentsTool: Converted query to vector of length ${queryVector.length}`);

      // Determine category - either directly provided or derived from filename
      let category = options.category;
      if (!category && options.filename) {
        category = await this.whichCategoryForFileName(options.userId, options.filename);
        console.log(`QueryDocumentsTool: Derived category ${category} from filename ${options.filename}`);
      }

      // Initialize namespaces array
      let namespaces: string[] = [];

      // If a direct namespace is provided, use it (highest priority)
      if (options.namespace) {
        console.log(`QueryDocumentsTool: Using directly provided namespace: ${options.namespace}`);
        namespaces = [options.namespace];
      }
      // Handle both specific categories and 'Unknown' category (similar to chatGroq API)
      else if (category && category !== 'Unknown') {
        // For specific categories, fetch all associated namespaces
        namespaces = await this.fetchNamespacesForCategory(options.userId, category);
        console.log(`QueryDocumentsTool: Found ${namespaces.length} namespaces for category ${category}:`, namespaces);

        // If no namespaces found for category, fall back to filename if available
        if (namespaces.length === 0 && options.filename) {
          console.warn(`QueryDocumentsTool: No namespaces found for category ${category}, falling back to filename`);

          // Try to find the namespace associated with the filename
          const fileNamespace = await this.getNamespaceForFilename(options.userId, options.filename);
          if (fileNamespace) {
            console.log(`QueryDocumentsTool: Found namespace ${fileNamespace} for filename ${options.filename}`);
            namespaces = [fileNamespace];
          } else {
            // If no namespace found, use the filename directly
            console.log(`QueryDocumentsTool: No namespace found for filename ${options.filename}, using filename directly`);
            namespaces = [options.filename];
          }
        }

        // Log the namespaces that will be used for the query
        console.log(`QueryDocumentsTool: Using ${namespaces.length} namespaces for category ${category}:`, namespaces);
      } else if (options.filename) {
        // For 'Unknown' category or no category, try to find the namespace for the filename
        console.log(`QueryDocumentsTool: Looking up namespace for filename ${options.filename}`);

        // Try to find the namespace associated with the filename
        const fileNamespace = await this.getNamespaceForFilename(options.userId, options.filename);
        if (fileNamespace) {
          console.log(`QueryDocumentsTool: Found namespace ${fileNamespace} for filename ${options.filename}`);
          namespaces = [fileNamespace];
        } else {
          // If no namespace found, use the filename directly (like chatGroq API)
          console.log(`QueryDocumentsTool: No namespace found for filename ${options.filename}, using filename directly`);
          namespaces = [options.filename];
        }
      } else {
        // If neither namespace, category, nor filename is available, return error
        return {
          success: false,
          content: "",
          error: "Could not determine namespace for the query. Please provide a namespace, filename, or category."
        };
      }

      // Check if we have any namespaces to query
      if (namespaces.length === 0) {
        console.warn(`QueryDocumentsTool: No namespaces available for query`);

        // Create a more specific error message based on whether a category was provided
        let errorMessage = "";
        let contentMessage = "";

        if (options.category) {
          errorMessage = `No namespaces found for category "${options.category}"`;
          contentMessage = `I couldn't find any documents in the "${options.category}" category. This category might be empty or might not exist. Please try a different category or specify a particular document you'd like me to search through.`;
        } else if (options.filename) {
          errorMessage = `No namespace found for file "${options.filename}"`;
          contentMessage = `I couldn't find the document "${options.filename}" in your collection. Please check the filename and try again, or specify a different document to search through.`;
        } else {
          errorMessage = `No namespaces available for query`;
          contentMessage = `I couldn't find any document collections to search through. Please specify which documents you'd like me to search through, or provide more details about the type of information you're looking for.`;
        }

        if (options.useInternetSearch) {
          return await this.handleInternetSearchFallback(options.query, options.model, options.modelOptions);
        }

        return {
          success: false,
          content: contentMessage,
          error: errorMessage
        };
      }

      // Create content selector for the user
      const contentSelector = new ContentSelector(options.userId);

      // Select content based on vector similarity
      // This implementation allows the QueryDocumentsAgent to handle both filename-based requests
      // (category 'Unknown') and files with categories in the same way as the chatGroq API
      const contentResult = await contentSelector.selectContent(
        queryVector,
        namespaces
      );

      // If no relevant content found and internet search is enabled, use as fallback
      if (!contentResult && options.useInternetSearch) {
        return await this.handleInternetSearchFallback(options.query, options.model, options.modelOptions);
      }

      // If still no content, return error
      if (!contentResult) {
        // Create error message
        const errorMessage = `I couldn't find any relevant content for your query in the available documents. Please try rephrasing your question or provide more specific details about what you're looking for. You might also want to specify which documents or document categories you'd like me to search through.`;

        // Process the error message with the markdown-renderer-tool
        let formattedErrorMessage = errorMessage;
        try {
          const markdownResult = await markdownRendererTool.process({
            markdown: errorMessage,
            operation: 'preprocess'
          });

          if (markdownResult.success) {
            formattedErrorMessage = markdownResult.content;
          }
        } catch (error) {
          console.error("Error processing error message with markdown-renderer-tool:", error);
        }

        return {
          success: false,
          content: formattedErrorMessage,
          error: "No relevant content found for the query"
        };
      }

      // Process content with Groq (passing model parameters if provided)
      const processedResponse = await this.processWithGroq(
        options.query,
        contentResult.content,
        options.model,
        options.modelOptions,
        options.category
      );

      // Return successful result with processed response
      return {
        success: true,
        content: processedResponse,
        sources: contentResult.metadata.sources,
        metadata: {
          chunkCount: contentResult.metadata.chunkCount,
          averageRelevance: contentResult.metadata.averageRelevance,
          internetSearchUsed: false,
          namespaces: namespaces,
          category: category
        }
      };
    } catch (error) {
      console.error("Error in queryDocumentsTool:", error);
      return {
        success: false,
        content: "",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Convert prompt to vector representation using the vectorEmbeddingTool
   *
   * @param prompt - User query to convert
   * @returns Vector representation of the query
   */
  private async convertPromptToVector(prompt: string): Promise<number[]> {
    try {
      console.log("Converting prompt to vector using vectorEmbeddingTool");

      // Initialize the vector embedding tool if not already initialized
      if (!vectorEmbeddingTool.isInitialized()) {
        await vectorEmbeddingTool.initialize();
      }

      // Use the embeddings from the vector embedding tool
      const queryEmbedding = await vectorEmbeddingTool.createEmbeddingVector(prompt);
      console.log(`Generated vector of length: ${queryEmbedding.length}`);

      return queryEmbedding;
    } catch (error) {
      console.error("Error converting prompt to vector:", error);
      throw new Error("Failed to convert prompt to vector");
    }
  }

  /**
   * Fetch category for a specific filename
   *
   * @param userId - User ID
   * @param filename - Filename to look up
   * @returns Category string or 'Unknown'
   */
  private async whichCategoryForFileName(userId: string, filename: string): Promise<string> {
    try {
      console.log(`Looking up category for filename: ${filename}`);

      // Query Firestore for the file's category
      const snapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('name', '==', filename)
        .limit(1)
        .get();

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        const data = doc.data();
        console.log(`Found category for ${filename}:`, data.category);
        return data.category || 'Unknown';
      }

      // If no document found, return 'Unknown' category
      console.log(`No category found for filename: ${filename}, using 'Unknown'`);
      return 'Unknown';
    } catch (error) {
      console.error("Error fetching category:", error);
      return 'Unknown';
    }
  }

  /**
   * Fetch namespaces for a specific category
   *
   * @param userId - User ID
   * @param category - Category or JobId
   * @returns Array of namespace strings
   */
  private async fetchNamespacesForCategory(userId: string, category: string): Promise<string[]> {
    try {
      console.log(`QueryDocumentsTool: Fetching namespaces for category "${category}" and user "${userId}"`);

      const snapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('category', '==', category)
        .get();

      console.log(`QueryDocumentsTool: Found ${snapshot.docs.length} files with category "${category}"`);

      // Extract namespaces from the query results
      const namespaces = snapshot.docs
        .map(doc => {
          const data = doc.data();
          console.log(`QueryDocumentsTool: File "${data.name || doc.id}" has namespace "${data.namespace || 'undefined'}"`);
          return data.namespace;
        })
        .filter((namespace): namespace is string => !!namespace);

      console.log(`QueryDocumentsTool: Extracted ${namespaces.length} valid namespaces for category "${category}":`, namespaces);

      return namespaces;
    } catch (error) {
      console.error(`QueryDocumentsTool: Error fetching namespaces for category "${category}":`, error);
      return [];
    }
  }

  /**
   * Get the namespace associated with a filename
   *
   * @param userId - User ID
   * @param filename - Filename to look up
   * @returns Namespace string or null if not found
   */
  private async getNamespaceForFilename(userId: string, filename: string): Promise<string | null> {
    try {
      console.log(`Looking up namespace for filename: ${filename}`);

      // Query the files collection to find the file by name
      const snapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('name', '==', filename)
        .limit(1)
        .get();

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        const data = doc.data();
        const namespace = data.namespace;

        if (namespace) {
          console.log(`Found namespace ${namespace} for filename ${filename}`);
          return namespace;
        } else {
          console.log(`File found for ${filename}, but no namespace field`);
        }
      } else {
        console.log(`No file found with name ${filename}`);
      }

      return null;
    } catch (error) {
      console.error(`Error getting namespace for filename ${filename}:`, error);
      return null;
    }
  }

  /**
   * Process query and content with Groq LLM
   *
   * @param query - User query
   * @param content - Document content to process
   * @param model - Optional model name to use (defaults to deepseek-r1-distill-llama-70b)
   * @param modelOptions - Optional model configuration options
   * @returns Processed response from Groq
   */
  private async processWithGroq(
    query: string,
    content: string,
    model?: string,
    modelOptions?: Record<string, any>,
    category?: string
  ): Promise<string> {
    try {
      // Check if the query is asking for a list of files
      const isListingFilesQuery =
        (query.toLowerCase().includes("list") ||
         query.toLowerCase().includes("how") && query.toLowerCase().includes("file")) &&
        (query.toLowerCase().includes("file") ||
         query.toLowerCase().includes("document") ||
         query.toLowerCase().includes("publication") ||
         query.toLowerCase().includes("report"));

      // Create a more specific prompt for listing files queries
      let systemPrompt = "";

      if (isListingFilesQuery) {
        systemPrompt = `
You are an AI assistant that helps users find information in their documents.

The user is asking for a list of files, documents, or publications. Based on the provided content,
extract and list ALL unique file names or document titles mentioned.

DOCUMENT CONTENT:
${content}

USER QUERY:
${query}

INSTRUCTIONS:
1. Create a comprehensive list of ALL files or documents mentioned in the content
2. Format your response in proper markdown with bullet points or numbers
3. Include the file type/extension if available (e.g., .pdf, .txt)
4. DO NOT include any technical information like namespace IDs or internal identifiers
5. Group similar files together if appropriate
6. If the same file is mentioned multiple times, only list it once
7. Provide a brief introduction before the list
8. End with a brief conclusion after the list

FORMATTING REQUIREMENTS:
- Use a simple format like "1. File Name" or "- File Name"
- Do NOT include any technical IDs or namespaces
- Do NOT use complex formatting like "**File Name:** - namespace:1234"
- Keep your response clean, simple, and easy to read
- For numbered lists, use the format: "1. File Name"
- For bullet points, use the format: "- File Name"
- Format your response according to EnhancedMarkdownContent.txt guidelines
- Use headings (## for main sections, ### for subsections) to organize information
- Use **bold** for emphasis on important points

Your response should be well-formatted, grammatically correct, and focused on providing a complete list of documents.
`;
      } else {
        // Standard prompt for other queries
        systemPrompt = `
You are an AI assistant that answers questions based on the provided document content.
Use the following content to answer the user's question. If the content doesn't contain
relevant information to answer the question, acknowledge that and provide a general response
based on your knowledge.

DOCUMENT CONTENT:
${content}

USER QUERY:
${query}

INSTRUCTIONS:
1. Provide a clear, concise, and accurate response based on the document content
2. Format your response in proper markdown
3. DO NOT include any technical information like namespace IDs or internal identifiers
4. Make sure your response is grammatically correct and well-structured

FORMATTING REQUIREMENTS:
- Use clean, simple markdown formatting
- DO NOT include any technical IDs, UUIDs, or namespace identifiers
- If you mention file names, use a simple format like "File Name" without technical details
- Keep your response professional and easy to read
- Use proper headings, lists, and paragraphs as appropriate
- Avoid complex nested formatting
- Format your response according to EnhancedMarkdownContent.txt guidelines
- Use headings (## for main sections, ### for subsections) to organize information
- Use **bold** for emphasis on important points
- Use bullet points (- ) or numbered lists (1. ) for listing items

Provide a clear, concise, and accurate response based on the document content.
`;
      }

      // Log the type of prompt being used
      console.log(`QueryDocumentsTool: Using ${isListingFilesQuery ? 'file listing' : 'standard'} prompt for query`);

      // Process with Groq
      const response = await processWithGroq({
        prompt: systemPrompt,
        model: "deepseek-r1-distill-llama-70b",
        modelOptions: {
          temperature: modelOptions?.temperature ?? 0.3,
          maxTokens: modelOptions?.maxTokens ?? 3000,
          ...modelOptions
        }
      });

      // Post-process the response to remove any remaining namespace IDs and format with markdown-renderer-tool
      const cleanedResponse = await this.cleanResponseOfNamespaceIds(response);

      return cleanedResponse;
    } catch (error) {
      console.error("Error processing with Groq:", error);
      throw new Error("Failed to process query with Groq");
    }
  }

  /**
   * Handle internet search as fallback when document content is insufficient
   *
   * @param query - User query to search for
   * @returns Search results formatted as a QueryDocumentsResult
   */
  private async handleInternetSearchFallback(
    query: string,
    model?: string,
    modelOptions?: Record<string, any>
  ): Promise<QueryDocumentsResult> {
    try {
      const searchResult = await this.performInternetSearch(query);

      if (!searchResult.content) {
        return {
          success: false,
          content: "",
          error: "No relevant information found from internet search"
        };
      }

      // Process search results with Groq (passing model parameters if provided)
      const processedResponse = await this.processWithGroq(
        query,
        searchResult.content,
        model,
        modelOptions,
        undefined // No category for internet search
      );

      return {
        success: true,
        content: processedResponse,
        sources: searchResult.sources.map(url => ({
          title: url,
          page: 1,
          doc_id: `ext_${this.hashString(url)}`,
          relevance: 0.7
        })),
        metadata: {
          internetSearchUsed: true
        }
      };
    } catch (error) {
      console.error("Error in internet search fallback:", error);
      return {
        success: false,
        content: "",
        error: "Failed to retrieve information from internet search"
      };
    }
  }

  /**
   * Perform internet search for the given query
   *
   * @param query - Search query
   * @returns Search content and sources
   */
  private async performInternetSearch(query: string): Promise<{content: string, sources: string[]}> {
    try {
      // Simple implementation for now
      // In a real implementation, this would call an actual search API
      console.log(`Performing internet search for: ${query}`);

      // Mock search results with more realistic content
      const mockContent = `
[NOTE: The requested information was not found in the internal knowledge base.
The following information has been sourced from external internet searches.]

[Source 1] Understanding Document Queries
URL: https://example.com/document-queries
Content: Document queries allow users to search through their uploaded documents using natural language. The system converts the query to a vector representation and finds similar content in the document collection.

[Source 2] Vector Search Explained
URL: https://example.com/vector-search
Content: Vector search works by converting text into numerical representations (vectors) and finding similar vectors in a database. This allows for semantic search rather than just keyword matching.

[Source 3] Best Practices for Document Management
URL: https://example.com/document-management
Content: Organizing documents by category helps improve search results. Regular updates to document collections ensure that the information remains relevant and accurate.
`;

      return {
        content: mockContent,
        sources: [
          'https://example.com/document-queries',
          'https://example.com/vector-search',
          'https://example.com/document-management'
        ]
      };
    } catch (error) {
      console.error("Error performing internet search:", error);
      throw new Error("Failed to perform internet search");
    }
  }

  /**
   * Validate input parameters
   *
   * @param options - Input options to validate
   */
  private validateInput(options: QueryDocumentsOptions): void {
    if (!options.query) {
      throw new Error("Query is required");
    }
    if (!options.userId) {
      throw new Error("UserId is required");
    }
    if (!options.category && !options.filename && !options.namespace) {
      throw new Error("Either category, filename, or namespace is required");
    }
  }

  /**
   * Create a simple hash of a string for use in document IDs
   *
   * @param str - String to hash
   * @returns Simple hash string
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Clean response of namespace IDs and other technical information
   * and format it using the markdown-renderer-tool
   *
   * @param response - The response to clean
   * @returns Cleaned and formatted response
   */
  private async cleanResponseOfNamespaceIds(response: string): Promise<string> {
    // Remove namespace IDs (UUIDs) from the response
    const uuidPattern = /\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi;
    let cleanedResponse = response.replace(uuidPattern, '');

    // Remove lines that contain "namespace:" or "Namespace:"
    cleanedResponse = cleanedResponse.split('\n')
      .filter(line => !line.toLowerCase().includes('namespace:'))
      .join('\n');

    // Clean up any double quotes around filenames
    cleanedResponse = cleanedResponse.replace(/"([^"]+)"/g, '$1');

    // Remove any remaining technical patterns like "- **Namespace:**"
    cleanedResponse = cleanedResponse.replace(/- \*\*Namespace:\*\*.*$/gm, '');

    // Remove lines with just a dash and a UUID or empty content after namespace removal
    cleanedResponse = cleanedResponse.split('\n')
      .filter(line => {
        const trimmedLine = line.trim();
        // Keep lines that have meaningful content
        return trimmedLine !== '-' &&
               trimmedLine !== '- ' &&
               !trimmedLine.match(/^- \s*$/);
      })
      .join('\n');

    // Clean up any double newlines that might have been created
    cleanedResponse = cleanedResponse.replace(/\n\s*\n\s*\n/g, '\n\n');

    // Format file listings better
    // Replace patterns like "1. **File Name:** - " with "1. File Name"
    cleanedResponse = cleanedResponse.replace(/(\d+\.\s+)\*\*([^*]+)\*\*:\s*-?\s*/g, '$1$2');

    // Replace patterns like "- **File Name:** - " with "- File Name"
    cleanedResponse = cleanedResponse.replace(/(-\s+)\*\*([^*]+)\*\*:\s*-?\s*/g, '$1$2');

    // Convert any remaining "**File Name:**" patterns to "File Name:"
    cleanedResponse = cleanedResponse.replace(/\*\*([^*]+)\*\*:/g, '$1:');

    // Process the cleaned response with the markdown-renderer-tool
    try {
      const result = await markdownRendererTool.process({
        markdown: cleanedResponse,
        operation: 'preprocess'
      });

      if (result.success) {
        return result.content;
      } else {
        console.warn("Failed to process markdown with markdown-renderer-tool:", result.error);
        return cleanedResponse; // Fall back to the basic cleaned response
      }
    } catch (error) {
      console.error("Error processing markdown with markdown-renderer-tool:", error);
      return cleanedResponse; // Fall back to the basic cleaned response
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "queryDocumentsTool",
        description: "Search through user documents to find relevant information based on a query",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The search query"
            },
            category: {
              type: "string",
              description: "The document category to search within"
            },
            filename: {
              type: "string",
              description: "The specific filename to search within"
            },
            namespace: {
              type: "string",
              description: "The direct namespace ID to search within (bypasses filename lookup)"
            },
            useInternetSearch: {
              type: "boolean",
              description: "Whether to use internet search as fallback if no relevant documents are found"
            },
            model: {
              type: "string",
              description: "The model to use for processing (e.g., 'deepseek-r1-distill-llama-70b', 'llama-3.3-70b-versatile')"
            },
            modelOptions: {
              type: "object",
              description: "Additional model configuration options like temperature and maxTokens",
              properties: {
                temperature: {
                  type: "number",
                  description: "Temperature for model generation (0.0-1.0)"
                },
                maxTokens: {
                  type: "integer",
                  description: "Maximum number of tokens to generate"
                }
              }
            }
          },
          required: ["query"]
        }
      }
    };
  }
}

/**
 * ContentSelector class for vector-based content selection
 */
class ContentSelector {
  private userId: string;

  constructor(userId: string) {
    this.userId = userId;
  }

  /**
   * Normalizes chunk IDs by removing duplicate segments while preserving the final identifier
   *
   * @param chunkId - Raw chunk identifier that may contain duplicates
   * @returns Cleaned chunk identifier
   */
  private cleanChunkId(chunkId: string): string {
    const parts = chunkId.split('_');
    if (parts.length > 2) {
      const uniqueParts = [...new Set(parts.slice(0, -1))];
      return `${uniqueParts.join('_')}_${parts[parts.length - 1]}`;
    }
    return chunkId;
  }

  /**
   * Fetches document content directly from Firestore as a fallback when vector search fails
   *
   * @param namespaces - Array of namespaces (document IDs) to fetch
   * @returns Array of document content objects with title, content, and metadata
   */
  private async fetchDirectDocumentContent(namespaces: string[]): Promise<Array<{
    docId: string;
    title: string;
    content: string;
    page?: number;
  }>> {
    const results: Array<{
      docId: string;
      title: string;
      content: string;
      page?: number;
    }> = [];

    // Try to fetch each document directly from Firestore
    await Promise.all(namespaces.map(async (namespace) => {
      try {
        console.log(`Attempting to fetch direct content for namespace/filename: ${namespace}`);

        // First approach: Try to get the document from the files collection using namespace field
        const fileByNamespaceSnapshot = await adminDb.collection('users')
          .doc(this.userId)
          .collection('files')
          .where('namespace', '==', namespace)
          .limit(1)
          .get();

        // Second approach: Try to get the document from the files collection using name field (for filename-based requests)
        const fileByNameSnapshot = await adminDb.collection('users')
          .doc(this.userId)
          .collection('files')
          .where('name', '==', namespace)
          .limit(1)
          .get();

        // Use the first approach result if available, otherwise use the second approach
        const fileSnapshot = !fileByNamespaceSnapshot.empty ? fileByNamespaceSnapshot : fileByNameSnapshot;

        if (!fileSnapshot.empty) {
          const fileDoc = fileSnapshot.docs[0];
          const fileData = fileDoc.data();
          const actualNamespace = fileData.namespace || namespace;

          console.log(`Found file metadata for ${namespace}, actual namespace: ${actualNamespace}`);

          // Now try to get the actual content from the raw_content collection
          const contentSnapshot = await adminDb.collection('users')
            .doc(this.userId)
            .collection('raw_content')
            .doc(actualNamespace)
            .get();

          if (contentSnapshot.exists) {
            const contentData = contentSnapshot.data();
            console.log(`Found raw content for namespace ${actualNamespace}`);
            results.push({
              docId: actualNamespace,
              title: fileData.name || `Document ${namespace}`,
              content: contentData?.content || contentData?.text || '',
              page: 1
            });
          } else {
            // If no raw_content, try to get content from the file document itself
            if (fileData.content) {
              console.log(`Using content from file document for ${namespace}`);
              results.push({
                docId: actualNamespace,
                title: fileData.name || `Document ${namespace}`,
                content: fileData.content,
                page: 1
              });
            }
          }
        } else {
          // If we can't find the file metadata, try to get content directly from raw_content
          console.log(`No file metadata found, trying raw_content directly for ${namespace}`);
          const directContentSnapshot = await adminDb.collection('users')
            .doc(this.userId)
            .collection('raw_content')
            .doc(namespace)
            .get();

          if (directContentSnapshot.exists) {
            const contentData = directContentSnapshot.data();
            console.log(`Found direct raw content for ${namespace}`);
            results.push({
              docId: namespace,
              title: `Document ${namespace}`,
              content: contentData?.content || contentData?.text || '',
              page: 1
            });
          } else {
            console.log(`No content found for ${namespace} in raw_content collection`);
          }
        }
      } catch (error) {
        console.error(`Error fetching direct content for namespace ${namespace}:`, error);
      }
    }));

    return results;
  }

  /**
   * Selects and processes content across multiple namespaces based on vector similarity
   *
   * @param queryVector - Vector representation of the query
   * @param namespaces - Array of namespaces to search within
   * @returns Processed content with metadata or null if no relevant content found
   */
  /**
   * Fetch document chunks from bytestore collection for a namespace
   *
   * @param namespace - The namespace to fetch chunks for
   * @returns Array of document chunks
   */
  /**
   * Queries a specific namespace in Pinecone for similar vectors
   *
   * @param namespace - Namespace to query within Pinecone
   * @param queryVector - Vector to find similarities against
   * @returns Array of matching results with metadata
   */
  private async queryNamespace(
    namespace: string,
    queryVector: number[]
  ): Promise<any[]> {
    try {
      console.log(`Querying namespace: ${namespace}`);

      // Get the Pinecone index from the vector embedding tool
      const pineconeIndex = await vectorEmbeddingTool.getPineconeIndex();

      if (!pineconeIndex) {
        console.error("Failed to get Pinecone index");
        return [];
      }

      const queryResponse = await pineconeIndex.namespace(namespace).query({
        vector: queryVector,
        topK: 5,  // Retrieve top 5 most similar vectors
        includeValues: true,
        includeMetadata: true,
      });

      // Get file information for this namespace
      let fileName = `Document from ${namespace}`;
      try {
        const filesRef = adminDb.collection(`users/${this.userId}/files`);
        const fileQuery = filesRef.where('namespace', '==', namespace);
        const fileSnapshot = await fileQuery.get();

        if (!fileSnapshot.empty) {
          const fileData = fileSnapshot.docs[0].data();
          fileName = fileData.name || fileName;
          console.log(`ContentSelector: Found file "${fileName}" for namespace ${namespace}`);
        }
      } catch (fileError) {
        console.error(`ContentSelector: Error fetching file info for namespace ${namespace}:`, fileError);
      }

      // Clean and normalize chunk IDs in the response
      const mappedMatches = (queryResponse.matches || []).map((match: any) => {
        // Include file information in the content
        let content = match.metadata?.content || "";
        content = `File Information: This content is from file "${fileName}"\n\n${content}`;

        return {
          ...match,
          metadata: {
            ...match.metadata,
            chunkId: this.cleanChunkId(
              match.metadata?.chunkId ?
              match.metadata.chunkId.toString() :
              `${namespace}_${match.id}`
            ),
            content: content,
            title: fileName,
            namespace: namespace,
            doc_id: namespace // Ensure doc_id is set to the namespace
          }
        };
      });

      mappedMatches.forEach((match: any) => {
        console.log(`  - Chunk ID from ${namespace}: ${match.metadata.chunkId}`);
      });

      // If no matches found in Pinecone, try to fetch from bytestore
      if (mappedMatches.length === 0) {
        console.log(`No matches found in Pinecone for namespace ${namespace}, trying bytestore`);
        return await this.fetchChunksFromBytestore(namespace);
      }

      return mappedMatches;
    } catch (error) {
      console.error(`Error querying namespace ${namespace}:`, error);

      // If Pinecone query fails, try to fetch from bytestore
      console.log(`Error querying Pinecone for namespace ${namespace}, trying bytestore`);
      return await this.fetchChunksFromBytestore(namespace);
    }
  }

  private async fetchChunksFromBytestore(namespace: string): Promise<any[]> {
    try {
      console.log(`ContentSelector: Fetching chunks from bytestore for namespace ${namespace}`);

      // First, try to get the file information for this namespace
      let fileName = `Document from ${namespace}`;
      try {
        const filesRef = adminDb.collection(`users/${this.userId}/files`);
        const fileQuery = filesRef.where('namespace', '==', namespace);
        const fileSnapshot = await fileQuery.get();

        if (!fileSnapshot.empty) {
          const fileData = fileSnapshot.docs[0].data();
          fileName = fileData.name || fileName;
          console.log(`ContentSelector: Found file "${fileName}" for namespace ${namespace}`);
        }
      } catch (fileError) {
        console.error(`ContentSelector: Error fetching file info for namespace ${namespace}:`, fileError);
      }

      // Query the bytestore collection for chunks with the given namespace as doc_id
      const bytestoreRef = adminDb.collection(`users/${this.userId}/byteStoreCollection`);
      const query = bytestoreRef.where('metadata.doc_id', '==', namespace);
      const snapshot = await query.get();

      console.log(`ContentSelector: Found ${snapshot.docs.length} chunks in bytestore for namespace ${namespace}`);

      // Extract the chunks from the query results
      const chunks = snapshot.docs.map(doc => {
        const data = doc.data();

        // Include file information in the content
        let content = data.content || data.pageContent || "";
        content = `File Information: This content is from file "${fileName}"\n\n${content}`;

        return {
          id: doc.id,
          score: 0.8, // Default score since we're fetching directly
          metadata: {
            ...data.metadata,
            chunkId: doc.id,
            content: content,
            title: fileName,
            page: data.metadata?.page || 1,
            namespace: namespace,
            doc_id: namespace // Ensure doc_id is set to the namespace
          }
        };
      });

      return chunks;
    } catch (error) {
      console.error(`ContentSelector: Error fetching chunks from bytestore for namespace ${namespace}:`, error);
      return [];
    }
  }

  public async selectContent(
    queryVector: number[],
    namespaces: string[]
  ): Promise<ContentSelectionResult | null> {
    try {
      console.log(`ContentSelector: Processing query for user ${this.userId} across ${namespaces.length} namespaces`);

      if (namespaces.length === 0) {
        console.warn("No namespaces provided for content selection");
        return null;
      }

      // Initialize the vector embedding tool
      if (!vectorEmbeddingTool.isInitialized()) {
        await vectorEmbeddingTool.initialize();
      }

      // Get the Pinecone index from the vector embedding tool
      const pineconeIndex = await vectorEmbeddingTool.getPineconeIndex();

      if (!pineconeIndex) {
        console.error("Failed to get Pinecone index");
        return null;
      }

      // Log each namespace being queried
      console.log(`ContentSelector: Querying the following namespaces:`, namespaces);

      // Query all specified namespaces in parallel
      const queryResults = await Promise.all(
        namespaces.map(namespace => this.queryNamespace(namespace, queryVector))
      );

      // Flatten and process results
      const allResults = queryResults.flat();

      // Log the number of results from each namespace
      const resultsByNamespace = namespaces.map((namespace, index) => {
        const count = queryResults[index]?.length || 0;
        return { namespace, count };
      });
      console.log("ContentSelector: Results by namespace:", resultsByNamespace);
      console.log(`ContentSelector: Total results across all namespaces: ${allResults.length}`);

      if (allResults.length === 0) {
        console.warn("No search results found across namespaces");

        // Try to fetch document content directly from Firestore as a fallback
        try {
          console.log("Attempting to fetch document content directly from Firestore as fallback");

          // Try to get document content directly using the namespace as the document ID
          const directContent = await this.fetchDirectDocumentContent(namespaces);

          if (directContent && directContent.length > 0) {
            console.log("Successfully retrieved direct document content from Firestore");

            // Create sources from the direct content
            const directSources = directContent.map((doc, index) => ({
              title: doc.title || `Document ${index + 1}`,
              page: doc.page || 1,
              doc_id: doc.docId,
              relevance: 0.8 // Assign a reasonable relevance score
            }));

            // Combine all content
            const combinedContent = directContent.map(doc => doc.content).join("\n\n");

            return {
              content: combinedContent,
              metadata: {
                sources: directSources,
                totalTokens: combinedContent.length / 4, // Rough estimate of tokens
                chunkCount: directContent.length,
                averageRelevance: 0.8,
                namespaceDistribution: namespaces.reduce((acc, namespace) => {
                  acc[namespace] = 1; // Each namespace contributes one document
                  return acc;
                }, {} as Record<string, number>)
              }
            };
          }
        } catch (fallbackError) {
          console.error("Error in direct document fallback:", fallbackError);
        }

        // If direct fetch also failed, create a fallback result with basic information
        const fallbackContent = `No specific content found for the query in the selected documents.

The search was performed across the following namespaces:
${namespaces.join('\n')}

Please try a different query or select different documents.`;

        const fallbackSources = namespaces.map((namespace, index) => ({
          title: `Document ${index + 1}`,
          page: 1,
          doc_id: namespace,
          relevance: 0.5
        }));

        return {
          content: fallbackContent,
          metadata: {
            sources: fallbackSources,
            totalTokens: fallbackContent.length / 4,
            chunkCount: 0,
            averageRelevance: 0.5,
            namespaceDistribution: namespaces.reduce((acc, namespace) => {
              acc[namespace] = 0;
              return acc;
            }, {} as Record<string, number>)
          }
        };
      }

      // Extract content from results
      const content = allResults
        .map((result: any) => result.metadata.content || "")
        .filter(Boolean)
        .join("\n\n");

      // Create sources from results
      const sources: Source[] = allResults.map((result: any, index: number) => ({
        title: result.metadata.title || `Document ${index + 1}`,
        page: result.metadata.page || 1,
        doc_id: result.id,
        relevance: result.score
      }));

      // Calculate average relevance
      const averageRelevance = sources.reduce((sum, source) => sum + source.relevance, 0) / sources.length;

      // Create namespace distribution
      const namespaceDistribution = namespaces.reduce((acc, namespace) => {
        const count = allResults.filter((result: any) => result.metadata.namespace === namespace).length;
        if (count > 0) {
          acc[namespace] = count;
        }
        return acc;
      }, {} as Record<string, number>);

      // Create the final result
      const result = {
        content,
        metadata: {
          sources,
          totalTokens: content.length / 4, // Rough estimate of tokens
          chunkCount: allResults.length,
          averageRelevance,
          namespaceDistribution
        }
      };

      // Log the final result metadata
      console.log("ContentSelector: Final result metadata:", {
        contentLength: content.length,
        sourcesCount: sources.length,
        chunkCount: allResults.length,
        averageRelevance,
        namespaceDistribution
      });

      return result;
    } catch (error) {
      console.error("Error in content selection:", error);
      return null;
    }
  }
}

// Export a singleton instance for easy import
export const queryDocumentsTool = new QueryDocumentsTool();
