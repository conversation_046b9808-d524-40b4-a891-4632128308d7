/**
 * Document Processing Queue
 *
 * This module implements an asynchronous job queue for document processing tasks.
 * It allows long-running document processing operations to be performed in the background
 * without blocking the main request/response cycle.
 */

import { v4 as uuidv4 } from 'uuid';

// Job status enum
export enum JobStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// Job priority enum
export enum JobPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  URGENT = 3
}

// Document processing job interface
export interface DocumentProcessingJob {
  id: string;
  status: JobStatus;
  priority: JobPriority;
  createdAt: Date;
  updatedAt: Date;  // Added missing property
  startedAt?: Date;
  completedAt?: Date;
  progress?: number;
  attempts?: number; // Added missing property
  error?: string;

  // Job parameters
  userId: string;
  documentQuery: string;
  category?: string;
  filename?: string;
  namespace?: string;
  useInternetSearch?: boolean;
  modelName?: string;

  // Job results
  result?: any;

  // Callback information
  callbackUrl?: string;
  callbackHeaders?: Record<string, string>;
}

// In-memory job storage (would be replaced with a database in production)
const jobStore: Record<string, DocumentProcessingJob> = {};

// Simple in-memory queue implementation
class DocumentProcessingQueue {
  private queue: string[] = [];
  private processing: boolean = false;
  private maxConcurrent: number = 2;
  private currentlyProcessing: number = 0;
  private processors: Map<string, (job: DocumentProcessingJob) => Promise<any>> = new Map();

  /**
   * Check if a processor is registered for a specific job type
   *
   * @param jobType - The type of job to check
   * @returns True if a processor is registered, false otherwise
   */
  public isProcessorRegistered(jobType: string): boolean {
    return this.processors.has(jobType);
  }

  /**
   * Add a job to the queue
   *
   * @param jobData - The job data
   * @returns The created job
   */
  public enqueue(jobData: Omit<DocumentProcessingJob, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'attempts'>): DocumentProcessingJob {
    const id = uuidv4();
    const now = new Date();
    const job: DocumentProcessingJob = {
      id,
      status: JobStatus.PENDING,
      createdAt: now,
      updatedAt: now,
      attempts: 0,
      progress: 0,
      ...jobData,
      // Ensure priority has a default value if not provided in jobData
      priority: jobData.priority || JobPriority.NORMAL
    };

    jobStore[id] = job;

    // Add to queue based on priority
    if (job.priority === JobPriority.URGENT) {
      this.queue.unshift(id); // Add to front of queue
    } else {
      this.queue.push(id); // Add to end of queue
    }

    // Start processing if not already running
    if (!this.processing) {
      this.processQueue();
    }

    return job;
  }

  /**
   * Register a processor function for a specific job type
   *
   * @param jobType - The type of job to process
   * @param processor - The processor function
   */
  public registerProcessor(jobType: string, processor: (job: DocumentProcessingJob) => Promise<any>): void {
    this.processors.set(jobType, processor);
  }

  /**
   * Get a job by ID
   *
   * @param id - The job ID
   * @returns The job or undefined if not found
   */
  public getJob(id: string): DocumentProcessingJob | undefined {
    return jobStore[id];
  }

  /**
   * Update a job's status and other properties
   *
   * @param id - The job ID
   * @param updates - The updates to apply
   * @returns The updated job or undefined if not found
   */
  public updateJob(id: string, updates: Partial<DocumentProcessingJob>): DocumentProcessingJob | undefined {
    const job = jobStore[id];
    if (!job) return undefined;

    Object.assign(job, updates);
    return job;
  }

  /**
   * Cancel a job
   *
   * @param id - The job ID
   * @returns Promise that resolves to true if the job was cancelled, false otherwise
   */
  public async cancelJob(id: string): Promise<boolean> {
    const job = jobStore[id];
    if (!job || job.status !== JobStatus.PENDING) return false;

    job.status = JobStatus.CANCELLED;
    job.updatedAt = new Date();
    job.completedAt = new Date();

    // Remove from queue if still there
    const index = this.queue.indexOf(id);
    if (index !== -1) {
      this.queue.splice(index, 1);
    }

    return true;
  }

  /**
   * Process the queue
   * This method processes jobs in the queue concurrently up to maxConcurrent
   */
  private async processQueue(): Promise<void> {
    this.processing = true;

    while (this.queue.length > 0 && this.currentlyProcessing < this.maxConcurrent) {
      const jobId = this.queue.shift();
      if (!jobId) continue;

      const job = jobStore[jobId];
      if (!job || job.status !== JobStatus.PENDING) continue;

      this.currentlyProcessing++;
      job.status = JobStatus.PROCESSING;
      job.startedAt = new Date();

      // Process the job asynchronously
      this.processJob(job).finally(() => {
        this.currentlyProcessing--;
        // Continue processing the queue if there are more jobs
        if (this.queue.length > 0) {
          this.processQueue();
        } else if (this.currentlyProcessing === 0) {
          this.processing = false;
        }
      });
    }

    if (this.queue.length === 0 && this.currentlyProcessing === 0) {
      this.processing = false;
    }
  }

  /**
   * Process a single job
   *
   * @param job - The job to process
   */
  private async processJob(job: DocumentProcessingJob): Promise<void> {
    try {
      // Increment attempts counter
      job.attempts = (job.attempts || 0) + 1;
      job.updatedAt = new Date();

      // Get the appropriate processor for this job
      const processor = this.processors.get('documentProcessing');
      if (!processor) {
        throw new Error('No processor registered for document processing jobs');
      }

      // Process the job
      const result = await processor(job);

      // Update the job with the result
      job.status = JobStatus.COMPLETED;
      job.completedAt = new Date();
      job.updatedAt = new Date();
      job.result = result;
      job.progress = 100;

      // Call the callback URL if provided
      if (job.callbackUrl) {
        await this.sendCallback(job);
      }
    } catch (error) {
      console.error(`Error processing job ${job.id}:`, error);

      // Update the job with the error
      job.status = JobStatus.FAILED;
      job.completedAt = new Date();
      job.updatedAt = new Date();
      job.error = error instanceof Error ? error.message : String(error);

      // Call the callback URL with the error if provided
      if (job.callbackUrl) {
        await this.sendCallback(job);
      }
    }
  }

  /**
   * Send a callback to the provided URL
   *
   * @param job - The job to send a callback for
   */
  private async sendCallback(job: DocumentProcessingJob): Promise<void> {
    if (!job.callbackUrl) return;

    try {
      const response = await fetch(job.callbackUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(job.callbackHeaders || {})
        },
        body: JSON.stringify({
          jobId: job.id,
          status: job.status,
          result: job.result,
          error: job.error
        })
      });

      if (!response.ok) {
        console.error(`Failed to send callback for job ${job.id}: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error(`Error sending callback for job ${job.id}:`, error);
    }
  }
}

// Create and export a singleton instance
export const documentProcessingQueue = new DocumentProcessingQueue();

// Export default for convenience
export default documentProcessingQueue;
