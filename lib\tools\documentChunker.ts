/**
 * Document Chunking Utility
 *
 * This utility provides functions to split large documents into manageable chunks
 * for more efficient processing by AI models.
 */

/**
 * Options for document chunking
 */
export interface ChunkingOptions {
  /**
   * Maximum size of each chunk in characters
   * Default: 8000 characters
   */
  maxChunkSize?: number;

  /**
   * Overlap between chunks in characters to maintain context
   * Default: 200 characters
   */
  overlapSize?: number;

  /**
   * Whether to try to preserve paragraph boundaries
   * Default: true
   */
  preserveParagraphs?: boolean;

  /**
   * Whether to try to preserve section boundaries (e.g., markdown headings)
   * Default: true
   */
  preserveSections?: boolean;

  /**
   * Whether to include metadata in each chunk
   * Default: true
   */
  includeMetadata?: boolean;
}

/**
 * A document chunk with content and metadata
 */
export interface DocumentChunk {
  /**
   * The content of the chunk
   */
  content: string;

  /**
   * Metadata about the chunk
   */
  metadata: {
    /**
     * The index of the chunk in the document
     */
    chunkIndex: number;

    /**
     * The total number of chunks in the document
     */
    totalChunks: number;

    /**
     * The start position of the chunk in the original document
     */
    startPosition: number;

    /**
     * The end position of the chunk in the original document
     */
    endPosition: number;

    /**
     * The title of the document
     */
    documentTitle?: string;

    /**
     * The ID of the document
     */
    documentId?: string;
  };
}

/**
 * Split a document into chunks based on the provided options
 *
 * @param document - The document content to split
 * @param options - Chunking options
 * @returns An array of document chunks
 */
/**
 * Get optimal chunk size based on model name
 *
 * @param modelName - The name of the model
 * @returns The optimal chunk size in characters
 */
export function getOptimalChunkSize(modelName?: string): number {
  if (!modelName) return 8000; // Default chunk size

  // Normalize the model name for consistent matching
  const normalizedModel = modelName.toLowerCase().trim();

  // Specific Groq models
  const groqModels = [
    'llama-3.3-70b-versatile',
    'deepseek-r1-distill-llama-70b',
    'meta-llama/llama-4-maverick-17b-128e-instruct',
    'llama 3.3 70b versatile',
    'deepseek r1 distill llama 70b'
  ];

  // Groq models (Llama models)
  if (groqModels.some(model => normalizedModel.includes(model.toLowerCase())) ||
      normalizedModel.includes('meta-llama/llama-4')) {
    return 10000;  // Groq models generally have good throughput
  }

  // OpenAI models
  if (normalizedModel.startsWith('gpt-') ||
      normalizedModel.startsWith('o3-') ||
      normalizedModel.startsWith('o1-') ||
      normalizedModel === 'gpt-4o' ||
      normalizedModel.includes('o3-2025') ||
      normalizedModel.includes('o1-mini')) {
    return 12000;  // OpenAI models can handle larger chunks
  }

  // Claude models
  if (normalizedModel.startsWith('claude') ||
      normalizedModel.includes('claude 3')) {
    return 10000;  // Claude models have large context windows
  }

  // Gemini models
  if (normalizedModel.includes('gemini')) {
    return 16000;  // Gemini models have very large context windows
  }

  // DeepSeek models (that aren't specifically served through Groq)
  if (normalizedModel.includes('deepseek') &&
      !normalizedModel.includes('deepseek-r1-distill-llama-70b') &&
      !normalizedModel.includes('deepseek r1 distill llama 70b')) {
    return 6000;  // DeepSeek models have smaller context windows
  }

  // Llama models (not in the Groq-specific list)
  if (normalizedModel.includes('llama')) {
    return 8000;  // Standard Llama models have medium context windows
  }

  // Default for other models
  return 8000;
}

export function chunkDocument(
  document: string,
  documentTitle?: string,
  documentId?: string,
  options?: ChunkingOptions,
  modelName?: string
): DocumentChunk[] {
  // Set default options
  const {
    maxChunkSize: userSpecifiedChunkSize,
    overlapSize = 200,
    preserveParagraphs = true,
    preserveSections = true,
    includeMetadata = true
  } = options || {};

  // Use model-specific chunk size if not explicitly specified by user
  const maxChunkSize = userSpecifiedChunkSize || getOptimalChunkSize(modelName);

  // If document is smaller than max chunk size, return it as a single chunk
  if (document.length <= maxChunkSize) {
    return [{
      content: document,
      metadata: {
        chunkIndex: 0,
        totalChunks: 1,
        startPosition: 0,
        endPosition: document.length,
        documentTitle,
        documentId
      }
    }];
  }

  const chunks: DocumentChunk[] = [];
  let currentPosition = 0;

  // Find section boundaries (markdown headings)
  const sectionBoundaries: number[] = [];
  if (preserveSections) {
    const headingRegex = /^#{1,6}\s+.+$/gm;
    let match;
    while ((match = headingRegex.exec(document)) !== null) {
      sectionBoundaries.push(match.index);
    }
  }

  // Process document until we've covered the entire content
  while (currentPosition < document.length) {
    let chunkEnd = currentPosition + maxChunkSize;

    // Don't go beyond the document length
    if (chunkEnd > document.length) {
      chunkEnd = document.length;
    }

    // Try to preserve paragraph boundaries
    if (preserveParagraphs && chunkEnd < document.length) {
      // Look for paragraph breaks (double newlines)
      const paragraphBreak = document.lastIndexOf('\n\n', chunkEnd);
      if (paragraphBreak > currentPosition && paragraphBreak > chunkEnd - 500) {
        chunkEnd = paragraphBreak + 2; // Include the double newline
      } else {
        // If no paragraph break, look for single newline
        const newlineBreak = document.lastIndexOf('\n', chunkEnd);
        if (newlineBreak > currentPosition && newlineBreak > chunkEnd - 200) {
          chunkEnd = newlineBreak + 1; // Include the newline
        }
      }
    }

    // Try to preserve section boundaries
    if (preserveSections && chunkEnd < document.length) {
      // Find the nearest section boundary before the chunk end
      let nearestBoundary = -1;
      for (const boundary of sectionBoundaries) {
        if (boundary > currentPosition && boundary < chunkEnd) {
          nearestBoundary = boundary;
        } else if (boundary >= chunkEnd) {
          break;
        }
      }

      // If we found a section boundary, use it as the chunk end
      if (nearestBoundary !== -1) {
        chunkEnd = nearestBoundary;
      }
    }

    // Extract the chunk content
    const chunkContent = document.substring(currentPosition, chunkEnd);

    // Add the chunk to the result
    chunks.push({
      content: chunkContent,
      metadata: {
        chunkIndex: chunks.length,
        totalChunks: 0, // Will be updated later
        startPosition: currentPosition,
        endPosition: chunkEnd,
        documentTitle,
        documentId
      }
    });

    // Move to the next chunk, accounting for overlap
    currentPosition = chunkEnd - overlapSize;

    // Make sure we're making progress
    if (currentPosition <= 0 || currentPosition >= document.length) {
      break;
    }
  }

  // Update total chunks in metadata
  chunks.forEach(chunk => {
    chunk.metadata.totalChunks = chunks.length;
  });

  return chunks;
}

/**
 * Reassemble chunks into a complete document
 *
 * @param chunks - The document chunks to reassemble
 * @returns The reassembled document
 */
export function reassembleChunks(chunks: DocumentChunk[]): string {
  // Sort chunks by index
  const sortedChunks = [...chunks].sort((a, b) =>
    a.metadata.chunkIndex - b.metadata.chunkIndex
  );

  // Reassemble the document
  let result = '';
  let lastEndPosition = 0;

  for (const chunk of sortedChunks) {
    // Skip overlapping content
    if (chunk.metadata.startPosition < lastEndPosition) {
      result += chunk.content.substring(lastEndPosition - chunk.metadata.startPosition);
    } else {
      result += chunk.content;
    }

    lastEndPosition = chunk.metadata.endPosition;
  }

  return result;
}
