/**
 * Prompt Optimizer Tool
 *
 * This tool takes an original prompt and uses Claude 4.0 to optimize it with a two-step process:
 * 1. Infer an optimization goal for the prompt
 * 2. Optimize the prompt based on the inferred goal
 *
 * It uses Claude 4.0 as the primary model with Google AI as a fallback.
 */

import { processWithClaude } from './claude-ai';
import { processWithGoogleAI } from './google-ai';

// Define interfaces for the tool - maintain compatibility with existing PromptOptimizerResult
export interface SimplePromptOptimizerOptions {
  originalPrompt: string;
  criteria?: string[];  // Optional but maintained for compatibility
  includeExplanation?: boolean;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

export interface SimplePromptOptimizerResult {
  success: boolean;
  optimizedPrompt: string;
  explanation?: string;
  error?: string;
}

export class SimplePromptOptimizer {
  /**
   * Static description of the tool and its usage
   */
  static description = {
    name: "optimizePrompt",
    description: "Optimizes a prompt using Claude 4.0 with Google AI as fallback.",
    parameters: {
      type: "object",
      properties: {
        originalPrompt: {
          type: "string",
          description: "The original prompt to optimize."
        },
        temperature: {
          type: "number",
          description: "Controls randomness in the output. Lower values are more deterministic.",
          default: 0.5
        },
        maxTokens: {
          type: "number",
          description: "Maximum number of tokens to generate.",
          default: 4000
        }
      },
      required: ["originalPrompt"]
    },
    returns: {
      type: "object",
      properties: {
        success: {
          type: "boolean",
          description: "Whether the operation was successful."
        },
        optimizedPrompt: {
          type: "string",
          description: "The optimized prompt."
        },
        explanation: {
          type: "string",
          description: "Explanation of changes if requested and successful."
        },
        error: {
          type: "string",
          description: "Error message if the operation failed."
        }
      }
    }
  };

  /**
   * Infers an optimization goal for a given prompt using Claude 4.0.
   * @param originalPrompt The prompt for which to infer an optimization goal.
   * @param modelOptions Additional model options like temperature.
   * @returns A string representing the inferred optimization goal.
   * @throws Error if goal inference fails after fallbacks.
   */
  async inferOptimizationGoal(originalPrompt: string, modelOptions: any = {}): Promise<string | null> {
    console.log("\n--- Inferring Optimization Goal using Claude ---");
    console.log("Original Prompt for Goal Inference:", originalPrompt);

    const systemPrompt = "You are an AI assistant that helps users refine their prompts. Given the following user prompt, suggest a concise optimization goal. For example, if the prompt is 'Write a story about a cat,' a good goal might be 'Make it more specific and set in a futuristic city.' or 'Make it funnier and for a younger audience.' or 'Focus on the cat's perspective and internal thoughts.' Return *only* the suggested optimization goal, without any other explanatory text or preamble.";
    const userPrompt = `Analyze the following prompt and suggest an optimization goal:\n"${originalPrompt}"`;

    let claudeError: any = null;
    try {
      // Try with Claude 4.0 first
      const response = await processWithClaude({
        prompt: userPrompt,
        model: "claude-sonnet-4-0",
        modelOptions: {
          temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.5,
          maxTokens: 150, // Goals are usually short
          systemPrompt: systemPrompt
        }
      });

      const inferredGoal = response.trim();
      console.log("Inferred Optimization Goal:", inferredGoal);
      if (inferredGoal) return inferredGoal;
      // If Claude returns an empty goal, treat it as a soft failure to try Google
      claudeError = new Error("Claude returned an empty optimization goal.");

    } catch (error: any) {
      console.error("Error inferring optimization goal with Claude:", error.message);
      claudeError = error;
    }

    // Fallback to Google AI if Claude failed or returned empty
    try {
      console.log("Falling back to Google AI for goal inference");
      const fallbackPrompt = `${systemPrompt}\n\n${userPrompt}`;
      const response = await processWithGoogleAI({
        prompt: fallbackPrompt,
        model: "gemini-1.5-pro-latest"
      });

      const inferredGoal = response.trim();
      console.log("Inferred Optimization Goal (Google AI fallback):", inferredGoal);
      if (inferredGoal) return inferredGoal;
      throw new Error("Google AI also returned an empty optimization goal.");

    } catch (fallbackError: any) {
      console.error("Error inferring optimization goal with Google AI fallback:", fallbackError.message);
      // Throw an error that includes context from both attempts
      const claudeErrorMessage = claudeError ? claudeError.message : "Claude primary attempt failed silently or was skipped.";
      throw new Error(`Failed to infer optimization goal using both Claude and Google AI. Last error (Google AI): ${fallbackError.message}. Claude error: ${claudeErrorMessage}`);
    }
  }


  /**
   * Optimizes a given prompt using Claude 4.0, based on an inferred optimization goal.
   * @param originalPrompt The prompt to be optimized.
   * @param inferredGoal The inferred optimization goal.
   * @param includeExplanation Whether to include an explanation of changes.
   * @param modelOptions Additional model options.
   * @returns The optimized prompt and optional explanation.
   * @throws Error if optimization fails after fallbacks.
   */
  async generateOptimizedPrompt(
    originalPrompt: string,
    inferredGoal: string | null,
    includeExplanation: boolean = false,
    modelOptions: any = {}
  ): Promise<{ optimizedPrompt: string; explanation?: string }> {
    console.log("\n--- Optimizing Prompt with Inferred Goal using Claude ---");
    console.log("Original Prompt for Optimization:", originalPrompt);
    console.log("Using Inferred Goal:", inferredGoal);

    const optimizationInstructions = inferredGoal ||
      "Make it clear and specific, add appropriate context, structure it for a comprehensive response, ensure it communicates all necessary requirements, and make it grammatically correct and well-formed.";

    const systemPrompt = "You are a prompt optimization assistant. Your task is to rewrite the given prompt according to the provided optimization instructions.";
    const userPrompt = `Optimize the following prompt:\n"${originalPrompt}"\n\nOptimization Instructions: ${optimizationInstructions}\n\n${
      includeExplanation
        ? "Please provide both the optimized prompt and an explanation of your changes. Structure your response with 'OPTIMIZED PROMPT:' followed by the prompt, and then 'EXPLANATION:' followed by the explanation."
        : "Return *only* the optimized prompt, without any other explanatory text or preamble."
    }`;

    let claudeError: any = null;
    try {
      // Try with Claude 4.0 first
      const response = await processWithClaude({
        prompt: userPrompt,
        model: "claude-sonnet-4-0",
        modelOptions: {
          temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.7,
          maxTokens: modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 2000,
          systemPrompt: systemPrompt
        }
      });

      let optimizedPrompt = "";
      let explanation = "";

      if (includeExplanation) {
        const promptMatch = response.match(/(?:OPTIMIZED PROMPT:|Optimized prompt:)\s*([\s\S]*?)(?:\s*(?:EXPLANATION:|Explanation:)|\s*$)/i);
        const explanationMatch = response.match(/(?:EXPLANATION:|Explanation:)\s*([\s\S]*?)$/i);

        optimizedPrompt = promptMatch && promptMatch[1] ? promptMatch[1].trim() : response.trim(); // Fallback to full response if no "OPTIMIZED PROMPT:"
        explanation = explanationMatch && explanationMatch[1] ? explanationMatch[1].trim() : "";
        
        // If "OPTIMIZED PROMPT:" was not found but "EXPLANATION:" was, assume the part before explanation is the prompt
        if (!(promptMatch && promptMatch[1]) && explanationMatch && explanationMatch[0]) {
            optimizedPrompt = response.substring(0, response.indexOf(explanationMatch[0])).trim();
        }

      } else {
        optimizedPrompt = response.trim();
      }
      
      if (optimizedPrompt) { // Ensure we got something
        console.log("Final Optimized Prompt (Claude):", optimizedPrompt);
        return { optimizedPrompt, explanation };
      }
      claudeError = new Error("Claude returned an empty optimized prompt.");


    } catch (error: any) {
      console.error("Error optimizing prompt with Claude:", error.message);
      claudeError = error;
    }
    
    // Fallback to Google AI
    try {
      console.log("Falling back to Google AI for prompt optimization");
      const fallbackPrompt = `${systemPrompt}\n\n${userPrompt}`; // Reconstruct if needed or ensure scope
      const response = await processWithGoogleAI({
        prompt: fallbackPrompt,
        model: "gemini-1.5-pro-latest"
      });

      let optimizedPrompt = "";
      let explanation = "";

      if (includeExplanation) {
        const promptMatch = response.match(/(?:OPTIMIZED PROMPT:|Optimized prompt:)\s*([\s\S]*?)(?:\s*(?:EXPLANATION:|Explanation:)|\s*$)/i);
        const explanationMatch = response.match(/(?:EXPLANATION:|Explanation:)\s*([\s\S]*?)$/i);

        optimizedPrompt = promptMatch && promptMatch[1] ? promptMatch[1].trim() : response.trim();
        explanation = explanationMatch && explanationMatch[1] ? explanationMatch[1].trim() : "";

        if (!(promptMatch && promptMatch[1]) && explanationMatch && explanationMatch[0]) {
            optimizedPrompt = response.substring(0, response.indexOf(explanationMatch[0])).trim();
        }

      } else {
        optimizedPrompt = response.trim();
      }

      if (optimizedPrompt) { // Ensure we got something
          console.log("Final Optimized Prompt (Google AI fallback):", optimizedPrompt);
          return { optimizedPrompt, explanation };
      }
      throw new Error("Google AI also returned an empty optimized prompt.");


    } catch (googleFallbackError: any) {
      console.error("Error optimizing prompt with Google AI fallback:", googleFallbackError.message);
      const claudeErrorMessage = claudeError ? claudeError.message : "Claude primary attempt failed silently or was skipped.";
      throw new Error(
        `Failed to optimize prompt using both Claude and Google AI. Last error (Google AI): ${googleFallbackError.message}. Claude error: ${claudeErrorMessage}`
      );
    }
  }

  /**
   * Optimize a prompt using the two-step process
   * @param options - Options for prompt optimization
   * @returns - Optimized prompt and optional explanation
   */
  async optimizePrompt(options: SimplePromptOptimizerOptions): Promise<SimplePromptOptimizerResult> {
    const {
      originalPrompt,
      includeExplanation = false,
      modelOptions = {}
    } = options;

    if (!originalPrompt || originalPrompt.trim() === "") {
      return {
        success: false,
        optimizedPrompt: "",
        error: "Original prompt is required and cannot be empty."
      };
    }

    try {
      let inferredGoal: string | null = null;
      try {
        inferredGoal = await this.inferOptimizationGoal(originalPrompt, modelOptions);
      } catch (goalError: any) {
        console.warn(`Could not infer optimization goal: ${goalError.message}. Proceeding with default optimization instructions.`);
        // Do not re-throw, allow proceeding with a null goal (default instructions)
      }

      const { optimizedPrompt, explanation } = await this.generateOptimizedPrompt(
        originalPrompt,
        inferredGoal,
        includeExplanation,
        modelOptions
      );

      if (!optimizedPrompt || optimizedPrompt.trim().length < 10) {
        const message = "The AI model returned a very short or empty optimized prompt. The original prompt is shown instead.";
        return {
          success: true, // Process completed, but result not ideal
          optimizedPrompt: originalPrompt.trim(),
          explanation: includeExplanation ? (explanation ? `${explanation}\n\n${message}` : message) : undefined,
          error: !includeExplanation ? message : undefined
        };
      }

      return {
        success: true,
        optimizedPrompt: optimizedPrompt.trim(),
        ...(includeExplanation && explanation ? { explanation: explanation.trim() } : {})
      };
    } catch (error: any) {
      console.error("Critical error during prompt optimization process:", error);
      return {
        success: false,
        optimizedPrompt: originalPrompt.trim(), // Return original prompt for UI consistency
        error: `Optimization failed: ${error.message || "Unknown error occurred"}`,
        ...(includeExplanation ? {
            explanation: `The optimization process encountered an error: ${error.message || "Unknown error"}. The original prompt is shown.`
        } : {})
      };
    }
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof SimplePromptOptimizer.description {
    return SimplePromptOptimizer.description;
  }
}

// Export a singleton instance
export const simplePromptOptimizer = new SimplePromptOptimizer();