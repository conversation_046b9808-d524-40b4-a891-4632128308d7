'use client';

import React from 'react';
import ChartGenerator from './ChartGenerator';
import { AlertTriangle, Info, Lightbulb, ArrowRight } from 'lucide-react';

/**
 * Dashboard component for displaying multiple visualizations
 * @param {Object} props - Component props
 * @param {Object} props.dashboardConfig - Dashboard configuration object
 */
export default function Dashboard({ dashboardConfig }) {
  // If no dashboard config is provided, show a placeholder
  if (!dashboardConfig) {
    return (
      <div className="p-6 bg-zinc-900 rounded-lg border border-zinc-700 text-zinc-400 text-center">
        <div className="mx-auto mb-4 text-zinc-600 grid place-items-center">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
            <line x1="8" y1="21" x2="16" y2="21"></line>
            <line x1="12" y1="17" x2="12" y2="21"></line>
          </svg>
        </div>
        <h3 className="text-lg font-semibold mb-2 text-zinc-300">No Dashboard Data Available</h3>
        <p>Enter a prompt to generate a dashboard visualization.</p>
      </div>
    );
  }

  // Extract dashboard configuration
  const {
    title,
    subtitle,
    layout = 'grid',
    insights,
    recommendations,
    visualizations = []
  } = dashboardConfig;

  // Determine the layout class based on the specified layout
  const getLayoutClass = () => {
    switch (layout) {
      case 'rows':
        return 'grid grid-cols-1 gap-10';
      case 'columns':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10';
      case 'featured':
        return 'grid grid-cols-1 gap-10';
      case 'grid':
      default:
        return 'grid grid-cols-1 md:grid-cols-2 gap-10';
    }
  };

  // Get the width class for a visualization based on its width property
  const getWidthClass = (width) => {
    if (!width || width >= 1) return 'col-span-full';
    if (width >= 0.66) return 'col-span-full md:col-span-2';
    return 'col-span-1';
  };

  // Render featured layout with one main visualization and supporting ones
  const renderFeaturedLayout = () => {
    const featuredViz = visualizations.find(viz => viz.featured) || visualizations[0];
    const supportingVizs = visualizations.filter(viz => viz !== featuredViz);

    return (
      <>
        {/* Featured visualization */}
        <div className="col-span-full mb-10">
          <ChartGenerator chartConfig={featuredViz} />
        </div>

        {/* Supporting visualizations */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
          {supportingVizs.map(viz => (
            <div key={viz.id} className="col-span-1">
              <ChartGenerator chartConfig={viz} />
            </div>
          ))}
        </div>
      </>
    );
  };

  return (
    <div className="dashboard bg-zinc-950 rounded-lg border border-zinc-800 p-8">
      {/* Dashboard header */}
      <div className="mb-8 text-center">
        <h2 className="text-2xl font-bold text-white">{title}</h2>
        {subtitle && <p className="text-zinc-400 mt-2">{subtitle}</p>}
      </div>

      {/* Insights section */}
      {insights && (
        <div className="mb-10 p-5 bg-blue-900/20 border border-blue-800 rounded-md">
          <div className="flex items-start">
            <Info className="text-blue-400 mr-3 mt-1 flex-shrink-0" size={20} />
            <div>
              <h3 className="text-blue-300 font-semibold mb-2">Key Insights</h3>
              <p className="text-blue-100 text-sm">{insights}</p>
            </div>
          </div>
        </div>
      )}

      {/* Visualizations */}
      <div className={layout === 'featured' ? 'grid grid-cols-1 gap-10' : getLayoutClass()}>
        {layout === 'featured' ? (
          renderFeaturedLayout()
        ) : (
          visualizations.map(viz => (
            <div
              key={viz.id}
              className={getWidthClass(viz.width)}
              style={{ height: viz.height ? `${viz.height}px` : 'auto' }}
            >
              <ChartGenerator chartConfig={viz} />
            </div>
          ))
        )}
      </div>

      {/* Recommendations section */}
      {recommendations && (
        <div className="mt-10 p-5 bg-amber-900/20 border border-amber-800 rounded-md">
          <div className="flex items-start">
            <Lightbulb className="text-amber-400 mr-3 mt-1 flex-shrink-0" size={20} />
            <div>
              <h3 className="text-amber-300 font-semibold mb-3">Recommendations</h3>
              <ul className="text-amber-100 text-sm space-y-3">
                {recommendations.split('\n').map((rec, index) => (
                  <li key={index} className="flex items-start">
                    <ArrowRight className="text-amber-400 mr-2 mt-1 flex-shrink-0" size={14} />
                    <span>{rec}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
