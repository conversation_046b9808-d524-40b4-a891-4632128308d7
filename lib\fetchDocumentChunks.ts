import { FirestoreStore } from "lib/FirestoreStore"; // Adjust the path as necessary
import { Document } from "langchain/document";

/**
 * Fetches document chunks from Firestore based on the provided document IDs.
 * @param docIds - An array of base document IDs (without the "_x" suffix).
 * @returns An array of Document instances.
 */
export async function fetchDocumentChunks(docIds: string[]): Promise<Document[]> {
  const firestoreStore = new FirestoreStore({ collectionPath: "byteStoreCollection" }); // Replace with your Firestore collection path
  const allChunks: Document[] = [];

  // Iterate through each document ID
  for (const docId of docIds) {
    let chunkIndex = 1;

    // Loop to retrieve chunks for the current document ID
    while (true) {
      const chunkDocId = `${docId}_${chunkIndex}`; // Construct the chunk ID with "_x"
      try {
        const chunk = await firestoreStore.mget([chunkDocId]); // Attempt to fetch the chunk

        // If the chunk is found, add it to the list and increment the index
        if (chunk && chunk.length > 0 && chunk[0] !== undefined) {
          allChunks.push(chunk[0]);
          chunkIndex++;
        } else {
          // Exit the loop if no chunk is found
          break;
        }
      } catch (error) {
        console.error(`Error fetching chunk for ID: ${chunkDocId}`, error);
        break; // Exit the loop on error
      }
    }
  }

  // Return all collected chunks
  //console.log("bytestore doc chunks", allChunks)
  return allChunks;
}
