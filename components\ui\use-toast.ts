// Simplified toast implementation
import { useState, useEffect } from 'react';

type ToastProps = {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
};

type ToastState = ToastProps & {
  id: string;
  visible: boolean;
};

let toasts: ToastState[] = [];
let listeners: ((toasts: ToastState[]) => void)[] = [];

const notifyListeners = () => {
  listeners.forEach(listener => listener([...toasts]));
};

export function toast(props: ToastProps) {
  const id = Math.random().toString(36).substring(2, 9);
  const newToast: ToastState = {
    ...props,
    id,
    visible: true,
  };

  toasts.push(newToast);
  notifyListeners();

  // Auto-dismiss after duration
  setTimeout(() => {
    toasts = toasts.filter(t => t.id !== id);
    notifyListeners();
  }, props.duration || 3000);

  return {
    id,
    dismiss: () => {
      toasts = toasts.filter(t => t.id !== id);
      notifyListeners();
    },
    update: (props: ToastProps) => {
      toasts = toasts.map(t => (t.id === id ? { ...t, ...props } : t));
      notifyListeners();
    },
  };
}

export function useToast() {
  const [currentToasts, setCurrentToasts] = useState<ToastState[]>(toasts);

  useEffect(() => {
    const listener = (updatedToasts: ToastState[]) => {
      setCurrentToasts(updatedToasts);
    };

    listeners.push(listener);
    return () => {
      listeners = listeners.filter(l => l !== listener);
    };
  }, []);

  return {
    toast,
    toasts: currentToasts,
    dismiss: (id: string) => {
      toasts = toasts.filter(t => t.id !== id);
      notifyListeners();
    },
    dismissAll: () => {
      toasts = [];
      notifyListeners();
    },
  };
}
