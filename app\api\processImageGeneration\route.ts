import { NextRequest, NextResponse } from 'next/server';
import { adminDb, adminStorage } from 'components/firebase-admin';
import { GenerateImageAgent } from 'components/Agents/GenerateImageAgent';
import { GenerateImageTool } from 'components/tools/generateImageTool';
import { v4 as uuidv4 } from 'uuid';

interface JobData {
  prompt: string;
  originalPrompt?: string;
  refinedPrompt?: string;
  id: string;
  userId: string;
  model?: string;
  size?: string;
  style?: string;
  quality?: string;
  format?: string;
  background?: string;
  compression?: number;
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  let jobData: JobData | undefined;
  try {
    // 1) Parse and validate request
    const { jobId, userId } = await req.json();
    if (!jobId) {
      throw new Error('No jobId provided in request.');
    }
    if (!userId) {
      throw new Error('No userId provided in request.');
    }
    console.log(`Processing job: ${jobId}`);

    // 2) Validate API key
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured in environment variables.');
    }

    // 3) Initialize image generation services
    const imageTool = new GenerateImageTool(apiKey);
    const imageAgent = new GenerateImageAgent({ generateImageTool: imageTool });

    // Get the job document using direct reference
    let jobDoc;
    try {
      const jobRef = adminDb.collection('users').doc(userId).collection('images').doc(jobId);
      jobDoc = await jobRef.get();
      console.log(`Firestore query completed for job: ${jobId}`);

      if (!jobDoc.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }

      jobData = jobDoc.data() as JobData;
      if (!jobData || !jobData.prompt) {
        throw new Error(`No prompt found in job ${jobId}`);
      }
      console.log(`Job data retrieved for user: ${userId}`);
    } catch (error) {
      console.error('Firestore query error:', error);
      throw new Error(`Failed to fetch job: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 4) Generate the image and handle response
    let imageResponse;
    try {
      // Use the refined prompt if available, otherwise use the original prompt
      const promptToUse = jobData.refinedPrompt || jobData.prompt;
      console.log(`Using prompt for image generation: ${promptToUse.substring(0, 100)}${promptToUse.length > 100 ? '...' : ''}`);

      // Extract all parameters from job data
      const model = jobData.model || 'gpt-image-1'; // Default to gpt-image-1 instead of dall-e-3
      const size = jobData.size || '1024x1024';
      const style = jobData.style || 'vivid';
      const quality = jobData.quality || 'auto';
      const format = jobData.format || 'jpeg';
      const background = jobData.background || 'auto';

      console.log(`[processImageGeneration] Model from job data: ${jobData.model}`);
      console.log(`[processImageGeneration] Using model for image generation: ${model}`);
      console.log(`[processImageGeneration] Using size: ${size}`);
      console.log(`[processImageGeneration] Using style: ${style}`);
      console.log(`[processImageGeneration] Using quality: ${quality}`);
      console.log(`[processImageGeneration] Using format: ${format}`);
      console.log(`[processImageGeneration] Using background: ${background}`);

      imageResponse = await imageAgent.generateImage({
        prompt: promptToUse,
        model: model,
        size: size,
        style: style,
        quality: quality,
        format: format,
        background: background,
        compression: jobData.compression
      });
      console.log(`Image generated for job: ${jobId}`);
    } catch (error) {
      console.error('Image generation error:', error);
      throw new Error(`Failed to generate image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    if (!imageResponse.base64Image) {
      throw new Error(imageResponse.error || 'Failed to generate image');
    }

    // 5) Process the base64 string
    const base64Image = imageResponse.base64Image;

    // 6) Handle potential data URI prefix
    const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');

    // 7) Convert to buffer with validation
    let imageBuffer: Buffer;
    try {
      imageBuffer = Buffer.from(base64Data, 'base64');
      if (imageBuffer.length === 0) {
        throw new Error('Generated image buffer is empty');
      }
      console.log(`Image buffer created for job: ${jobId}`);
    } catch (error) {
      throw new Error(`Failed to process image data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 8) Upload to Firebase Storage using user-specific path
    const bucket = adminStorage.bucket();
    // Use the format from job data or default to jpeg
    const format = jobData.format || 'jpeg';
    const extension = format === 'png' ? 'png' : format === 'webp' ? 'webp' : 'jpeg';
    const filePath = `users/${userId}/generated/${jobId}.${extension}`;
    const file = bucket.file(filePath);

    try {
      // Create metadata without undefined values
      const fileMetadata: Record<string, any> = {
        jobId,
        userId,
        model: jobData.model || 'gpt-image-1', // Default to gpt-image-1 instead of dall-e-3
        format: format,
        generatedAt: new Date().toISOString()
      };

      // Only add compression if it's defined
      if (jobData.compression !== undefined) {
        fileMetadata.compression = jobData.compression;
      }

      await file.save(imageBuffer, {
        metadata: {
          contentType: format === 'png' ? 'image/png' : format === 'webp' ? 'image/webp' : 'image/jpeg',
          metadata: fileMetadata
        }
      });
      console.log(`Image uploaded to Firebase Storage for job: ${jobId}`);
    } catch (error) {
      console.error('Firebase Storage upload error:', error);
      throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 9) Generate signed URL
    let downloadUrl;
    try {
      [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      console.log(`Signed URL generated for job: ${jobId}`);
    } catch (error) {
      console.error('Signed URL generation error:', error);
      throw new Error(`Failed to generate signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // 10) Update Firestore job status in user-specific collection
    const jobRef = adminDb.collection('users').doc(userId).collection('images').doc(jobId);
    try {
      await jobRef.update({
        status: 'completed',
        imageUrl: downloadUrl,
        updatedAt: new Date(),
        processedAt: new Date()
      });
      console.log(`Firestore job status updated for job: ${jobId}`);
    } catch (error) {
      console.error('Firestore update error:', error);
      throw new Error(`Failed to update job status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

// 11) Create record in files collection with namespace
const namespace = uuidv4();
try {
  // Use the namespace as the document ID instead of auto-generating one
  const fileRef = adminDb.collection('users').doc(userId).collection('files').doc(namespace);
  // Create file record without undefined values
  const fileRecord: Record<string, any> = {
    category: 'My Images',
    createdAt: new Date(),
    downloadUrl: downloadUrl,
    imageUrl: downloadUrl, // Add imageUrl field to match what's in Firebase
    isImage: true,
    name: jobData.prompt,
    namespace: namespace,  // This will now match the document ID
    ref: `uploads/${userId}/generated/${jobId}.${extension}`,
    size: imageBuffer.length,
    type: format === 'png' ? 'image/png' : format === 'webp' ? 'image/webp' : 'image/jpeg',
    jobId: jobId,
    description: jobData.originalPrompt || jobData.prompt, // Use originalPrompt as description
    originalPrompt: jobData.originalPrompt || jobData.prompt, // Keep originalPrompt field for reference
    model: jobData.model || 'gpt-image-1' // Default to gpt-image-1 instead of dall-e-3
  };

  // Only add compression if it's defined
  if (jobData.compression !== undefined) {
    fileRecord.compression = jobData.compression;
  }

  await fileRef.set(fileRecord);
  console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);
} catch (error) {
  console.error('Files collection update error:', error);
  throw new Error(`Failed to create files record: ${error instanceof Error ? error.message : 'Unknown error'}`);
}

    // 12) Return success response with namespace
    return NextResponse.json({
      success: true,
      imageUrl: downloadUrl,
      namespace: namespace,
      jobId
    });

  } catch (error) {
    console.error('Image generation process error:', error);

    // Attempt to update job status on failure
    try {
      if (jobData?.userId && jobData?.id) {
        const jobRef = adminDb.collection('users').doc(jobData.userId).collection('images').doc(jobData.id);
        await jobRef.update({
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error',
          updatedAt: new Date(),
          failedAt: new Date()
        });
        console.log(`Job status updated to failed for job: ${jobData.id}`);
      }
    } catch (updateError) {
      console.error('Failed to update job status:', updateError);
    }

    // Return error response
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process image',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}