import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { MarketingAgentManager } from '../../../lib/agents/marketing/MarketingAgentManager';

/**
 * API endpoint for marketing agents
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized. Please sign in.' },
        { status: 401 }
      );
    }
    
    // Parse request body
    const { action, params } = await req.json();
    
    if (!action) {
      return NextResponse.json(
        { error: 'Missing required parameter: action' },
        { status: 400 }
      );
    }
    
    // Create marketing agent manager
    const manager = new MarketingAgentManager({
      userId: session.user.email,
      defaultLlmProvider: 'openai',
      defaultLlmModel: 'gpt-4o'
    });
    
    // Initialize marketing team
    const team = await manager.initializeMarketingTeam();
    
    // Process the requested action
    switch (action) {
      case 'createMarketingCampaign':
        // Validate required parameters
        if (!params?.productName || !params?.productDescription || !params?.campaignObjectives || !params?.targetAudience) {
          return NextResponse.json(
            { error: 'Missing required parameters for createMarketingCampaign' },
            { status: 400 }
          );
        }
        
        // Create marketing campaign
        const campaignId = await manager.createMarketingCampaign(
          params.productName,
          params.productDescription,
          params.campaignObjectives,
          params.targetAudience,
          params.budget || 10000,
          params.duration || 30
        );
        
        return NextResponse.json({
          success: true,
          campaignId
        });
      
      case 'analyzeProduct':
        // Validate required parameters
        if (!params?.productName || !params?.productDescription) {
          return NextResponse.json(
            { error: 'Missing required parameters for analyzeProduct' },
            { status: 400 }
          );
        }
        
        // Analyze product
        const productAnalysis = await team.strategicDirector.analyzeProduct(
          params.productName,
          params.productDescription,
          params.marketData || ''
        );
        
        return NextResponse.json({
          success: true,
          productAnalysis
        });
      
      case 'conductMarketResearch':
        // Validate required parameters
        if (!params?.productName || !params?.targetAudience) {
          return NextResponse.json(
            { error: 'Missing required parameters for conductMarketResearch' },
            { status: 400 }
          );
        }
        
        // Conduct market research
        const marketResearch = await team.researchInsights.conductMarketResearch(
          params.productName,
          params.targetAudience,
          params.uniqueSellingPoints || ''
        );
        
        return NextResponse.json({
          success: true,
          marketResearch
        });
      
      case 'generateSocialMediaPosts':
        // Validate required parameters
        if (!params?.productName || !params?.productDescription || !params?.targetAudience || !params?.keyFeatures || !params?.platforms) {
          return NextResponse.json(
            { error: 'Missing required parameters for generateSocialMediaPosts' },
            { status: 400 }
          );
        }
        
        // Generate social media posts
        const socialMediaPosts = await team.contentCreator.generateSocialMediaPosts(
          params.productName,
          params.productDescription,
          params.targetAudience,
          params.keyFeatures,
          params.platforms,
          params.postsPerPlatform || 3
        );
        
        return NextResponse.json({
          success: true,
          socialMediaPosts
        });
      
      case 'createBlogPost':
        // Validate required parameters
        if (!params?.title || !params?.topic || !params?.targetAudience || !params?.keywords) {
          return NextResponse.json(
            { error: 'Missing required parameters for createBlogPost' },
            { status: 400 }
          );
        }
        
        // Create blog post
        const blogPost = await team.contentCreator.createBlogPost(
          params.title,
          params.topic,
          params.targetAudience,
          params.keywords,
          params.tone || 'professional',
          params.callToAction || 'Learn more',
          params.wordCount || 800
        );
        
        return NextResponse.json({
          success: true,
          blogPost
        });
      
      case 'createHashtagStrategy':
        // Validate required parameters
        if (!params?.platform || !params?.brandName || !params?.productName || !params?.industry) {
          return NextResponse.json(
            { error: 'Missing required parameters for createHashtagStrategy' },
            { status: 400 }
          );
        }
        
        // Create hashtag strategy
        const hashtagStrategy = await team.socialMediaOrchestrator.createHashtagStrategy(
          params.platform,
          params.brandName,
          params.productName,
          params.industry
        );
        
        return NextResponse.json({
          success: true,
          hashtagStrategy
        });
      
      case 'trackMetric':
        // Validate required parameters
        if (!params?.name || !params?.value || !params?.unit || !params?.category) {
          return NextResponse.json(
            { error: 'Missing required parameters for trackMetric' },
            { status: 400 }
          );
        }
        
        // Track metric
        const metric = await team.analyticsReporting.trackMetric(
          params.name,
          params.description || '',
          params.value,
          params.unit,
          params.category,
          params.platform,
          params.contentId,
          params.target,
          params.previousValue
        );
        
        return NextResponse.json({
          success: true,
          metric
        });
      
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error processing marketing agents request:', error);
    
    return NextResponse.json(
      { error: error.message || 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}

/**
 * API endpoint for getting marketing agent information
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized. Please sign in.' },
        { status: 401 }
      );
    }
    
    // Get query parameters
    const url = new URL(req.url);
    const action = url.searchParams.get('action');
    
    if (!action) {
      return NextResponse.json(
        { error: 'Missing required parameter: action' },
        { status: 400 }
      );
    }
    
    // Create marketing agent manager
    const manager = new MarketingAgentManager({
      userId: session.user.email,
      defaultLlmProvider: 'openai',
      defaultLlmModel: 'gpt-4o'
    });
    
    // Initialize marketing team
    const team = await manager.initializeMarketingTeam();
    
    // Process the requested action
    switch (action) {
      case 'getAgentInfo':
        // Get information about all agents
        const agentInfo = {
          strategicDirector: team.strategicDirector.getInfo(),
          researchInsights: team.researchInsights.getInfo(),
          contentCreator: team.contentCreator.getInfo(),
          socialMediaOrchestrator: team.socialMediaOrchestrator.getInfo(),
          analyticsReporting: team.analyticsReporting.getInfo()
        };
        
        return NextResponse.json({
          success: true,
          agentInfo
        });
      
      case 'getPlatforms':
        // Get social media platforms
        const platforms = team.socialMediaOrchestrator.getPlatforms();
        
        return NextResponse.json({
          success: true,
          platforms
        });
      
      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Error processing marketing agents request:', error);
    
    return NextResponse.json(
      { error: error.message || 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
}
