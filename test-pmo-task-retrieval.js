/**
 * Test script for PMO Task Retrieval Implementation
 * 
 * This script tests the new PMO task retrieval functionality
 * in the marketing collaboration workflow.
 */

const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';

async function testPMOTaskRetrieval() {
  console.log('🧪 Testing PMO Task Retrieval Implementation\n');

  // Test data for PMO workflow
  const testPMOData = {
    pmoId: 'test-pmo-' + Date.now(),
    projectTitle: 'iKe Marketing Campaign with Task Retrieval',
    projectDescription: 'Develop a comprehensive marketing campaign for iKe SaaS knowledge explorer targeting project managers in small businesses (10-50 employees).',
    pmoAssessment: `
    Requirements Specification for iKe Marketing Campaign
    
    TASKS IDENTIFIED:
    1. Conduct market research on project management tools for small businesses
    2. Develop targeted messaging for 10-50 employee companies
    3. Create content strategy focusing on information silos and process inefficiencies
    4. Design multi-channel campaign including digital and content marketing
    5. Establish KPIs and measurement framework for campaign success
    6. Develop budget allocation across marketing channels
    
    ASSESSMENT:
    The marketing campaign should address critical pain points including information silos, 
    inconsistent processes, and difficulty locating key project information. The target 
    audience struggles with knowledge management and seeks solutions to streamline access 
    to best practices and team collaboration.
    
    REQUIREMENTS:
    - Campaign must demonstrate measurable ROI within 6 months
    - Content should emphasize time savings and improved project delivery
    - Multi-channel approach required (social media, content marketing, webinars)
    - Budget constraint of $50,000 for initial campaign phase
    `,
    teamSelectionRationale: 'Marketing team selected for comprehensive campaign development and strategic market positioning.',
    priority: 'High',
    category: 'Marketing',
    userId: '<EMAIL>'
  };

  try {
    // Step 1: Test Marketing Collaboration with PMO metadata
    console.log('📤 Step 1: Testing Marketing Collaboration with PMO Task Retrieval...');
    
    const marketingPrompt = `
# PMO Marketing Requirements Analysis

## Project Overview
**Title:** ${testPMOData.projectTitle}
**Priority:** ${testPMOData.priority}
**PMO ID:** ${testPMOData.pmoId}

## Project Description
${testPMOData.projectDescription}

## PMO Assessment
${testPMOData.pmoAssessment}

## Marketing Team Objectives
Based on the PMO requirements above, please provide a comprehensive marketing strategy analysis that includes all identified tasks and requirements.
    `.trim();

    const collaborationBody = {
      prompt: marketingPrompt,
      modelProvider: 'openai',
      modelName: 'gpt-4o',
      userId: testPMOData.userId,
      context: `PMO Context: ${testPMOData.pmoAssessment}`,
      category: testPMOData.category,
      metadata: {
        source: 'PMO',
        pmoId: testPMOData.pmoId,
        recordTitle: testPMOData.projectTitle,
        projectTitle: testPMOData.projectTitle,
        notificationId: 'test-notification-' + Date.now(),
        autoTriggered: false,
        triggerTimestamp: new Date().toISOString()
      }
    };

    const collaborationResponse = await fetch(`${baseUrl}/api/marketing-agent-collaboration`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(collaborationBody)
    });

    if (!collaborationResponse.ok) {
      const errorData = await collaborationResponse.json();
      throw new Error(`Marketing collaboration failed: ${collaborationResponse.status} - ${errorData.error || collaborationResponse.statusText}`);
    }

    const collaborationResult = await collaborationResponse.json();
    console.log('✅ Marketing collaboration completed successfully');
    console.log(`   Request ID: ${collaborationResult.requestId}`);
    console.log(`   Conversation messages: ${collaborationResult.conversation?.length || 0}`);

    // Step 2: Analyze conversation for PMO task retrieval
    console.log('\n🔍 Step 2: Analyzing conversation for PMO Task Retrieval...');
    
    const conversation = collaborationResult.conversation || [];
    const pmoTaskMessage = conversation.find(msg => msg.from === 'pmo-task-retrieval');
    
    if (pmoTaskMessage) {
      console.log('✅ PMO Task Retrieval step found in conversation');
      console.log(`   Message: ${pmoTaskMessage.message}`);
    } else {
      console.log('⚠️  PMO Task Retrieval step not found in conversation');
      console.log('   Available conversation steps:');
      conversation.forEach((msg, index) => {
        console.log(`   ${index + 1}. ${msg.from} → ${msg.to}`);
      });
    }

    // Step 3: Check final response for PMO task integration
    console.log('\n📋 Step 3: Checking final response for PMO task integration...');
    
    const finalMessage = conversation.find(msg => 
      msg.from === 'strategic-director' && 
      msg.to === 'user' && 
      msg.thinking
    );
    
    if (finalMessage) {
      const responseContent = finalMessage.message.toLowerCase();
      const taskKeywords = ['task', 'requirement', 'pmo', 'assessment'];
      const foundKeywords = taskKeywords.filter(keyword => responseContent.includes(keyword));
      
      console.log('✅ Final strategic response found');
      console.log(`   Response length: ${finalMessage.message.length} characters`);
      console.log(`   PMO-related keywords found: ${foundKeywords.join(', ')}`);
      
      if (foundKeywords.length >= 2) {
        console.log('✅ Response appears to integrate PMO context');
      } else {
        console.log('⚠️  Response may not fully integrate PMO context');
      }
    } else {
      console.log('❌ Final strategic response not found');
    }

    // Step 4: Summary
    console.log('\n📊 Test Summary:');
    console.log(`✅ Marketing collaboration API: Working`);
    console.log(`${pmoTaskMessage ? '✅' : '❌'} PMO Task Retrieval step: ${pmoTaskMessage ? 'Found' : 'Missing'}`);
    console.log(`${finalMessage ? '✅' : '❌'} Final response generation: ${finalMessage ? 'Working' : 'Failed'}`);
    
    if (collaborationResult.pmoWorkflow) {
      console.log(`✅ PMO workflow guidance: Provided`);
      console.log(`   Next steps: ${collaborationResult.pmoWorkflow.nextSteps?.action || 'N/A'}`);
    }

    return {
      success: true,
      requestId: collaborationResult.requestId,
      pmoTaskRetrievalFound: !!pmoTaskMessage,
      finalResponseGenerated: !!finalMessage,
      testData: testPMOData
    };

  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error.message,
      testData: testPMOData
    };
  }
}

// Run the test
if (require.main === module) {
  testPMOTaskRetrieval()
    .then(result => {
      console.log('\n🏁 Test completed');
      if (result.success) {
        console.log('✅ PMO Task Retrieval implementation appears to be working correctly');
      } else {
        console.log('❌ PMO Task Retrieval implementation needs attention');
        console.log(`Error: ${result.error}`);
      }
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testPMOTaskRetrieval };
