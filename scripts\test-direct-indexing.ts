/**
 * Test script for the new Direct Codebase Indexing functionality
 * 
 * This bypasses the PDF storage bottleneck and goes directly to vector embeddings
 */

import { codebaseIndexingTool } from '../lib/tools/codebase-indexing-tool';
import { queryDocumentsTool } from '../lib/tools/queryDocumentsTool';

async function testDirectIndexing() {
  console.log('🚀 Testing Direct Codebase Indexing (No PDF Storage)');
  console.log('=====================================================\n');

  const testUserId = 'test-user-direct';
  const testProjectName = 'ike-project-direct-test';
  const testRootPath = process.cwd();

  try {
    // Test with a smaller subset first
    console.log('📚 Step 1: Direct indexing (bypassing PDF storage)...');
    
    const indexResult = await codebaseIndexingTool.indexCodebase({
      rootPath: testRootPath,
      userId: testUserId,
      projectName: testProjectName,
      excludePatterns: [
        'node_modules', '.git', '.next', 'dist', 'build',
        'coverage', '.vscode', '__pycache__', '.env*',
        'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml'
      ],
      includeExtensions: ['.ts', '.tsx', '.js', '.jsx'],
      chunkSize: 800,
      chunkOverlap: 100
    });

    if (!indexResult.success) {
      throw new Error(`Direct indexing failed: ${indexResult.error}`);
    }

    console.log(`✅ Direct indexing completed successfully!`);
    console.log(`📊 Files processed: ${indexResult.totalFiles}`);
    console.log(`📄 Vector chunks created: ${indexResult.totalChunks}`);
    console.log(`🆔 Document ID: ${indexResult.documentId}`);
    console.log(`🔗 Chunk IDs: ${indexResult.chunkIds?.length || 0} chunks\n`);

    // Test semantic search on the directly indexed content
    console.log('🔍 Step 2: Testing semantic search on direct embeddings...');
    
    const searchQueries = [
      'React components and TypeScript',
      'API routes and Next.js handlers',
      'Firebase authentication',
      'Vector embeddings and Pinecone'
    ];

    for (const query of searchQueries) {
      console.log(`\n🔎 Searching for: "${query}"`);
      
      const searchResult = await queryDocumentsTool.process({
        userId: testUserId,
        query,
        category: 'Codebase Documentation',
        maxResults: 3
      });

      if (searchResult.success && searchResult.sources && searchResult.sources.length > 0) {
        console.log(`✅ Found ${searchResult.sources.length} relevant code chunks`);
        console.log(`📝 Sample content: ${searchResult.content.substring(0, 150)}...`);
        
        // Show source files
        searchResult.sources.forEach((source, index) => {
          console.log(`   📄 Source ${index + 1}: ${source.title || 'Unknown file'}`);
        });
      } else {
        console.log(`❌ No results found for: ${query}`);
      }
    }

    console.log('\n🎉 Direct indexing test completed successfully!');
    console.log('\n💡 Key Benefits of Direct Approach:');
    console.log('   ✅ No PDF storage bottleneck');
    console.log('   ✅ Faster processing for large codebases');
    console.log('   ✅ Direct vector embedding storage');
    console.log('   ✅ No hanging on large file counts');
    console.log('   ✅ Maintains all existing functionality');

  } catch (error) {
    console.error('❌ Direct indexing test failed:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testDirectIndexing()
    .then(() => {
      console.log('\n✅ All direct indexing tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Direct indexing test suite failed:', error);
      process.exit(1);
    });
}

export { testDirectIndexing };
