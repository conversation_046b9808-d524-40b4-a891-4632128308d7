/**
 * Test script for the new Codebase Indexing functionality
 * 
 * This script demonstrates how the enhanced CodebaseDocumentationOrchestratorAgent
 * now uses semantic search instead of hallucination.
 */

import { codebaseIndexingTool } from '../lib/tools/codebase-indexing-tool';
import { queryDocumentsTool } from '../lib/tools/queryDocumentsTool';
import { CodebaseDocumentationOrchestratorAgent } from '../lib/agents/pmo/CodebaseDocumentationOrchestratorAgent';

async function testCodebaseIndexing() {
  console.log('🚀 Testing Enhanced Codebase Documentation System');
  console.log('================================================\n');

  const testUserId = 'test-user-123';
  const testProjectName = 'ike-project-test';
  const testRootPath = process.cwd(); // Current project directory

  try {
    // Step 1: Index the codebase
    console.log('📚 Step 1: Indexing codebase...');
    const indexResult = await codebaseIndexingTool.indexCodebase({
      rootPath: testRootPath,
      userId: testUserId,
      projectName: testProjectName,
      excludePatterns: ['node_modules', '.git', '.next', 'dist'],
      includeExtensions: ['.ts', '.tsx', '.js', '.jsx', '.md']
    });

    if (!indexResult.success) {
      throw new Error(`Indexing failed: ${indexResult.error}`);
    }

    console.log(`✅ Successfully indexed ${indexResult.totalFiles} files into ${indexResult.totalChunks} chunks`);
    console.log(`📄 Document ID: ${indexResult.documentId}\n`);

    // Step 2: Test semantic search
    console.log('🔍 Step 2: Testing semantic search...');
    const searchQueries = [
      'Next.js API routes and middleware',
      'Firebase authentication and database',
      'React components and TypeScript interfaces',
      'Agent orchestration and AI processing'
    ];

    for (const query of searchQueries) {
      console.log(`\n🔎 Searching for: "${query}"`);
      
      const searchResult = await queryDocumentsTool.process({
        userId: testUserId,
        query,
        category: 'Codebase Documentation',
        maxResults: 3
      });

      if (searchResult.success) {
        console.log(`✅ Found relevant content (${searchResult.sources?.length || 0} sources)`);
        console.log(`📝 Content preview: ${searchResult.content.substring(0, 200)}...`);
      } else {
        console.log(`❌ Search failed for query: ${query}`);
      }
    }

    // Step 3: Test the enhanced orchestrator
    console.log('\n🤖 Step 3: Testing enhanced CodebaseDocumentationOrchestratorAgent...');
    
    const orchestrator = new CodebaseDocumentationOrchestratorAgent({
      userId: testUserId,
      includeExplanation: true,
      streamResponse: false,
      codebasePaths: ['lib/agents', 'app/api'],
      documentationScope: 'specific',
      outputFormat: 'markdown'
    });

    // Test with a focused documentation request
    const docResult = await orchestrator.processDocumentationRequest(
      ['lib/agents/pmo', 'app/api/codebase-documentation'],
      'Document the codebase documentation system architecture and API endpoints',
      'Focus on the new RAG-enhanced approach vs the old hallucination-based approach'
    );

    if (docResult.success) {
      console.log('✅ Enhanced documentation generation completed!');
      console.log(`📊 Generated ${docResult.subAgentResults?.length || 0} sub-agent analyses`);
      console.log(`📄 Output length: ${docResult.output.length} characters`);
      console.log('\n📝 Sample output:');
      console.log(docResult.output.substring(0, 500) + '...\n');
    } else {
      console.log('❌ Documentation generation failed');
    }

    console.log('🎉 Test completed successfully!');
    console.log('\n💡 Key Improvements:');
    console.log('   ✅ Sub-agents now receive actual code content instead of hallucinating');
    console.log('   ✅ Semantic search finds relevant code for each specialization');
    console.log('   ✅ Documentation is grounded in real codebase implementation');
    console.log('   ✅ Leverages existing Pinecone + Firebase RAG infrastructure');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testCodebaseIndexing()
    .then(() => {
      console.log('\n✅ All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testCodebaseIndexing };
