import { doc, updateDoc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './config';
import { User } from '../../../admin/planner/types';

/**
 * Synchronizes a user's profile image between NextAuth session and Firestore
 *
 * @param userId - The user's ID (typically their email)
 * @param currentAvatar - The current avatar URL in the user's profile
 * @param newImageUrl - The new image URL from NextAuth session
 * @returns The final avatar URL (either updated or current)
 */
export async function syncUserProfileImage(
  userId: string,
  currentAvatar: string | null | undefined,
  newImageUrl: string | null | undefined
): Promise<string | null> {
  // If no new image or images are the same, return current
  if (!newImageUrl || currentAvatar === newImageUrl) {
    return currentAvatar || null;
  }

  try {
    console.log(`Syncing profile image for user ${userId}`);
    console.log(`Current avatar: ${currentAvatar || 'none'}`);
    console.log(`New image URL: ${newImageUrl}`);

    // Get user document reference
    const userDocRef = doc(db, 'users', userId);

    // Check if user document exists
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      // Update existing user document
      await updateDoc(userDocRef, {
        avatar: newImageUrl,
        updatedAt: serverTimestamp()
      });
      console.log('Successfully updated user avatar in Firestore');
    } else {
      // Create new user document with the image
      console.log('User document not found, creating new profile');
      await setDoc(userDocRef, {
        name: userId.split('@')[0], // Use email prefix as name
        email: userId,
        role: 'user',
        avatar: newImageUrl,
        availability: 'Full-time',
        isAuthorized: true,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      console.log('Created new user profile with avatar');
    }

    return newImageUrl;
  } catch (error) {
    console.error('Error syncing user profile image:', error);
    // Return the new URL anyway for local state
    return newImageUrl;
  }
}

/**
 * Fetches a user's profile from Firestore
 *
 * @param userId - The user's ID (typically their email)
 * @returns The user profile or null if not found
 */
export async function fetchUserProfile(userId: string): Promise<User | null> {
  try {
    const userDocRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userDocRef);

    if (userDoc.exists()) {
      const userData = userDoc.data();
      return {
        id: userDoc.id,
        name: userData.name || 'Unknown User',
        email: userData.email || userId,
        role: userData.role || 'user',
        avatar: userData.avatar || '/avatars/default.png',
        availability: userData.availability || 'Full-time',
        isAuthorized: userData.isAuthorized !== undefined ? userData.isAuthorized : false,
        createdAt: userData.createdAt ? new Date(userData.createdAt.seconds * 1000) : new Date(),
        updatedAt: userData.updatedAt ? new Date(userData.updatedAt.seconds * 1000) : new Date(),
        photoURL: userData.avatar || '/avatars/default.png',
        displayName: userData.name || 'Unknown User'
      };
    }

    return null;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
}

/**
 * Updates a user's profile in Firestore
 *
 * @param userId - The user's ID (typically their email)
 * @param profileData - The profile data to update
 * @returns True if successful, false otherwise
 */
export async function updateUserProfile(
  userId: string,
  profileData: Partial<User>
): Promise<boolean> {
  try {
    const userDocRef = doc(db, 'users', userId);

    // Remove id from the data to be updated
    const { id, ...dataToUpdate } = profileData;

    await updateDoc(userDocRef, {
      ...dataToUpdate,
      updatedAt: serverTimestamp()
    });

    return true;
  } catch (error) {
    console.error('Error updating user profile:', error);
    return false;
  }
}
