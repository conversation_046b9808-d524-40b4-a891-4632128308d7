# PMO Agent Meeting Room System

## Overview

The PMO Agent Meeting Room System is a sophisticated voice-enabled interface that allows users to have real-time conversations with specialized PMO (Project Management Office) agents using ElevenLabs voice AI technology. Each agent is an expert in their respective domain and has access to contextual project documents through an integrated knowledge base.

## Features

### 🎯 Specialized PMO Agents
- **Marketing Director**: Expert in marketing strategy, campaigns, and brand development
- **Research Lead**: Specialist in research methodology, data analysis, and insights
- **Software Design Architect**: Expert in software architecture, system design, and technical solutions
- **Sales Director**: Specialist in sales strategy, customer relations, and revenue growth
- **Business Analyst**: Expert in business analysis, requirements gathering, and process optimization
- **Investigative Researcher**: Specialist in investigative research, fact-checking, and deep analysis

### 🗣️ Voice-Enabled Conversations
- Real-time voice conversations using ElevenLabs Conversational AI
- Natural speech recognition and synthesis
- Professional voice personas for each agent type
- Voice activity indicators for both user and agent

### 📚 Contextual Knowledge Base
- Automatic integration with Agent_Output documents
- Document filtering by agent type for relevant context
- Real-time knowledge base updates
- Document selection and preview capabilities

### 🎛️ Meeting Controls
- Start/end voice conversations
- Mute/unmute functionality
- Volume control
- Connection status monitoring
- Error handling and recovery

## System Architecture

### Components

#### 1. AgentMeetingRoom (Main Component)
- **Location**: `components/PMO/AgentMeetingRoom.tsx`
- **Purpose**: Main container component that orchestrates the entire meeting experience
- **Features**:
  - Agent selection management
  - Document context loading
  - Meeting state coordination
  - UI layout management

#### 2. AgentSelectionPanel
- **Location**: `components/PMO/AgentSelectionPanel.tsx`
- **Purpose**: Interface for selecting and initializing PMO agents
- **Features**:
  - Visual agent cards with specializations
  - Voice preview functionality
  - Agent initialization status tracking
  - Real-time initialization feedback

#### 3. MeetingInterface
- **Location**: `components/PMO/MeetingInterface.tsx`
- **Purpose**: Main meeting interface with voice controls
- **Features**:
  - Split-screen video call layout
  - Voice activity visualization
  - Meeting controls (mute, volume, start/end)
  - Connection status monitoring

#### 4. DocumentContextPanel
- **Location**: `components/PMO/DocumentContextPanel.tsx`
- **Purpose**: Document management and context selection
- **Features**:
  - Document filtering and search
  - Document selection for knowledge base
  - Document preview and metadata display
  - Category-based organization

### Services

#### 1. PMO Agent Voice Configuration
- **Location**: `lib/agents/voice/pmoAgentVoiceConfig.ts`
- **Purpose**: Central configuration for all PMO agent types
- **Features**:
  - Voice ID mappings for ElevenLabs
  - Agent specializations and descriptions
  - Prompt generation for each agent type
  - Agent identification and validation

#### 2. PMO Voice Conversation Hook
- **Location**: `lib/hooks/usePMOVoiceConversation.ts`
- **Purpose**: React hook for managing ElevenLabs voice conversations
- **Features**:
  - Conversation state management
  - Real-time voice activity tracking
  - Error handling and recovery
  - Volume and mute controls

#### 3. PMO Agent Initialization Service
- **Location**: `lib/services/PMOAgentInitializationService.ts`
- **Purpose**: Service for creating and configuring ElevenLabs agents
- **Features**:
  - Agent creation and caching
  - Knowledge base setup and document upload
  - Conversation session management
  - Initialization status tracking

### API Endpoints

#### 1. ElevenLabs Agent Creation
- **Location**: `app/api/elevenlabs/create-agent/route.ts`
- **Purpose**: Create and configure ElevenLabs conversational agents
- **Features**:
  - Agent creation with custom prompts
  - Knowledge base setup and document upload
  - Conversation session initialization
  - Error handling and validation

#### 2. Agent Outputs Global
- **Location**: `app/api/agent-outputs/global/route.ts`
- **Purpose**: Fetch Agent_Output documents for knowledge base integration
- **Features**:
  - Agent type filtering
  - Document metadata enrichment
  - Batch document processing
  - User access control

## Usage Guide

### 1. Accessing the Meeting Room
Navigate to the PMO services page and click on the "Agent Meeting" tab to access the meeting room interface.

### 2. Selecting an Agent
1. Browse available PMO agents in the selection panel
2. Review agent specializations and descriptions
3. Click "Voice Preview" to hear the agent's voice
4. Select your preferred agent
5. Click "Initialize & Start Meeting" to begin setup

### 3. Agent Initialization
The system will automatically:
1. Create or retrieve the ElevenLabs agent
2. Upload relevant documents to the knowledge base
3. Configure the agent with specialized prompts
4. Establish a conversation session

### 4. Starting a Conversation
1. Click the green phone button to start the voice conversation
2. Wait for the connection to establish
3. Begin speaking naturally with the agent
4. Use meeting controls as needed (mute, volume, etc.)

### 5. Document Context
- The system automatically loads relevant documents based on agent type
- Use the document panel to select specific documents for context
- Documents are uploaded to the agent's knowledge base for reference

### 6. Ending the Meeting
Click the red phone button to end the voice conversation and disconnect from the agent.

## Configuration

### Environment Variables
```env
NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY=your_elevenlabs_api_key
```

### Voice Configuration
Each agent is configured with specific ElevenLabs voice IDs in `pmoAgentVoiceConfig.ts`. Update voice IDs as needed for your ElevenLabs account.

### Agent Prompts
Agent prompts are dynamically generated based on:
- Agent type and specialization
- Available document context
- Project metadata
- User interaction history

## Integration Points

### PMO System Integration
- Integrated as a tab in the main PMO interface
- Shares authentication and user context
- Accesses Agent_Output documents from Firestore
- Maintains session state across tab switches

### ElevenLabs Integration
- Uses ElevenLabs Conversational AI API
- Implements real-time voice streaming
- Manages agent lifecycle and knowledge bases
- Handles conversation sessions and state

### Firebase Integration
- Fetches documents from Agent_Output collection
- Filters by user access and agent type
- Maintains document metadata and relationships
- Supports real-time document updates

## Technical Requirements

### Dependencies
- React 18+
- Next.js 13+
- ElevenLabs React SDK
- Firebase Admin SDK
- NextAuth.js for authentication

### Browser Support
- Modern browsers with WebRTC support
- Microphone access permissions required
- Stable internet connection for voice streaming

## Troubleshooting

### Common Issues

#### 1. Agent Initialization Fails
- Check ElevenLabs API key configuration
- Verify user authentication
- Ensure document access permissions

#### 2. Voice Connection Issues
- Check microphone permissions
- Verify internet connection stability
- Try refreshing the page and reconnecting

#### 3. Document Loading Problems
- Verify Firestore access permissions
- Check document filtering parameters
- Ensure Agent_Output collection exists

#### 4. Audio Quality Issues
- Adjust volume settings
- Check microphone quality
- Verify ElevenLabs voice configuration

## Future Enhancements

### Planned Features
- Multi-agent conversations
- Meeting transcription and recording
- Advanced document search and filtering
- Custom agent training with user data
- Integration with calendar systems
- Meeting analytics and insights

### Performance Optimizations
- Agent caching and reuse
- Document preloading
- Connection pooling
- Voice streaming optimization

## Support

For technical support or feature requests, please refer to the main project documentation or contact the development team.
