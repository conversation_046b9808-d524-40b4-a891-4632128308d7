import React from 'react';
import { authOptions } from '@/api/auth/[...nextauth]/authOptions';
import { getServerSession } from "next-auth/next"
import { doc, getDoc } from 'firebase/firestore';
import { db } from 'components/firebase';
import { redirect } from 'next/navigation'
import ChatPageWrapper from './ChatPageWrapper';

export default async function ChatPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getServerSession(authOptions)
  const { id } = await params  // Await params before destructuring
  const chatId = id

  if (!session) {
    redirect('/api/auth/signin')
  }

  let chatData = null
  let errorMessages = null

  try {
    const chatDoc = doc(db, 'users', session.user?.email!, 'chats', chatId)
    const chatSnapshot = await getDoc(chatDoc)

    if (chatSnapshot.exists()) {
      // Serialize the Firestore timestamp to a string or number
      const rawData = chatSnapshot.data()
      chatData = {
        ...rawData,
        createdAt: rawData.createdAt?.toMillis() // Convert timestamp to milliseconds
      }
    } else {
      throw new Error('Chat not found')
    }
  } catch (error) {
    console.error("Error fetching chat data:", error)
    errorMessages = "Failed to load chat. Please try again."
  }

  return (
    <ChatPageWrapper
      session={session}
      chatData={chatData}
      errorMessages={errorMessages}
      chatId={chatId}
    />
  )
}