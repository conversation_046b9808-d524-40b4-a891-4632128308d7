'use client';

import { useState, useEffect } from 'react';
import { JobStatus } from '../lib/queue/jobStatus';

interface AsyncJobStatusProps {
  jobId: string;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  pollingInterval?: number; // in milliseconds
}

/**
 * Component to display the status of an asynchronous document processing job
 * and poll for updates until the job is completed or failed
 */
export default function AsyncJobStatus({
  jobId,
  onComplete,
  onError,
  pollingInterval = 2000
}: AsyncJobStatusProps) {
  const [status, setStatus] = useState<JobStatus | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState<boolean>(true);

  // Poll for job status updates
  useEffect(() => {
    if (!jobId || !isPolling) return;

    const checkStatus = async () => {
      try {
        const response = await fetch(`/api/document-processing/${jobId}`);

        if (!response.ok) {
          const errorData = await response.json();
          setError(errorData.error || 'Failed to fetch job status');
          setIsPolling(false);
          if (onError) onError(errorData.error || 'Failed to fetch job status');
          return;
        }

        const data = await response.json();
        setStatus(data.status);
        setProgress(data.progress || 0);

        // If the job is completed, stop polling and call the onComplete callback
        if (data.status === JobStatus.COMPLETED) {
          setResult(data.result);
          setIsPolling(false);
          if (onComplete) onComplete(data.result);
        }

        // If the job failed, stop polling and call the onError callback
        if (data.status === JobStatus.FAILED) {
          setError(data.error || 'Job failed');
          setIsPolling(false);
          if (onError) onError(data.error || 'Job failed');
        }

        // If the job was cancelled, stop polling
        if (data.status === JobStatus.CANCELLED) {
          setError('Job was cancelled');
          setIsPolling(false);
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Unknown error occurred');
        // Don't stop polling on network errors, they might be temporary
      }
    };

    // Check immediately on mount
    checkStatus();

    // Set up polling interval
    const intervalId = setInterval(checkStatus, pollingInterval);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [jobId, isPolling, pollingInterval, onComplete, onError]);

  // Function to cancel the job
  const cancelJob = async () => {
    if (!jobId) return;

    try {
      const response = await fetch(`/api/document-processing/${jobId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to cancel job');
        return;
      }

      const data = await response.json();
      setStatus(data.status);
      setIsPolling(false);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Unknown error occurred');
    }
  };

  // Render different UI based on job status
  return (
    <div className="bg-white shadow-md rounded-lg p-4 my-4">
      <h3 className="text-lg font-semibold mb-2">Document Processing Status</h3>

      {/* Status indicator */}
      <div className="mb-4">
        <p className="text-sm text-gray-600">Job ID: {jobId}</p>
        <p className="text-sm font-medium">
          Status: {' '}
          <span className={`
            ${status === JobStatus.PENDING ? 'text-yellow-600' : ''}
            ${status === JobStatus.PROCESSING ? 'text-blue-600' : ''}
            ${status === JobStatus.COMPLETED ? 'text-green-600' : ''}
            ${status === JobStatus.FAILED ? 'text-red-600' : ''}
            ${status === JobStatus.CANCELLED ? 'text-gray-600' : ''}
          `}>
            {status || 'Unknown'}
          </span>
        </p>
      </div>

      {/* Progress bar for processing jobs */}
      {status === JobStatus.PROCESSING && (
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
          <div
            className="bg-blue-600 h-2.5 rounded-full transition-all duration-300 ease-in-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          <p className="text-sm">{error}</p>
        </div>
      )}

      {/* Cancel button for pending or processing jobs */}
      {(status === JobStatus.PENDING || status === JobStatus.PROCESSING) && (
        <button
          onClick={cancelJob}
          className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded"
        >
          Cancel Processing
        </button>
      )}

      {/* Result preview for completed jobs */}
      {status === JobStatus.COMPLETED && result && (
        <div className="mt-4">
          <h4 className="text-md font-medium mb-2">Processing Result:</h4>
          <div className="bg-gray-50 p-3 rounded border border-gray-200 max-h-60 overflow-y-auto">
            <pre className="text-xs whitespace-pre-wrap">{JSON.stringify(result, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
}
