/**
 * Test for Adaptive Truncation Feature in Codebase Indexing Tool
 * 
 * This test verifies that the adaptive truncation functionality works correctly
 * by testing different file types and ensuring appropriate context limits are applied.
 */

const { CodebaseIndexingTool } = require('../lib/tools/codebase-indexing-tool');
const fs = require('fs').promises;
const path = require('path');

describe('Adaptive Truncation Tests', () => {
  let indexingTool;

  beforeEach(() => {
    indexingTool = new CodebaseIndexingTool({
      storageTool: {
        savePdfToByteStore: jest.fn().mockResolvedValue('mock-result')
      }
    });
  });

  describe('getAdaptiveContextLimit', () => {
    test('should return 2500 for config files', () => {
      const testCases = [
        { filePath: 'config/app.config.js', language: 'JavaScript', expected: 2500 },
        { filePath: 'package.json', language: 'JSON', expected: 2500 },
        { filePath: 'tsconfig.json', language: 'JSON', expected: 2500 },
        { filePath: 'settings.yaml', language: 'YAML', expected: 2500 },
      ];

      testCases.forEach(({ filePath, language, expected }) => {
        const result = indexingTool.getAdaptiveContextLimit(filePath, language, '');
        expect(result).toBe(expected);
      });
    });

    test('should return 2000 for type definition files', () => {
      const testCases = [
        { filePath: 'types/user.types.ts', language: 'TypeScript', expected: 2000 },
        { filePath: 'interfaces/api.interface.ts', language: 'TypeScript', expected: 2000 },
        { filePath: 'global.d.ts', language: 'TypeScript', expected: 2000 },
        { filePath: 'src/types/index.ts', language: 'TypeScript', expected: 2000 },
      ];

      testCases.forEach(({ filePath, language, expected }) => {
        const result = indexingTool.getAdaptiveContextLimit(filePath, language, '');
        expect(result).toBe(expected);
      });
    });

    test('should return 1800 for API routes and handlers', () => {
      const testCases = [
        { filePath: 'api/users/route.ts', language: 'TypeScript', expected: 1800 },
        { filePath: 'routes/auth.js', language: 'JavaScript', expected: 1800 },
        { filePath: 'handlers/webhook.handler.ts', language: 'TypeScript', expected: 1800 },
        { filePath: 'src/api/posts.ts', language: 'TypeScript', expected: 1800 },
      ];

      testCases.forEach(({ filePath, language, expected }) => {
        const result = indexingTool.getAdaptiveContextLimit(filePath, language, '');
        expect(result).toBe(expected);
      });
    });

    test('should return 2000 for entry point files', () => {
      const testCases = [
        { filePath: 'index.ts', language: 'TypeScript', expected: 2000 },
        { filePath: 'src/index.js', language: 'JavaScript', expected: 2000 },
        { filePath: 'main.ts', language: 'TypeScript', expected: 2000 },
        { filePath: 'app.ts', language: 'TypeScript', expected: 2000 },
      ];

      testCases.forEach(({ filePath, language, expected }) => {
        const result = indexingTool.getAdaptiveContextLimit(filePath, language, '');
        expect(result).toBe(expected);
      });
    });

    test('should return 1600 for React components with Props', () => {
      const fileContent = `
        interface UserCardProps {
          user: User;
          onEdit: () => void;
        }
        
        export const UserCard: React.FC<UserCardProps> = ({ user, onEdit }) => {
          return <div>...</div>;
        };
      `;

      const result = indexingTool.getAdaptiveContextLimit(
        'components/UserCard.tsx', 
        'React TypeScript', 
        fileContent
      );
      expect(result).toBe(1600);
    });

    test('should return 1200 for regular source files', () => {
      const testCases = [
        { filePath: 'utils/helpers.ts', language: 'TypeScript', expected: 1200 },
        { filePath: 'src/services/api.js', language: 'JavaScript', expected: 1200 },
        { filePath: 'lib/validation.ts', language: 'TypeScript', expected: 1200 },
      ];

      testCases.forEach(({ filePath, language, expected }) => {
        const result = indexingTool.getAdaptiveContextLimit(filePath, language, '');
        expect(result).toBe(expected);
      });
    });
  });

  describe('smartTruncateContent', () => {
    test('should return full content if under limit', () => {
      const content = 'const x = 1;\nconst y = 2;';
      const result = indexingTool.smartTruncateContent(content, 1000);
      expect(result).toBe(content);
    });

    test('should truncate at natural breakpoints', () => {
      const content = `
import React from 'react';
import { User } from './types';

export const UserComponent = () => {
  const [user, setUser] = useState<User | null>(null);
  
  useEffect(() => {
    fetchUser().then(setUser);
  }, []);

  return <div>{user?.name}</div>;
};
      `.trim();

      const result = indexingTool.smartTruncateContent(content, 100);
      
      // Should truncate but try to preserve import section
      expect(result.length).toBeLessThanOrEqual(100);
      expect(result).toContain('import React');
    });

    test('should break at comment blocks when appropriate', () => {
      const content = `
/**
 * This is a long comment block
 * that explains the functionality
 */
export class MyClass {
  // This is the main implementation
  method() {
    return 'hello';
  }
}
      `.trim();

      const result = indexingTool.smartTruncateContent(content, 80);
      
      expect(result.length).toBeLessThanOrEqual(80);
      // Should try to break at end of comment block
      if (result.includes('*/')) {
        expect(result.endsWith('*/')).toBe(true);
      }
    });
  });

  describe('calculateCostSavings', () => {
    test('should calculate correct cost savings', () => {
      const originalLength = 4000; // 1000 tokens
      const truncatedLength = 1000; // 250 tokens
      
      const result = indexingTool.calculateCostSavings(originalLength, truncatedLength);
      
      expect(result.savedTokens).toBe(750); // 1000 - 250
      expect(result.savedCost).toBeCloseTo(0.000225); // (750/1M) * $0.30
    });

    test('should handle zero savings', () => {
      const result = indexingTool.calculateCostSavings(1000, 1000);
      
      expect(result.savedTokens).toBe(0);
      expect(result.savedCost).toBe(0);
    });
  });

  describe('Integration Test', () => {
    test('should track cost savings during file analysis', async () => {
      // Mock the LLM call to avoid actual API calls
      const originalAnalyzeFileWithContext = indexingTool.analyzeFileWithContext;
      indexingTool.analyzeFileWithContext = jest.fn().mockResolvedValue('Mock file purpose');

      // Create a mock file content
      const longFileContent = 'x'.repeat(5000); // 5000 characters
      
      // Call the method that should track cost savings
      await indexingTool.analyzeFileWithContext(
        'test/long-file.ts',
        'TypeScript',
        longFileContent,
        'Test file'
      );

      // Verify that cost tracking data was recorded
      expect(indexingTool.costTrackingData).toHaveLength(1);
      expect(indexingTool.costTrackingData[0].originalSize).toBe(5000);
      expect(indexingTool.costTrackingData[0].truncatedSize).toBeLessThan(5000);
    });
  });
});

// Mock the external dependencies
jest.mock('../lib/tools/google-ai', () => ({
  processWithGoogleAI: jest.fn().mockResolvedValue('Mock LLM response')
}));

jest.mock('../lib/tools/storage-tool', () => ({
  StorageTool: jest.fn().mockImplementation(() => ({
    savePdfToByteStore: jest.fn().mockResolvedValue('mock-result')
  }))
}));
