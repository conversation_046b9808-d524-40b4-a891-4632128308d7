import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { v4 as uuidv4 } from "uuid";
import { adminDb, adminStorage } from "components/firebase-admin";

interface SaveImageRequest {
  imageUrl: string;
  prompt: string;
  model: string;
  jobId: string;
  namespace: string;
}

/**
 * API endpoint to save a generated image to the user's gallery
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Get the user session
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - No valid session" },
        { status: 401 }
      );
    }

    // Get the user ID from the session
    const userId = session.user.email;

    // Parse the request body
    const { imageUrl, prompt, model, jobId, namespace }: SaveImageRequest = await req.json();

    if (!imageUrl || !prompt) {
      return NextResponse.json(
        { success: false, error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Use the provided namespace if it exists, otherwise generate a new one
    // For Imagen models, we should use the namespace provided by the Imagen API
    const fileNamespace = namespace ? namespace : uuidv4();
    console.log(`Using namespace for image in gallery: ${fileNamespace}`);

    // Check if the imageUrl is a base64 string
    let finalImageUrl = imageUrl;
    if (imageUrl.startsWith('data:image')) {
      try {
        // Extract the base64 data
        const base64Data = imageUrl.replace(/^data:image\/\w+;base64,/, '');
        const imageBuffer = Buffer.from(base64Data, 'base64');

        if (imageBuffer.length === 0) {
          throw new Error('Generated image buffer is empty');
        }

        // Upload to Firebase Storage
        const bucket = adminStorage.bucket();
        const filePath = `users/${userId}/generated/${jobId || uuidv4()}.png`;
        const file = bucket.file(filePath);

        await file.save(imageBuffer, {
          metadata: {
            contentType: 'image/png',
            metadata: {
              jobId: jobId || 'direct-upload',
              userId,
              generatedAt: new Date().toISOString()
            }
          }
        });

        // Generate signed URL
        const [downloadUrl] = await file.getSignedUrl({
          action: 'read',
          expires: '03-01-2500'
        });

        // Use the download URL instead of the base64 data
        finalImageUrl = downloadUrl;
        console.log(`Image uploaded to Firebase Storage and URL generated`);
      } catch (error) {
        console.error('Error processing base64 image:', error);
        return NextResponse.json(
          { success: false, error: "Failed to process image data" },
          { status: 500 }
        );
      }
    }

    // Create a record in the user's files collection
    const fileRef = adminDb.collection('users').doc(userId).collection('files').doc(fileNamespace);

    await fileRef.set({
      category: 'My Images',
      createdAt: new Date(),
      downloadUrl: finalImageUrl,
      imageUrl: finalImageUrl, // Add imageUrl field to match what's in Firebase
      isImage: true,
      name: prompt.substring(0, 100), // Limit name length
      namespace: fileNamespace,
      ref: `generated/${jobId || 'direct-upload'}.png`,
      size: 0, // Size is unknown at this point
      type: 'image/png',
      jobId: jobId || 'direct-upload',
      description: prompt,
      originalPrompt: prompt, // Add originalPrompt field
      model: model || 'image-generation'
    });

    // Return success response with the namespace for the gallery link
    const responseData = {
      success: true,
      namespace: fileNamespace,
      imageUrl: finalImageUrl,
      storedImageUrl: finalImageUrl, // Add storedImageUrl for compatibility
      message: 'Image saved to gallery successfully',
      galleryUrl: '/imageGallery'
    };
    console.log("Returning response from saveEditedImageToGallery API:", responseData);
    return NextResponse.json(responseData);
  } catch (error: any) {
    console.error("Error saving image to gallery:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    );
  }
}

export async function OPTIONS(req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}
