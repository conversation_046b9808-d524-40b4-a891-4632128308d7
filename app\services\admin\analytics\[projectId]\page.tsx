'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { ArrowLeft, Download, Share, Printer, AlertTriangle } from 'lucide-react';
import { usePlanner } from '../../../../context/PlannerContext';
import ProjectReport from '../components/ProjectReport';
import { Project } from '../../../../../admin/planner/types';

interface ProjectReportPageProps {
  params: Promise<{
    projectId: string;
  }>;
}

export default function ProjectReportPage({ params }: ProjectReportPageProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const { projects, tasks, users, loading } = usePlanner();
  const [project, setProject] = useState<Project | null>(null);
  const [accessDenied, setAccessDenied] = useState(false);
  const [projectId, setProjectId] = useState<string | null>(null);

  // Handle async params
  useEffect(() => {
    params.then(({ projectId }) => {
      setProjectId(projectId);
    });
  }, [params]);

  useEffect(() => {
    if (!loading && session?.user?.email && projectId) {
      const foundProject = projects.find(p => p.id === projectId);
      if (foundProject) {
        // Check if the current user is an admin
        const isAdmin = users.find(u =>
          u.email === session.user?.email && u.role === 'admin'
        );

        // Check if the current user is the project owner or a member
        const isOwner = foundProject.owner === session.user?.email;

        // Get the current user's ID
        const currentUser = users.find(u => u.email === session.user?.email);
        const isMember = currentUser && foundProject.members.includes(currentUser.id);

        if (isAdmin || isOwner || isMember) {
          setProject(foundProject);
        } else {
          setAccessDenied(true);
        }
      } else {
        // Project not found, redirect back to analytics
        router.push('/services/admin/analytics');
      }
    }
  }, [loading, projects, projectId, router, session, users]);

  const handlePrint = () => {
    window.print();
  };

  const handleExportPDF = () => {
    // This would be implemented with a PDF generation library
    alert('PDF export functionality would be implemented here');
  };

  const handleShare = () => {
    // This would be implemented with a sharing mechanism
    alert('Share functionality would be implemented here');
  };

  if (accessDenied) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-center mt-20">
            <div className="bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full text-center">
              <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
              <p className="text-gray-300 mb-6">
                You don't have permission to view this project's analytics. Only project members and administrators can access this data.
              </p>
              <button
                onClick={() => router.push('/services/admin/analytics')}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md text-white transition-colors"
              >
                Return to Analytics
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (loading || !project || !projectId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl text-gray-300">Loading project report...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div className="flex items-center">
            <button
              onClick={() => router.push('/services/admin/analytics')}
              className="text-gray-400 hover:text-white mr-4"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-white">Project Report</h1>
              <p className="text-gray-400 mt-1">{project.name}</p>
            </div>
          </div>

          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            <button
              onClick={handlePrint}
              className="flex items-center px-3 py-1.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              <Printer className="w-4 h-4 mr-2" />
              Print
            </button>

            <button
              onClick={handleExportPDF}
              className="flex items-center px-3 py-1.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              <Download className="w-4 h-4 mr-2" />
              Export PDF
            </button>

            <button
              onClick={handleShare}
              className="flex items-center px-3 py-1.5 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              <Share className="w-4 h-4 mr-2" />
              Share
            </button>
          </div>
        </div>

        {/* Project Report */}
        <ProjectReport
          project={project}
          tasks={tasks}
          users={users}
        />
      </div>
    </div>
  );
}
