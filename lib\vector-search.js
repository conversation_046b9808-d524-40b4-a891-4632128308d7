import { Pinecone } from '@pinecone-database/pinecone';
import { OpenAIEmbeddings } from '@langchain/openai';

/**
 * Vector search client for semantic search functionality
 */
class VectorSearchClient {
  constructor() {
    this.pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY,
    });
    
    this.index = this.pinecone.Index(process.env.PINECONE_INDEX);
    
    this.embeddings = new OpenAIEmbeddings({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  /**
   * Search for similar content using vector embeddings
   * @param {string} query - The search query
   * @param {string} namespace - Optional namespace to search within
   * @param {number} topK - Number of results to return
   * @returns {Promise<Array>} - Search results
   */
  async search(query, namespace = null, topK = 5) {
    try {
      // Generate embedding for the query
      const queryEmbedding = await this.embeddings.embedQuery(query);
      
      // Prepare search parameters
      const searchParams = {
        vector: queryEmbedding,
        topK,
        includeMetadata: true,
      };
      
      // Add namespace if provided
      if (namespace) {
        searchParams.namespace = namespace;
      }
      
      // Perform the search
      const results = await this.index.query(searchParams);
      
      return results.matches.map(match => ({
        id: match.id,
        score: match.score,
        metadata: match.metadata,
      }));
    } catch (error) {
      console.error('Vector search error:', error);
      throw new Error(`Failed to perform vector search: ${error.message}`);
    }
  }

  /**
   * Store content embeddings in the vector database
   * @param {string} content - The content to embed
   * @param {string} id - Unique identifier for the content
   * @param {Object} metadata - Metadata for the content
   * @param {string} namespace - Optional namespace to store within
   * @returns {Promise<Object>} - Result of the operation
   */
  async storeEmbedding(content, id, metadata = {}, namespace = null) {
    try {
      // Generate embedding for the content
      const embedding = await this.embeddings.embedQuery(content);
      
      // Prepare upsert parameters
      const upsertParams = {
        id,
        values: embedding,
        metadata,
      };
      
      // Perform the upsert
      if (namespace) {
        await this.index.namespace(namespace).upsert([upsertParams]);
      } else {
        await this.index.upsert([upsertParams]);
      }
      
      return {
        success: true,
        id,
        namespace: namespace || 'default',
      };
    } catch (error) {
      console.error('Vector storage error:', error);
      throw new Error(`Failed to store vector embedding: ${error.message}`);
    }
  }

  /**
   * Delete embeddings from the vector database
   * @param {Array<string>} ids - Array of IDs to delete
   * @param {string} namespace - Optional namespace to delete from
   * @returns {Promise<Object>} - Result of the operation
   */
  async deleteEmbeddings(ids, namespace = null) {
    try {
      if (namespace) {
        await this.index.namespace(namespace).deleteMany(ids);
      } else {
        await this.index.deleteMany(ids);
      }
      
      return {
        success: true,
        deletedCount: ids.length,
        namespace: namespace || 'default',
      };
    } catch (error) {
      console.error('Vector deletion error:', error);
      throw new Error(`Failed to delete vector embeddings: ${error.message}`);
    }
  }
}

// Export a singleton instance
export const vectorSearchClient = new VectorSearchClient();

/**
 * Process content for vector search by chunking and creating embeddings
 * @param {string} content - The content to process
 * @param {string} documentId - Document identifier
 * @param {Object} metadata - Additional metadata
 * @returns {Promise<Object>} - Processing results
 */
export async function processContentForVectorSearch(content, documentId, metadata = {}) {
  try {
    // Split content into chunks (implement your chunking logic here)
    const chunks = splitContentIntoChunks(content);
    
    const results = [];
    
    // Process each chunk
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkId = `${documentId}_chunk_${i}`;
      
      // Add chunk-specific metadata
      const chunkMetadata = {
        ...metadata,
        documentId,
        chunkId,
        chunkIndex: i,
        totalChunks: chunks.length,
        chunkSize: chunk.length,
      };
      
      // Store the embedding
      const result = await vectorSearchClient.storeEmbedding(
        chunk,
        chunkId,
        chunkMetadata,
        documentId
      );
      
      results.push(result);
    }
    
    return {
      success: true,
      documentId,
      totalChunks: chunks.length,
      results,
    };
  } catch (error) {
    console.error('Content processing error:', error);
    throw new Error(`Failed to process content for vector search: ${error.message}`);
  }
}

/**
 * Split content into chunks for vector storage
 * @param {string} content - The content to split
 * @param {number} chunkSize - Maximum size of each chunk
 * @param {number} overlap - Overlap between chunks
 * @returns {Array<string>} - Array of content chunks
 */
function splitContentIntoChunks(content, chunkSize = 1000, overlap = 200) {
  if (!content || typeof content !== 'string') {
    return [];
  }
  
  // Simple paragraph-based splitting
  const paragraphs = content.split('\n\n');
  const chunks = [];
  let currentChunk = '';
  
  for (const paragraph of paragraphs) {
    if (currentChunk.length + paragraph.length > chunkSize) {
      // Add current chunk to results
      chunks.push(currentChunk.trim());
      
      // Start new chunk with overlap
      const words = currentChunk.split(' ');
      const overlapWords = words.slice(-Math.floor(overlap / 5)); // Approximate word count for overlap
      currentChunk = overlapWords.join(' ') + '\n\n' + paragraph;
    } else {
      // Add paragraph to current chunk
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph;
    }
  }
  
  // Add the last chunk if not empty
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }
  
  return chunks;
}
