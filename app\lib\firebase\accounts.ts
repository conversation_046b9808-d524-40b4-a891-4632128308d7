import { doc, getDoc, setDoc, updateDoc } from 'firebase/firestore';
import { db } from '../../../components/firebase';

/**
 * Checks if a user has access to the marketing agent tests
 * @param email The user's email address
 * @returns A promise that resolves to a boolean indicating if the user has access
 */
export const checkMarketingAgentAccess = async (email: string): Promise<boolean> => {
  // System admin always has access
  if (email === '<EMAIL>') {
    return true;
  }

  try {
    // Check user rights in Accounts collection
    const accountDoc = await getDoc(doc(db, 'Accounts', email));
    
    if (accountDoc.exists()) {
      const accountData = accountDoc.data();
      
      // Check if user has accessRights.Admin or accessRights.Agents set to true
      if (accountData.accessRights && 
          (accountData.accessRights.Admin === true || 
           accountData.accessRights.Agents === true)) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('Error checking marketing agent access:', error);
    return false;
  }
};

/**
 * Grants a user access to the marketing agent tests
 * @param email The user's email address
 * @returns A promise that resolves when the operation is complete
 */
export const grantMarketingAgentAccess = async (email: string): Promise<void> => {
  try {
    const accountRef = doc(db, 'Accounts', email);
    const accountDoc = await getDoc(accountRef);
    
    if (accountDoc.exists()) {
      // Update existing account
      const accountData = accountDoc.data();
      
      // Create or update accessRights field
      const accessRights = accountData.accessRights || {};
      accessRights.Agents = true;
      
      await updateDoc(accountRef, { accessRights });
    } else {
      // Create new account with access rights
      await setDoc(accountRef, {
        email,
        createdAt: new Date().toISOString(),
        accessRights: {
          Agents: true,
          Admin: false
        }
      });
    }
  } catch (error) {
    console.error('Error granting marketing agent access:', error);
    throw error;
  }
};

/**
 * Revokes a user's access to the marketing agent tests
 * @param email The user's email address
 * @returns A promise that resolves when the operation is complete
 */
export const revokeMarketingAgentAccess = async (email: string): Promise<void> => {
  // Don't allow revoking access from the system admin
  if (email === '<EMAIL>') {
    throw new Error('Cannot revoke access from the system administrator');
  }
  
  try {
    const accountRef = doc(db, 'Accounts', email);
    const accountDoc = await getDoc(accountRef);
    
    if (accountDoc.exists()) {
      const accountData = accountDoc.data();
      
      // Create or update accessRights field
      const accessRights = accountData.accessRights || {};
      accessRights.Agents = false;
      
      await updateDoc(accountRef, { accessRights });
    }
  } catch (error) {
    console.error('Error revoking marketing agent access:', error);
    throw error;
  }
};
