import { NextRequest, NextResponse } from 'next/server';
import { pdfGeneratorTool } from '../../../lib/tools/pdf-generator';

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    const { title, content, documentType, timestamp, modelInfo } = body;

    // Create content for PDF
    const pdfContent = [{
      title: title,
      content: content
    }];
    
    // Set options for PDF generation
    const options = {
      title: `${title} - ${documentType.charAt(0).toUpperCase() + documentType.slice(1)}`,
      subtitle: timestamp ? `Generated on ${timestamp}` : undefined,
      includeCover: true,
      includeToc: false,
      saveToByteStore: false,
      generatedBy: modelInfo?.provider || 'Marketing Agent',
      documentType: documentType
    };
    
    // Generate PDF
    const pdfResult = await pdfGeneratorTool.generatePdf(pdfContent, options);

    // Ensure we have a Buffer for the response
    if (!Buffer.isBuffer(pdfResult)) {
      throw new Error('PDF generation did not return a Buffer');
    }

    // Return the PDF as a response
    return new NextResponse(pdfResult, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${documentType}.pdf"`
      }
    });
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}
