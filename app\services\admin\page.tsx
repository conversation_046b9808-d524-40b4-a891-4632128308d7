'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  Calendar,
  Users,
  BarChart2,
  <PERSON><PERSON>s,
  ArrowRight,
  PlusCircle,
  RefreshCw,
  UserCheck,
  <PERSON>rkles
} from 'lucide-react';
import { usePlanner } from '../../context/PlannerContext';
import { Project, Task } from '../../../admin/planner/types';
import TabsContextDebugger from '../../components/TabsContextDebugger';

export default function AdminDashboard() {
  const { users, projects, tasks, loading, refreshData } = usePlanner();
  const [stats, setStats] = useState({
    projects: 0,
    activeProjects: 0,
    completedProjects: 0,
    users: 0,
    tasks: 0,
    pendingTasks: 0,
    completedTasks: 0,
    upcomingDeadlines: 0,
    nextDeadline: ''
  });

  useEffect(() => {
    // Only calculate stats when data is loaded and not loading
    if (!loading && projects.length > 0 && tasks.length > 0) {
      // Calculate stats from actual data using a more efficient approach
      // Use a single pass through the arrays where possible

      // Project stats
      let activeProjects = 0;
      let completedProjects = 0;

      // Optimize by counting in a single loop
      for (const project of projects) {
        if (project.status === 'Active') activeProjects++;
        else if (project.status === 'Completed') completedProjects++;
      }

      // Task stats
      let pendingTasks = 0;
      let completedTasks = 0;
      const today = new Date();
      const nextWeek = new Date(today);
      nextWeek.setDate(today.getDate() + 7);

      // Track upcoming tasks in the same loop
      let upcomingTasks = [];

      for (const task of tasks) {
        if (task.status === 'Complete') {
          completedTasks++;
        } else {
          pendingTasks++;

          // Check if it's an upcoming task
          const dueDate = new Date(task.dueDate);
          if (dueDate >= today && dueDate <= nextWeek) {
            upcomingTasks.push(task);
          }
        }
      }

      // Sort upcoming tasks by due date (only if we have any)
      let nextDeadline = 'None';
      if (upcomingTasks.length > 0) {
        upcomingTasks.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
        nextDeadline = new Date(upcomingTasks[0].dueDate).toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric'
        });
      }

      // Update stats in a single state update
      setStats({
        projects: projects.length,
        activeProjects,
        completedProjects,
        users: users.length,
        tasks: tasks.length,
        pendingTasks,
        completedTasks,
        upcomingDeadlines: upcomingTasks.length,
        nextDeadline
      });
    }
  }, [loading, projects, tasks, users]);

  // Add state for refresh button
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
    } catch (error) {
      console.error('Error during manual refresh:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-100">Admin Dashboard</h1>

        <button
          onClick={handleRefresh}
          disabled={isRefreshing || loading}
          className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RefreshCw className={`w-5 h-5 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
        </button>

      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-purple-500">
          <h2 className="text-lg font-medium text-gray-300">Projects</h2>
          <p className="text-3xl font-bold text-gray-100 mt-2">{stats.projects}</p>
          <p className="text-sm text-gray-400 mt-1">{stats.activeProjects} active, {stats.completedProjects} completed</p>
        </div>

        <div className="bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-blue-500">
          <h2 className="text-lg font-medium text-gray-300">Team Members</h2>
          <p className="text-3xl font-bold text-gray-100 mt-2">{stats.users}</p>
          <p className="text-sm text-gray-400 mt-1">Ready to collaborate</p>
        </div>

        <div className="bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-green-500">
          <h2 className="text-lg font-medium text-gray-300">Tasks</h2>
          <p className="text-3xl font-bold text-gray-100 mt-2">{stats.tasks}</p>
          <p className="text-sm text-gray-400 mt-1">{stats.pendingTasks} pending, {stats.completedTasks} completed</p>
        </div>

        <div className="bg-gray-800 rounded-lg shadow-md p-6 border-l-4 border-yellow-500">
          <h2 className="text-lg font-medium text-gray-300">Upcoming Deadlines</h2>
          <p className="text-3xl font-bold text-gray-100 mt-2">{stats.upcomingDeadlines}</p>
          <p className="text-sm text-gray-400 mt-1">Next: {stats.nextDeadline}</p>
        </div>
      </div>

      {/* Tabs Context Debugger */}
      <TabsContextDebugger />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-lg font-medium text-gray-300 mb-4">Quick Actions</h2>

          <div className="space-y-4">
            <Link
              href="/services/admin/planner"
              className="flex items-center justify-between p-4 bg-purple-900/30 rounded-lg hover:bg-purple-900/50 transition-colors"
            >
              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-purple-400 mr-3" />
                <span className="font-medium text-gray-200">Project Planner</span>
              </div>
              <ArrowRight className="w-5 h-5 text-purple-400" />
            </Link>

            <Link
              href="/services/admin/calendar"
              className="flex items-center justify-between p-4 bg-cyan-900/30 rounded-lg hover:bg-cyan-900/50 transition-colors"
            >
              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-cyan-400 mr-3" />
                <span className="font-medium text-gray-200">Calendar View</span>
              </div>
              <ArrowRight className="w-5 h-5 text-cyan-400" />
            </Link>

            <Link
              href="/services/admin/team"
              className="flex items-center justify-between p-4 bg-blue-900/30 rounded-lg hover:bg-blue-900/50 transition-colors"
            >
              <div className="flex items-center">
                <Users className="w-5 h-5 text-blue-400 mr-3" />
                <span className="font-medium text-gray-200">Manage Team</span>
              </div>
              <ArrowRight className="w-5 h-5 text-blue-400" />
            </Link>

            <Link
              href="/services/admin/analytics"
              className="flex items-center justify-between p-4 bg-green-900/30 rounded-lg hover:bg-green-900/50 transition-colors"
            >
              <div className="flex items-center">
                <BarChart2 className="w-5 h-5 text-green-400 mr-3" />
                <span className="font-medium text-gray-200">View Analytics</span>
              </div>
              <ArrowRight className="w-5 h-5 text-green-400" />
            </Link>

            <Link
              href="/services/admin/settings"
              className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center">
                <Settings className="w-5 h-5 text-gray-400 mr-3" />
                <span className="font-medium text-gray-200">Settings</span>
              </div>
              <ArrowRight className="w-5 h-5 text-gray-400" />
            </Link>

            <Link
              href="/services/admin/planner"
              className="flex items-center justify-between p-4 bg-indigo-900/30 rounded-lg hover:bg-indigo-900/50 transition-colors"
            >
              <div className="flex items-center">
                <PlusCircle className="w-5 h-5 text-indigo-400 mr-3" />
                <span className="font-medium text-gray-200">Create New Project</span>
              </div>
              <ArrowRight className="w-5 h-5 text-indigo-400" />
            </Link>

            <Link
              href="/services/admin/marketing-access"
              className="flex items-center justify-between p-4 bg-amber-900/30 rounded-lg hover:bg-amber-900/50 transition-colors"
            >
              <div className="flex items-center">
                <UserCheck className="w-5 h-5 text-amber-400 mr-3" />
                <span className="font-medium text-gray-200">Marketing Agent Access</span>
              </div>
              <ArrowRight className="w-5 h-5 text-amber-400" />
            </Link>

            <Link
              href="/marketing-agent-tests/login"
              className="flex items-center justify-between p-4 bg-purple-900/30 rounded-lg hover:bg-purple-900/50 transition-colors"
            >
              <div className="flex items-center">
                <Sparkles className="w-5 h-5 text-purple-400 mr-3" />
                <span className="font-medium text-gray-200">Marketing Agent Tests</span>
              </div>
              <ArrowRight className="w-5 h-5 text-purple-400" />
            </Link>

            <Link
              href="/services/pmo"
              className="flex items-center justify-between p-4 bg-pink-900/30 rounded-lg hover:bg-pink-900/50 transition-colors"
            >
              <div className="flex items-center">
                <Sparkles className="w-5 h-5 text-pink-400 mr-3" />
                <span className="font-medium text-gray-200">PMO Agent</span>
              </div>
              <ArrowRight className="w-5 h-5 text-pink-400" />
            </Link>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-lg font-medium text-gray-300 mb-4">Recent Activity</h2>

          {tasks.length === 0 && users.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <p>No recent activity</p>
              <p className="text-sm mt-2">Start by creating a project and adding tasks</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="border-l-2 border-purple-500 pl-4 py-1">
                <p className="text-sm text-gray-400">
                  <span className="font-medium text-gray-300">System</span> initialized with
                  <span className="font-medium text-gray-300"> {users.length} users</span>
                </p>
                <p className="text-xs text-gray-500 mt-1">Just now</p>
              </div>

              <div className="border-l-2 border-blue-500 pl-4 py-1">
                <p className="text-sm text-gray-400">
                  <span className="font-medium text-gray-300">You</span> logged in to the
                  <span className="font-medium text-gray-300"> admin dashboard</span>
                </p>
                <p className="text-xs text-gray-500 mt-1">Just now</p>
              </div>

              <div className="border-l-2 border-green-500 pl-4 py-1">
                <p className="text-sm text-gray-400">
                  <span className="font-medium text-gray-300">Database</span> connected and
                  <span className="font-medium text-gray-300"> ready for use</span>
                </p>
                <p className="text-xs text-gray-500 mt-1">Just now</p>
              </div>
            </div>
          )}

          <button className="mt-4 text-sm text-purple-400 hover:text-purple-300 font-medium">
            View all activity
          </button>
        </div>
      </div>
    </div>
  );
}