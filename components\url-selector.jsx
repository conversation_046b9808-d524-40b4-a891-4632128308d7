"use client";

import { useState } from "react";
import { CheckCircle, ExternalLink, Search } from "lucide-react";

/**
 * UrlSelector component displays a list of URLs with checkboxes for selection
 * 
 * @param {Object} props Component props
 * @param {Array<string>} props.urls List of URLs to display
 * @param {Array<string>} props.selectedUrls List of currently selected URLs
 * @param {Function} props.onUrlSelection Callback function when URL selection changes
 * @returns {JSX.Element} Rendered component
 */
export default function UrlSelector({ urls, selectedUrls, onUrlSelection }) {
  const [searchTerm, setSearchTerm] = useState("");
  
  // Filter URLs based on search term
  const filteredUrls = urls.filter(url => 
    url.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Handle empty states
  if (!urls || urls.length === 0) {
    return (
      <div className="p-4 bg-zinc-800 rounded-md text-zinc-400 text-center border border-zinc-700">
        No URLs found. Try scraping a different website.
      </div>
    );
  }
  
  return (
    <div className="url-selector">
      {/* Search filter for URLs */}
      {urls.length > 5 && (
        <div className="mb-4 relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Filter URLs..."
            className="pl-10 pr-4 py-2 w-full bg-zinc-800 rounded-md border border-zinc-700 
                     text-zinc-200 placeholder-zinc-500 focus:outline-none focus:ring-2 
                     focus:ring-blue-500 focus:border-blue-500"
          />
          <Search className="absolute left-3 top-2.5 text-zinc-500" size={18} />
        </div>
      )}
      
      {/* Display filtered results summary if filtering */}
      {searchTerm && (
        <div className="mb-2 text-sm text-zinc-400">
          Showing {filteredUrls.length} of {urls.length} URLs
        </div>
      )}
      
      {/* URL list with checkboxes */}
      <div className="max-h-96 overflow-y-auto pr-2 space-y-2">
        {filteredUrls.length > 0 ? (
          filteredUrls.map((url, index) => {
            const isSelected = selectedUrls.includes(url);
            
            return (
              <div 
                key={`url-${index}`}
                className={`flex items-start p-3 rounded-md border ${
                  isSelected 
                    ? "bg-blue-900/20 border-blue-800" 
                    : "bg-zinc-800 border-zinc-700 hover:bg-zinc-800/70"
                }`}
              >
                <div className="flex-shrink-0 mr-3">
                  <input
                    type="checkbox"
                    id={`url-${index}`}
                    checked={isSelected}
                    onChange={(e) => onUrlSelection(url, e.target.checked)}
                    className="h-5 w-5 rounded border-zinc-600 text-blue-600 
                              focus:ring-blue-500 focus:ring-offset-zinc-900"
                  />
                </div>
                
                <div className="flex-grow min-w-0">
                  <label 
                    htmlFor={`url-${index}`}
                    className="block text-sm font-medium text-zinc-200 cursor-pointer overflow-hidden text-ellipsis"
                  >
                    {url}
                  </label>
                  
                  <div className="mt-1 flex gap-2">
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-xs text-blue-400 hover:text-blue-300"
                    >
                      <ExternalLink size={12} className="mr-1" />
                      Open
                    </a>
                    
                    {isSelected && (
                      <span className="inline-flex items-center text-xs text-emerald-400">
                        <CheckCircle size={12} className="mr-1" />
                        Selected
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="p-4 bg-zinc-800 rounded-md text-zinc-400 text-center border border-zinc-700">
            No matching URLs found. Try a different search term.
          </div>
        )}
      </div>
    </div>
  );
}