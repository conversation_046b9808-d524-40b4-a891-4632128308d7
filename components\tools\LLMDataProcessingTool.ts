import { ChatGroq } from "@langchain/groq";
import { AIMessage, BaseMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import Ajv, { ValidateFunction } from "ajv";

// Define Interfaces for Processing Metrics and Errors
interface ProcessingMetrics {
    startTime: number;
    endTime?: number;
    totalDuration?: number;
    totalSteps?: number;
    successRate?: number;
    processingSteps: Array<{
        step: string;
        duration: number;
        success: boolean;
        error?: string;
    }>;
}

interface EnhancedMetrics extends ProcessingMetrics {
    errorContext?: string;
}

interface ProcessingError extends Error {
    metrics: EnhancedMetrics;
}

// Define Interface for LangChainResponse
interface LangChainResponse {
    type: string;
    id: string[];
    kwargs?: {
        content?: string;
        additional_kwargs?: Record<string, any>;
    };
    content?: string;
}

// Define Interfaces for Normalized Data
interface NormalizedDataPoint {
    time?: string;
    metrics: Record<string, number>;
    categories: string[];
}

interface NormalizedMetadata {
    type: 'time-series' | 'categorical' | 'distribution' | 'comparison';
    metrics: string[];
    timeUnit?: 'day' | 'month' | 'quarter' | 'year' | null;
}

interface TimeMatch {
    value: string;
    type: string;
    originalFormat: string;
}

interface ProcessedResponse {
    data: NormalizedDataPoint[];
    metadata: NormalizedMetadata;
    timeMatches: TimeMatch[];
}

// Define JSON Schema for ProcessedResponse Validation
const processedResponseSchema = {
    type: "object",
    properties: {
        data: {
            type: "array",
            items: {
                type: "object",
                properties: {
                    time: { type: ["string", "null"] },
                    metrics: {
                        type: "object",
                        patternProperties: {
                            "^[A-Za-z ]+$": { type: "number" }
                        },
                        additionalProperties: false
                    },
                    categories: {
                        type: "array",
                        items: { type: "string" }
                    }
                },
                required: ["metrics", "categories"]
            }
        },
        metadata: {
            type: "object",
            properties: {
                type: { type: "string", enum: ["time-series", "categorical", "distribution", "comparison"] },
                metrics: {
                    type: "array",
                    items: { type: "string" }
                },
                timeUnit: { type: ["string", "null"], enum: ["day", "month", "quarter", "year", null] }
            },
            required: ["type", "metrics"]
        },
        timeMatches: {
            type: "array",
            items: {
                type: "object",
                properties: {
                    value: { type: "string" },
                    type: { type: "string" },
                    originalFormat: { type: "string" }
                },
                required: ["value", "type", "originalFormat"]
            }
        }
    },
    required: ["data", "metadata", "timeMatches"]
};

// Main Class Implementation
export class LLMDataProcessingTool {
  setNavigateFunction(arg0: (url: string) => void) {
    throw new Error('Method not implemented.');
  }
    private readonly GroqChat: ChatGroq;
    private metrics: ProcessingMetrics;
    private ajv: Ajv;
    private validateProcessedResponse: ValidateFunction;
    constructor(
        public readonly apiKey: string,
        public readonly config: {
            model?: string;
            temperature?: number;
            maxRetries?: number;
        } = {}
    ) {
        // Initialize ChatOpenAI with provided configurations
        this.GroqChat = new ChatGroq({
            apiKey: process.env.GROQ_API_KEY || '********************************************************',
            model: config.model || 'llama-3.3-70b-versatile',
            maxRetries: config.maxRetries || 3
        });

        // Initialize Processing Metrics
        this.metrics = {
            startTime: Date.now(),
            processingSteps: [],
            totalSteps: 0,
            successRate: 0
        };

        // Initialize AJV for JSON Schema Validation
        this.ajv = new Ajv({ allErrors: true });
        this.validateProcessedResponse = this.ajv.compile(processedResponseSchema);

        // Log Initialization
        this.debugLog('INIT', 'Tool initialized with configuration', {
            model: config.model || "llama-3.3-70b-versatile",
            maxRetries: config.maxRetries || 3
        });
    }

    /**
     * Logs debug information with structured data.
     * @param phase The phase of processing.
     * @param message The log message.
     * @param data Optional additional data to log.
     */
    private debugLog(phase: string, message: string, data?: any) {
        const timestamp = new Date().toISOString();
        const logEntry = {
            timestamp,
            phase,
            message,
            data: this.safeStringify(data)
        };

        console.log(`[${timestamp}][LLMDataProcessingTool][${phase}] ${message}`);
        if (data) {
            console.log(`[${timestamp}][LLMDataProcessingTool][${phase}] Data Structure:`, logEntry.data);
            console.log(`[${timestamp}][LLMDataProcessingTool][${phase}] Type Information:`, {
                constructorName: data?.constructor?.name,
                typeOf: typeof this.safeStringify(data),
                isArray: Array.isArray(data),
                hasContent: 'content' in (data || {}),
                hasKwargs: 'kwargs' in (data || {}),
                propertyNames: data ? Object.getOwnPropertyNames(data) : []
            });
        }

        this.recordMetric(phase, true);
        return logEntry;
    }

    /**
     * Records processing metrics for each step.
     * @param step The processing step name.
     * @param success Whether the step was successful.
     * @param error Optional error message.
     */
    private recordMetric(step: string, success: boolean, error?: string) {
        const currentTime = Date.now();
        const lastStep = this.metrics.processingSteps[this.metrics.processingSteps.length - 1];
        const duration = lastStep ? currentTime - (lastStep.duration + this.metrics.startTime) : 0;

        this.metrics.processingSteps.push({
            step,
            duration,
            success,
            error
        });

        this.metrics.totalSteps = this.metrics.processingSteps.length;
        this.metrics.successRate = this.calculateSuccessRate();
        this.metrics.totalDuration = currentTime - this.metrics.startTime;
    }

    /**
     * Safely stringifies data to JSON, handling circular references and functions.
     * @param data The data to stringify.
     * @returns The JSON string representation.
     */
    private safeStringify(data: any): string {
        const seen = new WeakSet();
        return JSON.stringify(data, (key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (seen.has(value)) {
                    return '[Circular]';
                }
                seen.add(value);
            }
            if (typeof value === 'function') {
                return '[Function]';
            }
            return value;
        }, 2);
    }

    /**
     * Determines if the response conforms to LangChainResponse interface.
     * @param response The response object to check.
     * @returns True if LangChainResponse, else false.
     */
    private isLangChainResponse(response: any): response is LangChainResponse {
        return (
            typeof response === 'object' &&
            response !== null &&
            typeof response.type === 'string' &&
            Array.isArray(response.id) &&
            (!response.kwargs || typeof response.kwargs === 'object')
        );
    }

    /**
     * Extracts the message content from various response types.
     * @param response The response object.
     * @returns The extracted and cleaned content.
     */
    private extractMessageContent(response: any): string {
        this.debugLog('EXTRACT', 'Starting content extraction', { response });

        try {
            if (!response) {
                throw new Error('No response received');
            }

            let content: string | undefined;

            if (this.isLangChainResponse(response)) {
                content = response.kwargs?.content || response.content;
            } else if (response instanceof AIMessage) {
                content = this.extractContentFromAIMessage(response);
            } else if (typeof response === 'object') {
                content = response.text || response.message || response.content;
            } else if (typeof response === 'string') {
                content = response;
            }

            if (!content || typeof content !== 'string') {
                throw new Error('Unable to extract valid content from response');
            }

            const cleanedContent = this.cleanMarkdownContent(content);
            this.debugLog('EXTRACT', 'Content cleaned and prepared', { cleanedContent });

            return cleanedContent;

        } catch (error) {
            this.recordMetric('EXTRACT', false, error instanceof Error ? error.message : 'Unknown error');
            throw this.enhanceError(error, 'Content extraction failed');
        }
    }

    /**
     * Extracts content from AIMessage instances.
     * @param message The AIMessage instance.
     * @returns The extracted content string.
     */
    private extractContentFromAIMessage(message: AIMessage): string {
        if (Array.isArray(message.content)) {
            return message.content.join(' ');
        }
        return message.content || '';
    }

    /**
     * Cleans markdown content by removing code blocks and trimming whitespace.
     * @param content The raw content string.
     * @returns The cleaned content string.
     */
    private cleanMarkdownContent(content: string): string {
        let cleaned = content.replace(/\`\`\`(?:json)?\n/g, '');
        cleaned = cleaned.replace(/\`\`\`$/gm, '');
        cleaned = cleaned.trim().replace(/\r\n/g, '\n');
        return cleaned;
    }

    /**
     * Parses the cleaned content string into a JSON object.
     * @param content The cleaned content string.
     * @returns The parsed JSON object.
     */
    private parseContent(content: string): any {
        try {
            this.debugLog('PARSE', 'Attempting to parse content', { contentSample: content.slice(0, 100) });

            const cleanedContent = this.cleanMarkdownContent(content);
            this.debugLog('PARSE', 'Content cleaned', { cleanedContent });

            // Sanitize the content by replacing 'undefined' with 'null'
            const sanitizedContent = cleanedContent.replace(/: undefined/g, ': null');

            const parsedContent = JSON.parse(sanitizedContent);
            this.debugLog('PARSE', 'Successfully parsed content', { parsedContent });

            return parsedContent;
        } catch (error) {
            this.recordMetric('PARSE', false, error instanceof Error ? error.message : 'Unknown error');
            const errorMessage = `Failed to parse response content: ${error instanceof Error ? error.message : 'Unknown error'}`;
            const contextualError = new Error(`${errorMessage}\nProblematic content: ${content.slice(0, 200)}...`);
            throw contextualError;
        }
    }

    /**
     * Recursively searches for a specified field within a nested object.
     * @param obj The object to search within.
     * @param fieldName The name of the field to find.
     * @returns The value of the field if found; otherwise, undefined.
     */
    private findFieldRecursively(obj: any, fieldName: string): any | undefined {
        if (obj === null || typeof obj !== 'object') return undefined;

        if (obj.hasOwnProperty(fieldName)) {
            return obj[fieldName];
        }

        for (const key of Object.keys(obj)) {
            const result = this.findFieldRecursively(obj[key], fieldName);
            if (result !== undefined) {
                return result;
            }
        }

        return undefined;
    }

    /**
     * Validates and normalizes the parsed response.
     * @param response The parsed JSON response.
     * @returns The ProcessedResponse object.
     */
    private validateAndNormalizeResponse(response: any): ProcessedResponse {
        this.debugLog('VALIDATION', 'Starting response validation using JSON Schema and recursive search');

        // Validate response against JSON Schema
        const valid = this.validateProcessedResponse(response);
        if (!valid) {
            this.debugLog('VALIDATION_ERROR', 'Schema validation failed', { errors: this.validateProcessedResponse.errors });
            throw new Error(`Invalid response structure: ${this.ajv.errorsText(this.validateProcessedResponse.errors)}`);
        }

        try {
            // Normalize Data
            const normalizedData = this.normalizeData((response as any).data);
            this.debugLog('VALIDATION', 'Data normalized', { normalizedData });

            // Dynamically find metadata using recursive search
            const metadata = this.findFieldRecursively(response, 'metadata');
            if (!metadata || typeof metadata !== 'object') {
                this.debugLog('VALIDATION_ERROR', 'Metadata is missing or not an object', { metadata });
                throw new Error('Invalid response structure: Metadata is missing or not an object');
            }
            this.debugLog('VALIDATION', 'Metadata found', { metadata });

            // Normalize Metadata
            const normalizedMetadata = {
                type: this.validateType(metadata.type),
                metrics: Array.isArray(metadata.metrics) ? metadata.metrics : [],
                timeUnit: this.validateTimeUnit(metadata.timeUnit)
            };
            this.debugLog('VALIDATION', 'Metadata normalized', { normalizedMetadata });

           // Normalize Time Matches
            const timeMatches = Array.isArray(this.findFieldRecursively(response, 'timeMatches'))
              ? (this.findFieldRecursively(response, 'timeMatches') as any[]).map(this.normalizeTimeMatch.bind(this))
              : [];
            this.debugLog('VALIDATION', 'Time matches normalized', { timeMatches });

            // Construct ProcessedResponse
            const result: ProcessedResponse = {
                data: normalizedData,
                metadata: normalizedMetadata,
                timeMatches
            };

            this.debugLog('VALIDATION', 'Response validation complete', { result });
            return result;

        } catch (error) {
            this.debugLog('VALIDATION_ERROR', 'Failed during response normalization', { error });
            throw new Error(`Response validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Normalizes the data array into an array of NormalizedDataPoint objects.
     * @param data The raw data array.
     * @returns The normalized data array.
     */
    private normalizeData(data: any[]): NormalizedDataPoint[] {
        if (!Array.isArray(data)) {
            throw new Error('Invalid data structure: Expected an array');
        }

        // Use an arrow function to maintain 'this' context
        return data.flatMap(item => this.normalizeDataPoint(item));
    }

   /**
     * Normalizes a single data point.
     * @param item The raw data point.
     * @returns An array containing a single NormalizedDataPoint object.
     */
    private normalizeDataPoint = (item: any): NormalizedDataPoint[] => {
        if (Array.isArray(item)) {
            return item.flatMap(innerItem => this.normalizeDataPoint(innerItem));
        }

        if (typeof item !== 'object' || item === null) {
            console.warn('Invalid data point: Expected non-null object');
            return [];
        }

        if (!item.metrics) {
            console.warn('Invalid data point: Missing "metrics" property');
            return [];
        }

        return [{
            time: item.time ? this.standardizeTimeFormat(item.time) : undefined,
            metrics: this.normalizeMetrics(item.metrics),
            categories: Array.isArray(item.categories) ? item.categories : []
        }];
    }

    /**
     * Recursively normalizes the entire data structure.
     * @param data The raw data structure.
     * @returns The normalized data structure.
     */
    private normalizeDataStructure(data: any): any {
        if (Array.isArray(data)) {
            return data.map(item => this.normalizeDataStructure(item));
        }

        if (typeof data === 'object' && data !== null) {
            const normalized: Record<string, any> = {};
            for (const [key, value] of Object.entries(data)) {
                normalized[key] = this.normalizeDataStructure(value);
            }
            return normalized;
        }

        return data;
    }

    /**
     * Validates the response structure.
     * @param response The parsed JSON response.
     * @returns True if valid, else false.
     */
    private isValidResponse(response: any): boolean {
        const valid = this.validateProcessedResponse(response);
        return valid;
    }

    /**
     * Normalizes the metrics object by ensuring all values are numbers.
     * @param metrics The raw metrics object.
     * @returns The normalized metrics object.
     */
    private normalizeMetrics(metrics: any): Record<string, number> {
        if (typeof metrics !== 'object' || metrics === null) {
            console.warn('Invalid metrics structure: Expected non-null object');
            return {};
        }

        const normalized: Record<string, number> = {};

        for (const [key, value] of Object.entries(metrics)) {
            try {
                if (typeof value === 'number' && !isNaN(value)) {
                    normalized[key] = value;
                } else if (typeof value === 'string') {
                    const cleanValue = value.replace(/[$,\s]/g, '');
                    const multiplier = this.getValueMultiplier(cleanValue);
                    const baseValue = cleanValue.replace(/[KMB]$/i, '');
                    const numValue = parseFloat(baseValue) * multiplier;

                    if (!isNaN(numValue)) {
                        normalized[key] = numValue;
                    } else {
                        console.warn(`Unable to parse metric value: ${value}`);
                    }
                } else {
                    console.warn(`Invalid metric value type: ${typeof value}`);
                }
            } catch (error) {
                console.error(`Failed to normalize metric '${key}': ${error instanceof Error ? error.message : 'Unknown error'}`);
            }
        }

        return normalized;
    }

    /**
     * Determines the multiplier based on suffix (K, M, B).
     * @param value The string value to check.
     * @returns The corresponding multiplier.
     */
    private getValueMultiplier(value: string): number {
        const suffix = value.match(/[KMB]$/i)?.[0]?.toUpperCase();
        switch (suffix) {
            case 'K': return 1000;
            case 'M': return 1000000;
            case 'B': return 1000000000;
            default: return 1;
        }
    }

    /**
     * Validates the visualization type.
     * @param type The type to validate.
     * @returns The validated type.
     */
    private validateType(type: any): 'time-series' | 'categorical' | 'distribution' | 'comparison' {
        const validTypes = ['time-series', 'categorical', 'distribution', 'comparison'] as const;

        if (!type || !validTypes.includes(type)) {
            throw new Error(`Invalid visualization type: ${type}. Must be one of: ${validTypes.join(', ')}`);
        }

        return type;
    }

    /**
     * Validates the time unit.
     * @param unit The time unit to validate.
     * @returns The validated time unit.
     */
    private validateTimeUnit(unit: any): 'day' | 'month' | 'quarter' | 'year' | null {
        if (!unit) return null;

        const validUnits = ['day', 'month', 'quarter', 'year'] as const;

        if (!validUnits.includes(unit)) {
            throw new Error(`Invalid time unit: ${unit}. Must be one of: ${validUnits.join(', ')}`);
        }

        return unit;
    }

    /**
     * Normalizes a time match object.
     * @param match The raw time match object.
     * @returns The normalized TimeMatch object.
     */
    private normalizeTimeMatch(match: any): TimeMatch {
        if (!match || typeof match !== 'object') {
            throw new Error('Invalid time match structure: Expected non-null object');
        }

        const normalized = {
            value: String(match.value || ''),
            type: String(match.type || ''),
            originalFormat: String(match.originalFormat || '')
        };

       // Ensure original format is converted to an empty string if null
        if (match.originalFormat === null) {
            normalized.originalFormat = '';
        }

        if (!normalized.value || !normalized.type) {
            throw new Error('Time match must include non-empty value and type properties');
        }

        return normalized;
    }

    /**
     * Standardizes the time format. Currently returns the time as-is.
     * @param time The raw time string.
     * @returns The standardized time string.
     */
    private standardizeTimeFormat(time: string): string {
        // Since the time represents seasons, return it as is.
        return time;
    }

   /**
     * Processes the input by invoking the AI model and handling the response.
     * @param input The input containing content and optional options.
     * @returns The processed and normalized response.
     */
   async call(input: {
        content: string;
        options?: {
            includeTimeAnalysis?: boolean;
            includeSuggestions?: boolean;
        };
    }): Promise<ProcessedResponse> {
        this.metrics.startTime = Date.now();
        this.debugLog('CALL', 'Processing call request', input);

        try {
            // Build the system prompt based on input type and options
            const systemPrompt = this.buildSystemPrompt(
                this.detectInputType(input.content),
                input.options
            );

            // Invoke the AI model with the prompt and content
            const response = await this.GroqChat.invoke(
                `${systemPrompt}\n\n${input.content}`
            );

            if (!response) {
                throw new Error('No response received from GroqChat');
            }

            this.debugLog('CALL_RESPONSE', 'Raw response received', { response });

            // Extract and clean the message content
            const messageContent = this.extractMessageContent(response);

            // Parse the cleaned content into JSON
            const parsedContent = this.parseContent(messageContent);

            // Normalize the entire data structure recursively
            const normalizedContent = this.normalizeDataStructure(parsedContent);

            // Validate and normalize the response
            const validatedResult = this.validateAndNormalizeResponse(normalizedContent);

            // Record end time
            this.metrics.endTime = Date.now();

            return validatedResult;

        } catch (error) {
            this.metrics.endTime = Date.now();
            this.debugLog('CALL_ERROR', 'Call processing failed', { error });
            throw this.enhanceError(error);
        }
    }

    /**
     * Detects the input type based on content analysis.
     * @param text The input text.
     * @returns The detected input type.
     */
    private detectInputType(text: string): 'table' | 'structured' | 'unstructured' {
        this.debugLog('DETECTION', 'Analyzing input type', { sampleText: text.slice(0, 100) });

        const hasTableMarkers = text.includes('|') ||
                              (text.includes(',') && this.hasConsistentDelimiters(text));
        if (hasTableMarkers) {
            return 'table';
        }

        if (text.includes(':') || text.includes('{') || text.includes('[')) {
            return 'structured';
        }

        return 'unstructured';
    }

    /**
     * Checks if the input text has consistent delimiters (commas).
     * @param text The input text.
     * @returns True if delimiters are consistent, else false.
     */
    private hasConsistentDelimiters(text: string): boolean {
        const lines = text.trim().split('\n');
        if (lines.length < 2) return false;

        const firstLineDelimiters = (lines[0].match(/,/g) || []).length;
        return lines.every(line =>
            (line.match(/,/g) || []).length === firstLineDelimiters
        );
    }

    /**
     * Builds the system prompt based on input type and options.
     * @param inputType The detected input type.
     * @param options Optional processing options.
     * @returns The constructed system prompt string.
     */
     private buildSystemPrompt(
        inputType: 'table' | 'structured' | 'unstructured',
        options?: { includeTimeAnalysis?: boolean; includeSuggestions?: boolean; }
    ): string {
        return `You are a data processing specialist focused on preparing ${inputType} data for visualization.

Your task is to:
1. Extract and normalize all data points
2. Identify time patterns and metrics
3. Maintain numerical precision
4. Structure data consistently

Return exactly this JSON structure:
{
    "data": [{
    "time": string (if applicable),
    "metrics": { [key: string]: number },
    "categories": string[]
}],
    "metadata": {
        "type": "time-series" | "categorical" | "distribution" | "comparison",
        "metrics": string[],
        "timeUnit": "day" | "month" | "quarter" | "year" | null
    }${options?.includeTimeAnalysis ? `,
    "timeMatches": [
        {
            "value": string,
            "type": string,
            "originalFormat": string
        }
    ]` : ''}
}

Processing rules:
- Convert all currencies and numbers to raw numerical values
- Normalize all time formats consistently
- Preserve all decimal precision
- Convert units (k = thousands, M = millions)
- Handle missing or null values appropriately
- Use null instead of undefined for absent values in JSON structures
- ALWAYS ensure categories is an array of strings
- ALWAYS ensure originalFormat is a string, return an empty string if its null or absent.

IMPORTANT: Reply with the JSON structure ONLY`;
    }
    /**
     * Enhances errors with additional metrics and context.
     * @param error The original error.
     * @param context Optional contextual information.
     * @returns The enhanced ProcessingError.
     */
    private enhanceError(error: unknown, context?: string): ProcessingError {
        const baseMessage = error instanceof Error ? error.message : 'Unknown error in data processing';
        const contextualMessage = context ? `${context}: ${baseMessage}` : baseMessage;
        const enhancedError = new Error(`LLMDataProcessingTool Error: ${contextualMessage}`) as ProcessingError;

        if (error instanceof Error && error.stack) {
            enhancedError.stack = error.stack;
        }

        enhancedError.metrics = {
            ...this.metrics,
            endTime: Date.now(),
            totalDuration: Date.now() - this.metrics.startTime,
            errorContext: context
        };

        this.debugLog('ERROR_ENHANCED', 'Error enhanced with metrics and context', {
            originalError: error,
            enhancedError
        });

        return enhancedError;
    }

    /**
     * Retrieves the current processing metrics.
     * @returns The ProcessingMetrics object.
     */
    public getProcessingMetrics(): ProcessingMetrics {
        const currentTime = Date.now();
        return {
            ...this.metrics,
            endTime: this.metrics.endTime || currentTime,
            totalDuration: (this.metrics.endTime || currentTime) - this.metrics.startTime,
            totalSteps: this.metrics.processingSteps.length,
            successRate: this.calculateSuccessRate()
        };
    }

    /**
     * Calculates the success rate based on processing steps.
     * @returns The success rate as a percentage.
     */
    private calculateSuccessRate(): number {
        if (this.metrics.processingSteps.length === 0) return 0;

        const successfulSteps = this.metrics.processingSteps.filter(step => step.success).length;
        return (successfulSteps / this.metrics.processingSteps.length) * 100;
    }
}