/**
 * Internet Search Tool for performing web searches
 * Uses Brave Search API to find relevant web pages
 */

// Define interfaces for search results
export interface SearchResultItem {
  title: string;
  link: string;
  snippet?: string;
}

export interface SearchOptions {
  numResults?: number;
}

export interface SearchResultMetadata {
  source: string;
  searchTime?: number;
  resultCount?: number;
  error?: string;
}

export interface SearchResult {
  success: boolean;
  results: SearchResultItem[];
  formattedResults: string;
  metadata: SearchResultMetadata;
}

export class InternetSearchTool {
  private apiKey: string | undefined;

  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "internetSearch",
    description: "Search the web for information. Returns results in markdown format.\nEach result includes the URL, title, and a snippet from the page if available.\n\nThis tool uses Brave Search API to find relevant web pages.",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query to send."
        },
        numResults: {
          type: "integer",
          description: "Number of results to return (default: 5, max: 10).",
          default: 5,
          minimum: 1,
          maximum: 10
        }
      },
      required: ["query"]
    },
    returns: {
      type: "object",
      properties: {
        results: {
          type: "array",
          description: "List of search results",
          items: {
            type: "object",
            properties: {
              title: { type: "string", description: "Title of the page" },
              link: { type: "string", description: "URL of the page" },
              snippet: { type: "string", description: "Text snippet from the page" }
            }
          }
        },
        formattedResults: {
          type: "string",
          description: "Search results formatted as markdown"
        }
      }
    },
    examples: [
      {
        input: { query: "climate change solutions" },
        output: "Returns search results about climate change solutions"
      },
      {
        input: { query: "electric vehicles", numResults: 3 },
        output: "Returns 3 search results about electric vehicles"
      }
    ]
  };

  constructor() {
    // Get API key from environment
    this.apiKey = process.env.SEARCH_API || process.env.BRAVE_SEARCH_API_KEY;
    if (!this.apiKey) {
      console.warn('Warning: SEARCH_API or BRAVE_SEARCH_API_KEY environment variable is not set. Internet search may not work properly.');
    }
  }

  /**
   * Perform a web search using Brave Search API
   * @param query - The search query
   * @param options - Search options
   * @returns - Search results
   */
  async search(query: string, options: SearchOptions = {}): Promise<SearchResult> {
    if (!query) {
      return {
        success: false,
        results: [],
        formattedResults: 'Error: Search query is required',
        metadata: {
          source: 'brave_search',
          error: 'Search query is required'
        }
      };
    }

    try {
      const startTime = Date.now();
      const numResults = Math.min(options.numResults || 5, 10);

      const response = await fetch(
        `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}`,
        {
          headers: {
            'Accept': 'application/json',
            'X-Subscription-Token': this.apiKey || ''
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Brave Search API error: ${response.statusText}`);
      }

      const data = await response.json();

      // Extract and process results
      const webResults = data.web?.results?.slice(0, numResults) || [];
      const results: SearchResultItem[] = webResults.map((result: any) => ({
        title: result.title,
        link: result.url,
        snippet: result.description
      }));

      return {
        success: true,
        results,
        formattedResults: this._formatResultsAsMarkdown(results),
        metadata: {
          source: 'brave_search',
          searchTime: Date.now() - startTime,
          resultCount: results.length
        }
      };
    } catch (error: any) {
      console.error('Internet search error:', error);
      return {
        success: false,
        results: [],
        formattedResults: `Error: ${error.message || 'Unknown error occurred'}`,
        metadata: {
          source: 'brave_search',
          error: error.message || 'Unknown error occurred'
        }
      };
    }
  }

  /**
   * Format search results as markdown
   * @private
   * @param items - Search result items
   * @returns - Markdown formatted results
   */
  private _formatResultsAsMarkdown(items: SearchResultItem[]): string {
    if (!items || !Array.isArray(items) || items.length === 0) {
      return 'No results found.';
    }

    return items.map((item, index) => {
      return `### ${index + 1}. [${item.title}](${item.link})

${item.snippet || 'No description available.'}
`;
    }).join('\n');
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof InternetSearchTool.description {
    return InternetSearchTool.description;
  }

  /**
   * Get all available tool methods with their descriptions
   * @returns Map of method names to their descriptions
   */
  getAvailableMethods(): Record<string, string> {
    return {
      search: "Search the web for information"
    };
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling LLM APIs with function calling
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "internetSearch",
        description: "Search the internet for current information that might not be available in the document collection",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The search query"
            },
            numResults: {
              type: "integer",
              description: "Number of search results to return (default: 5, max: 10)",
              default: 5,
              minimum: 1,
              maximum: 10
            }
          },
          required: ["query"]
        }
      }
    };
  }

  /**
   * Process a search query and return formatted results
   * This method provides compatibility with other tools that expect a process method
   * @param options - Search options including query
   * @returns Formatted search results
   */
  async process(options: { query: string; numResults?: number }): Promise<{
    success: boolean;
    content: string;
    sources: SearchResultItem[];
    error?: string;
  }> {
    try {
      const { query, numResults } = options;

      if (!query) {
        return {
          success: false,
          content: '',
          sources: [],
          error: 'Search query is required'
        };
      }

      const searchResult = await this.search(query, { numResults });

      if (!searchResult.success || searchResult.results.length === 0) {
        return {
          success: false,
          content: '',
          sources: [],
          error: searchResult.metadata.error || 'No search results found'
        };
      }

      return {
        success: true,
        content: searchResult.formattedResults,
        sources: searchResult.results
      };
    } catch (error) {
      console.error('Error in internet search process:', error);
      return {
        success: false,
        content: '',
        sources: [],
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

// Export a singleton instance
export const internetSearchTool = new InternetSearchTool();
