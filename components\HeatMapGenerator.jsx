'use client';

import React from 'react';
import { Info } from 'lucide-react';

/**
 * HeatMapGenerator component for rendering heat maps
 * @param {Object} props - Component props
 * @param {Object} props.heatmapConfig - Heat map configuration
 */
export default function HeatMapGenerator({ heatmapConfig }) {
  if (!heatmapConfig || !heatmapConfig.data || heatmapConfig.data.length === 0) {
    return (
      <div className="p-4 bg-zinc-800 rounded-md">
        <p className="text-zinc-400">No heat map data available</p>
      </div>
    );
  }

  const { 
    data, 
    title, 
    subtitle, 
    explanation, 
    xAxis = {}, 
    colorScale = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'],
    showValues = true
  } = heatmapConfig;

  // Extract column keys (excluding the name/row key)
  const rowKey = xAxis.dataKey || 'name';
  const columnKeys = Object.keys(data[0] || {}).filter(key => key !== rowKey);

  // Calculate min and max values for color scaling
  const allValues = data.flatMap(row => 
    columnKeys.map(col => typeof row[col] === 'number' ? row[col] : 0)
  );
  const minValue = Math.min(...allValues);
  const maxValue = Math.max(...allValues);

  // Function to get color based on value
  const getColor = (value) => {
    if (typeof value !== 'number') return '#1f2937';
    
    // Normalize value between 0 and 1
    const normalizedValue = (value - minValue) / (maxValue - minValue || 1);
    
    // If we have a color scale with just 2 colors, interpolate between them
    if (colorScale.length === 2) {
      return interpolateColor(colorScale[0], colorScale[1], normalizedValue);
    }
    
    // Otherwise, use the color scale as a gradient
    const colorIndex = Math.min(
      Math.floor(normalizedValue * (colorScale.length - 1)),
      colorScale.length - 2
    );
    
    const colorRatio = (normalizedValue * (colorScale.length - 1)) - colorIndex;
    return interpolateColor(
      colorScale[colorIndex],
      colorScale[colorIndex + 1],
      colorRatio
    );
  };

  // Function to interpolate between two colors
  const interpolateColor = (color1, color2, ratio) => {
    const hex2rgb = (hex) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 0, g: 0, b: 0 };
    };

    const rgb2hex = (r, g, b) => 
      '#' + [r, g, b].map(x => Math.round(x).toString(16).padStart(2, '0')).join('');

    const c1 = hex2rgb(color1);
    const c2 = hex2rgb(color2);

    return rgb2hex(
      c1.r + (c2.r - c1.r) * ratio,
      c1.g + (c2.g - c1.g) * ratio,
      c1.b + (c2.b - c1.b) * ratio
    );
  };

  // Function to determine text color based on background brightness
  const getTextColor = (bgColor) => {
    const hex2rgb = (hex) => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 0, g: 0, b: 0 };
    };
    
    const rgb = hex2rgb(bgColor);
    // Calculate brightness using the formula (0.299*R + 0.587*G + 0.114*B)
    const brightness = (0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b) / 255;
    return brightness > 0.6 ? '#000' : '#fff';
  };

  return (
    <div className="heatmap-container bg-zinc-900 rounded-lg border border-zinc-700 p-6">
      {/* Chart header */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white">{title}</h3>
        {subtitle && <p className="text-zinc-400 mt-1">{subtitle}</p>}
      </div>

      {/* Heat map visualization */}
      <div className="overflow-x-auto mb-6">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="p-2 text-left text-zinc-300 border border-zinc-700 bg-zinc-800">
                {xAxis.label || rowKey}
              </th>
              {columnKeys.map(key => (
                <th key={key} className="p-2 text-center text-zinc-300 border border-zinc-700 bg-zinc-800">
                  {key}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, rowIndex) => (
              <tr key={rowIndex}>
                <td className="p-2 text-left text-zinc-300 border border-zinc-700 bg-zinc-800 font-medium">
                  {row[rowKey]}
                </td>
                {columnKeys.map(colKey => {
                  const value = row[colKey];
                  const cellColor = getColor(value);
                  const textColor = getTextColor(cellColor);
                  
                  return (
                    <td 
                      key={colKey} 
                      className="p-2 text-center border border-zinc-700"
                      style={{ 
                        backgroundColor: cellColor,
                        color: textColor,
                        fontWeight: 500
                      }}
                    >
                      {showValues && value}
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Color scale legend */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="text-sm text-zinc-400">Min: {minValue}</div>
          <div className="flex-1 mx-4 h-4 rounded-md" style={{
            background: `linear-gradient(to right, ${colorScale.join(', ')})`
          }}></div>
          <div className="text-sm text-zinc-400">Max: {maxValue}</div>
        </div>
      </div>

      {/* Chart explanation */}
      {explanation && (
        <div className="mt-4 p-4 bg-zinc-800 rounded-md border border-zinc-700">
          <div className="flex items-start">
            <Info className="text-blue-400 mr-2 mt-1 flex-shrink-0" size={16} />
            <p className="text-zinc-300 text-sm">{explanation}</p>
          </div>
        </div>
      )}
    </div>
  );
}
