import { NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified?: string;
  children?: FileSystemItem[];
}

interface FileSystemRequest {
  rootPath?: string;
  maxDepth?: number;
  includeHidden?: boolean;
  fileExtensions?: string[];
}

/**
 * GET /api/codebase-documentation/file-system
 * 
 * Browse the local file system for codebase exploration
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const rootPath = searchParams.get('rootPath') || process.cwd();
    const maxDepth = parseInt(searchParams.get('maxDepth') || '3');
    const includeHidden = searchParams.get('includeHidden') === 'true';
    const fileExtensions = searchParams.get('fileExtensions')?.split(',') || [];

    // Security check: ensure we're not accessing sensitive system directories
    const safePath = path.resolve(rootPath);
    const normalizedPath = safePath.toLowerCase();

    // Block access to sensitive system directories
    const blockedPaths = [
      'c:\\windows',
      'c:\\program files',
      'c:\\program files (x86)',
      'c:\\programdata',
      'c:\\system volume information',
      'c:\\$recycle.bin'
    ];

    const isBlocked = blockedPaths.some(blocked =>
      normalizedPath.startsWith(blocked.toLowerCase())
    );

    if (isBlocked) {
      return NextResponse.json(
        { success: false, error: 'Access denied: Cannot access system directories' },
        { status: 403 }
      );
    }

    const fileSystemTree = await buildFileSystemTree(
      safePath,
      maxDepth,
      includeHidden,
      fileExtensions,
      0
    );

    return NextResponse.json({
      success: true,
      rootPath: safePath,
      fileSystem: fileSystemTree
    });

  } catch (error: any) {
    console.error('File system API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to read file system' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/codebase-documentation/file-system
 * 
 * Get detailed information about specific files or directories
 */
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { paths } = body;

    if (!Array.isArray(paths)) {
      return NextResponse.json(
        { success: false, error: 'Paths must be an array' },
        { status: 400 }
      );
    }

    const fileDetails = await Promise.all(
      paths.map(async (filePath: string) => {
        try {
          const safePath = path.resolve(filePath);

          // Security check: block sensitive system directories
          const normalizedPath = safePath.toLowerCase();
          const blockedPaths = [
            'c:\\windows',
            'c:\\program files',
            'c:\\program files (x86)',
            'c:\\programdata',
            'c:\\system volume information',
            'c:\\$recycle.bin'
          ];

          const isBlocked = blockedPaths.some(blocked =>
            normalizedPath.startsWith(blocked.toLowerCase())
          );

          if (isBlocked) {
            return {
              path: filePath,
              error: 'Access denied: Cannot access system directories'
            };
          }

          const stats = await fs.stat(safePath);
          const isDirectory = stats.isDirectory();

          let content = null;
          if (!isDirectory && stats.size < 1024 * 1024) { // Only read files smaller than 1MB
            try {
              content = await fs.readFile(safePath, 'utf-8');
            } catch (readError) {
              // File might be binary or unreadable
              content = null;
            }
          }

          return {
            path: filePath,
            name: path.basename(safePath),
            type: isDirectory ? 'directory' : 'file',
            size: stats.size,
            lastModified: stats.mtime.toISOString(),
            isReadable: content !== null,
            content: content?.substring(0, 10000), // Limit content to first 10KB
            lineCount: content ? content.split('\n').length : null
          };

        } catch (error: any) {
          return {
            path: filePath,
            error: error.message
          };
        }
      })
    );

    return NextResponse.json({
      success: true,
      files: fileDetails
    });

  } catch (error: any) {
    console.error('File details API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get file details' 
      },
      { status: 500 }
    );
  }
}

/**
 * Build a file system tree structure
 */
async function buildFileSystemTree(
  dirPath: string,
  maxDepth: number,
  includeHidden: boolean,
  fileExtensions: string[],
  currentDepth: number
): Promise<FileSystemItem[]> {
  if (currentDepth >= maxDepth) {
    return [];
  }

  try {
    const items = await fs.readdir(dirPath);
    const fileSystemItems: FileSystemItem[] = [];

    for (const item of items) {
      // Skip hidden files/directories unless explicitly included
      if (!includeHidden && item.startsWith('.')) {
        continue;
      }

      // Skip common directories that shouldn't be documented
      const skipDirs = ['node_modules', '.git', '.next', 'dist', 'build', '.vscode'];
      if (skipDirs.includes(item)) {
        continue;
      }

      const itemPath = path.join(dirPath, item);
      const stats = await fs.stat(itemPath);
      const isDirectory = stats.isDirectory();

      // Filter by file extensions if specified
      if (!isDirectory && fileExtensions.length > 0) {
        const ext = path.extname(item).toLowerCase();
        if (!fileExtensions.includes(ext)) {
          continue;
        }
      }

      const fileSystemItem: FileSystemItem = {
        name: item,
        path: itemPath,
        type: isDirectory ? 'directory' : 'file',
        size: stats.size,
        lastModified: stats.mtime.toISOString()
      };

      // Recursively build children for directories
      if (isDirectory && currentDepth < maxDepth - 1) {
        fileSystemItem.children = await buildFileSystemTree(
          itemPath,
          maxDepth,
          includeHidden,
          fileExtensions,
          currentDepth + 1
        );
      }

      fileSystemItems.push(fileSystemItem);
    }

    // Sort: directories first, then files, both alphabetically
    return fileSystemItems.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error);
    return [];
  }
}
