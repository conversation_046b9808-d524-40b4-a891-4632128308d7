'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import {
  getUsers,
  getProjects,
  getTasks,
  addProject,
  updateProject,
  deleteProject,
  addTask,
  updateTask,
  deleteTask
} from '../lib/firebase/planner';
import { User, Project, Task } from '../../admin/planner/types';
import { useAuth } from './AuthContext';

interface PlannerContextType {
  users: User[];
  projects: Project[];
  tasks: Task[];
  currentUser: User | null;
  loading: boolean;
  error: string | null;
  refreshData: () => Promise<void>;
  createProject: (project: Omit<Project, 'id'>) => Promise<string>;
  updateProject: (projectId: string, project: Partial<Project>) => Promise<void>;
  updateProjectData: (projectId: string, project: Partial<Project>) => Promise<void>;
  removeProject: (projectId: string) => Promise<void>;
  createTask: (task: Omit<Task, 'id'>) => Promise<string>;
  updateTask: (taskId: string, task: Partial<Task>) => Promise<void>;
  updateTaskData: (taskId: string, task: Partial<Task>) => Promise<void>;
  removeTask: (taskId: string) => Promise<void>;
}

const PlannerContext = createContext<PlannerContextType | undefined>(undefined);

export const usePlanner = () => {
  const context = useContext(PlannerContext);
  if (context === undefined) {
    throw new Error('usePlanner must be used within a PlannerProvider');
  }
  return context;
};

interface PlannerProviderProps {
  children: ReactNode;
}

export const PlannerProvider: React.FC<PlannerProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    const fetchData = async () => {
      if (!user) {
        console.log('No user in AuthContext, skipping data fetch');
        return;
      }

      console.log('Starting data fetch with user:', user.email);
      setLoading(true);
      setError(null);

      try {
        console.log('Fetching users, projects, and tasks...');
        // Fetch users first
        const usersData = await getUsers();

        // Then fetch projects with user email
        console.log('Fetching projects for user:', user.email);
        const projectsData = await getProjects(user.email);

        // Finally fetch tasks
        const tasksData = await getTasks();
        console.log(`Fetch complete - Users: ${usersData.length}, Projects: ${projectsData.length}, Tasks: ${tasksData.length}`);

        // Ensure all users have isAuthorized set (default to true for production)
        console.log('Processing users to ensure isAuthorized is set...');
        const processedUsers = usersData.map(user => {
          console.log('Processing user:', user.name, 'isAuthorized before:', user.isAuthorized);
          const processed = {
            ...user,
            isAuthorized: user.isAuthorized === false ? false : true
          };
          console.log('User after processing:', processed.name, 'isAuthorized after:', processed.isAuthorized);
          return processed;
        });

        console.log(`Setting state with ${processedUsers.length} users, ${projectsData.length} projects, ${tasksData.length} tasks`);
        setUsers(processedUsers);
        setProjects(projectsData);
        setTasks(tasksData);
      } catch (err: any) {
        console.error('Error fetching planner data:', err);
        setError(err.message || 'Failed to load planner data');
      } finally {
        setLoading(false);
        console.log('Data fetch complete, loading set to false');
      }
    };

    fetchData();
  }, [user]);

  const refreshData = async () => {
    if (!user) {
      console.log('No user in AuthContext, skipping refresh');
      return;
    }

    console.log('Starting data refresh with user:', user.email);
    setLoading(true);
    setError(null);

    try {
      console.log('Refreshing users, projects, and tasks...');
      // Fetch users first
      const usersData = await getUsers();

      // Then fetch projects with user email
      console.log('Refreshing projects for user:', user.email);
      const projectsData = await getProjects(user.email);

      // Finally fetch tasks
      const tasksData = await getTasks();
      console.log(`Refresh complete - Users: ${usersData.length}, Projects: ${projectsData.length}, Tasks: ${tasksData.length}`);

      // Ensure all users have isAuthorized set (default to true for production)
      console.log('Processing refreshed users to ensure isAuthorized is set...');
      const processedUsers = usersData.map(user => {
        console.log('Refresh - Processing user:', user.name, 'isAuthorized before:', user.isAuthorized);
        const processed = {
          ...user,
          isAuthorized: user.isAuthorized === false ? false : true
        };
        console.log('Refresh - User after processing:', processed.name, 'isAuthorized after:', processed.isAuthorized);
        return processed;
      });

      console.log(`Setting state with ${processedUsers.length} refreshed users, ${projectsData.length} projects, ${tasksData.length} tasks`);

      // Log project data before setting state
      projectsData.forEach(project => {
        console.log(`Project ${project.id} before setting state:`, project);
        console.log(`Project ${project.id} categories:`, project.categories);
      });

      // Update state with new data
      setUsers(processedUsers);
      setProjects(projectsData);
      setTasks(tasksData);

      // Log state after update
      console.log('State updated with new data');
    } catch (err: any) {
      console.error('Error refreshing planner data:', err);
      setError(err.message || 'Failed to refresh planner data');
    } finally {
      setLoading(false);
      console.log('Data refresh complete, loading set to false');
    }
  };

  const createProject = async (project: Omit<Project, 'id'>): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const projectId = await addProject(project);
      await refreshData();
      return projectId;
    } catch (err: any) {
      console.error('Error creating project:', err);
      setError(err.message || 'Failed to create project');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateProjectData = async (projectId: string, project: Partial<Project>): Promise<void> => {
    console.log('DIRECT CONTEXT - updateProjectData called with:', projectId, project);
    console.log('DIRECT CONTEXT - Description in project data:', project.description);
    setLoading(true);
    setError(null);

    try {
      // Log each field explicitly for debugging
      console.log('DIRECT CONTEXT - Project fields being updated:');
      console.log('DIRECT CONTEXT - Name:', project.name);
      console.log('DIRECT CONTEXT - Description:', project.description);
      console.log('DIRECT CONTEXT - Start Date:', project.startDate);
      console.log('DIRECT CONTEXT - End Date:', project.endDate);
      console.log('DIRECT CONTEXT - Owner:', project.owner);
      console.log('DIRECT CONTEXT - Status:', project.status);
      console.log('DIRECT CONTEXT - Categories:', project.categories);
      console.log('DIRECT CONTEXT - Members:', project.members);

      // Create a clean update object with all fields explicitly included
      const cleanProject = {
        ...project,
        // Ensure description is explicitly included
        description: project.description !== undefined ? project.description : ''
      };

      console.log('DIRECT CONTEXT - Calling updateProject with:', projectId, cleanProject);
      console.log('DIRECT CONTEXT - Description being sent to updateProject:', cleanProject.description);

      await updateProject(projectId, cleanProject);
      console.log('DIRECT CONTEXT - updateProject successful, refreshing data...');
      await refreshData();
      console.log('DIRECT CONTEXT - Data refresh complete');
    } catch (err: any) {
      console.error('DIRECT CONTEXT - Error updating project:', err);
      setError(err.message || 'Failed to update project');
      throw err;
    } finally {
      setLoading(false);
      console.log('DIRECT CONTEXT - updateProjectData complete');
    }
  };

  const removeProject = async (projectId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await deleteProject(projectId);
      await refreshData();
    } catch (err: any) {
      console.error('Error deleting project:', err);
      setError(err.message || 'Failed to delete project');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const createTask = async (task: Omit<Task, 'id'>): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      const taskId = await addTask(task);
      await refreshData();
      return taskId;
    } catch (err: any) {
      console.error('Error creating task:', err);
      setError(err.message || 'Failed to create task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateTaskData = async (taskId: string, task: Partial<Task>): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await updateTask(taskId, task);
      await refreshData();
    } catch (err: any) {
      console.error('Error updating task:', err);
      setError(err.message || 'Failed to update task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const removeTask = async (taskId: string): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      await deleteTask(taskId);
      await refreshData();
    } catch (err: any) {
      console.error('Error deleting task:', err);
      setError(err.message || 'Failed to delete task');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add a user directly to the state (for debugging)
  // This function is kept for debugging but not exposed in the interface
  const addUserToState = (user: User) => {
    console.log('Adding user directly to state:', user);
    setUsers(prevUsers => {
      // Check if user already exists
      const exists = prevUsers.some(u => u.id === user.id);
      if (exists) {
        console.log('User already exists in state, updating');
        return prevUsers.map(u => u.id === user.id ? user : u);
      } else {
        console.log('Adding new user to state');
        return [...prevUsers, user];
      }
    });
  };

  const value = {
    users,
    projects,
    tasks,
    currentUser: user,
    loading,
    error,
    refreshData,
    createProject,
    updateProject: updateProjectData,
    updateProjectData,
    removeProject,
    createTask,
    updateTask: updateTaskData,
    updateTaskData,
    removeTask
  };

  return (
    <PlannerContext.Provider value={value}>
      {children}
    </PlannerContext.Provider>
  );
};
