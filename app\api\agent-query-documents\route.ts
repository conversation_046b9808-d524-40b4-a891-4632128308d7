import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { queryDocumentsAgent } from '../../../components/Agents/QueryDocumentsAgent';
import { markdownRendererTool } from '../../../lib/tools/markdown-renderer-tool';

interface RequestBody {
  query: string;
  filename?: string;
  category?: string;
  categories?: string[];
  useInternetSearch?: boolean;
  model?: string;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

export async function POST(req: NextRequest) {
  try {
    // Auth validation
    const session = await getServerSession(authOptions);

    // Determine if we're in a development/testing environment
    const isDevelopment = process.env.NODE_ENV === 'development';

    // In production, require authentication
    if (!session?.user?.email && !isDevelopment) {
      console.error('Authentication required: No user session found in production environment');
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Request body validation
    const body: RequestBody = await req.json();
    const { query, filename, category, categories, useInternetSearch = false, model, modelOptions } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    // Validate that at least one of filename, category, or categories is provided
    if (!filename && !category && (!categories || categories.length === 0)) {
      return NextResponse.json(
        { success: false, error: 'Either filename, category, or categories is required' },
        { status: 400 }
      );
    }

    // Process the query with the queryDocumentsAgent
    let userId: string;

    if (session?.user?.email) {
      // Use the authenticated user's email if available
      userId = session.user.email;
      console.log(`Using authenticated user ID: ${userId} for document query`);
    } else if (isDevelopment) {
      // In development, allow a fallback user ID with a clear warning
      userId = 'test-user-' + Date.now();
      console.warn(`⚠️ WARNING: Using fallback test user ID (${userId}) because no authenticated user was found.`);
      console.warn(`⚠️ This is only allowed in development/testing environments.`);
      console.warn(`⚠️ To authenticate properly, visit /tools-test/login`);
    } else {
      // This should never happen due to the earlier check, but just in case
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Processing agent query:', {
      query,
      userId,
      filename,
      category,
      categories,
      useInternetSearch,
      model,
      modelOptions
    });

    try {
      let result;

      // Check if function calling should be used
      const useFunctionCalling = req.headers.get('x-use-function-calling') === 'true';

      if (useFunctionCalling) {
        // Use function calling with Groq model
        console.log('Using function calling with Groq model');

        result = await queryDocumentsAgent.processWithFunctionCalling(
          query,
          userId,
          {
            category,
            filename,
            useInternetSearch,
            model,
            modelOptions
          }
        );
      } else if (categories && categories.length > 0) {
        // Handle multi-category search if categories array is provided
        result = await queryDocumentsAgent.processMultiCategory(
          {
            query,
            userId,
            useInternetSearch,
            model,
            modelOptions
          },
          categories
        );
      } else {
        // Handle single category or filename search
        result = await queryDocumentsAgent.process({
          query,
          userId,
          filename,
          category,
          useInternetSearch,
          model,
          modelOptions
        });
      }

      console.log('Agent query result:', {
        success: result.success,
        contentLength: result.content?.length || 0,
        error: result.error,
        functionCallingUsed: result.metadata?.functionCallingUsed,
        followUpQuestionsCount: result.followUpQuestions?.length || 0,
        relatedCategoriesCount: result.relatedCategories?.length || 0
      });

      // Process the content with markdown-renderer-tool
      if (result.success && result.content) {
        try {
          const markdownResult = await markdownRendererTool.process({
            markdown: result.content,
            operation: 'preprocess'
          });

          if (markdownResult.success) {
            result.content = markdownResult.content;
          } else {
            console.warn("Failed to process content with markdown-renderer-tool:", markdownResult.error);
          }
        } catch (error) {
          console.error("Error processing content with markdown-renderer-tool:", error);
        }
      }

      // Return the result
      return NextResponse.json(result);
    } catch (processingError) {
      console.error('Error in queryDocumentsAgent processing:', processingError);
      return NextResponse.json({
        success: false,
        error: processingError instanceof Error ? processingError.message : 'Error processing query'
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in agent-query-documents API:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve all categories for a user
export async function GET(req: NextRequest) {
  try {
    // Auth validation
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;
    const categories = await queryDocumentsAgent.getAllCategories(userId);

    return NextResponse.json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      },
      { status: 500 }
    );
  }
}
