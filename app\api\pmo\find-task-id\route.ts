/**
 * API endpoint for finding PMO task IDs by title
 * This endpoint provides server-side access to the findTaskIdByTitle function
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';
import { findTaskIdByTitle } from '../../../../lib/firebase/pmoHierarchical';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      userId,
      pmoId,
      projectId,
      taskTitle,
      exactMatch = false
    } = body;

    // Validate required parameters
    if (!userId || !pmoId || !projectId || !taskTitle) {
      return NextResponse.json(
        { 
          error: 'Missing required parameters: userId, pmoId, projectId, taskTitle',
          success: false
        },
        { status: 400 }
      );
    }

    // Ensure the requesting user matches the userId or has admin privileges
    if (session.user.email !== userId) {
      // You might want to add admin role checking here
      console.warn(`User ${session.user.email} attempting to access data for ${userId}`);
    }

    console.log(`API: find-task-id - Searching for task "${taskTitle}" in project ${projectId}`);
    console.log(`  - PMO ID: ${pmoId}`);
    console.log(`  - User ID: ${userId}`);
    console.log(`  - Exact Match: ${exactMatch}`);

    // Call the server-side function
    const result = await findTaskIdByTitle(
      userId,
      pmoId,
      projectId,
      taskTitle,
      exactMatch
    );

    console.log(`API: find-task-id - Result:`, {
      success: result.success,
      taskId: result.taskId,
      matchedTitle: result.matchedTitle,
      error: result.error
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('API: find-task-id - Error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 });
  }
}

// Also support GET requests for simpler usage
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const pmoId = searchParams.get('pmoId');
    const projectId = searchParams.get('projectId');
    const taskTitle = searchParams.get('taskTitle');
    const exactMatch = searchParams.get('exactMatch') === 'true';

    // Validate required parameters
    if (!userId || !pmoId || !projectId || !taskTitle) {
      return NextResponse.json(
        { 
          error: 'Missing required query parameters: userId, pmoId, projectId, taskTitle',
          success: false
        },
        { status: 400 }
      );
    }

    // Ensure the requesting user matches the userId or has admin privileges
    if (session.user.email !== userId) {
      console.warn(`User ${session.user.email} attempting to access data for ${userId}`);
    }

    console.log(`API: find-task-id (GET) - Searching for task "${taskTitle}" in project ${projectId}`);

    // Call the server-side function
    const result = await findTaskIdByTitle(
      userId,
      pmoId,
      projectId,
      taskTitle,
      exactMatch
    );

    return NextResponse.json(result);

  } catch (error) {
    console.error('API: find-task-id (GET) - Error:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 });
  }
}
