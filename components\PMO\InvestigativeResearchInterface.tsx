'use client';

import React, { useState, useEffect } from 'react';
import { Loader2, Send, Search, FileText, Users, Settings, CheckCircle, AlertCircle } from "lucide-react";
import MarkdownRenderer from '../MarkdownRenderer';
import { InvestigationType } from '../../lib/agents/investigative/InvestigativeResearchAgent';

interface JournalistPersona {
  id: string;
  name: string;
  specialty: InvestigationType;
  description: string;
  preferredModel: string;
  preferredProvider: string;
  expertise: string[];
  investigationStyle: string;
}

interface InvestigationPreview {
  investigationType: InvestigationType;
  selectedJournalists: JournalistPersona[];
  estimatedDuration: string;
  expectedOutputs: string[];
  modelConfiguration: {
    criteriaModel: string;
    optimizationModel: string;
    assessmentModel: string;
    consolidationModel?: string;
  };
}

interface InvestigationProgress {
  step: 'criteria' | 'optimization' | 'investigation' | 'assessment' | 'consolidation' | 'complete';
  percentage: number;
  currentActivity: string;
  estimatedTimeRemaining?: string;
}

interface InvestigativeResearchInterfaceProps {
  pmoId?: string;
  onInvestigationComplete?: (result: any) => void;
  onClose?: () => void;
}

const InvestigativeResearchInterface: React.FC<InvestigativeResearchInterfaceProps> = ({
  pmoId,
  onInvestigationComplete,
  onClose
}) => {
  // State for form inputs
  const [title, setTitle] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [investigationType, setInvestigationType] = useState<InvestigationType>(InvestigationType.INVESTIGATIVE);
  const [selectedJournalistIds, setSelectedJournalistIds] = useState<string[]>([]);
  const [consolidate, setConsolidate] = useState<boolean>(true);
  const [priority, setPriority] = useState<'Low' | 'Medium' | 'High' | 'Critical'>('Medium');

  // State for available options
  const [investigationTypes, setInvestigationTypes] = useState<any[]>([]);
  const [availableJournalists, setAvailableJournalists] = useState<JournalistPersona[]>([]);
  const [recommendedJournalists, setRecommendedJournalists] = useState<JournalistPersona[]>([]);

  // State for preview and progress
  const [preview, setPreview] = useState<InvestigationPreview | null>(null);
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [progress, setProgress] = useState<InvestigationProgress | null>(null);

  // State for results
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'criteria' | 'investigations' | 'assessment' | 'consolidated'>('criteria');

  // Load initial data
  useEffect(() => {
    loadInvestigationTypes();
    loadJournalists();
  }, []);

  // Update recommended journalists when investigation type changes
  useEffect(() => {
    if (investigationType) {
      loadRecommendedJournalists(investigationType);
    }
  }, [investigationType]);

  const loadInvestigationTypes = async () => {
    try {
      const response = await fetch('/api/investigative-research?action=types');
      const data = await response.json();
      if (data.success) {
        setInvestigationTypes(data.data);
      }
    } catch (error) {
      console.error('Failed to load investigation types:', error);
    }
  };

  const loadJournalists = async () => {
    try {
      const response = await fetch('/api/investigative-research?action=journalists');
      const data = await response.json();
      if (data.success) {
        setAvailableJournalists(data.data);
      }
    } catch (error) {
      console.error('Failed to load journalists:', error);
    }
  };

  const loadRecommendedJournalists = async (type: InvestigationType) => {
    try {
      const response = await fetch(`/api/investigative-research?action=journalists&investigationType=${type}`);
      const data = await response.json();
      if (data.success) {
        setRecommendedJournalists(data.data);
        // Auto-select recommended journalists if none selected
        if (selectedJournalistIds.length === 0) {
          setSelectedJournalistIds(data.data.slice(0, 3).map((j: JournalistPersona) => j.id));
        }
      }
    } catch (error) {
      console.error('Failed to load recommended journalists:', error);
    }
  };

  const generatePreview = async () => {
    if (!title || !investigationType) return;

    try {
      const response = await fetch('/api/investigative-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'preview',
          investigationType,
          selectedJournalistIds,
          consolidate
        })
      });

      const data = await response.json();
      if (data.success) {
        setPreview(data.data);
        setShowPreview(true);
      } else {
        setError(data.error);
      }
    } catch (error: any) {
      setError(error.message);
    }
  };

  const conductInvestigation = async () => {
    if (!title || !investigationType) return;

    setLoading(true);
    setError(null);
    setResult(null);
    setShowPreview(false);

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (!prev) return { step: 'criteria', percentage: 10, currentActivity: 'Generating investigation criteria...' };
        
        const nextPercentage = Math.min(prev.percentage + Math.random() * 15 + 5, 95);
        let nextStep = prev.step;
        let nextActivity = prev.currentActivity;

        if (nextPercentage >= 25 && prev.step === 'criteria') {
          nextStep = 'optimization';
          nextActivity = 'Optimizing investigation prompt...';
        } else if (nextPercentage >= 40 && prev.step === 'optimization') {
          nextStep = 'investigation';
          nextActivity = `Conducting investigation with ${selectedJournalistIds.length} journalists...`;
        } else if (nextPercentage >= 75 && prev.step === 'investigation') {
          nextStep = 'assessment';
          nextActivity = 'Assessing and comparing journalist findings...';
        } else if (nextPercentage >= 90 && prev.step === 'assessment' && consolidate) {
          nextStep = 'consolidation';
          nextActivity = 'Consolidating investigation findings...';
        }

        return {
          step: nextStep,
          percentage: nextPercentage,
          currentActivity: nextActivity,
          estimatedTimeRemaining: `${Math.max(1, Math.round((100 - nextPercentage) / 10))} minutes`
        };
      });
    }, 1000);

    try {
      const response = await fetch('/api/investigative-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'conduct',
          pmoId: pmoId || `pmo_${Date.now()}`,
          title,
          description,
          investigationType,
          selectedJournalistIds,
          consolidate,
          priority
        })
      });

      const data = await response.json();
      clearInterval(progressInterval);

      if (data.success) {
        setProgress({ step: 'complete', percentage: 100, currentActivity: 'Investigation completed successfully' });
        setResult(data.data);
        setActiveTab('criteria');

        // Dispatch PMO event for workflow integration
        const pmoEvent = new CustomEvent('pmo-investigative-research-completed', {
          detail: {
            success: true,
            requestId: data.data.investigationId,
            pmoId: pmoId,
            result: data.data
          }
        });
        window.dispatchEvent(pmoEvent);

        if (onInvestigationComplete) {
          onInvestigationComplete(data.data);
        }
      } else {
        setError(data.error);
      }
    } catch (error: any) {
      clearInterval(progressInterval);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleJournalistToggle = (journalistId: string) => {
    setSelectedJournalistIds(prev => 
      prev.includes(journalistId)
        ? prev.filter(id => id !== journalistId)
        : [...prev, journalistId]
    );
  };

  const handleSaveToPdf = async () => {
    if (!result) return;

    try {
      const response = await fetch('/api/investigative-research-pdf', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          investigationResult: result,
          includeIndividualReports: true,
          includeAssessment: true,
          includeConsolidated: true
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate PDF');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `investigative-report-${result.investigationId}-${Date.now()}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error: any) {
      setError(error.message);
    }
  };

  return (
    <div className="bg-zinc-900 rounded-lg border border-zinc-700 overflow-hidden">
      <div className="p-4 border-b border-zinc-700">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <Search className="mr-2 h-5 w-5 text-purple-400" />
              Investigative Research Agent
            </h2>
            <p className="text-sm text-zinc-400">
              Multi-perspective investigative analysis using specialized journalist AI agents
            </p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-zinc-400 hover:text-white"
            >
              ×
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
        {/* Configuration Panel */}
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="title" className="block text-sm font-medium text-zinc-300">
                Investigation Title
              </label>
              <input
                id="title"
                type="text"
                placeholder="Enter investigation title..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="description" className="block text-sm font-medium text-zinc-300">
                Investigation Description
              </label>
              <textarea
                id="description"
                rows={3}
                placeholder="Provide context and background for the investigation..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label htmlFor="investigation-type" className="block text-sm font-medium text-zinc-300">
                  Investigation Type
                </label>
                <select
                  id="investigation-type"
                  value={investigationType}
                  onChange={(e) => setInvestigationType(e.target.value as InvestigationType)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  {investigationTypes.map(type => (
                    <option key={type.type} value={type.type}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <label htmlFor="priority" className="block text-sm font-medium text-zinc-300">
                  Priority
                </label>
                <select
                  id="priority"
                  value={priority}
                  onChange={(e) => setPriority(e.target.value as any)}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                >
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Critical">Critical</option>
                </select>
              </div>
            </div>
          </div>

          {/* Journalist Selection */}
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium text-zinc-300">
                Select Journalists ({selectedJournalistIds.length} selected)
              </label>
              <button
                onClick={() => setSelectedJournalistIds(recommendedJournalists.slice(0, 3).map(j => j.id))}
                className="text-xs text-purple-400 hover:text-purple-300"
              >
                Use Recommended
              </button>
            </div>

            <div className="space-y-2 max-h-64 overflow-y-auto">
              {availableJournalists.map(journalist => (
                <div
                  key={journalist.id}
                  className={`p-3 rounded-md border cursor-pointer transition-colors ${
                    selectedJournalistIds.includes(journalist.id)
                      ? 'border-purple-500 bg-purple-900/20'
                      : 'border-zinc-700 bg-zinc-800/50 hover:border-zinc-600'
                  }`}
                  onClick={() => handleJournalistToggle(journalist.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium text-white">{journalist.name}</h4>
                        {recommendedJournalists.some(r => r.id === journalist.id) && (
                          <span className="ml-2 px-2 py-0.5 text-xs bg-green-900/30 text-green-400 rounded">
                            Recommended
                          </span>
                        )}
                      </div>
                      <p className="text-xs text-zinc-400 mt-1">{journalist.description}</p>
                      <div className="flex items-center mt-2 text-xs text-zinc-500">
                        <span className="mr-3">Model: {journalist.preferredModel}</span>
                        <span>Style: {journalist.investigationStyle}</span>
                      </div>
                    </div>
                    <div className="ml-3">
                      {selectedJournalistIds.includes(journalist.id) ? (
                        <CheckCircle className="h-5 w-5 text-purple-400" />
                      ) : (
                        <div className="h-5 w-5 border border-zinc-600 rounded-full" />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label htmlFor="consolidate" className="text-sm font-medium text-zinc-300">
                Generate Consolidated Report
              </label>
              <input
                type="checkbox"
                id="consolidate"
                checked={consolidate}
                onChange={(e) => setConsolidate(e.target.checked)}
                className="h-4 w-4 rounded border-zinc-600 text-purple-600 focus:ring-purple-500"
              />
            </div>
            <p className="text-xs text-zinc-500">
              Combines the best elements from all journalist investigations into a unified report
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button
              onClick={generatePreview}
              disabled={!title || !investigationType || loading}
              className="flex-1 flex justify-center items-center px-4 py-2 bg-zinc-700 hover:bg-zinc-600 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Settings className="mr-2 h-4 w-4" />
              Preview Configuration
            </button>

            <button
              onClick={conductInvestigation}
              disabled={!title || !investigationType || selectedJournalistIds.length === 0 || loading}
              className="flex-1 flex justify-center items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <Loader2 className="animate-spin mr-2 h-4 w-4" />
                  Investigating...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Start Investigation
                </>
              )}
            </button>
          </div>
        </div>

        {/* Results Panel */}
        <div className="bg-zinc-800/50 rounded-lg border border-zinc-700 overflow-hidden">
          <div className="p-4 border-b border-zinc-700">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-white">Investigation Results</h3>
              {result && (
                <button
                  onClick={handleSaveToPdf}
                  className="flex items-center px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors text-sm"
                >
                  <FileText className="mr-1.5 h-4 w-4" />
                  Save PDF
                </button>
              )}
            </div>

            {result && (
              <div className="mt-3 flex space-x-1">
                <button
                  onClick={() => setActiveTab('criteria')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    activeTab === 'criteria' ? 'bg-purple-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600'
                  }`}
                >
                  Criteria
                </button>
                <button
                  onClick={() => setActiveTab('investigations')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    activeTab === 'investigations' ? 'bg-purple-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600'
                  }`}
                >
                  Investigations
                </button>
                <button
                  onClick={() => setActiveTab('assessment')}
                  className={`px-3 py-1 text-sm rounded-md ${
                    activeTab === 'assessment' ? 'bg-purple-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600'
                  }`}
                >
                  Assessment
                </button>
                {result.consolidatedReport && (
                  <button
                    onClick={() => setActiveTab('consolidated')}
                    className={`px-3 py-1 text-sm rounded-md ${
                      activeTab === 'consolidated' ? 'bg-purple-600 text-white' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600'
                    }`}
                  >
                    Consolidated
                  </button>
                )}
              </div>
            )}
          </div>

          <div className="p-0 h-96 overflow-y-auto">
            {showPreview && preview ? (
              <div className="p-4">
                <h4 className="text-lg font-medium text-white mb-3">Investigation Preview</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-medium text-zinc-300 mb-2">Selected Journalists</h5>
                    <div className="space-y-2">
                      {preview.selectedJournalists.map(journalist => (
                        <div key={journalist.id} className="flex items-center text-sm">
                          <Users className="h-4 w-4 text-purple-400 mr-2" />
                          <span className="text-white">{journalist.name}</span>
                          <span className="text-zinc-400 ml-2">({journalist.preferredModel})</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-zinc-300 mb-2">Estimated Duration</h5>
                    <p className="text-white">{preview.estimatedDuration}</p>
                  </div>

                  <div>
                    <h5 className="text-sm font-medium text-zinc-300 mb-2">Expected Outputs</h5>
                    <ul className="space-y-1">
                      {preview.expectedOutputs.map((output, index) => (
                        <li key={index} className="text-sm text-zinc-400 flex items-start">
                          <span className="text-purple-400 mr-2">•</span>
                          {output}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ) : loading && progress ? (
              <div className="flex flex-col items-center justify-center h-full p-4">
                <div className="text-center mb-6">
                  <div className="relative w-32 h-32 mb-4">
                    <svg className="w-full h-full" viewBox="0 0 100 100">
                      <circle
                        className="text-zinc-700"
                        strokeWidth="4"
                        stroke="currentColor"
                        fill="transparent"
                        r="48"
                        cx="50"
                        cy="50"
                      />
                      <circle
                        className="text-purple-500 transition-all duration-300 ease-in-out"
                        strokeWidth="4"
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="48"
                        cx="50"
                        cy="50"
                        strokeDasharray={`${progress.percentage * 3.02}, 302`}
                        transform="rotate(-90 50 50)"
                      />
                    </svg>
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <p className="text-2xl font-bold text-white">{Math.round(progress.percentage)}%</p>
                      <p className="text-xs text-purple-400 capitalize">{progress.step}</p>
                    </div>
                  </div>

                  <div className="bg-zinc-800 rounded-lg p-3">
                    <h4 className="text-sm font-medium text-white mb-1 capitalize">
                      {progress.currentActivity}
                    </h4>
                    {progress.estimatedTimeRemaining && (
                      <p className="text-xs text-zinc-400">
                        Estimated time remaining: {progress.estimatedTimeRemaining}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ) : error ? (
              <div className="p-4 bg-red-900/20 m-4 rounded-md text-red-400">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 mr-2" />
                  <p className="font-semibold">Error</p>
                </div>
                <p className="mt-1">{error}</p>
              </div>
            ) : !result ? (
              <div className="flex items-center justify-center h-full text-zinc-500">
                <div className="text-center">
                  <Search className="h-12 w-12 mx-auto mb-3 text-zinc-600" />
                  <p>Configure and start an investigation to see results</p>
                </div>
              </div>
            ) : (
              <div className="h-full">
                {activeTab === 'criteria' && (
                  <div className="p-4">
                    <div className="mb-4">
                      <h4 className="text-lg font-medium text-white mb-2">Investigation Criteria</h4>
                      <div className="text-xs text-zinc-400 mb-3">
                        Generated by {result.criteriaModel} ({result.criteriaProvider})
                      </div>
                      <MarkdownRenderer content={result.criteria} />
                    </div>

                    <div>
                      <h4 className="text-lg font-medium text-white mb-2">Optimized Research Question</h4>
                      <div className="text-xs text-zinc-400 mb-3">
                        Optimized by {result.optimizationModel} ({result.optimizationProvider})
                      </div>
                      <div className="bg-zinc-800 p-3 rounded-md border border-zinc-700">
                        <p className="text-amber-100 whitespace-pre-wrap">{result.optimizedPrompt}</p>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'investigations' && (
                  <div>
                    {result.journalistResponses.map((response: any, index: number) => (
                      <div key={index} className="border-b border-zinc-700 last:border-b-0">
                        <div className="p-4 bg-zinc-800/30">
                          <h4 className="text-lg font-medium text-white flex items-center">
                            <span className="mr-2">{response.journalistName}</span>
                            <span className="text-xs text-zinc-400">
                              ({response.model} via {response.provider})
                            </span>
                          </h4>
                          <p className="text-sm text-purple-400 mt-1">{response.investigationAngle}</p>
                        </div>
                        <div className="p-4">
                          {response.error ? (
                            <div className="bg-red-900/20 p-3 rounded-md text-red-400">
                              <p>Error: {response.error}</p>
                            </div>
                          ) : (
                            <MarkdownRenderer content={response.response || ''} />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeTab === 'assessment' && (
                  <div className="p-4">
                    <div className="mb-3">
                      <h4 className="text-lg font-medium text-white">Editorial Assessment</h4>
                      <div className="text-xs text-zinc-400">
                        Assessed by {result.assessmentModel} ({result.assessmentProvider})
                      </div>
                    </div>
                    <MarkdownRenderer content={result.assessment} />
                  </div>
                )}

                {activeTab === 'consolidated' && result.consolidatedReport && (
                  <div className="p-4">
                    <div className="mb-3">
                      <h4 className="text-lg font-medium text-white">Consolidated Investigation Report</h4>
                      <div className="text-xs text-zinc-400">
                        Consolidated by {result.consolidationModel} ({result.consolidationProvider})
                      </div>
                    </div>
                    <MarkdownRenderer content={result.consolidatedReport} />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvestigativeResearchInterface;
