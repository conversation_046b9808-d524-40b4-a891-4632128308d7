'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  Shield,
  CheckCircle,
  XCircle,
  Search,
  UserPlus,
  Mail,
  AlertTriangle
} from 'lucide-react';
import { useSession } from 'next-auth/react';
import { usePlanner } from '../../../context/PlannerContext';
import { User, UserRole } from '../../../../admin/planner/types';
import {
  getUsers,
  updateUser,
  addUserAccess,
  removeUserAccess,
  checkIsSystemAdmin
} from '../../../../admin/planner/firebase/firestore';

export default function SettingsPage() {
  const { data: session } = useSession();
  const { users, currentUser, refreshData } = usePlanner();
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddingUser, setIsAddingUser] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [isSystemAdmin, setIsSystemAdmin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Check if current user is system admin
  useEffect(() => {
    // First check if the <NAME_EMAIL> directly
    if (session?.user?.email === '<EMAIL>' || currentUser?.email === '<EMAIL>') {
      console.log('System admin detected directly, setting isSystemAdmin to true');
      setIsSystemAdmin(true);
      return;
    }

    // Otherwise, use the async function
    const checkAdmin = async () => {
      // First try to use the NextAuth session
      if (session?.user?.email) {
        console.log('Checking if user is system admin:', session.user.email);
        const isAdmin = await checkIsSystemAdmin(session.user.email);
        console.log('Is system admin result:', isAdmin);
        setIsSystemAdmin(isAdmin);
      }
      // Fallback to using the currentUser from PlannerContext
      else if (currentUser?.email) {
        console.log('Checking if user is system admin (fallback):', currentUser.email);
        const isAdmin = await checkIsSystemAdmin(currentUser.email);
        console.log('Is system admin result (fallback):', isAdmin);
        setIsSystemAdmin(isAdmin);
      }
    };

    checkAdmin();
  }, [session, currentUser]);

  // Filter users based on search query
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Grant access to a new user
  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      if (!newUserEmail.trim()) {
        throw new Error('Please enter a valid email address');
      }

      // Check if user already exists
      const existingUser = users.find(user => user.email.toLowerCase() === newUserEmail.toLowerCase());
      if (existingUser) {
        throw new Error('User already has access');
      }

      await addUserAccess(newUserEmail);
      setSuccess(`Access granted to ${newUserEmail}`);
      setNewUserEmail('');
      setIsAddingUser(false);
      refreshData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add user');
    } finally {
      setLoading(false);
    }
  };

  // Remove access from a user
  const handleRemoveAccess = async (userId: string, userEmail: string) => {
    if (!confirm(`Are you sure you want to remove access for ${userEmail}?`)) {
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await removeUserAccess(userId);
      setSuccess(`Access removed for ${userEmail}`);
      refreshData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove user access');
    } finally {
      setLoading(false);
    }
  };

  // Toggle user role between admin and user
  const handleToggleRole = async (user: User) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const newRole: UserRole = user.role === 'admin' ? 'user' : 'admin';
      await updateUser(user.id, { role: newRole });
      setSuccess(`${user.name}'s role updated to ${newRole}`);
      refreshData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user role');
    } finally {
      setLoading(false);
    }
  };

  // Add debug logging
  console.log('Settings page render state:', {
    isSystemAdmin,
    sessionEmail: session?.user?.email,
    currentUserEmail: currentUser?.email,
    loading
  });

  if (!isSystemAdmin) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-100 flex items-center justify-center">
        <div className="bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Access Restricted</h1>
          <p className="text-gray-300 mb-6">
            Only the system administrator (<EMAIL>) can access this page.
          </p>
          <div className="text-xs text-gray-400 mt-4">
            <p>Debug info:</p>
            <p>Session email: {session?.user?.email || 'none'}</p>
            <p>Current user email: {currentUser?.email || 'none'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-100">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-white">User Access Management</h1>
            <p className="text-gray-400 mt-1">Control who has access to the admin panel</p>
          </div>
        </div>

        {/* Error and success messages */}
        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-100 px-4 py-3 rounded mb-4 flex items-center">
            <XCircle className="w-5 h-5 mr-2" />
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-900/50 border border-green-500 text-green-100 px-4 py-3 rounded mb-4 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2" />
            {success}
          </div>
        )}

        {/* Search and add user */}
        <div className="bg-gray-800 rounded-lg p-4 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
            <div className="relative w-full md:w-64 mb-4 md:mb-0">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>

            <button
              onClick={() => setIsAddingUser(true)}
              className="w-full md:w-auto flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            >
              <UserPlus className="w-5 h-5 mr-2" />
              Add User
            </button>
          </div>

          {/* Add user form */}
          {isAddingUser && (
            <form onSubmit={handleAddUser} className="bg-gray-700 rounded-md p-4 mt-4">
              <h3 className="text-lg font-medium mb-3">Grant Access to New User</h3>
              <div className="flex flex-col md:flex-row gap-3">
                <div className="relative flex-grow">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                  <input
                    type="email"
                    placeholder="Enter email address"
                    value={newUserEmail}
                    onChange={(e) => setNewUserEmail(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-gray-600 border border-gray-500 rounded-md text-gray-200 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    required
                  />
                </div>
                <div className="flex gap-2">
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                  >
                    {loading ? 'Adding...' : 'Add User'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsAddingUser(false)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
              <p className="text-xs text-gray-400 mt-2">
                Note: The user will need to sign in with this email address to access the system.
              </p>
            </form>
          )}
        </div>

        {/* Users list */}
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-700 flex items-center">
            <Users className="w-5 h-5 mr-2 text-gray-400" />
            <h2 className="text-lg font-medium">Authorized Users</h2>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-700">
              <thead className="bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    User
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Role
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredUsers.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 text-center text-gray-400">
                      No users found
                    </td>
                  </tr>
                ) : (
                  filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-750">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-600 flex items-center justify-center overflow-hidden">
                            {user.avatar && user.avatar !== '/avatars/default.png' && user.avatar !== '/avatars/admin.png' ? (
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="h-10 w-10 rounded-full object-cover"
                                referrerPolicy="no-referrer"
                              />
                            ) : (
                              <span className="text-lg font-medium text-gray-300">
                                {user.name.charAt(0)}
                              </span>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-200">{user.name}</div>
                            <div className="text-sm text-gray-400">{user.jobTitle || ''}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-200">{user.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.role === 'admin'
                            ? 'bg-purple-900 text-purple-200'
                            : 'bg-blue-900 text-blue-200'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleToggleRole(user)}
                            disabled={loading || user.email === '<EMAIL>'}
                            className="text-indigo-400 hover:text-indigo-300 disabled:opacity-50 disabled:cursor-not-allowed"
                            title={user.role === 'admin' ? 'Demote to User' : 'Promote to Admin'}
                          >
                            <Shield className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleRemoveAccess(user.id, user.email)}
                            disabled={loading || user.email === '<EMAIL>'}
                            className="text-red-400 hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
                            title="Remove Access"
                          >
                            <XCircle className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
