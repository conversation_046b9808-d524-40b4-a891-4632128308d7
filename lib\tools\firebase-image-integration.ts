/**
 * Firebase integration for image generation tools
 * Provides helper functions to work with the Firebase APIs
 */

import { generateImageTool } from './generate-image';
import { storeImagenImageInFirebase } from './imagen';

/**
 * Initialize an image generation job in Firebase
 * @param prompt - The prompt to generate an image from
 * @param userId - The user ID for Firebase storage
 * @returns The job ID
 */
export async function initializeImageGeneration(
  prompt: string,
  userId: string
): Promise<string> {
  try {
    // In a real implementation, this would call the API endpoint
    // For now, we'll just make a fetch request to the API
    const response = await fetch('/api/initializeImageGeneration', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ prompt, userId }),
    });

    if (!response.ok) {
      throw new Error('Failed to initialize image generation');
    }

    const data = await response.json();
    return data.jobId;
  } catch (error: any) {
    console.error('Error initializing image generation:', error);
    throw new Error(`Failed to initialize image generation: ${error.message}`);
  }
}

/**
 * Process an image generation job in Firebase
 * @param jobId - The job ID to process
 * @param userId - The user ID for Firebase storage
 * @returns The result of the image generation
 */
export async function processImageGeneration(
  jobId: string,
  userId: string
): Promise<{
  success: boolean;
  imageUrl?: string;
  namespace?: string;
  error?: string;
}> {
  try {
    // In a real implementation, this would call the API endpoint
    // For now, we'll just make a fetch request to the API
    const response = await fetch('/api/processImageGeneration', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ jobId, userId }),
    });

    if (!response.ok) {
      throw new Error('Failed to process image generation');
    }

    const data = await response.json();
    return {
      success: data.success,
      imageUrl: data.imageUrl,
      namespace: data.namespace,
      error: data.error
    };
  } catch (error: any) {
    console.error('Error processing image generation:', error);
    return {
      success: false,
      error: `Failed to process image generation: ${error.message}`
    };
  }
}

/**
 * Generate an image and store it in Firebase
 * This is a convenience function that combines initialization and processing
 * @param prompt - The prompt to generate an image from
 * @param userId - The user ID for Firebase storage
 * @param model - The model to use
 * @returns The result of the image generation
 */
export async function generateAndStoreImage(
  prompt: string,
  userId: string,
  model: string = 'gpt-image-1'
): Promise<{
  success: boolean;
  imageUrl?: string;
  namespace?: string;
  jobId?: string;
  error?: string;
}> {
  try {
    // Step 1: Initialize the job
    const jobId = await initializeImageGeneration(prompt, userId);
    console.log(`Job initialized with ID: ${jobId}`);

    // Step 2: Process the job
    const result = await processImageGeneration(jobId, userId);

    if (!result.success) {
      throw new Error(result.error || 'Failed to process image generation');
    }

    return {
      success: true,
      imageUrl: result.imageUrl,
      namespace: result.namespace,
      jobId
    };
  } catch (error: any) {
    console.error('Error generating and storing image:', error);
    return {
      success: false,
      error: `Failed to generate and store image: ${error.message}`
    };
  }
}

/**
 * Save a generated image to the user's gallery
 * @param imageUrl - The URL of the generated image
 * @param prompt - The prompt used to generate the image
 * @param userId - The user ID for Firebase storage
 * @param jobId - The job ID for the image
 * @param model - The model used to generate the image
 * @param namespace - Optional namespace to use for the image (UUIDv4)
 * @returns The result of saving the image
 */
export async function saveImageToGallery(
  imageUrl: string,
  prompt: string,
  userId: string,
  jobId: string,
  model: string = 'gpt-image-1',
  namespace?: string
): Promise<{
  success: boolean;
  namespace?: string;
  error?: string;
}> {
  try {
    // In a real implementation, this would call the API endpoint
    // For now, we'll just make a fetch request to the API
    const response = await fetch('/api/saveImageToGallery', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        imageUrl,
        prompt,
        userId,
        jobId,
        model,
        namespace
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to save image to gallery');
    }

    const data = await response.json();
    return {
      success: data.success,
      namespace: data.namespace,
      error: data.error
    };
  } catch (error: any) {
    console.error('Error saving image to gallery:', error);
    return {
      success: false,
      error: `Failed to save image to gallery: ${error.message}`
    };
  }
}

/**
 * Process a file image for ByteStore indexing
 * @param namespace - The namespace for the image
 * @param userId - The user ID for Firebase storage
 * @param fileName - The file name for the image
 * @param fileUrl - The URL of the image
 * @returns The result of processing the file
 */
export async function processFileImage(
  namespace: string,
  userId: string,
  fileName: string,
  fileUrl: string
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // In a real implementation, this would call the API endpoint
    // For now, we'll just make a fetch request to the API
    const response = await fetch('/api/processFileImage', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        docId: namespace,
        userId,
        fileName,
        fileType: 'image/png',
        fileUrl,
        isImage: true,
        category: 'My Images'
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to process file image');
    }

    const data = await response.json();
    return {
      success: data.success,
      error: data.error
    };
  } catch (error: any) {
    console.error('Error processing file image:', error);
    return {
      success: false,
      error: `Failed to process file image: ${error.message}`
    };
  }
}
