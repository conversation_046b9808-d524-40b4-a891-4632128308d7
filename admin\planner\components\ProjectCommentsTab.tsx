"use client"

import type React from "react"
import { useState, useEffect, useMemo } from "react"
import { usePlanner } from "../../../app/services/context/PlannerContext"
import { useFirebase } from "../context/FirebaseContext"
import type { TaskComment, Task } from "../types"
import { Send, MessageSquare, RefreshCw, List } from "lucide-react"
import { format } from "date-fns"

interface ProjectCommentsTabProps {
  projectId: string
  users: any[]
  currentAuthUser?: any // Add this prop to receive auth user from wrapper
}

const ProjectCommentsTab: React.FC<ProjectCommentsTabProps> = ({ projectId, users, currentAuthUser }) => {
  const { taskComments, fetchTaskComments, addTaskComment, loading: contextLoading } = usePlanner()
  const { currentUser: firebaseUser, tasks: allTasks, refreshData } = useFirebase()
  const [comment, setComment] = useState("")
  const [error, setError] = useState("")
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null)
  const [projectTasks, setProjectTasks] = useState<Task[]>([])
  const [tasksLoading, setTasksLoading] = useState<boolean>(true)
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)
  const [activeTab, setActiveTab] = useState<"add" | "view">("add")
  const [allProjectComments, setAllProjectComments] = useState<Array<TaskComment & { taskTitle: string }>>([])
  const [allCommentsLoading, setAllCommentsLoading] = useState<boolean>(false)

  const [taskFilter, setTaskFilter] = useState<string>("")
  const [userFilter, setUserFilter] = useState<string>("")
  const [sortBy, setSortBy] = useState<string>("newest")

  // Combine auth context user with firebase user
  // This ensures we have a user even if one context doesn't provide it
  const effectiveUser = currentAuthUser || firebaseUser

  // Function to refresh tasks
  const refreshTasks = async () => {
    try {
      console.log("ProjectCommentsTab: Manually refreshing tasks")
      setTasksLoading(true)

      // Refresh data from Firebase
      await refreshData()

      // Filter tasks for this project
      const filteredTasks = allTasks.filter((task: Task) => task.projectId === projectId)
      console.log(`ProjectCommentsTab: Refresh found ${filteredTasks.length} tasks for project ${projectId}`)

      setProjectTasks(filteredTasks)

      // Reset selectedTaskId if it no longer exists, or select first task if none selected
      if (filteredTasks.length > 0) {
        if (!filteredTasks.some((task: Task) => task.id === selectedTaskId)) {
          setSelectedTaskId(filteredTasks[0].id)
        }
      } else {
        setSelectedTaskId(null)
      }

      setError("")
    } catch (err) {
      console.error("Error refreshing tasks:", err)
      setError("Failed to refresh tasks")
    } finally {
      setTasksLoading(false)
    }
  }

  // Filter tasks when allTasks or projectId changes
  useEffect(() => {
    try {
      console.log("ProjectCommentsTab: Filtering tasks for project", projectId)
      setTasksLoading(true)

      // Filter tasks for this project from the context
      const filteredTasks = allTasks.filter((task: Task) => task.projectId === projectId)
      console.log(`ProjectCommentsTab: Found ${filteredTasks.length} tasks for project ${projectId}`)

      setProjectTasks(filteredTasks)

      // Select the first task by default if none is selected
      if (filteredTasks.length > 0 && !selectedTaskId) {
        console.log("ProjectCommentsTab: Selecting first task by default:", filteredTasks[0].id)
        setSelectedTaskId(filteredTasks[0].id)
      } else if (filteredTasks.length === 0) {
        setSelectedTaskId(null)
      }

      setError("")
    } catch (err) {
      console.error("Error filtering tasks:", err)
      setError("Failed to load tasks")
    } finally {
      setTasksLoading(false)
    }
  }, [allTasks, projectId, selectedTaskId])

  // Fetch comments when selected task changes
  useEffect(() => {
    if (selectedTaskId) {
      console.log("ProjectCommentsTab: Fetching comments for task:", selectedTaskId)
      fetchTaskComments(selectedTaskId)
        .then((comments) => {
          console.log("ProjectCommentsTab: Fetched comments:", comments.length)
        })
        .catch((err) => {
          console.error("ProjectCommentsTab: Error fetching comments:", err)
          setError("Failed to load comments")
        })
    }
  }, [selectedTaskId, fetchTaskComments])

  // Get comments for the selected task
  const currentTaskComments = selectedTaskId ? taskComments[selectedTaskId] || [] : []

  // Function to load all comments across all tasks in the project
  const loadAllProjectComments = async () => {
    try {
      setAllCommentsLoading(true)

      // Array to store all comments with task information
      const allComments: Array<TaskComment & { taskTitle: string }> = []

      // Fetch comments for each task in the project
      for (const task of projectTasks) {
        const comments = await fetchTaskComments(task.id)
        // Add task title to each comment
        const commentsWithTaskInfo = comments.map((comment: TaskComment) => ({
          ...comment,
          taskTitle: task.title,
        }))

        allComments.push(...commentsWithTaskInfo)
      }

      // Sort by date (newest first)
      allComments.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

      setAllProjectComments(allComments)
    } catch (err) {
      console.error("Error loading all project comments:", err)
      setError("Failed to load all comments")
    } finally {
      setAllCommentsLoading(false)
    }
  }

  // Load all comments when viewing the comments tab
  useEffect(() => {
    if (activeTab === "view" && projectTasks.length > 0) {
      loadAllProjectComments()
    }
  }, [activeTab, projectTasks.length])

  // Handle comment submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!comment.trim() || !selectedTaskId) {
      setError("Please select a task and enter a comment.")
      return
    }

    if (!effectiveUser) {
      setError("You must be logged in to add comments.")
      console.error("No current user found when trying to add comment")
      return
    }

    try {
      setIsSubmitting(true)
      console.log("Submitting comment:", {
        taskId: selectedTaskId,
        comment,
        user: effectiveUser.email || effectiveUser.id || "unknown",
      })

      // Use email if available, otherwise fall back to id
      const userIdentifier = effectiveUser.email || effectiveUser.id

      if (!userIdentifier) {
        setError("User identification is missing. Please log in again.")
        return
      }

      await addTaskComment(selectedTaskId, comment, userIdentifier)
      setComment("")
      setError("")

      // Refresh comments after adding
      await fetchTaskComments(selectedTaskId)

      // If we're in the view tab, refresh all comments
      if (activeTab === "view") {
        await loadAllProjectComments()
      }
    } catch (err: any) {
      console.error("Error adding comment:", err)
      setError(err.message || "Failed to add comment")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return format(new Date(date), "MMM d, yyyy h:mm a")
  }

  // Find user name by email
  const getUserName = (email: string) => {
    const user = users.find((u) => u.email === email)
    return user ? user.name : email
  }

  // Check if the user is authenticated
  const isUserAuthenticated = Boolean(effectiveUser)

  // Tab switching handler
  const handleTabChange = (tab: "add" | "view") => {
    setActiveTab(tab)
    if (tab === "view" && projectTasks.length > 0) {
      loadAllProjectComments()
    }
  }

  // Check if we're on a mobile device
  const [isMobile, setIsMobile] = useState(false)

  // Update mobile state when window resizes
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768) // 768px is a common breakpoint for tablets
    }

    // Set initial value
    checkMobile()

    // Add event listener
    window.addEventListener('resize', checkMobile)

    // Clean up
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Filter and sort comments
  const filteredAndSortedComments = useMemo(() => {
    // First apply filters
    let filtered = [...allProjectComments]

    if (taskFilter) {
      const task = projectTasks.find((t) => t.id === taskFilter)
      if (task) {
        filtered = filtered.filter((comment) => comment.taskTitle === task.title)
      }
    }

    if (userFilter) {
      filtered = filtered.filter((comment) => comment.createdBy === userFilter)
    }

    // Then sort
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        case "oldest":
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        case "task":
          return a.taskTitle.localeCompare(b.taskTitle)
        case "user":
          return getUserName(a.createdBy).localeCompare(getUserName(b.createdBy))
        default:
          return 0
      }
    })
  }, [allProjectComments, taskFilter, userFilter, sortBy, projectTasks])

  return (
    <div className={`bg-gray-800 rounded-lg shadow-md ${isMobile ? 'p-3' : 'p-6'}`}>
      {/* Tab Navigation */}
      <div className="flex flex-wrap border-b border-gray-600 mb-6 overflow-x-hidden">
        <button
          onClick={() => handleTabChange("add")}
          className={`flex-1 min-w-0 px-2 sm:px-3 py-2 font-medium text-xs sm:text-sm ${
            activeTab === "add"
              ? "text-purple-400 border-b-2 border-purple-400"
              : "text-gray-400 hover:text-gray-200"
          }`}
        >
          {isMobile ? (
            <div className="flex flex-col items-center">
              <Send className="h-4 w-4 mb-1" />
              <span className="truncate">Add</span>
            </div>
          ) : (
            <span className="truncate">Add Comments</span>
          )}
        </button>
        <button
          onClick={() => handleTabChange("view")}
          className={`flex-1 min-w-0 px-2 sm:px-3 py-2 font-medium text-xs sm:text-sm ${
            activeTab === "view"
              ? "text-purple-400 border-b-2 border-purple-400"
              : "text-gray-400 hover:text-gray-200"
          }`}
        >
          {isMobile ? (
            <div className="flex flex-col items-center">
              <List className="h-4 w-4 mb-1" />
              <span className="truncate">View</span>
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <List className="h-4 w-4 mr-1" />
              <span className="truncate">View Comments</span>
            </div>
          )}
        </button>
      </div>

      {/* Add Comments Tab */}
      {activeTab === "add" && (
        <div className={`${isMobile ? 'flex flex-col' : 'grid grid-cols-1 md:grid-cols-3'} gap-6`}>
          {/* Task Selection Dropdown Panel */}
          <div className={`bg-gray-700/50 rounded-lg overflow-hidden ${isMobile ? 'order-2' : ''}`}>
            <div className="bg-purple-900/40 p-3 border-b border-gray-600">
              <h3 className="text-lg font-medium text-white flex items-center">
                <span className="inline-block w-3 h-3 bg-purple-400 rounded-full mr-2"></span>
                Project Tasks
              </h3>
              <p className="text-sm text-gray-400 mt-1">Select a task to comment on</p>
            </div>

            <div className="p-3">
              {tasksLoading ? (
                <div className="text-center py-4 text-gray-400">
                  <div className="animate-pulse flex flex-col items-center">
                    <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-600 rounded w-1/2"></div>
                    <p className="mt-4">Loading tasks...</p>
                  </div>
                </div>
              ) : projectTasks.length === 0 ? (
                <div className="text-center py-4 text-gray-400">
                  <p>No tasks found for this project</p>
                  <button
                    onClick={refreshTasks}
                    className="mt-2 px-3 py-1 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm"
                  >
                    Refresh Tasks
                  </button>
                </div>
              ) : (
                <div className="relative">
                  {/* Selected Task Display */}
                  {selectedTaskId && (
                    <div className="mb-2 p-3 bg-purple-900/40 border-l-4 border-purple-500 rounded-lg">
                      <div className="font-medium text-white truncate">
                        {projectTasks.find((t) => t.id === selectedTaskId)?.title}
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <span
                          className={`text-xs px-2 py-0.5 rounded ${
                            projectTasks.find((t) => t.id === selectedTaskId)?.status === "Complete"
                              ? "bg-green-900/40 text-green-400"
                              : projectTasks.find((t) => t.id === selectedTaskId)?.status === "In Progress"
                                ? "bg-blue-900/40 text-blue-400"
                                : "bg-amber-900/40 text-amber-400"
                          }`}
                        >
                          {projectTasks.find((t) => t.id === selectedTaskId)?.status}
                        </span>
                        <span className="text-xs text-gray-400">
                          {taskComments[selectedTaskId]?.length || 0} comments
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Dropdown */}
                  <div className="relative">
                    <select
                      value={selectedTaskId || ""}
                      onChange={(e) => setSelectedTaskId(e.target.value)}
                      className="appearance-none w-full bg-gray-800 border border-gray-600 text-white py-2 px-3 pr-8 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                    >
                      <option value="" disabled>
                        -- Select a task --
                      </option>
                      {projectTasks.map((task) => (
                        <option key={task.id} value={task.id}>
                          {task.title.length > 40 && isMobile
                            ? task.title.substring(0, 40) + "..."
                            : task.title} ({task.status})
                        </option>
                      ))}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                      <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                      </svg>
                    </div>
                  </div>

                  {/* Task Quick Stats */}
                  <div className="mt-4 grid grid-cols-2 gap-2">
                    <div className="bg-gray-800/40 p-3 rounded-lg">
                      <div className="text-xs text-gray-400">Total Tasks</div>
                      <div className="text-lg font-medium text-white">{projectTasks.length}</div>
                    </div>
                    <div className="bg-gray-800/40 p-3 rounded-lg">
                      <div className="text-xs text-gray-400">Total Comments</div>
                      <div className="text-lg font-medium text-white">
                        {Object.values(taskComments).reduce((sum, comments) => sum + comments.length, 0)}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Refresh button */}
            <div className="bg-gray-800/40 p-3 flex justify-center items-center border-t border-gray-600">
              <button
                onClick={refreshTasks}
                disabled={tasksLoading}
                className="px-3 py-1.5 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors text-sm flex items-center disabled:opacity-50 w-full justify-center"
              >
                {tasksLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Loading Tasks...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Tasks
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Comments Section */}
          <div className={`md:col-span-2 bg-gray-700/50 rounded-lg overflow-hidden flex flex-col ${isMobile ? 'order-1 mb-4' : ''}`}>
            {selectedTaskId ? (
              <>
                <div className="bg-gray-800/40 p-3 border-b border-gray-600">
                  <div className="flex items-center">
                    <span className="inline-block w-3 h-3 bg-emerald-400 rounded-full mr-2"></span>
                    <h3 className="text-lg font-medium text-white truncate">
                      {projectTasks.find((t) => t.id === selectedTaskId)?.title || "Task Comments"}
                    </h3>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-sm text-gray-400">
                      {currentTaskComments.length} comment{currentTaskComments.length !== 1 ? "s" : ""}
                    </p>
                    {currentTaskComments.length > 0 && (
                      <p className="text-xs text-gray-400">
                        Last activity:{" "}
                        {currentTaskComments.length > 0 ? formatDate(currentTaskComments[0].createdAt) : "None"}
                      </p>
                    )}
                  </div>
                </div>

                {/* Comments List */}
                <div className="p-3 flex-grow overflow-y-auto max-h-[400px] space-y-3">
                  {currentTaskComments.length === 0 ? (
                    <div className="text-center py-8 text-gray-400 flex flex-col items-center">
                      <MessageSquare className="h-12 w-12 mb-2 opacity-30" />
                      <p>No comments yet</p>
                      <p className="text-sm mt-1">Be the first to add a comment</p>
                    </div>
                  ) : (
                    currentTaskComments.map((comment: TaskComment, index: number) => (
                      <div
                        key={comment.id}
                        className={`rounded-lg p-3 ${index % 2 === 0 ? "bg-gray-700" : "bg-gray-700/50"}`}
                      >
                        <div className={`${isMobile ? 'flex-col' : 'flex items-start gap-3'}`}>
                          <div className={`${isMobile ? 'flex items-center gap-2 mb-2' : ''}`}>
                            <div className="w-8 h-8 rounded-full bg-purple-900/60 flex items-center justify-center text-purple-300 font-medium text-sm">
                              {getUserName(comment.createdBy).charAt(0).toUpperCase()}
                            </div>
                            {isMobile && (
                              <div className="font-medium text-white">{getUserName(comment.createdBy)}</div>
                            )}
                          </div>
                          <div className="flex-1">
                            {!isMobile && (
                              <div className="flex justify-between items-start">
                                <div className="font-medium text-white">{getUserName(comment.createdBy)}</div>
                                <div className="text-xs text-gray-400 bg-gray-800/40 px-2 py-0.5 rounded">
                                  {formatDate(comment.createdAt)}
                                </div>
                              </div>
                            )}
                            {isMobile && (
                              <div className="text-xs text-gray-400 bg-gray-800/40 px-2 py-0.5 rounded inline-block mb-2">
                                {formatDate(comment.createdAt)}
                              </div>
                            )}
                            <div className="mt-2 text-gray-300">{comment.content}</div>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Error Message */}
                {error && (
                  <div className="mx-3 mb-3 bg-red-900/30 border border-red-500 text-red-200 px-3 py-2 rounded-md text-sm">
                    {error}
                  </div>
                )}

                {/* Authentication Warning */}
                {!isUserAuthenticated && (
                  <div className="mx-3 mb-3 bg-yellow-900/30 border border-yellow-500 text-yellow-200 px-3 py-2 rounded-md text-sm">
                    You need to log in to comment on tasks.
                  </div>
                )}

                {/* Comment Form */}
                <div className="p-3 bg-gray-800/40 border-t border-gray-600">
                  <form onSubmit={handleSubmit} className="flex flex-col gap-2">
                    <div className="text-sm text-gray-400 mb-1">
                      {isUserAuthenticated ? "Add your comment" : "Please log in to comment"}
                    </div>
                    <div className={`${isMobile ? 'flex flex-col gap-2' : 'flex'}`}>
                      <input
                        type="text"
                        value={comment}
                        onChange={(e) => setComment(e.target.value)}
                        placeholder={!isUserAuthenticated ? "Please log in to comment" : "Type your comment here..."}
                        disabled={!selectedTaskId || !isUserAuthenticated || isSubmitting}
                        className={`flex-1 bg-gray-700 border border-gray-600 ${isMobile ? 'rounded-md' : 'rounded-l-md'} px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50`}
                      />
                      <button
                        type="submit"
                        disabled={isSubmitting || !selectedTaskId || !comment.trim() || !isUserAuthenticated}
                        className={`bg-purple-600 text-white px-3 py-2 ${isMobile ? 'rounded-md' : 'rounded-r-md'} hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed flex items-center justify-center`}
                      >
                        {isSubmitting ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            Send
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full py-12 text-gray-400">
                <div className="bg-gray-800/40 rounded-full p-6 mb-4">
                  <MessageSquare className="h-16 w-16 opacity-30" />
                </div>
                <p className="text-lg">Select a task to view comments</p>
                <p className="text-sm mt-2">
                  {isMobile ? "Choose a task from the dropdown below" : "Choose a task from the list on the left"}
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* View Comments Tab */}
      {activeTab === "view" && (
        <div className="bg-gray-700/50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-white">All Project Comments</h3>
            <div className="flex space-x-2">
              <button
                onClick={loadAllProjectComments}
                disabled={allCommentsLoading}
                className="px-3 py-1 bg-gray-600 text-white rounded-md hover:bg-gray-500 transition-colors text-sm flex items-center"
              >
                {allCommentsLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Refresh
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-gray-800 rounded-lg p-3 mb-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
              <div>
                <label htmlFor="task-filter" className="block text-sm font-medium text-gray-300 mb-1">
                  {isMobile ? "Task" : "Filter by Task"}
                </label>
                <select
                  id="task-filter"
                  className="bg-gray-700 text-white p-2 rounded w-full focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  onChange={(e) => setTaskFilter(e.target.value)}
                  value={taskFilter}
                >
                  <option value="">All Tasks</option>
                  {projectTasks.map((task) => (
                    <option key={task.id} value={task.id}>
                      {task.title.length > 30 && isMobile
                        ? task.title.substring(0, 30) + "..."
                        : task.title}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="user-filter" className="block text-sm font-medium text-gray-300 mb-1">
                  {isMobile ? "User" : "Filter by User"}
                </label>
                <select
                  id="user-filter"
                  className="bg-gray-700 text-white p-2 rounded w-full focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  onChange={(e) => setUserFilter(e.target.value)}
                  value={userFilter}
                >
                  <option value="">All Users</option>
                  {Array.from(new Set(allProjectComments.map((comment) => comment.createdBy))).map((email) => (
                    <option key={email} value={email}>
                      {getUserName(email)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="sort-by" className="block text-sm font-medium text-gray-300 mb-1">
                  Sort By
                </label>
                <select
                  id="sort-by"
                  className="bg-gray-700 text-white p-2 rounded w-full focus:outline-none focus:ring-2 focus:ring-purple-500 text-sm"
                  onChange={(e) => setSortBy(e.target.value)}
                  value={sortBy}
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="task">Task Name</option>
                  <option value="user">User Name</option>
                </select>
              </div>
            </div>
          </div>

          {/* Comments - Table for desktop, Cards for mobile */}
          {isMobile ? (
            // Mobile card view
            <div className="space-y-4">
              {allCommentsLoading ? (
                <div className="text-center py-8 text-gray-400">
                  <div className="animate-pulse flex flex-col items-center">
                    <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-600 rounded w-1/2 mb-4"></div>
                    <p className="mt-2">Loading comments...</p>
                  </div>
                </div>
              ) : filteredAndSortedComments.length === 0 ? (
                <div className="text-center py-12 text-gray-400">
                  <div className="flex flex-col items-center">
                    <MessageSquare className="h-16 w-16 mb-4 opacity-30" />
                    <p className="text-lg">No comments found</p>
                    <p className="text-sm mt-2">
                      {projectTasks.length === 0
                        ? "Add tasks to this project first"
                        : taskFilter || userFilter
                          ? "Try changing your filters"
                          : "Add comments to tasks in this project"}
                    </p>
                  </div>
                </div>
              ) : (
                filteredAndSortedComments.map((comment, index) => (
                  <div
                    key={comment.id}
                    className={`rounded-lg p-4 ${index % 2 === 0 ? "bg-gray-700/70" : "bg-gray-700/40"}`}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div className="px-2 py-1 bg-blue-900/30 text-blue-300 rounded-md text-sm">
                        {getUserName(comment.createdBy)}
                      </div>
                      <div className="text-right">
                        <div className="text-purple-300 text-sm font-medium">
                          {format(new Date(comment.createdAt), "MMM d, yyyy")}
                        </div>
                        <div className="text-xs text-gray-400">
                          {format(new Date(comment.createdAt), "h:mm a")}
                        </div>
                      </div>
                    </div>

                    <div className="px-2 py-1 bg-emerald-900/30 text-emerald-300 rounded-md text-sm inline-block mb-3">
                      {comment.taskTitle}
                    </div>

                    <div className="text-gray-300 border-t border-gray-600 pt-3">
                      {comment.content}
                    </div>
                  </div>
                ))
              )}
            </div>
          ) : (
            // Desktop table view
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-gray-800 text-left">
                    <th className="p-3 text-purple-400 font-medium rounded-tl-lg">Date/Time</th>
                    <th className="p-3 text-emerald-400 font-medium">Task</th>
                    <th className="p-3 text-amber-400 font-medium">Comment</th>
                    <th className="p-3 text-blue-400 font-medium rounded-tr-lg">User</th>
                  </tr>
                </thead>
                <tbody>
                  {allCommentsLoading ? (
                    <tr>
                      <td colSpan={4} className="text-center py-8 text-gray-400">
                        <div className="animate-pulse flex flex-col items-center">
                          <div className="h-4 bg-gray-600 rounded w-3/4 mb-2"></div>
                          <div className="h-4 bg-gray-600 rounded w-1/2 mb-4"></div>
                          <p className="mt-2">Loading comments...</p>
                        </div>
                      </td>
                    </tr>
                  ) : filteredAndSortedComments.length === 0 ? (
                    <tr>
                      <td colSpan={4} className="text-center py-12 text-gray-400">
                        <div className="flex flex-col items-center">
                          <MessageSquare className="h-16 w-16 mb-4 opacity-30" />
                          <p className="text-lg">No comments found</p>
                          <p className="text-sm mt-2">
                            {projectTasks.length === 0
                              ? "Add tasks to this project first"
                              : taskFilter || userFilter
                                ? "Try changing your filters"
                                : "Add comments to tasks in this project"}
                          </p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredAndSortedComments.map((comment, index) => (
                      <tr
                        key={comment.id}
                        className={`${index % 2 === 0 ? "bg-gray-700/70" : "bg-gray-700/40"} hover:bg-gray-600 transition-colors`}
                      >
                        <td className="p-3 border-b border-gray-600">
                          <div className="text-purple-300 font-medium">
                            {format(new Date(comment.createdAt), "MMM d, yyyy")}
                          </div>
                          <div className="text-xs text-gray-400">{format(new Date(comment.createdAt), "h:mm a")}</div>
                        </td>
                        <td className="p-3 border-b border-gray-600">
                          <div className="px-2 py-1 bg-emerald-900/30 text-emerald-300 rounded-md text-sm inline-block">
                            {comment.taskTitle}
                          </div>
                        </td>
                        <td className="p-3 border-b border-gray-600 text-gray-300">{comment.content}</td>
                        <td className="p-3 border-b border-gray-600">
                          <div className="px-2 py-1 bg-blue-900/30 text-blue-300 rounded-md text-sm inline-block">
                            {getUserName(comment.createdBy)}
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}

          {/* Comment Count */}
          {!allCommentsLoading && filteredAndSortedComments.length > 0 && (
            <div className="mt-4 text-sm text-gray-400">
              Showing {filteredAndSortedComments.length} of {allProjectComments.length} comments
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ProjectCommentsTab
