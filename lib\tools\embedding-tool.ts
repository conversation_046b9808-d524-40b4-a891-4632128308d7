/**
 * embedding-tool.ts
 *
 * This tool provides embedding functionality for text using OpenAI's embedding API.
 * It converts text into vector representations that can be used for semantic similarity
 * comparisons and other NLP tasks.
 */

import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Cache for embeddings to reduce API calls
const embeddingCache = new Map<string, number[]>();

/**
 * Get an embedding for the given text
 *
 * @param text - Text to get embedding for
 * @returns Vector representation of the text
 */
export async function getEmbedding(text: string): Promise<number[]> {
  // Normalize text for caching
  const normalizedText = text.trim().toLowerCase();

  // Check cache first
  if (embeddingCache.has(normalizedText)) {
    return embeddingCache.get(normalizedText)!;
  }

  try {
    // Call OpenAI embedding API
    const response = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: normalizedText,
    });

    // Extract embedding from response
    const embedding = response.data[0].embedding;

    // Cache the result
    embeddingCache.set(normalizedText, embedding);

    return embedding;
  } catch (error) {
    console.error('Error getting embedding:', error);

    // Return a zero vector as fallback
    return new Array(1536).fill(0);
  }
}

/**
 * Calculate cosine similarity between two embeddings
 *
 * @param embedding1 - First embedding vector
 * @param embedding2 - Second embedding vector
 * @returns Similarity score between 0 and 1
 */
export function calculateSimilarity(embedding1: number[], embedding2: number[]): number {
  if (embedding1.length !== embedding2.length) {
    throw new Error('Embeddings must have the same length');
  }

  let dotProduct = 0;
  let magnitude1 = 0;
  let magnitude2 = 0;

  for (let i = 0; i < embedding1.length; i++) {
    dotProduct += embedding1[i] * embedding2[i];
    magnitude1 += embedding1[i] * embedding1[i];
    magnitude2 += embedding2[i] * embedding2[i];
  }

  magnitude1 = Math.sqrt(magnitude1);
  magnitude2 = Math.sqrt(magnitude2);

  if (magnitude1 === 0 || magnitude2 === 0) {
    return 0;
  }

  return dotProduct / (magnitude1 * magnitude2);
}

/**
 * Find the most similar texts to a query text
 *
 * @param queryText - Text to compare against
 * @param candidateTexts - Array of texts to compare with the query
 * @param topK - Number of most similar texts to return
 * @returns Array of {text, score} objects sorted by similarity
 */
export async function findMostSimilarTexts(
  queryText: string,
  candidateTexts: string[],
  topK: number = 3
): Promise<Array<{text: string, score: number}>> {
  // Get query embedding
  const queryEmbedding = await getEmbedding(queryText);

  // Get embeddings for all candidate texts
  const candidateEmbeddings = await Promise.all(
    candidateTexts.map(text => getEmbedding(text))
  );

  // Calculate similarity scores
  const scoredCandidates = candidateTexts.map((text, i) => ({
    text,
    score: calculateSimilarity(queryEmbedding, candidateEmbeddings[i])
  }));

  // Sort by score (descending) and take top K
  return scoredCandidates
    .sort((a, b) => b.score - a.score)
    .slice(0, topK);
}
