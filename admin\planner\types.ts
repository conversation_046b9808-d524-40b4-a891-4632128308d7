// Task status options
export type TaskStatus = 'Not Started' | 'In Progress' | 'Reviewed' | 'Complete';

// Task priority options
export type TaskPriority = 'Low' | 'Medium' | 'High' | 'Critical';

// Backlog item status options
export type BacklogStatus = 'Proposed' | 'Approved' | 'Rejected';

// User role options
export type UserRole = 'admin' | 'user';

// User interface
export interface User {
  photoURL: any;
  displayName: string;
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatar: string;
  availability: 'Full-time' | 'Part-time' | 'Contract';
  phone?: string;
  department?: string;
  jobTitle? : string;
  skills?: string[];
  isAuthorized?: boolean; // Whether the user is authorized to access the admin panel
  createdAt?: Date;
  updatedAt?: Date;
}

// Project interface
export interface Project {
  id: string;
  name: string;
  description: string;
  startDate: Date;
  endDate: Date;
  owner: string; // User ID of project owner
  members: string[]; // Array of user IDs
  categories?: string[];
  status: 'Active' | 'Completed' | 'On Hold' | 'Cancelled';
  createdAt?: Date;
  updatedAt?: Date;
}

// Task comment interface
export interface TaskComment {
  id: string;
  taskId: string;
  content: string;
  createdBy: string; // User ID or email of comment creator
  createdAt: Date;
}

// Subtask interface
export interface Subtask {
  id: string;
  parentTaskId: string; // ID of the parent task
  title: string;
  description: string;
  status: TaskStatus;
  assignedTo: string[]; // Array of user IDs
  priority: TaskPriority;
  dueDate?: Date;
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Task interface
export interface Task {
  id: string;
  projectId: string; // ID of the project this task belongs to
  title: string;
  description: string;
  category: string;
  status: TaskStatus;
  startDate: Date;
  dueDate: Date;
  assignedTo: string[]; // Array of user IDs
  priority: TaskPriority;
  dependencies: string[]; // Array of task IDs that this task depends on
  subtasks?: Subtask[]; // Array of subtasks
  notes?: string;
  attachments?: string[];
  comments?: TaskComment[]; // Array of comments
  createdBy?: string; // User ID of task creator
  createdAt?: Date;
  updatedAt?: Date;
}

// Resource interface
export interface Resource {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  jobTitle?: string; // Added jobTitle field for job titles like 'UX Designer'
  avatar: string;
  availability: 'Full-time' | 'Part-time' | 'Contract';
  phone?: string;
  department?: string;
  skills?: string[];
  isAuthorized?: boolean; // Whether the resource is authorized to access the admin panel
  createdAt?: Date;
  updatedAt?: Date;
}

// Filter interface
export interface TaskFilter {
  status: string;
  category: string;
  search: string;
  assignedTo: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  priority?: TaskPriority | 'all';
  projectId?: string;
}

// Backlog item interface
export interface BacklogItem {
  id: string;
  projectId: string; // ID of the project this backlog item belongs to
  title: string;
  description: string;
  priority: TaskPriority;
  status: BacklogStatus;
  votes: number; // Number of votes/upvotes from team members
  category?: string;
  createdBy: string; // User ID or email of the creator
  createdAt: Date;
  updatedAt?: Date;
  approvedBy?: string; // User ID or email of the admin who approved/rejected
  approvedAt?: Date;
  comments?: BacklogComment[];
}

// Backlog comment interface
export interface BacklogComment {
  id: string;
  backlogItemId: string;
  text: string;
  createdBy: string; // User ID or email
  createdAt: Date;
}

// Personal item interface
export interface PersonalItem {
  id: string;
  userId: string;
  title: string;
  description?: string;
  startDate: Date;
  endDate: Date;
  startTime?: string; // Format: 'HH:MM' (24-hour format)
  endTime?: string; // Format: 'HH:MM' (24-hour format)
  color?: string;
  createdAt: Date;
  updatedAt?: Date;
}

// Calendar filter interface
export interface CalendarFilter {
  showUserTasks: boolean;
  showUserItems: boolean;
  showAllProjectTasks: boolean;
}

// Calendar view type
export type CalendarViewType = 'day' | 'week' | 'month';

// Calendar event interface (for rendering)
export interface CalendarEvent {
  id: string;
  title: string;
  start: string | Date;
  end: string | Date;
  color: string;
  type: 'task' | 'personal';
  location?: string;
  attendees?: { length: number } | boolean;
  priority?: TaskPriority;
  status?: TaskStatus;
  projectId?: string;
  projectName?: string;
}
