/**
 * Document Processing API
 *
 * This API provides endpoints for asynchronous document processing:
 * - POST /api/document-processing: Submit a document processing job
 * - GET /api/document-processing/:id: Get the status of a document processing job
 * - DELETE /api/document-processing/:id: Cancel a document processing job
 */

import { NextRequest, NextResponse } from 'next/server';
import { documentProcessingQueue, JobStatus, JobPriority, DocumentProcessingJob } from '../../../lib/queue/documentProcessingQueue'; // Adjust path as needed
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions'; // Adjust path as needed
import { StrategicDirectorAgent } from '../../../lib/agents/marketing/StrategicDirectorAgent'; // Adjust path as needed

// Define a type for the user/actor identifier obtained after authentication.
interface AuthenticatedActor {
  id: string; // Represents the user ID (e.g., email or 'anonymous' for session users, or provided ID for internal calls)
}

// Define a type for Session from next-auth for stronger typing
interface Session {
  user?: {
    email?: string;
    name?: string;
    image?: string;
  };
  expires?: string;
}

/**
 * Unified authentication helper: supports session-based auth and internal secret-based auth.
 * Returns the authenticated actor or an error response.
 */
async function authenticateAndGetActor(request: NextRequest): Promise<{ actor: AuthenticatedActor | null; response: NextResponse | null }> {
  const internalAuthHeader = request.headers.get('X-Internal-Auth');
  const internalSecretFromEnv = process.env.INTERNAL_API_SECRET;

  // Enable debugging to help troubleshoot authentication issues
  console.log(`[Auth Debug] Received X-Internal-Auth header: ${internalAuthHeader ? "Present" : "Missing"}`);
  console.log(`[Auth Debug] ENV INTERNAL_API_SECRET: ${internalSecretFromEnv ? "Configured" : "Missing"}`);

  // Handle internal authentication
  if (internalAuthHeader) {
    if (!internalSecretFromEnv || internalSecretFromEnv.trim() === '') {
      console.error('INTERNAL_API_SECRET environment variable is not configured properly for document processing API.');
      return {
        actor: null,
        response: NextResponse.json({ error: 'Internal authentication is not configured correctly on the server.' }, { status: 500 })
      };
    }

    // Trim both header and env var for comparison to avoid whitespace issues
    if (internalAuthHeader.trim() === internalSecretFromEnv.trim()) {
      const actingAsUserId = request.headers.get('X-Acting-As-User-Id');
      if (actingAsUserId && actingAsUserId.trim() !== '') {
        console.log(`Internal authentication successful. Acting as user: ${actingAsUserId.trim()}`);
        return { actor: { id: actingAsUserId.trim() }, response: null };
      } else {
        // If no user ID is provided, use a default system user ID
        console.log(`Internal authentication successful. No user ID provided, using system default.`);
        return { actor: { id: 'system-default-user' }, response: null };
      }
    } else {
      console.warn('Internal call attempt with invalid X-Internal-Auth secret.');
      console.warn(`[Auth Debug] Secret mismatch detected. Please check configuration.`);
      return { actor: null, response: NextResponse.json({ error: 'Invalid internal authentication credentials.' }, { status: 401 }) };
    }
  }

  // Standard session-based authentication (for external users / clients)
  try {
    console.log('Attempting session-based authentication for document processing API.');
    const session = await getServerSession(authOptions) as Session | null;

    if (!session?.user?.email) { // Prefer email as a more stable ID
      console.warn('No valid session or user email found for the request.');
      return { actor: null, response: NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 }) };
    }

    const userIdFromSession = session.user.email;
    console.log(`User authenticated via session: ${userIdFromSession}`);
    return { actor: { id: userIdFromSession }, response: null };
  } catch (error) {
    console.error('Error during session authentication for document processing API:', error);
    return {
      actor: null,
      response: NextResponse.json({ error: 'Authentication error occurred.' }, { status: 500 })
    };
  }
}

// Register the document processing processor
// This should ideally be done once when the application starts,
// but for Next.js API routes, placing it here ensures it's registered
// when the route module is loaded.
if (!documentProcessingQueue.isProcessorRegistered('documentProcessing')) {
    documentProcessingQueue.registerProcessor('documentProcessing', async (job: DocumentProcessingJob) => {
    console.log(`Processing document job ID ${job.id} for user: ${job.userId}`);

    // Create a strategic director agent for the user associated with the job
    const strategicDirector = new StrategicDirectorAgent(job.userId); // Assuming constructor takes userId

    // Process the document query
    const result = await strategicDirector.queryDocumentsEnhanced(
        job.documentQuery,
        job.category,
        job.filename,
        job.namespace,
        job.useInternetSearch || false, // Default to false if not provided
        job.modelName
    );

    console.log(`Finished processing document job ID ${job.id} for user: ${job.userId}`);
    return result;
    });
}


/**
 * POST /api/document-processing
 * Submit a document processing job
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Received POST /api/document-processing request');

    const authContext = await authenticateAndGetActor(request);
    if (authContext.response) {
      console.log('Authentication failed for POST /api/document-processing, returning error response');
      return authContext.response;
    }

    const actorId = authContext.actor!.id;
    console.log(`Request authenticated for POST /api/document-processing as user: ${actorId}`);

    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('Failed to parse request body for POST /api/document-processing:', parseError);
      return NextResponse.json({ error: 'Invalid JSON payload provided.' }, { status: 400 });
    }

    if (!body.documentQuery || typeof body.documentQuery !== 'string' || body.documentQuery.trim() === '') {
      console.warn('Missing or invalid required field: documentQuery');
      return NextResponse.json({ error: 'Missing or invalid required field: documentQuery' }, { status: 400 });
    }

    const jobData: Omit<DocumentProcessingJob, 'id' | 'status' | 'createdAt' | 'updatedAt' | 'progress' | 'attempts' | 'result' | 'error' | 'startedAt' | 'completedAt'> = {
      userId: actorId,
      documentQuery: body.documentQuery,
      category: body.category,
      filename: body.filename,
      namespace: body.namespace,
      useInternetSearch: body.useInternetSearch || false,
      modelName: body.modelName || 'google/gemini-pro', // Default model if not specified
      priority: body.priority || JobPriority.NORMAL,
      callbackUrl: body.callbackUrl,
      callbackHeaders: body.callbackHeaders
    };

    console.log(`Enqueueing document processing job for user ${actorId}, namespace: ${body.namespace || 'default'}`);

    const job = documentProcessingQueue.enqueue(jobData);
    console.log(`Job created successfully with ID: ${job.id} for user: ${actorId}`);

    return NextResponse.json({
      jobId: job.id,
      status: job.status,
      message: 'Document processing job submitted successfully.'
    }, { status: 202 });
  } catch (error) {
    console.error('Error submitting document processing job:', error);

    // Check for specific error types
    if (error instanceof Error) {
      if (error.message.includes('authentication') || error.message.includes('auth')) {
        console.error('Authentication error detected:', error.message);
        return NextResponse.json({
          error: 'Authentication error: ' + error.message,
          details: 'Please check your authentication credentials and try again.',
          code: 'AUTH_ERROR'
        }, { status: 401 });
      } else if (error.message.includes('processor') || error.message.includes('queue')) {
        console.error('Document processing queue error:', error.message);
        return NextResponse.json({
          error: 'Document processing system error: ' + error.message,
          details: 'The document processing system is currently unavailable. Please try again later.',
          code: 'QUEUE_ERROR'
        }, { status: 503 });
      }

      // Generic error with stack trace for debugging
      return NextResponse.json({
        error: error.message,
        details: error.stack,
        code: 'GENERAL_ERROR'
      }, { status: 500 });
    }

    // Unknown error type
    return NextResponse.json({
      error: 'An unknown error occurred while submitting the document processing job.',
      code: 'UNKNOWN_ERROR'
    }, { status: 500 });
  }
}

// Note: GET and DELETE methods for specific job IDs should be implemented
// in a separate dynamic route file: app/api/document-processing/[id]/route.ts
// These methods have been temporarily removed to fix Next.js 15 build issues