/**
 * Test script to verify PMO agent routing is working correctly
 * This script tests the team-specific routing for Research vs Marketing PMO records
 */

// Mock agent outputs for testing
const mockResearchAgentOutput = {
  requestId: 'research-test-123',
  agentType: 'ResearchAgentManager',
  pmoMetadata: {
    pmoId: 'pmo-research-456',
    teamName: 'Research',
    teamId: 'Research',
    source: 'PMO'
  },
  metadata: {
    teamName: 'Research',
    pmoId: 'pmo-research-456'
  },
  result: {
    output: `# Research Strategic Implementation Plan

## Executive Summary
This comprehensive research strategy focuses on market analysis and competitive intelligence.

## Key Research Tasks:
1. **Market Analysis** - Conduct comprehensive market research
   - Duration: 2 weeks
   - Team: Research Team
   - Priority: High

2. **Competitive Intelligence** - Analyze competitor strategies
   - Duration: 1 week  
   - Team: Research Team
   - Priority: Medium

3. **Customer Insights** - Gather customer feedback and insights
   - Duration: 3 weeks
   - Team: Research Team
   - Priority: High`
  }
};

const mockMarketingAgentOutput = {
  requestId: 'marketing-test-789',
  agentType: 'strategic-director',
  pmoMetadata: {
    pmoId: 'pmo-marketing-101',
    teamName: 'Marketing',
    teamId: 'Marketing',
    source: 'PMO'
  },
  metadata: {
    teamName: 'Marketing',
    pmoId: 'pmo-marketing-101'
  },
  result: {
    output: `# Marketing Strategic Implementation Plan

## Executive Summary
This comprehensive marketing strategy focuses on brand development and customer acquisition.

## Key Marketing Tasks:
1. **Brand Strategy Development** - Create comprehensive brand strategy
   - Duration: 3 weeks
   - Team: Marketing Team
   - Priority: High

2. **Content Creation** - Develop marketing content and campaigns
   - Duration: 2 weeks
   - Team: Marketing Team
   - Priority: Medium

3. **Digital Marketing** - Implement digital marketing campaigns
   - Duration: 4 weeks
   - Team: Marketing Team
   - Priority: High`
  }
};

// Test functions
function testTeamAssignmentDetection() {
  console.log('=== Testing Team Assignment Detection ===');
  
  // Test Research team detection
  const researchTeam = determineTeamAssignment(mockResearchAgentOutput);
  console.log(`Research Agent Output -> Team: ${researchTeam}`);
  console.log(`Expected: Research, Actual: ${researchTeam}, Match: ${researchTeam === 'Research'}`);
  
  // Test Marketing team detection
  const marketingTeam = determineTeamAssignment(mockMarketingAgentOutput);
  console.log(`Marketing Agent Output -> Team: ${marketingTeam}`);
  console.log(`Expected: Marketing, Actual: ${marketingTeam}, Match: ${marketingTeam === 'Marketing'}`);
  
  console.log('');
}

function testExpectedAgentTypes() {
  console.log('=== Testing Expected Agent Types ===');
  
  // Test Research expected agent type
  const researchExpected = getExpectedAgentType('Research');
  console.log(`Research Team -> Expected Agent: ${researchExpected}`);
  console.log(`Expected: ResearchAgentManager, Actual: ${researchExpected}, Match: ${researchExpected === 'ResearchAgentManager'}`);
  
  // Test Marketing expected agent type
  const marketingExpected = getExpectedAgentType('Marketing');
  console.log(`Marketing Team -> Expected Agent: ${marketingExpected}`);
  console.log(`Expected: strategic-director, Actual: ${marketingExpected}, Match: ${marketingExpected === 'strategic-director'}`);
  
  console.log('');
}

function testAgentTypeValidation() {
  console.log('=== Testing Agent Type Validation ===');
  
  // Test Research validation
  const researchTeam = determineTeamAssignment(mockResearchAgentOutput);
  const researchExpected = getExpectedAgentType(researchTeam);
  const researchActual = mockResearchAgentOutput.agentType;
  const researchValid = researchExpected === researchActual;
  console.log(`Research Validation: Team=${researchTeam}, Expected=${researchExpected}, Actual=${researchActual}, Valid=${researchValid}`);
  
  // Test Marketing validation
  const marketingTeam = determineTeamAssignment(mockMarketingAgentOutput);
  const marketingExpected = getExpectedAgentType(marketingTeam);
  const marketingActual = mockMarketingAgentOutput.agentType;
  const marketingValid = marketingExpected === marketingActual;
  console.log(`Marketing Validation: Team=${marketingTeam}, Expected=${marketingExpected}, Actual=${marketingActual}, Valid=${marketingValid}`);
  
  console.log('');
}

// Helper functions (copied from PMOProjectsTaskAgent)
function determineTeamAssignment(agentOutput) {
  // Check PMO metadata first
  const pmoTeam = agentOutput.pmoMetadata?.teamName || agentOutput.pmoMetadata?.teamId;
  if (pmoTeam) {
    return pmoTeam;
  }

  // Check general metadata
  const metadataTeam = agentOutput.metadata?.teamName || agentOutput.metadata?.teamId;
  if (metadataTeam) {
    return metadataTeam;
  }

  // Determine from agent type
  const agentType = agentOutput.agentType;
  if (agentType === 'ResearchAgentManager') {
    return 'Research';
  } else if (agentType === 'strategic-director') {
    return 'Marketing';
  }

  // Check category for team hints
  const category = agentOutput.category || '';
  if (category.toLowerCase().includes('research')) {
    return 'Research';
  } else if (category.toLowerCase().includes('marketing')) {
    return 'Marketing';
  }

  return 'Unknown';
}

function getExpectedAgentType(teamAssignment) {
  const team = teamAssignment.toLowerCase();
  
  if (team === 'research') {
    return 'ResearchAgentManager';
  } else if (team === 'marketing') {
    return 'strategic-director';
  }
  
  return null;
}

// Run tests
console.log('🧪 PMO Agent Routing Test Suite\n');
testTeamAssignmentDetection();
testExpectedAgentTypes();
testAgentTypeValidation();

console.log('✅ Test Summary:');
console.log('- Research PMO records should route to ResearchAgentManager output');
console.log('- Marketing PMO records should route to Strategic Director Agent output');
console.log('- Team assignment detection should work from metadata and agent type');
console.log('- Agent type validation should catch mismatches');
