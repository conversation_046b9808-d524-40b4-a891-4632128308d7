'use client';

import React, { useState, useEffect } from 'react';
import { X, Copy, Check, FileDown, Edit } from 'lucide-react';
import MarkdownRenderer from '../MarkdownRenderer'; // Assuming this path is correct

interface PMOAssessmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  content: string;
  onEdit: () => void;
  onSubmit: () => void;
  isSubmitting?: boolean;
}

const PMOAssessmentModal: React.FC<PMOAssessmentModalProps> = ({
  isOpen,
  onClose,
  title,
  content,
  onEdit,
  onSubmit,
  isSubmitting = false,
}) => {
  const [copied, setCopied] = useState(false);
  const [generating, setGenerating] = useState(false);

  // Close modal on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose]);

  // Prevent scrolling of the body when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy content:', error);
    }
  };

  const handleGeneratePdf = () => {
    try {
      setGenerating(true);

      // Create a printable window with the content
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${title}</title>
              <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }
                h1 { color: #6b46c1; }
                h2 { color: #805ad5; }
                pre { white-space: pre-wrap; background: #f0f0f0; padding: 10px; border-radius: 5px; }
                code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
                .header { margin-bottom: 20px; border-bottom: 1px solid #e2e8f0; padding-bottom: 10px; }
                .content { margin-top: 20px; }
                @media print {
                  body { font-size: 12pt; }
                  h1 { font-size: 18pt; }
                  h2 { font-size: 16pt; }
                  .no-print { display: none; }
                  a { text-decoration: none; color: #000; }
                }
              </style>
            </head>
            <body>
              <div class="header">
                <h1>${title}</h1>
                <div class="no-print">
                  <button onclick="window.print();" style="padding: 8px 16px; background: #6b46c1; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                    Save as PDF
                  </button>
                  <p style="font-size: 12px; color: #666;">
                    (Use your browser's print function to save as PDF)
                  </p>
                </div>
              </div>
              <div class="content">
                ${formatMarkdownForHTML(content)}
              </div>
            </body>
          </html>
        `);
        printWindow.document.close();

        // Add a small delay to ensure the content is loaded before printing
        setTimeout(() => {
          printWindow.focus();
          setGenerating(false);
        }, 500);
      } else {
        setGenerating(false);
        alert('Unable to open print window. Please check your popup blocker settings.');
      }
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      setGenerating(false);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  // Format markdown content for HTML display
  const formatMarkdownForHTML = (markdown: string) => {
    // Basic markdown formatting
    let html = markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Bold
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      // Lists (simplified - this might need a more robust parser for nested lists or combined list types)
      .replace(/^\s*\d+\.\s+(.*$)/gim, (match, p1) => `<li>${p1}</li>`) // Handle list items
      .replace(/(<li>.*?<\/li>\s*)+/gim, (match) => `<ol>${match}</ol>`) // Wrap OL items
      .replace(/^\s*[\-\*]\s+(.*$)/gim, (match, p1) => `<li>${p1}</li>`) // Handle list items
      .replace(/(<li>.*?<\/li>\s*)+/gim, (match) => `<ul>${match}</ul>`) // Wrap UL items - ensure this doesn't over-wrap
      // Line breaks
      .replace(/\n/gim, '<br>');

    // Refine list handling to avoid nested <ol/ul> inside <ol/ul> if not intended
    // This simplified list conversion might not be perfect for all markdown cases.
    // A dedicated library is better for robust markdown-to-HTML.
    // For now, we'll keep it as it was, with a note that complex lists might render imperfectly.
    html = markdown
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      .replace(/^\s*\d+\.\s+(.*$)/gim, '<ol><li>$1</li></ol>') // Original behavior
      .replace(/^\s*[\-\*]\s+(.*$)/gim, '<ul><li>$1</li></ul>') // Original behavior
      .replace(/\n/gim, '<br>');


    return html;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
      <div className="relative w-full h-full max-w-full max-h-full flex flex-col bg-gray-900 text-white">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-purple-300">{title}</h2>
            <div className="text-sm text-gray-400 flex items-center">
              <span className="mr-2">PMO Assessment</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleCopyToClipboard}
              className="p-2 bg-gray-800 hover:bg-gray-700 active:bg-gray-600 rounded-full transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900"
              title="Copy to clipboard"
            >
              {copied ? (
                <Check className="h-5 w-5 text-green-400" />
              ) : (
                <Copy className="h-5 w-5 text-purple-300" />
              )}
            </button>
            <button
              onClick={handleGeneratePdf}
              className="p-2 bg-gray-800 hover:bg-gray-700 active:bg-gray-600 rounded-full transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900"
              title="Print / Save as PDF"
              disabled={generating}
            >
              <FileDown className={`h-5 w-5 ${generating ? 'text-gray-500' : 'text-purple-300'}`} />
            </button>
            <button
              onClick={onEdit}
              className="p-2 bg-gray-800 hover:bg-gray-700 active:bg-gray-600 rounded-full transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900"
              title="Edit Assessment"
            >
              <Edit className="h-5 w-5 text-purple-300" />
            </button>
            <button
              onClick={onClose}
              className="p-2 bg-gray-800 hover:bg-gray-700 active:bg-gray-600 rounded-full transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900"
              title="Close"
            >
              <X className="h-5 w-5 text-purple-300" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto custom-scrollbar p-4 text-sm">
          <MarkdownRenderer content={content} />
        </div>

        {/* Footer with action buttons */}
        <div className="p-4 border-t border-gray-700 flex justify-end space-x-3">
          <button
            onClick={onEdit}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 active:bg-gray-500 text-white rounded-md transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-gray-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900"
          >
            Edit Request
          </button>
          <button
            onClick={onSubmit}
            disabled={isSubmitting}
            className={`px-4 py-2 bg-purple-600 hover:bg-purple-700 active:bg-purple-800 text-white rounded-md transition-colors focus:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-900 ${
              isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              'Submit PMO Request'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PMOAssessmentModal;