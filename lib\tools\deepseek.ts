/**
 * DeepSeek integration for the LLM tool
 */

// Import axios for API requests
import axios from 'axios';

// Define interfaces for DeepSeek processing
export interface DeepSeekProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: {
    temperature?: number;
    maxTokens?: number;
    [key: string]: any;
  };
}

/**
 * Process content with DeepSeek LLM
 *
 * @param options - Processing options including prompt and model settings
 * @returns The generated content
 */
export async function processWithDeepSeek(options: DeepSeekProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "deepseek-coder",
      modelOptions = {}
    } = options;

    console.log(`Processing with DeepSeek model: ${model}`);

    // Get the API key from environment variables
    const deepseekApiKey = process.env.DEEPSEEK_API_KEY || '********************************************************************************************************************************************************************';

    if (!deepseekApiKey) {
      console.warn('DEEPSEEK_API_KEY environment variable is not set. Using development fallback mode.');
      
      // In development, return a mock response
      if (process.env.NODE_ENV === 'development') {
        return `[DEVELOPMENT MODE] This is a mock response from DeepSeek model "${model}" for the prompt: "${prompt.substring(0, 100)}...".
For testing purposes, this mock response is being returned instead of making an actual API call.`;
      } else {
        throw new Error('DEEPSEEK_API_KEY environment variable is not set');
      }
    }

    // Make API request to DeepSeek
    const response = await axios.post(
      'https://api.deepseek.com/v1/chat/completions',
      {
        model,
        messages: [{ role: 'user', content: prompt }],
        temperature: modelOptions.temperature || 0.3,
        max_tokens: modelOptions.maxTokens || 2000,
        ...modelOptions
      },
      {
        headers: {
          'Authorization': `Bearer ${deepseekApiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Return the text content from the response
    return response.data.choices[0]?.message?.content || "";
  } catch (error: any) {
    console.error("Error processing content with DeepSeek:", error);
    
    // Handle API errors
    if (error.response) {
      console.error("DeepSeek API error:", error.response.data);
      return `Error from DeepSeek API: ${error.response.data.error?.message || JSON.stringify(error.response.data)}`;
    }
    
    return `Error from DeepSeek: ${error.message || "Unknown error"}`;
  }
}

/**
 * Get available DeepSeek models
 * @returns List of available models
 */
export function getDeepSeekModels(): string[] {
  return [
    "deepseek-coder",
    "deepseek-chat",
    "deepseek-llm-67b-chat"
  ];
}
