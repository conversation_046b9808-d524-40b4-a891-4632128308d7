'use client';

import React, { useState, useEffect } from 'react';
import { Task } from '../types';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { format, addDays, eachDayOfInterval, startOfMonth, endOfMonth, isSameDay, differenceInDays, isWithinInterval } from 'date-fns';

interface GanttChartViewProps {
  projectId: string;
  tasks: Task[];
}

const GanttChartView: React.FC<GanttChartViewProps> = ({ projectId, tasks }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [days, setDays] = useState<Date[]>([]);
  const [sortedTasks, setSortedTasks] = useState<Task[]>([]);

  // Initialize days array for the current month
  useEffect(() => {
    const start = startOfMonth(currentDate);
    const end = endOfMonth(currentDate);
    setDays(eachDayOfInterval({ start, end }));
  }, [currentDate]);

  // Sort tasks by start date
  useEffect(() => {
    const sorted = [...tasks].sort((a, b) => {
      return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
    });
    setSortedTasks(sorted);
  }, [tasks]);

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentDate(prev => {
      const prevMonth = new Date(prev);
      prevMonth.setMonth(prev.getMonth() - 1);
      return prevMonth;
    });
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentDate(prev => {
      const nextMonth = new Date(prev);
      nextMonth.setMonth(prev.getMonth() + 1);
      return nextMonth;
    });
  };

  // Go to current month
  const goToCurrentMonth = () => {
    setCurrentDate(new Date());
  };

  // Get color based on task priority
  const getTaskColor = (priority: string): string => {
    switch (priority) {
      case 'Critical':
        return '#ef4444'; // Red
      case 'High':
        return '#f97316'; // Orange
      case 'Medium':
        return '#3b82f6'; // Blue
      case 'Low':
        return '#6b7280'; // Gray
      default:
        return '#3b82f6'; // Default blue
    }
  };

  // Get status color
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'Complete':
        return '#10b981'; // Green
      case 'In Progress':
        return '#3b82f6'; // Blue
      case 'Reviewed':
        return '#8b5cf6'; // Purple
      case 'Not Started':
        return '#6b7280'; // Gray
      default:
        return '#6b7280'; // Default gray
    }
  };

  // Calculate task bar position and width
  const calculateTaskBar = (task: Task) => {
    const startDate = new Date(task.startDate);
    const endDate = new Date(task.dueDate);

    // Check if task is within the current month view
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);

    if (
      (startDate > monthEnd) ||
      (endDate < monthStart)
    ) {
      return null; // Task is not visible in current month
    }

    // Adjust dates if they fall outside the current month
    const visibleStartDate = startDate < monthStart ? monthStart : startDate;
    const visibleEndDate = endDate > monthEnd ? monthEnd : endDate;

    // Calculate position and width
    const startOffset = differenceInDays(visibleStartDate, monthStart);
    const duration = differenceInDays(visibleEndDate, visibleStartDate) + 1; // +1 to include the end date

    return {
      left: `${(startOffset / days.length) * 100}%`,
      width: `${(duration / days.length) * 100}%`,
    };
  };

  return (
    <div className="gantt-chart bg-gray-800 rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <button
            onClick={goToPreviousMonth}
            className="p-1.5 rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600"
          >
            <ChevronLeft size={18} />
          </button>
          <button
            onClick={goToNextMonth}
            className="p-1.5 rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600"
          >
            <ChevronRight size={18} />
          </button>
          <h3 className="text-xl font-medium text-white ml-2">{format(currentDate, 'MMMM yyyy')}</h3>
        </div>
        <button
          onClick={goToCurrentMonth}
          className="flex items-center px-3 py-1.5 bg-gray-700 text-gray-300 rounded-md hover:bg-gray-600"
        >
          <Calendar size={16} className="mr-1" />
          Current Month
        </button>
      </div>

      <div className="gantt-header grid grid-cols-[200px_1fr] border-b border-gray-700">
        <div className="p-3 font-medium text-gray-300 border-r border-gray-700">Task</div>
        <div className="grid" style={{ gridTemplateColumns: `repeat(${days.length}, 1fr)` }}>
          {days.map((day, index) => (
            <div
              key={index}
              className={`p-2 text-center text-xs border-r border-gray-700 last:border-r-0 ${
                isSameDay(day, new Date()) ? 'bg-purple-900/30' : ''
              }`}
            >
              <div className="font-medium text-gray-400">{format(day, 'EEE')}</div>
              <div className={`${isSameDay(day, new Date()) ? 'text-purple-400 font-bold' : 'text-white'}`}>
                {format(day, 'd')}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="gantt-body" style={{ minHeight: '400px' }}>
        {sortedTasks.length > 0 ? (
          sortedTasks.map(task => {
            const taskBar = calculateTaskBar(task);

            if (!taskBar) return null; // Skip tasks not visible in current month

            return (
              <div key={task.id} className="grid grid-cols-[200px_1fr] border-b border-gray-700 hover:bg-gray-750">
                <div className="p-3 border-r border-gray-700">
                  <div className="font-medium text-white truncate" title={task.title}>
                    {task.title}
                  </div>
                  <div className="flex mt-1 space-x-2">
                    <span
                      className="px-1.5 py-0.5 text-xs rounded-full"
                      style={{ backgroundColor: `${getTaskColor(task.priority)}30`, color: getTaskColor(task.priority) }}
                    >
                      {task.priority}
                    </span>
                    <span
                      className="px-1.5 py-0.5 text-xs rounded-full"
                      style={{ backgroundColor: `${getStatusColor(task.status)}30`, color: getStatusColor(task.status) }}
                    >
                      {task.status}
                    </span>
                  </div>
                </div>
                <div className="relative h-16">
                  <div
                    className="absolute top-2 bottom-2 rounded-md"
                    style={{
                      left: taskBar.left,
                      width: taskBar.width,
                      backgroundColor: `${getTaskColor(task.priority)}30`,
                      borderLeft: `3px solid ${getTaskColor(task.priority)}`,
                    }}
                  >
                    <div className="h-full flex items-center px-2 overflow-hidden">
                      <span className="text-xs text-white truncate">
                        {format(new Date(task.startDate), 'MMM d')} - {format(new Date(task.dueDate), 'MMM d')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="flex flex-col items-center justify-center h-60 text-gray-400">
            <div className="text-lg mb-2">No tasks found for this project</div>
            <div className="text-sm text-gray-500 max-w-md text-center">
              <p>Tasks with project ID "{projectId}" could not be loaded.</p>
              <p className="mt-2">This could be due to:</p>
              <ul className="list-disc text-left pl-8 mt-1">
                <li>No tasks created for this project yet</li>
                <li>Permission issues accessing the tasks</li>
                <li>Tasks having a different project ID format</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GanttChartView;
