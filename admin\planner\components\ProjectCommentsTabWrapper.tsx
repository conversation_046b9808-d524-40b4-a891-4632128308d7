'use client';

import React, { useEffect, useState } from 'react';
import { FirebaseProvider } from '../context/FirebaseContext';
import ProjectCommentsTab from './ProjectCommentsTab';
import { useAuth } from '../../../app/context/AuthContext';

interface ProjectCommentsTabWrapperProps {
  projectId: string;
  users: any[];
}

const ProjectCommentsTabWrapper: React.FC<ProjectCommentsTabWrapperProps> = ({ projectId, users }) => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Just to ensure auth state is loaded
    if (user !== undefined) {
      setIsLoading(false);
    }
  }, [user]);

  if (isLoading) {
    return (
      <div className="bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-medium text-white mb-4">Project Comments</h2>
        <div className="text-center py-8 text-gray-400">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-700 rounded w-3/4 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-700 rounded w-1/2 mx-auto"></div>
          </div>
          <p className="mt-4">Loading comments...</p>
        </div>
      </div>
    );
  }

  return (
    <FirebaseProvider>
      <ProjectCommentsTab projectId={projectId} users={users} currentAuthUser={user} />
    </FirebaseProvider>
  );
};

export default ProjectCommentsTabWrapper;