// app/providers.tsx
'use client';

import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { SelectedDocProvider } from "components/SelectedDocContext";
import { AnalyticsProvider } from "lib/analytics/analyticsProvider";
import { AuthProvider } from "./context/AuthContext";
import { PlannerProvider } from "./context/PlannerContext";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <SessionProvider>
      <AuthProvider>
        <PlannerProvider>
          <SelectedDocProvider>
            <AnalyticsProvider>
              {children}
            </AnalyticsProvider>
          </SelectedDocProvider>
        </PlannerProvider>
      </AuthProvider>
    </SessionProvider>
  );
}