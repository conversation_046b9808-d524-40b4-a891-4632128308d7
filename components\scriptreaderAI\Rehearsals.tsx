"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Mic, Volume2, VolumeX, Loader, Info, Play, StopCircle, ChevronLeft, ChevronRight } from "lucide-react"
import type { VoiceSettings } from "./RealtimeConnection"

// Define the component's props with complete type definitions
interface RehearsalsProps {
  // Connection state props
  apiConfigStatus: "unchecked" | "valid" | "invalid" | "connecting"
  detailedErrorInfo: string | null
  isListening: boolean
  voiceStatus: "connected" | "disconnected"
  isMuted: boolean
  isSpeaking: boolean
  hasPermission: boolean
  voiceErrorMessage: string
  sessionStatus?: "loading" | "authenticated" | "unauthenticated"
  diagnosisInProgress?: boolean

  // Connection quality props
  connectionQuality?: "excellent" | "good" | "poor" | "unknown"
  isReconnecting?: boolean

  // Interaction mode props
  interactionMode: "direct" | "conversational"
  setInteractionMode: React.Dispatch<React.SetStateAction<"direct" | "conversational">>

  // Handler functions
  toggleMute: () => void
  handleEndConversation: () => Promise<void>
  handleStartConversation: () => Promise<void>
  setVoiceErrorMessage: React.Dispatch<React.SetStateAction<string>>

  // Script-related props
  isScriptLoading: boolean
  isScriptReady: boolean
  scriptName: string | null
}

function Rehearsals({
  // Connection state props
  apiConfigStatus,
  detailedErrorInfo,
  isListening,
  voiceStatus,
  isMuted,
  isSpeaking,
  hasPermission,
  voiceErrorMessage,
  sessionStatus = "authenticated",
  diagnosisInProgress = false,

  // Connection quality props
  connectionQuality = "unknown",
  isReconnecting = false,

  // Interaction mode props
  interactionMode,
  setInteractionMode,

  // Handler functions
  toggleMute,
  handleEndConversation,
  handleStartConversation,
  setVoiceErrorMessage,

  // Script-related props
  isScriptLoading,
  isScriptReady,
  scriptName,
}: RehearsalsProps) {
  // Local state for UI interactions
  const [showDetails, setShowDetails] = useState<boolean>(false)
  const [isProcessing, setIsProcessing] = useState<boolean>(false)
  const [voiceGender, setVoiceGender] = useState<"male" | "female">("male")

  // Voice style settings
  const [voiceTone, setVoiceTone] = useState<"neutral" | "professional" | "friendly" | "dramatic">("neutral")
  const [emotionalIntensity, setEmotionalIntensity] = useState<"low" | "medium" | "high">("medium")
  const [readingPace, setReadingPace] = useState<"slow" | "normal" | "fast">("normal")
  const [showVoiceSettings, setShowVoiceSettings] = useState<boolean>(false)

  // Load saved voice preferences from localStorage
  useEffect(() => {
    try {
      // Load gender preference
      const savedVoiceGender = localStorage.getItem("scriptReaderVoiceGender")
      if (savedVoiceGender === "male" || savedVoiceGender === "female") {
        setVoiceGender(savedVoiceGender as "male" | "female")
      }

      // Load tone preference
      const savedVoiceTone = localStorage.getItem("scriptReaderVoiceTone")
      if (
        savedVoiceTone === "neutral" ||
        savedVoiceTone === "professional" ||
        savedVoiceTone === "friendly" ||
        savedVoiceTone === "dramatic"
      ) {
        setVoiceTone(savedVoiceTone as "neutral" | "professional" | "friendly" | "dramatic")
      }

      // Load emotional intensity preference
      const savedEmotionalIntensity = localStorage.getItem("scriptReaderEmotionalIntensity")
      if (
        savedEmotionalIntensity === "low" ||
        savedEmotionalIntensity === "medium" ||
        savedEmotionalIntensity === "high"
      ) {
        setEmotionalIntensity(savedEmotionalIntensity as "low" | "medium" | "high")
      }

      // Load reading pace preference
      const savedReadingPace = localStorage.getItem("scriptReaderReadingPace")
      if (savedReadingPace === "slow" || savedReadingPace === "normal" || savedReadingPace === "fast") {
        setReadingPace(savedReadingPace as "slow" | "normal" | "fast")
      }
    } catch (error) {
      console.error("Error loading saved voice preferences:", error)
    }

    // Add event listeners for settings panel toggle and hide
    const handleToggleSettings = () => {
      setShowVoiceSettings(prev => !prev);
    };

    const handleHideSettings = () => {
      setShowVoiceSettings(false);
    };

    document.addEventListener("toggle-settings-panel", handleToggleSettings);
    document.addEventListener("hide-settings-panel", handleHideSettings);

    return () => {
      document.removeEventListener("toggle-settings-panel", handleToggleSettings);
      document.removeEventListener("hide-settings-panel", handleHideSettings);
    };
  }, [])

  // Function to get voice instructions based on settings
  const getVoiceInstructions = () => {
    // Tone instructions
    let toneInstructions = ""
    switch (voiceTone) {
      case "professional":
        toneInstructions = "Maintain a professional, authoritative tone throughout the reading."
        break
      case "friendly":
        toneInstructions = "Use a warm, friendly, and approachable tone throughout the reading."
        break
      case "dramatic":
        toneInstructions = "Read with dramatic flair and theatrical emphasis where appropriate."
        break
      default: // neutral
        toneInstructions = "Maintain a neutral, balanced tone throughout the reading."
    }

    // Emotional intensity instructions
    let emotionInstructions = ""
    switch (emotionalIntensity) {
      case "low":
        emotionInstructions = "Keep emotional expression subtle and restrained."
        break
      case "high":
        emotionInstructions = "Express emotions strongly and vividly when called for in the script."
        break
      default: // medium
        emotionInstructions = "Express emotions with moderate intensity when appropriate."
    }

    // Reading pace instructions
    let paceInstructions = ""
    switch (readingPace) {
      case "slow":
        paceInstructions = "Read at a slower, more deliberate pace to allow for clarity and emphasis."
        break
      case "fast":
        paceInstructions = "Read at a quicker pace while maintaining clarity."
        break
      default: // normal
        paceInstructions = "Read at a natural, conversational pace."
    }

    return `${toneInstructions} ${emotionInstructions} ${paceInstructions}`
  }

  // Handle connection start with loading state
  const startRehearsal = async () => {
    try {
      setIsProcessing(true)
      setVoiceErrorMessage("") // Clear any previous errors

      // Pass the selected voice to the parent component
      const selectedVoice = getVoiceName()
      console.log(`Starting rehearsal with ${voiceGender} voice: ${selectedVoice}`)

      // Get voice style instructions
      const voiceInstructions = getVoiceInstructions()
      console.log("Voice style instructions:", voiceInstructions)

      // Store all voice settings in localStorage for persistence
      localStorage.setItem("scriptReaderVoiceGender", voiceGender)
      localStorage.setItem("scriptReaderVoiceTone", voiceTone)
      localStorage.setItem("scriptReaderEmotionalIntensity", emotionalIntensity)
      localStorage.setItem("scriptReaderReadingPace", readingPace)

      // Create a voice settings object to pass to the parent component
      const voiceSettings: VoiceSettings = {
        gender: voiceGender,
        tone: voiceTone,
        emotionalIntensity: emotionalIntensity,
        readingPace: readingPace,
        instructions: voiceInstructions,
      }

      // Pass the voice settings to the parent component via a custom event
      console.log("Voice settings:", voiceSettings)

      // Create a custom event with the voice settings
      const voiceSettingsEvent = new CustomEvent("voice-settings-update", {
        detail: voiceSettings,
      })

      // Dispatch the event to be caught by the parent component
      document.dispatchEvent(voiceSettingsEvent)

      await handleStartConversation()
    } catch (error) {
      console.error("Failed to start rehearsal:", error)
      setVoiceErrorMessage(error instanceof Error ? error.message : String(error))
    } finally {
      setIsProcessing(false)
    }
  }

  // Handle connection end with loading state
  const endRehearsal = async () => {
    try {
      setIsProcessing(true)
      // Hide settings panel when ending rehearsal
      setShowVoiceSettings(false)
      await handleEndConversation()
    } catch (error) {
      console.error("Failed to end rehearsal:", error)
    } finally {
      setIsProcessing(false)
    }
  }

  // Determine button disable state and title
  const isStartDisabled =
    isProcessing || isScriptLoading || !isScriptReady || !hasPermission || apiConfigStatus === "connecting"
  let startButtonTitle = "Connect"
  if (!scriptName) startButtonTitle = "Select a script first"
  else if (!isScriptReady) startButtonTitle = "Script not ready or has no content"
  else if (!hasPermission) startButtonTitle = "Microphone access required"
  else if (apiConfigStatus === "connecting") startButtonTitle = "Connecting..."
  else if (isScriptLoading) startButtonTitle = "Script is loading..."

  // Function to get voice name based on gender selection
  const getVoiceName = () => {
    return voiceGender === "male" ? "echo" : "alloy"
  }

  // Render the component UI
  return (
    <div className="flex flex-col h-full">
      {/* Script info display - Simplified header */}
      <div className="bg-[#070706] rounded-lg p-3 md:p-4 mb-4">
        <div className="flex justify-between items-center">


          {/* Streamlined Controls */}
          <div className="flex items-center gap-2 flex-shrink-0">
            {/* Connection quality indicator - Compact */}
            {voiceStatus === "connected" && (
              <div
                className={`w-2 h-2 rounded-full ${
                  isReconnecting
                    ? "bg-white animate-pulse"
                    : connectionQuality === "excellent"
                      ? "bg-white"
                      : connectionQuality === "good"
                        ? "bg-gray-300"
                        : connectionQuality === "poor"
                          ? "bg-gray-500"
                          : "bg-gray-700"
                }`}
                title={`Connection quality: ${isReconnecting ? "Reconnecting..." : connectionQuality}`}
              />
            )}

            {/* End/Connect Button */}
            {voiceStatus === "connected" ? (
              <button
                onClick={endRehearsal}
                disabled={isProcessing}
                className="px-2.5 py-1.5 bg-white/80 hover:bg-white text-black rounded-md text-xs font-medium flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? (
                  <Loader className="w-3 h-3 mr-1.5 animate-spin" />
                ) : (
                  <StopCircle className="w-3 h-3 mr-1.5" />
                )}
                End Rehearsal
              </button>
            ) : null}
          </div>
        </div>

        {/* Script readiness indicator */}


        {/* Connection error message with improved display */}
        {voiceErrorMessage && (
          <div className="mt-3 bg-red-500/10 text-red-400 p-3 rounded-md text-xs flex items-start">
            <Info className="w-3.5 h-3.5 mt-0.5 mr-2 flex-shrink-0" />
            <div className="flex-1">
              <p className="font-medium">{voiceErrorMessage}</p>

              {/* Show troubleshooting tips based on error message */}
              <div className="mt-2 text-gray-300">
                {voiceErrorMessage.includes("WebRTC") && (
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Try using a different browser (Chrome or Edge recommended)</li>
                    <li>Check if your firewall is blocking WebRTC connections</li>
                    <li>Try disabling any VPN or proxy services</li>
                    <li>Ensure your microphone is properly connected</li>
                  </ul>
                )}

                {voiceErrorMessage.includes("Failed to start") && (
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Try refreshing the page and reconnecting</li>
                    <li>Check your internet connection</li>
                    <li>Make sure your microphone is enabled in browser settings</li>
                  </ul>
                )}
              </div>

              {detailedErrorInfo && (
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="text-purple-400 hover:text-purple-300 mt-2 underline text-xs inline-flex items-center"
                >
                  {showDetails ? "Hide technical details" : "Show technical details"}
                  <span className="ml-1 text-xs opacity-70">(for developers)</span>
                </button>
              )}
            </div>
          </div>
        )}

        {/* Detailed error info expansion */}
        {showDetails && detailedErrorInfo && (
          <div className="mt-2 p-2 bg-black/40 rounded-md border border-purple-500/20 text-xs font-mono text-gray-400 whitespace-pre-wrap overflow-auto max-h-32">
            {detailedErrorInfo}
          </div>
        )}
      </div>

      {/* Overlay when settings panel is open */}
      {showVoiceSettings && (
        <div
          className="fixed inset-0 bg-black/40 z-10 transition-opacity duration-300"
          onClick={() => setShowVoiceSettings(false)}
        />
      )}

      {/* Settings Panel - Positioned absolutely relative to the main container */}
      <div className="relative z-20">
        {/* Toggle Button for Settings Panel */}
        <button
          onClick={() => setShowVoiceSettings(!showVoiceSettings)}
          className={`fixed top-16 ${showVoiceSettings ? 'right-80' : 'right-0'} p-2 mt-5 bg-[#070706]/90 backdrop-blur-md border border-white/20 border-r-0 rounded-l-lg shadow-xl transition-all duration-300 ease-in-out text-white hover:bg-[#070706]/70`}
          title={showVoiceSettings ? "Hide Settings" : "Show Settings"}
        >
          <div className="flex items-center text-gray-400">
            {showVoiceSettings ? (
              <ChevronRight className="w-4 h-4 mr-2" />
            ) : (
              <ChevronLeft className="w-4 h-4 mr-2" />
            )}
            <span>Connect</span>
          </div>
        </button>

        <div
          className={`fixed top-16 right-0 w-80 bg-[#070706]/90 backdrop-blur-md border border-white/20 mt-4 rounded-l-lg shadow-xl transition-all duration-300 ease-in-out ${
            showVoiceSettings ? "translate-x-0" : "translate-x-full"
          }  `}
        >
          <div className="p-4 mt-4">
            {/* Panel Title */}
            <div className="flex justify-between items-center mb-4 border-b border-white/10 pb-2">
              <h2 className="text-sm font-medium text-white">Connection Status</h2>
            </div>

            {/* Status Indicators */}
            <div className="mb-4 space-y-2">
              {sessionStatus === "loading" && (
                <div className="status-badge bg-white/20 text-white w-full">
                  <Loader className="w-3 h-3 mr-1 animate-spin" />
                  Loading session...
                </div>
              )}
              {sessionStatus === "unauthenticated" && (
                <div className="status-badge bg-white/20 text-white w-full">
                  <Info className="w-3 h-3 mr-1" />
                  Not authenticated
                </div>
              )}
              {apiConfigStatus === "connecting" && (
                <div className="status-badge bg-white/20 text-xs text-white w-full">
                  <Loader className="w-3 h-3 inline-block mr-1 animate-spin" />
                  Connecting Voice...
                </div>
              )}
              {apiConfigStatus === "valid" && isListening && (
                <div className="status-badge text-xs text-white w-full">
                  <Mic className="w-3 h-3 inline-block mr-1" />
                  Connected
                </div>
              )}
              {apiConfigStatus === "invalid" && (
                <div className="status-badge bg-white/20 text-white w-full">
                  <Info className="w-3 h-3 mr-1" />
                  Voice Error
                </div>
              )}
              {diagnosisInProgress && (
                <div className="status-badge bg-white/20 text-xs text-white w-full">
                  <Loader className="w-3 h-3 mr-1 animate-spin inline-block" />
                  Diagnosing...
                </div>
              )}
            </div>

            {/* Connection Status Section */}
            {voiceStatus === "disconnected" && !isProcessing && !isScriptLoading && (
              <div className="mb-4 border-b border-white/10 pb-3">

                <div className="text-xs text-gray-300 mb-2">
                  Ready to connect with {scriptName ? `"${scriptName}"` : "a script"}?
                </div>
                <div className="text-xs text-gray-400 mb-2">
                  {
                    scriptName && isScriptReady && hasPermission
                      ? 'Press "Connect" to establish a connection.'
                      : !scriptName
                        ? "Select a script from the list."
                        : !isScriptReady
                          ? "The selected script needs to be ready."
                          : "Microphone access is needed."
                  }
                </div>
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-2 h-2 rounded-full mr-2 bg-red-500"></div>
                      <span className="text-xs text-red-400">Disconnected</span>
                    </div>

                    {/* Connect Button - Moved from main content area to settings panel */}
                    {scriptName && isScriptReady && hasPermission && (
                      <button
                        onClick={startRehearsal}
                        disabled={isStartDisabled}
                        className="px-2 py-1 bg-white/80 hover:bg-white text-black rounded-md text-xs font-medium flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        title={startButtonTitle}
                      >
                        {isProcessing || apiConfigStatus === "connecting" || isScriptLoading ? (
                          <Loader className="w-3 h-3 mr-1 animate-spin" />
                        ) : (
                          <Play className="w-3 h-3 mr-1" />
                        )}
                        Connect
                      </button>
                    )}
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full h-1 bg-gray-700/50 rounded-full overflow-hidden mt-2">
                    <div
                      className="h-full bg-red-500 transition-all duration-300"
                      style={{ width: "25%" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}

            {/* Microphone Controls */}
            {isListening && (
              <div className="mb-4 border-b border-white/10 pb-3">
                <h3 className="text-sm font-medium text-white mb-2">Microphone</h3>
                <button
                  onClick={toggleMute}
                  className={`px-3 py-1.5 rounded-md text-xs font-medium flex items-center transition-colors ${
                    isMuted
                      ? "bg-red-500/20 text-red-400 hover:bg-red-500/30"
                      : "bg-green-500/20 text-green-400 hover:bg-green-500/30"
                  }`}
                  title={isMuted ? "Unmute Microphone" : "Mute Microphone"}
                >
                  {isMuted ? <VolumeX className="w-3.5 h-3.5 mr-1.5" /> : <Volume2 className="w-3.5 h-3.5 mr-1.5" />}
                  {isMuted ? "Unmute Microphone" : "Mute Microphone"}
                </button>
              </div>
            )}

            <h3 className="text-sm font-medium text-white mb-4">Voice Control</h3>

            {/* Voice Gender Selection */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-gray-300">Voice gender:</span>
              </div>
              <div className="flex space-x-4">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="voiceGender"
                    value="female"
                    checked={voiceGender === "female"}
                    onChange={() => setVoiceGender("female")}
                    className="text-white focus:ring-white"
                  />
                  <span className="text-xs text-white">Female</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="voiceGender"
                    value="male"
                    checked={voiceGender === "male"}
                    onChange={() => setVoiceGender("male")}
                    className="text-white focus:ring-white"
                  />
                  <span className="text-xs text-white">Male</span>
                </label>
              </div>
            </div>

            {/* Tone selection */}
            <div className="mb-4">
              <label className="block text-xs text-gray-300 mb-1">Tone:</label>
              <select
                value={voiceTone}
                onChange={(e) =>
                  setVoiceTone(e.target.value as "neutral" | "professional" | "friendly" | "dramatic")
                }
                className="w-full bg-black/50 border border-white/20 text-white rounded px-2 py-1.5 text-xs"
              >
                <option value="neutral">Neutral</option>
                <option value="professional">Professional</option>
                <option value="friendly">Friendly</option>
                <option value="dramatic">Dramatic</option>
              </select>
            </div>

            {/* Emotional intensity selection */}
            <div className="mb-4">
              <label className="block text-xs text-gray-300 mb-1">Emotional intensity:</label>
              <select
                value={emotionalIntensity}
                onChange={(e) => setEmotionalIntensity(e.target.value as "low" | "medium" | "high")}
                className="w-full bg-black/50 border border-white/20 text-white rounded px-2 py-1.5 text-xs"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
              </select>
            </div>

            {/* Reading pace selection */}
            <div>
              <label className="block text-xs text-gray-300 mb-1">Reading pace:</label>
              <select
                value={readingPace}
                onChange={(e) => setReadingPace(e.target.value as "slow" | "normal" | "fast")}
                className="w-full bg-black/50 border border-white/20 text-white rounded px-2 py-1.5 text-xs"
              >
                <option value="slow">Slow</option>
                <option value="normal">Normal</option>
                <option value="fast">Fast</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className={`flex-1 bg-[#070706] rounded-lg p-4 overflow-y-auto transition-all duration-300 ${
        showVoiceSettings ? "filter blur-sm" : ""
      }`}>
        <div className="flex flex-col items-center justify-center h-full text-center text-gray-400">
          {isListening ? (
            // --- LISTENING STATE ---
            <div className="p-4">
              <div className="relative mb-4 flex justify-center">
                {/* Voice Wave Animations - Positioned outside the mic container */}
                {isListening && (
                  <>
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <div className="w-28 h-28 rounded-full bg-white/30 animate-voiceWave"></div>
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                      <div className="w-28 h-28 rounded-full bg-white/30 animate-voiceWave-delayed"></div>
                    </div>
                  </>
                )}

                {/* Mic container */}
                <div
                  className={`relative w-28 h-28 flex items-center justify-center rounded-full transition-colors duration-300 ${
                    isListening
                      ? "bg-white/30" // White background for listening state
                      : "bg-gray-500/20" // Should not happen if isListening is true, fallback
                  }`}
                >
                  <Mic
                    className={`w-12 h-12 transition-colors duration-300 ${isListening ? "text-white" : "text-gray-400"}`}
                  />
                </div>

                {/* Speaking Indicator (Visual Ring) */}
                {isSpeaking && (
                  <div className="absolute inset-0 border-8 border-white rounded-full pointer-events-none animate-pulse" />
                )}
              </div>

              <h3 className="text-lg font-medium text-white mb-1">
                {isSpeaking ? "Assistant is speaking..." : "Listening..."}
                {isReconnecting && <span className="text-yellow-400 text-sm ml-2">(Reconnecting...)</span>}
              </h3>
              <p className="text-sm max-w-md mx-auto">
                {isSpeaking
                  ? "Please wait while the assistant responds."
                  : "Speak your lines or give instructions. The assistant will respond accordingly."}
              </p>

              {/* Connection quality indicator */}
              {connectionQuality !== "unknown" && !isReconnecting && (
                <div className="mt-3 flex items-center justify-center">
                  <div className="flex items-center bg-black/30 px-3 py-1 rounded-full">
                    <div
                      className={`w-2 h-2 rounded-full mr-2 ${
                        connectionQuality === "excellent"
                          ? "bg-white"
                          : connectionQuality === "good"
                            ? "bg-gray-300"
                            : connectionQuality === "poor"
                              ? "bg-gray-500"
                              : "bg-gray-700"
                      }`}
                    />
                    <span className="text-xs">
                      Connection quality:
                      <span
                        className={`ml-1 font-medium ${
                          connectionQuality === "excellent"
                            ? "text-white"
                            : connectionQuality === "good"
                              ? "text-gray-300"
                              : connectionQuality === "poor"
                                ? "text-gray-500"
                                : "text-gray-400"
                        }`}
                      >
                        {connectionQuality}
                      </span>
                    </span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            // --- NOT LISTENING STATE ---
            <div className="px-20 py-3 flex -mt-19 flex-col items-center justify-center border border-white/20 rounded-2xl">
              {/* Logo in center */}
              <div className="mb-1">
                <img src="/Clogo4.png" alt="CASTMATE Logo" className="w-40 h-28" />
              </div>

              {/* CASTMATE title */}
              <h2 className="text-3xl font-bold text-white mb-2 -mt-5">CastMate</h2>

              {/* Script ready text */}
              <p className="text-green-400 text-sm mb-1">script ready for rehearsal</p>

              {/* Script name */}
              <p className="text-white text-sm mb-6">{scriptName || "myscript.pdf"}</p>

              {/* Choose interaction mode section */}
              <div className="bg-black/20 p-4 rounded-md border border-white/10 w-full max-w-xs">
                <div className="text-center mb-3 text-sm text-white">Choose interaction mode</div>
                <div className="flex justify-center space-x-4">
               <button
                  onClick={() => setInteractionMode("direct")}
                  className={`px-3 py-2 text-xs rounded-md transition-colors w-36 flex flex-col items-center justify-center h-16 ${
                    interactionMode === "direct"
                      ? "bg-white text-black"
                      : "bg-gray-700/50 text-gray-300 hover:bg-gray-600/50"
                  }`}
                >
                  <span className="font-medium mb-1">Direct</span>
                  <div className="text-xs opacity-70 text-center">Formal, minimal conversation</div>
                </button>
                <button
                  onClick={() => setInteractionMode("conversational")}
                  className={`px-3 py-2 text-xs rounded-md transition-colors w-36 flex flex-col items-center justify-center h-16 ${
                    interactionMode === "conversational"
                      ? "bg-white text-black"
                      : "bg-gray-700/50 text-gray-300 hover:bg-gray-600/50"
                  }`}
                >
                  <span className="font-medium mb-1">Conversational</span>
                  <div className="text-xs opacity-70 text-center">Informal, interactive</div>
                </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Global styles for status badges */}
      <style jsx global>{`
        .status-badge {
          @apply text-xs px-2 py-1 rounded-md flex items-center;
          font-size: 0.7rem;
          display: inline-flex;
          white-space: nowrap;
        }
        @media (min-width: 640px) {
          .status-badge {
            font-size: 0.75rem;
          }
        }
      `}</style>
    </div>
  )
}

export default Rehearsals
