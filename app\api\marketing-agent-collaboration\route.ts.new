/**
 * Marketing Agent Collaboration API
 * 
 * This API endpoint orchestrates collaboration between multiple agents to provide
 * comprehensive marketing strategy analysis and recommendations.
 * 
 * The workflow:
 * 1. Strategic Director analyzes the request and searches for relevant documents
 * 2. Question Answer Agent extracts specific information from documents
 * 3. Strategic Director synthesizes a final response
 */

import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { StrategicDirectorAgent } from '../../../lib/agents/marketing/StrategicDirectorAgent';
import { findInternalDocumentsTool } from '../../../lib/tools/findInternalDocumentsTool';
import { adminDb } from '../../../components/firebase/admin';
import { LlmProvider } from '../../../lib/tools/llm-tool';

// Define the request body interface
interface RequestBody {
  prompt: string;
  modelProvider?: string | LlmProvider;
  modelName?: string;
  context?: string;
  documentReferences?: string[];
  category?: string;
  userId?: string;
}

// Define the agent message interface
interface AgentMessage {
  from: string;
  to: string;
  message: string;
  thinking?: string;
  timestamp: Date;
}

export async function POST(req: Request) {
  try {
    // Parse the request body
    const body = await req.json();
    
    // Extract parameters from the request
    const {
      prompt,
      modelProvider = 'openai' as LlmProvider,
      modelName = 'gpt-4o',
    } = body;
    
    // Validate prompt
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      return NextResponse.json(
        { error: 'A valid prompt is required' },
        { status: 400 }
      );
    }
    
    // Get user ID from request or session
    let userId = body.userId || '<EMAIL>';
    
    console.log(`Processing marketing agent collaboration request for user ${userId}`);
    console.log(`Prompt: "${prompt}"`);
    console.log(`Model: ${modelProvider}/${modelName}`);
    
    // Initialize the conversation array
    const conversation: AgentMessage[] = [];
    
    // Create the Strategic Director Agent with the user's ID
    const strategicDirector = new StrategicDirectorAgent(
      'strategic-director',
      'Strategic Director',
      userId,
      modelProvider as LlmProvider,
      modelName
    );
    
    // Prepare document query options
    let queryOptions: any = {
      useInternetSearch: true // Use internet search as fallback
    };
    
    // Default to marketing category only if no specific context is provided
    if (!body.context && !body.documentReferences && !body.category) {
      queryOptions.category = 'marketing';
    }
    
    // Initialize document query
    let documentQuery = `Find information related to: ${prompt}`;
    
    // If a category is specified, use it
    if (body.category) {
      queryOptions.category = body.category;
      documentQuery = `Find information related to: "${prompt}" in the category "${body.category}".`;
      queryOptions.useVectorSearch = true;
    }
    
    // Add document query to conversation
    conversation.push({
      from: 'strategic-director',
      to: 'query-documents',
      message: documentQuery,
      timestamp: new Date()
    });
    
    // Execute the query
    let queryResult;
    
    // Use the standard approach
    queryResult = await strategicDirector.queryDocuments(
      documentQuery,
      queryOptions.category,
      undefined,
      queryOptions.useInternetSearch
    );
    
    // If we have documents or the query was successful, proceed with normal processing
    // Step 2: Strategic Director initial analysis
    console.log('Step 2: Strategic Director initial analysis');
    
    // Pass the context from QueryDocumentsAgent to the Strategic Director's thinking process
    const queryContext = queryResult.success ? queryResult.content : '';
    const strategicThinking = await strategicDirector.getThinking(prompt, queryContext);
    const initialResponse = await strategicDirector.processRequest(prompt, queryContext);
    
    conversation.push({
      from: 'strategic-director',
      to: 'user',
      message: initialResponse,
      thinking: strategicThinking,
      timestamp: new Date()
    });
    
    // Add the document query result to the conversation
    conversation.push({
      from: 'query-documents',
      to: 'strategic-director',
      message: queryResult.success
        ? `Found the following information:\n\n${queryResult.content}`
        : queryResult.content || 'No relevant documents found.',
      timestamp: new Date()
    });
    
    // Step 3: Strategic Director formulates questions
    console.log('Step 3: Strategic Director formulates questions');
    
    // Create a question context
    let questionContext = `
Based on the user request: "${prompt}" and the document search results, I need to answer some key questions.

${body.category ? `The documents are from the category "${body.category}".` : ''}

Please identify 3-5 key questions that would help extract the most relevant information, and then answer those questions based on the document content.
`;
    
    conversation.push({
      from: 'strategic-director',
      to: 'question-answer',
      message: questionContext,
      timestamp: new Date()
    });
    
    // Step 4: Question Answer Agent processes the request
    console.log('Step 4: Question Answer Agent processes the request');
    
    // Prepare context for question answering
    let context = queryResult.content;
    
    // Add custom context if provided
    if (body.context) {
      context = `${body.context}\n\n${context}`;
    }
    
    // Create the question answer request
    const questionResult = await strategicDirector.answerQuestion(
      prompt,
      context,
      queryOptions.category || 'marketing',
      {
        internetSearch: true
      }
    );
    
    // Add the questions and answers to the conversation
    if (questionResult.questions && questionResult.questions.length > 0) {
      questionResult.questions.forEach((qa: any, index: number) => {
        conversation.push({
          from: 'question-answer',
          to: 'strategic-director',
          message: `Question ${index + 1}: ${qa.question}\n\nAnswer: ${qa.answer || 'No answer found.'}`,
          timestamp: new Date(Date.now() + index * 1000) // Stagger timestamps for display
        });
      });
    }
    
    // Step 5: Strategic Director synthesizes final response
    console.log('Step 5: Strategic Director synthesizes final response');
    
    // Prepare final prompt with context information
    let contextInfo = '';
    if (body.context) {
      contextInfo = `\nUser-provided context: "${body.context}"`;
    } else if (body.documentReferences && body.documentReferences.length > 0) {
      contextInfo = `\nUser selected document(s): "${body.documentReferences.join('", "')}"`;
    } else if (body.category) {
      contextInfo = `\nUser selected category: "${body.category}"`;
    } else {
      contextInfo = `\nNo specific context was provided by the user.`;
    }
    
    const finalPrompt = `
Based on all the information gathered:
1. User request: "${prompt}"${contextInfo}
2. Document search results: ${queryResult.success
  ? queryResult.content.substring(0, 500) + '...'
  : 'No relevant documents found. ' + (queryResult.content || '')}
3. Questions and answers: ${JSON.stringify(questionResult.questions.map((qa: any) => ({ question: qa.question, answer: qa.answer })))}

Provide a comprehensive, strategic marketing response that addresses the user's request.
`;
    
    const finalThinking = await strategicDirector.getThinking(finalPrompt, '');
    const finalResponse = await strategicDirector.processRequest(finalPrompt, '');
    
    conversation.push({
      from: 'strategic-director',
      to: 'user',
      message: finalResponse,
      thinking: finalThinking,
      timestamp: new Date()
    });
    
    // Generate a unique request ID
    const requestId = uuidv4();
    
    // Return the result
    return NextResponse.json({
      requestId,
      conversation,
      modelInfo: {
        provider: modelProvider,
        model: modelName
      },
      contextOptions: {
        customContext: body.context || null,
        documentReferences: body.documentReferences || null,
        category: body.category || null
      }
    });
    
  } catch (error) {
    console.error('Error processing marketing agent collaboration request:', error);
    
    // Provide more detailed error information
    let errorMessage = 'Failed to process collaboration request';
    let errorDetails = error instanceof Error ? error.message : String(error);
    
    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
