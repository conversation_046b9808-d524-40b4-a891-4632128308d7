import { getApp, getApps, initializeApp, FirebaseApp } from "firebase/app";
import { getFirestore, Firestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getAuth } from "firebase/auth";
import { handleDomainAuthError, isDomainAuthError } from './domainAuth';

// Type for Firebase configuration
interface FirebaseConfiguration {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId?: string;
}

// Type for initialized services
interface FirebaseServices {
  app: FirebaseApp;
  db: Firestore;
}

// Firebase configuration
const firebaseConfig: FirebaseConfiguration = {
  apiKey: "AIzaSyAJF-vqQ6vm8oY5KOF-D06G_MsBrOhbIXg",
  authDomain: "indef2024-d11b5.firebaseapp.com",
  projectId: "indef2024-d11b5",
  storageBucket: "indef2024-d11b5.appspot.com",
  messagingSenderId: "598647748129",
  appId: "1:598647748129:web:656ca404c0ca21a1571560"
};

// Singleton pattern for Firebase services
let firebaseServices: FirebaseServices | null = null;

// Initialize Firebase with services
export const initializeFirebase = (): FirebaseServices => {
  if (firebaseServices) {
    return firebaseServices;
  }

  const apps = getApps();

  if (!apps.length) {
    try {
      const app = initializeApp(firebaseConfig);
      const db = getFirestore(app);
      firebaseServices = { app, db };
      return firebaseServices;
    } catch (error) {
      console.error('Error initializing Firebase:', error);
      throw new Error('Failed to initialize Firebase services');
    }
  }

  firebaseServices = {
    app: apps[0],
    db: getFirestore(apps[0])
  };

  return firebaseServices;
};

// Initialize app and services with error handling
let app;
try {
  app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();
} catch (error) {
  console.error('Error initializing Firebase app:', error);
  if (isDomainAuthError(error)) {
    handleDomainAuthError(error);
  }
  // Fallback to a dummy app to prevent the entire application from crashing
  app = getApps()[0] || initializeApp(firebaseConfig);
}

// Initialize Firestore with explicit settings
const firestoreSettings = {
  // Increase timeouts for production environment
  cacheSizeBytes: 1048576 * 100, // 100 MB cache size
  experimentalForceLongPolling: true, // Use long polling for better reliability
  ignoreUndefinedProperties: true, // Ignore undefined fields
  experimentalAutoDetectLongPolling: true, // Auto-detect best connection method
};

const db = getFirestore(app);

// Apply settings to Firestore
import { connectFirestoreEmulator, enableIndexedDbPersistence, enableMultiTabIndexedDbPersistence } from 'firebase/firestore';

// Only run this in the browser
if (typeof window !== 'undefined') {
  try {
    // Enable offline persistence when possible
    if (process.env.NODE_ENV === 'production') {
      console.log('Configuring Firestore for production environment');
      // For production, try multi-tab persistence first
      enableMultiTabIndexedDbPersistence(db).catch((err) => {
        if (err.code === 'failed-precondition') {
          // Multiple tabs open, fall back to single-tab persistence
          console.log('Multi-tab persistence not available, falling back to single-tab');
          enableIndexedDbPersistence(db).catch((err) => {
            console.error('Error enabling Firestore persistence:', err);
          });
        } else if (err.code === 'unimplemented') {
          console.warn('Firestore persistence is not available in this environment');
        } else {
          console.error('Unknown error enabling Firestore persistence:', err);
        }
      });
    } else {
      console.log('Configuring Firestore for development environment');
    }
  } catch (err) {
    console.error('Error configuring Firestore:', err);
  }
}

const storage = getStorage(app);
const auth = getAuth(app);

// Analytics Collections Configuration
export const ANALYTICS_COLLECTIONS = {
  QUERY_METRICS: 'query_metrics',
  TOKEN_METRICS: 'token_metrics',
  CHUNK_METRICS: 'chunk_metrics',
  CONTENT_METRICS: 'content_metrics'
} as const;

// Analytics Configuration
export const ANALYTICS_CONFIG = {
  BATCH_SIZE: 500,
  DEFAULT_RETENTION_DAYS: 30,
  MAX_QUERY_DURATION: 300000, // 5 minutes in milliseconds
  ERROR_RETRY_ATTEMPTS: 3,
  ERROR_RETRY_DELAY: 1000, // 1 second
} as const;

// Collection schemas for analytics
export const COLLECTION_SCHEMAS = {
  [ANALYTICS_COLLECTIONS.QUERY_METRICS]: {
    required: ['queryId', 'userId', 'timestamp', 'status'],
    indexes: ['userId', 'timestamp', 'status']
  },
  [ANALYTICS_COLLECTIONS.TOKEN_METRICS]: {
    required: ['queryId', 'totalTokens', 'timestamp'],
    indexes: ['queryId', 'timestamp']
  },
  [ANALYTICS_COLLECTIONS.CHUNK_METRICS]: {
    required: ['queryId', 'chunkId', 'timestamp'],
    indexes: ['queryId', 'timestamp', 'relevanceScore']
  },
  [ANALYTICS_COLLECTIONS.CONTENT_METRICS]: {
    required: ['queryId', 'totalTokens', 'timestamp'],
    indexes: ['queryId', 'timestamp']
  }
} as const;

// Error handler utility
export class CustomFirebaseError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly originalError?: any
  ) {
    super(message);
    this.name = 'CustomFirebaseError';
  }
}

// Retry utility for Firebase operations
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxAttempts: number = ANALYTICS_CONFIG.ERROR_RETRY_ATTEMPTS
): Promise<T> {
  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      if (attempt === maxAttempts) break;

      await new Promise(resolve =>
        setTimeout(resolve, ANALYTICS_CONFIG.ERROR_RETRY_DELAY * attempt)
      );
    }
  }

  throw new CustomFirebaseError(
    `Operation failed after ${maxAttempts} attempts`,
    'retry-exhausted',
    lastError
  );
}

// Helper function to get Firestore instance
export function getDb(): Firestore {
  return db;
}

// Server-side Firestore instance getter
export function getServerFirestore(): Firestore {
  if (typeof window === 'undefined') {
    return db;
  }
  throw new Error('Server-side Firestore instance requested in client context');
}

export { db, storage, auth };
