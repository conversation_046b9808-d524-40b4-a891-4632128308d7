/**
 * Utility functions to handle Firebase domain authorization issues
 */

import { FirebaseError } from 'firebase/app';
import { FirestoreError } from 'firebase/firestore';

/**
 * List of known domains that should be authorized in Firebase console
 */
export const knownDomains = [
  'localhost',
  'ike-ai.com',
  'www.ike-ai.com',
  'cheri-ai.com',
  'www.cheri-ai.com'
];

/**
 * Check if an error is related to domain authorization
 */
export function isDomainAuthError(error: any): boolean {
  // Check if it's a Firebase or Firestore error
  if (!(error instanceof FirebaseError) && !(error instanceof FirestoreError)) {
    // Also check for error code property which is common in Firebase errors
    if (!error || typeof error.code !== 'string') {
      return false;
    }
  }

  // Common Firebase error codes related to domain authorization
  const domainAuthErrorCodes = [
    'auth/unauthorized-domain',
    'auth/domain-not-allowed',
    'permission-denied',
    'PERMISSION_DENIED'
  ];

  return domainAuthErrorCodes.includes(error.code);
}

/**
 * Get helpful message for domain authorization errors
 */
export function getDomainAuthErrorMessage(error: any): string {
  if (!isDomainAuthError(error)) {
    return 'Unknown error';
  }

  const currentDomain = typeof window !== 'undefined' ? window.location.hostname : 'unknown';

  return `
    Firebase domain authorization error: ${error.message}

    Current domain "${currentDomain}" is not authorized in Firebase.

    To fix this issue:
    1. Go to the Firebase console (https://console.firebase.google.com/)
    2. Select your project "indef2024-d11b5"
    3. Go to Authentication > Settings > Authorized domains
    4. Add "${currentDomain}" to the list of authorized domains

    Known domains that should be authorized:
    ${knownDomains.map(domain => `- ${domain}`).join('\n')}
  `;
}

/**
 * Handle domain authorization errors
 */
export function handleDomainAuthError(error: any): void {
  if (isDomainAuthError(error)) {
    console.error(getDomainAuthErrorMessage(error));

    // Log additional information to help debugging
    console.error('Firebase error details:', {
      code: error.code,
      message: error.message,
      domain: typeof window !== 'undefined' ? window.location.hostname : 'unknown'
    });
  }
}
