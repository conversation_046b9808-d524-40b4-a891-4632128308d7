// Test script to verify agent ID resolution functionality
async function testAgentIdResolution() {
  try {
    console.log('Testing agent ID resolution...');
    
    const userGeneratedId = '<EMAIL>-pmo-investigativeresearch';
    const userId = '<EMAIL>';
    
    // Test the PMO agent existence check
    const response = await fetch('/api/elevenlabs/create-agent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        agentId: userGeneratedId,
        name: 'PMO Investigative Research Director - <EMAIL>',
        voiceId: 'test-voice-id',
        prompt: 'Test prompt',
        knowledgeBase: []
      })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to test agent creation: ${response.status}`);
    }
    
    const result = await response.json();
    console.log('Agent creation/resolution result:', result);
    
    if (result.wasExisting) {
      console.log('✅ Found existing agent:', result.agentId);
      console.log('✅ Agent name:', result.name);
    } else {
      console.log('✅ Created new agent:', result.agentId);
    }
    
    // Test listing agents to see if duplicates exist
    const listResponse = await fetch('/api/elevenlabs/create-agent?list=true&search=investigativeresearch', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (listResponse.ok) {
      const listData = await listResponse.json();
      console.log('Investigative research agents found:', listData.agents?.length || 0);
      
      if (listData.agents) {
        listData.agents.forEach((agent, index) => {
          console.log(`  ${index + 1}. ${agent.name} (ID: ${agent.agent_id})`);
        });
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test if this script is executed directly
if (typeof window !== 'undefined') {
  testAgentIdResolution();
}

console.log('Agent ID Resolution Test Script Loaded');
console.log('Run testAgentIdResolution() to test the functionality');
