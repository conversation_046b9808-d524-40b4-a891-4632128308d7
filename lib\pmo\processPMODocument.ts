/**
 * Process PMO Document
 *
 * This file contains functions for processing PMO documents and storing them in Firebase.
 * It handles chunking the document content and storing it in the files collection and byteStoreCollection.
 */

import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from '@langchain/openai';
import { FirestoreStore } from '../FirestoreStore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../components/firebase';
import { v4 as uuidv4 } from 'uuid';
import { getPineconeIndex } from '../pinecone-client';
import { pdfGeneratorTool, PdfContent } from '../tools/pdf-generator';

// Constants
const CHUNK_SIZE = 1000;
const CHUNK_OVERLAP = 200;

// Interface for document processing options
export interface ProcessPMODocumentOptions {
  title: string;
  content: string;
  pmoId: string;
  userId: string;
  category?: string;
  metadata?: Record<string, any>;
}

// Interface for document processing result
export interface ProcessPMODocumentResult {
  success: boolean;
  documentId?: string;
  downloadUrl?: string;
  error?: string;
}

/**
 * Clean metadata by removing undefined values
 */
function cleanMetadata(metadata: Record<string, any>): Record<string, any> {
  const cleanedMetadata: Record<string, any> = {};

  Object.entries(metadata).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      cleanedMetadata[key] = value;
    }
  });

  return cleanedMetadata;
}

/**
 * Process a PMO document and store it in Firebase
 */
export async function processPMODocument(options: ProcessPMODocumentOptions): Promise<ProcessPMODocumentResult> {
  try {
    // Generate a unique document ID
    const documentId = uuidv4();
    // Ensure the filename has a .pdf extension and is sanitized for storage
    const sanitizedTitle = options.title.replace(/[^a-zA-Z0-9_-]/g, '_').substring(0, 50);
    const fileName = `PMO_Requirements_${sanitizedTitle}_${options.pmoId}_${documentId}.pdf`;

    // Get the system admin email from environment or use default
    const sysAdmin = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';

    // Create PDF buffer from content
    const pdfBuffer = await createPDFBuffer(options.title, options.content);

    // Upload PDF to Firebase Storage with explicit PDF extension
    const storage = getStorage();
    const storagePath = `uploads/${sysAdmin}/${documentId}.pdf`;
    const storageRef = ref(storage, storagePath);

    // Upload with content type explicitly set to PDF
    await uploadBytes(storageRef, pdfBuffer, {
      contentType: 'application/pdf',
      customMetadata: {
        title: options.title,
        category: options.category || 'PMO',
        generatedBy: 'PMOAgent',
        pmoId: options.pmoId
      }
    });
    const downloadUrl = await getDownloadURL(storageRef);

    // Store metadata in files collection
    await setDoc(doc(db, `users/${sysAdmin}/files`, documentId), {
      category: options.category || 'PMO',
      createdAt: serverTimestamp(),
      downloadUrl,
      name: fileName,
      namespace: documentId,
      ref: storagePath, // Use the same path with .pdf extension
      size: pdfBuffer.length,
      type: 'application/pdf',
      title: options.title,
      generatedBy: 'PMOAgent',
      pmoId: options.pmoId,
      ...options.metadata
    });

    // Process content for byteStoreCollection
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: CHUNK_SIZE,
      chunkOverlap: CHUNK_OVERLAP,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });

    const textChunks = await textSplitter.createDocuments([options.content]);

    // Initialize Firestore store for byte storage
    const byteStoreCollection = `users/${sysAdmin}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteStoreCollection });

    // Initialize OpenAI embeddings
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY
    });

    // Initialize Pinecone index
    const pineconeIndex = await getPineconeIndex();

    // Process each chunk
    for (let i = 0; i < textChunks.length; i++) {
      const chunk = textChunks[i];
      const chunkId = `${documentId}_${i + 1}`;

      // Create metadata for this chunk
      const chunkMetadata = cleanMetadata({
        doc_id: documentId,
        chunk_id: chunkId,
        document_title: options.title,
        category: options.category || 'PMO',
        file_type: 'application/pdf',
        position: i + 1,
        total_chunks: textChunks.length,
        is_summary: i === 0, // First chunk is considered the summary
        source_url: downloadUrl,
        processed_at: new Date().toISOString(),
        pmoId: options.pmoId,
        ...options.metadata
      });

      // Store the raw chunk in Firestore
      await firestoreStore.mset([[chunkId, {
        pageContent: chunk.pageContent,
        metadata: chunkMetadata
      }]]);

      // Generate embedding for this chunk
      const embedding = await embeddings.embedQuery(chunk.pageContent);

      // Store the embedding in Pinecone
      await pineconeIndex.namespace(documentId).upsert([
        {
          id: chunkId,
          values: embedding,
          metadata: chunkMetadata
        }
      ]);
    }

    return {
      success: true,
      documentId,
      downloadUrl
    };
  } catch (error: any) {
    console.error('Error processing PMO document:', error);
    return {
      success: false,
      error: error.message || 'Failed to process PMO document'
    };
  }
}

/**
 * Create a PDF buffer from content
 * Uses the pdfGeneratorTool to create a PDF
 */
async function createPDFBuffer(title: string, content: string): Promise<Buffer> {
  try {
    // Create PDF content structure
    const pdfContent: PdfContent[] = [
      {
        title: title,
        content: content
      }
    ];

    // Generate PDF using the pdfGeneratorTool
    const pdfBuffer = await pdfGeneratorTool.generatePdf(pdfContent, {
      title: title,
      includeCover: true,
      includeToc: false,
      saveToByteStore: false // We'll handle storage ourselves
    });

    // The tool returns either a Buffer or a SavePdfToByteStoreResult
    // We need to ensure we're returning a Buffer
    if (Buffer.isBuffer(pdfBuffer)) {
      return pdfBuffer;
    } else {
      throw new Error('PDF generation returned a non-buffer result');
    }
  } catch (error) {
    console.error('Error creating PDF buffer:', error);

    // Fallback to creating a simple PDF using jsPDF if the PDF generator tool fails
    try {
      console.log('Attempting fallback PDF creation with jsPDF');
      const { jsPDF } = require('jspdf');
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(title, 20, 20);

      // Add content (simplified)
      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");

      // Split content into lines to fit on page
      const contentLines = doc.splitTextToSize(content, 170);
      doc.text(contentLines, 20, 30);

      // Get PDF as buffer
      const pdfArrayBuffer = doc.output("arraybuffer");
      return Buffer.from(pdfArrayBuffer);
    } catch (fallbackError) {
      console.error('Fallback PDF creation also failed:', fallbackError);
      // Last resort: return a text buffer with PDF header to force PDF mime type
      const pdfHeader = "%PDF-1.4\n";
      const pdfContent = `${pdfHeader}${title}\n\n${content}`;
      return Buffer.from(pdfContent);
    }
  }
}
