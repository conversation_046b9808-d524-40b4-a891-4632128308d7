import OpenAI from "openai";

export interface WebSearchOptions {
  prompt: string;
  model?: string;
  searchContextSize?: "low" | "medium" | "high";
  userLocation?: {
    country?: string;
    city?: string;
    region?: string;
    timezone?: string;
  };
  forceWebSearch?: boolean;
}

export interface WebSearchResult {
  text: string;
  citations: {
    url: string;
    title: string;
    startIndex: number;
    endIndex: number;
  }[];
  searchCallId?: string;
}

/**
 * Check if the OpenAI API key is configured
 * @returns True if the API key is configured, false otherwise
 */
function isApiKeyConfigured(): boolean {
  const apiKey = process.env.OPENAI_API_KEY;
  return !!apiKey && apiKey.length > 0;
}

/**
 * Process a prompt with OpenAI's web search capability
 * @param options - Web search options
 * @returns The generated content with web search results and citations
 */
export async function processWithWebSearch(options: WebSearchOptions): Promise<WebSearchResult> {
  try {
    const {
      prompt,
      model = "gpt-4o-search-preview",
      searchContextSize = "medium",
      userLocation,
      forceWebSearch = false,
    } = options;

    console.log(`Processing with OpenAI web search using model: ${model}`);

    // Check if API key is configured
    if (!isApiKeyConfigured()) {
      throw new Error("OpenAI API key is not configured. Please set the OPENAI_API_KEY environment variable.");
    }

    // Initialize the OpenAI client with explicit API key from environment
    const openaiClient = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY || '',
    });

    // Configure a simple web search tool
    const webSearchTool: any = {
      type: "function",
      function: {
        name: "search_web",
        description: "Search the web for current information",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The search query to look up on the web"
            }
          },
          required: ["query"]
        }
      }
    };

    // Prepare the API request with corrected format
    const requestOptions: any = {
      model: model,
      messages: [{ role: "user", content: prompt }],
      tools: [webSearchTool],
    };

    // Add user location if provided
    if (userLocation && Object.keys(userLocation).length > 0) {
      // Instead of modifying the tool parameters, we'll include location in the prompt
      const locationInfo = `\n\nUser location: ${userLocation.city || ''} ${userLocation.region || ''} ${userLocation.country || ''} ${userLocation.timezone || ''}`;

      // Update the messages with location information
      requestOptions.messages[0].content += locationInfo;
    }

    if (forceWebSearch) {
      requestOptions.tool_choice = {
        type: "function",
        function: { name: "search_web" }
      };
    }

    // Add search context size as a system message
    if (searchContextSize) {
      requestOptions.messages.unshift({
        role: "system",
        content: `When searching the web, use ${searchContextSize} context size to get ${searchContextSize === 'high' ? 'comprehensive' : searchContextSize === 'medium' ? 'balanced' : 'quick'} results.`
      });
    }

    // Make the API call using the correct method
    let response;
    try {
      response = await openaiClient.chat.completions.create(requestOptions);
      console.log("OpenAI response:", JSON.stringify(response, null, 2));
    } catch (apiError: any) {
      // Check if the error is related to web search not being available
      if (apiError.message) {
        console.warn(`API error: ${apiError.message}. Falling back to regular completion`);
        // Remove web search specific options and try again
        const fallbackOptions = { ...requestOptions };
        delete fallbackOptions.tools;
        delete fallbackOptions.tool_choice;

        // Keep only the user message for simplicity
        fallbackOptions.messages = [{
          role: "user",
          content: `Please search the web for information about: ${prompt}`
        }];

        try {
          response = await openaiClient.chat.completions.create(fallbackOptions);
          console.log("OpenAI fallback response:", JSON.stringify(response, null, 2));
        } catch (fallbackError: any) {
          console.error("Fallback also failed:", fallbackError.message);
          throw fallbackError;
        }
      } else {
        // Re-throw other errors
        throw apiError;
      }
    }

    // Extract the output text from the first choice
    let outputText = response.choices[0].message.content || "";

    // Initialize result variables
    const citations: Array<{
      url: string;
      title: string;
      startIndex: number;
      endIndex: number;
    }> = [];
    let searchCallId: string | undefined;

    // Extract search call ID if tool calls are present
    if (response.choices[0].message.tool_calls && Array.isArray(response.choices[0].message.tool_calls)) {
      // Just use the first tool call ID as the search call ID
      if (response.choices[0].message.tool_calls.length > 0) {
        searchCallId = response.choices[0].message.tool_calls[0].id;
      }
    }

    // Extract citations from the text using regex
    // Look for patterns like [1]: https://example.com or [Source 1](https://example.com)
    const urlRegex = /\[(\d+|Source \d+)\](?:\: |\()?(https?:\/\/[^\s\)]+)/g;
    let match;

    while ((match = urlRegex.exec(outputText)) !== null) {
      const startIndex = match.index;
      const endIndex = startIndex + match[0].length;
      const url = match[2];

      // Try to extract title from nearby text
      let title = url;
      const titleMatch = outputText.substring(endIndex, endIndex + 100).match(/["']([^"']+)["']/);
      if (titleMatch) {
        title = titleMatch[1];
      }

      citations.push({
        url,
        title,
        startIndex,
        endIndex
      });
    }

    // Provide a fallback if no text is returned
    const finalText =
      outputText || "The web search did not return any results. Please try a different query or check your API configuration.";

    return {
      text: finalText,
      citations,
      searchCallId,
    };
  } catch (error: any) {
    console.error("Error processing content with OpenAI web search:", error);

    // Provide more detailed error information
    let errorMessage = "Unknown error";

    if (error.message) {
      errorMessage = error.message;
    }

    if (error.response) {
      // Extract API error details if available
      const statusCode = error.response.status;
      const responseData = error.response.data;

      errorMessage = `API Error (${statusCode}): ${JSON.stringify(responseData)}`;
    }

    // Check for common issues
    if (errorMessage.includes("authentication") || errorMessage.includes("api key")) {
      errorMessage = "OpenAI API key is invalid or missing. Please check your environment variables.";
    } else if (errorMessage.includes("rate limit")) {
      errorMessage = "OpenAI API rate limit exceeded. Please try again later.";
    } else if (errorMessage.includes("billing")) {
      errorMessage = "OpenAI API billing issue. Please check your account.";
    }

    throw new Error(`Error from OpenAI web search: ${errorMessage}`);
  }
}