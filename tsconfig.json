{"compilerOptions": {"target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "plugins": [{"name": "next"}], "paths": {"@/*": ["./app/*"], "@components/*": ["./app/components/*"], "@providers/*": ["./app/providers/*"], "@utils/*": ["./lib/utils/*"]}}, "include": ["./pinecone-extensions.d.ts", "next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "lib/pdf-generator.js", "lib/utils/enhancedMemoryManager.ts"], "exclude": ["node_modules"]}