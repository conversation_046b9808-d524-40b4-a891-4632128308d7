import React from 'react';
import { X, Download } from 'lucide-react';

interface ImageModalProps {
  image: {
    url: string;
    description: string;
    originalPrompt?: string;
  };
  onClose: () => void;
}

const ImageModal: React.FC<ImageModalProps> = ({ image, onClose }) => {
  // Function to force download an image to local drive
  const forceDownload = (e: React.MouseEvent, imageUrl: string) => {
    e.preventDefault();

    // Extract a filename from the URL
    const urlParts = imageUrl.split('/');
    let filename = urlParts[urlParts.length - 1].split('?')[0]; // Remove query parameters

    // If filename doesn't have an extension or has .png, change to .jpg
    if (!filename.includes('.') || filename.endsWith('.png')) {
      // Remove .png extension if present
      if (filename.endsWith('.png')) {
        filename = filename.substring(0, filename.length - 4);
      }
      filename += '.jpg';
    } else if (!filename) {
      filename = 'image.jpg';
    }

    // Create a new XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('GET', imageUrl, true);
    xhr.responseType = 'blob';

    xhr.onload = function() {
      if (this.status === 200) {
        // Create a blob from the response with JPEG MIME type
        const blob = new Blob([this.response], { type: 'image/jpeg' });

        // Create a temporary URL for the blob
        const url = window.URL.createObjectURL(blob);

        // Create a temporary link element
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;

        // Append to body, click, and remove
        document.body.appendChild(link);
        link.click();

        // Clean up
        setTimeout(() => {
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }, 100);
      }
    };

    xhr.send();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-4 max-w-3xl w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold text-gray-800">Image Details</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={24} />
          </button>
        </div>

        {/* Image container with download button positioned above */}
        <div className="relative mt-6">
          {/* Download button centered above the image */}
          <div className="absolute -top-5 left-0 right-0 flex justify-center">
            <button
              onClick={(e) => forceDownload(e, image.url)}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-10 font-medium"
              aria-label="Download image"
            >
              <Download size={18} />
              Download
            </button>
          </div>

          <img src={image.url} alt={image.originalPrompt || image.description} className="w-full h-auto max-h-[70vh] object-contain mb-4" />
        </div>
        <p className="text-gray-600">{image.originalPrompt || image.description}</p>
      </div>
    </div>
  );
};

export default ImageModal;

