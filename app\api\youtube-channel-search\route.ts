import { NextRequest, NextResponse } from 'next/server';

/**
 * API endpoint to search for YouTube channels by name
 */
export async function GET(request: NextRequest) {
  // Get query parameters
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('q');
  const maxResults = searchParams.get('maxResults') || '5';

  // Validate query parameter
  if (!query) {
    return NextResponse.json(
      { error: 'Missing query parameter', details: 'A search query (q) is required' },
      { status: 400 }
    );
  }

  try {
    // Get API key from environment variables
    const apiKey = process.env.YOUTUBE_API_KEY;

    if (!apiKey) {
      console.warn('YouTube API key not found in environment variables');
      return NextResponse.json(
        { error: 'API key not configured', details: 'YouTube API key is missing' },
        { status: 500 }
      );
    }

    // Search for channels
    const searchApiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(query)}&maxResults=${maxResults}&key=${apiKey}&type=channel`;

    // Make the request to the YouTube Search API with a referrer header
    const searchResponse = await fetch(searchApiUrl, {
      headers: {
        'Referer': process.env.NODE_ENV === 'development'
          ? 'http://localhost:3000'
          : 'https://ike-ai.com'
      }
    });

    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error('YouTube Channel Search API error:', searchResponse.status, errorText);

      // Check for specific error types and provide helpful messages
      let errorMessage = `YouTube API error: ${searchResponse.status}`;
      let errorDetails = errorText;

      try {
        // Try to parse the error response for more details
        const errorData = JSON.parse(errorText);

        // Check for referrer blocked error
        if (errorData.error?.errors?.some((e: any) => e.reason === "forbidden" && e.message.includes("referer"))) {
          errorMessage = "Requests from this referrer are blocked";
          errorDetails = "Your YouTube API key has HTTP referrer restrictions. You need to configure your API key to allow requests from this domain.";
        }
        // Check for quota exceeded
        else if (errorData.error?.errors?.some((e: any) => e.reason === "quotaExceeded")) {
          errorMessage = "YouTube API quota exceeded";
          errorDetails = "Your daily quota for the YouTube API has been exceeded. Please try again tomorrow.";
        }
      } catch (parseError) {
        // If we can't parse the error, just use the original error text
        console.error('Error parsing YouTube API error response:', parseError);
      }

      console.error('YouTube API error occurred:', errorMessage);

      return NextResponse.json(
        { error: errorMessage, details: errorDetails },
        { status: searchResponse.status }
      );
    }

    // Parse the search response
    const searchData = await searchResponse.json();

    // Transform the response to a simpler format
    const channels = searchData.items.map((item: any) => ({
      id: item.id.channelId,
      title: item.snippet.title,
      description: item.snippet.description,
      thumbnailUrl: item.snippet.thumbnails.default?.url || '',
      publishedAt: item.snippet.publishedAt
    }));

    return NextResponse.json({ channels });
  } catch (error) {
    console.error('Error searching for YouTube channels:', error);

    return NextResponse.json(
      { error: 'Failed to search for YouTube channels', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
