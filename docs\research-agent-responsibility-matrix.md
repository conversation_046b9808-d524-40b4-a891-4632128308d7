# Research Agent Responsibility Matrix

## Overview
This document defines the clear separation of responsibilities between the ResearchLeadAgent (coordinator/delegator) and specialist research agents (execution specialists). The ResearchLead focuses on strategic oversight, coordination, and quality assurance while delegating hands-on research work to appropriate specialist agents.

## ResearchLeadAgent - Leadership & Coordination Role

### PRIMARY RESPONSIBILITIES
- **Strategic Oversight**: High-level research strategy and direction
- **Task Decomposition**: Breaking complex research into manageable subtasks
- **Agent Coordination**: Assigning tasks to appropriate specialist agents
- **Quality Assurance**: Validating outputs and ensuring research standards
- **Cross-Team Communication**: Interfacing with other agentic teams (Ag001-Ag005)
- **PMO Integration**: Coordinating with PMO systems and strategic planning
- **Research Type Classification**: Using Gemini 2.5 Pro to determine research methodology
- **Progress Monitoring**: Tracking task completion and managing dependencies
- **Stakeholder Management**: Communicating with users and other system components

### DELEGATION PATTERNS
- **Chart Generation** → DataAnalystSynthesizerAgent
- **PMO Document Analysis** → InformationRetrievalAgent  
- **Strategic Task Creation** → DataAnalystSynthesizerAgent
- **Information Gathering** → InformationRetrievalAgent
- **Data Analysis** → DataAnalystSynthesizerAgent
- **Report Writing** → ReportWriterFormatterAgent
- **Quality Review** → QualityAssuranceReviewerAgent

### METHODS THAT DELEGATE (NOT EXECUTE)
```typescript
// Coordination methods - ResearchLead orchestrates
- determineResearchRequestType() // Uses Gemini 2.5 Pro for classification
- analyzeTask() // Determines team assignments
- decomposeTask() // Breaks down into subtasks
- assignAndMonitorTasks() // Delegates and tracks
- processQaFeedback() // Manages quality cycles

// Delegated methods - ResearchLead coordinates, specialists execute
- generateResearchCharts() // → DataAnalystSynthesizerAgent
- retrievePMOTasks() // → InformationRetrievalAgent
- createStrategicPlanningTasks() // → DataAnalystSynthesizerAgent
```

## Specialist Agent Responsibilities

### InformationRetrievalAgent - Data Collection Specialist

#### PRIMARY RESPONSIBILITIES
- **Web Search & Retrieval**: Finding information from online sources
- **Academic Database Search**: Accessing scholarly articles and papers
- **Document Analysis**: Analyzing PMO documents and internal files
- **Source Evaluation**: Assessing reliability and credibility of sources
- **Data Extraction**: Extracting structured information from documents
- **Citation Management**: Maintaining proper source attribution

#### RESEARCH STUDY TYPES HANDLED
- **User Research Studies**: Gathering user feedback, survey data, interview transcripts
- **Market Research**: Industry analysis, competitor intelligence, market sizing
- **Competitive Analysis**: Competitor research, feature comparison, positioning analysis
- **Technical Research**: Technology assessment, documentation review
- **PMO Document Analysis**: Project documentation, requirements extraction

#### NEW DELEGATED CAPABILITIES
```typescript
- analyzePMODocuments() // Delegated from ResearchLeadAgent
- handleTask() // Enhanced to handle delegation requests
```

### DataAnalystSynthesizerAgent - Analysis & Synthesis Specialist

#### PRIMARY RESPONSIBILITIES
- **Data Analysis**: Statistical analysis, pattern identification, trend analysis
- **Information Synthesis**: Combining multiple sources into coherent insights
- **Visualization Creation**: Generating charts, graphs, and visual representations
- **Strategic Task Planning**: Creating detailed task breakdowns and workflows
- **Insight Generation**: Transforming raw data into actionable recommendations
- **Methodology Design**: Developing research approaches and frameworks

#### RESEARCH STUDY TYPES HANDLED
- **Feasibility Studies**: Technical feasibility, market viability, resource assessment
- **Algorithmic Exploration**: Algorithm research, computational methods analysis
- **Experimental Design**: A/B testing frameworks, statistical design, hypothesis testing
- **Quantitative Data Analysis**: Statistical modeling, metric calculation, data mining
- **Insight Synthesis**: Cross-data synthesis, strategic recommendations
- **Strategic Planning**: Task breakdown, workflow design, resource allocation

#### NEW DELEGATED CAPABILITIES
```typescript
- generateCharts() // Delegated from ResearchLeadAgent
- createStrategicTasks() // Delegated from ResearchLeadAgent
- handleTask() // Enhanced to handle multiple delegation types
```

### ReportWriterFormatterAgent - Documentation Specialist

#### PRIMARY RESPONSIBILITIES
- **Report Creation**: Writing comprehensive research reports
- **Document Formatting**: Professional formatting and presentation
- **Content Organization**: Structuring information for clarity and impact
- **Executive Summaries**: Creating concise summaries for stakeholders
- **Presentation Materials**: Developing slides and visual presentations

#### RESEARCH STUDY TYPES HANDLED
- **Qualitative Data Analysis**: Interview analysis, thematic coding, narrative synthesis
- **Research Documentation**: Methodology documentation, process recording
- **Stakeholder Communication**: Reports tailored for different audiences

### QualityAssuranceReviewerAgent - Quality Control Specialist

#### PRIMARY RESPONSIBILITIES
- **Methodology Review**: Validating research approaches and methods
- **Accuracy Verification**: Fact-checking and source validation
- **Completeness Assessment**: Ensuring all requirements are met
- **Standards Compliance**: Maintaining research quality standards
- **Bias Detection**: Identifying potential biases in analysis

## Research Request Type Classification

### Using Gemini 2.5 Pro for Advanced Classification
The ResearchLeadAgent uses `determineResearchRequestType()` with Gemini 2.5 Pro to classify research requests:

#### RESEARCH TYPE CATEGORIES
1. **USER_RESEARCH**: User interviews, surveys, behavioral analysis → InformationRetrievalAgent
2. **FEASIBILITY_STUDY**: Technical/market viability assessment → DataAnalystSynthesizerAgent  
3. **ALGORITHMIC_EXPLORATION**: Algorithm research, computational methods → DataAnalystSynthesizerAgent
4. **EXPERIMENTAL_DESIGN**: A/B testing, hypothesis testing → DataAnalystSynthesizerAgent
5. **QUALITATIVE_ANALYSIS**: Interview analysis, thematic coding → ReportWriterFormatterAgent
6. **QUANTITATIVE_ANALYSIS**: Statistical analysis, data modeling → DataAnalystSynthesizerAgent
7. **INSIGHT_SYNTHESIS**: Strategic recommendations, actionable insights → DataAnalystSynthesizerAgent
8. **MARKET_RESEARCH**: Industry analysis, competitive landscape → InformationRetrievalAgent
9. **COMPETITIVE_ANALYSIS**: Competitor research, positioning → InformationRetrievalAgent
10. **TECHNICAL_RESEARCH**: Technology assessment, system analysis → InformationRetrievalAgent

## Delegation Workflow Patterns

### 1. Standard Research Workflow
```
ResearchLead → InformationRetrievalAgent → DataAnalystSynthesizerAgent → ReportWriterFormatterAgent → QualityAssuranceReviewerAgent
```

### 2. Chart Generation Workflow
```
ResearchLead.generateResearchCharts() → DataAnalystSynthesizerAgent.generateCharts() → Return to ResearchLead
```

### 3. PMO Analysis Workflow  
```
ResearchLead.retrievePMOTasks() → InformationRetrievalAgent.analyzePMODocuments() → Return to ResearchLead
```

### 4. Strategic Planning Workflow
```
ResearchLead.createStrategicPlanningTasks() → DataAnalystSynthesizerAgent.createStrategicTasks() → Return to ResearchLead
```

## Quality Assurance & Validation

### ResearchLead Validation Points
- **Task Assignment Validation**: Ensuring appropriate agent selection
- **Progress Monitoring**: Tracking completion and quality metrics
- **Output Validation**: Reviewing specialist agent deliverables
- **Integration Coordination**: Ensuring outputs work together cohesively

### Specialist Agent Quality Standards
- **InformationRetrievalAgent**: Source reliability, citation accuracy, data completeness
- **DataAnalystSynthesizerAgent**: Analysis methodology, statistical validity, insight quality
- **ReportWriterFormatterAgent**: Clarity, organization, professional presentation
- **QualityAssuranceReviewerAgent**: Comprehensive review, bias detection, standards compliance

## Implementation Status

### ✅ COMPLETED
- Delegation infrastructure in ResearchLeadAgent
- Enhanced DataAnalystSynthesizerAgent with chart generation and strategic planning
- Enhanced InformationRetrievalAgent with PMO document analysis
- Research request type classification using Gemini 2.5 Pro
- Clear responsibility separation between coordination and execution

### 🔄 IN PROGRESS
- Full integration testing of delegation workflows
- Enhanced error handling and fallback mechanisms
- Performance optimization for delegation patterns

### 📋 FUTURE ENHANCEMENTS
- Real-time agent communication protocols
- Advanced quality metrics and monitoring
- Dynamic agent selection based on workload and expertise
- Cross-agent collaboration optimization
