#!/usr/bin/env node

/**
 * Debug PMO Data Consistency Script
 *
 * This script helps debug and validate data consistency between:
 * 1. Task metadata displayed in the UI
 * 2. Actual Firebase subcollection structure
 * 3. Legacy PMO arrays
 *
 * Usage:
 *   node scripts/debug-pmo-data-consistency.js [userId] [pmoId] [projectId] [taskId]
 *   node scripts/debug-pmo-data-consistency.js validate-all [userId] [pmoId]
 */

const { adminDb } = require('../components/firebase-admin');
const {
  getHierarchicalPMOData,
  validateTaskMetadata,
  getProjectTask,
  getProjectTasks
} = require('../lib/firebase/pmoHierarchical');

// Configuration
const DEFAULT_USER_ID = '<EMAIL>';

/**
 * Debug specific task metadata consistency
 */
async function debugTaskConsistency(userId, pmoId, projectId, taskId) {
  console.log('\n🔍 DEBUGGING TASK METADATA CONSISTENCY');
  console.log('=====================================');
  console.log(`User ID: ${userId}`);
  console.log(`PMO ID: ${pmoId}`);
  console.log(`Project ID: ${projectId}`);
  console.log(`Task ID: ${taskId}`);
  console.log('');

  try {
    // 1. Check if task exists in specified subcollection location using getProjectTask
    console.log('1️⃣ Checking subcollection location using getProjectTask...');
    const taskResult = await getProjectTask(userId, pmoId, projectId, taskId);

    if (taskResult.success && taskResult.task) {
      console.log(`✅ Task found in subcollection: users/${userId}/PMO/${pmoId}/projects/${projectId}/tasks/${taskId}`);
      console.log(`   Title: "${taskResult.task.title}"`);
      console.log(`   Status: ${taskResult.task.status}`);
      console.log(`   Created: ${taskResult.task.createdAt?.toDate?.() || taskResult.task.createdAt}`);
    } else {
      console.log(`❌ Task NOT found in specified subcollection location`);
      console.log(`   Error: ${taskResult.error}`);
    }

    // 1b. Also check using getProjectTasks for comprehensive validation
    console.log('\n1️⃣b Checking using getProjectTasks for comprehensive validation...');
    const projectTasksResult = await getProjectTasks(userId, pmoId, projectId, true);

    if (projectTasksResult.success && projectTasksResult.tasks) {
      console.log(`✅ Project tasks retrieved: ${projectTasksResult.tasks.length} tasks found`);
      const foundTask = projectTasksResult.tasks.find(task => task.id === taskId);

      if (foundTask) {
        console.log(`✅ Target task found in getProjectTasks result`);
        console.log(`   Title: "${foundTask.title}"`);
        console.log(`   Status: ${foundTask.status}`);
      } else {
        console.log(`❌ Target task NOT found in getProjectTasks result`);
        console.log(`   Available tasks: ${projectTasksResult.tasks.map(t => `${t.id}:${t.title}`).slice(0, 3).join(', ')}`);
      }
    } else {
      console.log(`❌ Failed to get project tasks: ${projectTasksResult.error}`);
    }

    // 2. Check legacy global tasks collection
    console.log('\n2️⃣ Checking legacy global tasks collection...');
    try {
      const globalTaskDoc = await adminDb.collection('tasks').doc(taskId).get();
      if (globalTaskDoc.exists) {
        const globalTaskData = globalTaskDoc.data();
        console.log(`✅ Task found in global collection: tasks/${taskId}`);
        console.log(`   Title: "${globalTaskData.title}"`);
        console.log(`   Project ID: ${globalTaskData.projectId}`);
        console.log(`   Status: ${globalTaskData.status}`);

        if (globalTaskData.projectId !== projectId) {
          console.log(`⚠️  PROJECT ID MISMATCH: Global collection shows projectId="${globalTaskData.projectId}", but metadata shows "${projectId}"`);
        }
      } else {
        console.log(`❌ Task NOT found in global tasks collection`);
      }
    } catch (error) {
      console.log(`❌ Error checking global tasks collection: ${error.message}`);
    }

    // 3. Check PMO legacy arrays
    console.log('\n3️⃣ Checking PMO legacy arrays...');
    try {
      const pmoDoc = await adminDb
        .collection('users')
        .doc(userId)
        .collection('PMO')
        .doc(pmoId)
        .get();

      if (pmoDoc.exists) {
        const pmoData = pmoDoc.data();
        const projectIds = pmoData?.projectIds || [];
        const taskIds = pmoData?.taskIds || [];

        console.log(`PMO Document found with ${projectIds.length} projects and ${taskIds.length} tasks`);
        console.log(`Project IDs: ${projectIds.slice(0, 3).join(', ')}${projectIds.length > 3 ? '...' : ''}`);
        console.log(`Task IDs: ${taskIds.slice(0, 3).join(', ')}${taskIds.length > 3 ? '...' : ''}`);

        const projectInArray = projectIds.includes(projectId);
        const taskInArray = taskIds.includes(taskId);

        console.log(`Project ${projectId} in legacy array: ${projectInArray ? '✅' : '❌'}`);
        console.log(`Task ${taskId} in legacy array: ${taskInArray ? '✅' : '❌'}`);
      } else {
        console.log(`❌ PMO document not found`);
      }
    } catch (error) {
      console.log(`❌ Error checking PMO legacy arrays: ${error.message}`);
    }

    // 4. Check hierarchical subcollection structure
    console.log('\n4️⃣ Checking hierarchical subcollection structure...');
    const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);

    if (hierarchicalResult.success && hierarchicalResult.data) {
      console.log(`✅ Hierarchical structure found with ${hierarchicalResult.data.length} projects`);

      let foundInHierarchy = false;
      // Iterate through hierarchical data (HierarchicalPMOData[])
    for (const project of hierarchicalResult.data) {
        console.log(`   Project ${project.projectId}: ${project.taskIds.length} tasks`);

        if (project.projectId === projectId) {
          console.log(`   ✅ Target project found in hierarchy`);
          if (project.taskIds.includes(taskId)) {
            console.log(`   ✅ Target task found in project's task list`);
            foundInHierarchy = true;
          } else {
            console.log(`   ❌ Target task NOT found in project's task list`);
            console.log(`   Available tasks: ${project.taskIds.slice(0, 5).join(', ')}${project.taskIds.length > 5 ? '...' : ''}`);
          }
        }
      }

      if (!foundInHierarchy) {
        console.log(`❌ Task ${taskId} not found in hierarchical structure for project ${projectId}`);
      }
    } else {
      console.log(`❌ No hierarchical structure found: ${hierarchicalResult.error}`);
    }

    // 5. Validate metadata consistency
    console.log('\n5️⃣ Validating metadata consistency...');
    const validationResult = await validateTaskMetadata(userId, pmoId, projectId, taskId);

    if (validationResult.success) {
      if (validationResult.isConsistent) {
        console.log(`✅ Metadata is CONSISTENT with Firebase structure`);
        console.log(`   Location: ${validationResult.actualLocation}`);
      } else {
        console.log(`❌ Metadata is INCONSISTENT with Firebase structure`);
        console.log(`   Expected: users/${userId}/PMO/${pmoId}/projects/${projectId}/tasks/${taskId}`);
        console.log(`   Actual: ${validationResult.actualLocation || 'Not found'}`);
        console.log(`   Error: ${validationResult.error}`);
      }
    } else {
      console.log(`❌ Validation failed: ${validationResult.error}`);
    }

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  }
}

/**
 * Validate all tasks in a PMO
 */
async function validateAllTasks(userId, pmoId) {
  console.log('\n🔍 VALIDATING ALL TASKS IN PMO');
  console.log('==============================');
  console.log(`User ID: ${userId}`);
  console.log(`PMO ID: ${pmoId}`);
  console.log('');

  try {
    const hierarchicalResult = await getHierarchicalPMOData(userId, pmoId);

    if (!hierarchicalResult.success || !hierarchicalResult.data) {
      console.log(`❌ No hierarchical data found: ${hierarchicalResult.error}`);
      return;
    }

    let totalTasks = 0;
    let consistentTasks = 0;
    let inconsistentTasks = 0;

    // Validate each project in the hierarchical structure (HierarchicalPMOData[])
    for (const project of hierarchicalResult.data) {
      console.log(`\n📁 Project: ${project.projectId} (${project.taskIds.length} tasks)`);

      for (const taskId of project.taskIds) {
        totalTasks++;

        const validationResult = await validateTaskMetadata(userId, pmoId, project.projectId, taskId);

        if (validationResult.success && validationResult.isConsistent) {
          consistentTasks++;
          console.log(`   ✅ ${taskId}`);
        } else {
          inconsistentTasks++;
          console.log(`   ❌ ${taskId} - ${validationResult.error || 'Inconsistent'}`);
        }
      }
    }

    console.log('\n📊 SUMMARY');
    console.log('===========');
    console.log(`Total tasks: ${totalTasks}`);
    console.log(`Consistent: ${consistentTasks} (${Math.round(consistentTasks/totalTasks*100)}%)`);
    console.log(`Inconsistent: ${inconsistentTasks} (${Math.round(inconsistentTasks/totalTasks*100)}%)`);

  } catch (error) {
    console.error('❌ Error during validation:', error);
  }
}

/**
 * Main function
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log('Usage:');
    console.log('  node scripts/debug-pmo-data-consistency.js [userId] [pmoId] [projectId] [taskId]');
    console.log('  node scripts/debug-pmo-data-consistency.js validate-all [userId] [pmoId]');
    console.log('');
    console.log('Examples:');
    console.log('  node scripts/debug-pmo-data-consistency.js <EMAIL> c76670a7-bc7b-44ea-9905-189a4bcf36c8 AMIp7WUTE26kDnzZC84T klSkNTXipB3RGG0kTmHG');
    console.log('  node scripts/debug-pmo-data-consistency.js validate-all <EMAIL> c76670a7-bc7b-44ea-9905-189a4bcf36c8');
    process.exit(1);
  }

  if (args[0] === 'validate-all') {
    const userId = args[1] || DEFAULT_USER_ID;
    const pmoId = args[2];

    if (!pmoId) {
      console.error('❌ PMO ID is required for validate-all command');
      process.exit(1);
    }

    await validateAllTasks(userId, pmoId);
  } else {
    const userId = args[0] || DEFAULT_USER_ID;
    const pmoId = args[1];
    const projectId = args[2];
    const taskId = args[3];

    if (!pmoId || !projectId || !taskId) {
      console.error('❌ PMO ID, Project ID, and Task ID are required');
      process.exit(1);
    }

    await debugTaskConsistency(userId, pmoId, projectId, taskId);
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  debugTaskConsistency,
  validateAllTasks
};
