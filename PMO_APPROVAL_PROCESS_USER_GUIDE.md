# PMO Project Approval Process - User Guide

## 🎯 **Answer: Manual Navigation Required**

**Yes, the user must manually navigate to find and trigger the approval form.** The approval process is **NOT automatically generated** - it requires user action.

## 📍 **Where to Find the Approval Form**

### **Step-by-Step Navigation**

1. **Go to PMO Service Page**: `/services/pmo`
2. **Click "PMO Output" Tab** (should be visible as a tab on the page)
3. **Find Strategic Director Output** (look for outputs with "Strategic Director" agent type)
4. **Click on the Output** to select it (it will highlight)
5. **Click "Create Project" Button** (orange button with settings icon)
6. **Approval Modal Opens** with task review interface

### **Visual Guide**

```
/services/pmo
├── PMO Form Tab (where you create records)
├── PMO Output Tab ← **CLICK HERE**
    ├── List of Agent Outputs
    ├── Strategic Director Output ← **SELECT THIS**
    └── "Create Project" Button ← **CLICK HERE**
        └── Approval Modal Opens ← **REVIEW & APPROVE**
```

## 🔍 **What to Look For**

### **In PMO Output Tab**
- **Agent Type**: "Strategic Director" 
- **Orange Button**: "Create Project" with settings icon
- **Output Content**: Should show strategic analysis
- **Metadata**: Should include your PMO record title

### **Button Appearance**
```
🔧 Create Project
```
- **Color**: Orange background
- **Icon**: Settings/gear icon
- **Text**: "Create Project"
- **Location**: Below the output content

## ⚠️ **Common Issues & Solutions**

### **"I don't see the Create Project button"**
**Possible Causes**:
1. **Wrong output selected** - Make sure it's "Strategic Director" type
2. **Missing PMO metadata** - Output must be linked to a PMO record
3. **Output still processing** - Wait for strategic analysis to complete

**Solution**: Look for outputs with:
- Agent Type: "Strategic Director"
- Has PMO metadata (pmoId)
- Shows your PMO record title

### **"I don't see any outputs"**
**Possible Causes**:
1. **Strategic analysis not complete** - Wait for processing
2. **Wrong tab** - Make sure you're in "PMO Output" tab
3. **No PMO record sent** - Make sure you clicked "Send to Marketing"

**Solution**: 
1. Check if strategic analysis completed
2. Refresh the page
3. Verify PMO record was sent to marketing

### **"The modal doesn't open"**
**Possible Causes**:
1. **JavaScript error** - Check browser console
2. **Loading state** - Button shows "Loading..." 
3. **Network issue** - Check internet connection

**Solution**:
1. Refresh the page and try again
2. Check browser console for errors
3. Wait for loading to complete

## 🚀 **Complete User Workflow**

### **Phase 1: Create & Send PMO Record**
1. Go to `/services/pmo`
2. Fill out PMO form (title, description, etc.)
3. Click "Send to Marketing"
4. Wait for strategic analysis to complete

### **Phase 2: Navigate to Approval (Manual)**
5. **Stay on same page** (`/services/pmo`)
6. **Click "PMO Output" tab**
7. **Find Strategic Director output** (newest one)
8. **Click on the output** to select it
9. **Click "Create Project" button**

### **Phase 3: Review & Approve**
10. **Review project details** (name, description, dates)
11. **Review extracted tasks** (approve/reject individually)
12. **Review subtasks** (expand and approve/reject)
13. **Check statistics** (total tasks, approved tasks)
14. **Click "Commit Project"** to create

### **Phase 4: Verification**
15. **Check success message** 
16. **Verify project created** with correct owner (Admin User)
17. **Verify tasks created** (only approved ones)

## 💡 **Pro Tips**

### **Faster Navigation**
- **Bookmark** `/services/pmo` for quick access
- **Keep PMO Output tab open** while waiting for analysis
- **Refresh the page** if outputs don't appear immediately

### **Better Approval Process**
- **Review all tasks carefully** before approving
- **Expand subtasks** to see full scope
- **Use statistics** to track approval progress
- **Approve in batches** rather than one-by-one

### **Troubleshooting**
- **Check browser console** for any JavaScript errors
- **Refresh the page** if buttons don't respond
- **Wait for loading states** to complete
- **Verify PMO metadata** is present in output

## 🔧 **Technical Details**

### **Button Visibility Conditions**
The "Create Project" button only appears when:
1. `selectedOutput.agentType === 'strategic-director'`
2. `selectedOutput.metadata?.pmoId` exists
3. Output is selected (clicked on)

### **Expected Console Logs**
When strategic analysis completes:
```
[AGENT_OUTPUT] Successfully stored strategic analysis output with requestId: xxx
[PROJECT_CREATION] PMO workflow detected - skipping automatic project creation for manual approval
[PROJECT_CREATION] User can trigger project creation via "Create Project" button in PMO Output tab
```

### **API Response Enhancement**
The marketing collaboration API now returns PMO-specific guidance:
```json
{
  "pmoWorkflow": {
    "nextSteps": {
      "action": "Navigate to PMO Output tab to create project",
      "location": "PMO Output tab → Find Strategic Director output → Click Create Project button",
      "description": "Strategic analysis complete. Ready for project creation with task approval."
    }
  }
}
```

---

## ✅ **Summary**

**The approval form is NOT automatically generated.** Users must:

1. **Manually navigate** to PMO Output tab
2. **Find and select** the Strategic Director output  
3. **Click "Create Project"** button to trigger approval modal
4. **Review and approve** tasks in the modal
5. **Commit** to create the project

This manual process ensures **user control** over project creation and **task approval**, which is the intended behavior for PMO workflows.
