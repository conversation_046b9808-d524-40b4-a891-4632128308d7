'use client';

import React, { useState, useEffect } from 'react';
import { Task, Resource, TaskStatus, TaskPriority } from '../types';
import { Calendar, Clock, AlertCircle, Trash2 } from 'lucide-react';

interface TaskFormProps {
  initialValues?: Partial<Task>;
  resources: Resource[];
  tasks: Task[];
  onSubmit: (task: Task) => void;
  onCancel: () => void;
  projectCategories?: string[];
  onDelete?: (taskId: string) => void;
  canDelete?: boolean;
}

const TaskForm: React.FC<TaskFormProps> = ({
  initialValues,
  resources,
  tasks,
  onSubmit,
  onCancel,
  projectCategories = [],
  onDelete,
  canDelete = false
}) => {
  const [formValues, setFormValues] = useState<Partial<Task>>({
    title: '',
    description: '',
    category: '',
    status: 'Not Started' as TaskStatus,
    startDate: new Date(),
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Default to 1 week from now
    assignedTo: [],
    priority: 'Medium' as TaskPriority,
    dependencies: [],
    notes: '',
    ...initialValues
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [availableTasks, setAvailableTasks] = useState<Task[]>([]);

  // Filter out the current task from available dependencies
  useEffect(() => {
    if (initialValues?.id) {
      setAvailableTasks(tasks.filter(task => task.id !== initialValues.id));
    } else {
      setAvailableTasks(tasks);
    }
  }, [tasks, initialValues]);

  // Log project categories when they change
  useEffect(() => {
    console.log('Project categories available for task:', projectCategories);
  }, [projectCategories]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'startDate' | 'dueDate') => {
    const date = new Date(e.target.value);
    setFormValues(prev => ({ ...prev, [field]: date }));

    // Clear error when field is edited
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleMultiSelect = (e: React.ChangeEvent<HTMLSelectElement>, field: 'assignedTo' | 'dependencies') => {
    const options = Array.from(e.target.selectedOptions).map(option => option.value);
    setFormValues(prev => ({ ...prev, [field]: options }));

    // Clear error when field is edited
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formValues.title?.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formValues.category?.trim()) {
      newErrors.category = 'Category is required';
    }

    if (!formValues.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    if (!formValues.dueDate) {
      newErrors.dueDate = 'Due date is required';
    } else if (formValues.startDate && formValues.dueDate && formValues.dueDate < formValues.startDate) {
      newErrors.dueDate = 'Due date must be after start date';
    }

    if (!formValues.assignedTo?.length) {
      newErrors.assignedTo = 'At least one resource must be assigned';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit(formValues as Task);
    }
  };

  // Format date for input
  const formatDateForInput = (date: Date | undefined) => {
    if (!date) return '';
    return date.toISOString().split('T')[0];
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 bg-gray-800 p-4 rounded-lg">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Title */}
        <div className="col-span-2">
          <label className="block text-sm font-medium text-white mb-1">
            Task Title*
          </label>
          <input
            type="text"
            name="title"
            value={formValues.title || ''}
            onChange={handleChange}
            className={`w-full p-2 border rounded-md bg-gray-700 text-white ${errors.title ? 'border-red-500' : 'border-gray-600'}`}
            placeholder="Enter task title"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.title}
            </p>
          )}
        </div>

        {/* Category */}
        <div>
          <label className="block text-sm font-medium text-white mb-1">
            Category*
          </label>
          {projectCategories.length > 0 ? (
            <select
              name="category"
              value={formValues.category || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md bg-gray-700 text-white ${errors.category ? 'border-red-500' : 'border-gray-600'}`}
            >
              <option value="">Select a category</option>
              {projectCategories.map((category, index) => (
                <option key={index} value={category}>
                  {category}
                </option>
              ))}
            </select>
          ) : (
            <input
              type="text"
              name="category"
              value={formValues.category || ''}
              onChange={handleChange}
              className={`w-full p-2 border rounded-md bg-gray-700 text-white ${errors.category ? 'border-red-500' : 'border-gray-600'}`}
              placeholder="e.g., Marketing, Design, Development"
            />
          )}
          {errors.category && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.category}
            </p>
          )}
        </div>

        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-white mb-1">
            Status
          </label>
          <select
            name="status"
            value={formValues.status || 'Not Started'}
            onChange={handleChange}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white"
          >
            <option value="Not Started">Not Started</option>
            <option value="In Progress">In Progress</option>
            <option value="Reviewed">Reviewed</option>
            <option value="Complete">Complete</option>
          </select>
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-white mb-1">
            Start Date*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-gray-300" />
            </div>
            <input
              type="date"
              value={formatDateForInput(formValues.startDate)}
              onChange={(e) => handleDateChange(e, 'startDate')}
              className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.startDate ? 'border-red-500' : 'border-gray-600'}`}
            />
          </div>
          {errors.startDate && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.startDate}
            </p>
          )}
        </div>

        {/* Due Date */}
        <div>
          <label className="block text-sm font-medium text-white mb-1">
            Due Date*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-gray-300" />
            </div>
            <input
              type="date"
              value={formatDateForInput(formValues.dueDate)}
              onChange={(e) => handleDateChange(e, 'dueDate')}
              className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.dueDate ? 'border-red-500' : 'border-gray-600'}`}
            />
          </div>
          {errors.dueDate && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.dueDate}
            </p>
          )}
        </div>

        {/* Priority */}
        <div>
          <label className="block text-sm font-medium text-white mb-1">
            Priority
          </label>
          <select
            name="priority"
            value={formValues.priority || 'Medium'}
            onChange={handleChange}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white"
          >
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
            <option value="Critical">Critical</option>
          </select>
        </div>

        {/* Assigned To */}
        <div className="col-span-2">
          <label className="block text-sm font-medium text-white mb-1">
            Assigned To*
          </label>
          <select
            multiple
            value={formValues.assignedTo || []}
            onChange={(e) => handleMultiSelect(e, 'assignedTo')}
            className={`w-full p-2 border rounded-md h-24 bg-gray-700 text-white ${errors.assignedTo ? 'border-red-500' : 'border-gray-600'}`}
          >
            {resources.map(resource => (
              <option key={resource.id} value={resource.id}>
                {resource.name} ({resource.jobTitle || resource.role})
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-gray-400">Hold Ctrl/Cmd to select multiple resources</p>
          {errors.assignedTo && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.assignedTo}
            </p>
          )}
        </div>

        {/* Dependencies */}
        <div className="col-span-2">
          <label className="block text-sm font-medium text-white mb-1">
            Dependencies
          </label>
          <select
            multiple
            value={formValues.dependencies || []}
            onChange={(e) => handleMultiSelect(e, 'dependencies')}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white h-24"
          >
            {availableTasks.map(task => (
              <option key={task.id} value={task.id}>
                {task.title}
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-gray-400">Tasks that must be completed before this one</p>
        </div>

        {/* Description */}
        <div className="col-span-2">
          <label className="block text-sm font-medium text-white mb-1">
            Description
          </label>
          <textarea
            name="description"
            value={formValues.description || ''}
            onChange={handleChange}
            rows={3}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white"
            placeholder="Enter task description"
          />
        </div>

        {/* Notes */}
        <div className="col-span-2">
          <label className="block text-sm font-medium text-white mb-1">
            Notes
          </label>
          <textarea
            name="notes"
            value={formValues.notes || ''}
            onChange={handleChange}
            rows={2}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white"
            placeholder="Additional notes"
          />
        </div>
      </div>

      <div className="flex justify-between pt-4 border-t border-gray-700">
        {/* Delete button - only shown for existing tasks when canDelete is true */}
        <div>
          {initialValues?.id && canDelete && onDelete && (
            <button
              type="button"
              onClick={() => initialValues.id && onDelete(initialValues.id)}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Delete Task
            </button>
          )}
        </div>

        {/* Cancel and Submit buttons */}
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-600 rounded-md text-white bg-gray-700 hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            {initialValues?.id ? 'Update Task' : 'Create Task'}
          </button>
        </div>
      </div>
    </form>
  );
};

export default TaskForm;
