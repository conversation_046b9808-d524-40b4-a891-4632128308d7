import React from 'react';

interface ProgressStepProps {
  step: number;
  currentStep: number;
  label: string;
  description: string;
}

const ProgressStep: React.FC<ProgressStepProps> = ({ step, currentStep, label, description }) => {
  const isActive = currentStep >= step;
  const isPrevious = currentStep > step;
  
  return (
    <div className="flex items-center mb-4">
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
        isPrevious ? 'bg-green-500' : isActive ? 'bg-purple-600 animate-pulse' : 'bg-zinc-700'
      }`}>
        {isPrevious ? (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
          </svg>
        ) : (
          <span className="text-white text-sm font-medium">{step}</span>
        )}
      </div>
      <div>
        <h4 className={`font-medium ${isActive ? 'text-white' : 'text-zinc-400'}`}>{label}</h4>
        <p className={`text-xs ${isActive ? 'text-zinc-300' : 'text-zinc-500'}`}>{description}</p>
      </div>
    </div>
  );
};

interface ImageGenerationProgressProps {
  currentStep: number;
}

const ImageGenerationProgress: React.FC<ImageGenerationProgressProps> = ({ currentStep }) => {
  return (
    <div className="bg-zinc-800 rounded-lg p-4 mb-4 border border-zinc-700">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <svg className="w-5 h-5 mr-2 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
        </svg>
        Image Generation Progress
      </h3>
      
      <div className="space-y-2">
        <ProgressStep 
          step={1} 
          currentStep={currentStep} 
          label="Initializing" 
          description="Setting up the image generation job" 
        />
        <ProgressStep 
          step={2} 
          currentStep={currentStep} 
          label="Refining Prompt" 
          description="Enhancing your prompt for better results" 
        />
        <ProgressStep 
          step={3} 
          currentStep={currentStep} 
          label="Generating Image" 
          description="Creating your image with AI" 
        />
        <ProgressStep 
          step={4} 
          currentStep={currentStep} 
          label="Processing" 
          description="Saving and optimizing your image" 
        />
        <ProgressStep 
          step={5} 
          currentStep={currentStep} 
          label="Complete" 
          description="Your image is ready to view and download" 
        />
      </div>
    </div>
  );
};

export default ImageGenerationProgress;
