import { streamText } from "ai";
import { openai } from "@ai-sdk/openai";
import { adminDb } from 'components/firebase/admin';
import { db } from 'components/firebase/config';
import { collection, addDoc, getDocs, query, where, orderBy, limit as firestoreLimit, serverTimestamp } from 'firebase/firestore';

/**
 * Social Media service for generating and managing social media content
 */
class SocialMediaService {
  /**
   * Generate social media posts based on content
   * @param content - The content to base posts on
   * @param platforms - The platforms to generate for (twitter, linkedin, facebook, instagram)
   * @param count - Number of posts to generate per platform
   * @returns Generated posts for each platform
   */
  async generatePosts(content: string, platforms: string[] = ['twitter'], count: number | Record<string, any> = 1): Promise<{ success: boolean, posts?: Record<string, string[]>, error?: string }> {
    // Handle if count is passed as an options object
    if (typeof count === 'object') {
      count = 1; // Default to 1 if options object is passed instead of count
    }
    try {
      if (!content) {
        throw new Error('Content is required for post generation');
      }

      // Validate platforms
      const validPlatforms = ['twitter', 'linkedin', 'facebook', 'instagram'];
      const filteredPlatforms = platforms.filter(p => validPlatforms.includes(p.toLowerCase()));

      if (filteredPlatforms.length === 0) {
        throw new Error('At least one valid platform is required');
      }

      // Limit count to reasonable number
      const postCount = Math.min(Math.max(count, 1), 5);

      // Generate posts for each platform
      const results: Record<string, string[]> = {};

      for (const platform of filteredPlatforms) {
        results[platform] = await this._generatePostsForPlatform(content, platform, postCount);
      }

      return { success: true, posts: results };
    } catch (error: any) {
      console.error('Error generating social media posts:', error);
      return { success: false, error: error.message || 'Unknown error occurred' };
    }
  }

  /**
   * Generate posts for a specific platform
   * @private
   * @param content - The content to base posts on
   * @param platform - The platform to generate for
   * @param count - Number of posts to generate
   * @returns Array of generated posts
   */
  private async _generatePostsForPlatform(content: string, platform: string, count: number): Promise<string[]> {
    try {
      // Create prompt based on platform
      const prompt = this._createPromptForPlatform(content, platform, count);

      // Use OpenAI to generate posts
      const modelProvider = openai('gpt-4o');

      const { textStream } = await streamText({
        model: modelProvider,
        prompt: prompt,
        temperature: 0.7,
        maxTokens: 1000
      });

      // Collect the response
      let result = "";
      for await (const delta of textStream) {
        result += delta;
      }

      // Parse the response into individual posts
      return this._parsePostsFromResponse(result, platform);
    } catch (error: any) {
      console.error(`Error generating ${platform} posts:`, error);
      return [`Error generating ${platform} posts: ${error.message}`];
    }
  }

  /**
   * Create a prompt for a specific platform
   * @private
   * @param content - The content to base posts on
   * @param platform - The platform to generate for
   * @param count - Number of posts to generate
   * @returns Prompt for the AI
   */
  private _createPromptForPlatform(content: string, platform: string, count: number): string {
    // Truncate content if too long
    const truncatedContent = content.length > 2000
      ? content.substring(0, 2000) + '...'
      : content;

    // Platform-specific instructions
    const platformInstructions: Record<string, string> = {
      twitter: `
        - Keep each post under 280 characters
        - Use hashtags sparingly (1-3 per post)
        - Consider adding a call to action
        - Make it conversational and engaging
      `,
      linkedin: `
        - Professional tone appropriate for business audience
        - Can be longer (up to 1300 characters)
        - Include 1-2 relevant hashtags
        - Consider adding a thought-provoking question
      `,
      facebook: `
        - Conversational and friendly tone
        - Can include questions to encourage engagement
        - Moderate length (100-250 words)
        - Consider adding emojis where appropriate
      `,
      instagram: `
        - Visual-focused - describe what image would accompany this
        - Use more hashtags (5-10 relevant ones)
        - Conversational and authentic tone
        - Include a call to action (like, comment, share)
      `
    };

    return `
      Generate ${count} engaging social media posts for ${platform} based on the following content.

      CONTENT:
      ${truncatedContent}

      PLATFORM-SPECIFIC GUIDELINES:
      ${platformInstructions[platform] || ''}

      GENERAL GUIDELINES:
      - Each post should be unique and highlight different aspects of the content
      - Make the posts engaging and shareable
      - Format each post as a separate numbered item (1., 2., etc.)
      - Do not include any explanations, just the posts themselves

      Generate ${count} posts:
    `;
  }

  /**
   * Parse posts from AI response
   * @private
   * @param response - The AI response
   * @param platform - The platform the posts are for
   * @returns Array of parsed posts
   */
  private _parsePostsFromResponse(response: string, _platform: string): string[] {
    try {
      // Split by numbered items (1., 2., etc.)
      const posts = response.split(/\n\s*\d+\.\s*/)
        .map(post => post.trim())
        .filter(post => post.length > 0);

      // If splitting didn't work, try line breaks
      if (posts.length <= 1) {
        return response.split('\n\n')
          .map(post => post.trim())
          .filter(post => post.length > 0);
      }

      return posts;
    } catch (error) {
      console.error('Error parsing posts from response:', error);
      return [response]; // Return the whole response as a single post
    }
  }

  /**
   * Save generated posts to the database
   * @param posts - The posts to save
   * @param userId - The user ID who generated the posts
   * @param metadata - Additional metadata
   * @returns The saved post IDs
   */
  async savePosts(posts: Record<string, string[]>, userId: string, metadata: Record<string, any> = {}): Promise<string[]> {
    try {
      const savedPostIds: string[] = [];

      // Save each post to Firestore
      for (const [platform, platformPosts] of Object.entries(posts)) {
        for (const postContent of platformPosts) {
          const postData = {
            content: postContent,
            platform,
            userId,
            status: 'draft',
            metadata,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          };

          const docRef = await addDoc(collection(db, 'social_media_posts'), postData);
          savedPostIds.push(docRef.id);
        }
      }

      return savedPostIds;
    } catch (error) {
      console.error('Error saving social media posts:', error);
      throw error;
    }
  }

  /**
   * Get posts for a user
   * @param userId - The user ID
   * @param platform - Optional platform filter
   * @param status - Optional status filter
   * @param limit - Maximum number of posts to return
   * @returns The user's posts
   */
  async getUserPosts(userId: string, platform?: string, status?: string, limit: number = 50): Promise<any[]> {
    try {
      // Build query
      let postsQuery = query(
        collection(db, 'social_media_posts'),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        firestoreLimit(limit)
      );

      // Add platform filter if specified
      if (platform) {
        postsQuery = query(
          postsQuery,
          where('platform', '==', platform)
        );
      }

      // Add status filter if specified
      if (status) {
        postsQuery = query(
          postsQuery,
          where('status', '==', status)
        );
      }

      // Execute query
      const snapshot = await getDocs(postsQuery);

      // Format results
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error getting user posts:', error);
      throw error;
    }
  }

  /**
   * Generate a content calendar for social media
   * @param content - The content to base the calendar on
   * @param platforms - The platforms to generate for
   * @param days - Number of days to generate content for
   * @param options - Additional options for generation
   * @returns Generated content calendar
   */
  async generateContentCalendar(content: string, platforms: string[] = ['twitter', 'linkedin', 'facebook'], days: number = 7, options: Record<string, any> = {}): Promise<any> {
    try {
      if (!content) {
        throw new Error('Content is required for calendar generation');
      }

      // Validate platforms
      const validPlatforms = ['twitter', 'linkedin', 'facebook', 'instagram'];
      const filteredPlatforms = platforms.filter(p => validPlatforms.includes(p.toLowerCase()));

      if (filteredPlatforms.length === 0) {
        throw new Error('At least one valid platform is required');
      }

      // Limit days to reasonable number
      const calendarDays = Math.min(Math.max(days, 1), 30);

      // Create prompt for content calendar
      const prompt = this._createCalendarPrompt(content, filteredPlatforms, calendarDays, options);

      // Use OpenAI to generate calendar
      const modelProvider = openai('gpt-4o');

      const { textStream } = await streamText({
        model: modelProvider,
        prompt: prompt,
        temperature: 0.7,
        maxTokens: 2000
      });

      // Collect the response
      let result = "";
      for await (const delta of textStream) {
        result += delta;
      }

      // Parse the calendar from the response
      const calendar = this._parseCalendarFromResponse(result, filteredPlatforms, calendarDays);

      return {
        success: true,
        calendar,
        platforms: filteredPlatforms,
        days: calendarDays
      };
    } catch (error: any) {
      console.error('Error generating content calendar:', error);
      return {
        success: false,
        error: error.message || 'Unknown error occurred'
      };
    }
  }

  /**
   * Create a prompt for content calendar generation
   * @private
   * @param content - The content to base the calendar on
   * @param platforms - The platforms to generate for
   * @param days - Number of days to generate content for
   * @param options - Additional options for generation
   * @returns Prompt for the AI
   */
  private _createCalendarPrompt(content: string, platforms: string[], days: number, options: Record<string, any>): string {
    // Truncate content if too long
    const truncatedContent = content.length > 2000
      ? content.substring(0, 2000) + '...'
      : content;

    // Extract options
    const tone = options.tone || 'professional';
    const targetAudience = options.targetAudience || 'general audience';
    const theme = options.theme || '';

    return `
      Generate a ${days}-day social media content calendar for the following platforms: ${platforms.join(', ')}.

      CONTENT TO BASE CALENDAR ON:
      ${truncatedContent}

      CALENDAR SPECIFICATIONS:
      - Tone: ${tone}
      - Target Audience: ${targetAudience}
      ${theme ? `- Theme: ${theme}` : ''}
      - Create a structured calendar with one post per platform per day
      - For each day, include the date and day of the week
      - For each post, include:
        * Platform
        * Post content
        * Best time to post
        * Hashtags (if applicable)

      Format the calendar as follows:

      # Day 1: [Day of Week, Date]

      ## [Platform 1]
      - Content: [Post content]
      - Time: [Best time to post]
      - Hashtags: [Relevant hashtags]

      ## [Platform 2]
      - Content: [Post content]
      - Time: [Best time to post]
      - Hashtags: [Relevant hashtags]

      # Day 2: [Day of Week, Date]

      And so on...

      Generate a ${days}-day calendar starting from tomorrow:
    `;
  }

  /**
   * Parse content calendar from AI response
   * @private
   * @param response - The AI response
   * @param platforms - The platforms the calendar is for
   * @param days - Number of days in the calendar
   * @returns Parsed content calendar
   */
  private _parseCalendarFromResponse(response: string, platforms: string[], _days: number): any {
    try {
      // Split by days
      const dayRegex = /# Day \d+: [\w\s,]+/g;
      const dayMatches = response.split(dayRegex).filter(Boolean);
      const dayTitles = response.match(dayRegex) || [];

      const calendar = [];

      for (let i = 0; i < Math.min(dayMatches.length, dayTitles.length); i++) {
        const dayTitle = dayTitles[i];
        const dayContent = dayMatches[i + 1] || dayMatches[i]; // Handle off-by-one in split

        // Extract date information
        const dateMatch = dayTitle.match(/Day (\d+): ([\w\s,]+)/);
        const dayNumber = dateMatch ? parseInt(dateMatch[1]) : i + 1;
        const dateInfo = dateMatch ? dateMatch[2].trim() : `Day ${dayNumber}`;

        // Extract platform posts
        const platformPosts: Record<string, { content: string, time: string, hashtags: string }> = {};

        for (const platform of platforms) {
          const platformRegex = new RegExp(`## ${platform}[\s\S]*?(?=## |$)`, 'i');
          const platformMatch = dayContent.match(platformRegex);

          if (platformMatch) {
            const platformContent = platformMatch[0];

            // Extract post details
            const contentMatch = platformContent.match(/Content: ([\s\S]*?)(?=- Time:|$)/);
            const timeMatch = platformContent.match(/Time: ([\s\S]*?)(?=- Hashtags:|$)/);
            const hashtagsMatch = platformContent.match(/Hashtags: ([\s\S]*?)(?=$)/);

            platformPosts[platform.toLowerCase()] = {
              content: contentMatch ? contentMatch[1].trim() : '',
              time: timeMatch ? timeMatch[1].trim() : '',
              hashtags: hashtagsMatch ? hashtagsMatch[1].trim() : ''
            };
          }
        }

        calendar.push({
          day: dayNumber,
          date: dateInfo,
          posts: platformPosts
        });
      }

      return calendar;
    } catch (error) {
      console.error('Error parsing calendar from response:', error);
      return []; // Return empty calendar on error
    }
  }

  /**
   * Save a single post to the database
   * @param post - The post content
   * @param userId - The user ID who generated the post
   * @param documentId - Optional document ID associated with the post
   * @returns The saved post ID
   */
  async savePost(post: { content: string, platform: string }, userId: string, documentId?: string): Promise<string> {
    try {
      if (!post || !post.content || !post.platform) {
        throw new Error('Post content and platform are required');
      }

      const postData = {
        content: post.content,
        platform: post.platform,
        userId,
        documentId: documentId || null,
        status: 'draft',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      const docRef = await addDoc(collection(db, 'social_media_posts'), postData);
      return docRef.id;
    } catch (error) {
      console.error('Error saving social media post:', error);
      throw error;
    }
  }

  /**
   * Schedule a post for publishing
   * @param postId - The post ID
   * @param scheduledTime - When to publish the post
   * @returns Success status
   */
  async schedulePost(postId: string, scheduledTime: Date): Promise<boolean> {
    try {
      // This would integrate with a scheduling system
      // For now, just update the post status
      await adminDb.collection('social_media_posts').doc(postId).update({
        status: 'scheduled',
        scheduledTime,
        updatedAt: serverTimestamp()
      });

      return true;
    } catch (error) {
      console.error('Error scheduling post:', error);
      return false;
    }
  }
}

// Export a singleton instance
export const socialMediaService = new SocialMediaService();