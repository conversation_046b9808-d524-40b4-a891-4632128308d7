import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { createPMORecordFromForm } from '../../../lib/firebase/pmoCollection';
import { PMOFormInput } from '../../../lib/agents/pmo/PMOInterfaces';

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { formData } = body;

    // Validate request body
    if (!formData) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create the PMO record
    const pmoId = await createPMORecordFromForm(session.user.email, formData);

    // Return the result
    return NextResponse.json({
      success: true,
      pmoId
    });
  } catch (error: any) {
    console.error('Error creating PMO record:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create PMO record' },
      { status: 500 }
    );
  }
}
