/**
 * Chart Generation Tool for creating data visualizations from text prompts
 * Uses LLM to convert natural language to chart data and configuration
 *
 * Note: This file contains several type definitions that are not directly used
 * in the code but are kept for documentation and type inference purposes.
 */

import { LlmTool, LlmProvider } from './llm-tool';
import { z } from 'zod';

// Initialize the LLM tool for processing prompts
const llmTool = new LlmTool();

/**
 * Chart types supported by the tool
 */
export const CHART_TYPES = {
  BAR: 'bar',
  LINE: 'line',
  PIE: 'pie',
  AREA: 'area',
  SCATTER: 'scatter',
  RADAR: 'radar',
  COMPOSED: 'composed',
  TABLE: 'table',
  FLOW: 'flow',
  HEATMAP: 'heatmap',
  BUBBLE: 'bubble'
} as const;

export type ChartType = typeof CHART_TYPES[keyof typeof CHART_TYPES];

/**
 * Zod schemas for chart validation
 */

// Schema for axis configuration
const AxisSchema = z.object({
  label: z.string(),
  dataKey: z.string().optional(),
});

type AxisConfig = z.infer<typeof AxisSchema>;

// Schema for table column
const TableColumnSchema = z.object({
  header: z.string(),
  accessorKey: z.string(),
  type: z.enum(['string', 'number', 'date', 'boolean']),
  // Add aliases for compatibility with different naming conventions
  title: z.string().optional(),
  dataKey: z.string().optional(),
});

type TableColumn = z.infer<typeof TableColumnSchema>;

// Base chart schema with common properties for all chart types
const BaseChartSchema = z.object({
  chartType: z.enum(['bar', 'line', 'pie', 'area', 'scatter', 'radar', 'composed', 'table', 'flow', 'heatmap', 'bubble'] as [string, ...string[]]),
  title: z.string(),
  subtitle: z.string().optional(),
  colors: z.array(z.string()).optional(),
  legend: z.boolean().optional(),
  tooltip: z.boolean().optional(),
  grid: z.boolean().optional(),
  explanation: z.string().optional(),
});

type BaseChartConfig = z.infer<typeof BaseChartSchema>;

// Schema for standard chart types (bar, line, pie, area, radar)
const StandardChartSchema = BaseChartSchema.extend({
  data: z.array(
    z.record(z.string(), z.union([z.string(), z.number()]))
  ).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
}).refine(data => {
  // For standard charts, ensure data is properly structured
  return data.chartType !== CHART_TYPES.SCATTER;
}, {
  message: "Standard chart schema cannot be used for scatter plots",
  path: ["chartType"],
});

type StandardChartConfig = z.infer<typeof StandardChartSchema>;

// Schema specifically for scatter plots
const ScatterChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.SCATTER),
  data: z.array(
    z.object({
      name: z.string().optional(),
      x: z.number(),
      y: z.number(),
    })
  ).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
});

type ScatterChartConfig = z.infer<typeof ScatterChartSchema>;

// Schema specifically for table visualizations
const TableChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.TABLE),
  columns: z.array(TableColumnSchema).min(1),
  data: z.array(z.record(z.string(), z.union([z.string(), z.number(), z.boolean(), z.date()]))).min(1),
  pagination: z.boolean().optional(),
  rowsPerPage: z.number().optional(),
});

type TableChartConfig = z.infer<typeof TableChartSchema>;

// Schema for flow chart nodes
const FlowNodeSchema = z.object({
  id: z.string(),
  type: z.string().optional(),
  position: z.object({
    x: z.number(),
    y: z.number()
  }),
  data: z.object({
    label: z.string(),
    description: z.string().optional(),
  }).optional(),
  style: z.record(z.string(), z.union([z.string(), z.number()])).optional(),
});

type FlowNode = z.infer<typeof FlowNodeSchema>;

// Schema for flow chart edges
const FlowEdgeSchema = z.object({
  id: z.string(),
  source: z.string(),
  target: z.string(),
  label: z.string().optional(),
  type: z.string().optional(),
  animated: z.boolean().optional(),
  style: z.record(z.string(), z.union([z.string(), z.number()])).optional(),
});

type FlowEdge = z.infer<typeof FlowEdgeSchema>;

// Schema specifically for flow charts
const FlowChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.FLOW),
  nodes: z.array(FlowNodeSchema).min(1),
  edges: z.array(FlowEdgeSchema).min(0),
  direction: z.enum(['LR', 'RL', 'TB', 'BT']).optional(),
  fitView: z.boolean().optional(),
});

type FlowChartConfig = z.infer<typeof FlowChartSchema>;

// Schema specifically for heat map charts
const HeatMapChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.HEATMAP),
  data: z.array(z.record(z.string(), z.union([z.string(), z.number()]))).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
  colorScale: z.array(z.string()).optional(),
  showValues: z.boolean().optional(),
});

type HeatMapChartConfig = z.infer<typeof HeatMapChartSchema>;

// Schema specifically for bubble charts
const BubbleChartSchema = BaseChartSchema.extend({
  chartType: z.literal(CHART_TYPES.BUBBLE),
  data: z.array(
    z.object({
      name: z.string().optional(),
      x: z.number(),
      y: z.number(),
      z: z.number(),
      category: z.string().optional(),
    })
  ).min(1),
  xAxis: AxisSchema.optional(),
  yAxis: AxisSchema.optional(),
  zAxis: z.object({
    label: z.string().optional(),
    dataKey: z.string().optional(),
    range: z.tuple([z.number(), z.number()]).optional(),
  }).optional(),
  colorKey: z.string().optional(),
});

type BubbleChartConfig = z.infer<typeof BubbleChartSchema>;

// Combined chart schema that can be any of the specific chart types
const ChartConfigSchema = z.union([
  StandardChartSchema,
  ScatterChartSchema,
  TableChartSchema,
  FlowChartSchema,
  HeatMapChartSchema,
  BubbleChartSchema
]);

export type ChartConfig = z.infer<typeof ChartConfigSchema>;

// Types for chart generation options
export interface ChartGenerationOptions {
  prompt: string;
  chartType?: ChartType;
  model?: string;
  provider?: LlmProvider;
}

// Types for chart generation result
export interface ChartGenerationResult {
  success: boolean;
  chartConfig?: ChartConfig;
  rawResponse?: string;
  cleaned?: boolean;
  error?: string;
}

// Types for LLM model options
// Using the ModelOptions from llm-tool.ts

/**
 * Chart Generation Tool class
 */
export class ChartTool {
  /**
   * Static description of the tool and its usage
   * This helps AI agents understand how to use the tool effectively
   */
  static description = {
    name: "generateChart",
    description: "Generate data visualizations from natural language descriptions.",
    parameters: {
      type: "object",
      properties: {
        prompt: {
          type: "string",
          description: "Natural language description of the chart to generate."
        },
        chartType: {
          type: "string",
          enum: Object.values(CHART_TYPES),
          description: "Optional specific chart type to generate. If not provided, the LLM will determine the best chart type."
        },
        model: {
          type: "string",
          description: "The LLM model to use for processing the prompt.",
          default: "gpt-4o"
        },
        provider: {
          type: "string",
          description: "The LLM provider to use.",
          default: "openai"
        }
      },
      required: ["prompt"]
    }
  };

  /**
   * Generate a chart from a natural language prompt
   * @param options - Chart generation options
   * @returns - Chart configuration and data
   */
  async generateChart(options: ChartGenerationOptions): Promise<ChartGenerationResult> {
    try {
      const {
        prompt,
        chartType,
        model = "gpt-4o",
        provider = "openai"
      } = options;

      if (!prompt) {
        throw new Error("Prompt is required");
      }

      // Create a system prompt that instructs the LLM how to generate chart data
      const systemPrompt = this._createSystemPrompt(chartType);

      // Process the prompt with the LLM
      const llmResponse = await llmTool.processContent({
        prompt,
        context: systemPrompt,
        model,
        provider,
        modelOptions: {
          temperature: 0.7,
          maxTokens: 2000
        }
      });

      // Debug output for raw LLM response
      console.log("Raw LLM response:", llmResponse);

      // Parse the LLM response to extract chart configuration
      let parsedConfig = this._parseChartResponse(llmResponse);

      // Debug output for parsed config
      console.log("Parsed config:", JSON.stringify(parsedConfig, null, 2));

      // Validate the chart configuration using Zod schema
      try {
        const validatedConfig = this._validateChartConfig(parsedConfig);
        console.log("Validated config:", JSON.stringify(validatedConfig, null, 2));
        return {
          success: true,
          chartConfig: validatedConfig,
          rawResponse: llmResponse
        };
      } catch (validationError: any) {
        console.log("Validation error, attempting LLM-based cleanup:", validationError.message);
        // If validation fails, try to fix the JSON using LLM
        const cleanedConfig = await this._cleanupChartConfigWithLLM(parsedConfig, prompt, model, provider);
        // Validate again after cleanup
        const validatedConfig = this._validateChartConfig(cleanedConfig);
        return {
          success: true,
          chartConfig: validatedConfig,
          rawResponse: llmResponse,
          cleaned: true
        };
      }
    } catch (error: any) {
      console.error("Error generating chart:", error);
      return {
        success: false,
        error: error.message || "Failed to generate chart"
      };
    }
  }

  /**
   * Use LLM to validate and fix chart configuration
   * @param chartConfig - Parsed but potentially invalid chart config
   * @param originalPrompt - The original user prompt
   * @param model - LLM model to use for cleanup
   * @param provider - Provider to use for cleanup
   * @returns - Cleaned up chart configuration
   */
  async _cleanupChartConfigWithLLM(chartConfig: any, originalPrompt: string, model: string, provider: string): Promise<any> {
    console.log("Using LLM to clean up chart configuration");

    // Create a system prompt specifically for cleanup
    const cleanupSystemPrompt = `You are a JSON validation and repair expert.
    You will be given a JSON object that represents a chart configuration, but it may have issues.
    Your task is to fix any problems with the JSON while preserving the original intent.

    The original user request was: "${originalPrompt}"

    Here is the current chart configuration that needs to be fixed:
    ${JSON.stringify(chartConfig, null, 2)}

    Please analyze this configuration and fix any issues, such as:
    1. Missing required fields (chartType, title, data)
    2. Incorrect data structure for the specified chart type
    3. Invalid or inconsistent data values
    4. Structural problems that would prevent rendering

    Return ONLY the fixed JSON object with no additional text or explanation.
    Ensure the JSON is valid and follows the schema requirements for the chart type.
    `;

    // Use a different model for cleanup if the original model failed
    const cleanupModel = model === "gpt-4o" ? "claude-sonnet-4-0" : "gpt-4o";
    const cleanupProvider: LlmProvider = provider === "openai" ? "anthropic" : "openai";

    try {
      const cleanupResponse = await llmTool.processContent({
        prompt: "Fix this chart configuration JSON",
        context: cleanupSystemPrompt,
        model: cleanupModel,
        provider: cleanupProvider,
        modelOptions: {
          temperature: 0.1, // Lower temperature for more deterministic output
          maxTokens: 2000
        }
      });

      // Parse the cleaned up response
      const cleanedConfig = this._parseChartResponse(cleanupResponse);
      console.log("Successfully cleaned up chart configuration with LLM");
      return cleanedConfig;
    } catch (cleanupError) {
      console.error("LLM cleanup failed:", cleanupError);
      // If cleanup fails, return the original config and let the caller handle it
      return chartConfig;
    }
  }

  /**
   * Create a system prompt for the LLM based on the requested chart type
   * @param chartType - Optional specific chart type
   * @returns - System prompt for the LLM
   */
  _createSystemPrompt(chartType?: ChartType): string {
    const availableChartTypes = Object.values(CHART_TYPES).join(', ');

    return `You are a data visualization expert. Your task is to convert a natural language description into a complete chart configuration that can be rendered with Recharts, a React charting library.

${chartType ? `The user has requested a ${chartType} chart specifically.` : `Choose the most appropriate chart type from: ${availableChartTypes}.`}

Analyze the user's request and generate a complete JSON response with the following structure:

{
  "chartType": "the chart type (${availableChartTypes})",
  "title": "chart title",
  "subtitle": "optional subtitle",
  "data": [
    // Array of data points with appropriate structure for the chart type
    // For example, for a bar chart:
    { "name": "Category 1", "value": 100 },
    { "name": "Category 2", "value": 200 }
  ],
  "xAxis": {
    "label": "x-axis label",
    "dataKey": "the key in data objects to use for x-axis"
  },
  "yAxis": {
    "label": "y-axis label"
  },
  "colors": ["#hexcolor1", "#hexcolor2"], // Optional array of colors
  "legend": true, // Whether to show a legend
  "tooltip": true, // Whether to show tooltips
  "grid": true, // Whether to show grid lines
  "explanation": "A brief explanation of why this chart type was chosen and what insights it reveals"
}

IMPORTANT: YOUR RESPONSE WILL BE VALIDATED AGAINST A STRICT SCHEMA

JSON FORMATTING AND VALIDATION RULES:
1. Use double quotes for all strings and property names
2. Do not use trailing commas in arrays or objects
3. Ensure all brackets and braces are properly closed and matched
4. Do not include comments in the final JSON
5. Do not include any text outside the JSON object
6. Ensure all array elements are separated by commas
7. Ensure all object properties are separated by commas
8. DO NOT wrap the JSON in markdown code blocks or backtick tags
9. Return ONLY the raw JSON object with no additional formatting
10. All required fields must be present and have the correct types
11. For scatter plots, each data point MUST have numeric x and y properties
12. For tables, all column accessorKeys must exist in every data row
13. DO NOT include any explanatory text before or after the JSON
14. DO NOT include phrases like "Here's the JSON:" or "The chart configuration is:"
15. ONLY RETURN THE RAW JSON OBJECT AND NOTHING ELSE

For different chart types, adjust the data structure appropriately:
- Bar charts: data with name/category and value(s)
  Example: [{ "name": "Category 1", "value": 100 }, { "name": "Category 2", "value": 200 }]

- Line charts: data with x values (often time-based) and y values
  Example: [{ "name": "Jan", "value": 100 }, { "name": "Feb", "value": 200 }]

- Pie charts: data with name and value (representing portions of a whole)
  Example: [{ "name": "Segment 1", "value": 30 }, { "name": "Segment 2", "value": 70 }]

- Area charts: similar to line charts but with filled areas
  Example: [{ "name": "Jan", "value": 100 }, { "name": "Feb", "value": 200 }]

- Scatter plots: data with x and y coordinates
  Example: [{ "name": "Point 1", "x": 10, "y": 20 }, { "name": "Point 2", "x": 30, "y": 40 }]
  IMPORTANT: For scatter plots, each data point MUST have 'x' and 'y' properties as numeric values

- Radar charts: data with multiple dimensions/metrics
  Example: [{ "name": "Metric 1", "value": 80 }, { "name": "Metric 2", "value": 60 }]

- Composed charts: combination of multiple chart types (specify in configuration)
  Example: [{ "name": "Jan", "bar": 100, "line": 80, "area": 60 }, { "name": "Feb", "bar": 200, "line": 150, "area": 120 }]

- Flow charts: nodes and edges representing a directed graph or process flow
  Example structure:
  {
    "chartType": "flow",
    "title": "Simple Process Flow",
    "nodes": [
      { "id": "1", "position": { "x": 0, "y": 0 }, "data": { "label": "Start", "description": "Process begins here" } },
      { "id": "2", "position": { "x": 200, "y": 0 }, "data": { "label": "Process A" } },
      { "id": "3", "position": { "x": 400, "y": 0 }, "data": { "label": "End" } }
    ],
    "edges": [
      { "id": "e1-2", "source": "1", "target": "2", "label": "Next step" },
      { "id": "e2-3", "source": "2", "target": "3", "label": "Complete", "animated": true }
    ],
    "direction": "LR",
    "fitView": true,
    "explanation": "This flow chart shows a simple linear process."
  }
  IMPORTANT: For flow charts, each node must have a unique id and position, and each edge must have source and target ids that match existing nodes

- Heat maps: data visualized as a matrix with color intensity
  Example structure:
  {
    "chartType": "heatmap",
    "title": "Sales Performance by Region and Quarter",
    "data": [
      { "name": "North", "Q1": 45, "Q2": 62, "Q3": 78, "Q4": 56 },
      { "name": "South", "Q1": 52, "Q2": 43, "Q3": 36, "Q4": 49 },
      { "name": "East", "Q1": 38, "Q2": 43, "Q3": 71, "Q4": 86 },
      { "name": "West", "Q1": 74, "Q2": 81, "Q3": 79, "Q4": 92 }
    ],
    "xAxis": { "dataKey": "name", "label": "Region" },
    "colorScale": ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"],
    "showValues": true,
    "explanation": "This heat map shows sales performance across regions and quarters, with darker colors indicating higher values."
  }
  IMPORTANT: For heat maps, data should be structured as a matrix with rows and columns, where each cell contains a numeric value

- Bubble charts: scatter plots with an additional dimension shown by bubble size
  Example structure:
  {
    "chartType": "bubble",
    "title": "Product Comparison",
    "data": [
      { "name": "Product A", "x": 65, "y": 78, "z": 120, "category": "Electronics" },
      { "name": "Product B", "x": 42, "y": 53, "z": 80, "category": "Electronics" },
      { "name": "Product C", "x": 78, "y": 32, "z": 150, "category": "Home" },
      { "name": "Product D", "x": 55, "y": 47, "z": 200, "category": "Home" }
    ],
    "xAxis": { "label": "Price ($)", "dataKey": "x" },
    "yAxis": { "label": "Customer Rating" },
    "zAxis": { "label": "Sales Volume", "dataKey": "z", "range": [20, 100] },
    "colorKey": "category",
    "explanation": "This bubble chart compares products by price (x-axis), rating (y-axis), and sales volume (bubble size)."
  }
  IMPORTANT: For bubble charts, each data point must have x, y, and z values, where z determines the bubble size

- Tables: tabular data with columns and rows
  Example structure:
  {
    "chartType": "table",
    "title": "Sales Data by Region",
    "columns": [
      { "header": "Region", "accessorKey": "region", "type": "string", "title": "Region", "dataKey": "region" },
      { "header": "Q1 Sales", "accessorKey": "q1", "type": "number", "title": "Q1 Sales", "dataKey": "q1" },
      { "header": "Q2 Sales", "accessorKey": "q2", "type": "number", "title": "Q2 Sales", "dataKey": "q2" }
    ],
    "data": [
      { "region": "North", "q1": 12500, "q2": 14200 },
      { "region": "South", "q1": 9800, "q2": 10600 },
      { "region": "East", "q1": 15200, "q2": 16100 },
      { "region": "West", "q1": 8900, "q2": 9300 }
    ],
    "pagination": true,
    "rowsPerPage": 10,
    "explanation": "This table shows quarterly sales data by region."
  }

  IMPORTANT FOR TABLES:
  - ALWAYS include the "columns" property with proper headers for each column
  - Make column headers clear, concise, and descriptive
  - Use proper capitalization for column headers
  - For number columns, specify "type": "number" to ensure proper formatting
  - The "accessorKey" in each column must match the property name in the data objects
  - CRITICAL: Each column MUST have "accessorKey", "header", and "type" properties exactly as shown in the example
  - CRITICAL: Include both "title" and "dataKey" properties that match "header" and "accessorKey" respectively for compatibility

Use realistic, plausible data that matches the user's request. If the user doesn't specify exact values, create reasonable sample data that illustrates the concept they're asking for.

Ensure your response is valid JSON that can be parsed directly. Do not include any text outside the JSON object.`;
  }

  /**
   * Parse the LLM response to extract chart configuration
   * @param response - Raw LLM response
   * @returns - Parsed chart configuration
   */
  _parseChartResponse(response: string): any {
    try {
      console.log("Parsing LLM response:", response.substring(0, 100) + "...");

      // First, check for markdown code blocks with JSON
      const markdownJsonMatch = response.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (markdownJsonMatch && markdownJsonMatch[1]) {
        try {
          console.log("Found markdown code block, attempting to parse");
          return JSON.parse(markdownJsonMatch[1]);
        } catch (markdownError) {
          console.error("Error parsing markdown JSON:", markdownError);
          // Continue to other parsing methods
        }
      }

      // Next, try to find a JSON object in the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        // If no JSON object is found, try to extract from text that might contain instructions
        const lines = response.split('\n');
        let jsonLines: string[] = [];
        let inJson = false;

        for (const line of lines) {
          if (line.trim().startsWith('{') && !inJson) {
            inJson = true;
            jsonLines.push(line);
          } else if (inJson) {
            jsonLines.push(line);
            if (line.trim().endsWith('}')) {
              break;
            }
          }
        }

        if (jsonLines.length > 0) {
          const extractedJson = jsonLines.join('\n');
          console.log("Extracted potential JSON from lines:", extractedJson.substring(0, 100) + "...");
          try {
            return JSON.parse(extractedJson);
          } catch (extractError) {
            console.error("Error parsing extracted JSON:", extractError);
          }
        }

        throw new Error("No valid JSON found in the response");
      }

      let jsonStr = jsonMatch[0];
      let chartConfig;

      try {
        // Try to parse the JSON directly
        chartConfig = JSON.parse(jsonStr);
        console.log("Successfully parsed JSON directly");
      } catch (parseError) {
        // If direct parsing fails, try to fix common JSON issues
        console.log("Initial JSON parsing failed, attempting to fix JSON:", (parseError as Error).message);

        // Fix trailing commas in arrays and objects
        jsonStr = jsonStr.replace(/,\s*([\]\}])/g, '$1');

        // Fix missing commas between array elements or object properties
        jsonStr = jsonStr.replace(/("[^"]*"|\d+)\s*(\[)/g, '$1,$2');
        jsonStr = jsonStr.replace(/(\])\s*("[^"]*")/g, '$1,$2');

        // Fix unquoted property names
        jsonStr = jsonStr.replace(/(\{|,)\s*([a-zA-Z0-9_]+)\s*:/g, '$1"$2":');

        // Fix single quotes used instead of double quotes
        jsonStr = jsonStr.replace(/'/g, '"');

        // Remove any non-ASCII characters that might cause issues
        jsonStr = jsonStr.replace(/[^\x00-\x7F]/g, '');

        // Remove any comments (both // and /* */ style)
        jsonStr = jsonStr.replace(/\/\/.*?\n/g, '\n');
        jsonStr = jsonStr.replace(/\/\*[\s\S]*?\*\//g, '');

        // Try parsing again with the fixed JSON
        try {
          chartConfig = JSON.parse(jsonStr);
          console.log("Successfully fixed and parsed JSON");
        } catch (fixedParseError) {
          // If still failing, try a more aggressive approach - use a JSON5 like approach
          console.log("Fixed JSON still failed to parse, using fallback method");

          // Last resort: Use Function constructor as a more lenient parser (similar to JSON5)
          // This is safe in this context as we're only parsing, not executing arbitrary code
          try {
            // Convert to a JavaScript object literal and evaluate it
            const fallbackParse = new Function('return ' + jsonStr);
            chartConfig = fallbackParse();
            console.log("Successfully parsed using fallback method");
          } catch (fallbackError) {
            console.error("All parsing methods failed", fallbackError);
            throw new Error(`Could not parse JSON after multiple attempts: ${(fallbackError as Error).message}`);
          }
        }
      }

      // Validate the chart configuration
      this._validateChartConfig(chartConfig);

      return chartConfig;
    } catch (error: any) {
      console.error("Error parsing chart response:", error);
      throw new Error(`Failed to parse chart configuration: ${error.message}`);
    }
  }

  /**
   * Validate the chart configuration using Zod schemas
   * @param config - Chart configuration to validate
   * @returns - Validated chart configuration
   * @throws {Error} If the configuration is invalid
   */
  _validateChartConfig(config: any): ChartConfig {
    try {
      // Parse and validate the configuration using the appropriate Zod schema
      const validatedConfig = ChartConfigSchema.parse(config);

      // Additional validation for specific chart types
      if (validatedConfig.chartType === CHART_TYPES.TABLE && 'columns' in validatedConfig && Array.isArray(validatedConfig.columns)) {
        // For tables, ensure all column accessorKeys exist in every data row
        const columnKeys = validatedConfig.columns.map(col => col.accessorKey);

        // Add title and dataKey properties for compatibility with different components
        validatedConfig.columns = validatedConfig.columns.map(col => ({
          ...col,
          title: col.title || col.header,
          dataKey: col.dataKey || col.accessorKey
        }));

        validatedConfig.data.forEach((row: any, idx: number) => {
          const rowKeys = Object.keys(row);
          const missingKeys = columnKeys.filter(key => !rowKeys.includes(key));
          if (missingKeys.length > 0) {
            throw new Error(`Table row at index ${idx} is missing properties: ${missingKeys.join(', ')}`);
          }
        });
      } else if (validatedConfig.chartType === CHART_TYPES.COMPOSED) {
        // For composed charts, ensure data has appropriate properties for each chart type
        const firstDataPoint = validatedConfig.data[0];
        const dataKeys = Object.keys(firstDataPoint).filter(key => key !== 'name');
        if (dataKeys.length === 0) {
          throw new Error('Composed chart must have at least one data series');
        }
      }

      return validatedConfig;
    } catch (error: any) {
      // Handle Zod validation errors
      if (error.errors) {
        const formattedErrors = error.errors.map((err: any) => {
          return `${err.path.join('.')}: ${err.message}`;
        }).join('\n');
        throw new Error(`Chart configuration validation failed:\n${formattedErrors}`);
      }
      throw error;
    }
  }

  /**
   * Get the tool description for use by AI agents
   * @returns Tool description in a standardized format
   */
  getDescription(): typeof ChartTool.description {
    return ChartTool.description;
  }

  /**
   * Get all available tool methods with their descriptions
   * @returns Map of method names to their descriptions
   */
  getAvailableMethods(): Record<string, string> {
    return {
      generateChart: "Generate a chart from a natural language prompt"
    };
  }
}

// Export a singleton instance
export const chartTool = new ChartTool();