#!/usr/bin/env node

/**
 * PMO Hierarchical Migration Script
 * 
 * This script helps migrate existing PMO records from flat structure to hierarchical structure
 * and provides utilities for testing and validation.
 * 
 * Usage:
 *   node scripts/pmo-hierarchical-migration.js [command] [options]
 * 
 * Commands:
 *   migrate [userId] [--dry-run]     - Migrate all PMO records for a user
 *   validate [userId] [pmoId]        - Validate hierarchical structure
 *   test [userId] [pmoId]            - Test hierarchical data access
 *   help                             - Show this help message
 */

const { migratePMOToHierarchical, migrateAllPMOsToHierarchical, validateHierarchicalStructure } = require('../lib/firebase/pmoMigration');
const { getHierarchicalPMOData, addProjectTaskMapping, getProjectTasks } = require('../lib/firebase/pmoHierarchical');

// Default user for testing
const DEFAULT_USER = '<EMAIL>';

async function showHelp() {
  console.log(`
PMO Hierarchical Migration Script

Commands:
  migrate [userId] [--dry-run]     - Migrate all PMO records for a user to hierarchical structure
  migrate-single [userId] [pmoId] [--dry-run] - Migrate a single PMO record
  validate [userId] [pmoId]        - Validate hierarchical structure of a PMO record
  test [userId] [pmoId]            - Test hierarchical data access for a PMO record
  list [userId]                    - List all PMO records for a user
  help                             - Show this help message

Options:
  --dry-run                        - Simulate migration without making changes
  --verbose                        - Show detailed output

Examples:
  node scripts/pmo-hierarchical-migration.js migrate ${DEFAULT_USER} --dry-run
  node scripts/pmo-hierarchical-migration.js validate ${DEFAULT_USER} c76670a7-bc7b-44ea-9905-189a4bcf36c8
  node scripts/pmo-hierarchical-migration.js test ${DEFAULT_USER} c76670a7-bc7b-44ea-9905-189a4bcf36c8
`);
}

async function migrateAllCommand(userId, options = {}) {
  console.log(`🚀 ${options.dryRun ? 'Simulating' : 'Executing'} migration for user: ${userId}`);
  console.log('=' .repeat(60));

  try {
    const result = await migrateAllPMOsToHierarchical(userId, options.dryRun);
    
    console.log(`\n📊 Migration Results:`);
    console.log(`  Success: ${result.success}`);
    console.log(`  Migrated PMOs: ${result.migratedPMOs}`);
    console.log(`  Errors: ${result.errors.length}`);
    
    if (result.details.length > 0) {
      console.log(`\n📋 Details:`);
      result.details.forEach(detail => {
        const status = detail.status === 'success' ? '✅' : '❌';
        console.log(`  ${status} PMO ${detail.pmoId}: ${detail.projectCount} projects, ${detail.taskCount} tasks`);
        if (detail.error) {
          console.log(`      Error: ${detail.error}`);
        }
      });
    }
    
    if (result.errors.length > 0) {
      console.log(`\n❌ Errors:`);
      result.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }
    
    console.log(`\n${result.success ? '✅ Migration completed successfully!' : '❌ Migration completed with errors.'}`);
    
  } catch (error) {
    console.error(`❌ Migration failed:`, error);
    process.exit(1);
  }
}

async function migrateSingleCommand(userId, pmoId, options = {}) {
  console.log(`🚀 ${options.dryRun ? 'Simulating' : 'Executing'} migration for PMO: ${pmoId}`);
  console.log('=' .repeat(60));

  try {
    const result = await migratePMOToHierarchical(userId, pmoId, options.dryRun);
    
    if (result.success) {
      console.log(`✅ Migration successful!`);
      if (result.details) {
        console.log(`  Projects: ${result.details.projectCount}`);
        console.log(`  Tasks: ${result.details.taskCount}`);
        
        if (options.verbose && result.details.mapping) {
          console.log(`\n📋 Hierarchical Mapping:`);
          Object.entries(result.details.mapping).forEach(([projectId, projectData]) => {
            console.log(`  📁 Project: ${projectId}`);
            console.log(`    Name: ${projectData.projectInfo?.name || 'Unknown'}`);
            console.log(`    Tasks: ${projectData.taskIds.length}`);
            if (options.verbose && projectData.taskIds.length > 0) {
              projectData.taskIds.forEach(taskId => {
                console.log(`      - ${taskId}`);
              });
            }
          });
        }
      }
    } else {
      console.log(`❌ Migration failed: ${result.error}`);
    }
    
  } catch (error) {
    console.error(`❌ Migration failed:`, error);
    process.exit(1);
  }
}

async function validateCommand(userId, pmoId) {
  console.log(`🔍 Validating hierarchical structure for PMO: ${pmoId}`);
  console.log('=' .repeat(60));

  try {
    const result = await validateHierarchicalStructure(userId, pmoId);
    
    console.log(`\n📊 Validation Results:`);
    console.log(`  Valid: ${result.isValid ? '✅' : '❌'}`);
    console.log(`  Has Hierarchical Structure: ${result.details.hasHierarchicalStructure ? '✅' : '❌'}`);
    console.log(`  Has Legacy Structure: ${result.details.hasLegacyStructure ? '✅' : '❌'}`);
    console.log(`  Projects: ${result.details.projectCount}`);
    console.log(`  Tasks: ${result.details.taskCount}`);
    
    if (result.details.orphanedTasks.length > 0) {
      console.log(`  Orphaned Tasks: ${result.details.orphanedTasks.length}`);
      console.log(`    ${result.details.orphanedTasks.join(', ')}`);
    }
    
    if (result.issues.length > 0) {
      console.log(`\n⚠️ Issues Found:`);
      result.issues.forEach(issue => {
        console.log(`  - ${issue}`);
      });
    }
    
    console.log(`\n${result.isValid ? '✅ Structure is valid!' : '❌ Structure has issues.'}`);
    
  } catch (error) {
    console.error(`❌ Validation failed:`, error);
    process.exit(1);
  }
}

async function testCommand(userId, pmoId) {
  console.log(`🧪 Testing hierarchical data access for PMO: ${pmoId}`);
  console.log('=' .repeat(60));

  try {
    const result = await getHierarchicalPMOData(userId, pmoId);
    
    if (result.success && result.data) {
      console.log(`✅ Successfully retrieved hierarchical data!`);
      console.log(`  Projects found: ${result.data.length}`);
      
      result.data.forEach((projectData, index) => {
        console.log(`\n📁 Project ${index + 1}: ${projectData.projectId}`);
        if (projectData.projectInfo) {
          console.log(`    Name: ${projectData.projectInfo.name}`);
          console.log(`    Status: ${projectData.projectInfo.status}`);
          console.log(`    Created: ${projectData.projectInfo.createdAt}`);
        }
        console.log(`    Tasks: ${projectData.taskIds.length}`);
        
        if (projectData.taskIds.length > 0) {
          console.log(`    Task IDs: ${projectData.taskIds.slice(0, 3).join(', ')}${projectData.taskIds.length > 3 ? '...' : ''}`);
          
          // Test getting tasks for this specific project
          console.log(`\n🔍 Testing getProjectTasks for project ${projectData.projectId}...`);
          const projectTasksResult = await getProjectTasks(userId, pmoId, projectData.projectId);
          
          if (projectTasksResult.success) {
            console.log(`    ✅ getProjectTasks successful: ${projectTasksResult.taskIds?.length || 0} tasks`);
          } else {
            console.log(`    ❌ getProjectTasks failed: ${projectTasksResult.error}`);
          }
        }
      });
    } else {
      console.log(`❌ Failed to retrieve hierarchical data: ${result.error}`);
    }
    
  } catch (error) {
    console.error(`❌ Test failed:`, error);
    process.exit(1);
  }
}

async function listCommand(userId) {
  console.log(`📋 Listing PMO records for user: ${userId}`);
  console.log('=' .repeat(60));

  try {
    // This would require importing the PMO collection utilities
    // For now, we'll show a placeholder
    console.log(`⚠️ List command not yet implemented. Please use Firebase console to view PMO records.`);
    console.log(`   Collection path: users/${userId}/PMO`);
    
  } catch (error) {
    console.error(`❌ List failed:`, error);
    process.exit(1);
  }
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === 'help') {
    await showHelp();
    return;
  }
  
  const command = args[0];
  const options = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose')
  };
  
  // Remove flags from args
  const cleanArgs = args.filter(arg => !arg.startsWith('--'));
  
  switch (command) {
    case 'migrate':
      const userId = cleanArgs[1] || DEFAULT_USER;
      await migrateAllCommand(userId, options);
      break;
      
    case 'migrate-single':
      const singleUserId = cleanArgs[1] || DEFAULT_USER;
      const pmoId = cleanArgs[2];
      if (!pmoId) {
        console.error('❌ PMO ID is required for migrate-single command');
        process.exit(1);
      }
      await migrateSingleCommand(singleUserId, pmoId, options);
      break;
      
    case 'validate':
      const validateUserId = cleanArgs[1] || DEFAULT_USER;
      const validatePmoId = cleanArgs[2];
      if (!validatePmoId) {
        console.error('❌ PMO ID is required for validate command');
        process.exit(1);
      }
      await validateCommand(validateUserId, validatePmoId);
      break;
      
    case 'test':
      const testUserId = cleanArgs[1] || DEFAULT_USER;
      const testPmoId = cleanArgs[2];
      if (!testPmoId) {
        console.error('❌ PMO ID is required for test command');
        process.exit(1);
      }
      await testCommand(testUserId, testPmoId);
      break;
      
    case 'list':
      const listUserId = cleanArgs[1] || DEFAULT_USER;
      await listCommand(listUserId);
      break;
      
    default:
      console.error(`❌ Unknown command: ${command}`);
      await showHelp();
      process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = {
  migrateAllCommand,
  migrateSingleCommand,
  validateCommand,
  testCommand,
  listCommand
};
