//legacy SideBar

"use client"

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { useRouter, useParams } from 'next/navigation';
import {
  collection,
  addDoc,
  getDocs,
  query,
  where,
  limit,
  serverTimestamp,
  doc,
  getDoc,
} from 'firebase/firestore';
import {
  ArrowUpTrayIcon,
  BookOpenIcon,
  XMarkIcon,
} from '@heroicons/react/24/solid';
import { FolderOpen, ImageIcon } from 'lucide-react';
import { db } from 'components/firebase';
import MenuSection from './Menus/MenuSection';
import DragDropFiles from './drag-drop-files';


interface DocumentData {
  id: string;
  name: string;
}

interface ChatData {
  id: string;
  namespace: string;
  category: string;
  firstMessage: string;
  createdAt: {
    seconds: number;
    nanoseconds: number;
  } | string;
}

interface FileData {
  name: string;
  category: string;
}

export default function SideBar({ onClose }: { onClose?: () => void }) {
  const { data: session } = useSession();
  const router = useRouter();
  const params = useParams();

  // State definitions
  const [unknown, setUnknown] = useState<DocumentData[]>([]);
  const [groups, setGroups] = useState<DocumentData[]>([]);

  const [filesSelectedDocId, setFilesSelectedDocId] = useState<string | null>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('filesSelectedDocId') || null;
    }
    return null;
  });
  
  const [filesSelectedDocName, setFilesSelectedDocName] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('filesSelectedDocName') || '';
    }
    return '';
  });

  const [groupsSelectedDocId, setGroupsSelectedDocId] = useState<string | null>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('groupsSelectedDocId') || null;
    }
    return null;
  });
  
  const [groupsSelectedDocName, setGroupsSelectedDocName] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('groupsSelectedDocName') || '';
    }
    return '';
  });

  const [filesChats, setFilesChats] = useState<ChatData[]>([]);
  const [groupsChats, setGroupsChats] = useState<ChatData[]>([]);
  const [filesLoading, setFilesLoading] = useState(false);
  const [groupsLoading, setGroupsLoading] = useState(false);
  const [filesError, setFilesError] = useState<string | null>(null);
  const [groupsError, setGroupsError] = useState<string | null>(null);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);

  const [expanded, setExpanded] = useState(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('sidebarExpanded');
      return saved ? JSON.parse(saved) : {
        files: true,
        groups: true,
        allFiles: false,
      };
    }
    return {
      files: true,
      groups: true,
      allFiles: false,
    };
  });

  const [selectedDisplayName, setSelectedDisplayName] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('selectedDisplayName') || 'None';
    }
    return 'None';
  });
  
  const [selectedType, setSelectedType] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('selectedType') || '';
    }
    return '';
  });

  // Save states to localStorage
  useEffect(() => {
    if (filesSelectedDocId !== null) {
      localStorage.setItem('filesSelectedDocId', filesSelectedDocId);
    }
    if (filesSelectedDocName) {
      localStorage.setItem('filesSelectedDocName', filesSelectedDocName);
    }
    if (groupsSelectedDocId !== null) {
      localStorage.setItem('groupsSelectedDocId', groupsSelectedDocId);
    }
    if (groupsSelectedDocName) {
      localStorage.setItem('groupsSelectedDocName', groupsSelectedDocName);
    }
  }, [filesSelectedDocId, filesSelectedDocName, groupsSelectedDocId, groupsSelectedDocName]);

  useEffect(() => {
    if (params?.id) {
      setSelectedChatId(params.id as string);
    }
  }, [params?.id]);

  useEffect(() => {
    localStorage.setItem('sidebarExpanded', JSON.stringify(expanded));
  }, [expanded]);

  useEffect(() => {
    localStorage.setItem('selectedDisplayName', selectedDisplayName);
    localStorage.setItem('selectedType', selectedType);
  }, [selectedDisplayName, selectedType]);

  useEffect(() => {
    const fetchDocuments = async () => {
      if (session?.user?.email) {
        try {
          const filesCollection = collection(db, 'users', session.user.email, 'files');

          // Fetch Unknown category files
          const unknownQueryRef = query(filesCollection, where('category', '==', 'Unknown'));
          const unknownSnapshot = await getDocs(unknownQueryRef);
          const unknownDocs: DocumentData[] = unknownSnapshot.docs
            .map((doc) => ({
              id: doc.id,
              name: doc.data()['name'] || 'untitled',
            }))
            .sort((a, b) => a.name.localeCompare(b.name));
          setUnknown(unknownDocs);

          // Fetch all files to get categories
          const filesSnapshot = await getDocs(filesCollection);
          const categories = new Set<string>();
          
          // Collect all unique categories
          filesSnapshot.docs.forEach(doc => {
            const category = doc.data().category;
            if (category && category !== 'Unknown') {
              categories.add(category);
            }
          });

          // Convert categories to group documents
          const groupDocs = Array.from(categories)
            .map(category => ({
              id: category,
              name: category,
            }))
            .sort((a, b) => a.name.localeCompare(b.name));

          setGroups(groupDocs);

          // Restore chats for selected documents
          if (filesSelectedDocId) {
            fetchChats(filesSelectedDocId, 'Files');
          }
          if (groupsSelectedDocId) {
            fetchChats(groupsSelectedDocId, 'Groups');
          }
        } catch (err) {
          console.error('Error fetching documents:', err);
        }
      }
    };

    fetchDocuments();
  }, [session?.user?.email]);

  // Continue in Part 2...

  const fetchNamespaceForGroup = async (groupName: string): Promise<string | null> => {
    if (!session?.user?.email) return null;

    try {
      const filesQuery = query(
        collection(db, 'users', session.user.email, 'files'),
        where('category', '==', groupName)
      );
      const filesSnapshot = await getDocs(filesQuery);
      
      if (!filesSnapshot.empty) {
        // Return the ID of the first file in the category
        return filesSnapshot.docs[0].id;
      }
      return null;
    } catch (error) {
      console.error('Error fetching namespace for group:', error);
      return null;
    }
  };


  const fetchFileDocumentIdByCategory = async (
    category: string
  ): Promise<string | null> => {
    if (!session?.user?.email) return null;

    try {
      const filesQuery = query(
        collection(db, 'users', session.user.email, 'files'),
        where('category', '==', category),
        limit(1)
      );
      const filesSnapshot = await getDocs(filesQuery);
      
      if (!filesSnapshot.empty) {
        return filesSnapshot.docs[0].id;
      }
      return null;
    } catch (error) {
      console.error('Error fetching file document ID for category:', error);
      return null;
    }
  };

  const createNewChat = async (menuName: string) => {
    if (!session?.user?.email) {
      const error = 'User email is missing.';
      menuName === 'Groups' ? setGroupsError(error) : setFilesError(error);
      return;
    }
  
    try {
      if (menuName === 'Groups') {
        setGroupsLoading(true);
        setGroupsError(null);
      } else {
        setFilesLoading(true);
        setFilesError(null);
      }
  
      let fileDocumentId: string | undefined;
  
      if (menuName === 'Groups') {
        if (!groupsSelectedDocName) {
          setGroupsError('Please select a group before creating a new chat.');
          return;
        }
  
        // Direct query for files in the selected category
        const filesQuery = query(
          collection(db, 'users', session.user.email, 'files'),
          where('category', '==', groupsSelectedDocName)
        );
  
        const filesSnapshot = await getDocs(filesQuery);
        
        // Debug logging for troubleshooting
        console.log(`Found ${filesSnapshot.size} files in category ${groupsSelectedDocName}`);
        
        if (!filesSnapshot.empty) {
          // Get the first file document from the category
          const firstDoc = filesSnapshot.docs[0];
          fileDocumentId = firstDoc.id;
          
          // Debug logging
          console.log('Using file as namespace:', {
            id: fileDocumentId,
            category: firstDoc.data().category
          });
        } else {
          console.error('No files found in category:', groupsSelectedDocName);
          setGroupsError('No files found in this group');
          return;
        }
      } else if (menuName === 'Files') {
        if (!filesSelectedDocId) {
          setFilesError('Please select a file before creating a new chat.');
          return;
        }
        fileDocumentId = filesSelectedDocId;
      }
  
      if (!fileDocumentId) {
        const error = 'No valid file found to associate with chat.';
        menuName === 'Groups' ? setGroupsError(error) : setFilesError(error);
        return;
      }
  
      // Create chat document with the fileDocumentId
      const chatDocRef = await addDoc(
        collection(db, 'users', session.user.email, 'chats'),
        {
          userId: session.user.email,
          createdAt: serverTimestamp(),
          fileDocumentId: fileDocumentId,
          firstMessage: 'Begin messaging...',
          category: menuName === 'Groups' ? groupsSelectedDocName : 'Unknown'
        }
      );
  
      // Create new chat object for state update
      const newChat = {
        id: chatDocRef.id,
        namespace: fileDocumentId,
        category: menuName === 'Groups' ? groupsSelectedDocName : 'Unknown',
        firstMessage: 'Begin messaging...',
        createdAt: new Date().toISOString()
      };
  
      // Update UI state
      if (menuName === 'Groups') {
        setGroupsChats(prevChats => [newChat, ...prevChats]);
      } else {
        setFilesChats(prevChats => [newChat, ...prevChats]);
      }
  
      // Navigate to new chat
      setSelectedChatId(chatDocRef.id);
      router.push(`/chat/${chatDocRef.id}`);
  
    } catch (err) {
      console.error('Error creating new chat:', err);
      const error = 'Failed to create new chat. Please try again.';
      menuName === 'Groups' ? setGroupsError(error) : setFilesError(error);
    } finally {
      if (menuName === 'Groups') {
        setGroupsLoading(false);
      } else {
        setFilesLoading(false);
      }
    }
  };

  const fetchChats = async (docId: string, menuName: string) => {
    if (!session?.user?.email) return;

    if (menuName === 'Groups') {
      setGroupsLoading(true);
      setGroupsError(null);
    } else {
      setFilesLoading(true);
      setFilesError(null);
    }

    try {
      let namespaceId: string | null = null;

      if (menuName === 'Groups') {
        namespaceId = await fetchNamespaceForGroup(docId);
        if (!namespaceId) {
          // This is not an error condition, just means no files in this category have chats yet
          if (menuName === 'Groups') {
            setGroupsChats([]);
            setGroupsLoading(false);
          } else {
            setFilesChats([]);
            setFilesLoading(false);
          }
          return;
        }
      } else {
        namespaceId = docId;
      }

      const chatQuery = query(
        collection(db, 'users', session.user.email, 'chats'),
        where('fileDocumentId', '==', namespaceId)
      );

      const querySnapshot = await getDocs(chatQuery);
      const fetchedChats: ChatData[] = querySnapshot.docs.map((doc) => {
        const data = doc.data();
        let createdAt: ChatData['createdAt'];
        
        if (data.createdAt && 'seconds' in data.createdAt) {
          createdAt = {
            seconds: data.createdAt.seconds,
            nanoseconds: data.createdAt.nanoseconds
          };
        } else if (data.createdAt) {
          createdAt = data.createdAt;
        } else {
          createdAt = new Date().toISOString();
        }

        return {
          id: doc.id,
          namespace: data.fileDocumentId,
          category: data.category || '',
          firstMessage: data.firstMessage || '',
          createdAt
        };
      });

      // Set empty array if no chats found (not an error condition)
      if (menuName === 'Groups') {
        setGroupsChats(fetchedChats);
      } else {
        setFilesChats(fetchedChats);
      }
    } catch (error) {
      console.error('Error fetching chats:', error);
      const errorMessage = 'Failed to fetch chats. Please try again.';
      if (menuName === 'Groups') {
        setGroupsError(errorMessage);
      } else {
        setFilesError(errorMessage);
      }
    } finally {
      if (menuName === 'Groups') {
        setGroupsLoading(false);
      } else {
        setFilesLoading(false);
      }
    }
  };

  const handleDocumentChange = async (newSelectedId: string, menuName: string) => {
    if (menuName === 'Groups') {
      const selectedDoc = groups.find((group) => group.id === newSelectedId);
      if (selectedDoc) {
        setGroupsSelectedDocId(newSelectedId);
        setGroupsSelectedDocName(selectedDoc.name);
        setSelectedDisplayName(selectedDoc.name);
        setSelectedType('Group');
        await fetchChats(newSelectedId, menuName);
      }
    } else if (menuName === 'Files') {
      const selectedDoc = unknown.find((file) => file.id === newSelectedId);
      if (selectedDoc) {
        setFilesSelectedDocId(newSelectedId);
        setFilesSelectedDocName(selectedDoc.name);
        setSelectedDisplayName(selectedDoc.name);
        setSelectedType('File');
        await fetchChats(newSelectedId, menuName);
      }
    }
  };

  const handleSelectChat = async (chatId: string | null, menuName: string) => {
    if (!session?.user?.email || !chatId) {
      if (!chatId) {
        router.push('/fileManager');
      }
      return;
    }

    try {
      // Fetch the chat document to get the fileDocumentId
      const chatDocRef = doc(db, 'users', session.user.email, 'chats', chatId);
      const chatDoc = await getDoc(chatDocRef);
      
      if (!chatDoc.exists()) {
        console.error('Chat document not found');
        return;
      }

      const chatData = chatDoc.data();
      const fileDocumentId = chatData.fileDocumentId;

      // Fetch the file document to get its details
      const fileDocRef = doc(db, 'users', session.user.email, 'files', fileDocumentId);
      const fileDoc = await getDoc(fileDocRef);

      if (!fileDoc.exists()) {
        console.error('File document not found');
        return;
      }

      const fileData = fileDoc.data() as FileData;

      // Update states based on menu type
      if (menuName === 'Groups') {
        setGroupsSelectedDocId(fileData.category);
        setGroupsSelectedDocName(fileData.category);
        setSelectedDisplayName(fileData.category);
        setSelectedType('Group');
      } else {
        setFilesSelectedDocId(fileDocumentId);
        setFilesSelectedDocName(fileData.name);
        setSelectedDisplayName(fileData.name);
        setSelectedType('File');
      }

      // Update selected chat and navigate
      setSelectedChatId(chatId);
      router.push(`/chat/${chatId}`);

    } catch (error) {
      console.error('Error in handleSelectChat:', error);
    }
  };


  const handleDeleteChat = async (id: string) => {
    if (!session?.user?.email) {
      setGroupsError('User not authenticated.');
      setFilesError('User not authenticated.');
      return;
    }

    try {
      setGroupsChats((prevChats) => prevChats.filter((chat) => chat.id !== id));
      
      setFilesChats((prevChats) => prevChats.filter((chat) => chat.id !== id));

      if (selectedChatId === id) {
        setFilesSelectedDocId(null);
        setFilesSelectedDocName('');
        setGroupsSelectedDocId(null);
        setGroupsSelectedDocName('');
        router.push('/fileManager');
      }
    } catch (error) {
      console.error('Error deleting chat:', error);
      setGroupsError('Failed to delete chat. Please try again.');
      setFilesError('Failed to delete chat. Please try again.');
    }
  };

  const toggleMenu = (menuName: string) => {
    setExpanded((prev: { [x: string]: any; }) => ({
      ...prev,
      [menuName]: !prev[menuName as keyof typeof prev]
    }));
  };

  return (
    <div className="flex flex-col h-screen bg-ike-dark-purple text-xs p-2 text-gray-700 relative">
      <div className="md:hidden absolute top-2 right-2">
        <button
          onClick={onClose}
          className="p-2 text-ike-purple hover:text-blue-200 mt-2"
          aria-label="Close sidebar"
        >
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      <div className="sidebar-wrapper w-54">
        <div className="text-xl flex flex-row text-center items-center ml-2">
          <Link href="/">
            <img
              src="/logo5b.png"
              alt="PDF Reader Logo"
              className="h-12 w-auto mb-1 md:mb-0 md:mr-0 hover:opacity-80 transition-opacity duration-300"
            />
          </Link>
        </div>

        {/* Navigation buttons */}
        <div className="flex flex-row justify-center bg-gray-300 bg-opacity-65 border border-slate-600 items-center text-center mt-5 space-x-1 border-t-2 rounded-lg h-16">
          <Link href={filesSelectedDocId ? `/docViewers/${filesSelectedDocId}` : '#'}>
            <button
              aria-label="Open Document Viewer"
              className="py-2 px-4 flex items-center text-ike-purple hover:text-blue-200"
            >
              <BookOpenIcon className="h-6 w-6" />
            </button>
          </Link>

          <Link href="/imageGallery">
            <button
              aria-label="Open Image Gallery"
              className="py-2 px-4 flex items-center text-ike-purple hover:text-blue-200"
            >
              <ImageIcon className="h-6 w-6" />
            </button>
          </Link>
          <Link href="/dashboard/Upload">
            <button
              aria-label="Open Upload Page"
              className="py-2 px-4 flex items-center text-ike-purple hover:text-blue-200"
            >
              <ArrowUpTrayIcon className="h-6 w-6" />
            </button>
          </Link>
          <Link href="/fileManager">
            <button
              aria-label="Go to File Manager"
              className="py-2 px-4 flex items-center text-ike-purple hover:text-blue-200"
            >
              <FolderOpen className="h-6 w-6" />
            </button>
          </Link>
        </div>

        {/* Selected file/group display */}
        <div className='mt-5 mb-2 text-xs ml-2 text-blue-200 flex items-center'>
          <span className="whitespace-nowrap">Selected {selectedType}:</span>
          <div className="text-xs text-amber-400 ml-2 truncate w-full flex items-center whitespace-nowrap">
            <span className="truncate" style={{ maxWidth: 'calc(100% - 50px)' }}>
              {selectedDisplayName.length > 15 
                ? selectedDisplayName.slice(0, 12) + '...' 
                : selectedDisplayName}
            </span>
            {selectedDisplayName.length > 15 && (
              <span className="text-gray-400 ml-1">
                {selectedDisplayName.slice(-3)}
              </span>
            )}
          </div>
        </div>

        {/* Menu sections */}
        <MenuSection
          menuName="Files"
          toggleMenu={() => toggleMenu('files')}
          expanded={expanded.files}
          documents={unknown}
          selectedDocId={filesSelectedDocId}
          handleDocumentChange={handleDocumentChange}
          createNewChat={() => createNewChat('Files')}
          loading={filesLoading}
          error={!!filesError}
          chats={filesChats}
          selectedChatId={selectedChatId}
          handleSelectChat={handleSelectChat}
          handleDeleteChat={handleDeleteChat}
          isFirst={true}
          isLast={false}
          additionalClasses="mb-2"
        />

        <MenuSection
          menuName="Groups"
          toggleMenu={() => toggleMenu('groups')}
          expanded={expanded.groups}
          documents={groups}
          selectedDocId={groupsSelectedDocId}
          handleDocumentChange={handleDocumentChange}
          createNewChat={() => createNewChat('Groups')}
          loading={groupsLoading}
          error={!!groupsError}
          chats={groupsChats}
          selectedChatId={selectedChatId}
          handleSelectChat={handleSelectChat}
          handleDeleteChat={handleDeleteChat}
          isFirst={false}
          isLast={true}
          additionalClasses="mb-2"
        />

        {/* All Files section */}
        <div className="bg-gray-300 mt-4 rounded-lg bg-opacity-65 pt-2 pl-2 pb-3 pr-2 border-b border-gray-800">
                <DragDropFiles 
          expanded={expanded.allFiles} 
          toggleMenu={toggleMenu}
        />

      </div>




      </div>

      {/* Footer */}
      <div className="flex-grow flex flex-col pt-2 pl-2">
        <div className="text-sm border-t-2 border-gray-800">
          Current Index:{' '}
          <span className="text-sx text-gray-700">
            {process.env.NEXT_PUBLIC_PINECONE_INDEX || 'Not Set'}
          </span>
        </div>
      </div>
    </div>
  );
}