// app/api/optimize-pmo-prompt/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { simplePromptOptimizer, SimplePromptOptimizerResult } from '../../../lib/tools/simplePromptOptimizer'; // Adjust path

export async function POST(
  req: NextRequest
): Promise<NextResponse<SimplePromptOptimizerResult | { error: string }>> {
  try {
    const body = await req.json();
    const { originalPrompt } = body;

    if (!originalPrompt || typeof originalPrompt !== 'string') {
      return NextResponse.json({ error: 'Missing or invalid originalPrompt' }, { status: 400 });
    }

    const result = await simplePromptOptimizer.optimizePrompt({
      originalPrompt,
      includeExplanation: false, // For this use case, we likely just need the prompt
      modelOptions: { temperature: 0.5 } // Default or specific options
    });

    if (result.success) {
      return NextResponse.json(result);
    } else {
      // If optimizer itself caught an error and returned success: false
      return NextResponse.json(
        {
          success: false,
          optimizedPrompt: originalPrompt,
          error: result.error || "Optimization failed but returned success:false"
        },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('API Error optimizing PMO prompt:', error);
    return NextResponse.json(
      {
        success: false,
        optimizedPrompt: '',
        error: error.message || 'Internal server error during optimization'
      },
      { status: 500 }
    );
  }
}