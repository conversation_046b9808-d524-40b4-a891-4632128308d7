/**
 * Test script for investigative research prompt refinement
 */

const testCases = [
  {
    title: "Corporate Financial Investigation",
    description: "Look into potential financial irregularities in vendor contracts",
    expectedRefinements: [
      "investigation objectives",
      "evidence types",
      "verification methodology",
      "fact-checking protocols"
    ]
  },
  {
    title: "Whistleblower Investigation",
    description: "Investigate allegations of corporate misconduct",
    expectedRefinements: [
      "source protection",
      "evidence verification",
      "ethical guidelines",
      "reporting standards"
    ]
  }
];

async function testInvestigativeRefinement() {
  console.log("🔍 Testing Investigative Research Prompt Refinement\n");
  
  for (const testCase of testCases) {
    try {
      console.log(`📋 Testing: ${testCase.title}`);
      console.log(`📝 Original: ${testCase.description}`);
      
      const response = await fetch('http://localhost:3000/api/llm-comparison', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'optimize',
          prompt: testCase.description,
          optimizationType: 'investigative-research',
          criteria: {
            investigativeClarity: 'Clear investigation objectives and scope',
            evidenceSpecification: 'Specific types of evidence and sources needed',
            verificationRequirements: 'Multi-source verification methodology',
            factCheckingStandards: 'Professional fact-checking protocols',
            reportingStandards: 'Investigative journalism reporting standards',
            ethicalConsiderations: 'Ethical guidelines for investigative research'
          },
          models: {
            criteriaModel: 'claude-sonnet-4-0',
            optimizationModel: 'gpt-4o',
            assessmentModel: 'claude-sonnet-4-0',
            consolidationModel: 'claude-sonnet-4-0'
          }
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        console.log(`🤖 Refined: ${result.optimizedPrompt}`);
        
        // Check if expected refinements are present
        const refinedText = result.optimizedPrompt.toLowerCase();
        const foundRefinements = testCase.expectedRefinements.filter(refinement => 
          refinedText.includes(refinement.toLowerCase())
        );
        
        const score = (foundRefinements.length / testCase.expectedRefinements.length) * 100;
        const status = score >= 50 ? "✅ PASS" : "❌ FAIL";
        
        console.log(`📊 Refinement Score: ${score.toFixed(1)}% (${foundRefinements.length}/${testCase.expectedRefinements.length})`);
        console.log(`${status}\n`);
        
      } else {
        console.log(`❌ API Error: ${result.error}\n`);
      }
      
    } catch (error) {
      console.log(`❌ Test Error: ${error.message}\n`);
    }
  }
}

// Test the detection function
function testDetectionFunction() {
  console.log("🔍 Testing Investigative Research Detection\n");
  
  const detectionTests = [
    { text: "Investigate potential financial irregularities", expected: true },
    { text: "Conduct comprehensive investigative research", expected: true },
    { text: "Expose corruption in vendor contracts", expected: true },
    { text: "Deep dive analysis of industry trends", expected: true },
    { text: "Create a marketing campaign", expected: false },
    { text: "Develop a mobile application", expected: false },
    { text: "Regular market research study", expected: false }
  ];
  
  detectionTests.forEach(test => {
    // Simulate the detection logic
    const detected = test.text.toLowerCase().includes('investigat') || 
                    test.text.toLowerCase().includes('deep dive') || 
                    test.text.toLowerCase().includes('expose') || 
                    test.text.toLowerCase().includes('corruption') ||
                    test.text.toLowerCase().includes('comprehensive') && test.text.toLowerCase().includes('research');
    
    const status = detected === test.expected ? "✅ PASS" : "❌ FAIL";
    console.log(`${status} "${test.text}" - Expected: ${test.expected}, Got: ${detected}`);
  });
}

// Run tests
if (typeof window === 'undefined') {
  console.log("🧪 Starting Investigative Research Refinement Tests\n");
  testDetectionFunction();
  console.log("\n");
  testInvestigativeRefinement().catch(console.error);
}

module.exports = { testCases, testInvestigativeRefinement, testDetectionFunction };
