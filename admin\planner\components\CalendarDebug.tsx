'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { usePlanner } from '../../../app/services/context/PlannerContext';
import { useSession } from 'next-auth/react';
import { getPersonalItems, addPersonalItem } from '../../../app/lib/firebase/planner';
import { PersonalItem } from '../types';

const CalendarDebug: React.FC = () => {
  const { data: session } = useSession();
  const {
    tasks,
    loading,
    error,
    refreshData
  } = usePlanner();

  // Local state for personal items since they're not in the context
  const [personalItems, setPersonalItems] = useState<PersonalItem[]>([]);
  const [isLoadingPersonalItems, setIsLoadingPersonalItems] = useState(false);
  const [debugMessage, setDebugMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Function to fetch personal items - wrapped in useCallback to avoid dependency issues
  const fetchPersonalItems = useCallback(async () => {
    if (!session?.user?.email) {
      console.log('No user email available for fetching personal items');
      return;
    }

    setIsLoadingPersonalItems(true);
    try {
      const items = await getPersonalItems(session.user.email);
      setPersonalItems(items);
      console.log(`Fetched ${items.length} personal items`);
    } catch (err) {
      console.error('Error fetching personal items:', err);
    } finally {
      setIsLoadingPersonalItems(false);
    }
  }, [session, setIsLoadingPersonalItems, setPersonalItems]);

  // Fetch personal items on component mount
  useEffect(() => {
    if (session?.user?.email) {
      const loadItems = async () => {
        await fetchPersonalItems();
      };
      loadItems();
    }
  }, [session, fetchPersonalItems]);

  const testFetchPersonalItems = async () => {
    if (!session?.user?.email) {
      setDebugMessage('No user email available');
      return;
    }

    setIsLoading(true);
    setDebugMessage('Testing direct fetch from Firebase...');

    try {
      await fetchPersonalItems();
      setDebugMessage(`Successfully fetched ${personalItems.length} items directly from Firebase`);
    } catch (err) {
      console.error('Error in direct fetch:', err);
      setDebugMessage(`Error in direct fetch: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testRefreshData = async () => {
    setIsLoading(true);
    setDebugMessage('Refreshing all data from context...');

    try {
      await refreshData();
      await fetchPersonalItems(); // Also refresh personal items
      setDebugMessage(`Successfully refreshed data. Tasks: ${tasks.length}, Personal Items: ${personalItems.length}`);
    } catch (err) {
      console.error('Error refreshing data:', err);
      setDebugMessage(`Error refreshing data: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testCreateSampleItem = async () => {
    if (!session?.user?.email) {
      setDebugMessage('No user email available');
      return;
    }

    setIsLoading(true);
    setDebugMessage('Creating sample item directly in Firebase...');

    try {
      const sampleItem = {
        userId: session.user.email,
        title: 'Debug Test Item',
        description: 'This is a test item created from the debug panel',
        startDate: new Date(),
        endDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        color: '#ff0000', // Red
        createdAt: new Date()
      };

      const itemId = await addPersonalItem(sampleItem);
      setDebugMessage(`Successfully created sample item with ID: ${itemId}`);

      // Refresh personal items
      await fetchPersonalItems();
    } catch (err) {
      console.error('Error creating sample item:', err);
      setDebugMessage(`Error creating sample item: ${err instanceof Error ? err.message : String(err)}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-red-900/30 border border-red-500 text-red-200 p-4 rounded-md my-4">
      <h3 className="text-lg font-bold mb-2">Calendar Debug Info</h3>
      <div className="space-y-2 text-sm">
        <p><strong>Session:</strong> {session ? `Logged in as ${session.user?.email}` : 'Not logged in'}</p>
        <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
        <p><strong>Personal Items Loading:</strong> {isLoadingPersonalItems ? 'Yes' : 'No'}</p>
        <p><strong>Error:</strong> {error || 'None'}</p>
        <p><strong>Personal Items:</strong> {personalItems.length}</p>
        <p><strong>Tasks:</strong> {tasks.length}</p>

        <div className="flex space-x-2 mt-2">
          <button
            onClick={testFetchPersonalItems}
            disabled={isLoading}
            className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
          >
            Test Direct Fetch
          </button>
          <button
            onClick={testCreateSampleItem}
            disabled={isLoading}
            className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
          >
            Create Test Item
          </button>
          <button
            onClick={testRefreshData}
            disabled={isLoading}
            className="px-3 py-1 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
          >
            Refresh All Data
          </button>
        </div>

        {debugMessage && (
          <div className="mt-2 p-2 bg-gray-800 rounded">
            <p>{debugMessage}</p>
          </div>
        )}

        {personalItems.length > 0 && (
          <div>
            <p><strong>First Personal Item:</strong></p>
            <pre className="bg-gray-800 p-2 rounded text-xs overflow-auto">
              {JSON.stringify(personalItems[0], null, 2)}
            </pre>
          </div>
        )}

        {tasks.length > 0 && (
          <div>
            <p><strong>First Task:</strong></p>
            <pre className="bg-gray-800 p-2 rounded text-xs overflow-auto">
              {JSON.stringify(tasks[0], null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalendarDebug;
