import { NextRequest, NextResponse } from 'next/server';

// Empty mock data structure - we'll rely on the actual API
const MOCK_YOUTUBE_RESULTS = {
  "items": []
};

export async function GET(request: NextRequest) {
  // Get query parameters
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('q');
  const maxResults = searchParams.get('maxResults') || '3';
  const useMock = searchParams.get('mock') === 'true';

  // Validate query parameter
  if (!query) {
    return NextResponse.json(
      { error: 'Missing query parameter', details: 'A search query (q) is required' },
      { status: 400 }
    );
  }

  // Only use mock data if explicitly requested
  if (useMock) {
    console.log('Using mock YouTube data (explicitly requested)');
    return NextResponse.json(MOCK_YOUTUBE_RESULTS);
  }

  try {
    // Get API key from environment variables
    const apiKey = process.env.YOUTUBE_API_KEY || 'AIzaSyA1Nc7BqtD3Y85joJtWxo3i2S0LMC3Y-Kw';

    if (!apiKey) {
      console.warn('YouTube API key not found in environment variables');
      return NextResponse.json(
        { error: 'API key not configured', details: 'YouTube API key is missing' },
        { status: 500 }
      );
    }

    // First, search for videos
    const searchApiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(query)}&maxResults=${maxResults}&key=${apiKey}&type=video`;

    // Make the request to the YouTube Search API with a referrer header
    const searchResponse = await fetch(searchApiUrl, {
      headers: {
        'Referer': process.env.NODE_ENV === 'development'
          ? 'http://localhost:3000'
          : 'https://ike-ai.com'
      }
    });

    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error('YouTube Search API error:', searchResponse.status, errorText);

      // Check for specific error types and provide more helpful messages
      let errorMessage = `YouTube API error: ${searchResponse.status}`;
      let errorDetails = errorText;

      try {
        // Try to parse the error response for more details
        const errorData = JSON.parse(errorText);

        // Check for referrer blocked error
        if (errorData.error?.errors?.some((e: any) => e.reason === "forbidden" && e.message.includes("referer"))) {
          errorMessage = "Requests from this referrer are blocked";
          errorDetails = "Your YouTube API key has HTTP referrer restrictions. You need to configure your API key to allow requests from this domain.";
        }
        // Check for quota exceeded
        else if (errorData.error?.errors?.some((e: any) => e.reason === "quotaExceeded")) {
          errorMessage = "YouTube API quota exceeded";
          errorDetails = "Your daily quota for the YouTube API has been exceeded. Please try again tomorrow.";
        }
      } catch (parseError) {
        // If we can't parse the error, just use the original error text
        console.error('Error parsing YouTube API error response:', parseError);
      }

      console.error('YouTube API error occurred:', errorMessage);

      return NextResponse.json(
        { error: errorMessage, details: errorDetails },
        { status: searchResponse.status }
      );
    }

    // Parse the search response
    const searchData = await searchResponse.json();

    // Extract video IDs for the second API call
    const videoIds = searchData.items.map((item: any) => item.id.videoId).join(',');

    // If we have video IDs, get additional details
    let videoDetails = {};
    if (videoIds) {
      try {
        // Get video details including statistics and content details
        const detailsApiUrl = `https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=${videoIds}&key=${apiKey}`;
        const detailsResponse = await fetch(detailsApiUrl, {
          headers: {
            'Referer': process.env.NODE_ENV === 'development'
              ? 'http://localhost:3000'
              : 'https://ike-ai.com'
          }
        });

        if (detailsResponse.ok) {
          const detailsData = await detailsResponse.json();

          // Create a map of video details by ID for easy lookup
          videoDetails = detailsData.items.reduce((acc: any, item: any) => {
            acc[item.id] = {
              statistics: item.statistics || {},
              contentDetails: item.contentDetails || {}
            };
            return acc;
          }, {});
        } else {
          console.warn('Could not fetch video details, continuing with basic info only');
        }
      } catch (detailsError) {
        console.warn('Error fetching video details:', detailsError);
      }
    }

    // Transform the response to match our expected format with additional details
    const transformedData = {
      items: searchData.items.map((item: any) => {
        const videoId = item.id.videoId;
        const details = (videoDetails as any)[videoId] || {};

        return {
          id: { videoId },
          snippet: {
            title: item.snippet.title,
            description: item.snippet.description,
            thumbnails: {
              high: { url: item.snippet.thumbnails.high?.url || item.snippet.thumbnails.default?.url }
            },
            channelTitle: item.snippet.channelTitle,
            publishedAt: item.snippet.publishedAt
          },
          statistics: details.statistics || {},
          contentDetails: details.contentDetails || {}
        };
      })
    };

    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('Error fetching YouTube data:', error);

    // Return the error instead of falling back to mock data
    console.error('Error occurred while fetching YouTube data');

    return NextResponse.json(
      { error: 'Failed to fetch YouTube data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
