<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ElevenLabs API Test</title>
</head>
<body>
    <h1>ElevenLabs API Test</h1>
    <button onclick="testAPI()">Test ElevenLabs API</button>
    <div id="result"></div>
    <audio id="audioPlayer" controls style="display: none;"></audio>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            const audioPlayer = document.getElementById('audioPlayer');
            
            resultDiv.innerHTML = 'Testing ElevenLabs API...';
            
            try {
                // Use the same API key from the environment
                const apiKey = '***************************************************';
                const voiceId = '2mltbVQP21Fq8XgIfRQJ'; // Marketing Director voice
                const text = 'Hello, this is a test of the ElevenLabs API.';
                
                console.log('Making API request...');
                
                const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voiceId}`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'audio/mpeg',
                        'Content-Type': 'application/json',
                        'xi-api-key': apiKey,
                    },
                    body: JSON.stringify({
                        text: text,
                        model_id: 'eleven_turbo_v2_5',
                        voice_settings: {
                            stability: 0.5,
                            similarity_boost: 0.5,
                            style: 0.0,
                            use_speaker_boost: true
                        }
                    }),
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
                }
                
                const audioBlob = await response.blob();
                console.log('Audio blob size:', audioBlob.size, 'bytes');
                console.log('Audio blob type:', audioBlob.type);
                
                if (audioBlob.size === 0) {
                    throw new Error('Received empty audio blob');
                }
                
                const audioUrl = URL.createObjectURL(audioBlob);
                audioPlayer.src = audioUrl;
                audioPlayer.style.display = 'block';
                
                resultDiv.innerHTML = `
                    <p style="color: green;">✅ API Test Successful!</p>
                    <p>Audio blob size: ${audioBlob.size} bytes</p>
                    <p>Audio type: ${audioBlob.type}</p>
                    <p>Audio URL created: ${audioUrl}</p>
                `;
                
                // Try to play the audio
                try {
                    await audioPlayer.play();
                    resultDiv.innerHTML += '<p style="color: green;">🔊 Audio playback started successfully!</p>';
                } catch (playError) {
                    resultDiv.innerHTML += `<p style="color: orange;">⚠️ Audio created but autoplay blocked: ${playError.message}</p>`;
                }
                
            } catch (error) {
                console.error('Test failed:', error);
                resultDiv.innerHTML = `
                    <p style="color: red;">❌ API Test Failed!</p>
                    <p>Error: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
