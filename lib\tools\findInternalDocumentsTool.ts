/**
 * findInternalDocumentsTool.ts
 *
 * This tool allows the QueryDocumentsAgent to search for the existence of internal files
 * in the 'files' collection by document name or category. It helps the agent quickly find
 * relevant documents before searching their content using the QueryDocumentsTool.
 */

import { adminDb } from 'components/firebase-admin';
import { DocumentData, QueryDocumentSnapshot } from 'firebase-admin/firestore';

// Document metadata returned by the tool
export interface DocumentInfo {
  id: string;
  name: string;
  category: string;
  namespace: string;
  type: string;
  createdAt: string;
  size?: number;
  description?: string;
}

// Input options for the find internal documents tool
export interface FindInternalDocumentsOptions {
  userId: string;
  name?: string;
  category?: string;
  limit?: number;
}

// Result structure for the find internal documents tool
export interface FindInternalDocumentsResult {
  success: boolean;
  documents: DocumentInfo[];
  error?: string;
}

class FindInternalDocumentsTool {
  /**
   * Search for internal documents by name or category
   *
   * @param options - Options including userId, name, and/or category
   * @returns Array of matching document information
   */
  async process(options: FindInternalDocumentsOptions): Promise<FindInternalDocumentsResult> {
    try {
      const { userId, name, category, limit = 10 } = options;

      if (!userId) {
        return {
          success: false,
          documents: [],
          error: 'User ID is required'
        };
      }

      // At least one of name or category must be provided
      if (!name && !category) {
        return {
          success: false,
          documents: [],
          error: 'Either name or category must be provided'
        };
      }

      // Get reference to the files collection
      const filesRef = adminDb.collection('users').doc(userId).collection('files');
      let query: any = filesRef;

      // Apply filters based on provided parameters
      if (name && category) {
        // Note: Firestore doesn't support multiple equality filters on different fields
        // So we'll query by one field and filter the other client-side
        query = filesRef
          .where('name', '==', name)
          .limit(limit);
      } else if (name) {
        // Search by name only (exact match)
        query = filesRef
          .where('name', '==', name)
          .limit(limit);
      } else if (category) {
        // Search by category only
        query = filesRef
          .where('category', '==', category)
          .limit(limit);
      }

      // Execute the query
      const snapshot = await query.get();

      if (snapshot.empty) {
        return {
          success: true,
          documents: []
        };
      }

      // Map the results to DocumentInfo objects
      let documents: DocumentInfo[] = snapshot.docs.map((doc: QueryDocumentSnapshot<DocumentData>) => {
        const data = doc.data();

        // Format the createdAt timestamp
        let createdAtStr: string;
        try {
          if (data.createdAt instanceof Date) {
            createdAtStr = data.createdAt.toISOString();
          } else if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            // Firestore Timestamp
            createdAtStr = data.createdAt.toDate().toISOString();
          } else {
            createdAtStr = new Date().toISOString();
          }
        } catch (e) {
          console.warn(`Error formatting date for document ${doc.id}:`, e);
          createdAtStr = new Date().toISOString();
        }

        return {
          id: doc.id,
          name: data.name || 'Untitled',
          category: data.category || 'Uncategorized',
          namespace: data.namespace || doc.id,
          type: data.type || 'unknown',
          createdAt: createdAtStr,
          size: data.size,
          description: data.description
        };
      });

      // If we're doing a combined name and category search, filter by category client-side
      if (name && category) {
        documents = documents.filter(doc => doc.category === category);
      }

      return {
        success: true,
        documents
      };
    } catch (error) {
      console.error("Error finding internal documents:", error);
      return {
        success: false,
        documents: [],
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Search for documents by name with partial matching
   *
   * @param userId - The user ID
   * @param nameQuery - Partial name to search for
   * @param category - Optional category to filter by
   * @param limit - Maximum number of results to return
   * @returns Array of matching document information
   */
  async searchByPartialName(
    userId: string,
    nameQuery: string,
    category?: string,
    limit: number = 10
  ): Promise<DocumentInfo[]> {
    try {
      if (!userId || !nameQuery) {
        return [];
      }

      // Get all documents since Firestore doesn't support partial text search directly
      const filesRef = adminDb.collection('users').doc(userId).collection('files');
      const snapshot = await filesRef.get();

      // Filter documents client-side for partial name matches and optional category
      const matchingDocs = snapshot.docs
        .filter((doc: QueryDocumentSnapshot<DocumentData>) => {
          const data = doc.data();
          const name = data.name || '';
          const docCategory = data.category || '';

          // Check if name contains the query (case insensitive)
          const nameMatches = name.toLowerCase().includes(nameQuery.toLowerCase());

          // If category is provided, check if it matches
          const categoryMatches = !category || docCategory === category;

          return nameMatches && categoryMatches;
        })
        .map((doc: QueryDocumentSnapshot<DocumentData>) => {
          const data = doc.data();

          // Format the createdAt timestamp
          let createdAtStr: string;
          try {
            if (data.createdAt instanceof Date) {
              createdAtStr = data.createdAt.toISOString();
            } else if (data.createdAt && typeof data.createdAt.toDate === 'function') {
              // Firestore Timestamp
              createdAtStr = data.createdAt.toDate().toISOString();
            } else {
              createdAtStr = new Date().toISOString();
            }
          } catch (e) {
            console.warn(`Error formatting date for document ${doc.id}:`, e);
            createdAtStr = new Date().toISOString();
          }

          return {
            id: doc.id,
            name: data.name || 'Untitled',
            category: data.category || 'Uncategorized',
            namespace: data.namespace || doc.id,
            type: data.type || 'unknown',
            createdAt: createdAtStr,
            size: data.size,
            description: data.description
          };
        })
        .slice(0, limit);

      return matchingDocs;
    } catch (error) {
      console.error("Error searching by partial name:", error);
      return [];
    }
  }

  /**
   * Get a list of all documents in a specific category
   *
   * @param userId - The user ID
   * @param category - The category to search for
   * @param limit - Maximum number of results to return
   * @returns An array of document information
   */
  async getDocumentsByCategory(
    userId: string,
    category: string,
    limit: number = 10
  ): Promise<DocumentInfo[]> {
    try {
      if (!userId || !category) {
        return [];
      }

      const filesRef = adminDb.collection('users').doc(userId).collection('files');
      const query = filesRef
        .where('category', '==', category)
        .limit(limit);

      const snapshot = await query.get();

      return snapshot.docs.map((doc: QueryDocumentSnapshot<DocumentData>) => {
        const data = doc.data();

        // Format the createdAt timestamp
        let createdAtStr: string;
        try {
          if (data.createdAt instanceof Date) {
            createdAtStr = data.createdAt.toISOString();
          } else if (data.createdAt && typeof data.createdAt.toDate === 'function') {
            // Firestore Timestamp
            createdAtStr = data.createdAt.toDate().toISOString();
          } else {
            createdAtStr = new Date().toISOString();
          }
        } catch (e) {
          console.warn(`Error formatting date for document ${doc.id}:`, e);
          createdAtStr = new Date().toISOString();
        }

        return {
          id: doc.id,
          name: data.name || 'Untitled',
          category: data.category || 'Uncategorized',
          namespace: data.namespace || doc.id,
          type: data.type || 'unknown',
          createdAt: createdAtStr,
          size: data.size,
          description: data.description
        };
      });
    } catch (error) {
      console.error("Error getting documents by category:", error);
      return [];
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "findInternalDocuments",
        description: "Search for internal documents by name or category to quickly find relevant files",
        parameters: {
          type: "object",
          properties: {
            userId: {
              type: "string",
              description: "The user ID"
            },
            name: {
              type: "string",
              description: "The document name to search for (exact match)"
            },
            category: {
              type: "string",
              description: "The category to search for documents within"
            },
            limit: {
              type: "integer",
              description: "Maximum number of documents to return (default: 10)"
            }
          },
          required: ["userId"]
        }
      }
    };
  }
}

// Export a singleton instance for easy import
export const findInternalDocumentsTool = new FindInternalDocumentsTool();
