/**
 * API endpoint for creating projects from Strategic Director Agent outputs
 * 
 * This endpoint:
 * 1. Receives a request with requestId (Agent_Output document ID) and optional pmoId
 * 2. Uses CreateProjectAgent to extract projects from the agent output
 * 3. Uses pmoProjectsTaskAgent to extract and create tasks from the same agent output
 * 4. Updates PMO records if pmoId is provided (supports multiple projects per PMO)
 * 5. Returns the created projects and tasks summary
 */

import { NextRequest, NextResponse } from 'next/server';
import { createProjectAgent } from '../../../lib/agents/createProjectAgent';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';

export async function POST(request: NextRequest) {
  try {
    console.log('API: create-projects-from-agent-output - Starting request processing');

    // Get the session to verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      console.log('API: create-projects-from-agent-output - Unauthorized request');
      return NextResponse.json(
        { error: 'Unauthorized. Please log in.' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { requestId, pmoId, userId } = body;

    // Validate required parameters
    if (!requestId) {
      console.log('API: create-projects-from-agent-output - Missing requestId');
      return NextResponse.json(
        { error: 'requestId is required' },
        { status: 400 }
      );
    }

    // Use the authenticated user's email or provided userId
    const effectiveUserId = userId || session.user.email;

    console.log(`API: create-projects-from-agent-output - Processing request for requestId: ${requestId}, userId: ${effectiveUserId}, pmoId: ${pmoId || 'none'}`);

    // Create projects from agent output using CreateProjectAgent
    const result = await createProjectAgent.createProjectsFromAgentOutput(
      requestId,
      effectiveUserId,
      pmoId
    );

    if (!result.success) {
      console.error(`API: create-projects-from-agent-output - Project creation failed: ${result.error}`);
      return NextResponse.json(
        { 
          error: 'Failed to create projects from agent output',
          details: result.error,
          analysis: result.analysis
        },
        { status: 500 }
      );
    }

    console.log(`API: create-projects-from-agent-output - Successfully created ${result.projectsCreated.length} projects with ${result.totalTasksCreated} total tasks`);

    // Return success response with detailed information
    return NextResponse.json({
      success: true,
      message: `Successfully created ${result.projectsCreated.length} projects with ${result.totalTasksCreated} tasks`,
      data: {
        projectsCreated: result.projectsCreated,
        totalProjects: result.projectsCreated.length,
        totalTasksCreated: result.totalTasksCreated,
        pmoUpdated: result.pmoUpdated,
        analysis: result.analysis,
        requestId,
        pmoId: pmoId || null,
        userId: effectiveUserId
      }
    });

  } catch (error) {
    console.error('API: create-projects-from-agent-output - Unexpected error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the session to verify authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized. Please log in.' },
        { status: 401 }
      );
    }

    // Return API documentation
    return NextResponse.json({
      endpoint: '/api/create-projects-from-agent-output',
      description: 'Creates projects and tasks from Strategic Director Agent outputs',
      methods: ['POST'],
      parameters: {
        requestId: {
          type: 'string',
          required: true,
          description: 'The ID of the Agent_Output document in Firebase'
        },
        pmoId: {
          type: 'string',
          required: false,
          description: 'Optional PMO record ID to update with created project IDs'
        },
        userId: {
          type: 'string',
          required: false,
          description: 'User ID to assign as project owner (defaults to authenticated user)'
        }
      },
      response: {
        success: 'boolean',
        message: 'string',
        data: {
          projectsCreated: 'Array<{projectId, projectName, description, tasksCreated}>',
          totalProjects: 'number',
          totalTasksCreated: 'number',
          pmoUpdated: 'boolean',
          analysis: 'string',
          requestId: 'string',
          pmoId: 'string|null',
          userId: 'string'
        }
      },
      features: [
        'Extracts project recommendations from Strategic Director Agent outputs using Groq deepseek LLM',
        'Creates projects in Firebase projects collection',
        'Extracts and creates tasks from agent output using pmoProjectsTaskAgent',
        'Assigns all tasks to ADMIN user (<EMAIL>) with HIGH priority',
        'Updates PMO records with created project IDs (supports multiple projects per PMO)',
        'Handles both Marketing and PMO contexts appropriately'
      ],
      example: {
        request: {
          method: 'POST',
          body: {
            requestId: 'agent_output_document_id',
            pmoId: 'optional_pmo_record_id',
            userId: 'optional_user_id'
          }
        },
        response: {
          success: true,
          message: 'Successfully created 2 projects with 15 tasks',
          data: {
            projectsCreated: [
              {
                projectId: 'project_id_1',
                projectName: 'Marketing Campaign Project',
                description: 'Comprehensive marketing campaign...',
                tasksCreated: 8
              },
              {
                projectId: 'project_id_2',
                projectName: 'Brand Strategy Project',
                description: 'Brand positioning and strategy...',
                tasksCreated: 7
              }
            ],
            totalProjects: 2,
            totalTasksCreated: 15,
            pmoUpdated: true,
            analysis: 'Extracted 2 actionable projects from strategic analysis...',
            requestId: 'agent_output_document_id',
            pmoId: 'pmo_record_id',
            userId: '<EMAIL>'
          }
        }
      }
    });

  } catch (error) {
    console.error('API: create-projects-from-agent-output - GET error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
