"use client";

import { useState, useEffect, useRef } from "react";

interface UseRealtimeConversationProps {
  ephemeralKey: string | null;
  scriptContent: string;
  onConnect: () => void;
  onDisconnect: () => void;
  onError: (error: Error) => void;
  onMessage: (message: string) => void;
}

interface UseRealtimeConversationReturn {
  startSession: () => Promise<void>;
  endSession: () => Promise<void>;
  toggleMute: () => void;
  isListening: boolean;
  isSpeaking: boolean;
  isMuted: boolean;
}

interface WebRTCEvent {
  type: string;
  delta?: string;
  message?: string;
}

export function useRealtimeConversation({
  ephemeralKey,
  scriptContent,
  onConnect,
  onDisconnect,
  onError,
  onMessage,
}: UseRealtimeConversationProps): UseRealtimeConversationReturn {
  const [isListening, setIsListening] = useState<boolean>(false);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(false);

  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const dataChannelRef = useRef<RTCDataChannel | null>(null);
  const audioElementRef = useRef<HTMLAudioElement | null>(null);
  const mediaStreamRef = useRef<MediaStream | null>(null);

  const startSession = async (): Promise<void> => {
    console.group("=== Start WebRTC Session ===");
    console.log("startSession called with ephemeralKey:", ephemeralKey ? "Present (not null)" : "Missing (null)");
    
    if (!ephemeralKey) {
      console.error("Ephemeral key not available");
      onError(new Error("Ephemeral key not available"));
      console.groupEnd();
      return;
    }

    try {
      console.log("Creating new RTCPeerConnection...");
      const pc = new RTCPeerConnection();
      peerConnectionRef.current = pc;

      console.log("Setting up audio element...");
      const audioEl = document.createElement("audio");
      audioEl.autoplay = true;
      audioElementRef.current = audioEl;

      console.log("Setting up ontrack event handler...");
      pc.ontrack = (e: RTCTrackEvent) => {
        console.log("ontrack event received:", e.streams.length, "streams");
        if (audioElementRef.current && e.streams[0]) {
          audioElementRef.current.srcObject = e.streams[0];
          setIsSpeaking(true);
          console.log("Audio stream set on audio element");
        } else {
          console.warn("Audio element or stream not available");
        }
      };

      console.log("Requesting microphone access...");
      const ms: MediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaStreamRef.current = ms;
      console.log("Microphone access granted, adding track to peer connection...");
      ms.getTracks().forEach(track => {
        pc.addTrack(track, ms);
        console.log("Added track to peer connection:", track.kind, track.id);
      });

      console.log("Creating data channel...");
      const dc: RTCDataChannel = pc.createDataChannel("oai-events");
      dataChannelRef.current = dc;

      console.log("Setting up data channel event handlers...");
      dc.onmessage = (e: MessageEvent<string>) => {
        try {
          const event: WebRTCEvent = JSON.parse(e.data);
          console.log("Data channel message received:", event.type);
          
          if (event.type === "response.audio.delta") {
            setIsSpeaking(true);
          } else if (event.type === "response.audio.done") {
            setIsSpeaking(false);
          } else if (event.type === "response.text.delta" && event.delta) {
            onMessage(event.delta);
          } else if (event.type === "error" && event.message) {
            console.error("Error message received from data channel:", event.message);
            onError(new Error(event.message));
          }
        } catch (parseError) {
          console.error("Error parsing data channel message:", parseError, e.data);
        }
      };

      dc.onopen = () => {
        console.log("Data channel opened - connection established");
        onConnect();
        setIsListening(true);
        
        console.log("Sending session update with script content...");
        try {
          dc.send(
            JSON.stringify({
              type: "session.update",
              session: {
                instructions: `You are an AI Script/Rehearsal Assistant that helps actors 
      practice their lines and prepare for performances. 
      The following are your strict instructions:
      1. You must always format your response in Markdown.
      2. Your purpose is to serve as a reliable rehearsal partner who can read opposite parts and help with line memorization.
      
As a rehearsal partner:

Read the other character's lines clearly when we practice scenes together
Prompt me when I forget my lines or make errors in the dialogue
Adjust your reading pace and emotional tone based on my requests
Continue the scene from wherever I indicate if we need to restart
Maintain awareness of the full script structure and character relationships

Your capabilities include:

Working with any script I provide, regardless of format
Distinguishing between different characters' dialogue
Detecting when I've skipped lines or made errors
Being available whenever I need to practice
Adapting to my specific rehearsal style and preferences

Your interaction style should be:

Patient and willing to repeat scenes as many times as needed
Non-judgmental about my performance
Clear in your delivery but flexible enough to change based on direction
Professional yet supportive in your approach
Capable of simulating different emotional qualities when reading other parts

When I'm ready to begin, I'll share the script with you, indicate which character I'm playing, and let you know where we should start... 
                Use the following script content to assist the user during rehearsal: ${scriptContent}`,
                turn_detection: { type: "server_vad" },
                voice: "alloy",
                modalities: ["text", "audio"],
              },
            })
          );
          console.log("Session update sent successfully");
        } catch (sendError) {
          console.error("Error sending session update:", sendError);
          onError(new Error("Failed to send session configuration"));
        }
      };

      dc.onclose = () => {
        console.log("Data channel closed");
        onDisconnect();
        setIsListening(false);
        setIsSpeaking(false);
      };

      dc.onerror = (event) => {
        console.error("Data channel error:", event);
        onError(new Error("WebRTC data channel error"));
      };

      console.log("Creating WebRTC offer...");
      const offer: RTCSessionDescriptionInit = await pc.createOffer();
      console.log("Setting local description with offer...");
      await pc.setLocalDescription(offer);

      const baseUrl = "https://api.openai.com/v1/realtime";
      const model = "gpt-4o-realtime-preview-2024-12-17";
      console.log("Sending SDP offer to OpenAI Realtime API...");
      const sdpResponse: Response = await fetch(`${baseUrl}?model=${model}`, {
        method: "POST",
        body: offer.sdp,
        headers: {
          Authorization: `Bearer ${ephemeralKey}`,
          "Content-Type": "application/sdp",
        },
      });

      console.log("SDP response received:", sdpResponse.status, sdpResponse.statusText);
      if (!sdpResponse.ok) {
        const errorText = await sdpResponse.text();
        console.error("Failed to establish WebRTC connection:", errorText);
        throw new Error(`Failed to establish WebRTC connection: ${sdpResponse.statusText} - ${errorText}`);
      }

      const answer: RTCSessionDescriptionInit = {
        type: "answer",
        sdp: await sdpResponse.text(),
      };
      console.log("Setting remote description with answer...");
      await pc.setRemoteDescription(answer);
      console.log("WebRTC connection established successfully");
    } catch (error) {
      console.error("Error in startSession:", error);
      onError(error instanceof Error ? error : new Error(String(error)));
      setIsListening(false);
    } finally {
      console.groupEnd();
    }
  };

  const endSession = async (): Promise<void> => {
    console.group("=== End WebRTC Session ===");
    console.log("endSession called");
    
    if (dataChannelRef.current && dataChannelRef.current.readyState === "open") {
      console.log("Closing data channel...");
      try {
        dataChannelRef.current.close();
      } catch (error) {
        console.warn("Error closing data channel:", error);
      }
      dataChannelRef.current = null;
    }
    
    if (peerConnectionRef.current) {
      console.log("Closing peer connection...");
      try {
        peerConnectionRef.current.close();
      } catch (error) {
        console.warn("Error closing peer connection:", error);
      }
      peerConnectionRef.current = null;
    }
    
    if (mediaStreamRef.current) {
      console.log("Stopping media stream tracks...");
      try {
        mediaStreamRef.current.getTracks().forEach((track: MediaStreamTrack) => {
          track.stop();
          console.log("Stopped track:", track.kind, track.id);
        });
      } catch (error) {
        console.warn("Error stopping media stream tracks:", error);
      }
      mediaStreamRef.current = null;
    }
    
    if (audioElementRef.current) {
      console.log("Clearing audio element...");
      try {
        audioElementRef.current.srcObject = null;
      } catch (error) {
        console.warn("Error clearing audio element:", error);
      }
      audioElementRef.current = null;
    }
    
    // Update state after cleanup
    setIsListening(false);
    setIsSpeaking(false);
    console.log("Session ended successfully");
    console.groupEnd();
  };

  const toggleMute = (): void => {
    console.log("toggleMute called");
    if (audioElementRef.current) {
      audioElementRef.current.muted = !audioElementRef.current.muted;
      setIsMuted(audioElementRef.current.muted);
      console.log("Muted state:", audioElementRef.current.muted);
    } else {
      console.warn("Toggle mute called but audio element is not available");
    }
  };

  // Clean up resources when the component unmounts
  useEffect(() => {
    console.log("Setting up cleanup for useRealtimeConversation");
    return () => {
      console.log("Cleaning up useRealtimeConversation resources");
      if (isListening) {
        endSession();
      }
    };
  }, []);

  return {
    startSession,
    endSession,
    toggleMute,
    isListening,
    isSpeaking,
    isMuted,
  };
}