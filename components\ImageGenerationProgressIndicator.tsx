import React from 'react';
import CircularProgressIndicator from './CircularProgressIndicator';

export type ImageGenerationStep = 
  'initializing' | 
  'refining' | 
  'generating' | 
  'processing' | 
  'complete' | 
  'error';

interface ImageGenerationProgressIndicatorProps {
  step: ImageGenerationStep;
  progress: number;
  errorMessage?: string;
}

const ImageGenerationProgressIndicator: React.FC<ImageGenerationProgressIndicatorProps> = ({
  step,
  progress,
  errorMessage
}) => {
  // Define step labels
  const stepLabels: Record<ImageGenerationStep, string> = {
    initializing: 'Initializing',
    refining: 'Refining Prompt',
    generating: 'Generating Image',
    processing: 'Processing',
    complete: 'Complete',
    error: 'Error'
  };

  // Define step descriptions
  const stepDescriptions: Record<ImageGenerationStep, string> = {
    initializing: 'Setting up the image generation job',
    refining: 'Enhancing your prompt for better results',
    generating: 'Creating your image with AI',
    processing: 'Saving and optimizing your image',
    complete: 'Your image is ready to view and download',
    error: errorMessage || 'An error occurred during image generation'
  };

  // Define colors for different steps
  const stepColors: Record<ImageGenerationStep, string> = {
    initializing: 'purple',
    refining: 'purple',
    generating: 'purple',
    processing: 'purple',
    complete: 'green',
    error: 'red'
  };

  return (
    <div className="flex flex-col items-center justify-center p-6">
      <div className="mb-8">
        <CircularProgressIndicator 
          progress={step === 'error' ? 100 : progress} 
          label={stepLabels[step]}
          size="lg"
          color={stepColors[step]}
        />
      </div>

      <div className="w-full max-w-md px-4">
        {/* Step indicators */}
        <div className="grid grid-cols-5 gap-2 mb-6">
          {['initializing', 'refining', 'generating', 'processing', 'complete'].map((currentStep, index) => {
            const isActive = step === currentStep;
            const isPrevious = 
              (currentStep === 'initializing' && ['refining', 'generating', 'processing', 'complete'].includes(step)) ||
              (currentStep === 'refining' && ['generating', 'processing', 'complete'].includes(step)) ||
              (currentStep === 'generating' && ['processing', 'complete'].includes(step)) ||
              (currentStep === 'processing' && ['complete'].includes(step));
            
            return (
              <div key={currentStep} className="flex flex-col items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 
                    ${isActive 
                      ? 'bg-purple-600 text-white' 
                      : isPrevious 
                        ? 'bg-green-600 text-white' 
                        : 'bg-zinc-700 text-zinc-400'
                    }
                    ${step === 'error' ? 'opacity-50' : ''}
                  `}
                >
                  {isPrevious ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  ) : (
                    index + 1
                  )}
                </div>
                <span className={`text-xs ${isActive ? 'text-white' : 'text-zinc-400'} capitalize`}>
                  {stepLabels[currentStep as ImageGenerationStep]}
                </span>
              </div>
            );
          })}
        </div>

        {/* Current step details */}
        <div className={`bg-zinc-800 rounded-lg p-4 mb-4 ${step === 'error' ? 'border border-red-500' : ''}`}>
          <h3 className={`text-sm font-medium mb-2 capitalize ${step === 'error' ? 'text-red-500' : 'text-white'}`}>
            {stepLabels[step]}
          </h3>
          <p className={`text-xs ${step === 'error' ? 'text-red-400' : 'text-zinc-400'}`}>
            {stepDescriptions[step]}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ImageGenerationProgressIndicator;
