'use client';

import { useState, useCallback } from 'react';

export interface PMOTaskIdResult {
  success: boolean;
  taskId?: string;
  matchedTitle?: string;
  error?: string;
  loading?: boolean;
}

export interface UsePMOTaskIdReturn {
  findTaskId: (
    userId: string,
    pmoId: string,
    projectId: string,
    taskTitle: string,
    exactMatch?: boolean
  ) => Promise<PMOTaskIdResult>;
  loading: boolean;
  lastResult: PMOTaskIdResult | null;
}

/**
 * Custom hook for finding PMO task IDs by title
 * This hook provides a convenient way to search for task IDs in PMO projects
 * and can be used in components that need to map task titles to Firebase task IDs
 */
export const usePMOTaskId = (): UsePMOTaskIdReturn => {
  const [loading, setLoading] = useState(false);
  const [lastResult, setLastResult] = useState<PMOTaskIdResult | null>(null);

  const findTaskId = useCallback(async (
    userId: string,
    pmoId: string,
    projectId: string,
    taskTitle: string,
    exactMatch: boolean = false
  ): Promise<PMOTaskIdResult> => {
    setLoading(true);

    try {
      console.log(`usePMOTaskId: Searching for task "${taskTitle}" in project ${projectId}`);

      // Call the API endpoint instead of the server-side function directly
      const response = await fetch('/api/pmo/find-task-id', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          pmoId,
          projectId,
          taskTitle,
          exactMatch
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const result = await response.json();

      const resultWithLoading = {
        ...result,
        loading: false
      };

      setLastResult(resultWithLoading);

      if (result.success) {
        console.log(`usePMOTaskId: ✅ Successfully found task ID: ${result.taskId}`);
      } else {
        console.log(`usePMOTaskId: ❌ Failed to find task ID: ${result.error}`);
      }

      return resultWithLoading;

    } catch (error) {
      const errorResult: PMOTaskIdResult = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        loading: false
      };

      setLastResult(errorResult);
      console.error('usePMOTaskId: Error finding task ID:', error);

      return errorResult;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    findTaskId,
    loading,
    lastResult
  };
};

/**
 * Hook for automatically finding task ID when PMO task data is available
 * This is a convenience hook that can be used when you have PMO metadata
 * and want to automatically resolve the task ID
 */
export const useAutoResolvePMOTaskId = (
  task: {
    notes?: string;
    title?: string;
    projectId?: string;
  },
  userId?: string
) => {
  const { findTaskId, loading, lastResult } = usePMOTaskId();
  const [autoResolvedTaskId, setAutoResolvedTaskId] = useState<string | null>(null);

  const extractPMOMetadata = useCallback((notes: string) => {
    const pmoIdMatch = notes.match(/PMO Record ID: ([^\n]+)/);
    const projectIdMatch = notes.match(/PMO Project ID: ([^\n]+)/);
    
    return {
      pmoId: pmoIdMatch ? pmoIdMatch[1].trim() : undefined,
      legacyProjectId: projectIdMatch ? projectIdMatch[1].trim() : undefined
    };
  }, []);

  const resolveTaskId = useCallback(async () => {
    if (!task.notes || !task.title || !task.projectId || !userId) {
      return null;
    }

    const metadata = extractPMOMetadata(task.notes);
    if (!metadata.pmoId) {
      return null;
    }

    try {
      const result = await findTaskId(
        userId,
        metadata.pmoId,
        task.projectId, // Use actual Firebase project ID
        task.title,
        false // Allow fuzzy matching
      );

      if (result.success && result.taskId) {
        setAutoResolvedTaskId(result.taskId);
        return result.taskId;
      }
    } catch (error) {
      console.error('useAutoResolvePMOTaskId: Error resolving task ID:', error);
    }

    return null;
  }, [task.notes, task.title, task.projectId, userId, findTaskId, extractPMOMetadata]);

  return {
    resolveTaskId,
    autoResolvedTaskId,
    loading,
    lastResult,
    extractPMOMetadata
  };
};
