/**
 * Pinecone Client
 *
 * This file contains functions for interacting with the Pinecone vector database.
 */

import { Pinecone } from '@pinecone-database/pinecone';

let pineconeClient: Pinecone | null = null;

/**
 * Get the Pinecone client
 * @returns - The Pinecone client
 */
export async function getPineconeClient(): Promise<Pinecone> {
  if (!pineconeClient) {
    pineconeClient = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY!,
    });
  }

  return pineconeClient;
}

/**
 * Get the Pinecone index
 * @param indexName - The name of the index (optional, defaults to environment variable)
 * @returns - The Pinecone index
 */
export async function getPineconeIndex(indexName?: string) {
  const client = await getPineconeClient();
  const index = client.index(indexName || process.env.PINECONE_INDEX!);
  return index;
}

/**
 * Delete vectors from Pinecone by namespace and filter
 * @param namespace - The namespace to delete from
 * @param filter - The filter to apply
 * @returns - The deletion result
 */
export async function deleteVectorsByFilter(namespace: string, filter: any) {
  const index = await getPineconeIndex();

  try {
    const result = await index.namespace(namespace).deleteMany({ filter });
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting vectors from Pinecone:', error);
    return { success: false, error };
  }
}

/**
 * Delete all vectors in a namespace
 * @param namespace - The namespace to delete
 * @returns - The deletion result
 */
export async function deleteNamespace(namespace: string) {
  const index = await getPineconeIndex();

  try {
    // Delete all vectors in the namespace
    const result = await index.namespace(namespace).deleteAll();
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting namespace from Pinecone:', error);
    return { success: false, error };
  }
}
