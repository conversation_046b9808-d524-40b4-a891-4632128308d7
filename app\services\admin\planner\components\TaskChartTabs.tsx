'use client';

import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { usePlanner } from '../../../../context/PlannerContext';
import { Task } from '../../../../../admin/planner/types';

// Helper function to normalize task status
const normalizeTaskStatus = (status: string): string => {
  const normalizedStatus = status.toLowerCase();
  if (normalizedStatus === 'not started') return 'Not Started';
  if (normalizedStatus === 'in progress') return 'In Progress';
  if (normalizedStatus === 'reviewed') return 'Reviewed';
  if (normalizedStatus === 'complete' || normalizedStatus === 'completed') return 'Complete';
  return status; // Default fallback
};

export default function TaskChartTabs() {
  const { tasks, projects } = usePlanner();
  const [activeTab, setActiveTab] = useState<'status' | 'priority'>('status');

  // Prepare chart data for tasks by project and status
  const statusChartData = useMemo(() => {
    if (!tasks.length) return [];

    // Group tasks by project
    const projectMap = new Map();

    // First, get all projects
    projects.forEach(project => {
      projectMap.set(project.id, {
        name: project.name,
        'Not Started': 0,
        'In Progress': 0,
        'Reviewed': 0,
        'Complete': 0
      });
    });

    // Count tasks by status for each project
    tasks.forEach(task => {
      // Skip if project doesn't exist in map (shouldn't happen, but just in case)
      if (!projectMap.has(task.projectId)) return;

      // Normalize task status
      const normalizedStatus = normalizeTaskStatus(task.status);

      // Increment the count for this status
      const projectData = projectMap.get(task.projectId);

      // Make sure we're using a valid status key that exists in our data structure
      if (projectData[normalizedStatus] !== undefined) {
        projectData[normalizedStatus] += 1;
      } else {
        console.warn(`Unknown task status: ${task.status} (normalized: ${normalizedStatus}), defaulting to Not Started`);
        projectData['Not Started'] += 1;
      }
    });

    // Convert map to array for chart
    return Array.from(projectMap.values());
  }, [tasks, projects]);

  // Prepare chart data for tasks by project and priority
  const priorityChartData = useMemo(() => {
    if (!tasks.length) return [];

    // Group tasks by project
    const projectMap = new Map();

    // First, get all projects
    projects.forEach(project => {
      projectMap.set(project.id, {
        name: project.name,
        'Low': 0,
        'Medium': 0,
        'High': 0,
        'Critical': 0
      });
    });

    // Count tasks by priority for each project
    tasks.forEach(task => {
      // Skip if project doesn't exist in map (shouldn't happen, but just in case)
      if (!projectMap.has(task.projectId)) return;

      // Get priority (default to Medium if not set)
      const priority = task.priority || 'Medium';

      // Increment the count for this priority
      const projectData = projectMap.get(task.projectId);

      // Make sure we're using a valid priority key that exists in our data structure
      if (projectData[priority] !== undefined) {
        projectData[priority] += 1;
      } else {
        console.warn(`Unknown task priority: ${priority}, defaulting to Medium`);
        projectData['Medium'] += 1;
      }
    });

    // Convert map to array for chart
    return Array.from(projectMap.values());
  }, [tasks, projects]);

  return (
    <div className="bg-gray-800 rounded-lg p-4 overflow-x-hidden">
      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-4">
        <button
          className={`px-2 sm:px-4 py-2 text-sm sm:text-base font-medium ${
            activeTab === 'status'
              ? 'text-white border-b-2 border-purple-500'
              : 'text-gray-400 hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('status')}
        >
          By Status
        </button>
        <button
          className={`px-2 sm:px-4 py-2 text-sm sm:text-base font-medium ${
            activeTab === 'priority'
              ? 'text-white border-b-2 border-purple-500'
              : 'text-gray-400 hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('priority')}
        >
          By Priority
        </button>
      </div>

      {/* Chart content */}
      <div className="h-80 w-full overflow-x-hidden">
        {activeTab === 'status' ? (
          // Status Chart
          <ResponsiveContainer width="99%" height="100%">
            <BarChart
              data={statusChartData}
              margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis
                dataKey="name"
                tick={{ fill: '#9ca3af', fontSize: 8 }}
                angle={-45}
                textAnchor="end"
                height={60}
                interval={0}
                tickFormatter={(value) => value.length > 10 ? `${value.substring(0, 10)}...` : value}
              />
              <YAxis tick={{ fill: '#9ca3af' }} />
              <Tooltip
                contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                itemStyle={{ color: '#f9fafb' }}
                cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
              />
              <Legend
                wrapperStyle={{ fontSize: 10, paddingTop: 10 }}
                iconSize={8}
              />
              <Bar dataKey="Not Started" stackId="a" fill="#6B7280" /> {/* Gray */}
              <Bar dataKey="In Progress" stackId="a" fill="#3B82F6" /> {/* Blue */}
              <Bar dataKey="Reviewed" stackId="a" fill="#F59E0B" /> {/* Yellow */}
              <Bar dataKey="Complete" stackId="a" fill="#10B981" /> {/* Green */}
            </BarChart>
          </ResponsiveContainer>
        ) : (
          // Priority Chart
          <ResponsiveContainer width="99%" height="100%">
            <BarChart
              data={priorityChartData}
              margin={{ top: 10, right: 10, left: 0, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis
                dataKey="name"
                tick={{ fill: '#9ca3af', fontSize: 8 }}
                angle={-45}
                textAnchor="end"
                height={60}
                interval={0}
                tickFormatter={(value) => value.length > 10 ? `${value.substring(0, 10)}...` : value}
              />
              <YAxis tick={{ fill: '#9ca3af' }} />
              <Tooltip
                contentStyle={{ backgroundColor: '#1f2937', borderColor: '#374151', color: '#f9fafb' }}
                itemStyle={{ color: '#f9fafb' }}
                cursor={{ fill: 'rgba(255, 255, 255, 0.1)' }}
              />
              <Legend
                wrapperStyle={{ fontSize: 10, paddingTop: 10 }}
                iconSize={8}
              />
              <Bar dataKey="Low" stackId="a" fill="#9CA3AF" /> {/* Gray */}
              <Bar dataKey="Medium" stackId="a" fill="#3B82F6" /> {/* Blue */}
              <Bar dataKey="High" stackId="a" fill="#F59E0B" /> {/* Yellow */}
              <Bar dataKey="Critical" stackId="a" fill="#EF4444" /> {/* Red */}
            </BarChart>
          </ResponsiveContainer>
        )}
      </div>
    </div>
  );
}