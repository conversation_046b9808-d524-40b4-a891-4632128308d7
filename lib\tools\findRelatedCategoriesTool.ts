/**
 * Tool for finding categories related to the current query
 * This tool helps users discover other document categories that might contain relevant information
 */

import { adminDb } from 'components/firebase-admin';

export interface CategoryInfo {
  name: string;
  documentCount: number;
}

export interface FindRelatedCategoriesOptions {
  userId: string;
  currentCategory?: string;
  limit?: number;
}

export interface FindRelatedCategoriesResult {
  success: boolean;
  categories: CategoryInfo[];
  error?: string;
}

class FindRelatedCategoriesTool {
  /**
   * Find categories related to the current query that might contain relevant information
   * 
   * @param options - Options including userId and current category
   * @returns Array of related category information
   */
  async process(options: FindRelatedCategoriesOptions): Promise<FindRelatedCategoriesResult> {
    try {
      const { userId, currentCategory, limit = 3 } = options;
      
      if (!userId) {
        return {
          success: false,
          categories: [],
          error: 'User ID is required'
        };
      }

      // Get all categories for the user
      const filesRef = adminDb.collection('users').doc(userId).collection('files');
      const snapshot = await filesRef.get();
      
      // Count documents per category
      const categoryCounts: Record<string, number> = {};
      snapshot.forEach(doc => {
        const category = doc.data().category || 'Uncategorized';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      });
      
      // Convert to array and filter out current category
      const categories = Object.entries(categoryCounts)
        .filter(([name]) => name !== currentCategory && name !== 'Uncategorized')
        .map(([name, count]) => ({ name, documentCount: count }))
        .sort((a, b) => b.documentCount - a.documentCount)
        .slice(0, limit);
      
      return {
        success: true,
        categories
      };
    } catch (error) {
      console.error("Error finding related categories:", error);
      return {
        success: false,
        categories: [],
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Get a list of all unique categories for a user
   * 
   * @param userId - The user ID
   * @returns An array of category names
   */
  async getAllCategories(userId: string): Promise<string[]> {
    try {
      if (!userId) {
        return [];
      }
      
      const filesRef = adminDb.collection('users').doc(userId).collection('files');
      const snapshot = await filesRef.get();
      
      const categories = new Set<string>();
      snapshot.forEach(doc => {
        const category = doc.data().category;
        if (category) {
          categories.add(category);
        }
      });
      
      return Array.from(categories).sort();
    } catch (error) {
      console.error("Error getting all categories:", error);
      return [];
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "findRelatedCategories",
        description: "Find document categories related to the current query that might contain relevant information",
        parameters: {
          type: "object",
          properties: {
            userId: {
              type: "string",
              description: "The user ID"
            },
            currentCategory: {
              type: "string",
              description: "The current category being queried (if any)"
            },
            limit: {
              type: "integer",
              description: "Maximum number of related categories to return (default: 3)"
            }
          },
          required: ["userId"]
        }
      }
    };
  }
}

// Export a singleton instance for easy import
export const findRelatedCategoriesTool = new FindRelatedCategoriesTool();
