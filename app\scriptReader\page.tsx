"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
// import { <PERSON><PERSON><PERSON> } from "lucide-react" // <PERSON><PERSON><PERSON> icon removed
import Image from "next/image"
import Link from "next/link"
import { signOut, useSession, signIn } from "next-auth/react"
import { useRouter } from "next/navigation"
import { doc, getDoc } from "firebase/firestore"
import { db } from "components/firebase"
import { Readermodal } from "components/scriptreaderAI/Reader-modal"
import UserDetailsModal from "components/UserDetailsModal"
import LoadingOverlay from "components/LoadingOverlay"

export default function AiScriptReaderPage() {
  // Original SceneMate states
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)

  // Auth-related states from Header component
  const { data: session, status, update } = useSession()
  const router = useRouter()
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [showUserDetailsModal, setShowUserDetailsModal] = useState(false)
  const [isProfileIncomplete, setIsProfileIncomplete] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const [hasCheckedProfile, setHasCheckedProfile] = useState(false)

  // Simulate loading completion for the landing page
  useEffect(() => {
    const timer = setTimeout(() => {
      setHasLoaded(true)
    }, 500)

    return () => clearTimeout(timer)
  }, [])

  // Check user account on authentication
  useEffect(() => {
    async function checkUserAccount() {
      if (status === "authenticated" && session?.user?.email && !hasCheckedProfile) {
        const accountDoc = await getDoc(doc(db, "Accounts", session.user.email))
        if (!accountDoc.exists()) {
          setShowUserDetailsModal(true)
          setIsProfileIncomplete(true)
        } else {
          setIsProfileIncomplete(false)
        }
        setHasCheckedProfile(true)
      }
    }

    checkUserAccount()
  }, [status, session, hasCheckedProfile])

  // Reset states when authentication status changes to unauthenticated
  useEffect(() => {
    if (status === "unauthenticated") {
      setIsUserMenuOpen(false)
      setShowUserDetailsModal(false)
      setIsProfileIncomplete(false)
      setIsSigningOut(false)
      setIsLoading(false)
      setHasCheckedProfile(false)
    }
  }, [status])

  // Function to open the reader modal with authentication check
  const handleOpenModal = () => {
    // Check if user is authenticated
    if (status === "authenticated" && session?.user && !isSigningOut) {
      // User is logged in, open the modal as usual
      setIsModalOpen(true)
    } else {
      // User is not logged in, redirect to Google sign-in
      // This uses the existing handleSignIn function which has the correct callback URL
      handleSignIn()
    }
  }

  // Function to close the reader modal
  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  // Auth-related functions from Header component
  const handleSignIn = async () => {
    try {
      setIsLoading(true)
      await signIn("google", { callbackUrl: "/scriptReader" })
    } catch (error) {
      console.error("Sign in error:", error)
      setIsLoading(false)
    }
  }

  const handleSignOut = async () => {
    setIsUserMenuOpen(false)
    setIsLoading(true)
    setIsSigningOut(true)

    try {
      await signOut({
        redirect: false,
      })

      await update()

      setIsUserMenuOpen(false)
      setShowUserDetailsModal(false)
      setIsProfileIncomplete(false)
      setHasCheckedProfile(false)

      router.push("/")
      router.refresh()
    } catch (error) {
      console.error("Sign out error:", error)
    } finally {
      setIsLoading(false)
      setIsSigningOut(false)
    }
  }

  const handleUserDetailsComplete = () => {
    setShowUserDetailsModal(false)
    setIsProfileIncomplete(false)
    setIsLoading(true)
    router.push("/dashboard/Upload")
  }

  const handleUserDetailsClose = () => {
    setShowUserDetailsModal(false)
  }

  // Determine if we should show the sign-in button
  const showSignInButton = status === "unauthenticated" || (isSigningOut && !session)

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  }

  return (
    <div className="min-h-screen bg-white text-black">
      {" "}
      {/* Changed bg and text color */}
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Adjusted gradient for light theme */}
        {/* <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-gray-200/30 to-transparent" /> */}
        {/* Adjusted blur colors for light theme */}
        {/* <div className="absolute -top-40 -right-40 w-80 h-80 bg-gray-300/20 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gray-300/20 rounded-full blur-3xl" /> */}
      </div>
      {/* Loading overlay */}
      {isLoading && <LoadingOverlay message="Please wait..." />}
      {/* Navigation header with authentication */}
      <header className="relative z-20 p-4">
        <nav className="container mx-auto">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center space-x-2 text-black">
              {" "}
              {/* Changed text color */}
              <h1 className="text-xl font-bold text-black">CastMate</h1> {/* Changed text color */}
            </Link>

            <div className="relative flex items-center">
              {status === "authenticated" && session?.user && !isSigningOut ? (
                <>
                  <div className="flex items-center cursor-pointer" onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}>
                    <Image
                      src={session.user.image || "/default-avatar.png"}
                      alt={`${session.user.name}'s profile`}
                      width={32}
                      height={32}
                      className="rounded-full border-2 border-gray-400" // Changed border color
                    />
                  </div>

                  {isUserMenuOpen && (
                    // Adjusted user menu for light theme
                    <div className="absolute top-12 right-0 bg-white p-4 rounded-lg shadow-xl w-64 z-50 border border-gray-200">
                      <button
                        className="absolute top-2 right-2 text-gray-500 hover:text-black" // Adjusted icon color
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                      <div className="flex flex-col items-center justify-center mt-4">
                        <Image
                          src={session.user.image || "/default-avatar.png"}
                          alt="User Profile"
                          width={40}
                          height={40}
                          className="rounded-full mb-2"
                        />
                        <h2 className="text-md font-semibold text-center text-black">{session.user.name}</h2>{" "}
                        {/* Changed text color */}
                        <p className="text-sm text-gray-500 text-center mb-4">{session.user.email}</p>{" "}
                        {/* Adjusted text color */}
                        {isProfileIncomplete && (
                          <button
                            onClick={() => setShowUserDetailsModal(true)}
                            // Adjusted button style for light theme
                            className="bg-blue-500 text-white font-bold py-2 px-4 rounded w-full mb-2 transition duration-300 ease-in-out transform hover:scale-105 hover:bg-blue-600"
                          >
                            Complete Profile
                          </button>
                        )}
                        <button
                          onClick={handleSignOut}
                          className="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded w-full transition duration-300 ease-in-out transform hover:scale-105"
                        >
                          Sign Out
                        </button>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                showSignInButton && (
                  <button
                    onClick={handleSignIn}
                    // Adjusted Sign In button for light theme
                    className="bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out hover:bg-blue-700"
                  >
                    Sign In with Google
                  </button>
                )
              )}
            </div>
          </div>
        </nav>
      </header>
      {/* Page content */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 py-8 sm:py-16">
        <motion.div
          className="flex flex-col items-center justify-center"
          variants={containerVariants}
          initial="hidden"
          animate={hasLoaded ? "visible" : "hidden"}
        >
          {/* Main title with face logos */}
          <motion.div className="flex items-center justify-center mb-20 w-full max-w-6xl" variants={itemVariants}> {/* mb-10 to mb-20 */}
            {/* Left face logo */}
            <div className="hidden md:flex items-center justify-center w-68 h-68 -mt-14 -mr-3">
              <Image src="/Clogo6A.png" alt="Left Face Logo" width={210} height={210} className="object-contain" /> {/* Adjusted width/height */}
            </div>

            {/* Center content */}
            <div className="flex flex-col items-center text-center -mb-4  -mt-14">
              <h1 className="text-4xl sm:text-5xl md:text-8xl font-light  text-black font-serif">Castmate</h1> {/* Removed font-mono, added font-serif */}
              <p className="text-lg sm:text-xl text-gray-700">Never rehearse alone again</p> {/* Removed font-mono, changed text-gray-600 to text-gray-700 */}
            </div>

            {/* Right face logo */}
            <div className="hidden md:flex items-center justify-center w-68 h-68 mt-2 -ml-5">
              <Image src="/Clogo7A.png" alt="Right Face Logo" width={210} height={210} className="object-contain" /> {/* Adjusted width/height */}
            </div>
          </motion.div>

          {/* Action button */}
          <motion.div variants={itemVariants} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <button
              onClick={handleOpenModal}
              // Adjusted button style for light theme (primary CTA)
              className="px-14 py-6 rounded-xl -mt-20 -mb-5 bg-black text-white font-serif font-medium text-2xl shadow-lg transition-all duration-300 flex items-center group hover:bg-gray-800" /* Removed font-mono */
            >
              {/* <Dumbbell className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" /> Removed Dumbbell icon */}
              Start rehearsing
            </button>
          </motion.div>

          {/* Faint horizontal line */}
          <motion.div variants={itemVariants} className="w-full flex justify-center my-8 mt-1">
            <div className="h-px bg-gray-300 w-80"></div>
          </motion.div>

          {/* Features section */}
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 font-serif md:grid-cols-3 gap-6 mt-0 max-w-5xl mx-auto" /* mt-16 to mt-0 */
            variants={containerVariants}
          >
            {[
              {
                title: "AI script reading",
                description: "personalized, emotion-aware line delivery that adapts to your role",
              },
              {
                title: "Vocal archive",
                description: "choose from a selection voices and assign them to different characters from your script",
              },
              {
                title: "Audition partner",
                description: "use castmate as a scene partner to record as many self tape attempts as it takes",
              },
            ].map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                // Adjusted feature card style for light theme
                className="bg-white rounded-lg p-6 border border-gray-200 shadow-md hover:border-gray-300 transition-all duration-300 hover:shadow-lg" /* rounded-xl to rounded-lg */
              >
                <h3 className="text-xl font-semibold mb-3 text-black">{feature.title}</h3> {/* Removed font-mono */}
                <p className="text-gray-600">{feature.description}</p> {/* Removed font-mono */}
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
      {/* The Readermodal Modal */}
      <Readermodal isOpen={isModalOpen} onClose={handleCloseModal} />
      {/* User Details Modal */}
      {showUserDetailsModal && session?.user && (
        <UserDetailsModal
          email={session.user.email}
          name={session.user.name}
          onClose={handleUserDetailsClose}
          onComplete={handleUserDetailsComplete}
        />
      )}
    </div>
  )
}