'use client';

import React, { useState, useEffect } from 'react';
import {
  PlusCircle,
  Users
} from 'lucide-react';
import TaskForm from './components/TaskForm';
import TaskList from './components/TaskList';
import ProjectHeader from './components/ProjectHeader';
import FilterBar from './components/FilterBar';
import ResourcePanel from './components/ResourcePanel';
import Timeline from './components/Timeline';
import TaskDetailsModal from './components/TaskDetailsModal';
import { Task, Resource, TaskStatus } from './types';

export default function ProjectPlanner() {
  // State for tasks, resources, and UI controls
  const [tasks, setTasks] = useState<Task[]>([]);
  const [resources, setResources] = useState<Resource[]>([]);
  const [isTaskFormOpen, setIsTaskFormOpen] = useState(false);
  const [isResourcePanelOpen, setIsResourcePanelOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null); // For task details modal
  const [filter, setFilter] = useState({
    status: 'all',
    category: 'all',
    search: '',
    assignedTo: 'all'
  });
  const [view, setView] = useState<'board' | 'timeline'>('board');

  // Load initial data
  useEffect(() => {
    // In a real app, this would be an API call
    const initialTasks: Task[] = [
      {
        id: '1',
        projectId: 'demo-project',  // Added projectId
        title: 'Complete feedback collection from beta testers',
        description: 'Gather all feedback from the beta testing phase',
        category: 'Working Prototype Finalization',
        status: 'In Progress',
        startDate: new Date('2025-04-08'),
        dueDate: new Date('2025-04-15'),
        assignedTo: ['1', '2'],
        priority: 'High',
        dependencies: []
      },
      {
        id: '2',
        projectId: 'demo-project',  // Added projectId
        title: 'Finalize tiered pricing model',
        description: 'Complete the Basic, Pro, and Enterprise pricing tiers',
        category: 'Pricing Structure Launch',
        status: 'Not Started',
        startDate: new Date('2025-04-08'),
        dueDate: new Date('2025-04-12'),
        assignedTo: ['3'],
        priority: 'High',
        dependencies: []
      },
      {
        id: '3',
        projectId: 'demo-project',  // Added projectId
        title: 'Finalize agreements with platform partners',
        description: 'Complete all partnership agreements and get signatures',
        category: 'Platform Partnership Activation',
        status: 'Not Started',
        startDate: new Date('2025-04-10'),
        dueDate: new Date('2025-04-18'),
        assignedTo: ['1', '4'],
        priority: 'Medium',
        dependencies: []
      }
    ];

    const initialResources: Resource[] = [
      { id: '1', name: 'Jane Smith', role: 'user', jobTitle: 'UX Designer', email: '<EMAIL>', avatar: '/avatars/jane.png', availability: 'Full-time' },
      { id: '2', name: 'John Doe', role: 'user', jobTitle: 'Marketing Specialist', email: '<EMAIL>', avatar: '/avatars/john.png', availability: 'Full-time' },
      { id: '3', name: 'Alice Johnson', role: 'user', jobTitle: 'Financial Analyst', email: '<EMAIL>', avatar: '/avatars/alice.png', availability: 'Part-time' },
      { id: '4', name: 'Bob Williams', role: 'user', jobTitle: 'Legal Counsel', email: '<EMAIL>', avatar: '/avatars/bob.png', availability: 'Contract' }
    ];

    setTasks(initialTasks);
    setResources(initialResources);
  }, []);

  // Add a new task
  const addTask = (task: Omit<Task, 'id'>) => {
    // Ensure task has a projectId
    if (!task.projectId) {
      task = {
        ...task,
        projectId: 'demo-project' // Default projectId if none provided
      };
    }

    const newTask = {
      ...task,
      id: Date.now().toString()
    };
    setTasks([...tasks, newTask]);
    setIsTaskFormOpen(false);
  };

  // Update an existing task
  const updateTask = (updatedTask: Task) => {
    setTasks(tasks.map(task => task.id === updatedTask.id ? updatedTask : task));
    setEditingTask(null);
    setIsTaskFormOpen(false);

    // Update dependent tasks' dates if this task's due date changed
    const originalTask = tasks.find(t => t.id === updatedTask.id);
    if (originalTask && originalTask.dueDate !== updatedTask.dueDate) {
      const daysDifference = Math.floor(
        (updatedTask.dueDate.getTime() - originalTask.dueDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDifference !== 0) {
        const dependentTasks = tasks.filter(t =>
          t.dependencies.includes(updatedTask.id)
        );

        if (dependentTasks.length > 0) {
          const updatedDependentTasks = dependentTasks.map(t => ({
            ...t,
            startDate: new Date(t.startDate.getTime() + daysDifference * 24 * 60 * 60 * 1000),
            dueDate: new Date(t.dueDate.getTime() + daysDifference * 24 * 60 * 60 * 1000)
          }));

          setTasks(tasks.map(t =>
            updatedDependentTasks.find(dt => dt.id === t.id) || t
          ));
        }
      }
    }
  };

  // Delete a task
  const deleteTask = (id: string) => {
    // Check if any tasks depend on this one
    const dependentTasks = tasks.filter(t => t.dependencies.includes(id));

    if (dependentTasks.length > 0) {
      if (confirm(`This task has ${dependentTasks.length} dependent tasks. Deleting it will remove the dependency. Continue?`)) {
        // Remove the dependency from dependent tasks
        const updatedTasks = tasks.map(t => {
          if (t.dependencies.includes(id)) {
            return {
              ...t,
              dependencies: t.dependencies.filter((d: string) => d !== id)
            };
          }
          return t;
        });

        // Then remove the task itself
        setTasks(updatedTasks.filter(t => t.id !== id));
      }
    } else {
      // No dependencies, just delete
      setTasks(tasks.filter(t => t.id !== id));
    }
  };

  // Update task status
  const updateTaskStatus = (id: string, status: TaskStatus) => {
    setTasks(tasks.map(task =>
      task.id === id ? { ...task, status } : task
    ));
  };

  // Add a new resource
  const addResource = (resource: Omit<Resource, 'id'>) => {
    const newResource = {
      ...resource,
      id: Date.now().toString()
    };
    setResources([...resources, newResource]);
    setIsResourcePanelOpen(false);
  };

  // Filter tasks based on current filter settings
  const filteredTasks = tasks.filter(task => {
    // Status filter
    if (filter.status !== 'all' && task.status !== filter.status) {
      return false;
    }

    // Category filter
    if (filter.category !== 'all' && task.category !== filter.category) {
      return false;
    }

    // Assigned to filter
    if (filter.assignedTo !== 'all' && !task.assignedTo.includes(filter.assignedTo)) {
      return false;
    }

    // Search filter
    if (filter.search && !task.title.toLowerCase().includes(filter.search.toLowerCase()) &&
        !task.description.toLowerCase().includes(filter.search.toLowerCase())) {
      return false;
    }

    return true;
  });

  // Get unique categories for filter dropdown
  const categories = Array.from(new Set(tasks.map(task => task.category)));

  return (
    <div className="min-h-screen bg-gray-100">
      <ProjectHeader
        projectName="SceneMate Marketing Action Plan"
        projectDate="April 8, 2025"
        view={view}
        setView={setView}
      />

      <div className="container mx-auto px-4 py-6">
        {/* Action buttons */}
        <div className="flex justify-between mb-6">
          <div className="flex space-x-4">
            <button
              onClick={() => {
                setEditingTask(null);
                setIsTaskFormOpen(true);
              }}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <PlusCircle className="w-5 h-5 mr-2" />
              Add Task
            </button>
            <button
              onClick={() => setIsResourcePanelOpen(true)}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Users className="w-5 h-5 mr-2" />
              Manage Resources
            </button>
          </div>

          <div className="flex space-x-4">
            <button
              onClick={() => setView('board')}
              className={`px-4 py-2 rounded-md ${
                view === 'board'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              } transition-colors`}
            >
              Board View
            </button>
            <button
              onClick={() => setView('timeline')}
              className={`px-4 py-2 rounded-md ${
                view === 'timeline'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              } transition-colors`}
            >
              Timeline View
            </button>
          </div>
        </div>

        {/* Filter bar */}
        <FilterBar
          filter={filter}
          setFilter={setFilter}
          categories={categories}
          resources={resources}
        />

        {/* Main content */}
        <div className="mt-6">
          {view === 'board' ? (
            <TaskList
              tasks={filteredTasks}
              resources={resources}
              onEdit={(task: Task) => {
                setEditingTask(task);
                setIsTaskFormOpen(true);
              }}
              onDelete={deleteTask}
              onStatusChange={updateTaskStatus}
            />
          ) : (
            <Timeline
              tasks={filteredTasks}
              resources={resources}
              onEdit={(task: Task) => {
                setEditingTask(task);
                setIsTaskFormOpen(true);
              }}
            />
          )}
        </div>
      </div>

      {/* Task form modal */}
      {isTaskFormOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
            <h2 className="text-2xl font-bold mb-4">
              {editingTask ? 'Edit Task' : 'Add New Task'}
            </h2>
            <TaskForm
              initialValues={editingTask || undefined}
              resources={resources}
              tasks={tasks}
              onSubmit={editingTask ? updateTask : addTask}
              onCancel={() => {
                setIsTaskFormOpen(false);
                setEditingTask(null);
              }}
            />
          </div>
        </div>
      )}

      {/* Resource panel modal */}
      {isResourcePanelOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
            <h2 className="text-2xl font-bold mb-4">Manage Resources</h2>
            <ResourcePanel
              resources={resources}
              onAddResource={addResource}
              onClose={() => setIsResourcePanelOpen(false)}
            />
          </div>
        </div>
      )}

      {/* Task details modal */}
      {selectedTask && (
        <TaskDetailsModal
          task={selectedTask}
          onClose={() => setSelectedTask(null)}
          onEdit={() => {
            // Close the details modal and open the edit modal
            const taskToEdit = selectedTask;
            setSelectedTask(null);
            setEditingTask(taskToEdit);
            setIsTaskFormOpen(true);
          }}
        />
      )}
    </div>
  );
}
