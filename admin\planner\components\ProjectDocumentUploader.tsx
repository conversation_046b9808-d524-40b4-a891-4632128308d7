'use client';

import React, { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useDropzone } from 'react-dropzone';
// No longer need Firebase client imports since we're using the server API
import { FileIcon, ImageIcon, XIcon, Upload, AlertCircle } from 'lucide-react';

// Supported file types
const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
];

const SUPPORTED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
];

// Maximum file sizes
const MAX_IMAGE_SIZE = 4 * 1024 * 1024; // 4MB
const MAX_DOC_SIZE = 50 * 1024 * 1024; // 50MB

interface ProjectDocumentUploaderProps {
  projectId: string;
  onUploadComplete?: () => void;
}

export default function ProjectDocumentUploader({ projectId, onUploadComplete }: ProjectDocumentUploaderProps) {
  const { data: session } = useSession();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  const validateFile = (file: File): boolean => {
    // Check if file type is supported
    if (![...SUPPORTED_IMAGE_TYPES, ...SUPPORTED_DOCUMENT_TYPES].includes(file.type)) {
      setError(`Unsupported file type: ${file.type}`);
      return false;
    }

    // Check file size
    const isImage = SUPPORTED_IMAGE_TYPES.includes(file.type);
    const maxSize = isImage ? MAX_IMAGE_SIZE : MAX_DOC_SIZE;
    if (file.size > maxSize) {
      const maxSizeInMB = maxSize / (1024 * 1024);
      setError(`File size exceeds the ${maxSizeInMB}MB limit`);
      return false;
    }

    return true;
  };

  const handleUpload = async () => {
    if (!selectedFile || !session?.user?.email) {
      setError('No file selected or user not authenticated.');
      return;
    }

    setError(null);
    setUploading(true);
    setUploadProgress(0);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', selectedFile);

      // Set up a simulated progress indicator since we can't track actual progress with fetch
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // Cap at 90% until we get confirmation
          }
          return prev + 5;
        });
      }, 300);

      // Send the file to our API endpoint
      const response = await fetch(`/api/projects/${projectId}/documents`, {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to upload document');
      }

      // Set progress to 100% when complete
      setUploadProgress(100);

      // Reset state after a short delay to show 100% completion
      setTimeout(() => {
        setSelectedFile(null);
        setFileName('');
        setPreview(null);
        setUploading(false);
        setUploadProgress(0);

        // Notify parent component
        if (onUploadComplete) {
          onUploadComplete();
        }
      }, 1000);
    } catch (error: any) {
      console.error('Error during file upload:', error);
      setError(error.message || 'Error during file upload');
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError(null);
    const file = acceptedFiles[0];

    if (!validateFile(file)) {
      return;
    }

    setFileName(file.name);
    setSelectedFile(file);

    // Create preview for images
    if (SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      const objectUrl = URL.createObjectURL(file);
      setPreview(objectUrl);
    } else {
      setPreview(null);
    }
  }, []);

  const clearSelection = () => {
    setSelectedFile(null);
    setFileName('');
    setPreview(null);
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': SUPPORTED_IMAGE_TYPES,
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
    },
    maxFiles: 1,
  });

  const getFileIcon = (fileType: string) => {
    if (SUPPORTED_IMAGE_TYPES.includes(fileType)) {
      return <ImageIcon className="w-16 h-16 text-gray-400" />;
    }
    return <FileIcon className="w-16 h-16 text-gray-400" />;
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-white mb-4">Upload Project Document</h3>

      {error && (
        <div className="mb-4 bg-red-900/50 border border-red-700 text-red-100 px-4 py-3 rounded relative">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 mr-2" />
            <p>{error}</p>
          </div>
        </div>
      )}

      {uploading ? (
        <div className="mb-4">
          <p className="text-white mb-2">Uploading {fileName}...</p>
          <div className="w-full bg-gray-700 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
          <p className="text-gray-400 text-sm mt-1">{uploadProgress}% complete</p>
        </div>
      ) : (
        <>
          <div
            {...getRootProps()}
            className={`p-6 border-2 border-dashed rounded-lg mb-4 transition-colors duration-200 ${
              isDragActive
                ? "border-blue-500 bg-blue-900/20"
                : "border-gray-600 hover:border-blue-500 hover:bg-blue-900/10"
            }`}
          >
            <input {...getInputProps()} />
            <div className="flex flex-col items-center justify-center text-gray-400">
              {selectedFile ? (
                <div className="space-y-4">
                  <div className="relative inline-block">
                    {preview ? (
                      <img
                        src={preview}
                        alt="Preview"
                        className="max-h-48 rounded-lg mx-auto"
                      />
                    ) : (
                      getFileIcon(selectedFile.type)
                    )}
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        clearSelection();
                      }}
                      className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                      aria-label="Remove file"
                    >
                      <XIcon className="w-4 h-4" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-300">{fileName}</p>
                </div>
              ) : isDragActive ? (
                <p className="text-blue-400">Drop the file here...</p>
              ) : (
                <>
                  <Upload className="h-12 w-12 mb-4 text-gray-500" />
                  <p className="text-center mb-2">Drag & drop a file here, or click to select</p>
                  <p className="text-xs text-gray-500 text-center">
                    Supported formats: PDF, Word, Text, Images (JPG, PNG, GIF)
                  </p>
                </>
              )}
            </div>
          </div>

          {selectedFile && (
            <button
              onClick={handleUpload}
              disabled={uploading}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center justify-center"
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Document
            </button>
          )}
        </>
      )}
    </div>
  );
}
