import { NextResponse } from "next/server";
import { analyticsService, generateAnalyticsReport } from "lib/analytics";

/**
 * API route for analytics data
 */
export async function POST(request) {
  try {
    const { action, days = 30, userId, documentId } = await request.json();
    
    let result;
    
    // Handle different actions
    switch (action) {
      case "getReport":
        result = await generateAnalyticsReport(days);
        break;
        
      case "getContentPerformanceMetrics":
        result = await analyticsService.getContentPerformanceMetrics(
          documentId,
          userId,
          days
        );
        break;
        
      case "getUserActivityMetrics":
        result = await analyticsService.getUserActivityMetrics(
          days,
          100 // Default limit
        );
        break;
        
      case "trackContentView":
        if (!documentId) {
          return NextResponse.json({ 
            error: "Document ID is required" 
          }, { status: 400 });
        }
        
        result = await analyticsService.trackContentView(
          documentId,
          userId || "anonymous",
          {} // No metadata for now
        );
        break;
        
      default:
        return NextResponse.json({ 
          error: "Invalid action. Supported actions: getReport, getContentPerformanceMetrics, getUserActivityMetrics, trackContentView" 
        }, { status: 400 });
    }
    
    // Return the result
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error in analytics API:", error);
    return NextResponse.json({ 
      success: false,
      error: error.message || "An error occurred while processing analytics data"
    }, { status: 500 });
  }
}
