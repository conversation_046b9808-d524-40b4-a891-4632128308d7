/**
 * Marketing Agents Test
 * 
 * This file demonstrates how to use the marketing agents.
 * You can run this test with:
 * 
 * ```
 * npx ts-node lib/tools/tests/marketing-agents.test.ts
 * ```
 */

import { MarketingAgentManager } from '../../agents/marketing/MarketingAgentManager';
import { StrategicDirectorAgent } from '../../agents/marketing/StrategicDirectorAgent';
import { ResearchInsightsAgent } from '../../agents/marketing/ResearchInsightsAgent';
import { ContentCreatorAgent } from '../../agents/marketing/ContentCreatorAgent';
import { SocialMediaOrchestratorAgent } from '../../agents/marketing/SocialMediaOrchestratorAgent';
import { AnalyticsReportingAgent } from '../../agents/marketing/AnalyticsReportingAgent';

async function testMarketingAgents() {
  console.log('Testing Marketing Agents...');
  
  // Create a marketing agent manager
  const manager = new MarketingAgentManager({
    userId: 'test-user',
    defaultLlmProvider: 'openai',
    defaultLlmModel: 'gpt-4o'
  });
  
  // Initialize the marketing team
  console.log('Initializing marketing team...');
  const team = await manager.initializeMarketingTeam();
  
  // Test Strategic Director Agent
  console.log('\nTesting Strategic Director Agent...');
  const strategicDirector = team.strategicDirector;
  
  // Analyze a product
  console.log('Analyzing product...');
  const productAnalysis = await strategicDirector.analyzeProduct(
    'Smart Home Assistant',
    'A voice-controlled smart home assistant that integrates with various smart devices and uses AI to learn user preferences and automate home tasks.',
    'The smart home market is growing rapidly, with increasing adoption of IoT devices and voice assistants.'
  );
  
  console.log('Product analysis complete:');
  console.log(`- Product: ${productAnalysis.name}`);
  console.log(`- Unique Selling Points: ${productAnalysis.uniqueSellingPoints.slice(0, 2).join(', ')}...`);
  console.log(`- SWOT Analysis: ${productAnalysis.swotAnalysis.strengths.length} strengths, ${productAnalysis.swotAnalysis.weaknesses.length} weaknesses`);
  
  // Test Research & Insights Agent
  console.log('\nTesting Research & Insights Agent...');
  const researchAgent = team.researchInsights;
  
  // Conduct market research
  console.log('Conducting market research...');
  const marketResearch = await researchAgent.conductMarketResearch(
    'Smart Home Assistant',
    'Tech-savvy homeowners aged 25-45',
    'AI-powered learning, seamless integration with multiple device brands'
  );
  
  console.log('Market research complete. Sample:');
  console.log(marketResearch.substring(0, 200) + '...');
  
  // Test Content Creator Agent
  console.log('\nTesting Content Creator Agent...');
  const contentCreator = team.contentCreator;
  
  // Generate social media posts
  console.log('Generating social media posts...');
  const socialMediaPosts = await contentCreator.generateSocialMediaPosts(
    'Smart Home Assistant',
    'A voice-controlled smart home assistant that integrates with various smart devices and uses AI to learn user preferences and automate home tasks.',
    'Tech-savvy homeowners aged 25-45',
    ['AI-powered learning', 'Multi-device integration', 'Voice control', 'Automation'],
    ['Twitter', 'Facebook'],
    2
  );
  
  console.log('Social media posts generated:');
  for (const [platform, posts] of Object.entries(socialMediaPosts)) {
    console.log(`- ${platform}: ${posts.length} posts`);
    if (posts.length > 0) {
      console.log(`  Sample: ${posts[0].content.substring(0, 100)}...`);
    }
  }
  
  // Test Social Media Orchestrator Agent
  console.log('\nTesting Social Media Orchestrator Agent...');
  const socialMediaOrchestrator = team.socialMediaOrchestrator;
  
  // Create a hashtag strategy
  console.log('Creating hashtag strategy...');
  const hashtagStrategy = await socialMediaOrchestrator.createHashtagStrategy(
    'Twitter',
    'TechHome',
    'SmartAssist',
    'smart home technology'
  );
  
  console.log('Hashtag strategy created:');
  console.log(`- Brand hashtags: ${hashtagStrategy.brandHashtags.join(', ')}`);
  console.log(`- Trending hashtags: ${hashtagStrategy.trendingHashtags.join(', ')}`);
  
  // Test Analytics & Reporting Agent
  console.log('\nTesting Analytics & Reporting Agent...');
  const analyticsAgent = team.analyticsReporting;
  
  // Track some metrics
  console.log('Tracking metrics...');
  const engagementMetric = await analyticsAgent.trackMetric(
    'Engagement Rate',
    'Percentage of followers who engage with posts',
    3.5,
    '%',
    'engagement',
    'Twitter',
    undefined,
    5,
    2.8
  );
  
  const conversionMetric = await analyticsAgent.trackMetric(
    'Conversion Rate',
    'Percentage of website visitors who complete a purchase',
    2.1,
    '%',
    'conversion',
    undefined,
    undefined,
    3,
    1.9
  );
  
  console.log('Metrics tracked:');
  console.log(`- ${engagementMetric.name}: ${engagementMetric.value}${engagementMetric.unit} (${engagementMetric.changePercentage?.toFixed(2)}% change)`);
  console.log(`- ${conversionMetric.name}: ${conversionMetric.value}${conversionMetric.unit} (${conversionMetric.changePercentage?.toFixed(2)}% change)`);
  
  // Test creating a marketing campaign
  console.log('\nTesting marketing campaign creation...');
  const campaignId = await manager.createMarketingCampaign(
    'Smart Home Assistant',
    'A voice-controlled smart home assistant that integrates with various smart devices and uses AI to learn user preferences and automate home tasks.',
    ['Increase brand awareness', 'Generate leads', 'Drive product sales'],
    'Tech-savvy homeowners aged 25-45',
    10000,
    30
  );
  
  console.log(`Marketing campaign created with ID: ${campaignId}`);
  
  console.log('\nMarketing Agents test complete!');
}

// Run the test
testMarketingAgents().catch(error => {
  console.error('Error testing marketing agents:', error);
});
