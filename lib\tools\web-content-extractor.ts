import { WebScraperTool, ScrapedContent } from "./web-scraper";
import { ContentFormatterTool, ContentFormattingOptions } from "./content-formatter";

/**
 * Interface for extraction options
 */
export interface ExtractionOptions extends ContentFormattingOptions {
  // Web scraping options
  followLinks?: boolean;
  maxLinksToFollow?: number;
  sameOriginOnly?: boolean;
  format?: string;

  // Marketing-specific options (for convenience, these are also in ContentFormattingOptions)
  domain?: string;           // Subject matter domain (e.g., "marketing", "finance", "technology")
  subjectMatter?: string;    // Specific subject within the domain (e.g., "social media marketing")
  perspective?: string;      // Perspective to write from (e.g., "marketing", "technical", "educational")
  tone?: string;             // Tone of the content (e.g., "professional", "conversational", "persuasive")
  contentGoal?: string;      // Goal of the content (e.g., "inform", "persuade", "educate")
}

/**
 * Interface for extraction result
 */
export interface ExtractionResult {
  url: string;
  title: string;
  rawContent: string;
  formattedContent: string;
  metadata: Record<string, string | null>;
  linkedPages?: ExtractionResult[];
}

/**
 * Web Content Extractor Tool
 * Combines web scraping and content formatting into a single tool
 */
export class WebContentExtractorTool {
  private scraper: WebScraperTool;
  private formatter: ContentFormatterTool;

  constructor() {
    this.scraper = new WebScraperTool();
    this.formatter = new ContentFormatterTool();
  }

  /**
   * Extract and format content from a URL
   * @param url - The URL to extract content from
   * @param options - Extraction options
   * @returns Extraction result with raw and formatted content
   */
  async extractContent(url: string, options: ExtractionOptions = {}): Promise<ExtractionResult> {
    try {
      console.log(`Extracting content from ${url}`);

      // Set scraping options with much longer timeout for problematic sites
      const scrapingOptions = {
        timeout: options.timeout || 300000, // 5 minute default timeout
        maxRetries: options.maxRetries || 3,
        retryDelay: options.retryDelay || 5000
      };

      // Step 1: Scrape the content with improved timeout and retry handling
      const scrapedContent = await this.scraper.scrapeContent(url, scrapingOptions);

      // Step 2: Format the content
      const formattedContent = await this.formatter.formatContent(
        scrapedContent.content,
        url,
        options.format || "markdown",
        options
      );

      // Create the base result
      const result: ExtractionResult = {
        url: scrapedContent.url,
        title: scrapedContent.title,
        rawContent: scrapedContent.content,
        formattedContent,
        metadata: scrapedContent.metadata,
      };

      // Step 3: Follow links if requested
      if (options.followLinks && options.maxLinksToFollow && options.maxLinksToFollow > 0) {
        result.linkedPages = await this.followLinks(url, options);
      }

      return result;
    } catch (error) {
      // Check if it's a timeout error
      const isTimeoutError =
        error instanceof Error &&
        (error.message.includes('timeout') ||
         error.message.includes('Timeout') ||
         error.message.includes('Navigation timeout') ||
         error.message.includes('net::ERR_TIMED_OUT') ||
         error.message.includes('Headers Timeout Error'));

      if (isTimeoutError) {
        console.error(`Timeout error extracting content from ${url}:`, error);
        throw new Error(`Timeout error extracting content from ${url}. The website may be slow or blocking automated access.`);
      } else {
        console.error(`Error extracting content from ${url}:`, error);
        throw new Error(`Failed to extract content from ${url}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }

  /**
   * Extract content from multiple URLs
   * @param urls - Array of URLs to extract content from
   * @param options - Extraction options
   * @returns Array of extraction results
   */
  async extractMultiple(urls: string[], options: ExtractionOptions = {}): Promise<ExtractionResult[]> {
    const results: ExtractionResult[] = [];

    for (const url of urls) {
      try {
        const result = await this.extractContent(url, {
          ...options,
          followLinks: false // Disable link following for batch processing
        });
        results.push(result);
      } catch (error) {
        console.error(`Error processing ${url}:`, error);
        // Add a placeholder result with error information
        results.push({
          url,
          title: "Error",
          rawContent: "",
          formattedContent: `Failed to extract content: ${error instanceof Error ? error.message : String(error)}`,
          metadata: { error: error instanceof Error ? error.message : String(error) }
        });
      }
    }

    return results;
  }

  /**
   * Combine formatted content from multiple URLs into a single document
   * @param urls - Array of URLs to extract and combine
   * @param options - Extraction options
   * @returns Combined formatted content
   */
  async combineContent(urls: string[], options: ExtractionOptions = {}): Promise<string> {
    const results = await this.extractMultiple(urls, options);

    let combinedContent = `# Combined Content from Multiple Sources\n\n`;

    for (const result of results) {
      combinedContent += `## ${result.title}\n`;
      combinedContent += `Source: ${result.url}\n\n`;
      combinedContent += result.formattedContent;
      combinedContent += `\n\n---\n\n`;
    }

    return combinedContent;
  }

  /**
   * Follow links from a page and extract their content
   * @param baseUrl - The base URL to follow links from
   * @param options - Extraction options
   * @returns Array of extraction results from linked pages
   * @private
   */
  private async followLinks(baseUrl: string, options: ExtractionOptions): Promise<ExtractionResult[]> {
    try {
      // Set scraping options with much longer timeout for problematic sites
      const scrapingOptions = {
        timeout: options.timeout || 300000, // 5 minute default timeout
        maxRetries: options.maxRetries || 3,
        retryDelay: options.retryDelay || 5000
      };

      // Extract links from the page with extended timeout
      const links = await this.scraper.extractLinks(
        baseUrl,
        options.sameOriginOnly !== false, // Default to same origin only
        scrapingOptions
      );

      // Limit the number of links to follow
      const maxLinks = Math.min(links.length, options.maxLinksToFollow || 3);
      const linksToFollow = links.slice(0, maxLinks);

      // Process each link
      const linkedResults: ExtractionResult[] = [];

      for (const link of linksToFollow) {
        try {
          // Recursive call with reduced depth
          const linkedResult = await this.extractContent(link, {
            ...options,
            followLinks: false, // Prevent infinite recursion
            maxLinksToFollow: 0
          });

          linkedResults.push(linkedResult);
        } catch (error) {
          console.error(`Error following link ${link}:`, error);
          // Skip failed links
        }
      }

      return linkedResults;
    } catch (error) {
      console.error(`Error following links from ${baseUrl}:`, error);
      return []; // Return empty array on error
    }
  }
}

// Export a singleton instance
export const webContentExtractorTool = new WebContentExtractorTool();

// Export a standalone function for backward compatibility
export async function extractAndFormatContent(url: string, options: ExtractionOptions = {}): Promise<string> {
  const result = await webContentExtractorTool.extractContent(url, options);
  return result.formattedContent;
}
