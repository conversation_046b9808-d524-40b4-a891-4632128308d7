import { TokenManagement } from "@/src/tokenTracker/tokenManagement";
import { ChatHistoryAgent } from "./ChatHistoryAgent";
import { InternetSearchAgent } from "./InternetSearchAgent";
import { ProcessGroqMessagesAgent } from "./ProcessGroqMessagesAgent";
import { StreamOptions } from "@/src/types/shared";
import { ProcessChatHistoryTool } from "components/tools/ProcessChatHistoryTool";
import { StreamToClientTool } from "components/tools/StreamToClientTool";
import { BasePromptValueInterface } from "@langchain/core/prompt_values";
import { VisualizationAgent } from "./VisualizationAgent";
import { AnalyticsMiddleware } from "lib/analytics/AnalyticsMiddleware";
import { VisualizationData } from "@/src/types/VisualizationTypes";
import { GenerateImageTool } from "components/tools/generateImageTool";
import { GenerateImageAgent } from "./GenerateImageAgent";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";

interface ChartGenerationProgress {
  stage: 'initializing' | 'processing' | 'generating' | 'indexing' | 'complete' | 'error';
  message: string;
  progress?: number;
}

interface ChartGenerationOptions {
  prompt: string;
  userId: string;
  onProgress?: (progress: ChartGenerationProgress) => void;
}

interface ChartGenerationResult {
  success: boolean;
  jobId?: string;
  chartUrl?: string;
  error?: string;
  namespace?: string;
}

interface SupervisorAgentConfig {
  tokenManagement: TokenManagement;
  streamOptions: StreamOptions;
}

interface SupervisorAgentTools {
  chatHistoryTool: ProcessChatHistoryTool;
  streamTool: StreamToClientTool;
  groqMessagesTool: ProcessGroqMessagesAgent;
  visualizationAgent?: VisualizationAgent;
  generateImageTool: GenerateImageTool;
}

interface SupervisorAgentInput {
  streamOptions: StreamOptions;
  queryVector: number[];
  namespaces: string[] | null;
  userQuery: string;
  rawChatHistory: string;
  category: string | null;
  userId: string;
  sessionEmail?: string;
  data?: any[];
  visualizationOptions?: {
    preferredType?: string;
    maxCategories?: number;
    minDataPoints?: number;
  };
}

interface SupervisorAgentOutput {
  success: boolean;
  error?: string;
  data?: {
    chatHistory: any;
    internetSearchResults?: {
      content: string;
      sources: string[];
      relevance: number;
    };
    visualization?: VisualizationData;
    generatedImage?: string;
  };
}

interface ProcessMessageInput {
  userEmail: string;
  streamOptions: StreamOptions;
  prompt: BasePromptValueInterface;
  data?: any[];
  config?: {
    temperature?: number;
    maxTokens?: number;
    visualizationOptions?: {
      preferredType?: string;
      maxCategories?: number;
      minDataPoints?: number;
    };
  };
  userId: string;
}

interface ProcessMessageOutput {
  success: boolean;
  error?: string;
  response?: any;
  visualization?: {
    type: "chart" | "table";
    subtype?: string;
    config: Record<string, any>;
    data: any[];
    reasoning?: string;
    metadata?: {
      type: string;
      metrics: string[];
      timeUnit?: string;
    };
    confidence?: number;
    url: string;
  };
  generatedImage?: string;
}

interface VisualizationRequest {
  query: string;
  data: any[];
  options: {
    type?: "chart" | "table";
    features?: string[];
    formatting?: Record<string, any>;
    maxCategories?: number;
    minDataPoints?: number;
  };
  streamOptions: StreamOptions;
}

export class SupervisorAgent {
  private chatHistoryAgent: ChatHistoryAgent;
  private internetSearchAgent: InternetSearchAgent;
  private groqMessagesAgent: ProcessGroqMessagesAgent;
  private visualizationAgent?: VisualizationAgent;
  private generateImageAgent: GenerateImageAgent;
  private analytics?: AnalyticsMiddleware;
  private readonly streamTool: StreamToClientTool;
  private retryAttempts = 3;
  private retryDelayBase = 1000;
  private defaultColors = [
    "#FF6384",
    "#36A2EB",
    "#FFCE56",
    "#4BC0C0",
    "#9966FF",
    "#FF9F40"
  ];

  static description = `SupervisorAgent orchestrates and coordinates multiple specialized agents to:
  - Manage chat history processing and token tracking
  - Coordinate internet searches when needed
  - Handle data visualization requests
  - Process messages through Groq's API
  - Generate images based on user prompts
  - Manage error handling and retries
  - Stream management for real-time responses
  - Analytics tracking and monitoring`;

  constructor(
    private readonly config: SupervisorAgentConfig,
    private readonly tools: SupervisorAgentTools,
    analytics?: AnalyticsMiddleware
  ) {
    this.analytics = analytics;
    this.streamTool = tools.streamTool;
    this.chatHistoryAgent = new ChatHistoryAgent(this.tools.chatHistoryTool);
    this.internetSearchAgent = new InternetSearchAgent({
      maxResults: 3,
      tokenManager: this.config.tokenManagement
    });
    this.groqMessagesAgent = new ProcessGroqMessagesAgent(
      this.tools.streamTool,
      this.tools.visualizationAgent
    );

    this.visualizationAgent = tools.visualizationAgent;
    this.generateImageAgent = new GenerateImageAgent({
      generateImageTool: tools.generateImageTool
    });

    console.log("SupervisorAgent: Initialized with required agents and tools");
  }

  async process(input: SupervisorAgentInput): Promise<SupervisorAgentOutput> {
    try {
      console.log("SupervisorAgent: Starting processing pipeline");
      this.validateInput(input);

      if (this.analytics) {
        await this.analytics.track("supervisor_process_start", {
          userId: input.userId,
          category: input.category
        });
      }

      const chatHistoryResult = await this.processChatHistory(input);
      if (!chatHistoryResult.success) {
        throw new Error(`Chat history processing failed: ${chatHistoryResult.error}`);
      }

      const outputData: NonNullable<SupervisorAgentOutput["data"]> = {
        chatHistory: chatHistoryResult
      };

      console.log("Here is the user request in SupervisorAgent:", input.userQuery);

 
      const requiresInternetSearch = this.determineIfSearchNeeded(input.userQuery);
      if (requiresInternetSearch) {
        const searchResult = await this.performInternetSearch(input.userQuery);
        if (searchResult.success && searchResult.content && searchResult.sources && searchResult.relevance) {
          outputData.internetSearchResults = {
            content: searchResult.content,
            sources: searchResult.sources,
            relevance: searchResult.relevance
          };
        }
      }

      return {
        success: true,
        data: outputData
      };
    } catch (error) {
      console.error("SupervisorAgent: Processing failed", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  async processGroqMessages(input: ProcessMessageInput): Promise<ProcessMessageOutput> {
    console.log("SupervisorAgent: Starting Groq message processing");
    try {
      this.streamTool.startProcessing(input.streamOptions);

      if (input.streamOptions?.metadata) {
        await this.streamTool.streamMetadata(input.streamOptions, input.streamOptions.metadata);
      }

      const userQuery = await this.extractUserQuery(input.prompt);
      if (this.isImageGenerationRequest(userQuery)) {
        const imageUrl = await this.generateImage(userQuery);
        if (imageUrl) {
          const imageJson = `{"type":"image","url":"${imageUrl}"}`;
          await this.streamTool.streamToken(input.streamOptions, imageJson);
          await this.streamTool.finishProcessing(input.streamOptions);

          return {
            success: true,
            response: { generations: [{ text: imageJson }] },
            generatedImage: imageUrl
          };
        }
      }

      console.log("SupervisorAgent: Delegating to ProcessGroqMessagesAgent");
      const result = await this.groqMessagesAgent.processMessage({
        userEmail: input.userEmail,
        streamOptions: input.streamOptions,
        prompt: input.prompt,
        data: input.data,
        config: {
          temperature: input.config?.temperature ?? 0.3,
          maxTokens: input.config?.maxTokens ?? 5000,
          visualizationOptions: input.config?.visualizationOptions
        }
      });

      if (!result.success) {
        throw new Error(result.error || "Failed to process messages");
      }

      console.log("SupervisorAgent: Message processing completed successfully", JSON.stringify(result, null, 2));

      let visualization: VisualizationData | undefined;

      if (result.visualization?.content) {
        const { type, config, data, reasoning = "" } = result.visualization.content;
        const metadata = result.visualization.content.metadata;
        const confidence = result.visualization.content.confidence;

        if (type && config && data && metadata && confidence !== undefined) {
          visualization = {
            charttype: "visualization",
            content: {
              type,
              config: config as {
                type: string;
                stacked: boolean;
                xAxis: string;
                yAxis: string;
                series: string[];
                colors: string[];
              },
              data,
              metadata: {
                type: metadata.type,
                metrics: metadata.metrics,
                timeUnit: metadata.timeUnit
              },
              confidence: confidence,
              reasoning: reasoning
            }
          };

          const encodedVisualization = JSON.stringify(visualization);
          const visualizationUrl = `/visualization?data=${encodeURIComponent(encodedVisualization)}`;
          visualization.content.url = visualizationUrl;

          console.log("SupervisorAgent: Generated Visualization URL:", visualizationUrl);

          const visualizationJson = `\n\n\`\`\`json\n${JSON.stringify(visualization, null, 2)}\n\`\`\``;
          await this.streamTool.streamToken(input.streamOptions, visualizationJson);
          console.log("SupervisorAgent: Embedded Visualization JSON into AI message text");
        } else {
          console.error("SupervisorAgent: Missing metadata or confidence in visualization result");
        }
      }

      return {
        ...result,
        visualization: visualization
          ? {
              type: visualization.content.type,
              config: visualization.content.config,
              data: visualization.content.data,
              reasoning: visualization.content.reasoning,
              metadata: visualization.content.metadata,
              confidence: visualization.content.confidence,
              url: visualization.content.url || ""
            }
          : undefined
      };
    } catch (error) {
      return await this.handleError(error, input);
    } finally {
      console.log("SupervisorAgent: Processing cycle complete");
    }
  }

  async processWithRetry(input: ProcessMessageInput): Promise<ProcessMessageOutput> {
    let lastError: Error | undefined;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        console.log(`SupervisorAgent: Attempt ${attempt} of ${this.retryAttempts}`);
        return await this.processGroqMessages(input);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`SupervisorAgent: Attempt ${attempt} failed:`, lastError.message);

        if (attempt < this.retryAttempts) {
          const delay = Math.min(this.retryDelayBase * Math.pow(2, attempt - 1), 10000);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    return await this.handleError(lastError || new Error("All retry attempts failed"), input);
  }

  // async processVisualization(
  //   request: VisualizationRequest
  // ): Promise<{ success: boolean; visualization?: VisualizationData; error?: string }> {
  //   if (!this.visualizationAgent) {
  //     return {
  //       success: false,
  //       error: "Visualization agent not initialized"
  //     };
  //   }

  //   try {
  //     const result = await this.visualizationAgent.process(request);
  //     if (!result.success || !result.data || !result.analysis) {
  //       return {
  //         success: false,
  //         error: result.error || "Failed to process visualization"
  //       };
  //     }
  //     const { data, analysis } = result;
  //     const metadata = data[0]?.metadata || {};
  //     const xAxis = metadata.timeUnit ? "time" : "category";
  //     const yAxis = metadata.metrics ? metadata.metrics[0] : undefined;
  //     const series = metadata.metrics || [];
  //     const colors = this.defaultColors.slice(0, series.length);

  //     const chartType = request.options.type || "stacked-bar";
  //     const isStacked = chartType === "stacked-bar";

  //     const visualization: VisualizationData = {
  //       charttype: "visualization",
  //       content: {
  //         type: "chart",
  //         config: {
  //           type: chartType,
  //           stacked: isStacked,
  //           xAxis,
  //           yAxis,
  //           series,
  //           colors
  //         },
  //         data: data.map(item => item.data),
  //         metadata: {
  //           type: data[0]?.metadata.type,
  //           metrics: data[0]?.metadata.metrics,
  //           timeUnit: data[0]?.metadata.timeUnit
  //         },
  //         confidence: analysis.confidence,
  //         reasoning: analysis.suggestion?.reason || ""
  //       }
  //     };
  //     return {
  //       success: true,
  //       visualization
  //     };
  //   } catch (error) {
  //     console.error("SupervisorAgent: Visualization processing failed", error);
  //     return {
  //       success: false,
  //       error: error instanceof Error ? error.message : "Unknown error occurred"
  //     };
  //   }
  // }

  async generateChart(options: ChartGenerationOptions): Promise<ChartGenerationResult> {
    try {
      options.onProgress?.({
        stage: 'initializing',
        message: 'Initializing chart generation...'
      });

      const initResult = await this.initializeChartGeneration(options);
      if (!initResult.success) {
        throw new Error(initResult.error || 'Failed to initialize chart generation');
      }

      options.onProgress?.({
        stage: 'processing',
        message: 'Processing data for visualization...'
      });

      const processResult = await this.processChartGeneration({
        jobId: initResult.jobId!,
        userId: options.userId,
      });

      if (!processResult.success) {
        throw new Error(processResult.error || 'Failed to process chart');
      }

      options.onProgress?.({
        stage: 'complete',
        message: 'Chart generation complete!'
      });

      return {
        success: true,
        jobId: initResult.jobId,
        chartUrl: processResult.chartUrl,
        namespace: processResult.namespace
      };

    } catch (error) {
      options.onProgress?.({
        stage: 'error',
        message: error instanceof Error ? error.message : 'An error occurred'
      });

    // ... existing code ...
    if (this.analytics) {
      await this.analytics.track('chart_generation_error', {
        userId: options.userId,
        category: null, // Assuming 'category' is expected
        // Remove 'error' if it's not expected
      });
    }
// ... existing code ...

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Chart generation failed'
      };
    }
  }

  private async initializeChartGeneration(options: ChartGenerationOptions): Promise<{ success: boolean; jobId?: string; error?: string }> {
    let attempts = 0;
    const maxRetries = this.retryAttempts;

    while (attempts < maxRetries) {
      try {
        const response = await fetch('/api/initializeChartGeneration', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            prompt: options.prompt,
            userId: options.userId
          })
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to initialize chart');
        }

        const data = await response.json();
        return {
          success: true,
          jobId: data.jobId
        };

      } catch (error) {
        attempts++;
        if (attempts === maxRetries) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed after retry attempts'
          };
        }
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
      }
    }

    return {
      success: false,
      error: 'Exceeded maximum retry attempts'
    };
  }

  private async processChartGeneration(params: { jobId: string; userId: string }): Promise<{ success: boolean; chartUrl?: string; namespace?: string; error?: string }> {
    let attempts = 0;
    const maxRetries = this.retryAttempts;

    while (attempts < maxRetries) {
      try {
        const response = await fetch('/api/processChartGeneration', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(params)
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process chart');
        }

        const data = await response.json();
        return {
          success: true,
          chartUrl: data.chartUrl,
          namespace: data.namespace
        };

      } catch (error) {
        attempts++;
        if (attempts === maxRetries) {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Failed after retry attempts'
          };
        }
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempts) * 1000));
      }
    }

    return {
      success: false,
      error: 'Exceeded maximum retry attempts'
    };
  }

  private determineIfSearchNeeded(query: string): boolean {
    return (
      query.toLowerCase().includes("search") ||
      query.toLowerCase().includes("find") ||
      query.toLowerCase().includes("latest")
    );
  }

  async performInternetSearch(
    query: string
  ): Promise<{ success: boolean; content?: string; sources?: string[]; relevance?: number; error?: string }> {
    console.log("SupervisorAgent: Delegating to InternetSearchAgent");
    try {
      const searchResults = await this.internetSearchAgent.search(query);

      if (!searchResults.success) {
        console.log("Internet search failed:", searchResults.error);
        return {
          success: false,
          error: searchResults.error
        };
      }

      return {
        success: true,
        content: this.formatSearchContent(searchResults.results),
        sources: searchResults.results.map((r: any) => r.link),
        relevance: this.calculateRelevanceScore(searchResults.results)
      };
    } catch (error) {
      console.error("SupervisorAgent: Internet search failed", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  private async processChatHistory(input: SupervisorAgentInput): Promise<any> {
    console.log("SupervisorAgent: Processing chat history");
    return await this.chatHistoryAgent.process({
      rawChatHistory: input.rawChatHistory,
      tokenManager: this.config.tokenManagement
    });
  }

  private async generateImage(prompt: string): Promise<string | undefined> {
    try {
      const response = await this.generateImageAgent.generateImage({ prompt });

      if (!response.base64Image) {
        console.error('No base64Image returned:', response.error);
        return undefined;
      }

      const storageService = getStorage();
      const fileName = `generated-images/${Date.now()}-${Math.random()
        .toString(36)
        .substring(7)}.png`;
      const imageRef = ref(storageService, fileName);

      const byteCharacters = atob(response.base64Image);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const imageBlob = new Blob([byteArray], { type: 'image/png' });

      const uploadTask = await uploadBytes(imageRef, imageBlob);
      const downloadUrl = await getDownloadURL(uploadTask.ref);

      console.log('SupervisorAgent: Image uploaded successfully:', downloadUrl);
      return downloadUrl;
    } catch (error) {
      console.error('SupervisorAgent: Error generating or uploading image:', error);
      return undefined;
    }
  }

  private async handleError(error: Error | unknown, input: ProcessMessageInput): Promise<ProcessMessageOutput> {
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    console.error("SupervisorAgent: Error encountered:", errorMessage);

    try {
      if (this.streamTool) {
        await this.streamTool.streamError(
          input.streamOptions,
          error instanceof Error ? error : new Error(String(error))
        );
        await this.streamTool.finishProcessing(input.streamOptions);
      }

      if (this.analytics) {
        this.analytics.track("supervisor_process_error", {
          userId: input.userId,
          category: null
        });
      }
    } catch (streamError) {
      console.error("SupervisorAgent: Error while handling error:", streamError);
    }

    return {
      success: false,
      error: errorMessage
    };
  }

  private formatSearchContent(results: any[]): string {
    if (!results?.length) return "";

    const disclaimer = `
[NOTE: The requested information was not found in the internal knowledge base. 
The following information has been sourced from external internet searches.]

`;

    const formattedResults = results
      .map((result, index) => {
        return `[Source ${index + 1}] ${result.title}
URL: ${result.link}
Last Updated: ${result.updated || "Not specified"}
Content: ${result.snippet}
---`;
      })
      .join("\n\n");

    return disclaimer + formattedResults;
  }

  private calculateRelevanceScore(results: any[]): number {
    if (!results?.length) return 0;

    const totalScore = results.reduce((score, result, index) => {
      const positionScore = 1 / (index + 1);
      const contentScore = result.snippet ? 1 : 0.5;
      const titleScore = result.title ? 0.5 : 0;
      return score + positionScore * (contentScore + titleScore);
    }, 0);

    return Number((totalScore / results.length).toFixed(2));
  }

  private isImageGenerationRequest(query: string): boolean {
    const lower = query.toLowerCase();
    return (
      lower.includes("generate an image") ||
      lower.includes("create an image") ||
      lower.includes("draw an image") ||
      lower.includes("image please")
    );
  }

  private validateInput(input: SupervisorAgentInput): void {
    if (!input.userId) {
      throw new Error("UserId is required");
    }
    if (!input.queryVector || !Array.isArray(input.queryVector)) {
      throw new Error("Valid queryVector is required");
    }
    if (typeof input.userQuery !== "string") {
      throw new Error("Valid userQuery is required");
    }
    if (typeof input.rawChatHistory !== "string") {
      throw new Error("Valid rawChatHistory is required");
    }
    if (input.data !== undefined && !Array.isArray(input.data)) {
      throw new Error("Data must be an array when provided");
    }
    if (input.visualizationOptions) {
      if (
        input.visualizationOptions.maxCategories &&
        typeof input.visualizationOptions.maxCategories !== "number"
      ) {
        throw new Error("maxCategories must be a number");
      }
      if (
        input.visualizationOptions.minDataPoints &&
        typeof input.visualizationOptions.minDataPoints !== "number"
      ) {
        throw new Error("minDataPoints must be a number");
      }
    }
  }

  private async extractUserQuery(prompt: BasePromptValueInterface): Promise<string> {
    const messages = await prompt.toChatMessages();
    const userMessage = messages.find((msg: any) => msg.type === "human");
    if (!userMessage) return "";
    return typeof userMessage.content === "string"
      ? userMessage.content
      : JSON.stringify(userMessage.content);
  }
}

export function createSupervisorAgent(streamOptions: StreamOptions): SupervisorAgent {
  const analyticsMiddleware = new AnalyticsMiddleware();
  const visualizationAgent = new VisualizationAgent({
    maxResults: 100,
    defaultVisualizationType: "chart"
  });

  return new SupervisorAgent(
    {
      tokenManagement: new TokenManagement(analyticsMiddleware),
      streamOptions
    },
    {
      chatHistoryTool: new ProcessChatHistoryTool(),
      streamTool: new StreamToClientTool(),
      groqMessagesTool: new ProcessGroqMessagesAgent(
        new StreamToClientTool(),
        visualizationAgent
      ),
      visualizationAgent,
      generateImageTool: new GenerateImageTool(process.env.OPENAI_API_KEY || "")
    },
    analyticsMiddleware
  );
}

