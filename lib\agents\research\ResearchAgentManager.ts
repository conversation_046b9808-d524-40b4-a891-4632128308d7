// research/ResearchAgentManager.ts

import { ResearchAgent } from './ResearchAgent';
import { ResearchLeadAgent } from './ResearchLeadAgent';
import { InformationRetrievalAgent } from './InformationRetrievalAgent';
import { DataAnalystSynthesizerAgent } from './DataAnalystSynthesizerAgent';
import { ReportWriterFormatterAgent } from './ReportWriterFormatterAgent';
import { QualityAssuranceReviewerAgent } from './QualityAssuranceReviewerAgent';
import { AgentMemoryManager } from '../AgentMemoryManager';
import { LlmProvider } from '../../tools/llm-tool';
import { ResearchPlan, ResearchTaskRequest } from './ResearchInterfaces'; // Adjust path
import { AgenticTeamId } from '../pmo/PMOInterfaces'; // PMO Integration

export interface ResearchAgentTeam {
  researchLead: ResearchLeadAgent;
  infoRetriever: InformationRetrievalAgent; // Can have multiple later
  analystSynthesizer: DataAnalystSynthesizerAgent;
  reportWriter: ReportWriterFormatterAgent;
  qaReviewer: QualityAssuranceReviewerAgent;
}

export interface ResearchAgentManagerConfig {
  userId: string;
  defaultLlmProvider?: LlmProvider;
  defaultLlmModel?: string;
}

/**
 * Manages and coordinates the Research Agent team
 */
export class ResearchAgentManager {
  private agents: Map<string, ResearchAgent> = new Map();
  private memoryManager: AgentMemoryManager;
  private userId: string;
  private defaultLlmProvider: LlmProvider;
  private defaultLlmModel: string;
  private team?: ResearchAgentTeam; // Hold initialized team instance

  constructor(config: ResearchAgentManagerConfig) {
    this.userId = config.userId;
    this.defaultLlmProvider = config.defaultLlmProvider || 'openai';
    this.defaultLlmModel = config.defaultLlmModel || 'o3-2025-04-16';
    // Ensure Memory Manager uses a path suitable for research agents if needed
    this.memoryManager = new AgentMemoryManager(this.userId); // Potentially adjust Firestore paths inside manager if needed
  }

  /**
   * Initialize the research agent team
   */
  async initializeResearchTeam(): Promise<ResearchAgentTeam> {
    console.log(`Initializing Research Team for user ${this.userId}...`);
    const researchLead = new ResearchLeadAgent(
      'research-lead', 'Research Lead', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );
    const infoRetriever = new InformationRetrievalAgent(
      'info-retriever-1', 'Information Retriever', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    ); // Name includes index for potential multiple retrievers
    const analystSynthesizer = new DataAnalystSynthesizerAgent(
      'data-analyst', 'Data Analyst & Synthesizer', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );
    const reportWriter = new ReportWriterFormatterAgent(
      'report-writer', 'Report Writer & Formatter', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );
    const qaReviewer = new QualityAssuranceReviewerAgent(
      'qa-reviewer', 'Quality Assurance Reviewer', this.userId, this.defaultLlmProvider, this.defaultLlmModel
    );

    this.registerAgent(researchLead);
    this.registerAgent(infoRetriever);
    this.registerAgent(analystSynthesizer);
    this.registerAgent(reportWriter);
    this.registerAgent(qaReviewer);

    this.team = {
        researchLead,
        infoRetriever,
        analystSynthesizer,
        reportWriter,
        qaReviewer
    };
    console.log("Research Team Initialized.");
    return this.team;
  }

  /**
   * Register an agent with the manager
   */
  registerAgent(agent: ResearchAgent): void {
    const agentInfo = agent.getInfo();
    if (this.agents.has(agentInfo.id)) {
        console.warn(`Agent with ID ${agentInfo.id} (${agentInfo.name}) is already registered. Overwriting.`);
    }
    this.agents.set(agentInfo.id, agent);
    console.log(`Research Agent registered: ${agentInfo.name} (${agentInfo.id})`);
  }

  /**
   * Get an agent by ID
   */
  getAgent(agentId: string): ResearchAgent | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Get all registered agents
   */
  getAllAgents(): ResearchAgent[] {
    return Array.from(this.agents.values());
  }

   /**
    * Get the initialized team structure
    */
   getTeam(): ResearchAgentTeam | undefined {
       return this.team;
   }

  /**
   * Start a new research task. Orchestrates the initial call to the Research Lead.
   * @param request - The research task details.
   * @returns The main task ID (or plan ID) initiated by the lead.
   */
  async startResearchTask(request: ResearchTaskRequest): Promise<string> {
    if (!this.team) {
      console.log("Team not initialized. Initializing now...");
      await this.initializeResearchTeam();
    }
    if (!this.team) { // Check again after initialization attempt
        throw new Error("Research team could not be initialized.");
    }

    const leadAgent = this.team.researchLead;
    if (!leadAgent) {
      throw new Error("Research Lead agent not found in the initialized team.");
    }

    console.log(`[Manager] Starting research task: ${request.topic} via agent ${leadAgent.getInfo().id}`);
    // Assign a unique ID if not provided
    if (!request.taskId) {
        request.taskId = `task-${Date.now()}-${Math.random().toString(36).substring(2, 7)}`;
    }

    try {
      // The lead agent handles the decomposition and assignment internally
      const planId = await leadAgent.initiateResearchTask(request);
      console.log(`[Manager] Research task initiated. Plan ID: ${planId}`);

      // The manager *could* monitor the overall plan status here,
      // or handle message routing if agents don't know each other's exact IDs.
      // For now, we assume the Lead manages the internal workflow.
      await this.routeTasksFromLead(planId); // Example: Resolve agent IDs

      return planId; // Return the identifier for the research process
    } catch (error) {
      console.error(`[Manager] Failed to start research task:`, error);
      throw error;
    }
  }


   /**
    * Resolve placeholder agent roles in a plan to actual agent IDs and assign.
    * This is a crucial step for the manager.
    */
   async routeTasksFromLead(planId: string): Promise<void> {
       const leadAgent = this.team?.researchLead;
       if (!leadAgent) return;

       // Retrieve the plan (assuming it's stored in lead's memory)
       const plan: ResearchPlan | undefined = leadAgent['memory'].Agent_Response[`plan_${planId}`]; // Accessing protected/private for example
       if (!plan) {
           console.error(`[Manager] Plan ${planId} not found in Lead's memory.`);
           return;
       }

       const agentRoleMap: { [key: string]: string | undefined } = {
           'InformationRetrieval': this.team?.infoRetriever.getInfo().id,
           'DataAnalystSynthesizer': this.team?.analystSynthesizer.getInfo().id,
           'ReportWriterFormatter': this.team?.reportWriter.getInfo().id,
           'QualityAssuranceReviewer': this.team?.qaReviewer.getInfo().id,
           'ResearchLead': this.team?.researchLead.getInfo().id, // Lead might assign tasks back?
       };

       for (const subTask of plan.subTasks) {
           if (subTask.assignedToAgentId.startsWith('agent_for_')) {
               const role = subTask.assignedToAgentId.replace('agent_for_', '');
               const actualAgentId = agentRoleMap[role];

               if (actualAgentId) {
                   subTask.assignedToAgentId = actualAgentId; // Resolve the ID in the plan

                   // Check dependencies before assigning (improved check)
                   const inputsReady = (subTask.requiredInputs || []).every(inputId => {
                       const inputTask = plan.subTasks.find(st => st.subTaskId === inputId);
                       return inputTask?.status === 'completed';
                   });

                   if (subTask.status === 'pending' && inputsReady) {
                       console.log(`[Manager] Routing task ${subTask.subTaskId} to actual agent ${actualAgentId} (${role})`);

                       // Create the task persistently via Manager/MemoryManager or directly via Lead
                       // Using lead to create the task for consistency:
                       await leadAgent.createTask(
                           `Sub-Task: ${subTask.instruction.substring(0, 50)}...`, // Title
                           subTask.instruction, // Description
                           'medium', // Priority
                           actualAgentId, // Assigned Agent
                           undefined, // Due date
                           { // Metadata
                               planId: plan.planId,
                               subTaskId: subTask.subTaskId,
                               parentTaskId: plan.parentTaskId,
                               // Include necessary input data here by fetching results of completed dependencies
                           }
                       );
                        // Update local status in the plan held by the lead
                        subTask.status = 'in-progress'; // Or keep pending until agent picks up?
                   }
               } else {
                   console.error(`[Manager] Could not find agent for role: ${role} for task ${subTask.subTaskId}`);
                    subTask.status = 'failed'; // Cannot assign
               }
           }
       }
        // Save the updated plan with resolved IDs back to the lead's memory
        leadAgent['memory'].Agent_Response[`plan_${planId}`] = plan;
        await leadAgent['saveMemoryToStorage'](); // Trigger save
        console.log(`[Manager] Task routing for plan ${planId} complete.`);
   }


  /**
   * Get the memory manager instance
   */
  getMemoryManager(): AgentMemoryManager {
    return this.memoryManager;
  }

  /**
   * PMO Integration: Create strategic implementation plan as Research team coordinator
   * ResearchAgentManager acts as the strategic director who coordinates team members
   */
  async createPMOStrategicPlan(params: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    pmoAssessment: string;
    teamSelectionRationale: string;
    priority: string;
    category: string;
    requirementsDocument?: string;
  }): Promise<{
    success: boolean;
    strategicPlan?: string;
    documentTitle?: string;
    documentUrl?: string;
    error?: string;
  }> {
    try {
      console.log(`[ResearchAgentManager] Creating strategic implementation plan for PMO project: ${params.projectTitle}`);

      if (!this.team) {
        throw new Error('Research team not initialized');
      }

      const leadAgent = this.team.researchLead;

      // Retrieve PMO context using InformationRetrievalAgent coordination
      const pmoContext = await leadAgent.retrievePMOTasks(params.pmoId, params.projectDescription);

      // Step 1: Use QueryDocumentsAgent to extract detailed content from PMO Assessment
      console.log(`[ResearchAgentManager] Using QueryDocumentsAgent to analyze PMO Assessment content`);
      let pmoAssessmentAnalysis = '';
      try {
        const queryResult = await leadAgent.queryDocuments(
          `Extract key findings, recommendations, and strategic insights from PMO Assessment: ${params.pmoAssessment}`,
          this.userId,
          params.category,
          undefined,
          false, // Don't use internet search for internal PMO data
          'claude-sonnet-4-20250514' // Use high-performance model
        );

        if (queryResult.success && queryResult.content) {
          pmoAssessmentAnalysis = queryResult.content;
          console.log(`[ResearchAgentManager] Successfully extracted PMO Assessment insights (${pmoAssessmentAnalysis.length} characters)`);
        } else {
          console.log(`[ResearchAgentManager] No additional PMO Assessment insights found, using original assessment`);
          pmoAssessmentAnalysis = params.pmoAssessment;
        }
      } catch (queryError) {
        console.warn(`[ResearchAgentManager] PMO Assessment query failed, using original assessment:`, queryError);
        pmoAssessmentAnalysis = params.pmoAssessment;
      }

      // Step 2: Use QuestionAnswerAgent to analyze PMO Assessment and generate strategic questions
      console.log(`[ResearchAgentManager] Using QuestionAnswerAgent to analyze PMO Assessment for strategic insights`);
      let qaAnalysis = null;
      try {
        qaAnalysis = await leadAgent.processQuestionAnswer(
          `Analyze this PMO Assessment and identify key strategic questions and implementation considerations: ${params.pmoAssessment}`,
          pmoAssessmentAnalysis,
          params.category
        );

        if (qaAnalysis && qaAnalysis.success) {
          console.log(`[ResearchAgentManager] Successfully generated strategic Q&A analysis from PMO Assessment`);
        }
      } catch (qaError) {
        console.warn(`[ResearchAgentManager] Q&A analysis failed:`, qaError);
      }

      // Generate comprehensive strategic plan as Research team coordinator
    const strategicPlanPrompt = `
      You are the ResearchAgentManager, the strategic coordinator and director for the Research team. You receive PMO tasks and coordinate a specialized research team to deliver comprehensive strategic implementation plans.

      YOUR ROLE AS STRATEGIC COORDINATOR:
      - Receive and analyze PMO requirements as the primary entry point for research tasks
      - Create a detailed version of PMO objectives, requirements, and deliverables
      - Create comprehensive strategic implementation plans that coordinate team member expertise
      - Delegate specific aspects of work to specialized team members:
      You are the **ResearchAgentManager**, the strategic coordinator and director for the Research team. Your core mission is to receive PMO tasks, orchestrate a specialized research team, and deliver comprehensive, actionable strategic implementation plans.

      **YOUR STRATEGIC COORDINATOR ROLE:**
      - **Analyze & Interpret:** Deconstruct PMO requirements into clear objectives and deliverables.
      - **Orchestrate Team:** Coordinate and direct specialized agents (InformationRetrievalAgent, DataAnalystSynthesizerAgent, ReportWriterFormatterAgent, QualityAssuranceReviewerAgent).
      - **Delegate & Oversee:** Assign specific work to team members and ensure their contributions align with the overall strategy.
      - **Synthesize & Consolidate:** Integrate diverse team outputs into a cohesive, high-quality strategic plan.
      - **Ensure Alignment:** Guarantee the final plan meets PMO objectives and leverages the unique specializations of each team member.

      **CRITICAL INSTRUCTION:** This is a PMO-delegated research task. Your plan must **ELABORATE and BUILD UPON** the detailed PMO assessment provided. Use the PMO analysis as your foundational starting point, enhancing it with coordinated research team insights and strategic recommendations.

      **PROJECT INFORMATION:**
      - **Title:** ${params.projectTitle}
      - **Description:** ${params.projectDescription}
      - **Priority:** ${params.priority}
      - **Category:** ${params.category}

      **ORIGINAL PMO ASSESSMENT:**
      ${params.pmoAssessment}

      **ENHANCED PMO ASSESSMENT ANALYSIS (QueryDocumentsAgent Insights):**
      ${pmoAssessmentAnalysis}

      ${qaAnalysis && qaAnalysis.success ? `
      **STRATEGIC Q&A ANALYSIS (QuestionAnswerAgent Insights):**
      ${qaAnalysis.summary || qaAnalysis.content || 'Strategic analysis completed'}

      **Key Questions Identified & Addressed:**
      ${qaAnalysis.questions ? qaAnalysis.questions.map((q: any) => `- ${q.question}: ${q.answer}`).join('\n') : 'Strategic questions analyzed'}
      ` : ''}

      **TEAM SELECTION RATIONALE:**
      ${params.teamSelectionRationale}

      **ADDITIONAL PMO CONTEXT:**
      - **Tasks:** ${pmoContext.tasks.join(', ')}
      - **Assessment:** ${pmoContext.assessment}
      - **Requirements:** ${pmoContext.requirements}

      ${params.requirementsDocument ? `**Requirements Document Available:** ${params.requirementsDocument}` : ''}

      ---

      **STRATEGIC COORDINATION PLAN REQUIREMENTS:**
      Create a comprehensive strategic implementation plan that addresses all PMO requirements, demonstrates seamless team coordination, and leverages specialized research expertise.

      **1. EXECUTIVE SUMMARY (Coordinated Strategic Overview)**
         - A detailed review of the original requirement as it was presented to the PMO.
         - Provide an enhanced project overview from a research-driven perspective, building upon the PMO analysis.
         - Summarize the strategic approach, highlighting how coordinated team efforts will achieve objectives.
         - Reference key insights from the **Enhanced PMO Assessment Analysis** and **Strategic Q&A Analysis**.

      **2. ENHANCED RESEARCH STRATEGY (Team-Coordinated Framework)**
         - **Information Retrieval Agent (IRA) Coordination:** Detail the plan for leveraging IRA for comprehensive data collection, PMO document analysis, and supporting PMO conclusions.
         - **Data Analyst & Synthesizer Agent (DASA) Coordination:** Outline how DASA will perform advanced analysis, synthesize findings, and develop strategic task breakdowns.
         - **Methodology Expansion:** Expand PMO data sources and analysis frameworks with advanced research methodologies and tools, integrating IRA and DASA capabilities.
         - **Quality Assurance Integration:** Incorporate comprehensive quality measures and assurance protocols (QualityAssuranceReviewerAgent).

      **3. STRATEGIC IMPLEMENTATION ROADMAP (Orchestrated Execution)**
         - Develop a detailed, phase-by-phase execution plan that leverages the specialized team members to fulfill PMO requirements and timelines.
         - Define clear milestones, task assignments, and inter-agent dependencies.
         - Detail resource allocation for research activities, including personnel and tools.
         - Build upon PMO risk assessment with advanced mitigation strategies informed by research.

      **4. TEAM COORDINATION FRAMEWORK (Research Team Management & Cross-Functional Collaboration)**
         - Define explicit delegation strategies for each specialized agent (IRA, DASA, ReportWriterFormatterAgent, QualityAssuranceReviewerAgent).
         - Outline communication protocols and data flow between agents.
         - Establish cross-team communication protocols for collaboration with other PMO-aligned teams (e.g., Marketing, Sales, Business Analysis).

      **5. ENHANCED DELIVERABLES AND OUTCOMES (Value-Driven Results)**
         - Build upon PMO-specified outputs with additional, high-value research deliverables.
         - Elaborate PMO strategic recommendations with detailed, evidence-based implementation guidance.
         - Expand PMO guidelines with comprehensive best practices and methodologies.
         - Expand PMO success metrics with advanced measurement and evaluation frameworks, validated by QA.

      **6. COMPREHENSIVE QUALITY ASSURANCE (Rigorous Validation & Continuous Improvement)**
         - Detail the validation protocols and quality frameworks (QualityAssuranceReviewerAgent) to ensure all outputs meet PMO standards and research excellence.
         - Outline processes for continuous improvement based on feedback and review.

      **7. STRATEGIC ANALYSIS COLLABORATION (Transparency & Insight Integration)**
         - Dedicate a section to explicitly show how **QueryDocumentsAgent** enhanced the PMO Assessment analysis.
         - Document the strategic questions and insights generated by **QuestionAnswerAgent**.
         - Show the specific findings and recommendations extracted from the **Enhanced PMO Assessment Analysis**.
         - Demonstrate how the **Strategic Q&A Analysis** informed the strategic implementation approach.
         - Reference specific quotes and findings from both the QueryDocumentsAgent and QuestionAnswerAgent outputs to illustrate their impact.

      ---

      **FINAL OUTPUT INSTRUCTIONS:**
      - **Format:** Present the response as a comprehensive, professional markdown document.
      - **Tone:** Maintain a detailed, analytical, and strategic tone, consistent with a ResearchAgentManager addressing PMO requirements.
      - **Content:** Ensure all PMO requirements are addressed with detailed answers.
      - **Integration:** Clearly demonstrate the seamless integration of insights from QueryDocumentsAgent and QuestionAnswerAgent throughout the plan.
      - **Clarity:** Use clear headings, bullet points, and structured sections for readability.
      `;

      // Process the strategic plan using the research lead agent's LLM capabilities
      const strategicPlan = await leadAgent.processRequest(strategicPlanPrompt);

      // Generate document title
      const documentTitle = `Strategic Implementation Plan - ${params.projectTitle} - ${params.pmoId}`;

      // Save the strategic plan to storage (both user-specific and global for PMO integration)
      try {
        // Save to user-specific collection using research lead agent's capabilities
        const documentId = await leadAgent.saveContent({
          title: documentTitle,
          content: strategicPlan,
          pmoId: params.pmoId,
          projectTitle: params.projectTitle,
          category: params.category || 'Research Strategy',
          priority: params.priority,
          createdBy: 'ResearchAgentManager',
          agentType: 'ResearchAgentManager',
          documentType: 'strategic_implementation_plan'
        }, 'pmo_strategic_plans');

        const documentUrl = `services/pmo/${documentId}`;

        // Also save to global Agent_Output collection for PMO integration
        try {
          const { v4: uuidv4 } = await import('uuid');
          const { adminDb } = await import('../../../components/firebase-admin');

          const requestId = uuidv4();

          // Prepare data for global storage (matching marketing workflow format)
          const agentOutputData = {
            requestId,
            timestamp: new Date(),
            agentType: 'ResearchAgentManager', // Research team strategic coordinator
            userId: this.userId,
            title: params.projectTitle, // Use project title as main title (like Marketing does)
            prompt: `Create strategic implementation plan for PMO project: ${params.projectTitle}`,
            result: {
              thinking: '', // Research strategic planning doesn't expose thinking process
              output: strategicPlan,
              documentUrl: documentUrl
            },
            agentMessages: [], // Research strategic planning uses internal coordination
            modelInfo: {
              provider: this.defaultLlmProvider,
              model: this.defaultLlmModel
            },
            // PMO-specific metadata for proper integration (matching marketing pattern)
            pmoMetadata: {
              pmoId: params.pmoId,
              teamId: 'Research',
              teamName: 'Research',
              source: 'PMO',
              processedAt: new Date().toISOString(),
              documentType: 'strategic_implementation_plan',
              autoTriggered: true
            },
            // Include metadata for PMO Output tab (matching marketing pattern)
            metadata: {
              recordTitle: params.projectTitle, // Use project title as record title
              projectTitle: params.projectTitle,
              source: 'PMO',
              pmoId: params.pmoId,
              teamName: 'Research',
              teamId: 'Research',
              outputType: 'strategic_plan'
            },
            // Category should represent the task type, not PMO identification
            category: params.category || 'Research Strategy',
            // Include context information
            contextOptions: {
              customContext: null,
              documentReferences: null,
              category: params.category || 'Research Strategy',
              pmoContext: {
                pmoId: params.pmoId,
                projectTitle: params.projectTitle,
                priority: params.priority
              }
            },
            // Include QueryDocumentsAgent and QuestionAnswerAgent interactions (following StrategicDirectorAgent pattern)
            agentInteractions: {
              documentQueries: leadAgent.getDocumentQueries(),
              questionAnswers: leadAgent.getQuestionAnswers(),
              pmoAssessmentAnalysis: pmoAssessmentAnalysis !== params.pmoAssessment ? pmoAssessmentAnalysis : null,
              qaAnalysisResult: qaAnalysis
            }
          };

          console.log(`[ResearchAgentManager] Storing research strategic plan in global collection with requestId: ${requestId}`);
          await adminDb.collection('Agent_Output').doc(requestId).set(agentOutputData);
          console.log(`[ResearchAgentManager] Successfully stored research strategic plan in global collection`);

          // Mark completion for client-side tab navigation
          console.log(`[ResearchAgentManager] Research processing completed successfully - ready for automatic tab navigation`);

          // Note: Client-side monitoring will detect this completion and trigger automatic tab navigation
          // The requestId and success status will be used for proper navigation handling
        } catch (globalStorageError) {
          console.error(`[ResearchAgentManager] Failed to save strategic plan to global collection:`, globalStorageError);
          // Continue even if global storage fails
        }

        console.log(`[ResearchAgentManager] Strategic plan saved with ID: ${documentId}`);

        return {
          success: true,
          strategicPlan,
          documentTitle,
          documentUrl
        };

      } catch (saveError) {
        console.error(`[ResearchAgentManager] Failed to save strategic plan:`, saveError);
        return {
          success: false,
          error: `Failed to save strategic plan: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`
        };
      }

    } catch (error) {
      console.error('[ResearchAgentManager] Error creating PMO strategic plan:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create strategic plan'
      };
    }
  }

  /**
   * PMO Integration: Start a PMO research task
   * This method integrates with PMO workflows while maintaining research expertise
   */
  async startPMOResearchTask(params: {
    pmoId: string;
    projectTitle: string;
    projectDescription: string;
    pmoAssessment: string;
    teamSelectionRationale: string;
    priority: string;
    category: string;
    requirementsDocument?: string;
  }): Promise<{
    success: boolean;
    strategicPlan?: string;
    documentTitle?: string;
    documentUrl?: string;
    researchPlanId?: string;
    error?: string;
  }> {
    try {
      if (!this.team) {
        console.log("Team not initialized. Initializing now...");
        await this.initializeResearchTeam();
      }
      if (!this.team) {
        throw new Error("Research team could not be initialized.");
      }

      const leadAgent = this.team.researchLead;
      console.log(`[Manager] Starting PMO research task: ${params.projectTitle} via agent ${leadAgent.getInfo().id}`);

      // As ResearchAgentManager, I am the strategic coordinator for PMO research tasks
      // I analyze requirements, create the strategic plan, and coordinate team member execution
      const strategicPlanResult = await this.createPMOStrategicPlan(params);

      if (!strategicPlanResult.success) {
        throw new Error(strategicPlanResult.error || 'Failed to create PMO strategic plan');
      }

      console.log(`[ResearchAgentManager] PMO strategic plan created successfully: ${strategicPlanResult.documentTitle}`);

      return {
        success: true,
        strategicPlan: strategicPlanResult.strategicPlan,
        documentTitle: strategicPlanResult.documentTitle,
        documentUrl: strategicPlanResult.documentUrl,
        researchPlanId: `strategic-plan-${params.pmoId}`, // Reference to strategic plan instead of separate research plan
        error: strategicPlanResult.error
      };
    } catch (error: any) {
      console.error(`[Manager] Failed to start PMO research task:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * PMO Integration: Analyze task for team recommendation
   * Delegates to the enhanced ResearchLeadAgent
   */
  async analyzePMOTask(params: {
    title: string;
    description: string;
    context: string;
    taskType: string;
  }): Promise<{
    success: boolean;
    recommendedTeams?: AgenticTeamId[];
    rationale?: string;
    error?: string;
  }> {
    try {
      if (!this.team) {
        await this.initializeResearchTeam();
      }
      if (!this.team) {
        throw new Error("Research team could not be initialized.");
      }

      const leadAgent = this.team.researchLead;
      return await leadAgent.analyzeTask(params);
    } catch (error: any) {
      console.error(`[Manager] Failed to analyze PMO task:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * PMO Integration: Retrieve PMO tasks and requirements
   * Delegates to the enhanced ResearchLeadAgent
   */
  async retrievePMOTasks(
    pmoId: string,
    userRequest: string,
    modelNameOrProvider?: string
  ): Promise<{
    success: boolean;
    tasks: string[];
    assessment: string;
    requirements: string;
    error?: string;
  }> {
    try {
      if (!this.team) {
        await this.initializeResearchTeam();
      }
      if (!this.team) {
        throw new Error("Research team could not be initialized.");
      }

      const leadAgent = this.team.researchLead;
      return await leadAgent.retrievePMOTasks(pmoId, userRequest, modelNameOrProvider);
    } catch (error: any) {
      console.error(`[Manager] Failed to retrieve PMO tasks:`, error);
      return {
        success: false,
        tasks: [],
        assessment: '',
        requirements: '',
        error: error.message
      };
    }
  }

  /**
   * Enhanced task routing that supports cross-team coordination
   * Extends the original method to handle PMO team assignments
   */
  async routeTasksFromLeadEnhanced(planId: string, crossTeamEnabled: boolean = false): Promise<void> {
    const leadAgent = this.team?.researchLead;
    if (!leadAgent) return;

    // First, handle traditional research team routing
    await this.routeTasksFromLead(planId);

    // If cross-team coordination is enabled, handle strategic task collections
    if (crossTeamEnabled) {
      try {
        // Check if there are strategic task collections that need cross-team routing
        const strategicCollections = leadAgent['activeStrategicCollections'] || new Map();

        for (const [collectionId, collection] of strategicCollections) {
          console.log(`[Manager] Processing strategic task collection: ${collectionId}`);

          // Log team assignments for cross-functional coordination
          Object.entries(collection.teamAssignments).forEach(([teamName, assignment]) => {
            console.log(`[Manager] Team ${teamName}: ${assignment.taskCount} tasks, ${assignment.estimatedWorkload}`);
          });

          // Here you would integrate with other team managers
          // For now, we log the cross-team coordination requirements
          const crossTeamTasks = collection.tasks.filter(task =>
            !task.assignedTeam.includes('Research') &&
            task.assignedTeam !== 'Strategic Director'
          );

          if (crossTeamTasks.length > 0) {
            console.log(`[Manager] ${crossTeamTasks.length} tasks require cross-team coordination`);
            // TODO: Integrate with other team managers (Marketing, Sales, etc.)
          }
        }
      } catch (error) {
        console.error(`[Manager] Error in enhanced task routing:`, error);
      }
    }
  }

  /**
   * Get enhanced team capabilities including PMO integration
   */
  getEnhancedTeamCapabilities(): {
    researchCapabilities: string[];
    pmoIntegration: string[];
    crossTeamCoordination: string[];
    strategicPlanning: string[];
  } {
    return {
      researchCapabilities: [
        'Information retrieval and analysis',
        'Data synthesis and insights generation',
        'Report writing and formatting',
        'Quality assurance and review',
        'Academic and web research'
      ],
      pmoIntegration: [
        'PMO task analysis and team recommendation',
        'PMO document retrieval and processing',
        'Strategic implementation plan creation',
        'PMO compliance and standards adherence',
        'Cross-functional project coordination'
      ],
      crossTeamCoordination: [
        'Marketing team collaboration',
        'Sales team research support',
        'Business analysis integration',
        'Software design research requirements',
        'Content team strategic guidance'
      ],
      strategicPlanning: [
        'Strategic task collection creation',
        'Timeline and milestone planning',
        'Resource allocation optimization',
        'Risk assessment and mitigation',
        'Success metrics definition'
      ]
    };
  }
}