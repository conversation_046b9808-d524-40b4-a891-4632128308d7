import React, { useState } from 'react';
import { Project, Task } from '../types';
import { Sparkles, X, Table, FileText } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { usePlanner } from '@/services/context/PlannerContext';
import { ChartGenerationResult, ExtendedChartConfig } from 'lib/tools';

interface CreateTasksModalProps {
  project: Project;
  onClose: () => void;
  onTasksCreated: () => void;
}

export default function CreateTasksModal({ project, onClose, onTasksCreated }: CreateTasksModalProps) {
  const [instructions, setInstructions] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationStatus, setGenerationStatus] = useState('');
  const [generatedTasks, setGeneratedTasks] = useState<Task[]>([]);
  const [reasoning, setReasoning] = useState<string>('');
  const [taskVisualization, setTaskVisualization] = useState<(ChartGenerationResult & {
    chartConfig: ExtendedChartConfig
  }) | null>(null);
  const [activeTab, setActiveTab] = useState<'tasks' | 'reasoning' | 'visualization'>('tasks');
  const [error, setError] = useState<string | null>(null);

  const { createTask } = usePlanner();
  const { data: session } = useSession();

  const handleCreateTasks = async () => {
    try {
      setIsGenerating(true);
      setGenerationStatus('Generating tasks with AI...');
      setError(null);

      // Call the API route instead of directly using the agent
      const response = await fetch('/api/planner/create-ai-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project,
          instructions
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate tasks');
      }

      const data = await response.json();
      setGeneratedTasks(data.tasks);
      setReasoning(data.reasoning || '');
      // Ensure the taskListVisualization is properly typed as ExtendedChartConfig
      if (data.taskListVisualization && data.taskListVisualization.chartConfig) {
        setTaskVisualization(data.taskListVisualization as ChartGenerationResult & {
          chartConfig: ExtendedChartConfig
        });
      } else {
        setTaskVisualization(null);
      }
      setGenerationStatus(`Generated ${data.tasks.length} tasks. Add them to your project?`);

      // If we have reasoning, switch to the reasoning tab
      if (data.reasoning) {
        setActiveTab('reasoning');
      }
    } catch (error) {
      console.error('Error generating tasks:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      setGenerationStatus('');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleAddTasksToProject = async () => {
    try {
      setIsGenerating(true);
      setGenerationStatus('Adding tasks to project...');

      // Get the current user's ID
      const promises = generatedTasks.map(async (task) => {
        // Create each task
        await createTask({
          ...task,
          projectId: project.id,
          createdBy: session?.user?.email || 'system'
        });
      });

      await Promise.all(promises);

      onTasksCreated();
      onClose();
    } catch (error) {
      console.error('Error adding tasks to project:', error);
      setError('Failed to add tasks to project. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg shadow-xl p-4 w-full max-w-2xl max-h-[80vh] h-[600px] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white flex items-center">
            <Sparkles className="w-5 h-5 mr-2 text-purple-400" />
            Create Tasks with AI
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-300 mb-4">
            Describe what tasks you want to create for this project. The AI will analyze your project details and generate relevant tasks.
          </p>

          <label className="block text-gray-400 mb-2">Instructions (optional)</label>
          <textarea
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            placeholder="E.g., Create tasks for the initial research phase, focusing on market analysis, competitor research, and user interviews."
            className="w-full h-64 p-3 bg-gray-700 text-white rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
            disabled={isGenerating}
          />
        </div>

        {error && (
          <div className="bg-red-900/30 border border-red-700 text-red-300 p-3 rounded-md mb-4">
            {error}
          </div>
        )}

        {generatedTasks.length > 0 && !error && (
          <div className="mb-4">
            {/* Tab Navigation */}
            <div className="flex border-b border-gray-600 mb-4">
              <button
                className={`px-4 py-2 font-medium ${
                  activeTab === 'tasks'
                    ? 'text-purple-400 border-b-2 border-purple-400'
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveTab('tasks')}
              >
                <div className="flex items-center">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Tasks
                </div>
              </button>

              <button
                className={`px-4 py-2 font-medium ${
                  activeTab === 'reasoning'
                    ? 'text-purple-400 border-b-2 border-purple-400'
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveTab('reasoning')}
                disabled={!reasoning}
              >
                <div className="flex items-center">
                  <FileText className="w-4 h-4 mr-2" />
                  Reasoning
                </div>
              </button>

              <button
                className={`px-4 py-2 font-medium ${
                  activeTab === 'visualization'
                    ? 'text-purple-400 border-b-2 border-purple-400'
                    : 'text-gray-400 hover:text-white'
                }`}
                onClick={() => setActiveTab('visualization')}
                disabled={!taskVisualization?.chartConfig}
              >
                <div className="flex items-center">
                  <Table className="w-4 h-4 mr-2" />
                  Table View
                </div>
              </button>
            </div>

            {/* Tab Content */}
            {activeTab === 'tasks' && (
              <>
                <h3 className="font-medium text-white mb-2">Generated Tasks:</h3>
                <div className="bg-gray-700 rounded-md p-2 max-h-80 overflow-y-auto">
                  {generatedTasks.map((task, index) => (
                    <div key={index} className="border-b border-gray-600 last:border-0 p-2">
                      <h4 className="font-medium text-white">{task.title}</h4>
                      <p className="text-gray-300 text-sm">{task.description}</p>
                    </div>
                  ))}
                </div>
              </>
            )}

            {activeTab === 'reasoning' && (
              <>
                <h3 className="font-medium text-white mb-2">AI Reasoning:</h3>
                <div className="bg-gray-700 rounded-md p-4 max-h-80 overflow-y-auto">
                  <div className="text-gray-300 whitespace-pre-wrap">{reasoning}</div>
                </div>
              </>
            )}

            {activeTab === 'visualization' && taskVisualization?.chartConfig && (
              <>
                <h3 className="font-medium text-white mb-2">Task Table View:</h3>
                <div className="bg-gray-700 rounded-md p-4 max-h-80 overflow-y-auto">
                  {/* Render the chart visualization */}
                  <div
                    className="text-sm text-gray-300"
                    dangerouslySetInnerHTML={{
                      __html: taskVisualization.chartConfig.explanation ||
                              'Task visualization generated by AI.'
                    }}
                  />

                  {/* If it's a table chart type, render it as HTML */}
                  {taskVisualization.chartConfig.chartType === 'table' && (
                    <div className="mt-4 overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-600">
                        <thead>
                          <tr>
                            {taskVisualization.chartConfig.columns?.map((column, idx) => (
                              <th
                                key={idx}
                                className="px-3 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                              >
                                {column.title || column.header || ''}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-600">
                          {taskVisualization.chartConfig.data.map((row, rowIdx) => (
                            <tr key={rowIdx} className={rowIdx % 2 === 0 ? 'bg-gray-800' : 'bg-gray-750'}>
                              {taskVisualization.chartConfig.columns?.map((column, colIdx) => {
                                const dataKey = column.dataKey || column.accessorKey || '';
                                return (
                                  <td
                                    key={colIdx}
                                    className="px-3 py-2 text-sm text-gray-200"
                                  >
                                    {String(row[dataKey] || '')}
                                  </td>
                                );
                              })}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        )}

        <div className="flex justify-between items-center mt-6">
          {generationStatus && (
            <p className="text-sm text-gray-300">
              {generationStatus}
            </p>
          )}

          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
              disabled={isGenerating}
            >
              Cancel
            </button>

            {generatedTasks.length === 0 ? (
              <button
                onClick={handleCreateTasks}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center"
                disabled={isGenerating}
              >
                {isGenerating ? (
                  <>
                    <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate Tasks with Reasoning
                  </>
                )}
              </button>
            ) : (
              <button
                onClick={handleAddTasksToProject}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                disabled={isGenerating}
              >
                {isGenerating ? 'Adding...' : 'Add Tasks to Project'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}