'use client';

import React, { useState, useEffect } from 'react';
import { db, auth } from '../lib/firebase/config';
import { collection, getDocs, query, limit, where } from 'firebase/firestore';
import { useSession } from 'next-auth/react';
import { useAuth } from '../context/AuthContext';

/**
 * A diagnostic component to test Firebase connectivity
 * This can be added to any page to help diagnose Firebase issues
 */
export default function FirebaseDiagnostic() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Testing Firebase connection...');
  const [details, setDetails] = useState<string[]>([]);
  const { data: session } = useSession();
  const { user } = useAuth();

  useEffect(() => {
    const runDiagnostics = async () => {
      setStatus('loading');
      setMessage('Testing Firebase connection...');
      setDetails([]);

      try {
        // Log environment
        const env = process.env.NODE_ENV;
        setDetails(prev => [...prev, `Environment: ${env}`]);

        // Test Firestore connection
        setDetails(prev => [...prev, 'Testing Firestore connection...']);

        // Try to fetch users collection
        const usersQuery = query(collection(db, 'users'), limit(1));
        const usersSnapshot = await getDocs(usersQuery);

        setDetails(prev => [...prev, `Successfully connected to Firestore. Found ${usersSnapshot.docs.length} users.`]);

        // Try to fetch projects collection
        const projectsQuery = query(collection(db, 'projects'), limit(10));
        const projectsSnapshot = await getDocs(projectsQuery);

        setDetails(prev => [...prev, `Successfully queried projects collection. Found ${projectsSnapshot.docs.length} projects.`]);

        // Log project details if any
        if (projectsSnapshot.docs.length > 0) {
          const projectIds = projectsSnapshot.docs.map(doc => doc.id).join(', ');
          setDetails(prev => [...prev, `Project IDs: ${projectIds}`]);
        }

        // Check Firebase auth state
        const firebaseUser = auth.currentUser;
        if (firebaseUser) {
          setDetails(prev => [...prev, `Firebase authenticated as: ${firebaseUser.email}`]);
        } else {
          setDetails(prev => [...prev, 'Not authenticated with Firebase']);
        }

        // Check NextAuth session
        if (session && session.user) {
          setDetails(prev => [
            ...prev,
            `NextAuth session: ${session?.user?.email || 'no email'}`,
            `NextAuth image: ${session?.user?.image || 'none'}`
          ]);
        } else {
          setDetails(prev => [...prev, 'No NextAuth session']);
        }

        // Check user context
        if (user) {
          setDetails(prev => [
            ...prev,
            `User context: ${user.email}`,
            `User avatar: ${user.avatar || 'none'}`
          ]);

          // Check if user exists in Firestore with matching email
          if (session && session.user && session.user.email) {
            const userEmail = session.user.email;
            const userQuery = query(collection(db, 'users'), where('email', '==', userEmail), limit(1));
            const userSnapshot = await getDocs(userQuery);

            if (!userSnapshot.empty) {
              const userData = userSnapshot.docs[0].data();
              setDetails(prev => [
                ...prev,
                `Firestore user found: ${userData.email || 'no email'}`,
                `Firestore avatar: ${userData.avatar || 'none'}`
              ]);
            } else {
              setDetails(prev => [...prev, 'User not found in Firestore']);
            }
          }
        } else {
          setDetails(prev => [...prev, 'No user in context']);
        }

        setStatus('success');
        setMessage('Firebase connection successful');
      } catch (error: any) {
        console.error('Firebase diagnostic error:', error);
        setStatus('error');
        setMessage(`Firebase connection error: ${error.message}`);

        // Add error details
        setDetails(prev => [
          ...prev,
          `Error code: ${error.code || 'unknown'}`,
          `Error message: ${error.message}`,
          `Stack trace: ${error.stack || 'not available'}`
        ]);
      }
    };

    runDiagnostics();
  }, []);

  return (
    <div className="p-4 mb-6 border rounded-lg" style={{
      backgroundColor: status === 'loading' ? '#2d3748' :
                       status === 'success' ? '#1a365d' :
                       '#4a2a2a',
      borderColor: status === 'loading' ? '#4a5568' :
                   status === 'success' ? '#2b6cb0' :
                   '#c53030'
    }}>
      <h3 className="text-lg font-medium mb-2" style={{
        color: status === 'loading' ? '#a0aec0' :
               status === 'success' ? '#63b3ed' :
               '#fc8181'
      }}>
        Firebase Diagnostic
      </h3>

      <p className="mb-2">{message}</p>

      {details.length > 0 && (
        <div className="mt-3 p-3 bg-black bg-opacity-30 rounded text-sm font-mono overflow-auto max-h-60">
          {details.map((detail, index) => (
            <div key={index} className="mb-1">
              {detail}
            </div>
          ))}
        </div>
      )}

      {status === 'error' && (
        <div className="mt-3">
          <p className="text-sm text-red-300 mb-2">
            Possible solutions:
          </p>
          <ul className="list-disc pl-5 text-sm text-red-200">
            <li>Check if your production domain is added to Firebase authorized domains</li>
            <li>Verify Firebase security rules allow access from your production environment</li>
            <li>Check for CORS issues in browser console</li>
            <li>Ensure your Firebase API key is not restricted to specific domains</li>
          </ul>
        </div>
      )}
    </div>
  );
}
